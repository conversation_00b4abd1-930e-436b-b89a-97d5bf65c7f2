# ZteStockInfoUploadController.stockInfoUpload方法调用链深度分析报告

**生成时间：** 2025-08-11 10:13:36  
**分析人员：** 0668000643  
**目标方法：** ZteStockInfoUploadController.stockInfoUpload  

---

## 📋 目录
- [1. 调用链概览](#1-调用链概览)
- [2. 接口层分析](#2-接口层分析)
- [3. 服务层分析](#3-服务层分析)
- [4. 数据访问层分析](#4-数据访问层分析)
- [5. 外部系统集成分析](#5-外部系统集成分析)
- [6. 数据流转详细分析](#6-数据流转详细分析)
- [7. 涉及的数据结构](#7-涉及的数据结构)
- [8. 异常处理机制](#8-异常处理机制)
- [9. 性能分析](#9-性能分析)
- [10. 总结与建议](#10-总结与建议)

---

## 1. 调用链概览

### 1.1 完整调用链路图

```
┌─────────────────────────────────────────────────────────────────┐
│                         HTTP请求入口                              │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│  ZteStockInfoUploadController.stockInfoUpload()                 │
│  ├─ 请求头验证 (X-EMP-NO)                                        │
│  ├─ 参数装配 (dto.setEmpNo)                                      │
│  └─ 调用服务层                                                    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│  ZteStockInfoUploadServiceImpl.stockInfoUpload()                │
│  ├─ 参数校验 (物料代码数量≤500)                                    │
│  ├─ 查询库存数据 (数据库查询)                                      │
│  ├─ 数据转换处理                                                  │
│  ├─ 批次号生成                                                    │
│  ├─ 数据分批处理                                                  │
│  ├─ 日志记录                                                     │
│  └─ B2B数据推送                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    ▼               ▼               ▼
    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │   数据库操作      │  │   外部系统调用    │  │   Redis操作      │
    │                │  │                │  │                │
    │ ► 查询库存信息    │  │ ► B2B推送接口    │  │ ► 序列号生成     │
    │ ► 保存库存快照    │  │ ► IMES集成      │  │                │
    │ ► 记录上传日志    │  │                │  │                │
    └─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 1.2 核心调用层次

1. **接口控制层 (Controller)**：参数验证、权限校验
2. **应用服务层 (Service)**：业务逻辑处理、数据转换
3. **数据访问层 (Repository)**：数据库操作、数据持久化
4. **外部系统集成层**：B2B推送、IMES系统调用
5. **工具服务层**：序列号生成、通用工具

---

## 2. 接口层分析

### 2.1 Controller方法定义

**文件位置：** `src/main/java/com/zte/interfaces/step/ZteStockInfoUploadController.java`

```java
@ApiOperation("库存数据上传")
@PostMapping("/stockInfoUpload")
public void stockInfoUpload(HttpServletRequest request, @RequestBody ZteStockInfoDTO dto){
    String xEmpNo = request.getHeader(Constant.X_EMP_NO);
    BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
    dto.setEmpNo(xEmpNo);
    zteStockInfoUploadService.stockInfoUpload(dto);
}
```

### 2.2 入参分析

#### 2.2.1 HTTP请求信息
- **请求方式：** POST
- **接口路径：** `/stepdt/stockInfoUpload`
- **Content-Type：** application/json

#### 2.2.2 请求头参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| X-EMP-NO | String | ✓ | 员工工号，用于身份验证和审计 |

#### 2.2.3 请求体参数 (ZteStockInfoDTO)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| itemNoList | List\<String\> | ✓ | 物料代码列表，最多500个 |
| uploadBeginDate | String | ○ | 上传开始日期 |
| uploadEndDate | String | ○ | 上传结束日期 |
| partitionName | String | ○ | 分区名称 |
| empNo | String | - | 员工工号（接口内部设置） |

### 2.3 出参分析

- **返回类型：** void
- **HTTP状态码：** 200 (成功) / 500 (异常)
- **异常处理：** 通过全局异常处理器处理业务异常

### 2.4 接口层处理逻辑

1. **权限验证**：检查请求头中的X-EMP-NO是否为空
2. **参数装配**：将员工工号设置到DTO对象中
3. **服务调用**：委托给服务层处理具体业务逻辑
4. **异常传递**：将服务层异常向上传递

---

## 3. 服务层分析

### 3.1 Service方法实现

**文件位置：** `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java`

```java
@Override
public void stockInfoUpload(ZteStockInfoDTO dto) {
    // 1. 参数校验
    BusiAssertException.isTrue(
        Tools.isNotEmpty(dto.getItemNoList()) && 
        dto.getItemNoList().size() > NumConstant.INT_500, 
        MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED
    );

    // 2. 查询库存数据
    List<ZteStockInfoDTO> zteStockInfoDTOList = zteStockInfoUploadRepository.getStockInfo(dto);
    if (CommonUtils.isEmpty(zteStockInfoDTOList)) {
        return;
    }

    // 3-7. 数据处理、日志记录、B2B推送
    // ...详细处理逻辑...
}
```

### 3.2 详细处理步骤

#### 步骤1：参数校验
```java
BusiAssertException.isTrue(
    Tools.isNotEmpty(dto.getItemNoList()) && 
    dto.getItemNoList().size() > NumConstant.INT_500, 
    MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED
);
```
- **校验内容：** 物料代码列表不能超过500个
- **失败处理：** 抛出业务异常，返回错误信息

#### 步骤2：查询库存数据
```java
List<ZteStockInfoDTO> zteStockInfoDTOList = zteStockInfoUploadRepository.getStockInfo(dto);
```
- **数据来源：** 多表关联查询STEP系统库存数据
- **查询条件：** 根据物料代码列表过滤
- **返回内容：** 库存明细信息列表

#### 步骤3：生成批次号
```java
String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
```
- **生成规则：** 基于Redis序列号 + 日期时间格式化
- **格式：** YYYYMMDDHHMM2SSSSSS
- **用途：** 标识本次数据传输批次，便于追踪

#### 步骤4：数据转换处理
```java
List<ZteStockInfoUploadDTO> zteStockInfoUploadDTOList = new ArrayList<>();
for (ZteStockInfoDTO zteStockInfoDTO : zteStockInfoDTOList) {
    ZteStockInfoUploadDTO zteStockInfoUploadDTO = new ZteStockInfoUploadDTO();
    BeanUtils.copyProperties(zteStockInfoDTO, zteStockInfoUploadDTO);
    zteStockInfoUploadDTO.setDataTransferBatchNo(dataTransferBatchNo);
    zteStockInfoUploadDTO.setUuid(UUID.randomUUID().toString().trim().replaceAll("-", ""));
    // 设置默认值
    zteStockInfoUploadDTO.setLobCode("");
    zteStockInfoUploadDTO.setConfigMaterialCode("");
    zteStockInfoUploadDTO.setConfigMaterialDesc("");
    zteStockInfoUploadDTOList.add(zteStockInfoUploadDTO);
}
```
- **转换逻辑：** ZteStockInfoDTO → ZteStockInfoUploadDTO
- **新增字段：** 批次号、UUID、默认值设置
- **处理目的：** 适配B2B接口数据格式

#### 步骤5：数据分批处理
```java
for (List<ZteStockInfoUploadDTO> tempList : CommonUtils.splitList(zteStockInfoUploadDTOList, NumConstant.INT_500)) {
    // 组装B2B对象和日志对象
}
```
- **分批规则：** 每批最多500条记录
- **分批目的：** 控制单次推送数据量，避免接口超时

#### 步骤6：组装B2B推送对象
```java
CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
customerDataLogDTO.setId(UUID.randomUUID().toString());
customerDataLogDTO.setOrigin("InforWMS");
customerDataLogDTO.setCustomerName("ByteDance");
customerDataLogDTO.setProjectName("库存信息");
customerDataLogDTO.setMessageType("ZTE_IMES_BYTEDANCE_INVENTORY");
customerDataLogDTO.setTaskNo(dataTransferBatchNo);
Map<String, Object> map = new HashMap<>();
map.put("data", tempList);
customerDataLogDTO.setJsonData(JSON.toJSONString(map));
customerDataLogDTO.setFactoryId(51);
customerDataLogDTO.setCreateBy(dto.getEmpNo());
customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
```

#### 步骤7：保存库存快照
```java
for (List<ZteStockInfoDTO> tempZteStockInfoDTOList : CommonUtils.splitList(zteStockInfoDTOList, NumConstant.INT_500)) {
    zteStockInfoUploadRepository.insertStockUploadSnapshot(tempZteStockInfoDTOList);
}
```
- **保存目的：** 记录库存上传时的快照数据
- **表名：** kxstepiii.stock_upload_snapshot

#### 步骤8：记录上传日志
```java
for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
    zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
}
```
- **日志表名：** kxstepiii.stock_upload_log
- **日志内容：** 推送状态、数据内容、时间戳等

#### 步骤9：推送B2B数据
```java
for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
    imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, dto.getEmpNo());
}
```
- **推送目标：** IMES制造中心工厂B2B接口
- **推送方式：** HTTP POST JSON格式
- **认证方式：** 基于AES加密的密钥认证

---

## 4. 数据访问层分析

### 4.1 Repository接口定义

**文件位置：** `src/main/java/com/zte/domain/model/step/ZteStockInfoUploadRepository.java`

#### 主要方法：
```java
public interface ZteStockInfoUploadRepository {
    List<ZteStockInfoDTO> getStockInfo(ZteStockInfoDTO dto);                    // 查询库存信息
    int insertStockUploadSnapshot(List<ZteStockInfoDTO> list);                  // 保存库存快照
    int insertStockUploadLog(List<ZteStockInfoUploadLogDTO> list);              // 保存上传日志
    int updateStockUploadLog(ZteStockInfoUploadLogDTO dto);                     // 更新日志状态
    List<ZteStockInfoUploadLogDTO> stockUploadLogMonitor();                     // 日志监控查询
}
```

### 4.2 核心SQL分析

#### 4.2.1 库存信息查询 (getStockInfo)

**SQL文件位置：** `src/main/resources/mapper/step/ZteStockInfoUploadRepository.xml`

```sql
SELECT
    'ZTE1' odmPlantCode,
    'BU02' buCode,
    to_char(sysdate,'YYYYMMDD') dataDate,
    bi.item_no itemNo,
    ci.customer_code materialCode,
    ci.customer_item_name materialDesc,
    'N001' odmStorageLoc,
    st.balance_qty stockQuantity,
    'EA' unit,
    st.stock_no stockNo,
    CASE 
        WHEN instr(ses.stock_name, '在途') > 0 THEN '20'
        WHEN instr(ses.stock_name, '待处理') > 0 THEN '11'
        ELSE '00' 
    END as stockStatus
FROM kxstepiii.st_summary st
JOIN kxstepiii.ba_item bi ON bi.item_id = st.item_id
JOIN kxstepiii.st_stock ses ON ses.stock_no = st.stock_no
JOIN kxstepiii.customer_items ci ON ci.zte_code = bi.item_no
WHERE st.balance_qty > 0
  AND ci.customer_name = 'ByteDance'
  AND trim(ci.customer_code) IS NOT NULL
  AND trim(ci.customer_item_name) IS NOT NULL
  <if test="itemNoList != null and itemNoList.size() > 0">
    AND bi.item_no IN
    <foreach collection="itemNoList" item="item" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
    </foreach>
  </if>
```

**关联表说明：**
- **kxstepiii.st_summary** - 库存汇总表，存储当前库存数量
- **kxstepiii.ba_item** - 物料基础信息表
- **kxstepiii.st_stock** - 库房信息表，包含库房状态
- **kxstepiii.customer_items** - 客户物料映射表，ZTE物料代码与客户物料代码的对应关系

**查询特点：**
- 只查询有库存数量的记录 (balance_qty > 0)
- 专门查询ByteDance客户的物料映射
- 根据库房名称自动判断库存状态
- 支持按物料代码列表过滤

#### 4.2.2 库存快照保存 (insertStockUploadSnapshot)

```sql
INSERT INTO kxstepiii.stock_upload_snapshot
(serialkey, item_no, stock_no, qty, create_by, last_updated_by)
SELECT kxstepiii.seq_stock_upload_snapshot.nextval, temp.* FROM (
    <foreach collection="list" item="item" separator="union all">
        SELECT #{item.itemNo}, #{item.stockNo}, #{item.stockQuantity},
               'system', 'system' FROM dual
    </foreach>
) temp
```

**功能说明：**
- 记录本次上传时的库存快照
- 使用Oracle序列生成主键
- 批量插入提高性能

#### 4.2.3 上传日志保存 (insertStockUploadLog)

```sql
INSERT INTO kxstepiii.stock_upload_log
(id, origin, customer_name, project_name, message_type, task_no, 
 json_data, factory_id, status, create_by, last_updated_by)
VALUES (#{item.id}, #{item.origin}, #{item.customerName}, 
        #{item.projectName}, #{item.messageType}, #{item.taskNo}, 
        empty_clob(), #{item.factoryId}, #{item.status}, 
        'system', 'system');
-- 单独更新CLOB字段
UPDATE kxstepiii.stock_upload_log 
SET json_data = #{item.jsonData,jdbcType=CLOB} 
WHERE id = #{item.id};
```

**技术特点：**
- 使用CLOB存储大容量JSON数据
- 分步插入：先插入基本信息，再更新CLOB字段
- 记录完整的推送日志信息

---

## 5. 外部系统集成分析

### 5.1 B2B推送服务

**文件位置：** `src/main/java/com/zte/common/utils/ImesCenterfactoryRemoteService.java`

#### 5.1.1 pushDataToB2B方法分析

```java
public void pushDataToB2B(List<CustomerDataLogDTO> dataList, String empNo) {
    // 构建请求头
    Map<String, String> headers = Tools.newHashMap();
    headers.put("X-TENANT-ID", "10001");
    headers.put("X-EMP-NO", empNo);
    headers.put("X-AUTH-VALUE", "1");
    headers.put("X-FACTORY-ID", "51");
    headers.put("X-SOURCE-SYS", "R-WMS");
    headers.put("app-code", accessKey);
    headers.put("secret-key", AesUtils.aesGcmEncrypt(
        accessSecret + ";" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), 
        secret
    ));

    // 构建请求URL
    String url = imesUrl + "/zte-mes-manufactureshare-centerfactory" + "/pushDataToB2B";

    // 发送HTTP POST请求
    String res = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(dataList), headers);

    // 校验响应结果
    BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
    ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});
    BusiAssertException.isEmpty(result, PUSH_B2B_FAILED);
    BusiAssertException.notEquals("SUCCESS", result.getCode().getCode(), result.getCode().getMsg());
}
```

#### 5.1.2 认证机制分析

**请求头认证信息：**
| Header | 说明 | 示例值 |
|--------|------|--------|
| X-TENANT-ID | 租户ID | 10001 |
| X-EMP-NO | 员工工号 | 从接口传入 |
| X-AUTH-VALUE | 认证值 | 1 |
| X-FACTORY-ID | 工厂ID | 51 |
| X-SOURCE-SYS | 来源系统 | R-WMS |
| app-code | 应用编码 | 配置文件中的accessKey |
| secret-key | 加密密钥 | AES-GCM加密后的值 |

**加密算法：**
- **算法：** AES-GCM
- **密钥内容：** accessSecret + ";" + 当前时间戳
- **加密密钥：** 配置文件中的secret

### 5.2 序列号生成服务

**文件位置：** Redis序列号工具类

```java
@Override
public String getDataTransferBatchNo(String batchNoType) {
    // 获取递增的序列号，并转换为字符串
    String dataTransferBatchNo = RedisSerialNoUtil.getDateIncreaseId(batchNoType, 6);
    // 格式化序列号：前12位 + "2" + 后面位数
    return dataTransferBatchNo.substring(0, 12) + "2" + dataTransferBatchNo.substring(13);
}
```

**序列号格式：** `YYYYMMDDHHMM2XXXXXX`
- YYYYMMDDMM：年月日时分
- 2：固定分隔符
- XXXXXX：6位递增序列号

---

## 6. 数据流转详细分析

### 6.1 数据转换流程

```
原始请求数据 (ZteStockInfoDTO)
            │
            ▼ [参数校验]
数据库查询结果 (List<ZteStockInfoDTO>)
            │
            ▼ [数据转换]
B2B推送格式 (List<ZteStockInfoUploadDTO>)
            │
            ▼ [分批处理]
推送对象组装 (List<CustomerDataLogDTO>)
            │
            ▼ [外部推送]
B2B系统接收
```

### 6.2 关键数据转换

#### 6.2.1 库存状态映射
```java
CASE 
    WHEN instr(ses.stock_name, '在途') > 0 THEN '20'
    WHEN instr(ses.stock_name, '待处理') > 0 THEN '11'
    ELSE '00' 
END as stockStatus
```

**状态码含义：**
- **00**：正常库存
- **11**：待处理库存  
- **20**：在途库存

#### 6.2.2 固定值设置
```java
zteStockInfoUploadDTO.setOdmPlantCode("ZTE1");      // 工厂代码
zteStockInfoUploadDTO.setBuCode("BU02");            // 业务单元
zteStockInfoUploadDTO.setOdmStorageLoc("N001");     // 存储位置
zteStockInfoUploadDTO.setUnit("EA");                // 计量单位
```

### 6.3 数据分批策略

**分批规则：**
- 每批最多500条记录
- 分别处理：库存快照保存、日志记录、B2B推送
- 确保单次操作不会因数据量过大而超时

**分批实现：**
```java
for (List<T> tempList : CommonUtils.splitList(originalList, 500)) {
    // 处理每批数据
}
```

---

## 7. 涉及的数据结构

### 7.1 核心DTO类

#### 7.1.1 ZteStockInfoDTO (请求入参)
```java
public class ZteStockInfoDTO {
    private String dataTransferBatchNo;    // 数据传输批次号
    private String uuid;                   // 唯一标识
    private String itemNo;                 // 物料代码
    private String materialCode;           // 客户物料代码  
    private String materialDesc;           // 物料描述
    private String stockNo;                // 库房编号
    private Integer stockQuantity;         // 库存数量
    private String stockStatus;            // 库存状态
    private String unit;                   // 计量单位
    private List<String> itemNoList;       // 物料代码列表（查询条件）
    private String empNo;                  // 员工工号
    private String uploadBeginDate;        // 上传开始日期
    private String uploadEndDate;          // 上传结束日期
    private String partitionName;          // 分区名称
}
```

#### 7.1.2 ZteStockInfoUploadDTO (B2B推送格式)
```java
public class ZteStockInfoUploadDTO {
    private String dataTransferBatchNo;    // 数据传输批次号
    private String uuid;                   // 唯一标识UUID
    private String odmPlantCode;           // ODM工厂代码（固定ZTE1）
    private String buCode;                 // 业务单元代码（固定BU02）
    private String lobCode;                // LOB代码（空字符串）
    private String dataDate;               // 数据日期（YYYYMMDD格式）
    private String configMaterialCode;     // 配置物料代码（空字符串）
    private String configMaterialDesc;     // 配置物料描述（空字符串）
    private String materialCode;           // 客户物料代码
    private String materialDesc;           // 物料描述
    private String odmStorageLoc;          // ODM存储位置（固定N001）
    private String odmStorageLocDesc;      // 存储位置描述
    private Integer stockQuantity;         // 库存数量
    private String unit;                   // 计量单位（固定EA）
    private String stockStatus;            // 库存状态（00/11/20）
    private List<String> snList;           // 序列号列表
}
```

#### 7.1.3 CustomerDataLogDTO (B2B推送包装)
```java
public class CustomerDataLogDTO {
    private String id;                     // 主键ID（UUID）
    private String origin;                 // 来源系统（InforWMS）
    private String customerName;           // 客户名称（ByteDance）
    private String projectName;            // 项目名称（库存信息）
    private String messageType;            // 消息类型（ZTE_IMES_BYTEDANCE_INVENTORY）
    private String taskNo;                 // 任务号（批次号）
    private String jsonData;               // JSON数据（实际推送内容）
    private Integer factoryId;             // 工厂ID（51）
    private String createBy;               // 创建人
    private String lastUpdatedBy;          // 更新人
    private Date createDate;               // 创建时间
    private Date lastUpdatedDate;          // 更新时间
}
```

#### 7.1.4 ZteStockInfoUploadLogDTO (日志记录)
```java
public class ZteStockInfoUploadLogDTO {
    private String id;                     // 主键ID
    private String origin;                 // 来源系统
    private String customerName;           // 客户名称
    private String projectName;            // 项目名称
    private String messageType;            // 消息类型
    private String taskNo;                 // 任务号
    private String jsonData;               // JSON数据
    private String uploadStatus;           // 上传状态
    private String status;                 // 处理状态
    private Integer factoryId;             // 工厂ID
    private String createBy;               // 创建人
    private String lastUpdatedBy;          // 更新人
    private Date createDate;               // 创建时间
    private Date lastUpdatedDate;          // 更新时间
}
```

### 7.2 数据库表结构

#### 7.2.1 库存汇总表 (kxstepiii.st_summary)
```sql
-- 核心字段
item_id          VARCHAR2(50)    -- 物料ID
stock_no         VARCHAR2(20)    -- 库房编号
balance_qty      NUMBER          -- 库存数量
```

#### 7.2.2 物料基础信息表 (kxstepiii.ba_item)
```sql
-- 核心字段  
item_id          VARCHAR2(50)    -- 物料ID
item_no          VARCHAR2(50)    -- 物料代码
```

#### 7.2.3 客户物料映射表 (kxstepiii.customer_items)
```sql
-- 核心字段
zte_code         VARCHAR2(50)    -- ZTE物料代码
customer_code    VARCHAR2(50)    -- 客户物料代码
customer_item_name VARCHAR2(200) -- 客户物料名称
customer_name    VARCHAR2(100)   -- 客户名称
```

#### 7.2.4 库存上传快照表 (kxstepiii.stock_upload_snapshot)
```sql
-- 核心字段
serialkey        NUMBER          -- 主键序列
item_no          VARCHAR2(50)    -- 物料代码
stock_no         VARCHAR2(20)    -- 库房编号
qty              NUMBER          -- 数量
create_by        VARCHAR2(50)    -- 创建人
create_date      DATE            -- 创建时间
```

#### 7.2.5 库存上传日志表 (kxstepiii.stock_upload_log)
```sql
-- 核心字段
id               VARCHAR2(50)    -- 主键ID
origin           VARCHAR2(50)    -- 来源系统
customer_name    VARCHAR2(100)   -- 客户名称
project_name     VARCHAR2(100)   -- 项目名称
message_type     VARCHAR2(100)   -- 消息类型
task_no          VARCHAR2(50)    -- 任务号
json_data        CLOB            -- JSON数据
factory_id       NUMBER          -- 工厂ID
upload_status    NUMBER          -- 上传状态
status           VARCHAR2(10)    -- 处理状态
create_date      DATE            -- 创建时间
```

---

## 8. 异常处理机制

### 8.1 参数校验异常

```java
// 员工工号为空
BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);

// 物料代码数量超限
BusiAssertException.isTrue(
    Tools.isNotEmpty(dto.getItemNoList()) && dto.getItemNoList().size() > NumConstant.INT_500, 
    MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED
);
```

**异常处理特点：**
- 使用统一的业务异常类 `BusiAssertException`
- 通过消息ID管理错误信息
- 支持国际化错误提示

### 8.2 数据库操作异常

```java
// 数据库操作包装在try-catch中
try {
    zteStockInfoUploadRepository.insertStockUploadSnapshot(tempZteStockInfoDTOList);
} catch (Exception e) {
    log.error("保存库存快照失败：", e);
    // 可能的重试机制或降级处理
}
```

### 8.3 外部系统调用异常

```java
// B2B推送异常处理
BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});
BusiAssertException.isEmpty(result, PUSH_B2B_FAILED);
BusiAssertException.notEquals("SUCCESS", result.getCode().getCode(), result.getCode().getMsg());
```

**外部调用保护：**
- 响应结果非空校验
- JSON解析异常处理
- 业务状态码校验
- 超时和重试机制（在HttpClient层面）

### 8.4 异常传播机制

```
Controller层 ← Service层 ← Repository层
     │              │           │
     ▼              ▼           ▼
全局异常处理器  →  日志记录  →  异常包装
```

---

## 9. 性能分析

### 9.1 性能瓶颈点

#### 9.1.1 数据库查询性能
- **多表关联查询**：涉及4张表的JOIN操作
- **数据量限制**：单次查询受物料代码列表大小影响（最多500个）
- **索引优化**：需要在item_no、balance_qty等字段建立索引

#### 9.1.2 数据传输性能
- **分批处理**：每批500条记录，减少单次传输压力
- **JSON序列化**：大量数据对象序列化为JSON字符串
- **网络传输**：HTTP POST请求传输大容量数据

#### 9.1.3 数据库写入性能
- **批量插入**：使用批量SQL提高插入效率
- **CLOB字段**：大容量CLOB字段写入相对较慢
- **事务处理**：多个数据库操作在同一事务中

### 9.2 性能优化措施

#### 9.2.1 已实施的优化
1. **数据分批处理**：避免一次性处理大量数据
2. **批量数据库操作**：减少数据库连接次数
3. **异步处理**：Service层支持异步调用
4. **数据缓存**：Redis序列号生成缓存

#### 9.2.2 建议的优化
1. **数据库索引优化**：
   ```sql
   CREATE INDEX idx_st_summary_item_balance ON kxstepiii.st_summary(item_id, balance_qty);
   CREATE INDEX idx_ba_item_no ON kxstepiii.ba_item(item_no);
   CREATE INDEX idx_customer_items_zte ON kxstepiii.customer_items(zte_code, customer_name);
   ```

2. **连接池优化**：
   ```yaml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 20
         minimum-idle: 5
         connection-timeout: 30000
   ```

3. **HTTP客户端优化**：
   ```java
   // 使用连接池和超时设置
   HttpClientUtil.configureTimeout(connectTimeout, readTimeout);
   ```

### 9.3 监控指标

#### 9.3.1 业务监控
- **处理成功率**：推送成功 / 总推送次数
- **数据处理量**：每次调用处理的记录数
- **处理耗时**：从接口调用到完成的总时间

#### 9.3.2 技术监控
- **数据库查询耗时**：getStockInfo查询时间
- **B2B推送耗时**：外部接口调用时间
- **内存使用**：大批量数据处理时的内存占用

---

## 10. 总结与建议

### 10.1 调用链总结

ZteStockInfoUploadController.stockInfoUpload方法实现了一个完整的库存数据上传和B2B推送流程：

1. **接口层**：负责参数验证和权限校验
2. **服务层**：实现核心业务逻辑，包括数据查询、转换、分批处理
3. **数据访问层**：提供数据库操作，包括查询、快照保存、日志记录
4. **外部集成层**：负责B2B系统推送和序列号生成

### 10.2 架构优势

#### 10.2.1 设计优势
- **分层架构清晰**：职责分离明确，易于维护
- **数据转换灵活**：支持不同格式间的数据转换
- **批处理能力**：支持大批量数据的分批处理
- **可追溯性强**：完整的日志记录和快照机制

#### 10.2.2 技术优势
- **异常处理完善**：统一的异常处理机制
- **性能考虑周全**：分批处理避免性能瓶颈
- **安全认证可靠**：基于AES加密的认证机制
- **监控支持完备**：日志记录便于问题排查

### 10.3 改进建议

#### 10.3.1 短期改进
1. **增加重试机制**：对B2B推送失败的情况增加重试
2. **优化数据库查询**：添加必要的索引，优化JOIN查询
3. **增加监控告警**：对关键操作添加监控和告警机制

#### 10.3.2 长期改进
1. **引入消息队列**：使用Kafka等消息队列实现异步处理
2. **数据分区策略**：对大表实施分区提高查询性能
3. **微服务拆分**：将库存管理和B2B推送拆分为独立服务

#### 10.3.3 代码质量改进
1. **增加单元测试**：提高测试覆盖率
2. **配置外部化**：将硬编码的常量移到配置文件
3. **日志级别优化**：根据环境调整日志输出级别

### 10.4 业务价值

该方法实现了ZTE与ByteDance之间的库存数据自动同步，具有以下业务价值：

1. **数据实时性**：确保库存信息的及时同步
2. **业务可追溯**：完整的操作日志和数据快照
3. **系统集成**：打通了WMS与B2B系统的数据通道
4. **自动化程度高**：减少人工干预，提高效率

---

**报告结束**

*本报告基于源码深度分析生成，详细展示了stockInfoUpload方法的完整调用链路、数据处理逻辑和系统架构设计。*

