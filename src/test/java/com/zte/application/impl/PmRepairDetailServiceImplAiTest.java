/*Started by AICoder, pid:gb2e7437f7n7f80145db097651b96d390ea91eb9*/
package com.zte.application.impl;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SmtSnMtlTracingT;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeExecInfo;
import com.zte.domain.model.technical.TechnicalChangeExecInfoRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({CommonUtils.class,BasicsettingRemoteService.class})
@RunWith(PowerMockRunner.class)
public class PmRepairDetailServiceImplAiTest {
    @Mock private SmtSnMtlTracingTService smtSnMtlTracingTService;
    @Mock private WipScanHistoryService wipScanHistoryService;

    @Mock private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    DatawbRemoteService datawbRemoteService;
    @InjectMocks
    private PmRepairDetailServiceImpl pmRepairDetailService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    AvlService avlService;

    @Mock
    TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Mock
    TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;

    private Map<String, SysLookupValuesDTO> tempMap;
    private BarcodeExpandDTO barcodeExpandDTO;
    private BigDecimal repairSnEnvAttr;


    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(CommonUtils.class,BasicsettingRemoteService.class);
        PowerMockito.when(CommonUtils.getLmbMessage((any()))).thenReturn("");
        tempMap = new HashMap<>();
        barcodeExpandDTO = new BarcodeExpandDTO();
        repairSnEnvAttr = new BigDecimal("1");
    }

    @Test
    public void testGetItemSnAndSupInfo_WithValidData() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setSubSn("123");
        dto.setSide("A");
        dto.setItemCode("ITEM1");
        dto.setFactoryId(BigDecimal.ONE);

        WipScanHistoryDTO queryDto = new WipScanHistoryDTO();
        queryDto.setSn("123");

        WipScanHistory timeDto = new WipScanHistory();
        timeDto.setTimeLimitLeftStr("2022-01-01 00:00:00");
        timeDto.setTimeLimitRightStr("2022-01-02 00:00:00");

        SmtSnMtlTracingTDTO queryItemSnDto = new SmtSnMtlTracingTDTO();
        queryItemSnDto.setSubSn("123");
        queryItemSnDto.setCraftSection("A");
        queryItemSnDto.setItemCode("ITEM1");
        queryItemSnDto.setQueryStartTimeStr("2022-01-01 00:00:00");
        queryItemSnDto.setQueryEndTimeStr("2022-01-02 00:00:00");
        queryItemSnDto.setFactoryId(1L);

        List<SmtSnMtlTracingT> itemSnList = new ArrayList<>();
        itemSnList.add(new SmtSnMtlTracingT());

        // Mocking the dependencies
        // wipScanHistoryService.getTimeRange(queryDto) will return timeDto
        // smtSnMtlTracingTService.getTraceDataBySubSn(queryItemSnDto) will return itemSnList

        List<SmtSnMtlTracingT> result = pmRepairDetailService.getItemSnAndSupInfo(dto);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetItemSnAndSupInfo_WithoutTimeRange() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setSubSn("123");
        dto.setSide("A");
        dto.setItemCode("ITEM1");
        dto.setFactoryId(BigDecimal.ONE);

        WipScanHistoryDTO queryDto = new WipScanHistoryDTO();
        queryDto.setSn("123");

        WipScanHistory timeDto = new WipScanHistory();
        timeDto.setTimeLimitLeftStr(null);
        timeDto.setTimeLimitRightStr(null);

        List<SmtSnMtlTracingT> itemSnList = new ArrayList<>();

        // Mocking the dependencies
        // wipScanHistoryService.getTimeRange(queryDto) will return timeDto
        // smtSnMtlTracingTService.getTraceDataBySubSn(any()) will return itemSnList

        List<SmtSnMtlTracingT> result = pmRepairDetailService.getItemSnAndSupInfo(dto);
        assertNotNull(result);
        assertEquals(0, result.size());
    }
    /*Ended by AICoder, pid:gb2e7437f7n7f80145db097651b96d390ea91eb9*/

    /*Started by AICoder, pid:k0220u998em0e3014b320af3903a778fb678b2f3*/
    @Test(timeout = 8000)
    public void testGetSmtSnMtlTracingTS_EmptyTimeDto() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        WipScanHistory timeDto = new WipScanHistory();
        List<SmtSnMtlTracingT> result = pmRepairDetailService.getSmtSnMtlTracingTS(dto, timeDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test(timeout = 8000)
    public void testGetSmtSnMtlTracingTS_NullTimeDto() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        WipScanHistory timeDto = null;
        List<SmtSnMtlTracingT> result = pmRepairDetailService.getSmtSnMtlTracingTS(dto, timeDto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test(timeout = 8000)
    public void testGetSmtSnMtlTracingTS_NotEmptyTimeDto() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setFactoryId(BigDecimal.ONE);
        WipScanHistory timeDto = new WipScanHistory();
        timeDto.setTimeLimitLeftStr("2024-07-04");
        List<SmtSnMtlTracingT> result = pmRepairDetailService.getSmtSnMtlTracingTS(dto, timeDto);
        assertEquals(new ArrayList<>(), result);
        timeDto.setTimeLimitRightStr("2024-07-04");
        result = pmRepairDetailService.getSmtSnMtlTracingTS(dto, timeDto);
        assertEquals(new ArrayList<>(), result);
    }
    /*Ended by AICoder, pid:k0220u998em0e3014b320af3903a778fb678b2f3*/
    /*Started by AICoder, pid:e060ef19a39ca3b1412709f0d0a3ba318926438f*/
    @Test(expected = MesBusinessException.class)
    public void testGetReplaceSnInfo_RepairInputSwitchIsTrue() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairInputSwitch(true);
        List<PmRepairDetailDTO> expected = new ArrayList<>();
        // expected.add(new PmRepairDetailDTO());
        assertEquals(expected, pmRepairDetailService.getReplaceSnInfo(dto));
    }

    @Test(expected = Exception.class)
    public void testGetReplaceSnInfo_RepairInputSwitchIsFalse() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairInputSwitch(false);
        List<PmRepairDetailDTO> expected = new ArrayList<>();
        // expected.add(new PmRepairDetailDTO());
        assertEquals(expected, pmRepairDetailService.getReplaceSnInfo(dto));
    }
    /*Ended by AICoder, pid:e060ef19a39ca3b1412709f0d0a3ba318926438f*/
    /* Started by AICoder, pid:k7c32ob57awa547147d20a1711e29b88b1c37775 */
    @Test
    public void testGetReplaceSnInfo_RepairInputSwitchTrue() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairInputSwitch(true);
        List<PmRepairDetailDTO> expected = new ArrayList<>();
        // expected.add(new PmRepairDetailDTO());
        dto.setReplaceSn("77888900010");
        dto.setItemSn("77888900010");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_SAME, e.getMessage());
        }
        dto.setItemSn("ZTE77888900011");
        dto.setRepairSn("77888900010");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_SAME, e.getMessage());
        }
        dto.setRepairSn("77888900011");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LOST_BOARD_CENTER, e.getMessage());
        }
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("ZTE200603023362");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setBrandName("barandName");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setSupplierName("barandName");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setCategoryCode("HSF-S");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setItemCode("HSF-S");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setIsLead("HSF-S");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        barcodeExpandDTO.setSourceBatchNo("200603023363");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        barcodeExpandDTO.setSourceBatchNo("200603023363");
        List<BarcodeExpandDTO> barcodeExpandDTOList1 = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO1 = new BarcodeExpandDTO();
        barcodeExpandDTO1.setBarcode("200603023363");
        barcodeExpandDTO1.setParentCategoryName("序列码");
        barcodeExpandDTO1.setSupplierName("SupplierName");
        barcodeExpandDTO1.setBrandName("barandName");
        barcodeExpandDTO1.setSpecModel("SpecModel");
        barcodeExpandDTOList1.add(barcodeExpandDTO1);
        List<String> snList = new ArrayList<>();
        snList.add(barcodeExpandDTO.getSourceBatchNo());
        BarcodeExpandQueryDTO queryDTO=new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(snList);
        when(barcodeCenterRemoteService.expandQueryOneByOne(queryDTO)).thenReturn(barcodeExpandDTOList1);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        barcodeExpandDTO.setSpecModel("barandName");
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType(new BigDecimal("1116"));
        sysLookupValuesDTO.setDescriptionChin("HSF-SS");
        sysLookupValuesDTO.setLookupMeaning("4");
        sysLookupValuesDTO.setAttribute1("4");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(anyObject())).thenReturn(new ArrayList<>());
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(anyObject())).thenReturn(sysLookupValuesDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_ENV_CONVERT_ERROR_BY_DICT, e.getMessage());
        }
        sysLookupValuesDTO.setLookupType(new BigDecimal("1116"));
        sysLookupValuesDTO.setDescriptionChin("HSF-S");
        sysLookupValuesDTO.setLookupMeaning("4");
        sysLookupValuesDTO.setAttribute1("4");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(anyObject())).thenReturn(sysLookupValuesDTOList);
        Map<String, Object> avlMap = new HashMap<>();
        avlMap.put(Constant.STATUS, Constant.S);
        Map<String, Object> finalAvlMap1 = avlMap;
        PowerMockito.when(avlService.getAvlInfo(anyObject())).thenReturn(new ServiceData() {{
            setBo(finalAvlMap1);
        }});
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.AVL_CHECK_FAILED, e.getMessage());
        }
        avlMap = new HashMap<>();
        avlMap.put(Constant.STATUS, Constant.E);
        Map<String, Object> finalAvlMap = avlMap;
        PowerMockito.when(avlService.getAvlInfo(anyObject())).thenReturn(new ServiceData() {{
            setBo(finalAvlMap);
        }});
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENV_CHECK_FAILED, e.getMessage());
        }
        PowerMockito.when(pmRepairRcvDetailService.getTaskEnvAttrBySn(anyObject())).thenReturn(BigDecimal.ONE);
        dto.setItemCode("HSF-S");
        pmRepairDetailService.getReplaceSnInfo(dto);
        Assert.assertNotNull(dto);
        dto.setItemCode("HSF-SS");
        dto.setIsZjSub("N");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_ITEM_CODE_VEIFY_FAILED, e.getMessage());
        }
        dto.setVeriyMsg("Y");
        pmRepairDetailService.getReplaceSnInfo(dto);
        Assert.assertNotNull(dto);
        dto.setVeriyMsg("");
        dto.setIsZjSub("Y");
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(any(), any())).thenReturn(new WipExtendIdentification());
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_FORM_REPLACED_SN_HAS_BOUND, e.getMessage());
        }
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(any(), any())).thenReturn(null);
        List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getItemInfoList(any())).thenReturn(mtlRelatedItemsEntityDTOList);
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_ITEM_CODE_VEIFY_FAILED, e.getMessage());
        }
        dto.setIsZjSub("N");
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(any(), any())).thenReturn(null);
        Assert.assertNotNull(dto);
        dto.setVeriyMsg("Y");
        dto.setIsZjSub("N");
        pmRepairDetailService.getReplaceSnInfo(dto);
        Assert.assertNotNull(dto);
        barcodeExpandDTO.setCategoryCode(Constant.BOARD_SNID);
        when(barcodeCenterRemoteService.expandQueryOneByOne(anyObject())).thenReturn(barcodeExpandDTOList);
        PowerMockito.when(pmRepairRcvDetailService.checkRepairNotReturn(any())).thenReturn("2");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        PowerMockito.when(pmRepairRcvDetailService.checkRepairNotReturn(any())).thenReturn("");
        PowerMockito.when(pmRepairRcvDetailService.checkZJ(any())).thenReturn(true);
        PowerMockito.when(pmRepairRcvDetailService.getZjSn(any())).thenReturn("true");
        pmRepairDetailService.getReplaceSnInfo(dto);
        Assert.assertNotNull(dto);
        PowerMockito.when(pmRepairRcvDetailService.checkZJ(any())).thenReturn(false);
        pmRepairDetailService.getReplaceSnInfo(dto);
        Assert.assertNotNull(dto);

        PowerMockito.when(pmRepairRcvDetailService.checkSnInLock(any(), any())).thenReturn("false");
        try {
            pmRepairDetailService.getReplaceSnInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:k7c32ob57awa547147d20a1711e29b88b1c37775 */
    /*Started by AICoder, pid:1d64153120h5b5b14d7a09f770f3729ed4f337c4*/

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testCheckHbAttr_EmptyEnv() {
        barcodeExpandDTO.setIsLead("key");
        tempMap.put("key",new SysLookupValuesDTO(){{setAttribute1("10");}});
        pmRepairDetailService.checkHbAttr(tempMap, barcodeExpandDTO, repairSnEnvAttr);
        barcodeExpandDTO.setIsLead("key");
        tempMap.clear();
        tempMap.put("key",new SysLookupValuesDTO());
        pmRepairDetailService.checkHbAttr(tempMap, barcodeExpandDTO, repairSnEnvAttr);
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testCheckHbAttr_NegativeEnv() {
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setAttribute1("-1");
        tempMap.put("key", dto);
        barcodeExpandDTO.setIsLead("key");
        pmRepairDetailService.checkHbAttr(tempMap, barcodeExpandDTO, repairSnEnvAttr);
    }

    @Test(timeout = 8000)
    public void testCheckHbAttr_ValidEnv() {
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setAttribute1("1");
        tempMap.put("key", dto);
        barcodeExpandDTO.setIsLead("key");
        pmRepairDetailService.checkHbAttr(tempMap, barcodeExpandDTO, repairSnEnvAttr);
        Assert.assertEquals("1", dto.getAttribute1());
    }
    /*Ended by AICoder, pid:1d64153120h5b5b14d7a09f770f3729ed4f337c4*/

    /*Started by AICoder, pid:e45a279badq365414ab008a680eb39664525be4f*/
    @Test(timeout = 8000)
    public void verifyItemCodeForSys_IsZjSub_Y() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("Y");
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNull(dto.getVeriyMsg());
    }

    @Test(timeout = 8000)
    public void verifyItemCodeForSys_EmptySysLookupTypesDTOList() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("N");
        dto.setRepairProductMstype("Type1");
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNull(dto.getVeriyMsg());
    }

    @Test(timeout = 8000)
    public void verifyItemCodeForSys_NotContainsRepairProductMstype() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("N");
        dto.setRepairProductMstype("Type1");
        List<SysLookupTypesDTO> sysLookupTypesDTOList = Arrays.asList(new SysLookupTypesDTO());
        sysLookupTypesDTOList.get(0).setLookupMeaning("Type2");
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNull(dto.getVeriyMsg());
    }

    @Test(timeout = 8000)
    public void verifyItemCodeForSys_EmptyReplaceSn() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("N");
        dto.setRepairProductMstype("Type1");
        List<SysLookupTypesDTO> sysLookupTypesDTOList = Arrays.asList(new SysLookupTypesDTO());
        sysLookupTypesDTOList.get(0).setLookupMeaning("Type1");
        dto.setReplaceSn("");
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNull(dto.getVeriyMsg());
    }
    /*Ended by AICoder, pid:e45a279badq365414ab008a680eb39664525be4f*/

    /* Started by AICoder, pid:7a5e5uc4afi079214daa09ae604fa6597929c810 */
    @Test(timeout = 8000)
    public void verifyItemCodeForSys() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("炉温名称");
        sysLookupTypesDTO.setLookupMeaning("Y");
        sysLookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(null);
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("N");
        dto.setRepairProductMstype("Type1");
        List<SysLookupTypesDTO> sysLookupTypesDTOList = Arrays.asList(new SysLookupTypesDTO());
        sysLookupTypesDTOList.get(0).setLookupMeaning("Type1");
        dto.setReplaceSn("");
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNotNull(sysLookupValueList);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupValueList);
        pmRepairDetailService.verifyItemCodeForSys(dto);

        Assert.assertNotNull(sysLookupValueList);
        sysLookupValueList.add(new SysLookupTypesDTO(){{setLookupMeaning("Type1");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupValueList);
        try {
            pmRepairDetailService.verifyItemCodeForSys(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPLACE_SN_IS_NULL, e.getMessage());
        }
        Assert.assertNotNull(sysLookupValueList);

        dto.setReplaceSn("sn");
        try {
            pmRepairDetailService.verifyItemCodeForSys(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LOST_BOARD_CENTER, e.getMessage());
        }
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("ZTE200603023362");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any())).thenReturn(barcodeExpandDTOList);
        when(technicalChangeDetailRepository.getListByProdId(anyObject())).thenReturn(null);
        try {
            pmRepairDetailService.verifyItemCodeForSys(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_CODES_BEFORE_AFTER_REPLACEMENT_NOT_SAME, e.getMessage());
        }
        when(technicalChangeDetailRepository.getListByProdId(anyObject())).thenReturn(Arrays.asList(new TechnicalChangeDetailDTO()));
        try {
            pmRepairDetailService.verifyItemCodeForSys(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_HAS_NO_TECHNICAL_UPGRADES, e.getMessage());
        }
        when(technicalChangeExecInfoRepository.selectBySn(anyObject())).thenReturn(Arrays.asList(new TechnicalChangeExecInfo()));
        pmRepairDetailService.verifyItemCodeForSys(dto);
        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:7a5e5uc4afi079214daa09ae604fa6597929c810 */

}