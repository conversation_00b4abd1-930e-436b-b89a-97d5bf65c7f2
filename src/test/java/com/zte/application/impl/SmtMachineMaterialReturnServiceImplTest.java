package com.zte.application.impl;

import com.zte.application.PkCodeHistoryService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.SmtMachineMtlOnlineStandbyService;
import com.zte.common.ConstantInterface;
import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.StorageCenterRemoteService;
import com.zte.interfaces.dto.LinesideStorageLocationInfoDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnQtyDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnQtyResultDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.storage.StorageDetailsDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/12/10 16:54
 */
@PrepareForTest({ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class, HttpClientUtils.class,
        ServiceDataUtil.class, PlanscheduleRemoteService.class, HttpClientUtil.class})
public class SmtMachineMaterialReturnServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtMachineMaterialReturnServiceImpl service;

    @Mock
    private SmtMachineMaterialReturnRepository smtMachineMaterialReturnRepository;

    @Mock
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Mock
    private SmtMachineMtlOnlineStandbyService smtMachineMtlOnlineStandbyService;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Mock
    private PkCodeInfoService pkCodeInfoService;

    @Mock
    private PkCodeHistoryService pkCodeHistoryService;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    private StorageCenterRemoteService storageCenterRemoteService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private ValueOperations<String, String> redisOpsValue;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;
    @Mock
    private Integer maxMaterialReturnTimes;
    @Before
    public void init() {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class,
                ServiceDataUtil.class, HttpClientUtils.class, PlanscheduleRemoteService.class);
    }

    @Test
    public void updateStatusByReelId() throws Exception {
        SmtMachineMaterialReturnDTO dto = new SmtMachineMaterialReturnDTO();
        dto.setWorkOrder("");
        try {
            service.updateStatusByReelId(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(smtMachineMaterialReturnRepository.updateStatusByReelId(any())).thenReturn(1);
        dto.setWorkOrder("7766554-SMT-A5801");
        dto.setCreateUser("00286523");
        dto.setStatus(new BigDecimal("2"));
        dto.setObjectId("7766554321");
        dto.setQty(123);
        Assert.assertTrue(service.updateStatusByReelId(dto) == 1);
        dto.setExternalType(Constant.CP);
        PowerMockito.doNothing().when(pkCodeInfoRepository).updateItemQtyByReelId(any());
        Assert.assertTrue(service.updateStatusByReelId(dto) == 1);
    }

    @Test
    public void threshouldCheck() throws Exception {
        List<SysLookupValuesDTO> listSys =new ArrayList<SysLookupValuesDTO>();
        SysLookupValuesDTO sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal("6927003"));
        sysLookupValuesDTO.setLookupType(new BigDecimal(6927));
        listSys.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
        List<SysLookupValuesDTO> listSys1 =new ArrayList<SysLookupValuesDTO>();
        SysLookupValuesDTO sysLookupValuesDTO1 =new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupCode(new BigDecimal("6927002"));
        sysLookupValuesDTO1.setLookupType(new BigDecimal(6927));
        listSys1.add(sysLookupValuesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys1);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
        SysLookupValuesDTO sysLookupValuesDTO2 =new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupCode(new BigDecimal("6927001"));
        sysLookupValuesDTO2.setLookupType(new BigDecimal(6927));
        listSys1.add(sysLookupValuesDTO2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys1);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
        List<SysLookupValuesDTO> listSys2 =new ArrayList<SysLookupValuesDTO>();
        sysLookupValuesDTO1.setLookupMeaning("100.10");
        listSys2.add(sysLookupValuesDTO1);
        listSys2.add(sysLookupValuesDTO2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys2);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
        sysLookupValuesDTO2.setLookupMeaning("10");
        List<SysLookupValuesDTO> listSys3 =new ArrayList<SysLookupValuesDTO>();
        listSys3.add(sysLookupValuesDTO1);
        listSys3.add(sysLookupValuesDTO2);
        try{
            PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys3);
            Whitebox.invokeMethod(service, "threshouldCheck");
        }catch(Exception e){
            Assert.assertEquals(MessageId.UPPER_AND_LOWER_NOT_INTEGER, e.getMessage());
        }
        sysLookupValuesDTO2.setLookupMeaning("10.10");
        sysLookupValuesDTO1.setLookupMeaning("100");
        List<SysLookupValuesDTO> listSys4 =new ArrayList<SysLookupValuesDTO>();
        listSys4.add(sysLookupValuesDTO1);
        listSys4.add(sysLookupValuesDTO2);
        try{
            PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys4);
            Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
        }catch(Exception e){
            Assert.assertEquals(MessageId.UPPER_AND_LOWER_NOT_INTEGER, e.getMessage());
        }
        sysLookupValuesDTO2.setLookupMeaning("10");
        List<SysLookupValuesDTO> listSys5 =new ArrayList<SysLookupValuesDTO>();
        listSys5.add(sysLookupValuesDTO1);
        listSys5.add(sysLookupValuesDTO2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys5);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "threshouldCheck"));
    }

    @Test
    public void updateQty() throws Exception {
        SmtMachineMaterialReturnQtyDTO dto = new SmtMachineMaterialReturnQtyDTO();
        StorageDetailsDTO flowDtoLast =new StorageDetailsDTO();
        flowDtoLast.setQtyChange(new BigDecimal("10"));
        SmtMachineMaterialReturnQtyResultDTO resultDTO = new SmtMachineMaterialReturnQtyResultDTO();
        dto.setQty(new BigDecimal("30"));
        Assert.assertNull(Whitebox.invokeMethod(service, "updateQty",dto,flowDtoLast,resultDTO));
        flowDtoLast.setQtyChange(new BigDecimal("50"));
        Assert.assertNull(Whitebox.invokeMethod(service, "updateQty",dto,flowDtoLast,resultDTO));
    }

    @Test
    public void checkAndSetSpecifTest() throws Exception {
        List<SysLookupValuesDTO> thresholdList = new ArrayList<>();
        SmtMachineMaterialReturnQtyResultDTO resultDTO = new SmtMachineMaterialReturnQtyResultDTO();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        SmtMachineMaterialReturnQtyDTO dto = new SmtMachineMaterialReturnQtyDTO();
        resultDTO.setSpecifTest("Y");
        Assert.assertNull(Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto));
        resultDTO.setSpecifTest("");
        Assert.assertNull(Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto));
        SysLookupValuesDTO sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal("6927002"));
        sysLookupValuesDTO.setLookupType(new BigDecimal(6927));
        sysLookupValuesDTO.setLookupMeaning("100");

        SysLookupValuesDTO sysLookupValuesDTO1 =new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupCode(new BigDecimal("6927001"));
        sysLookupValuesDTO1.setLookupType(new BigDecimal(6927));
        sysLookupValuesDTO1.setLookupMeaning("10");
        thresholdList.add(sysLookupValuesDTO);
        thresholdList.add(sysLookupValuesDTO1);
        pkCodeInfo.setItemQty(new BigDecimal("30"));
        dto.setQty(new BigDecimal("36"));
        Assert.assertNull(Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto));
        dto.setQty(new BigDecimal("10"));
        Assert.assertNull(Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto));
        dto.setQty(new BigDecimal("50"));
        Assert.assertNull(Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto));
        sysLookupValuesDTO1.setLookupCode(new BigDecimal("6927003"));
        resultDTO.setSpecifTest("");
        try {
            Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesDTO.setLookupCode(new BigDecimal("6927004"));
        try {
            Whitebox.invokeMethod(service, "checkAndSetSpecifTest",thresholdList,resultDTO,pkCodeInfo,dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void updateInfoByReelId() throws Exception {
        String factoryId = "52";
        SmtMachineMaterialReturnQtyDTO dto = new SmtMachineMaterialReturnQtyDTO();
        dto.setReelId("ZTE111");
        dto.setQty(BigDecimal.valueOf(4));
        dto.setLastUpdatedBy("112233");
        StorageDetailsDTO flowDtoLast = new StorageDetailsDTO();
        flowDtoLast.setQtyChange(BigDecimal.valueOf(5));
        PkCodeHistory pkCodeHistory = new PkCodeHistory();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemCode("129202008250156");
        pkCodeInfo.setItemQty(BigDecimal.valueOf(10));
        PowerMockito.when(pkCodeInfoRepository.getItemCodeAndQtyByReelId(any())).thenReturn(pkCodeInfo);
        PowerMockito.doNothing().when(pkCodeInfoRepository).updateItemQtyByReelId(any());
        PowerMockito.doNothing().when(smtMachineMaterialReturnRepository).updateQtyByReelId(any());
        pkCodeHistory.setHistoryId("123aaa");
        pkCodeHistory.setObjectId("ZTE111");
        pkCodeHistory.setWorkOrder("7119859-SMT-A5201");
        pkCodeHistory.setObjectType("ReelId");
        pkCodeHistory.setProgramName("点料机自动修改reelid数量");
        pkCodeHistory.setCurrentQty(BigDecimal.valueOf(4));
        pkCodeHistory.setFactoryId(BigDecimal.valueOf(52));
        pkCodeHistory.setCreateBy("112233");
        pkCodeHistory.setOldItemQty(BigDecimal.valueOf(10));
        pkCodeHistory.setLastUpdatedBy("112233");
        pkCodeHistory.setItemCode("129202008250156");
        PowerMockito.doNothing().when(pkCodeHistoryRepository).insertInfo(any());
        service.updateInfoByReelId(factoryId,dto,flowDtoLast);
        Assert.assertNotNull(pkCodeHistory);
    }

    @Test
    public void verifyReelIdReturnStatus() throws Exception {
        SmtMachineMaterialReturnDTO dto = new SmtMachineMaterialReturnDTO();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        dto.setStatus(new BigDecimal(1));
        Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        dto.setStatus(new BigDecimal(0));
        try {
            Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.UNCONFIRMED_REELID_CANNOT_BE_RETURNED);
        }
        dto.setLocationCode("123");
        List<LinesideStorageLocationInfoDTO> areaInfo = new ArrayList<>();
        LinesideStorageLocationInfoDTO area = new LinesideStorageLocationInfoDTO();
        areaInfo.add(area);
        PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseAndAreaByList(any())).thenReturn(areaInfo);
        try {
            Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.UNCONFIRMED_REELID_CANNOT_BE_RETURNED);
        }
        area.setFeederFlag("Y");
        Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        sysLookupTypesDTO.setLookupMeaning("5");
        pkCodeInfo.setReturnTimes(1);
        Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        pkCodeInfo.setReturnTimes(6);

        try {
            Whitebox.invokeMethod(service, "verifyReelIdReturnStatus",dto,dto,pkCodeInfo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RETURN_TIMES_MORE_THAN_MAX);
        }
    }
}
