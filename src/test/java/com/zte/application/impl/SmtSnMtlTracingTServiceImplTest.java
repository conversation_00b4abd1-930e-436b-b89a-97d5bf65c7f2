package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtSnMtlTracingTDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@PrepareForTest({CommonUtils.class, RedisCacheUtils.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class,
        EmailUtils.class})
public class SmtSnMtlTracingTServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtSnMtlTracingTServiceImpl smtSnMtlTracingTServiceImpl;

    private List<PsWorkOrderDTO> workOrderList;
    private SmtSnMtlTracingTDTO dipItem;
    private SmtSnMtlTracingTDTO otherTraceTypeItem;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        workOrderList = new ArrayList<>();

        PsWorkOrderDTO wo1 = new PsWorkOrderDTO();
        wo1.setCraftSection(Constant.DIP);
        workOrderList.add(wo1);

        PsWorkOrderDTO wo2 = new PsWorkOrderDTO();
        wo2.setCraftSection(Constant.CRAFTSECTION_BACKPLANE);
        workOrderList.add(wo2);

        PsWorkOrderDTO wo3 = new PsWorkOrderDTO();
        wo3.setCraftSection(Constant.PARTS_CRAFT);
        workOrderList.add(wo3);

        PsWorkOrderDTO wo4 = new PsWorkOrderDTO();
        wo4.setCraftSection("OTHERSTUFF");
        workOrderList.add(wo4);

        dipItem = new SmtSnMtlTracingTDTO();
        dipItem.setTraceType(Constant.DIP);

        otherTraceTypeItem = new SmtSnMtlTracingTDTO();
        otherTraceTypeItem.setTraceType("OTHER");

        HashMap<String, Object> lookupMap = new HashMap<>();
        lookupMap.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_TYPE_2868);
        ArrayList<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTOSMT = new SysLookupTypesDTO();
        sysLookupTypesDTOSMT.setLookupMeaning("SMT");
        sysLookupTypesDTOSMT.setAttribute1("SMT-A,SMT-B");
        sysLookupTypesDTOS.add(sysLookupTypesDTOSMT);
        SysLookupTypesDTO sysLookupTypesDTODIP = new SysLookupTypesDTO();
        sysLookupTypesDTODIP.setLookupMeaning("DIP");
        sysLookupTypesDTODIP.setAttribute1("DIP,背板,电源模块");
        sysLookupTypesDTOS.add(sysLookupTypesDTODIP);
        when(centerfactoryRemoteService.getSysLookUpValue(lookupMap)).thenReturn(sysLookupTypesDTOS);
    }

    @Test
    public void testGetFilteredListWithDipTraceType() throws Exception {
        List<PsWorkOrderDTO> result = getFilteredList(workOrderList, dipItem);
        assertEquals(3, result.size());
        assertTrue(result.stream().anyMatch(e -> Constant.DIP.equals(e.getCraftSection())));
        assertTrue(result.stream().anyMatch(e -> Constant.CRAFTSECTION_BACKPLANE.equals(e.getCraftSection())));
        assertTrue(result.stream().anyMatch(e -> Constant.PARTS_CRAFT.equals(e.getCraftSection())));
    }

    @Test
    public void testGetFilteredListWithOtherTraceType() throws Exception {
        // 注意：这里应该传递otherTraceTypeItem作为第二个参数
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl, "getFilteredList", workOrderList, otherTraceTypeItem));
        // 由于workOrderList中没有craftSection以"OTHER"开头的对象，所以结果应该是空的
//        assertEquals(1, result.size());
//        // 额外的验证：确保结果不包含任何workOrderList中的对象
//        for (PsWorkOrderDTO wo : workOrderList) {
//            assertFalse(result.contains(wo));
//        }
    }

    private List<PsWorkOrderDTO> getFilteredList(List<PsWorkOrderDTO> workOrderList, SmtSnMtlTracingTDTO item) throws Exception {
        return Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"getFilteredList", workOrderList, dipItem);
    }

    @Test
    public void testSetWorkOrderEmpty2() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setProductBatchCode("batch123");
        item.setTraceType("DIP"); // 或者其他非PCB的追溯类型
        item.setWorkOrder("");
        when(PlanscheduleRemoteService.getWorkOrderInfo("batch123")).thenReturn(Collections.emptyList());

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        Assert.assertNull(null);
    }

    @Test
    public void testSetWorkOrderEmpty1() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        Assert.assertNull(item.getWorkOrder());
    }


    @Test
    public void testSetWorkOrder_OtherTraceType_NoMatchingWorkOrder() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setProductBatchCode("batch123");
        item.setTraceType("DIP"); // 或者其他非PCB的追溯类型
        item.setWorkOrder("");

        PsWorkOrderDTO workOrderDTO = new PsWorkOrderDTO();
        workOrderDTO.setWorkOrderNo("WO789");
        workOrderDTO.setCraftSection("其他");

        List<PsWorkOrderDTO> workOrderList = Collections.singletonList(workOrderDTO);

        when(PlanscheduleRemoteService.getWorkOrderInfo("batch123")).thenReturn(workOrderList);

        try {
            Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        }catch (MesBusinessException e){
            assertEquals(MessageId.TRACE_TYPE_HAS_NO_WORK_ORDER, e.getExMsgId());
        }
    }

    @Test
    public void testSetWorkOrder_OtherTraceType_Success() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setProductBatchCode("batch123");
        item.setTraceType("DIP"); // 或者其他非PCB的追溯类型
        item.setWorkOrder("");

        PsWorkOrderDTO workOrderDTO1 = new PsWorkOrderDTO();
        workOrderDTO1.setWorkOrderNo("WO456");
        workOrderDTO1.setCraftSection("DIP");

        PsWorkOrderDTO workOrderDTO2 = new PsWorkOrderDTO();
        workOrderDTO2.setWorkOrderNo("WO789");
        workOrderDTO2.setCraftSection("其他");

        List<PsWorkOrderDTO> workOrderList = Arrays.asList(workOrderDTO1, workOrderDTO2);

        when(PlanscheduleRemoteService.getWorkOrderInfo("batch123")).thenReturn(workOrderList);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);

        assertNotNull(item.getInWorkOrder());
    }

    @Test
    public void testSetWorkOrder_PCBTraceType_NullWorkOrder() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setProductBatchCode("batch123");
        item.setTraceType(Constant.PCB);
        item.setWorkOrder("");

        when(PlanscheduleRemoteService.queryFirstWorkOrderByProdplanId("batch123")).thenReturn(null);

        try {
            Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        }catch (MesBusinessException e){
            assertEquals(MessageId.FAILED_TO_GET_BATCH_FIRST_COMMAND, e.getExMsgId());
        }
    }


    @Test
    public void testSetWorkOrder_PCBTraceType_Success() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setProductBatchCode("batch123");
        item.setTraceType(Constant.PCB);
        item.setWorkOrder("");

        PsWorkOrderBasicDTO psWorkOrderBasicDTO = new PsWorkOrderBasicDTO();
        psWorkOrderBasicDTO.setWorkOrderNo("WO123");

        when(PlanscheduleRemoteService.queryFirstWorkOrderByProdplanId("batch123")).thenReturn(psWorkOrderBasicDTO);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);

        assertNotNull(item.getInWorkOrder());
    }

    @Test
    public void testSetWorkOrder_TraceTypeIsDIPAndWorkOrderListIsNotEmpty() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 当 traceType 为 "DIP_FLAG" 并且 workOrderList 不为空时，应该筛选并设置 inWorkOrder
        item.setProductBatchCode("batchCode");
        item.setWorkOrder("11");
        item.setTraceType(Constant.DIP_FLAG);

        PsWorkOrderDTO mockOrder1 = new PsWorkOrderDTO();
        mockOrder1.setCraftSection("BACKPLANE");
        mockOrder1.setWorkOrderNo("workOrder1");
        List<PsWorkOrderDTO> mockList = Arrays.asList(mockOrder1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(any())).thenReturn(mockList);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        Assert.assertTrue(true);// 验证 setInWorkOrder 被调用
    }

}
