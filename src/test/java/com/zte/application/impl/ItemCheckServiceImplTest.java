package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.zte.application.ItemCheckinfoDetailService;
import com.zte.domain.model.ItemCheckInfoDetail;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ItemCheckDifferenceDTO;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({RedisHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,AsyncExportFileCommonService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class, RedisLock.class, EasyExcelFactory.class})
public class ItemCheckServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ItemCheckServiceImpl service;

    @Mock
    ItemCheckinfoDetailService itemCheckinfoDetailService;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    AsyncExportFileCommonService asyncExportFileCommonService;

    @Test
    public void exportExcel() throws Exception{
        PowerMockito.mockStatic(AsyncExportFileCommonService.class);
        HttpServletResponse response = null;
        ItemCheckDifferenceDTO dto = new ItemCheckDifferenceDTO();
        String prodPlanId = "7111333";
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        List<ItemCheckDifferenceDTO> totalRecordList = new ArrayList<>();
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(any(), any(), any())).thenReturn(totalRecordList);
        totalRecordList.add(itemCheckDifferenceDTO);
        dto.setProdPlanId("7111333");
        dto.setTaskNo("test");
        dto.setPage(1);
        dto.setRows(10);
        List<ItemCheckDifferenceDTO> checkFinishList = new ArrayList<>();
        ItemCheckDifferenceDTO itemCheckDifferenceDTO1 = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO1.setItemTotalQty(new BigDecimal("1"));
        itemCheckDifferenceDTO1.setItemTrayQty(1);
        checkFinishList.add(itemCheckDifferenceDTO1);
        PowerMockito.when(itemCheckinfoDetailService.getCheckQtyGroupByItemNo(any())).thenReturn(checkFinishList);
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(any(), any(), any())).thenReturn(checkFinishList);

        List<ItemCheckInfoDetail> itemCheckInfoDetails = new ArrayList<>();
        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        itemCheckInfoDetail.setCheckStatus("1");
        itemCheckInfoDetail.setLastUpdatedBy("1111");
        itemCheckInfoDetail.setItemNo("123");
        ItemCheckInfoDetail itemCheckInfoDetail1 = new ItemCheckInfoDetail();
        itemCheckInfoDetail1.setCheckStatus("0");
        itemCheckInfoDetail1.setLastUpdatedBy("2222");
        itemCheckInfoDetail1.setItemNo("123");
        itemCheckInfoDetails.add(itemCheckInfoDetail);
        itemCheckInfoDetails.add(itemCheckInfoDetail1);
        PowerMockito.when(itemCheckinfoDetailService.getPageByInfo(any(), any(), any())).thenReturn(itemCheckInfoDetails);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmDto = new HrmPersonInfoDTO();
        hrmDto.setEmpName("1111");
        hrmPersonInfoDTOMap.put("1111", hrmDto);
        hrmPersonInfoDTOMap.put("2222",null);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

}