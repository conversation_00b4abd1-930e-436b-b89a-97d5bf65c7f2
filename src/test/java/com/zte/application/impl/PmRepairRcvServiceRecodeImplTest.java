package com.zte.application.impl;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({BasicsettingRemoteService.class,NumConstant.class, MicroServiceRestUtil.class,ServiceDataBuilderUtil.class
        ,ObtainRemoteServiceDataUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class})
public class PmRepairRcvServiceRecodeImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairRcvServiceRecodeImpl serviceRecode;

    @Mock
    PmRepairRcvRepository pmRepairRcvRepository;

    @Mock
    PsWipInfoService psWipInfoService;
    @Mock
    private PmRepairRcvServiceImpl pmRepairRcvService;
    @Mock
    private PsScanHistoryServiceImpl scanHistoryServiceImpl;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    ExcelWriter writer;
    @Mock
    WriteSheet build;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    ExcelWriterBuilder writerBuilder;
    @Mock
    private ObjectMapper objectMapper;

    @Before
    public void init() {
        mockStatic(MicroServiceRestUtil.class);
        mockStatic(ServiceDataBuilderUtil.class);
        mockStatic(BasicsettingRemoteService.class);
        mockStatic(HttpClientUtil.class);
        mockStatic(JacksonJsonConverUtil.class);
    }


    @Test
    public void test_sendRepairDQAS() throws Exception {
        Map<String, Object> mapPost = new HashMap<>();
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(null);
        try {
            serviceRecode.sendRepairDQAS(mapPost);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.REPAIR_ZS_VAILDATE_FAIL,e.getExMsgId());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://sdafasdsad");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        when(HttpClientUtil.httpPostWithJSON(anyString(),anyString(),anyMap())).thenReturn("{}");
        try {
            serviceRecode.sendRepairDQAS(mapPost);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG,e.getExMsgId());
        }
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(objectMapper);
        when(objectMapper.readTree(anyString())).thenReturn(null);
        try {
            serviceRecode.sendRepairDQAS(mapPost);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.REPAIR_ZS_VAILDATE_FAIL,e.getExMsgId());
        }
        when(HttpClientUtil.httpPostWithJSON(anyString(),anyString(),anyMap())).thenReturn("{\"msg\": {\"code\": \"0\", \"msg\": \"success\"}}");
        ObjectMapper realMapper = new ObjectMapper();
        ObjectNode mockJsonNode = realMapper.createObjectNode();
        mockJsonNode.put("code", "0");
        mockJsonNode.put("msg", "success");
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(objectMapper);
        when(objectMapper.readTree(anyString())).thenReturn(mockJsonNode);
        try {
            serviceRecode.sendRepairDQAS(mapPost);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.REPAIR_ZS_VAILDATE_FAIL,e.getExMsgId());
        }
    }

    @Test
    public void test_insertMachine() throws Exception {
        List<SemiManufactureDealInfo> machineList = new ArrayList<>();
        serviceRecode.insertMachine(machineList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_sendRepairNew() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setPageType(new BigDecimal("1"));
        record.setFromStation("单板生产");
        when(psWipInfoRepository.getWorkOrderNoEmptyWipInfoBySns(any())).thenReturn(new ArrayList<>());
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new ArrayList<>();
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setOperationType("delete");
        pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
        when(psWipInfoService.getWipInfoList(anyList())).thenReturn(new ArrayList<>());
        when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(null);
        when(pmRepairRcvDetailService.deletePmRepairRcvDetailByReceptionDetailId(anyList(),anyInt())).thenReturn(1);
        List<PmRepairRcvDetail> detailList = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setItemCode("123456789123");
        detailList.add(pmRepairRcvDetail);
        when(pmRepairRcvDetailRepository.getDetailList(any())).thenReturn(detailList);
        PsWipInfo currWip = new PsWipInfo();
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        PowerMockito.doNothing().when(pmRepairRcvService).updateBatch(any());
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(anyList(),anyInt())).thenReturn(1);
        serviceRecode.sendRepairNew(record, pmRepairRcvDetailDTOs);
        Assert.assertTrue(true);
    }

    @Test
    public void test_returnCraftSection3() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("整机生产维修");
        List<PmRepairRcvDetail> detailDeleteList = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setItemCode("123456789123");
        detailDeleteList.add(pmRepairRcvDetail);
        PsWipInfo currWip = new PsWipInfo();
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        PowerMockito.doNothing().when(pmRepairRcvService).updateBatch(any());
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(anyList(),anyInt())).thenReturn(1);
        Whitebox.invokeMethod(serviceRecode,"returnCraftSection", record,detailDeleteList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_returnCraftSection2() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("整机单板送修");
        List<PmRepairRcvDetail> detailDeleteList = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        detailDeleteList.add(pmRepairRcvDetail);
        PsWipInfo currWip = new PsWipInfo();
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        PowerMockito.doNothing().when(pmRepairRcvService).updateBatch(any());
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(anyList(),anyInt())).thenReturn(1);
        Whitebox.invokeMethod(serviceRecode,"returnCraftSection", record,detailDeleteList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_returnCraftSection1() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("整机生产维修");
        List<PmRepairRcvDetail> detailDeleteList = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setItemCode("123456789123ABC");
        detailDeleteList.add(pmRepairRcvDetail);
        PsWipInfo currWip = new PsWipInfo();
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        PowerMockito.doNothing().when(pmRepairRcvService).updateBatch(any());
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(anyList(),anyInt())).thenReturn(1);
        Whitebox.invokeMethod(serviceRecode,"returnCraftSection", record,detailDeleteList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_returnCraftSection() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("单板生产");
        List<PmRepairRcvDetail> detailDeleteList = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        detailDeleteList.add(pmRepairRcvDetail);
        PsWipInfo currWip = new PsWipInfo();
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        PowerMockito.doNothing().when(pmRepairRcvService).updateBatch(any());
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(anyList(),anyInt())).thenReturn(1);
        Whitebox.invokeMethod(serviceRecode,"returnCraftSection", record,detailDeleteList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_getProcessMap() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getProcessInfo(anyString())).thenReturn(Collections.emptyList());
        PowerMockito.doNothing().when(pmRepairRcvService).verification(anyList(), anyMap());
        serviceRecode.getProcessMap();
        Assert.assertTrue(true);
    }

    /* Started by AICoder, pid:u3a7450e2bn587b1436b0a4880247c204d6759c4 */
    @Test
    public void insertMachine(){
        serviceRecode.insertMachine(null, false);
        List<SemiManufactureDealInfo> machineList = new LinkedList<>();
        for (int i = 0; i < 5; i++) {
            SemiManufactureDealInfo aa1 = new SemiManufactureDealInfo();
            machineList.add(aa1);
        }
        serviceRecode.insertMachine(machineList, false);
        serviceRecode.insertMachine(machineList, true);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:u3a7450e2bn587b1436b0a4880247c204d6759c4 */

    @Test
    public void testCheckSnProcessChange() throws Exception {
        Whitebox.invokeMethod(serviceRecode, "checkSnProcessChange", new ArrayList<>());
        List<PsWipInfoDTO> wipInfoList = new ArrayList<>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("1234");
        psWipInfoDTO.setCurrProcessCode("1234");
        wipInfoList.add(psWipInfoDTO);
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new ArrayList<>();
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setSn("1233");
        pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
        when(psWipInfoService.getWipInfoList(any())).thenReturn(wipInfoList);
        Whitebox.invokeMethod(serviceRecode, "checkSnProcessChange",pmRepairRcvDetailDTOs);
        pmRepairRcvDetailDTO.setSn("1234");
        try {
            Whitebox.invokeMethod(serviceRecode, "checkSnProcessChange", pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.REPAIR_SN_PROCESS_CHANGE);
        }
    }

    @Test
    public void updateWipInfo() {
        mockStatic(NumConstant.class);
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("test123");
        Map<String, String> processMap = new HashMap<>();
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new ArrayList<>();
        try {
            serviceRecode.updateWipInfo(record,processMap,pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setSn("test123");
        pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
        PsWipInfo currWip = new PsWipInfo();
        currWip.setSn("test123");
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pmRepairRcvService).updateBatch(any());
        try {
            record.setFromStation("单板生产");
            serviceRecode.updateWipInfo(record,processMap,pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void returnCraftSection() {
        mockStatic(NumConstant.class);
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("test123");
        Map<String, String> processMap = new HashMap<>();
        List<PmRepairRcvDetail> pmRepairRcvDetailDTOs = new ArrayList<>();
        try {
            serviceRecode.returnCraftSection(record,pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PmRepairRcvDetail pmRepairRcvDetailDTO = new PmRepairRcvDetail();
        pmRepairRcvDetailDTO.setSn("test123");
        pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
        PsWipInfo currWip = new PsWipInfo();
        currWip.setSn("test123");
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pmRepairRcvService).updateBatch(any());
        try {
            record.setFromStation("单板生产");
            serviceRecode.returnCraftSection(record,pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
}
