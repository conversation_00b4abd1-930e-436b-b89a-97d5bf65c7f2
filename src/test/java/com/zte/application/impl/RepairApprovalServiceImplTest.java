package com.zte.application.impl;

import com.zte.application.DocFilePropertiesService;
import com.zte.application.PsWipInfoService;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.enums.RepairApprovalStatusEnum;
import com.zte.common.enums.RepairApprovalTypeEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.RepairApproval;
import com.zte.domain.model.RepairApprovalRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.iss.approval.sdk.bean.ApprovalNodeInfoDTO;
import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.iss.approval.sdk.bean.ApproverInfo;
import com.zte.iss.approval.sdk.bean.ProcessBusinessIdDTO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.*;

/**
 * 生产管理-维修异常审批表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-25 09:32:11
 */
@PrepareForTest({BasicsettingRemoteService.class,MESHttpHelper.class,ApprovalFlowClient.class})
public class RepairApprovalServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private RepairApprovalServiceImpl service;
    @Mock
    private RepairApprovalRepository repository;
    /* Started by AICoder, pid:hb4b9g8ea095997141490b2900b18e14a371d147 */
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private DocFilePropertiesService docFilePropertiesService;
    /* Ended by AICoder, pid:hb4b9g8ea095997141490b2900b18e14a371d147 */

    @Mock
    private ConstantInterface constantInterface;

    /* Started by AICoder, pid:cb4b9x8ea0n5997141490b2900b18e04a374d147 */
    @Before
    public void setUp() {
        // Initialize any required setup here
        mockStatic(BasicsettingRemoteService.class);
        mockStatic(MESHttpHelper.class);
        mockStatic(ApprovalFlowClient.class);

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put(Constant.X_EMP_NO_SMALL, "10349620");
        headerMap.put(Constant.X_FACTORY_ID, "55");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
    }
    /* Ended by AICoder, pid:cb4b9x8ea0n5997141490b2900b18e04a374d147 */

    @Test
    public void testQueryPage() throws Exception {
        when(repository.selectPage(any())).thenReturn(new ArrayList<>());
        Page<RepairApprovalDTO> query = new Page<>();
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("createdDateStart", new Date().toString());
        params.put("createdDateEnd", new Date().toString());
        query.setParams(params);
        assertNotNull(service.queryPage(query));
    }

    @Test
    public void testCountExportTotal() {
        when(repository.selectCount(any())).thenReturn(1);
        assertNotNull(service.countExportTotal(new Page<>()));
    }

    @Test
    public void testQueryExportData() {
        when(repository.selectPage(any())).thenReturn(new ArrayList<>());
        assertNotNull(service.queryExportData(new Page<>(), 1, 10));
    }

    @Test
    public void queryPageTest() throws Exception {
        Page<RepairApprovalDTO> query = new Page<>();
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("createdDateStart", new Date().toString());
        params.put("createdDateEnd", new Date().toString());
        query.setParams(params);
        List<RepairApprovalDTO> rows = new ArrayList<>();
        RepairApprovalDTO dto = new RepairApprovalDTO();
        dto.setId("1");
        dto.setStatus(RepairApprovalStatusEnum.APPROVED.name());
        dto.setApprovalType("1");
        rows.add(dto);

        when(repository.selectPage(query)).thenReturn(rows);
        when(docFilePropertiesService.getList(any())).thenReturn(new ArrayList<>());

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>(Collections.emptyMap());
        when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        PageRows<RepairApprovalDTO> result = service.queryPage(query);

        assertNotNull(result);
        assertEquals(1, result.getRows().size());
        assertEquals(RepairApprovalStatusEnum.APPROVED.getChineseDescription(), result.getRows().get(0).getStatusDesc());
        assertEquals(RepairApprovalTypeEnum.getDescriptionByOrdinal(1), result.getRows().get(0).getApprovalTypeDesc());

        dto.setCreatedBy("10349620");
        dto.setLastUpdatedBy("10349620");
        hrmPersonInfoDTOMap.put("10349620", new HrmPersonInfoDTO());
        service.queryPage(query);
    }

    @Test
    public void countExportTotalTest() {
        Page<RepairApprovalDTO> query = new Page<>();
        RepairApprovalDTO params = new RepairApprovalDTO();
        query.setParams(params);

        when(repository.selectCount(any())).thenReturn(10);

        Integer result = service.countExportTotal(query);

        assertEquals(10, result.intValue());
    }

    @Test
    public void submitTest() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode("SN123");
        repairApprovalDTO.setApprovalType("1");
        repairApprovalDTO.setPositionNumber("P1");
        repairApprovalDTO.setApplicationReason("Reason");
        repairApprovalDTO.setCreatedBy("10349620");
        repairApprovalDTO.setLastUpdatedBy("10349620");

        ArrayList<ApprovalProcessInfoEntityDTO> approvalOperateList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setApproverId("10349620");
        approvalProcessInfoEntityDTO.setSeq(0);
        approvalOperateList.add(approvalProcessInfoEntityDTO);
        repairApprovalDTO.setApprovalOperator1(Collections.singletonList("10349620"));

        ArrayList<DocFilePropertiesEntityDTO> fileList = new ArrayList<>();
        fileList.add(new DocFilePropertiesEntityDTO());
        repairApprovalDTO.setFileList(fileList);

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setItemNo("item1");
        when(psWipInfoService.getWipInfoBySn("SN123")).thenReturn(wipInfo);

        Page<BsBomHierarchicalDetail> bomPage = new Page<>();
        List<BsBomHierarchicalDetail> bomDetails = new ArrayList<>();
        BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
        detail.setBomCode("item1");
        detail.setTagNum("P1");
        bomDetails.add(detail);
        bomPage.setRows(bomDetails);

        when(BasicsettingRemoteService.selectBsBomHierarchicalByPage(any())).thenReturn(bomPage);

        when(constantInterface.getUrl(InterfaceEnum.downLoadFileByKey)).thenReturn("http://example.com");

        String result = service.submit(repairApprovalDTO);

        assertNotNull(result);
        verify(repository, times(1)).insert(any());

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>(Collections.emptyMap());
        when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        hrmPersonInfoDTOMap.put("10349620", new HrmPersonInfoDTO());

        repairApprovalDTO.setFileList(new ArrayList<>());
        repairApprovalDTO.setPositionNumber("Q1");
        assertThrows(MessageId.POSITION_CODE_NOT_EXIST, MesBusinessException.class, () -> service.submit(repairApprovalDTO));

        repairApprovalDTO.setApprovalType("0");
        repairApprovalDTO.setApprovalOperator1(null);
        repairApprovalDTO.setApprovalOperator2(Collections.singletonList("10349620"));
        service.submit(repairApprovalDTO);

//        approvalProcessInfoEntityDTO.setApproverId(null);
        repairApprovalDTO.setApprovalOperator1(Collections.singletonList("10349620"));
        repairApprovalDTO.setApprovalOperator2(Collections.singletonList("10349620"));
        assertThrows(MessageId.EXIST_REPEAT_APPROVER, MesBusinessException.class, () -> service.submit(repairApprovalDTO));
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_SnNotExist() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode("SN123");

        when(psWipInfoService.getWipInfoBySn("SN123")).thenReturn(null);

        service.submit(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_PositionNotExist() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode("SN123");
        repairApprovalDTO.setApprovalType("1");
        repairApprovalDTO.setPositionNumber("P1");

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setItemNo("item1");
        when(psWipInfoService.getWipInfoBySn("SN123")).thenReturn(wipInfo);

        Page<BsBomHierarchicalDetail> bomPage = new Page<>();
        bomPage.setRows(new ArrayList<>());

        when(BasicsettingRemoteService.selectBsBomHierarchicalByPage(any())).thenReturn(bomPage);

        service.submit(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_PositionNotExist_1() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode("SN123");
        repairApprovalDTO.setApprovalType("1");
        repairApprovalDTO.setPositionNumber("P1");

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setItemNo("item1");
        when(psWipInfoService.getWipInfoBySn("SN123")).thenReturn(wipInfo);

        Page<BsBomHierarchicalDetail> bomPage = new Page<>();
//        bomPage.setRows(new ArrayList<>());

        when(BasicsettingRemoteService.selectBsBomHierarchicalByPage(any())).thenReturn(bomPage);

        service.submit(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_PositionNotExist_2() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setBarcode("SN123");
        repairApprovalDTO.setApprovalType("1");
        repairApprovalDTO.setPositionNumber("P1");

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setItemNo("item1");
        when(psWipInfoService.getWipInfoBySn("SN123")).thenReturn(wipInfo);

        Page<BsBomHierarchicalDetail> bomPage = new Page<>();
        bomPage.setRows(new ArrayList<>());

        when(BasicsettingRemoteService.selectBsBomHierarchicalByPage(any())).thenReturn(null);

        service.submit(repairApprovalDTO);
    }

    @Test
    public void uploadFileTest() throws Exception {
        MultipartFile file = mock(MultipartFile.class);
        when(file.isEmpty()).thenReturn(false);
        when(file.getOriginalFilename()).thenReturn("test.txt");
        when(file.getSize()).thenReturn(1024L);

        when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("docId");

        DocFilePropertiesEntityDTO result = service.uploadFile(file, "10349620");

        assertNotNull(result);
        assertEquals("docId", result.getDocId());

        when(file.getOriginalFilename()).thenReturn("");
        service.uploadFile(file, "10349620");
    }

    @Test
    public void uploadFileTest_EmptyFile() throws Exception {
        MultipartFile file = mock(MultipartFile.class);
        when(file.isEmpty()).thenReturn(true);

        DocFilePropertiesEntityDTO result = service.uploadFile(file, "10349620");

        assertNotNull(result);
        assertNull(result.getDocId());
    }

    @Test
    public void withdrawTest() {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        RepairApproval approval = new RepairApproval();
        approval.setId("1");
        approval.setCreatedBy("10349620");
        approval.setStatus(RepairApprovalStatusEnum.APPROVING.name());

        when(repository.getByCondition(any())).thenReturn(Collections.singletonList(approval));

        String result = service.withdraw(repairApprovalDTO);

        assertEquals("1", result);
        verify(repository, times(1)).updateById(any());
    }

    @Test(expected = MesBusinessException.class)
    public void withdrawTest_IdNull() {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();

        service.withdraw(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void withdrawTest_ApprovalNotFound() {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        when(repository.getByCondition(any())).thenReturn(Collections.emptyList());

        service.withdraw(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void withdrawTest_NotCreatedBy() {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        RepairApproval approval = new RepairApproval();
        approval.setId("1");
        approval.setCreatedBy("emp2");
        approval.setStatus(RepairApprovalStatusEnum.APPROVING.name());

        when(repository.getByCondition(any())).thenReturn(Collections.singletonList(approval));

        service.withdraw(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void withdrawTest_StatusErrpr() {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        RepairApproval approval = new RepairApproval();
        approval.setId("1");
        approval.setCreatedBy("emp2");
        approval.setStatus(RepairApprovalStatusEnum.CANCELLED.name());

        when(repository.getByCondition(any())).thenReturn(Collections.singletonList(approval));

        service.withdraw(repairApprovalDTO);
    }

    @Test
    public void updateBillStatusForApprovalTest() {
        ApprovalCenterCallbackDealDTO callbackDealDTO = new ApprovalCenterCallbackDealDTO();
        callbackDealDTO.setBillNo("1");
        callbackDealDTO.setApprovalStatus(Constant.ApprovalStatus.AGREE);
        callbackDealDTO.setApprover("10349620");

        RepairApproval approval = new RepairApproval();
        approval.setId("1");

        when(repository.getByCondition(any())).thenReturn(Collections.singletonList(approval));

        service.updateBillStatusForApproval(callbackDealDTO);
        verify(repository, times(1)).updateById(any());

        callbackDealDTO.setApprovalStatus(Constant.ApprovalStatus.REFUSE);
        service.updateBillStatusForApproval(callbackDealDTO);

        callbackDealDTO.setApprovalStatus(Constant.ApprovalStatus.TRANSFER);
        service.updateBillStatusForApproval(callbackDealDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void updateBillStatusForApprovalTest_BillNoNull() {
        ApprovalCenterCallbackDealDTO callbackDealDTO = new ApprovalCenterCallbackDealDTO();

        service.updateBillStatusForApproval(callbackDealDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void updateBillStatusForApprovalTest_ApprovalNotFound() {
        ApprovalCenterCallbackDealDTO callbackDealDTO = new ApprovalCenterCallbackDealDTO();
        callbackDealDTO.setBillNo("1");

        when(repository.getByCondition(any())).thenReturn(Collections.emptyList());

        service.updateBillStatusForApproval(callbackDealDTO);
    }

    @Test
    public void queryApprovalStatusTest() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        RepairApproval approval = new RepairApproval();
        approval.setId("1");
        approval.setStatus(RepairApprovalStatusEnum.CANCELLED.name());

        when(repository.getByCondition(any())).thenReturn(Collections.singletonList(approval));

        ApprovalNodeInfoDTO nodeInfoDTO = new ApprovalNodeInfoDTO();
        nodeInfoDTO.setNodeName("Node1");
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setResult(Constant.FLAG_Y);
        approverInfo.setApproverId("10349620");
        List<ApproverInfo> approverList = new ArrayList<>();
        approverList.add(approverInfo);
        nodeInfoDTO.setApproverList(approverList);

        List<ApprovalProcessDTO> approvalProcessDTOS = new ArrayList<>();
        ApprovalProcessDTO processDTO = new ApprovalProcessDTO();
        processDTO.setFlowInstanceId("flow1");
        approvalProcessDTOS.add(processDTO);

        when(ApprovalFlowClient.queryProcess(any(ProcessBusinessIdDTO.class))).thenReturn(approvalProcessDTOS);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOS = new ArrayList<>();
        approvalNodeInfoDTOS.add(nodeInfoDTO);
        when(ApprovalFlowClient.showFlowInstancePanorama(any())).thenReturn(approvalNodeInfoDTOS);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>(Collections.emptyMap());
        when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        hrmPersonInfoDTOMap.put("10349620", new HrmPersonInfoDTO());

        Map<String, ArrayList<ApproverInfo>> result = service.queryApprovalStatus(repairApprovalDTO);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Node1", result.keySet().iterator().next());
        assertEquals(1, result.values().iterator().next().size());

        ApproverInfo approverInfo1 = new ApproverInfo();
        approverInfo1.setResult(Constant.FLAG_N);
        approverList.add(approverInfo1);
        ApproverInfo approverInfo2 = new ApproverInfo();
        approverInfo2.setResult(Constant.FLAG_ON_LOWERCASE);
        approverList.add(approverInfo2);
        service.queryApprovalStatus(repairApprovalDTO);

        approvalNodeInfoDTOS.clear();
        service.queryApprovalStatus(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void queryApprovalStatusTest_IdNull() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();

        service.queryApprovalStatus(repairApprovalDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void queryApprovalStatusTest_ApprovalNotFound() throws Exception {
        RepairApprovalDTO repairApprovalDTO = new RepairApprovalDTO();
        repairApprovalDTO.setId("1");

        when(repository.getByCondition(any())).thenReturn(Collections.emptyList());

        service.queryApprovalStatus(repairApprovalDTO);
    }

    @Test
    public void testGetByCondition() {
        when(repository.getByCondition(any())).thenReturn(new ArrayList<RepairApproval>());
        assertNotNull(service.getByCondition(new RepairApprovalDTO()));
    }
}

