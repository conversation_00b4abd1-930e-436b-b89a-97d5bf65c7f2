package com.zte.application.impl;

import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.SnAttrInfo;
import com.zte.domain.model.SnAttrInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.ContainerInfoDTO;
import com.zte.interfaces.dto.SnAttrInfoEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class,
		HttpRemoteService.class,ProductionDeliveryRemoteService.class})
public class SnAttrInfoServiceImplTest extends PowerBaseTestCase {

    @Mock
    private SnAttrInfoRepository snAttrInfoRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @InjectMocks
    private SnAttrInfoServiceImpl snAttrInfoService;

	@Test
	public void getSnByLpnOrOriginalLpn () throws Exception{
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		ContainerInfoDTO record = new ContainerInfoDTO();
		record.setLpn("TEST");
		record.setPage(1);
		record.setRows(100);
		List<ContainerContentInfoDTO> list = new ArrayList<>();
		list.add(new ContainerContentInfoDTO());
		Page<ContainerContentInfoDTO> page = new Page<>();
		page.setRows(list);
		PowerMockito.when(ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(Mockito.any())).thenReturn(page);

		List<SnAttrInfoEntityDTO> infoList = new ArrayList<>();
		SnAttrInfoEntityDTO snAttrInfoEntityDTO = new SnAttrInfoEntityDTO();
		snAttrInfoEntityDTO.setSn("TEST");
		snAttrInfoEntityDTO.setEnCode("TEST");
		snAttrInfoEntityDTO.setSnCode("TEST");
		snAttrInfoEntityDTO.setMac("TEST");
		infoList.add(snAttrInfoEntityDTO);
		PowerMockito.when(snAttrInfoRepository.getEnInfoByEnCode(Mockito.anyList())).thenReturn(infoList);

		snAttrInfoService.getSnByLpnOrOriginalLpn(record);

		ContainerInfoDTO record1 = new ContainerInfoDTO();
		record1.setOriginalLpn("TEST");
		record1.setPage(1);
		record1.setRows(100);
		PowerMockito.when(ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(Mockito.any())).thenReturn(page);
		PowerMockito.when(snAttrInfoRepository.getEnInfoByEnCode(Mockito.anyList())).thenReturn(infoList);
		snAttrInfoService.getSnByLpnOrOriginalLpn(record1);

		ContainerInfoDTO record2 = new ContainerInfoDTO();
		record2.setOriginalLpn("TEST");
		record2.setLpn("TEST");
		record2.setPage(1);
		record2.setRows(100);

		PowerMockito.when(ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(Mockito.any())).thenReturn(page);
		PowerMockito.when(snAttrInfoRepository.getEnInfoByEnCode(Mockito.anyList())).thenReturn(infoList);
		Assert.assertNotNull(snAttrInfoService.getSnByLpnOrOriginalLpn(record2));

	}

	@Test
	public void getSnByLpnOrOriginalLpnTwo () throws Exception{
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		ContainerInfoDTO record3 = new ContainerInfoDTO();
		record3.setOriginalLpn("TEST");
		record3.setLpn("TEST");
		record3.setPage(1);

		try{
			snAttrInfoService.getSnByLpnOrOriginalLpn(record3);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PAGING_QUERY_PARAMETERS_IS_EMPTY);
		}
		ContainerInfoDTO record7 = new ContainerInfoDTO();
		record7.setOriginalLpn("TEST");
		record7.setLpn("TEST");
		record7.setRows(1);

		try{
			snAttrInfoService.getSnByLpnOrOriginalLpn(record7);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PAGING_QUERY_PARAMETERS_IS_EMPTY);
		}

		ContainerInfoDTO record4 = new ContainerInfoDTO();
		record4.setOriginalLpn("TEST");
		record4.setLpn("TEST");
		record4.setPage(1);
		record4.setRows(600);
		try{
			snAttrInfoService.getSnByLpnOrOriginalLpn(record4);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.ROWS_MORE_THAN_FIVE_HUNDRED);
		}

		ContainerInfoDTO record5 = new ContainerInfoDTO();
		record5.setPage(1);
		record5.setRows(500);
		try{
			snAttrInfoService.getSnByLpnOrOriginalLpn(record5);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.LPN_AND_ORIGINAL_LPN_ALL_NULL);
		}

		ContainerInfoDTO record6 = new ContainerInfoDTO();
		record6.setOriginalLpn("TEST");
		record6.setLpn("TEST");
		record6.setPage(1);
		record6.setRows(100);
		List<ContainerContentInfoDTO> list2 = new ArrayList<>();
		Page<ContainerContentInfoDTO> page1 = new Page<>();
		page1.setRows(list2);
		PowerMockito.when(ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(Mockito.any())).thenReturn(page1);
		try{
			snAttrInfoService.getSnByLpnOrOriginalLpn(record6);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.EN_INFO_NOT_EXIST);
		}

		List<ContainerContentInfoDTO> list3 = new ArrayList<>();
		list3.add(new ContainerContentInfoDTO());
		List<SnAttrInfoEntityDTO> infoList = new ArrayList<>();
		Page<ContainerContentInfoDTO> page2 = new Page<>();
		page2.setRows(list3);
		PowerMockito.when(ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(Mockito.any())).thenReturn(page2);
		PowerMockito.when(snAttrInfoRepository.getEnInfoByEnCode(Mockito.anyList())).thenReturn(infoList);
		snAttrInfoService.getSnByLpnOrOriginalLpn(record6);
	}

}
