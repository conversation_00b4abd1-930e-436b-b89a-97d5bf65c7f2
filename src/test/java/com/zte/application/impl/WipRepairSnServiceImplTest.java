package com.zte.application.impl;

import com.zte.domain.model.WipRepairSn;
import com.zte.domain.model.WipRepairSnRepository;
import com.zte.interfaces.dto.BarcodeDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/11 9:38
 */
@RunWith(PowerMockRunner.class)
public class WipRepairSnServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipRepairSnServiceImpl service;

    @Mock
    private WipRepairSnRepository repository;
    @Test
    public void batchGetRepairSn () {
        Assert.assertTrue(CollectionUtils.isEmpty(service.batchGetRepairSn(new ArrayList<>(), "","")));
        List<BarcodeDTO> listBarcode = new ArrayList<>();
        BarcodeDTO dto = new BarcodeDTO();
        listBarcode.add(dto);
        Assert.assertTrue(CollectionUtils.isEmpty(service.batchGetRepairSn(listBarcode, "","")));
        dto.setBarcode("321");
        Assert.assertTrue(CollectionUtils.isEmpty(service.batchGetRepairSn(listBarcode, "","")));
        Assert.assertTrue(CollectionUtils.isEmpty(service.batchGetRepairSn(listBarcode, "123","")));
        Assert.assertTrue(CollectionUtils.isEmpty(service.batchGetRepairSn(listBarcode, "123","321")));
        List<WipRepairSn> list = new ArrayList<>();
        list.add(new WipRepairSn());
        PowerMockito.when(repository.getRepairSnBatch(Mockito.anyMap())).thenReturn(list);
        Assert.assertFalse(CollectionUtils.isEmpty(service.batchGetRepairSn(listBarcode, "", "")));

    }
}
