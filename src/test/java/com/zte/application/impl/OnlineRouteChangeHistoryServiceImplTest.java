package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.OnlineRouteChangeHistory;
import com.zte.domain.model.OnlineRouteChangeHistoryRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.OnlineRouteChangeHistoryDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 在线工艺路径变更历史信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-06 13:30:07
 */
@RunWith(PowerMockRunner.class)
public class OnlineRouteChangeHistoryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private OnlineRouteChangeHistoryServiceImpl service;
    @Mock
    private OnlineRouteChangeHistoryRepository repository;
	@Mock
	private CenterfactoryRemoteService centerfactoryRemoteService;
    @Test
    public void testQueryPage() throws Exception {
		OnlineRouteChangeHistoryDTO dto = new OnlineRouteChangeHistoryDTO();
    	try{
			service.queryPage(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.ONLY_ITEMNO_OR_PRODPLANID_CAN_QUERIED_SEPARATELY, e.getMessage());
		}
		dto.setStatus(1);
		try{
			service.queryPage(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.ONLY_ITEMNO_OR_PRODPLANID_CAN_QUERIED_SEPARATELY, e.getMessage());
		}
		dto.setLastUpdatedBy("00286569");
		try{
			service.queryPage(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.QUERY_PARAM_PERSON_NEEDS_TO_COOPERATE_WITH_TIME, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("2024-03-02 11:11:13");
		dto.setLastUpdatedDateStart("2022-03-02 11:11:13");
		try{
			service.queryPage(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.QUERY_TIME_CANNOT_SPAN_ONE_YEAR, e.getMessage());
		}
		dto.setLastUpdatedDateStart("2024-03-01 11:11:13");
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<OnlineRouteChangeHistory>());
        Assert.assertNotNull(service.queryPage(dto));
    }

	@Test
	public void queryPage() throws Exception {
		Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
		HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
		b1.setEmpName("111");
		hrmPersonInfoMap.put("0026", b1);
		HrmPersonInfoDTO b2 = new HrmPersonInfoDTO();
		b2.setEmpName("111");
		hrmPersonInfoMap.put("0027", b2);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
				.thenReturn(hrmPersonInfoMap);
		List<OnlineRouteChangeHistory> resultList = new ArrayList<>();
		OnlineRouteChangeHistory dto = new OnlineRouteChangeHistory();
		dto.setItemNo("129206751425AKB");
		dto.setLastUpdatedBy("0027");
		OnlineRouteChangeHistory dto2 = new OnlineRouteChangeHistory();
		dto2.setItemNo("129206751425AKB");
		dto2.setLastUpdatedBy("0030");
		resultList.add(dto);
		resultList.add(dto2);
		PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(resultList);
		OnlineRouteChangeHistoryDTO dto1 = new OnlineRouteChangeHistoryDTO();
		dto1.setLastUpdatedDateEnd("2024-03-02 11:11:13");
		dto1.setLastUpdatedDateStart("2024-02-02 11:11:13");
		dto1.setItemNo("129206751425AKB");
		Assert.assertNotNull(service.queryPage(dto1));
	}

	@Test
	public void queryPageTwo() throws Exception {
		Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
		HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
		b1.setEmpName("111");
		hrmPersonInfoMap.put("0026", b1);
		HrmPersonInfoDTO b2 = new HrmPersonInfoDTO();
		b2.setEmpName("111");
		hrmPersonInfoMap.put("0027", b2);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
				.thenReturn(hrmPersonInfoMap);
		List<OnlineRouteChangeHistory> resultList = new ArrayList<>();
		OnlineRouteChangeHistory dto = new OnlineRouteChangeHistory();
		dto.setItemNo("129206751425AKB");
		resultList.add(dto);
		PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(resultList);
		OnlineRouteChangeHistoryDTO dto1 = new OnlineRouteChangeHistoryDTO();
		dto1.setLastUpdatedDateEnd("2024-03-02 11:11:13");
		dto1.setLastUpdatedDateStart("2024-02-02 11:11:13");
		dto1.setItemNo("129206751425AKB");
		Assert.assertNotNull(service.queryPage(dto1));
	}

	@Test
	public void queryPageThree() throws Exception {
		Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
		HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
		b1.setEmpName("111");
		hrmPersonInfoMap.put("0026", b1);
		HrmPersonInfoDTO b2 = new HrmPersonInfoDTO();
		b2.setEmpName("111");
		hrmPersonInfoMap.put("0027", b2);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
				.thenReturn(hrmPersonInfoMap);
		List<OnlineRouteChangeHistory> resultList = new ArrayList<>();
		OnlineRouteChangeHistory dto = new OnlineRouteChangeHistory();
		dto.setItemNo("129206751425AKB");
		resultList.add(dto);
		OnlineRouteChangeHistory dto2 = new OnlineRouteChangeHistory();
		dto2.setItemNo("129206751425AKB");
		dto2.setLastUpdatedBy("0027");
		resultList.add(dto2);
		PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(resultList);
		OnlineRouteChangeHistoryDTO dto1 = new OnlineRouteChangeHistoryDTO();
		dto1.setLastUpdatedDateEnd("2024-03-02 11:11:13");
		dto1.setLastUpdatedDateStart("2024-02-02 11:11:13");
		dto1.setItemNo("129206751425AKB");
		Assert.assertNotNull(service.queryPage(dto1));
	}


    @Test
    public void  checkQueryParam() throws Exception{
        OnlineRouteChangeHistoryDTO dto = new OnlineRouteChangeHistoryDTO();
        dto.setLastUpdatedDateStart("2024-02-02 11:11:13");
        try{
            Whitebox.invokeMethod(service, "checkQueryParam", dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.ONLY_ITEMNO_OR_PRODPLANID_CAN_QUERIED_SEPARATELY, e.getMessage());
        }
        dto.setLastUpdatedDateEnd("2024-03-02 11:11:13");
        try{
           Whitebox.invokeMethod(service, "checkQueryParam", dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.ONLY_ITEMNO_OR_PRODPLANID_CAN_QUERIED_SEPARATELY, e.getMessage());
        }
        dto.setLastUpdatedBy("112");
        try{
            Whitebox.invokeMethod(service, "checkQueryParam", dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.ONLY_ITEMNO_OR_PRODPLANID_CAN_QUERIED_SEPARATELY, e.getMessage());
        }
        OnlineRouteChangeHistoryDTO dto1 = new OnlineRouteChangeHistoryDTO();
        dto1.setStatus(1);
        Assert.assertNull(Whitebox.invokeMethod(service, "checkQueryParam", dto));
        dto1.setLastUpdatedBy("111");
        Assert.assertNull(Whitebox.invokeMethod(service, "checkQueryParam", dto));
        OnlineRouteChangeHistoryDTO dto2 = new OnlineRouteChangeHistoryDTO();
        dto2.setLastUpdatedBy("111");
        dto2.setItemNo("2111");
        Assert.assertNull(Whitebox.invokeMethod(service, "checkQueryParam", dto2));
        OnlineRouteChangeHistoryDTO dto3 = new OnlineRouteChangeHistoryDTO();
        dto3.setLastUpdatedBy("111");
        dto3.setProdplanId("2111");
        Assert.assertNull(Whitebox.invokeMethod(service, "checkQueryParam", dto3));
    }

    @Test
    public void testGetById() {
        PowerMockito.when(repository.selectById(Mockito.any())).thenReturn(new OnlineRouteChangeHistory());
		Assert.assertNotNull( service.getById("test"));
    }

	@Test
	public void testInsertChangeHistory() {
		boolean flag = true;
		Assert.assertTrue(flag);
		service.insertChangeHistory(new OnlineRouteChangeHistoryDTO());
    }

    @Test
    public void onlineChangeResultList() {
		PowerMockito.when(repository.onlineChangeResultList(Mockito.any())).thenReturn(new ArrayList<>());
		Assert.assertNotNull( service.onlineChangeResultList(new OnlineRouteChangeHistoryDTO()));
    }

	/* Started by AICoder, pid:q8d0db7037nd70414d7e0a47212ddc0fc2074de3 */
	@Test
	public void testSetMBom()throws Exception {
		// 创建并初始化PsEntityPlanBasic对象
		OnlineRouteChangeHistory dto1 = new OnlineRouteChangeHistory();
		dto1.setItemNo("前加工");
		dto1.setProdplanId("prodplanId_2");

		OnlineRouteChangeHistory dto2 = new OnlineRouteChangeHistory();
		dto2.setItemNo("前加工");
		dto2.setProdplanId("prodplanId_3");

		List<OnlineRouteChangeHistory> listEntity = new ArrayList<>();
		listEntity.add(dto1);
		listEntity.add(dto2);

		Whitebox.invokeMethod(service, "setMBom", null);
		assertNotNull(listEntity);

		// 模拟远程服务调用返回null的情况
		when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(null);
		Whitebox.invokeMethod(service, "setMBom", listEntity);
		assertNotNull(listEntity);

		// 创建并初始化BProdBomHeaderDTO对象
		List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
		bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
			setOriginalProductCode("taskNo1");
			setProductCode("taskNo1_1");
			setProdplanId("prodplanId_1");
		}});
		bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
			setOriginalProductCode("taskNo2");
			setProductCode("taskNo1_1");
			setProdplanId("prodplanId_2");
		}});

		// 模拟远程服务调用返回有效数据的情况
		when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOList);
		Whitebox.invokeMethod(service, "setMBom", listEntity);
		assertNotNull(listEntity);
	}
	/* Ended by AICoder, pid:q8d0db7037nd70414d7e0a47212ddc0fc2074de3 */

}

