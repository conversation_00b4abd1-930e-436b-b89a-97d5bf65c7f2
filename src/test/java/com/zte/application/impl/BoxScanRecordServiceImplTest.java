package com.zte.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BoxScanRecord;
import com.zte.domain.model.BoxScanRecordRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.BoxScanRecordDTO;
import com.zte.interfaces.dto.BoxScanRecordPageQueryDTO;
import com.zte.interfaces.dto.HrmPersonInfo;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-06 16:07:53
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BoxScanRecordServiceImplTest {

    @InjectMocks
    private BoxScanRecordServiceImpl boxScanRecordService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    private BoxScanRecordDTO dto;

    @Mock
    private IdGenerator idGenerator;

    @Mock
    private BoxScanRecordRepository boxScanRecordRepository;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Before
    public void setUp()throws Exception {
        dto = new BoxScanRecordDTO();
        dto.setBarcode("testBarcode");
        dto.setBoxNo("testBoxNo");
        dto.setUserConfirmation("Y");
        dto.setQty(new BigDecimal("1.0"));
        dto = new BoxScanRecordDTO();
        dto.setIp("127.0.0.1");
        dto.setBoxNo("testBoxNo");
        dto.setPrinter("printerName");
        dto.setTemplateName("templateName");
        PowerMockito.when(centerfactoryRemoteService.createCfBizCode(any())).thenReturn(Arrays.asList("MIX202505150001"));
    }


    @Test
    public void setValidIdentifier() throws Exception {
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setEnabledFlag("Y");
        BoxScanRecord record2 = new BoxScanRecord();
        record2.setEnabledFlag("N");

        List<BoxScanRecord> boxScanRecordList = Arrays.asList(record1,record2);

        Whitebox.invokeMethod(boxScanRecordService,"setValidIdentifier",boxScanRecordList);
        Assert.assertNotNull(boxScanRecordList);

        Whitebox.invokeMethod(boxScanRecordService,"setValidIdentifier",new ArrayList<>());
        Assert.assertNotNull(boxScanRecordList);


    }
    @Test
    public void testQueryPage() throws MesBusinessException, JsonProcessingException, JsonMappingException {
        PowerMockito.when(boxScanRecordRepository.selectPage(Mockito.any())).thenReturn(new ArrayList<BoxScanRecord>());
        boxScanRecordService.queryPage(new BoxScanRecordPageQueryDTO(){{setStartTime(new Date());}});
        Assert.assertNotNull(dto);
    }

    @Test
    public void testGetById() {
        PowerMockito.when(boxScanRecordRepository.selectById(Mockito.any())).thenReturn(new BoxScanRecord());
        boxScanRecordService.getById(null);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testAdd() {
        PowerMockito.when(boxScanRecordRepository.insert(Mockito.any())).thenReturn(1L);
        boxScanRecordService.add(new BoxScanRecord());
        Assert.assertNotNull(dto);
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(boxScanRecordRepository.updateById(Mockito.any())).thenReturn(1L);
        boxScanRecordService.updateById(new BoxScanRecord());
        Assert.assertNotNull(dto);
    }

    @Test
    public void testDeleteByIds() {
        PowerMockito.when(boxScanRecordRepository.deleteByIds(Mockito.any(),Mockito.any())).thenReturn(1L);
        boxScanRecordService.deleteByIds(new ArrayList());
        Assert.assertNotNull(dto);
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(boxScanRecordRepository.selectCount(Mockito.any())).thenReturn(1);
        boxScanRecordService.countExportTotal(new BoxScanRecordPageQueryDTO());
        Assert.assertNotNull(dto);
    }

    @Test
    public void testQueryExportData() {
        PowerMockito.when(boxScanRecordRepository.selectPage(Mockito.any())).thenReturn(new ArrayList<BoxScanRecord>());
        boxScanRecordService.queryExportData(new BoxScanRecordPageQueryDTO(), 1, 10);
        Assert.assertNotNull(dto);
    }


    /*Started by AICoder, pid:fe04bda7e0p17e7146d00b69e0826953cb414fb2*/
    @Test
    public void testGetById_NonExistingId() {
        String id = "non-existing-id";

        when(boxScanRecordRepository.selectById(id)).thenReturn(null);

        BoxScanRecord actualRecord = boxScanRecordService.getById(id);

        assertEquals(null, actualRecord);
    }

    @Test
    public void testGetById_ExistingId() {
        String id = "123";
        BoxScanRecord expectedRecord = new BoxScanRecord();
        expectedRecord.setId(id);

        when(boxScanRecordRepository.selectById(id)).thenReturn(expectedRecord);

        BoxScanRecord actualRecord = boxScanRecordService.getById(id);

        assertEquals(expectedRecord, actualRecord);
    }

    /*Started by AICoder, pid:6e6a0m716d780be1429809a71157f04b785480e9*/
    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_BoxNoIsNull() throws Exception {
        dto.setBoxNo(null);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_PrinterIsNull() throws Exception {
        dto.setPrinter(null);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testServerTemplatePrint_SinglePrint_ExpandListNotEmpty() throws Exception {
        dto.setDozenPrint(false);
        dto.setPrintCount(1);

        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(Arrays.asList(dto.getBoxNo()));
        BarcodeExpandDTO expandDTO = new BarcodeExpandDTO();
        List<BarcodeExpandDTO> expandList = Collections.singletonList(expandDTO);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(expandList);

        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_TemplateNameIsNull() throws Exception {
        dto.setTemplateName(null);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test
    public void testServerTemplatePrint_DozenPrint() throws Exception {
        dto.setDozenPrint(true);
        dto.setDozenPrintCount(1);

        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(Arrays.asList(dto.getBoxNo()));
        BarcodeExpandDTO expandDTO = new BarcodeExpandDTO();
        List<BarcodeExpandDTO> expandList = Collections.singletonList(expandDTO);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(expandList);

        PowerMockito.mockStatic(CommonUtils.class);
        boxScanRecordService.serverTemplatePrint(dto);

        verify(barcodeCenterRemoteService, never()).barcodeRegister(any(List.class));
        verify(barcodeCenterRemoteService, times(1)).serverTemplatePrint(any(BarcodeCenterTemplatePrintDTO.class));
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_IpIsNull() throws Exception {
        dto.setIp(null);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_DozenPrintCountIsInvalid() throws Exception {
        dto.setDozenPrint(true);
        dto.setDozenPrintCount(0);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_PrintCountIsInvalid() throws Exception {
        dto.setDozenPrint(false);
        dto.setPrintCount(0);
        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testServerTemplatePrint_DozenPrint_ExpandListIsEmpty() throws Exception {
        dto.setDozenPrint(true);
        dto.setDozenPrintCount(1);

        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(Arrays.asList(dto.getBoxNo()));
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        boxScanRecordService.serverTemplatePrint(dto);
    }

    @Test
    public void testServerTemplatePrint_SinglePrint() throws Exception {
        dto.setDozenPrint(false);
        dto.setPrintCount(1);

        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(Arrays.asList(dto.getBoxNo()));
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(Collections.emptyList());
        PowerMockito.mockStatic(CommonUtils.class);
        boxScanRecordService.serverTemplatePrint(dto);

        verify(barcodeCenterRemoteService, times(1)).barcodeRegister(any(List.class));
        verify(barcodeCenterRemoteService, times(1)).serverTemplatePrint(any(BarcodeCenterTemplatePrintDTO.class));
    }
    /*Ended by AICoder, pid:6e6a0m716d780be1429809a71157f04b785480e9*/


    /*Started by AICoder, pid:e2790fbc56c258514b9d0a9400b2b37320e4f709*/
    @Test(expected = MesBusinessException.class)
    public void testUnBind_NoEnabledRecordsFound() {
        List<String> idList = Arrays.asList("1", "2");
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setEnabledFlag(Constant.FLAG_N);
        BoxScanRecord record2 = new BoxScanRecord();
        record2.setEnabledFlag(Constant.FLAG_N);

        when(boxScanRecordService.getByIdList(idList)).thenReturn(Arrays.asList(record1, record2));

        boxScanRecordService.unBind(idList);
    }

    @Test(expected = MesBusinessException.class)
    public void testUnBind_NoRecordsFound() {
        List<String> idList = Arrays.asList("1", "2");
        when(boxScanRecordService.getByIdList(idList)).thenReturn(Collections.emptyList());

        boxScanRecordService.unBind(idList);
    }

    @Test
    public void testUnBind_ValidRecordsFound() {
        List<String> idList = Arrays.asList("1", "2");
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setId("1");
        record1.setEnabledFlag(Constant.FLAG_Y);
        BoxScanRecord record2 = new BoxScanRecord();
        record2.setId("2");
        record2.setEnabledFlag(Constant.FLAG_Y);

        when(boxScanRecordService.getByIdList(idList)).thenReturn(Arrays.asList(record1, record2));

        boxScanRecordService.unBind(idList);

        Assert.assertNotNull(idList);
    }
    /*Ended by AICoder, pid:e2790fbc56c258514b9d0a9400b2b37320e4f709*/


    /*Started by AICoder, pid:0d1aa58cc0i2d6b14e5208db80d8597b82011601*/
    @Test
    public void testGetCountByBoxNo_WithEmptyBoxNo() {
        // Given
        String boxNo = "";
        BigDecimal expectedCount = BigDecimal.ZERO;

        when(boxScanRecordRepository.getCountByBoxNo(boxNo)).thenReturn(expectedCount);

        // When
        BigDecimal actualCount = boxScanRecordService.getCountByBoxNo(boxNo);

        // Then
        assertEquals(expectedCount, actualCount);
    }

    @Test
    public void testGetCountByBoxNo_WithValidBoxNo() {
        // Given
        String boxNo = "B12345";
        BigDecimal expectedCount = BigDecimal.TEN;

        when(boxScanRecordRepository.getCountByBoxNo(boxNo)).thenReturn(expectedCount);

        // When
        BigDecimal actualCount = boxScanRecordService.getCountByBoxNo(boxNo);

        // Then
        assertEquals(expectedCount, actualCount);
    }

    @Test
    public void testGetCountByBoxNo_WithNullBoxNo() {
        // Given
        String boxNo = null;
        BigDecimal expectedCount = BigDecimal.ZERO;

        when(boxScanRecordRepository.getCountByBoxNo(boxNo)).thenReturn(expectedCount);

        // When
        BigDecimal actualCount = boxScanRecordService.getCountByBoxNo(boxNo);

        // Then
        assertEquals(expectedCount, actualCount);
    }
    /*Ended by AICoder, pid:0d1aa58cc0i2d6b14e5208db80d8597b82011601*/


    /*Started by AICoder, pid:yfc7agca91hc09e1447a0b4f918def8b9595c8f9*/
    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_BoxNoNotFoundInExpandList() throws Exception {
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("otherBoxNo");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_BarcodeNotFoundInExpandList() throws Exception {
        BarcodeExpandDTO expandDTO = new BarcodeExpandDTO();
        expandDTO.setBarcode("otherBarcode");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(expandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test
    public void testBarcodeScan_UserConfirmationNotY() throws Exception {
        dto.setUserConfirmation("N");
        dto.setBarcode("testBarcode");
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("NOT_SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);

        BoxScanRecordDTO result = boxScanRecordService.barcodeScan(dto);

        assertNotNull(result);
        assertEquals("N", result.getUserConfirmation());
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_NotMatchingRule() throws Exception {
        dto.setRule("^\\d+$");
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);

        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_QtyZeroOrNegative() throws Exception {
        dto.setQty(BigDecimal.ZERO);
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);

        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_BarcodeAlreadyScanned() throws Exception {
        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(new BoxScanRecord());
        boxScanRecordService.barcodeScan(dto);
    }

    @Test
    public void testBarcodeScan_ValidInput() throws Exception {
        dto.setBarcode("testBarcode");
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandDTOList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandDTOList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");

        BoxScanRecordDTO result = boxScanRecordService.barcodeScan(dto);

        assertNotNull(result);
        assertEquals("123456", result.getBoxScanRecord().getId());
        assertEquals("Y", result.getUserConfirmation());
        assertEquals(BigDecimal.ONE, result.getQty());
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_EmptyBoxNo() throws Exception {
        dto.setBoxNo(null);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_EmptyBarcode() throws Exception {
        dto.setBarcode(null);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScan_ExpandQueryEmpty() throws Exception {
        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(Collections.emptyList());
        boxScanRecordService.barcodeScan(dto);
    }
    /*Ended by AICoder, pid:yfc7agca91hc09e1447a0b4f918def8b9595c8f9*/
    /*Ended by AICoder, pid:fe04bda7e0p17e7146d00b69e0826953cb414fb2*/

    /*Started by AICoder, pid:0edd071ce9va016147760afd510fdd7c1bf0a3e0*/
    @Test
    public void testSetEmpName_EmptyList() throws Exception {
        List<BoxScanRecord> boxScanRecordList = Collections.emptyList();

        // Use reflection to call the private method
        java.lang.reflect.Method method = BoxScanRecordServiceImpl.class.getDeclaredMethod("setEmpName", List.class);
        method.setAccessible(true);
        method.invoke(boxScanRecordService, boxScanRecordList);

        assertEquals(0, boxScanRecordList.size());
    }

    @Test
    public void testSetEmpName_NoCreateByOrLastUpdatedBy() throws Exception {
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setCreateBy(null);
        record1.setLastUpdatedBy(null);

        List<BoxScanRecord> boxScanRecordList = Arrays.asList(record1);

        // Use reflection to call the private method
        java.lang.reflect.Method method = BoxScanRecordServiceImpl.class.getDeclaredMethod("setEmpName", List.class);
        method.setAccessible(true);
        method.invoke(boxScanRecordService, boxScanRecordList);

        assertEquals(null, record1.getCreateBy());
        assertEquals(null, record1.getLastUpdatedBy());
    }

    @Test
    public void testSetEmpName_WithCreateByAndLastUpdatedBy() throws Exception {
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setCreateBy("emp1");
        record1.setLastUpdatedBy("emp2");

        List<BoxScanRecord> boxScanRecordList = Arrays.asList(record1);

        Map<String, HrmPersonInfo> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfo personInfo1 = new HrmPersonInfo();
        personInfo1.setEmpName("Name1");
        HrmPersonInfo personInfo2 = new HrmPersonInfo();
        personInfo2.setEmpName("Name2");
        hrmPersonInfoMap.put("emp1", personInfo1);
        hrmPersonInfoMap.put("emp2", personInfo2);

        when(hrmUserInfoService.getPersonGeneralInfo(anyList())).thenReturn(hrmPersonInfoMap);

        // Use reflection to call the private method
        java.lang.reflect.Method method = BoxScanRecordServiceImpl.class.getDeclaredMethod("setEmpName", List.class);
        method.setAccessible(true);
        method.invoke(boxScanRecordService, boxScanRecordList);

        assertEquals("Name1emp1", record1.getCreateBy());
        assertEquals("Name2emp2", record1.getLastUpdatedBy());
    }

    @Test
    public void testSetEmpName_MixedCases() throws Exception {
        BoxScanRecord record1 = new BoxScanRecord();
        record1.setCreateBy("emp1");
        record1.setLastUpdatedBy(null);

        BoxScanRecord record2 = new BoxScanRecord();
        record2.setCreateBy(null);
        record2.setLastUpdatedBy("emp2");

        List<BoxScanRecord> boxScanRecordList = Arrays.asList(record1, record2);

        Map<String, HrmPersonInfo> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfo personInfo1 = new HrmPersonInfo();
        personInfo1.setEmpName("Name1");
        HrmPersonInfo personInfo2 = new HrmPersonInfo();
        personInfo2.setEmpName("Name2");
        hrmPersonInfoMap.put("emp1", personInfo1);
        hrmPersonInfoMap.put("emp2", personInfo2);

        when(hrmUserInfoService.getPersonGeneralInfo(anyList())).thenReturn(hrmPersonInfoMap);

        // Use reflection to call the private method
        java.lang.reflect.Method method = BoxScanRecordServiceImpl.class.getDeclaredMethod("setEmpName", List.class);
        method.setAccessible(true);
        method.invoke(boxScanRecordService, boxScanRecordList);

        assertEquals("Name1emp1", record1.getCreateBy());
        assertEquals(null, record1.getLastUpdatedBy());
        assertEquals(null, record2.getCreateBy());
        assertEquals("Name2emp2", record2.getLastUpdatedBy());
    }
    /*Ended by AICoder, pid:0edd071ce9va016147760afd510fdd7c1bf0a3e0*/
    @Test
    public void getByIdList() throws Exception {
        List<BoxScanRecord>  boxScanRecordList = boxScanRecordService.getByIdList(new ArrayList<>());
        Assert.assertEquals(boxScanRecordList.size(),0);
    }

    /*Started by AICoder, pid:v5ecdu0dact552814e200ba061fc6589e4d6bffb*/
    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanEmptyBarcode() throws Exception {
        dto.setBarcode(null);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanEmptyBoxNo() throws Exception {
        dto.setBoxNo(null);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanBarcodeAlreadyScanned() throws Exception {
        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(new BoxScanRecord());
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanExpandQueryEmpty() throws Exception {
        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(Collections.emptyList());
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanBarcodeNotFoundInExpandList() throws Exception {
        BarcodeExpandDTO expandDTO = new BarcodeExpandDTO();
        expandDTO.setBarcode("otherBarcode");

        List<BarcodeExpandDTO> expandList = Arrays.asList(expandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanBoxNoNotFoundInExpandList() throws Exception {
        BarcodeExpandDTO expandDTO = new BarcodeExpandDTO();
        expandDTO.setBarcode("testBarcode");

        List<BarcodeExpandDTO> expandList = Arrays.asList(expandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        boxScanRecordService.barcodeScan(dto);
    }

    @Test
    public void testBarcodeScanValidCase() throws Exception {
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");
        dto.setBarcode("testBarcode");
        BoxScanRecordDTO result = boxScanRecordService.barcodeScan(dto);

        assertNotNull(result);
        assertEquals("1234567890", result.getBoxScanRecord().getId());
        assertEquals(BigDecimal.ONE, result.getQty());
    }

    @Test
    public void testBarcodeScanNonSNCode() throws Exception {
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("NON_SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");
        dto.setBarcode("testBarcode");
        BoxScanRecordDTO result = boxScanRecordService.barcodeScan(dto);

        assertNotNull(result);
        assertEquals("N", result.getUserConfirmation());
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanInvalidQty() throws Exception {
        dto.setQty(BigDecimal.ZERO);

        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");

        boxScanRecordService.barcodeScan(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testBarcodeScanInvalidRule() throws Exception {
        dto.setRule("\\d+");
        dto.setBarcode("abc");

        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");

        boxScanRecordService.barcodeScan(dto);
    }
    /*Ended by AICoder, pid:v5ecdu0dact552814e200ba061fc6589e4d6bffb*/

    @Test
    public void barcodeScan() throws Exception {
        BoxScanRecordDTO boxScanRecordDTO = new BoxScanRecordDTO();
        boxScanRecordDTO.setBarcode("testBarcode");

        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("testBarcode");
        barcodeExpandDTO.setParentCategoryCode("SN_CODE");

        BarcodeExpandDTO boxNoExpandDTO = new BarcodeExpandDTO();
        boxNoExpandDTO.setBarcode("testBoxNo");

        List<BarcodeExpandDTO> expandList = Arrays.asList(barcodeExpandDTO, boxNoExpandDTO);

        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(new BoxScanRecord());
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");
        try{
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.LPN_IS_NULL, e.getMessage());
        }

        boxScanRecordDTO.setBoxNo("boxNo");
        try{
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_MATERIAL_BARCODE_HAS_BEEN_SCANNED, e.getMessage());
        }
        when(boxScanRecordRepository.selectByBarcode(anyString())).thenReturn(null);
        try{
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SN_LOST_BOARD_CENTER, e.getMessage());
        }
        when(barcodeCenterRemoteService.expandQueryOneByOne(any(BarcodeExpandQueryDTO.class))).thenReturn(expandList);
        try{
            boxScanRecordDTO.setBoxNo("2");
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SN_LOST_BOARD_CENTER, e.getMessage());
        }

        boxScanRecordDTO.setBoxNo("testBarcode");
        boxScanRecordDTO.setUserConfirmation("N");
        boxScanRecordDTO.setQty(new BigDecimal("-1"));
        try{
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.UP_TO_4_DECIMAL_PLACES, e.getMessage());
        }
        boxScanRecordDTO.setQty(BigDecimal.ONE);
        boxScanRecordDTO.setRule("^\\\\d+(\\\\.\\\\d{1,4})?$");
        try{
            boxScanRecordService.barcodeScan(boxScanRecordDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NOT_IN_COMPLIANCE_WITH_THE_PACKING_RULES, e.getMessage());
        }

    }

    /*Started by AICoder, pid:24c7cv0282xb6a9143610b0fc19a283b20f21ba3*/
    @Test
    public void testVerifyParams_BothDatesNull() {
        // Given
        BoxScanRecordPageQueryDTO query = new BoxScanRecordPageQueryDTO();
        query.setStartTime(null);
        query.setEndTime(null);

        // When
        try{
            boxScanRecordService.verifyParams(query);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_QUERY_CONDITIONS_CANNOT_ALL_BE_EMPTY, e.getMessage());
        }
        query.setStartTime(new Date());
        try{
            boxScanRecordService.verifyParams(query);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_QUERY_CONDITIONS_CANNOT_ALL_BE_EMPTY, e.getMessage());
        }
        query.setBarcode("2");
        try{
            boxScanRecordService.verifyParams(query);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_QUERY_CONDITIONS_CANNOT_ALL_BE_EMPTY, e.getMessage());
        }
        boxScanRecordService.verifyParams(query);
        query.setBoxNoList(Arrays.asList("3"));
        Assert.assertNotNull(query);
        // Then
        // No exception should be thrown
    }

    @Test
    public void testVerifyParams_EndDateWithinRange() {
        // Given
        Date startTime = new Date();
        Date endTime = new Date(startTime.getTime() + 1000 * 60 * 60 * 24); // 1 day later
        BoxScanRecordPageQueryDTO query = new BoxScanRecordPageQueryDTO();
        query.setStartTime(startTime);
        query.setEndTime(endTime);


        // When
        boxScanRecordService.verifyParams(query);
        Assert.assertNotNull(query);
        // Then
        // No exception should be thrown
    }

    @Test(expected = MesBusinessException.class)
    public void testVerifyParams_EndDateOutOfRange() {
        // Given
        Date startTime = new Date();
        Date endTime = DateUtil.addMonth(startTime,24); // 2 years later
        BoxScanRecordPageQueryDTO query = new BoxScanRecordPageQueryDTO();
        query.setStartTime(startTime);
        query.setEndTime(endTime);

        // When
        boxScanRecordService.verifyParams(query);
        Assert.assertNotNull(query);
        // Then
        // Exception should be thrown
    }
    /*Ended by AICoder, pid:24c7cv0282xb6a9143610b0fc19a283b20f21ba3*/
}

