package com.zte.application.impl;

import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.approval.ScrapFlowStartDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/29 17:19
 */
@PrepareForTest({ProductionDeliveryRemoteService.class, PlanscheduleRemoteService.class, DatawbRemoteService.class, CrafttechRemoteService.class})
public class ReturnAndOnlineServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ReturnAndOnlineServiceImpl service;
    @Mock
    private SnAttrInfoRepository snAttrInfoRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private PsWipScanHistoryRepository psWipScanHistoryRepository;
    @Mock
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;

    @Test
    public void returnAndOnline() {
        ReturnAndOnlineDTO returnAndOnlineDTO = new ReturnAndOnlineDTO();
        returnAndOnlineDTO.setType("sn");
        List<String> list = new ArrayList<>();
        list.add("112");
        returnAndOnlineDTO.setSnList(list);
        List<SnAttrInfoEntityDTO> snAttrInfoList = new ArrayList<>();
        snAttrInfoList.add(new SnAttrInfoEntityDTO(){{
            setSn("111");
            setEnCode("111");
            setMac("111");
        }});
        PowerMockito.when(snAttrInfoRepository.getSnInfoBatch(returnAndOnlineDTO)).thenReturn(snAttrInfoList);
        try {
            service.returnAndOnline(returnAndOnlineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_INFO_NOT_EXIST, e.getMessage());
        }
        list.clear();
        list.add("111");
        List<String> stockList = new ArrayList(){{
            add("111");
        }};
        PowerMockito.when(snAttrInfoRepository.getSnInfoBatch(returnAndOnlineDTO)).thenReturn(snAttrInfoList);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.when(ProductionDeliveryRemoteService.queryIsStock(list)).thenReturn(stockList);
        try {
            service.returnAndOnline(returnAndOnlineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_STOCK, e.getMessage());
        }
        PowerMockito.when(ProductionDeliveryRemoteService.queryIsStock(list)).thenReturn(null);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        wipInfoList.add(new PsWipInfo(){{
            setSn("113");
            setParentSn("332");
        }});
        PowerMockito.when(psWipInfoRepository.getParentSnBySnList(list)).thenReturn(wipInfoList);
        try {
            service.returnAndOnline(returnAndOnlineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST_PARENT_SN, e.getMessage());
        }
        wipInfoList.add(new PsWipInfo(){{
            setSn("111");
            setParentSn("333");
        }});
        List<ReturnAndOnlineResultDTO> list1 =  new ArrayList<>();
        list1.add(new ReturnAndOnlineResultDTO(){{
            setSn("114");
        }});
        PowerMockito.when(psWipScanHistoryRepository.getInfoByParentSn(list)).thenReturn(list1);
        service.returnAndOnline(returnAndOnlineDTO);

        returnAndOnlineDTO.setType("enCode");
        service.returnAndOnline(returnAndOnlineDTO);
        returnAndOnlineDTO.setType("mac");
        service.returnAndOnline(returnAndOnlineDTO);
        returnAndOnlineDTO.setType("88");
        service.returnAndOnline(returnAndOnlineDTO);
    }

    @Test
    public void checkIsStock() throws Exception {
        List<String> stockList = new ArrayList(){{
            add("111");
        }};
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.when(ProductionDeliveryRemoteService.queryIsStock(new ArrayList<>())).thenReturn(stockList);
        List<SnAttrInfoEntityDTO> snAttrInfoList = new ArrayList<>();
        snAttrInfoList.add(new SnAttrInfoEntityDTO(){{
            setEnCode("111");
            setMac("111");
        }});
        Map<String, String> map = new HashMap<>();
        map.put("111","123");
        try {
            Whitebox.invokeMethod(service,"checkIsStock",snAttrInfoList,new ArrayList<>(),map,"enCode");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_STOCK, e.getMessage());
        }
        try {
            Whitebox.invokeMethod(service,"checkIsStock",snAttrInfoList,new ArrayList<>(),map,"mac");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_STOCK, e.getMessage());
        }
    }

    @Test
    public void getParentSn() throws Exception {
        ReturnAndOnlineDTO returnAndOnlineDTO = new ReturnAndOnlineDTO();
        returnAndOnlineDTO.setType("sn");
        List<String> list = new ArrayList<>();
        list.add("112");
        returnAndOnlineDTO.setSnList(list);List<String> stockList = new ArrayList(){{
            add("111");
        }};
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.when(ProductionDeliveryRemoteService.queryIsStock(new ArrayList<>())).thenReturn(stockList);
        List<String> snList = new ArrayList<>();
        snList.add("122");
        snList.add("112");
        Map<String, String> map = new HashMap<>();
        map.put("122","112");
        try {
            Whitebox.invokeMethod(service,"getParentSn",returnAndOnlineDTO,new ArrayList<>(),snList,map);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST_PARENT_SN, e.getMessage());
        }
    }

    @Test
    public void passStation() throws Exception {
        // 增量UT
        PassStationDTO passStationDTO = new PassStationDTO();
        List<PassStationInfoDTO> passStationInfoList = new ArrayList() {{
            add(new PassStationInfoDTO(){{
                setParentSn("123123");
                setFormSn("7777777");
                setTaskNo("cba");
                setItemCode("987654321");
                setProcessName("QC抽检1");
                setWorkStationName("QC抽检1");
            }});
        }};
        passStationDTO.setList(passStationInfoList);
        List<PsWipInfo> wipInfoList = new ArrayList(){{
            add(new PsWipInfo(){{
                setParentSn("123123");
                setSn("7777779");
            }});
            add(new PsWipInfo(){{
                setParentSn("123123");
                setSn("7777778");
            }});
        }};
        List<PsWipInfo> wipInfoByParentSnList = new ArrayList(){{
            add(new PsWipInfo(){{
                setSn("7777777");
                setAttribute2("abc");
                setItemNo("123456789");
                setProcessName("QC抽检1");
            }});
        }};
        PowerMockito.when(psWipInfoRepository.getWipInfoByParentSnList(Mockito.anyList())).thenReturn(wipInfoByParentSnList);
        PowerMockito.when(psWipInfoRepository.getParentSnBySnList(Mockito.anyList())).thenReturn(wipInfoList);
        try {
            service.passStation(passStationDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST_PARENT_SN,e.getMessage());
        }
        wipInfoList.clear();
        wipInfoList.add(new PsWipInfo(){{
            setParentSn("123123");
            setSn("7777777");
            setParentSn("7777777");
            setAttribute2("abc");
            setItemNo("123456789");
        }});
        try {
            service.passStation(passStationDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_IS_DIFFERENT,e.getMessage());
        }
        wipInfoList.clear();
        wipInfoList.add(new PsWipInfo(){{
            setParentSn("123123");
            setSn("7777777");
            setParentSn("7777777");
            setAttribute2("cba");
            setItemNo("1234567890");
        }});
        PowerMockito.when(psWipInfoRepository.getParentSnBySnList(Mockito.anyList())).thenReturn(wipInfoList);
        try {
            service.passStation(passStationDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_NO_IS_DIFFERENT,e.getMessage());
        }
        wipInfoList.clear();
        wipInfoList.add(new PsWipInfo(){{
            setParentSn("123123");
            setSn("7777777");
            setParentSn("7777777");
            setAttribute2("cba");
            setItemNo("987654321");
        }});
        PowerMockito.when(psWipInfoRepository.getParentSnBySnList(Mockito.anyList())).thenReturn(wipInfoList);
        List<BSProcess> processList = new ArrayList<>();
        BSProcess bsProcess1 = new BSProcess();
        BSProcess bsProcess2 = new BSProcess();
        bsProcess1.setProcessName("QC抽检1");
        bsProcess1.setxType(MpConstant.PROCESS_X_TYPE_P);
        bsProcess1.setProcessCode("qwe");
        bsProcess2.setProcessName("QC抽检1");
        bsProcess2.setxType(MpConstant.PROCESS_X_TYPE_S);
        bsProcess2.setProcessCode("ewq");
        processList.add(bsProcess1);
        processList.add(bsProcess2);
        PowerMockito.when(assemblyRelaScanService.getProcessInfo(Mockito.any())).thenReturn(processList);
        service.passStation(passStationDTO);
        wipInfoList.clear();
        try {
            service.passStation(passStationDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST_PARENT_SN,e.getMessage());
        }
        wipInfoList.add(new PsWipInfo(){{
            setParentSn("7777777");
            setSn("7777777");
            setAttribute2("abc");
            setItemNo("123456789");
        }});
        wipInfoByParentSnList.clear();
        PowerMockito.when(psWipInfoRepository.getParentSnBySnList(Mockito.anyList())).thenReturn(wipInfoList);
        try {
            service.passStation(passStationDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_IS_DIFFERENT,e.getMessage());
        }
    }

    @Test
    public void checkProcessNameAndWorkStationName() throws Exception {
        List<PassStationInfoDTO> passStationInfoList = new ArrayList<>();
        PassStationInfoDTO dto = new PassStationInfoDTO(){{
            setProcessName("qwe");
            setWorkStationName("ewq");
        }};
        PassStationInfoDTO dto1 = new PassStationInfoDTO(){{
            setProcessName("qwe1");
            setWorkStationName("ewq1");
        }};
        passStationInfoList.add(dto);
        passStationInfoList.add(dto1);
        try {
            Whitebox.invokeMethod(service,"checkProcessNameAndWorkStationName",passStationInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_NAME_IS_DIFFERENT,e.getMessage());
        }
        dto1.setProcessName("qwe");
        try {
            Whitebox.invokeMethod(service,"checkProcessNameAndWorkStationName",passStationInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKSTATION_NAME_IS_DIFFERENT,e.getMessage());
        }
        dto1.setWorkStationName("ewq");
        Whitebox.invokeMethod(service,"checkProcessNameAndWorkStationName",passStationInfoList);
    }

    @Test
    public void setWipInfo() throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList(){{
            add(new PsWipInfo(){{
                setParentSn("7777779");
                setSn("7777779");
            }});
            add(new PsWipInfo(){{
                setParentSn("7777777");
                setSn("7777777");
            }});
        }};
        List<PassStationInfoDTO> passStationInfoList = new ArrayList() {{
            add(new PassStationInfoDTO(){{
                setParentSn("123123");
                setFormSn("7777777");
                setTaskNo("cba");
                setItemCode("987654321");
                setProcessName("QC抽检1");
                setWorkStationName("QC抽检1");
            }});
            add(new PassStationInfoDTO(){{
                setParentSn("123123");
                setFormSn("7777776");
                setTaskNo("cba");
                setItemCode("987654321");
                setProcessName("QC抽检1");
                setWorkStationName("QC抽检1");
            }});
        }};
        Assert.assertNotNull(wipInfoList);
        Whitebox.invokeMethod(service,"setWipInfo", wipInfoList, passStationInfoList);

    }
}
