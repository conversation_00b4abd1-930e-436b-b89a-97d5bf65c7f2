package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.ExceptionSkipInfoService;
import com.zte.application.IMESLogService;
import com.zte.application.PsWipInfoService;
import com.zte.application.StandardModeCommonScanService;
import com.zte.application.impl.common.FlowControlCommonServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.scan.BarcodeBindingDTO;
import com.zte.interfaces.dto.scan.BarcodeTestControlDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2022-02-16 10:14
 */
@PrepareForTest({BasicsettingRemoteService.class, HttpRemoteUtil.class,
        HttpClientUtils.class, ConstantInterface.class, CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, CommonUtils.class, SpringContextUtil.class
})
public class FlowControlCommonServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private FlowControlCommonServiceImpl flowControlCommonServiceImpl;

    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }


    @Test
    public void technicalControlBySnTwo() throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        barcodeBindingDTO.setSn("889975800001");
        barcodeBindingDTO.setProdplanId("8899758");

        List<TechnicalChangeDetailDTO> technicalList = null;
        PowerMockito.when(technicalChangeHeadRepository.querySnBatchDetail(Mockito.any()))
                .thenReturn(technicalList);


        PowerMockito.mockStatic(CommonUtils.class);
        Assert.assertNotNull(flowControlCommonServiceImpl.technicalControlBySn(barcodeBindingDTO));
    }
}
