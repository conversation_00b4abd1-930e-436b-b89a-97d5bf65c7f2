package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SnInformationPushCommonRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BarcodePriceApiDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.ZmsBoardSnTencentDTO;
import com.zte.interfaces.dto.craft.RouteDetailForSnScanPushDTO;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.customerdatapush.DataModuleConfig;
import com.zte.springbootframe.common.customerdatapush.FieldMapping;
import com.zte.springbootframe.common.customerdatapush.SnInformationPushConstant;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.enums.InterfaceEnum.queryRecordBySn;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_1003022002;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_6732;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_6732008;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_6732009;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_6734;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_6735;
import static com.zte.common.utils.Constant.PROBLEMS;
import static com.zte.springbootframe.common.customerdatapush.SnInformationPushConstant.BYTE_DANCE_SN_REPAIR_DATA;
import static com.zte.springbootframe.common.customerdatapush.SnInformationPushConstant.LINE_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({BasicsettingRemoteService.class, CrafttechRemoteService.class,PlanscheduleRemoteService.class,
        MESHttpHelper.class})
public class SnInformationPushServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SnInformationPushServiceImpl service;

    @Mock
    SnInformationPushCommonRepository commonRepository;

    @Mock
    MdsRemoteService mdsRemoteService;

    @Mock
    DatawbRemoteService datawbRemoteService;

    @Mock
    PlanscheduleRemoteService planscheduleRemoteService;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    DigitalPlatformRemoteService digitalPlatformRemoteService;

    @Mock
    PsWipInfoRepository wipInfoRepository;

    @Mock
    PmRepairRcvDetailRepository repairRcvDetailRepository;

    @Mock
    WipScanHistoryRepository wipScanHistoryRepository;

    @Before
    public void beforeTest() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CrafttechRemoteService.class,PlanscheduleRemoteService.class);
    }

    @Test
    public void snScanDataPush() throws Exception {
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, LOOKUP_TYPE_6735)))
                .thenReturn(null);
        try {
            service.snScanDataPush(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, LOOKUP_TYPE_6735)))
                .thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("SMT");setLookupCode(LOOKUP_TYPE_6732008);setLookupType(new BigDecimal(LOOKUP_TYPE_6732));}}));
        try {
            service.snScanDataPush(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, LOOKUP_TYPE_6735)))
                .thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("SMT");setLookupCode(LOOKUP_TYPE_6732009);setLookupType(new BigDecimal(LOOKUP_TYPE_6732));}}));
        try {
            service.snScanDataPush(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, LOOKUP_TYPE_6735)))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{setLookupMeaning("SMT");setLookupCode(LOOKUP_TYPE_6732009);setLookupType(new BigDecimal(LOOKUP_TYPE_6732));}},
                        new SysLookupValuesDTO(){{setLookupMeaning("SMT-A");setAttribute1("SMT");setLookupCode(LOOKUP_TYPE_6732009);setLookupType(new BigDecimal(LOOKUP_TYPE_6732));}},
                        new SysLookupValuesDTO(){{setLookupMeaning("SMT");setAttribute1("SMT");setLookupCode(LOOKUP_TYPE_6732008);setLookupType(new BigDecimal(LOOKUP_TYPE_6734));}},
                        new SysLookupValuesDTO(){{setLookupMeaning("SMT");setAttribute1("SMT");setLookupCode(LOOKUP_TYPE_6732008);setLookupType(new BigDecimal(LOOKUP_TYPE_6735));}}));
        try {
            service.snScanDataPush(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PUSH_DATE_TIME_NULL, e.getMessage());
        }

        try {
            service.snScanDataPush(Lists.newArrayList(new CustomerDataLogDTO()));
        } catch (MesBusinessException e) {}
        service.specialFieldProcessing(Lists.newArrayList());
        service.specialFieldProcessing(Lists.newArrayList(new HashMap(),
                new HashMap(){{put(PROBLEMS, Lists.newArrayList(new HashMap()));}}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void reTryFailData() throws Exception {
        PowerMockito.when(centerfactoryRemoteService.getPushErrorData(any())).thenReturn(Lists.newArrayList(new CustomerDataLogDTO()));
        CustomerDataLogDTO logDTO = new CustomerDataLogDTO();
        logDTO.setId("123");
        service.reTryFailData();
        try {
            service.reTryFailData();
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void pushNewestData() throws Exception {
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(), any())).thenReturn(
                Lists.newArrayList(new CustomerItemsDTO(){{setZteCode("1");setProjectType("pt");}},
                        new CustomerItemsDTO(){{setZteCode("2");setProjectType("pt2");}})
        );
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_7303)).thenReturn(
                Lists.newArrayList()
        );
        try {
            service.pushNewestData(
                    Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("2sdsd1 12:00:00");setLookupCode(LOOKUP_TYPE_6732008);}}),
                    new HashMap(){{put("SMT-A", "SMT");}});
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        try {
            service.pushNewestData(
                    Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("2023-01-01 12:00:00");setLookupCode(LOOKUP_TYPE_6732008);}}),
                    new HashMap(){{put("SMT-A", "SMT");}});
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_7303)).thenReturn(
                Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("pt");setLookupCode(LOOKUP_TYPE_6732008);}})
        );
        try {
            service.pushNewestData(
                    Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("2023-01-01 12:00:00");setLookupCode(LOOKUP_TYPE_6732008);}}),
                    new HashMap(){{put("SMT-A", "SMT");}});
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        PowerMockito.when(repairRcvDetailRepository.getRepairSnForCusDataPush(any(), any(), any(), any(), anyInt()))
                .thenReturn(Lists.newArrayList(new PmRepairRcvDetail(){{setSn("1");setReceptionDetailId("1");}}));
        PowerMockito.when(wipInfoRepository.getWipInfoByItemAndInterval(any(), any(), any(), anyInt(), anyString()))
                .thenReturn(Lists.newArrayList(new PsWipInfo(){{setSn("1");setRouteId("1");setCraftSection("1");}}));
        try {
            service.pushNewestData(
                    Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("2023-01-01 12:00:00");setLookupCode(LOOKUP_TYPE_6732008);}}),
                    new HashMap(){{put("SMT-A", "SMT");}});
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void pushBySn() throws Exception {
        try {
            service.pushBySn(new HashMap(){{put("SMT-B", "SMT");}},
                    Lists.newArrayList(
                            new PsWipInfo(){{setSn("1");setRouteId("rt1");setWorkOrderNo("test123");}},
                            new PsWipInfo(){{setSn("2");setRouteId("rt2");setWorkOrderNo("test123");}}
                    ),
                    Lists.newArrayList(new CustomerItemsDTO(){{setZteCode("1");setProjectType("pt");}},
                            new CustomerItemsDTO(){{setZteCode("2");setProjectType("pt2");}}));
        } catch (MesBusinessException e) {}
        PowerMockito.when(CrafttechRemoteService.getRouteDetailForSnScanPush(any()))
                .thenReturn(Lists.newArrayList(new RouteDetailForSnScanPushDTO(){{setRouteId("rt1");setCraftSection("SMT-A");setPreSections("SMT-B");}}));
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(any()))
                .thenReturn(new ArrayList<>());
        try {
            service.pushBySn(new HashMap(){{put("SMT-B", "SMT");}},
                    Lists.newArrayList(
                            new PsWipInfo(){{setSn("1");setRouteId("rt1");setCraftSection("SMT-A");setWorkOrderNo("test123");}},
                            new PsWipInfo(){{setSn("2");setRouteId("rt2");setWorkOrderNo("test123");}}
                    ),
                    Lists.newArrayList(new CustomerItemsDTO(){{setZteCode("1");setProjectType("pt");}},
                            new CustomerItemsDTO(){{setZteCode("2");setProjectType("pt2");}}));
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        PowerMockito.when(commonRepository.snInformationPushCommonQuery(any())).thenReturn(
                Lists.newArrayList(new HashMap(){{
                    put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                    put("create_by", "1");put("line_code", "1");put("item_no", "1");
                }})
        );
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setRouteId("rt1");setWorkOrderNo("test123");}}));
        try {
            service.pushBySn(new HashMap(){{put("SMT-B", "SMT");}},
                    Lists.newArrayList(
                            new PsWipInfo(){{setSn("1");setRouteId("rt1");setCraftSection("SMT-A");setWorkOrderNo("test123");}},
                            new PsWipInfo(){{setSn("2");setRouteId("rt2");setWorkOrderNo("test123");}}
                    ),
                    Lists.newArrayList(new CustomerItemsDTO(){{setZteCode("1");setProjectType("pt");}},
                            new CustomerItemsDTO(){{setZteCode("2");setProjectType("pt2");}}));
        } catch (MesBusinessException e) {
            Assert.assertFalse(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void pushToB2B() {
        try {
            service.pushToB2B(SnInformationPushConstant.BYTE_DANCE_SN_SCAN_DATA,
                    Lists.newArrayList(new HashMap(){{
                        put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("boardSn", "2");put("stationId", "2");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "2");
                    }}),
                    Lists.newArrayList(new CustomerItemsDTO(){{setZteCode("1");setProjectType("pt");}}));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            service.pushToB2B(SnInformationPushConstant.BYTE_DANCE_SN_SCAN_DATA,
                    Lists.newArrayList(new HashMap(){{
                        put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}), null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setPushDataLog() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setDataByTid() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setProjectData() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getSnScanBaseData() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getOutData() {
        try {
            Assert.assertNotNull(service.getOutData(SnInformationPushConstant.BYTE_DANCE_SN_SCAN_DATA,
                    Lists.newArrayList(new HashMap(){{
                        put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap())));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getSubData() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getDataByConfig() throws Exception{
        try {
            Assert.assertNotNull(service.getDataByConfig(SnInformationPushConstant.BYTE_DANCE_SN_MDS_DATA,
                    Lists.newArrayList(new HashMap(){{
                        put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("item_no", "1");
                    }})));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            service.getDataByConfig(LINE_NAME,
                    Lists.newArrayList(new HashMap(){{
                        put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("itemNo", "1");
                    }}, new HashMap(){{
                        put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                        put("create_by", "1");put("line_code", "1");put("item_no", "1");
                    }}));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            service.getDataByConfig(DataModuleConfig.builder().build(),
                    Lists.newArrayList());
        } catch (Exception e) {}
        try {
            service.getDataByConfig(DataModuleConfig.builder().fieldMappings(LINE_NAME.getFieldMappings())
                            .build(),
                    Lists.newArrayList());
        } catch (Exception e) {}
    }

    @Test
    public void getDataByMicroservice() {
        try {
            service.getDataByMicroservice(DataModuleConfig.builder().build(),
                    Lists.newArrayList());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_MICROSERVICES_CONFIGURED, e.getMessage());
        }
        Assert.assertThrows(NullPointerException.class, () -> service.getDataByMicroservice(DataModuleConfig.builder().fieldMappings(SnInformationPushConstant
                .BYTE_DANCE_SN_MDS_DATA.getFieldMappings()).interfaceEnum(queryRecordBySn).build(), Lists.newArrayList()));

    }

    @Test
    public void getMicroserviceParam() {
        service.getMicroserviceParam(Lists.newArrayList(), null);
        Assert.assertNotNull(service.getMicroserviceParam(SnInformationPushConstant.BYTE_DANCE_SN_MDS_DATA.getFieldMappings(),
                new HashMap(){{
                    put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                    put("create_by", "1");put("line_code", "1");put("item_no", "1");
                }}));
    }

    @Test
    public void getDataByInvoke() {
        try {
            Assert.assertNotNull(service.getDataByInvoke(DataModuleConfig.builder().build(),
                    Lists.newArrayList()));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_REFLECTION_METHOD_CONFIGURED, e.getMessage());
        }
    }

    @Test
    public void getDataByMethod() {
        try {
            Assert.assertNotNull(service.getDataByMethod("1",
                    Lists.newArrayList()));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REFLECTION_METHOD_CONFIGURATION_ERROR, e.getMessage());
        }
    }

    @Test
    public void getSubModuleData() {
        try {
            service.getSubModuleData(Lists.newArrayList(FieldMapping.builder().build()), Lists.newArrayList());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SUB_MODULE_NOT_CONFIGURED, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setSubDataToMain() {
        try {
            service.setSubDataToMain(Lists.newArrayList(new HashMap(){{
                put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("item_no", "1");
            }}),Lists.newArrayList(new HashMap(){{
                put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("item_no", "1");
            }}), FieldMapping.builder().subModule(BYTE_DANCE_SN_REPAIR_DATA).outFieldName("problems")
                    .specialFlag(SnInformationPushConstant.FieldsSpecialTypes.SUB_MODULE).outFieldType(SnInformationPushConstant.FieldsTypes.LIST).build());
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            service.setSubDataToMain(Lists.newArrayList(new HashMap(){{
                put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("item_no", "1");
            }}),Lists.newArrayList(new HashMap(){{
                put("boardSn", "1");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("boardSn", "2");put("stationId", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("itemNo", "1");
            }}, new HashMap(){{
                put("sn", "1");put("craft_section", "1");put("craft_name", "1");
                put("create_by", "1");put("line_code", "1");put("item_no", "1");
            }}), FieldMapping.builder().inFieldName("lineName").outFieldName("lineBody")
                    .subModule(LINE_NAME).build());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void singleFieldJoin() {
        service.singleFieldJoin(Lists.newArrayList(FieldMapping.builder().inFieldName("sn").outFieldName("boardSn").tableAlias("s").build()),
                Lists.newArrayList(new HashMap()), "sql");
        Assert.assertNotNull(service.singleFieldJoin(Lists.newArrayList(FieldMapping.builder().inFieldName("sn").outFieldName("boardSn").build()),
                Lists.newArrayList(new HashMap()), "sql"));
    }

    @Test
    public void specialFieldProcessing() {
        service.specialFieldProcessing(Lists.newArrayList());
        service.specialFieldProcessing(Lists.newArrayList(new HashMap(),
                new HashMap(){{put(PROBLEMS, Lists.newArrayList(new HashMap()));}}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setCraftToSection() {
        service.setCraftToSection(new SysLookupValuesDTO());
        service.setCraftToSection(new SysLookupValuesDTO(){{setAttribute1("1");}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    /* Started by AICoder, pid:1e667n6fec4fca614b6b08f1b2d49e482e317276 */
    @Test
    public void snScanDataPushOfTencentTest() throws Exception {
        java.util.List<CustomerDataLogDTO> dataLogs = new ArrayList<>();
        List<String> boardSnList = new ArrayList<>();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService
                .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, Constant.LOOKUP_1003022))).thenReturn(null);
        try {
            service.snScanDataPushOfTencent(dataLogs, boardSnList);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.SYS_LOOK_NOT_CONFIG));
        }
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, Constant.LOOKUP_1003022)))
                .thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("SMT");setLookupCode(LOOKUP_TYPE_6732008);setLookupType(new BigDecimal(LOOKUP_TYPE_6732));}}));
        try {
            service.snScanDataPushOfTencent(dataLogs, boardSnList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValuesDTO> sysValueList = new ArrayList<>();
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupType(new BigDecimal("1003022"));
        dto.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_1003022001));
        dto.setLookupMeaning("SMT-A,SMT-B,ICT,Test");
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, Constant.LOOKUP_1003022)))
                .thenReturn(sysValueList);
        try {
            service.snScanDataPushOfTencent(dataLogs, boardSnList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupValuesDTO dto1 = new SysLookupValuesDTO();
        dto1.setLookupType(new BigDecimal("6734"));
        dto1.setLookupCode(new BigDecimal("6734001"));
        dto1.setLookupMeaning("SMT-A");
        dto1.setAttribute1("组装");
        sysValueList.add(dto1);
        sysValueList.add(dto);
        dataLogs.add(new CustomerDataLogDTO());
        PowerMockito.when(BasicsettingRemoteService
                        .getBatchSysValueByCode(Lists.newArrayList(LOOKUP_TYPE_6732, LOOKUP_TYPE_6734, Constant.LOOKUP_1003022)))
                .thenReturn(sysValueList);
        service.snScanDataPushOfTencent(dataLogs, boardSnList);
        Assert.assertTrue(dataLogs.size() == 1);
        try {
            service.snScanDataPushOfTencent(null, boardSnList);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
    @Test
    public void pushLastDataOfTencentTest() throws Exception {
        List<SysLookupValuesDTO> lookUps = new ArrayList<>();
        Map<String, String> craftMap = new HashMap<>();
        List<String> boardSnList = new ArrayList<>();
        try {
            service.pushLastDataOfTencent(lookUps, craftMap, boardSnList);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupCode(LOOKUP_TYPE_1003022002);
        dto.setLookupMeaning("sds");
        lookUps.add(dto);
        try {
            service.pushLastDataOfTencent(lookUps, craftMap, boardSnList);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PUSH_DATE_TIME_NULL));
        }

        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<String, String>() {{put(Constants.HTTP_HEADER_X_FACTORY_ID.toLowerCase(), "52");}});
        dto.setLookupMeaning("2011-11-11 11:11:11");

        service.pushLastDataOfTencent(lookUps, craftMap, boardSnList);
    }

    @Test
    public void handlerPassStationInfoTest() throws Exception {
        List<ZmsBoardSnTencentDTO> tempList = new ArrayList<>();
        Map<String, String> craftMap = new HashMap<>();
        ZmsBoardSnTencentDTO zmsBoardSnTencentDTO1 = new ZmsBoardSnTencentDTO();
        zmsBoardSnTencentDTO1.setBoardSn("701223300001");
        tempList.add(zmsBoardSnTencentDTO1);
        ZmsBoardSnTencentDTO zmsBoardSnTencentDTO2 = new ZmsBoardSnTencentDTO();
        zmsBoardSnTencentDTO2.setBoardSn("701223400001");
        tempList.add(zmsBoardSnTencentDTO2);
        ZmsBoardSnTencentDTO zmsBoardSnTencentDTO3 = new ZmsBoardSnTencentDTO();
        zmsBoardSnTencentDTO3.setBoardSn("701223");
        tempList.add(zmsBoardSnTencentDTO3);
        ZmsBoardSnTencentDTO zmsBoardSnTencentDTO4 = new ZmsBoardSnTencentDTO();
        zmsBoardSnTencentDTO4.setBoardSn("701223500001");
        tempList.add(zmsBoardSnTencentDTO4);

        List<PsTask> taskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("7012233");
        psTask1.setItemNo("itemNo1");
        psTask1.setTaskNo("taskNo1");
        taskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("7012235");
        psTask2.setItemNo("123456789123ABC");
        psTask2.setTaskNo("taskNo2");
        taskList.add(psTask2);
        PowerMockito.when(planscheduleRemoteService.getTaskListByProdPlanId(Mockito.anyList())).thenReturn(taskList);

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("sda");
        customerItemsDTOList.add(customerItemsDTO1);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("123456789123");
        customerItemsDTO2.setCustomerMaterialType("test");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.queryListByCustomerAndItemNoList(Mockito.anyString(), Mockito.anyList())).thenReturn(customerItemsDTOList);

        List<BBomDetailDTO> pcbItemCodeInfoList = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
        bBomDetailDTO1.setProductCode("itemNo1");
        bBomDetailDTO1.setItemCode("itemCode1");
        pcbItemCodeInfoList.add(bBomDetailDTO1);
        BBomDetailDTO bBomDetailDTO2 = new BBomDetailDTO();
        bBomDetailDTO2.setProductCode("itemNo21");
        bBomDetailDTO2.setItemCode("itemCode21");
        bBomDetailDTO2.setUsageCount(new BigDecimal("4"));
        pcbItemCodeInfoList.add(bBomDetailDTO2);
        BBomDetailDTO bBomDetailDTO3 = new BBomDetailDTO();
        bBomDetailDTO3.setProductCode("itemNo22");
        bBomDetailDTO3.setItemCode("itemCode22");
        bBomDetailDTO3.setUsageCount(new BigDecimal("2"));
        pcbItemCodeInfoList.add(bBomDetailDTO3);
        PowerMockito.when(centerfactoryRemoteService.getPcbItemCode(Mockito.anyList())).thenReturn(pcbItemCodeInfoList);


        List<PsTask> subTaskInfoByZbjPlanNoList = new ArrayList<>();
        PsTask psTaskSub1 = new PsTask();
        psTaskSub1.setZbjprodplanNo("taskNo2");
        psTaskSub1.setTaskNo("taskNo2-1");
        psTaskSub1.setItemNo("itemNo21");
        psTaskSub1.setProdplanId("7012236");
        subTaskInfoByZbjPlanNoList.add(psTaskSub1);
        PsTask psTaskSub2 = new PsTask();
        psTaskSub2.setZbjprodplanNo("taskNo2");
        psTaskSub2.setTaskNo("taskNo2-2");
        psTaskSub2.setItemNo("itemNo22");
        psTaskSub2.setProdplanId("7012237");
        subTaskInfoByZbjPlanNoList.add(psTaskSub2);
        PowerMockito.when(planscheduleRemoteService.getSubTaskInfoByZbjPlanNoList(Mockito.anyList())).thenReturn(subTaskInfoByZbjPlanNoList);


        List<BarcodePriceApiDTO> materialInformation = new ArrayList<>();
        BarcodePriceApiDTO barcodePriceApiDTO = new BarcodePriceApiDTO();
        barcodePriceApiDTO.setProdPlanID("7012237");
        barcodePriceApiDTO.setItemCode("itemCode22");
        materialInformation.add(barcodePriceApiDTO);
        PowerMockito.when(digitalPlatformRemoteService.getMaterialInformation(Mockito.anyString(), Mockito.anyList())).thenReturn(materialInformation);
        try {
            Whitebox.invokeMethod(service, "handlerPassStationInfo", tempList, craftMap);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        barcodePriceApiDTO.setSupplierName("supplierName");
        try {
            Whitebox.invokeMethod(service, "handlerPassStationInfo", tempList, craftMap);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        barcodePriceApiDTO.setProductNo("productNo");



        List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
        WipScanHistory wipScanHistory1 = new WipScanHistory();
        wipScanHistory1.setSn("701223300001");
        wipScanHistory1.setWorkOrderNo("7012233-SMT-A");
        wipScanHistory1.setCraftSection("SMT-A");
        wipScanHistory1.setCreateDate(new Date());
        wipScanHistoryList.add(wipScanHistory1);

        WipScanHistory wipScanHistory2 = new WipScanHistory();
        wipScanHistory2.setSn("701223400001");
        wipScanHistory2.setWorkOrderNo("7012234-SMT-A");
        wipScanHistory2.setCraftSection("SMT-A");
        wipScanHistory2.setCreateDate(new Date());
        wipScanHistoryList.add(wipScanHistory2);

        WipScanHistory wipScanHistory3 = new WipScanHistory();
        wipScanHistory3.setSn("701223500001");
        wipScanHistory3.setWorkOrderNo("7012235-SMT-A");
        wipScanHistory3.setCraftSection("SMT-A");
        wipScanHistory3.setCreateDate(new Date());
        wipScanHistoryList.add(wipScanHistory3);
        PowerMockito.when(wipScanHistoryRepository.getWipScanHistoryListBySnAndCraft(Mockito.anyList(), Mockito.anyList())).thenReturn(wipScanHistoryList);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.anyMap())).thenReturn(workOrderList);
        Whitebox.invokeMethod(service, "handlerPassStationInfo", tempList, craftMap);
        PsWorkOrderBasic psWorkOrderBasic1 = new PsWorkOrderBasic();
        psWorkOrderBasic1.setRouteId("route1");
        psWorkOrderBasic1.setWorkOrderNo("7012233-SMT-A");
        workOrderList.add(psWorkOrderBasic1);
        PsWorkOrderBasic psWorkOrderBasic2 = new PsWorkOrderBasic();
        psWorkOrderBasic2.setRouteId("route2");
        psWorkOrderBasic2.setWorkOrderNo("7012234-SMT-A");
        workOrderList.add(psWorkOrderBasic2);
        PsWorkOrderBasic psWorkOrderBasic3 = new PsWorkOrderBasic();
        psWorkOrderBasic3.setRouteId("route3");
        psWorkOrderBasic3.setWorkOrderNo("7012235-SMT-A");
        workOrderList.add(psWorkOrderBasic3);
        Whitebox.invokeMethod(service, "handlerPassStationInfo", tempList, craftMap);
        Assert.assertTrue(tempList.size() > 0);
        List<RouteDetailForSnScanPushDTO> routes = new ArrayList<>();
        RouteDetailForSnScanPushDTO routeDetailForSnScanPushDTO1 = new RouteDetailForSnScanPushDTO();
        routeDetailForSnScanPushDTO1.setRouteId("route1");
        routeDetailForSnScanPushDTO1.setCraftSection("SMT-A");
        routeDetailForSnScanPushDTO1.setPreProcess("0");
        routes.add(routeDetailForSnScanPushDTO1);
        RouteDetailForSnScanPushDTO routeDetailForSnScanPushDTO2 = new RouteDetailForSnScanPushDTO();
        routeDetailForSnScanPushDTO2.setRouteId("route2");
        routeDetailForSnScanPushDTO2.setCraftSection("SMT-A");
        routeDetailForSnScanPushDTO2.setPreProcess("0");
        routes.add(routeDetailForSnScanPushDTO2);
        RouteDetailForSnScanPushDTO routeDetailForSnScanPushDTO3 = new RouteDetailForSnScanPushDTO();
        routeDetailForSnScanPushDTO3.setRouteId("route1");
        routeDetailForSnScanPushDTO3.setCraftSection("SMT-A");
        routeDetailForSnScanPushDTO3.setPreProcess("0");
        routes.add(routeDetailForSnScanPushDTO3);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getRouteDetailForSnScanPush(Mockito.anyList())).thenReturn(routes);
        Whitebox.invokeMethod(service, "handlerPassStationInfo", tempList, craftMap);
        Assert.assertTrue(tempList.size() > 0);
    }

    @Test
    public void queryPcbSupAndLotTest() throws Exception {
        Map<String, Pair<String, String>> snToProdIdAndPcbItemN = new HashMap<>();
        Map<String, Pair<String, String>> result = Whitebox.invokeMethod(service, "queryPcbSupAndLot", snToProdIdAndPcbItemN);
        Assert.assertTrue(result.size() == 0);
    }
    /* Ended by AICoder, pid:1e667n6fec4fca614b6b08f1b2d49e482e317276 */
}