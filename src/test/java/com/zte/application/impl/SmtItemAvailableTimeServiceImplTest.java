package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.PsWipInfoService;
import com.zte.application.ScrapBillDetailService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
public class SmtItemAvailableTimeServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    SmtItemAvailableTimeServiceImpl service;



    @Test
    public void generateAvailableTimedData()throws Exception {
        Assert.assertNotNull(service.generateAvailableTimedData(new BigDecimal(55),new SMTScanParamDTO(){{setPcbQty(5);setSmtItemAvailableTimeDTO(new SmtItemAvailableTimeDTO());}},new SmtMachineMaterialMouting(),new BigDecimal(55),"2"));
    }

}