package com.zte.application.impl;

import com.zte.application.IMESLogService;
import com.zte.common.DateUtil;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.common.model.MessageId;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class
        , RedisHelper.class, IMESLogService.class,Constant.class})
public class AssemblyOptRecordServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    AssemblyOptRecordServiceImpl service;

    @Mock
    private AssemblyOptRecordRepository assemblyOptRecordrepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private WipExtendIdentificationServiceImpl wipExtendIdentificationService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private IMESLogService imesLogService;

    @Test
    public void getListByParams() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class);
        AssemblyRelationshipQueryDTO dto = new AssemblyRelationshipQueryDTO();
        dto.setTaskNo("test123");
        dto.setEndTime(new Date());
        PowerMockito.when(assemblyOptRecordrepository.getPageListByParams(Mockito.any())).thenReturn(new ArrayList<>());
        service.getPageListByParams(dto);
        PowerMockito.when(assemblyOptRecordrepository.getPageListCountByParams(Mockito.any())).thenReturn(1);
        service.getPageListByParams(dto);
        List<AssemblyOptRecordEntityDTO> optRecordEntityDTOList = new ArrayList<>();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        assemblyOptRecordEntityDTO.setCreateDate(new Date());
        assemblyOptRecordEntityDTO.setProcessCode("1");
        assemblyOptRecordEntityDTO.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO.setOptType("1");
        assemblyOptRecordEntityDTO.setFormType("1");
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO);
        List<BSProcessDTO> bsProcessDTOList = new ArrayList<>();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("1");
        bsProcessDTO.setProcessName("1");
        bsProcessDTOList.add(bsProcessDTO);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1");
        sysLookupTypesDTO.setDescriptionChinV("test");
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.when(assemblyOptRecordrepository.getPageListByParams(Mockito.any())).thenReturn(optRecordEntityDTOList);
        PowerMockito.when(wipEntityScanInfoService.getProcessInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(bsProcessDTOList);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue((String) Mockito.any())).thenReturn(sysLookupTypesDTOList);
        service.getPageListByParams(dto);
        assemblyOptRecordEntityDTO.setOptType("2");
        service.getPageListByParams(dto);
        assemblyOptRecordEntityDTO.setOptType("3");
        service.getPageListByParams(dto);
        assemblyOptRecordEntityDTO.setFormType("2");
        Assert.assertNotNull(service.getPageListByParams(dto));
    }

    @Test
    public void sendAssemblyRelationToMes() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class,IMESLogService.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        List<AssemblyOptRecordEntityDTO> optRecordEntityDTOList = new ArrayList<>();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        assemblyOptRecordEntityDTO.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO.setProcessCode("1");
        assemblyOptRecordEntityDTO.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO.setProdplanId("test123");
        assemblyOptRecordEntityDTO.setFormSn("2865230001");
        assemblyOptRecordEntityDTO.setSn("28652300001");
        assemblyOptRecordEntityDTO.setOptType("1");
        assemblyOptRecordEntityDTO.setFormType("1");
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO1 = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO1.setCreateBy("00286523");
        assemblyOptRecordEntityDTO1.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO1.setProcessCode("1");
        assemblyOptRecordEntityDTO1.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO1.setProdplanId("test123");
        assemblyOptRecordEntityDTO1.setOptType("2");
        assemblyOptRecordEntityDTO1.setFormType("1");
        assemblyOptRecordEntityDTO1.setFormSn("2865230002");
        assemblyOptRecordEntityDTO1.setSn("28652300002");
        AssemblyRelationshipQueryDTO param = new AssemblyRelationshipQueryDTO();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO2 = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        assemblyOptRecordEntityDTO2.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO2.setProcessCode("1");
        assemblyOptRecordEntityDTO2.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO2.setProdplanId("test123");
        assemblyOptRecordEntityDTO2.setOptType("3");
        assemblyOptRecordEntityDTO2.setFormType("1");
        assemblyOptRecordEntityDTO2.setSubItemNo("test123");
        assemblyOptRecordEntityDTO2.setFormSn("2865230003");
        assemblyOptRecordEntityDTO2.setSn("28652300003");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(assemblyOptRecordrepository.getCountByParams(Mockito.any())).thenReturn(2);
        PowerMockito.when(assemblyOptRecordrepository.getListByParams(Mockito.any())).thenReturn(optRecordEntityDTOList).thenReturn(new ArrayList<>());
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO);
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO1);
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO2);
        sysLookupTypesDTO.setLookupMeaning("2023-07-19 00:00:00");
        sysLookupTypesDTO.setAttribute1("-20");
        sysLookupTypesDTO.setAttribute2("10");
        List<PsTask> tempPaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("test123");
        psTask.setItemNo("test123test123test123");
        psTask.setTaskNo("test123");
        psTask.setExternalType("test123");
        psTask.setOrgId(new BigDecimal("395"));
        tempPaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoList(Mockito.any())).thenReturn(tempPaskList);
        List<WipExtendIdentification> wipExtendListForAdd = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("test123test123test123");
        wipExtendIdentification.setFormSn("2865230001");
        wipExtendIdentification.setSn("28652300001");
        wipExtendIdentification.setTaskNo("test123");
        wipExtendIdentification.setCreateBy("286523");
        wipExtendIdentification.setCreateDate(new Date());
        wipExtendIdentification.setLastUpdatedBy("286523");
        wipExtendIdentification.setLastUpdatedDate(new Date());
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setItemNo("test123");
        wipExtendIdentification1.setFormSn("2865230002");
        wipExtendIdentification1.setSn("28652300002");
        wipExtendIdentification1.setTaskNo("test123");
        wipExtendIdentification1.setCreateBy("286523");
        wipExtendIdentification1.setCreateDate(new Date());
        wipExtendIdentification1.setLastUpdatedBy("286523");
        wipExtendIdentification1.setLastUpdatedDate(new Date());
        WipExtendIdentification wipExtendIdentification2 = new WipExtendIdentification();
        wipExtendIdentification2.setItemNo("test123");
        wipExtendIdentification2.setFormSn("2865230003");
        wipExtendIdentification2.setSn("28652300003");
        wipExtendIdentification2.setTaskNo("test123");
        wipExtendIdentification2.setCreateBy("286523");
        wipExtendIdentification2.setCreateDate(new Date());
        wipExtendIdentification2.setLastUpdatedBy("286523");
        wipExtendIdentification2.setLastUpdatedDate(new Date());
        wipExtendListForAdd.add(wipExtendIdentification);
        wipExtendListForAdd.add(wipExtendIdentification1);
        wipExtendListForAdd.add(wipExtendIdentification2);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(Mockito.any(), Mockito.anyList())).thenReturn(wipExtendListForAdd);
        List<MtlSystemItemsDTO> mtlSystemItemsDTOS = new ArrayList<>();
        MtlSystemItemsDTO mtlSystemItemsDTO = new MtlSystemItemsDTO();
        mtlSystemItemsDTO.setSegment1("test123test1");
        mtlSystemItemsDTOS.add(mtlSystemItemsDTO);
        PowerMockito.when(datawbRemoteService.getItemIdBySegment1List(Mockito.any())).thenReturn(mtlSystemItemsDTOS);
        mtlSystemItemsDTO.setInventoryItemId(1);
        service.sendAssemblyRelationToMes(param, "55");
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("test123");
        bsItemInfo.setItemName("test123");
        bsItemInfo.setVersion("test123");
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("test123test123test123");
        bsItemInfo1.setItemName("test123");
        bsItemInfo1.setVersion("test123");
        bsItemInfoList.add(bsItemInfo1);
        bsItemInfoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getItemByAmbiguityNos(Mockito.anyList())).thenReturn(bsItemInfoList);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(new ArrayList<>());
        List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS = new ArrayList<>();
        WsmAssembleLinesDTO wsmAssembleLinesDTO = new WsmAssembleLinesDTO();
        wsmAssembleLinesDTO.setMainItemBarcode("2865230003");
        wsmAssembleLinesDTO.setSubItemBarcode("28652300003");
        wsmAssembleLinesDTOS.add(wsmAssembleLinesDTO);
        PowerMockito.when(datawbRemoteService.getWsmAssemblyListByMainAndSub(Mockito.any())).thenReturn(wsmAssembleLinesDTOS);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(datawbRemoteService).batchInsertOrDeleteAssemble(any());
        service.sendAssemblyRelationToMes(param, "55");
        assemblyOptRecordEntityDTO1.setOptType("1");
        assemblyOptRecordEntityDTO2.setOptType("1");
        service.sendAssemblyRelationToMes(param, "55");

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        param.setTaskNo("test123");
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        param.setTaskNo("");
        param.setStartTime(DateUtil.convertStringToDate("2023-10-01 00:00:00", DateUtil.DATE_FORMATE_FULL));
        param.setEndTime(DateUtil.convertStringToDate("2023-10-20 00:00:00", DateUtil.DATE_FORMATE_FULL));
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }


    @Test
    public void sendAssemblyRelationToMesTest() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class,IMESLogService.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        List<AssemblyOptRecordEntityDTO> optRecordEntityDTOList = new ArrayList<>();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        assemblyOptRecordEntityDTO.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO.setProcessCode("1");
        assemblyOptRecordEntityDTO.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO.setProdplanId("test123");
        assemblyOptRecordEntityDTO.setFormSn("2865230001");
        assemblyOptRecordEntityDTO.setSn("28652300001");
        assemblyOptRecordEntityDTO.setOptType("1");
        assemblyOptRecordEntityDTO.setFormType("1");
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO1 = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO1.setCreateBy("00286523");
        assemblyOptRecordEntityDTO1.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO1.setProcessCode("1");
        assemblyOptRecordEntityDTO1.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO1.setProdplanId("test123");
        assemblyOptRecordEntityDTO1.setOptType("2");
        assemblyOptRecordEntityDTO1.setFormType("1");
        assemblyOptRecordEntityDTO1.setFormSn("2865230002");
        assemblyOptRecordEntityDTO1.setSn("28652300002");
        AssemblyRelationshipQueryDTO param = new AssemblyRelationshipQueryDTO();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO2 = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        assemblyOptRecordEntityDTO2.setCreateDate(DateUtil.convertStringToDate("2023-10-01 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyOptRecordEntityDTO2.setProcessCode("1");
        assemblyOptRecordEntityDTO2.setWorkStation("SMT-A");
        assemblyOptRecordEntityDTO2.setProdplanId("test123");
        assemblyOptRecordEntityDTO2.setOptType("3");
        assemblyOptRecordEntityDTO2.setFormType("1");
        assemblyOptRecordEntityDTO2.setSubItemNo("test123");
        assemblyOptRecordEntityDTO2.setFormSn("2865230003");
        assemblyOptRecordEntityDTO2.setSn("28652300003");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(assemblyOptRecordrepository.getCountByParams(Mockito.any())).thenReturn(2);
        PowerMockito.when(assemblyOptRecordrepository.getListByParams(Mockito.any())).thenReturn(optRecordEntityDTOList).thenReturn(new ArrayList<>());
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try {
            service.sendAssemblyRelationToMes(param, "55");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO);
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO1);
        optRecordEntityDTOList.add(assemblyOptRecordEntityDTO2);
        sysLookupTypesDTO.setLookupMeaning("2023-07-19 00:00:00");
        sysLookupTypesDTO.setAttribute1("-20");
        sysLookupTypesDTO.setAttribute2("10");
        List<PsTask> tempPaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("test123");
        psTask.setItemNo("test123test123test123");
        psTask.setTaskNo("test123");
        psTask.setExternalType("test123");
        psTask.setOrgId(new BigDecimal("395"));
        tempPaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoList(Mockito.any())).thenReturn(tempPaskList);
        List<WipExtendIdentification> wipExtendListForAdd = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("test123test123test123");
        wipExtendIdentification.setFormSn("2865230001");
        wipExtendIdentification.setSn("28652300001");
        wipExtendIdentification.setTaskNo("test123");
        wipExtendIdentification.setCreateBy("286523");
        wipExtendIdentification.setCreateDate(new Date());
        wipExtendIdentification.setLastUpdatedBy("286523");
        wipExtendIdentification.setLastUpdatedDate(new Date());
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setItemNo("test123");
        wipExtendIdentification1.setFormSn("2865230002");
        wipExtendIdentification1.setSn("28652300002");
        wipExtendIdentification1.setTaskNo("test123");
        wipExtendIdentification1.setCreateBy("286523");
        wipExtendIdentification1.setCreateDate(new Date());
        wipExtendIdentification1.setLastUpdatedBy("286523");
        wipExtendIdentification1.setLastUpdatedDate(new Date());
        WipExtendIdentification wipExtendIdentification2 = new WipExtendIdentification();
        wipExtendIdentification2.setItemNo("test123");
        wipExtendIdentification2.setFormSn("2865230003");
        wipExtendIdentification2.setSn("28652300003");
        wipExtendIdentification2.setTaskNo("test123");
        wipExtendIdentification2.setCreateBy("286523");
        wipExtendIdentification2.setCreateDate(new Date());
        wipExtendIdentification2.setLastUpdatedBy("286523");
        wipExtendIdentification2.setLastUpdatedDate(new Date());
        wipExtendListForAdd.add(wipExtendIdentification);
        wipExtendListForAdd.add(wipExtendIdentification1);
        wipExtendListForAdd.add(wipExtendIdentification2);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(Mockito.any(), Mockito.anyList())).thenReturn(wipExtendListForAdd);
        List<MtlSystemItemsDTO> mtlSystemItemsDTOS = new ArrayList<>();
        MtlSystemItemsDTO mtlSystemItemsDTO = new MtlSystemItemsDTO();
        mtlSystemItemsDTO.setSegment1("test123test1");
        mtlSystemItemsDTOS.add(mtlSystemItemsDTO);
        PowerMockito.when(datawbRemoteService.getItemIdBySegment1List(Mockito.any())).thenReturn(mtlSystemItemsDTOS);
        service.sendAssemblyRelationToMes(param, "55");
    }

    @Test
    public void sendEmailForFail() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,Constant.class);
        List<WipExtendIdentification> errorBuildList = new ArrayList<>();
        service.sendEmailForFail(errorBuildList);
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormSn("test123");
        wipExtendIdentification.setFormItemNo("test123");
        wipExtendIdentification.setSn("test123");
        wipExtendIdentification.setItemNo("test123");
        wipExtendIdentification.setRemark(Constant.SENT_ASSEMBLY_TO_MES_EXIST);
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setFormSn("test123");
        wipExtendIdentification1.setFormItemNo("test123");
        wipExtendIdentification1.setSn("test123");
        wipExtendIdentification1.setItemNo("test123");
        wipExtendIdentification1.setRemark("test4565");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("00286523");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(emailUtils.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        errorBuildList.add(wipExtendIdentification);
        errorBuildList.add(wipExtendIdentification1);
        service.sendEmailForFail(errorBuildList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void buildAddAssemblyToMES() throws Exception {
        service.buildAddAssemblyToMES(new ArrayList<>(), new ArrayList<>());

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<WipExtendIdentification> addWipList = new LinkedList<>();
        WipExtendIdentification a1 = new WipExtendIdentification();
        a1.setFormSn("123");
        a1.setSn("234");
        addWipList.add(a1);
        service.buildAddAssemblyToMES(new ArrayList<>(), addWipList);
        PowerMockito.when(BasicsettingRemoteService.getItemByAmbiguityNos(Mockito.any()))
                .thenReturn(null);
        Assert.assertNotNull(service.buildAddAssemblyToMES(new ArrayList<>(), addWipList));
    }

    @Test
    public void getBarcodeInfo() throws Exception {
        Assert.assertNotNull(service.getBarcodeInfo(new ArrayList<>()));
    }

    @Test
    public void getQueryDTO() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        try {
            service.getQueryDTO(new AssemblyRelationshipQueryDTO());
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("2023-09-09 00:00:00");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        service.getQueryDTO(new AssemblyRelationshipQueryDTO());
        sysLookupTypesDTO.setAttribute1("5");
        sysLookupTypesDTO.setAttribute2("10");
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO=new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setTaskNo("test123");
        service.getQueryDTO(assemblyRelationshipQueryDTO);
        sysLookupTypesDTO.setAttribute1("");
        sysLookupTypesDTO.setAttribute2("");
        assemblyRelationshipQueryDTO.setTaskNo("");
        assemblyRelationshipQueryDTO.setStartTime(DateUtil.convertStringToDate("2023-09-09 00:00:00",DateUtil.DATE_FORMATE_FULL));
        assemblyRelationshipQueryDTO.setEndTime(DateUtil.convertStringToDate("2023-10-09 00:00:00",DateUtil.DATE_FORMATE_FULL));
        service.getQueryDTO(assemblyRelationshipQueryDTO);
    }

    @Test
    public void buildDeleteAssemblyToMES() {
        List<AssemblyWriteBackDto> removeDtoList = new ArrayList<>();
        Assert.assertEquals(removeDtoList,service.buildDeleteAssemblyToMES(new ArrayList<>()));
    }

    @Test
    public void existedInMES() {
        List<WsmAssembleLinesDTO> wsmAssembleLinesDTOS = new ArrayList<>();
        WsmAssembleLinesDTO wsmAssembleLinesDTO=new WsmAssembleLinesDTO();
        wsmAssembleLinesDTO.setMainItemBarcode("test123");
        wsmAssembleLinesDTO.setSubItemBarcode("test123");
        wsmAssembleLinesDTOS.add(wsmAssembleLinesDTO);
        service.existedInMES(new ArrayList<>(),"test123", "test123");
        service.existedInMES(wsmAssembleLinesDTOS,"test123", "");
        service.existedInMES(wsmAssembleLinesDTOS,"", "test123");
        Assert.assertTrue(service.existedInMES(wsmAssembleLinesDTOS,"test123", "test123"));
    }

    @Test
    public void buildAssembleLinesWriteBack() {
        WipExtendIdentification subWip = new WipExtendIdentification();
        subWip.setItemNo("test123456789");
        subWip.setSn("test123");
        subWip.setCreateBy("123");
        subWip.setLastUpdatedBy("123");
        subWip.setAttribute1("123");
        subWip.setFormQty(new BigDecimal("123"));
        subWip.setCreateDate(new Date());
        subWip.setLastUpdatedDate(new Date());
        service.buildAssembleLinesWriteBack(new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),subWip);
        List<MtlSystemItemsDTO> mtlSystemItemsDTOS = new ArrayList<>();
        MtlSystemItemsDTO mtlSystemItemsDTO =new MtlSystemItemsDTO();
        mtlSystemItemsDTO.setSegment1("test12345678");
        mtlSystemItemsDTO.setInventoryItemId(11);
        mtlSystemItemsDTOS.add(mtlSystemItemsDTO);
        service.buildAssembleLinesWriteBack(mtlSystemItemsDTOS,new ArrayList<>(),new ArrayList<>(),subWip);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("test123456789");
        bsItemInfo.setItemName("test123456789");
        bsItemInfo.setVersion("test123456789");
        bsItemInfoList.add(bsItemInfo);
        service.buildAssembleLinesWriteBack(mtlSystemItemsDTOS,bsItemInfoList,new ArrayList<>(),subWip);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO =new BarcodeExpandDTO();
        barcodeExpandDTO.setSupplierName("test123");
        barcodeExpandDTO.setBarcode("test123");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        service.buildAssembleLinesWriteBack(mtlSystemItemsDTOS,bsItemInfoList,barcodeExpandDTOList,subWip);
        subWip.setItemNo("");
        Assert.assertNotNull(service.buildAssembleLinesWriteBack(mtlSystemItemsDTOS,bsItemInfoList,barcodeExpandDTOList,subWip));
    }

    @Test
    public void buildMainSn() {
        WipExtendIdentification mainWip = new WipExtendIdentification();
        mainWip.setFormItemNo("test123456789");
        mainWip.setItemNo("test123456789");
        mainWip.setSn("test123");
        mainWip.setCreateBy("123");
        mainWip.setLastUpdatedBy("123");
        mainWip.setFormQty(new BigDecimal("123"));
        mainWip.setCreateDate(new Date());
        mainWip.setLastUpdatedDate(new Date());
        service.buildMainSn(new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),"test123",mainWip);
        List<MtlSystemItemsDTO> mtlSystemItemsDTOS = new ArrayList<>();
        MtlSystemItemsDTO mtlSystemItemsDTO =new MtlSystemItemsDTO();
        mtlSystemItemsDTO.setSegment1("test12345678");
        mtlSystemItemsDTO.setInventoryItemId(11);
        mtlSystemItemsDTOS.add(mtlSystemItemsDTO);
        service.buildMainSn(mtlSystemItemsDTOS,new ArrayList<>(),new ArrayList<>(),"test123",mainWip);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("test123456789");
        bsItemInfo.setItemName("test123456789");
        bsItemInfo.setVersion("test123456789");
        bsItemInfoList.add(bsItemInfo);
        service.buildMainSn(mtlSystemItemsDTOS,bsItemInfoList,new ArrayList<>(),"test123",mainWip);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO =new BarcodeExpandDTO();
        barcodeExpandDTO.setParentCategoryName( Constant.TYPE_BATCH_CODE);
        barcodeExpandDTO.setSupplierName("test123");
        barcodeExpandDTO.setBarcode("test123");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        service.buildMainSn(mtlSystemItemsDTOS,bsItemInfoList,barcodeExpandDTOList,"test123",mainWip);
        barcodeExpandDTO.setParentCategoryName("test123");
        service.buildMainSn(mtlSystemItemsDTOS,bsItemInfoList,barcodeExpandDTOList,"test123",mainWip);
        mainWip.setFormItemNo("");
        Assert.assertNotNull(service.buildMainSn(mtlSystemItemsDTOS,bsItemInfoList,barcodeExpandDTOList,"test123",mainWip));
    }

    @Test
    public void getReplaceExtendList() {
        service.getReplaceExtendList(new ArrayList<>(),new ArrayList<>());
        List<AssemblyOptRecordEntityDTO> optList = new ArrayList<>();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setOptType("3");
        assemblyOptRecordEntityDTO.setSubItemNo("1");
        assemblyOptRecordEntityDTO.setFormSn("w");
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        optList.add(assemblyOptRecordEntityDTO);
        List<WipExtendIdentification> returnList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("1");
        wipExtendIdentification.setFormSn("w");
        returnList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySubItemNoAndFormSn(Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        service.getReplaceExtendList(new ArrayList<>(),optList);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySubItemNoAndFormSn(Mockito.anyList(),Mockito.anyList())).thenReturn(returnList);
        service.getReplaceExtendList(new ArrayList<>(),optList);
        wipExtendIdentification.setFormSn("2");
        service.getReplaceExtendList(new ArrayList<>(),optList);
        wipExtendIdentification.setFormSn("w");
        wipExtendIdentification.setItemNo("2");
        service.getReplaceExtendList(new ArrayList<>(),optList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getAddWipExtendList() {
        service.getAddWipExtendList(new ArrayList<>(),new ArrayList<>());
        List<AssemblyOptRecordEntityDTO> optList = new ArrayList<>();
        AssemblyOptRecordEntityDTO assemblyOptRecordEntityDTO = new AssemblyOptRecordEntityDTO();
        assemblyOptRecordEntityDTO.setOptType("3");
        assemblyOptRecordEntityDTO.setSn("1");
        assemblyOptRecordEntityDTO.setFormSn("w");
        assemblyOptRecordEntityDTO.setCreateBy("00286523");
        optList.add(assemblyOptRecordEntityDTO);
        List<WipExtendIdentification> returnList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("1");
        wipExtendIdentification.setFormSn("w");
        returnList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        service.getAddWipExtendList(new ArrayList<>(),optList);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(Mockito.anyList(),Mockito.anyList())).thenReturn(returnList);
        service.getAddWipExtendList(new ArrayList<>(),optList);
        wipExtendIdentification.setFormSn("2");
        service.getAddWipExtendList(new ArrayList<>(),optList);
        wipExtendIdentification.setFormSn("w");
        wipExtendIdentification.setSn("2");
        service.getAddWipExtendList(new ArrayList<>(),optList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}