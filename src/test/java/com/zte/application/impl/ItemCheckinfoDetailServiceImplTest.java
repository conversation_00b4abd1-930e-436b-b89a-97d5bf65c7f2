package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.zte.domain.model.ItemCheckInfoDetail;
import com.zte.domain.model.ItemCheckInfoDetailRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.ItemCheckDifferenceDTO;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({RedisHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, AsyncExportFileCommonService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class, RedisLock.class, EasyExcelFactory.class})
public class ItemCheckinfoDetailServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ItemCheckinfoDetailServiceImpl itemCheckinfoDetailServiceImpl;

    @Mock
    ItemCheckInfoDetailRepository itemCheckInfoDetailRepository;

    @Test
    public void getPageByInfo(){
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        String prodPlanId = "7111333";
        List<ItemCheckInfoDetail> list = new ArrayList<>();
        PowerMockito.when(itemCheckInfoDetailRepository.getPageByInfo(any(), any(), any())).thenReturn(list);
        itemCheckinfoDetailServiceImpl.getPageByInfo(itemCheckDifferenceDTO, 1, 4000);
        Assert.assertEquals("7111333", prodPlanId);
    }

}