package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.AssemblyOptRecordService;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AssemblyRelationshipQueryDTO;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @Date 2022/9/30 14:03
 */
@PrepareForTest({BasicsettingRemoteService.class, RequestHeadValidationUtil.class,PlanscheduleRemoteService.class, DatawbRemoteService.class,
        RedisHelper.class, MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class,CrafttechRemoteService.class})
public class WipExtendIdentificationServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipExtendIdentificationServiceImpl service;

    @Mock
    private WipExtendIdentificationRepository repository;
    @Mock
    private PsWipInfoRepository wipInfoRepository;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private WriteBackSpmFailInfoRepository writeBackSpmFailInfoRepository;
    @Mock
    private AssemUnbindHistoryInfoRepository assemUnbindHistoryInfoRepository;
    @Mock
    private RedisLock redisLock;
    @Mock
    private AssemblyOptRecordService assemblyOptRecordService;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;
    @Mock
    private AssemblyOptRecordRepository assemblyOptRecordRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private WarehouseEntryDetailServiceImpl warehouseEntryDetailService;

    @Before
    public void init(){
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
    }

    /* Ended by AICoder, pid:9c6beyf0cbd55d914ac7087560660d4a6a342105 */
    @Test
    public  void queryingWipExtendIdentification(){
        List<WipExtendIdentification> list = service.queryingWipExtendIdentification(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
        PowerMockito.when(repository.assemblyRelationshipCount(Mockito.any())).thenReturn(0);
        service.queryingWipExtendIdentification("123");
        PowerMockito.when(repository.assemblyRelationshipCount(Mockito.any())).thenReturn(1);
        service.queryingWipExtendIdentification("123");
    }
    /* Ended by AICoder, pid:9c6beyf0cbd55d914ac7087560660d4a6a342105 */

    @Test
    public void queryQuantityProducedOfTaskNo() throws Exception {
        List<String> list =new ArrayList<>();
        Map<String, Integer> map = service.queryQuantityProducedOfTaskNo("");
        Assert.assertNotNull(map);
        list.add("1");
        map = service.queryQuantityProducedOfTaskNo("list");
        Assert.assertNotNull(map);
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setOutputQty(1);setProdPlanId("1");}});
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setOutputQty(1);setProdPlanId(null);}});
        PowerMockito.when(repository.queryQuantityProducedOfTaskNo(any())).thenReturn(wipExtendIdentificationList);
        map = service.queryQuantityProducedOfTaskNo("list");
        Assert.assertNotNull(map);

    }
    @Test
    public void getAllChildSn() throws Exception {
        List<String> list =new ArrayList<>();
        service.getAllChildSn(list);
        list.add("1");
        service.getAllChildSn(list);
        List<WipExtendIdentification> tempList = new ArrayList<>();
        tempList.add(new WipExtendIdentification());
        PowerMockito.when(repository.getAllChildSn(any())).thenReturn(tempList);
        service.getAllChildSn(list);
        PowerMockito.when(repository.getAllChildSn(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getAllChildSn(list));
    }
    @Test
    public void sendWipExtToSpm() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class, DatawbRemoteService.class);
        SysLookupTypesDTO lookupValuesDTO = new SysLookupTypesDTO();
        lookupValuesDTO.setLookupMeaning("2022-09-01 00:00:00");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(),any())).thenReturn(lookupValuesDTO);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        PowerMockito.field(WipExtendIdentification.class, "sn").set(wipExtendIdentification, "123123123123");
        PowerMockito.field(WipExtendIdentification.class, "formSn").set(wipExtendIdentification, "321321321321");
        PowerMockito.field(WipExtendIdentification.class, "itemNo").set(wipExtendIdentification, "6546546");
        PowerMockito.field(WipExtendIdentification.class, "createDate").set(wipExtendIdentification, new Date());
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        PowerMockito.field(WipExtendIdentification.class, "sn").set(wipExtendIdentification1, "123123123123");
        PowerMockito.field(WipExtendIdentification.class, "formSn").set(wipExtendIdentification1, "321321321321");
        PowerMockito.field(WipExtendIdentification.class, "itemNo").set(wipExtendIdentification1, "6546546");
        PowerMockito.field(WipExtendIdentification.class, "createDate").set(wipExtendIdentification1, new Date());
        WipExtendIdentification wipExtendIdentification2 = new WipExtendIdentification();
        PowerMockito.field(WipExtendIdentification.class, "sn").set(wipExtendIdentification2, "123123123123");
        PowerMockito.field(WipExtendIdentification.class, "formSn").set(wipExtendIdentification2, "321321321321");
        PowerMockito.field(WipExtendIdentification.class, "itemNo").set(wipExtendIdentification2, "6546546");
        PowerMockito.field(WipExtendIdentification.class, "createDate").set(wipExtendIdentification2, new Date());
        wipExtendIdentificationList.add(wipExtendIdentification);
        wipExtendIdentificationList.add(wipExtendIdentification1);
        wipExtendIdentificationList.add(wipExtendIdentification2);
        PowerMockito.when(repository.getWipExtendIdentificationByCreateDate(any())).thenReturn(wipExtendIdentificationList);
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123123123");
        bsItemInfo.setItemType("0");
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("234234234");
        bsItemInfo1.setItemType("1");
        PowerMockito.field(BsItemInfo.class, "itemNo").set(bsItemInfo, "123123123");
        PowerMockito.field(BsItemInfo.class, "itemType").set(bsItemInfo, "321321321321");
        PowerMockito.field(BsItemInfo.class, "itemNo").set(bsItemInfo1, "234234234");
        PowerMockito.field(BsItemInfo.class, "itemType").set(bsItemInfo1, "1");
        itemInfoList.add(bsItemInfo);
        itemInfoList.add(bsItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.getItemType((any()))).thenReturn(itemInfoList);

        List<PsWipInfoDTO> wipList = new ArrayList<>();
        PsWipInfoDTO dto = new PsWipInfoDTO();
        PowerMockito.field(PsWipInfoDTO.class, "attribute1").set(dto, "234234234");
        wipList.add(dto);
        PowerMockito.when(wipInfoRepository.getListByBatchSnList((any()))).thenReturn(wipList);

        PowerMockito.when(datawbRemoteService.pushMaterialSpm(any())).thenReturn(1);

        PsTask psTask = new PsTask();
        Map<String, String> isTopMap = new HashMap<>();
        isTopMap.put("234234234234", "432432432432");
        Map<String, String> notTopMap = new HashMap<>();
        notTopMap.put("345345345345", "543543543543");
        psTask.setIsTopProdplanMap(isTopMap);
        psTask.setNotTopProdplanMap(notTopMap);
        PowerMockito.when(PlanscheduleRemoteService.getTopProdplan(any())).thenReturn(psTask);
        Assert.assertNotNull(service.sendWipExtToSpm("10313234", "52","180"));

        PowerMockito.when(datawbRemoteService.pushSemiSpm((any()))).thenReturn(1);
        PowerMockito.when(writeBackSpmFailInfoRepository.batchInsert(any())).thenReturn(1);

    }

    @Test
    public void sendSemiFailSpm() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<WriteBackSpmFailInfo> writeBackSpmFailInfoList = new ArrayList<>();
        WriteBackSpmFailInfo info = new WriteBackSpmFailInfo();
        PowerMockito.field(WriteBackSpmFailInfo.class, "sn").set(info, "123123123123");
        PowerMockito.field(WriteBackSpmFailInfo.class, "formSn").set(info, "321321321321");
        PowerMockito.field(WriteBackSpmFailInfo.class, "topProdplanId").set(info, "6546546");
        writeBackSpmFailInfoList.add(info);
        PowerMockito.when(writeBackSpmFailInfoRepository.getWriteBackSpmFailInfoByCreateDate(Mockito.anyObject())).thenReturn(writeBackSpmFailInfoList);
        List<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        WipExtendIdentification wip = new WipExtendIdentification();
        wip.setSn("321321321321");
        wip.setFormSn("654654654654");
        wipExtendIdentifications.add(wip);
        PowerMockito.when(repository.getTopFormSn(Mockito.anySet())).thenReturn(wipExtendIdentifications);
        Assert.assertNotNull(service.sendSemiFailSpm("180", "52"));
    }

    @Test
    public void getTopProdplan() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            PsTask psTask = new PsTask();
                            psTask.setTaskNo("123");
                            setBo(psTask);
                        }})));
        Map<String,String> topSnMap = new HashMap<>();
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        Assert.assertNotNull(PlanscheduleRemoteService.getTopProdplan(topSnMap));
    }


    @Test
    public void getRouteIdByWip() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        CommonUtils.getRouteIdByWip(new PsWipInfo());
        CommonUtils.getRouteIdByWip(new PsWipInfo(){{setWorkOrderNo("1");}});
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(new PsEntityPlanBasic(){{setRouteId("1");}});
        Assert.assertNotNull(CommonUtils.getRouteIdByWip(new PsWipInfo(){{setWorkOrderNo("1");}}));
    }

    @Test
    public void pushWipExtSemiToFactory() throws Exception {
        List<WipExtendIdentification> list = new ArrayList<>();
        WipExtendIdentification info = new WipExtendIdentification();
        PowerMockito.field(WipExtendIdentification.class, "sn").set(info, "test123");
        list.add(info);
        PowerMockito.when(repository.batchInsertWipExt(any())).thenReturn(1);
        Assert.assertNotNull(service.pushWipExtSemiToFactory(list));
    }

    @Test
    public void verifySnIsLockMain() throws Exception {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("111");
        psWipInfo.setCurrProcessCode("222");
        PowerMockito.doNothing().when(flowControlCommonService).snLockControl(Mockito.any());
        Assert.assertNull(Whitebox.invokeMethod(service, "verifySnIsLockMain", psWipInfo));
    }

    @Test
    public void assemblyUnbind() throws Exception{
        List<WipExtendIdentification> list = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification =new WipExtendIdentification();

        PowerMockito.field(WipExtendIdentification.class, "formItemNo").set(wipExtendIdentification, "test123");
        PowerMockito.field(WipExtendIdentification.class, "mainProductCode").set(wipExtendIdentification, "test123");
        list.add(wipExtendIdentification);
        Set<String> assSns = new HashSet<>();
        assSns.add("test123");
        PowerMockito.when(repository.disableByIds(any(),any())).thenReturn(1);
        PowerMockito.when(wipInfoRepository.updateAssembleFlagBySns(any())).thenReturn(1);
        PowerMockito.when(wipInfoRepository.updateSecAssembleFlagBySns(any())).thenReturn(1);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(assemUnbindHistoryInfoRepository).insertBatch(any());
        service.assemblyUnbind("00286523",list);
        PowerMockito.field(WipExtendIdentification.class, "mainProductCode").set(wipExtendIdentification, "");
        service.assemblyUnbind("00286523",list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void parameterVerification() {
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setFormType("test");
        try{
            service.parameterVerification(assemblyRelationshipQueryDTO);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void validConditions() {
        WipExtendIdentificationDTO conditions =new WipExtendIdentificationDTO();
        conditions.setFormType("3");
        conditions.setProdPlanId("test123");
        conditions.setPage(new Long("1"));
        conditions.setRows(new Long("10"));
        service.validConditions(conditions,false);
        try{
            service.validConditions(null,false);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        WipExtendIdentificationDTO cond =new WipExtendIdentificationDTO();
        try{
            service.validConditions(cond,false);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        cond.setFormType("3");
        try{
            service.validConditions(cond,false);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        cond.setEndTime(new Date());
        cond.setShowRes(false);
        try{
            service.validConditions(cond,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void verifySnIsLock() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(null);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.verifySnIsLock(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        try{
            service.verifySnIsLock(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getWipAndBindingSettingList() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, DatawbRemoteService.class);
        ProdBindingSettingDTO prodBindingSetting = new ProdBindingSettingDTO();
        prodBindingSetting.setTaskNo("test");
        List<ProdBindingSettingDTO> bindSettingLists = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindSettingLists);
        Assert.assertThrows(MessageId.PRODUCT_CODE_IS_NOT_EXIST_BIND_LIST, MesBusinessException.class, () -> service.getWipAndBindingSettingList(prodBindingSetting));
        prodBindingSetting.setItemCode("test123");
        prodBindingSetting.setUsageCount(NumConstant.BIG_TWO);
        bindSettingLists.add(prodBindingSetting);
        List<ItemListEntityDTO> erpList = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(any())).thenReturn(erpList);
        List<WipExtendIdentification> bindingList = new ArrayList<>();
        PowerMockito.when(repository.bindingWipListByTaskNo(any())).thenReturn(bindingList);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        service.getWipAndBindingSettingList(prodBindingSetting);
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setItemNo("test123");
        erpList.add(itemListEntityDTO);
        try {
            service.getWipAndBindingSettingList(prodBindingSetting);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        itemListEntityDTO.setRequiredQuantity("10");
        try {
            service.getWipAndBindingSettingList(prodBindingSetting);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("12345");
        wipExtendIdentification.setItemNo("test123");
        wipExtendIdentification.setFormQty(new BigDecimal("1"));
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setReplaceItemNo("test123");
        wipExtendIdentification1.setSn("23456");
        wipExtendIdentification1.setFormQty(new BigDecimal("1"));
        bindingList.add(wipExtendIdentification);
        bindingList.add(wipExtendIdentification1);
        try {
            service.getWipAndBindingSettingList(prodBindingSetting);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setItemCode("test123");
        barcodeExpandDTO.setBarcode("12345");
        barcodeExpandDTO.setSupplierName("名称");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        try {
            service.getWipAndBindingSettingList(prodBindingSetting);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        prodBindingSetting.setUsageCount(null);
        try {
            service.getWipAndBindingSettingList(prodBindingSetting);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void checkBarcodeNeedBind() throws Exception {
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("test123");
        try {
            service.checkBarcodeNeedBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setSn("test123");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        PowerMockito.when(assemblyRelaScanService.getBarCodeBySubSn(any())).thenReturn(barcodeExpandDTOList);
        List<ProdBindingSettingDTO> bindSettingLists = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindSettingLists);
        try {
            service.checkBarcodeNeedBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        ProdBindingSettingDTO prodBindingSetting = new ProdBindingSettingDTO();
        prodBindingSetting.setTaskNo("test");
        prodBindingSetting.setItemCode("test123");
        bindSettingLists.add(prodBindingSetting);
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setItemCode("test123");
        barcodeExpandDTO.setBarcode("12345");
        barcodeExpandDTO.setSupplierName("名称");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        List<WipExtendIdentification> bindingList = new ArrayList<>();
        PowerMockito.when(repository.bindingWipListByTaskNo(any())).thenReturn(bindingList);
        try {
            service.checkBarcodeNeedBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        bindingList.add(wipExtendIdentification);
        try {
            service.checkBarcodeNeedBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void checkBarcodeNeedBind_category() throws Exception {
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setItemCode("123456789");
        barcodeExpandDTO.setParentCategoryCode("CONTAINER_CODE");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(assemblyRelaScanService.getBarCodeBySubSn(any())).thenReturn(barcodeExpandDTOList);
        List<ProdBindingSettingDTO> bindSettingLists = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindSettingLists);
        ArrayList<ProdBindingSettingDTO> bindingSettingDTOS = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("123456789");
        bindingSettingDTOS.add(prodBindingSettingDTO);
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindingSettingDTOS);
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("123456789");
        wipExtendIdentification.setCategoryCode("CONTAINER_CODE");
        ArrayList<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        wipExtendIdentifications.add(wipExtendIdentification);
        PowerMockito.when(repository.bindingWipListByTaskNo(any())).thenReturn(wipExtendIdentifications);
        service.checkBarcodeNeedBind(wipExtendIdentification);
        wipExtendIdentification.setCategoryCode("SN_CODE");
        Assert.assertThrows(MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, MesBusinessException.class, () -> service.checkBarcodeNeedBind(wipExtendIdentification));
    }

    @Test
    public void auxMaterialUnBind() {
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        try {
            service.auxMaterialUnBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setSn("test123");
        try {
            service.auxMaterialUnBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setTaskNo("test123");
        wipExtendIdentification.setSn("");
        try {
            service.auxMaterialUnBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setSn("test123");
        PowerMockito.when(repository.unBindAuxBindRelation(any())).thenReturn(1);
        PowerMockito.when(assemblyOptRecordRepository.insertOptRecordFromAuxBind(any())).thenReturn(1);
        try {
            service.auxMaterialUnBind(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void auxMaterialBinding() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class,BasicsettingRemoteService.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFactoryId(new BigDecimal("58"));
        wipExtendIdentification.setTaskNo("test123");
        try {
            service.auxMaterialBinding(wipExtendIdentification);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        wipExtendIdentification.setSn("12345");
        wipExtendIdentification.setFormType(NumConstant.STR_TWO);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        List<ProdBindingSettingDTO> bindSettingLists = new ArrayList<>();
        List<WipExtendIdentification> bindingList = new ArrayList<>();
        List<SysLookupValuesDTO> lookupValues = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSetting = new ProdBindingSettingDTO();
        prodBindingSetting.setTaskNo("test");
        prodBindingSetting.setItemCode("test123");
        bindSettingLists.add(prodBindingSetting);
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setItemCode("test123");
        barcodeExpandDTO.setBarcode("12345");
        barcodeExpandDTO.setSupplierName("名称");
        barcodeExpandDTO.setParentCategoryName("批次码");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(assemblyRelaScanService.getBarCodeBySubSn(any())).thenReturn(barcodeExpandDTOList);
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindSettingLists);
        List<BarcodeExpandDTO> barcodeExpandDTOLists = new ArrayList<>();
        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setParentCategoryName("123");
        dto.setSupplierName("123");
        dto.setSpecModel("132");
        dto.setBrandName("132");
        barcodeExpandDTOLists.add(dto);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOLists);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(any())).thenReturn(lookupValues);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(repository).insertWipExtendIdentification(any());
        PowerMockito.when(assemblyOptRecordService.batchInsert(any())).thenReturn(1);
        ArrayList<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        wipExtendIdentifications.add(wipExtendIdentification);
        service.auxMaterialBinding(wipExtendIdentification);
        wipExtendIdentification.setFormType(NumConstant.STR_FOUR);
        service.auxMaterialBinding(wipExtendIdentification);
        wipExtendIdentification.setFormType(NumConstant.STR_ONE);
        service.auxMaterialBinding(wipExtendIdentification);
    }

    @Test
    public void checkLead() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> lookupValues = getSysLookupValuesDTOS();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormHbCode("HS");
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setIsLead("HS");
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(any())).thenReturn(lookupValues);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(new ArrayList<>());
        try {
            service.checkLead(wipExtendIdentification,barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(lookupValues);
        try {
            service.checkLead(wipExtendIdentification,barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        barcodeExpandDTO.setIsLead("HSF");
        try {
            service.checkLead(wipExtendIdentification,barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setFormHbCode("HSF-S");
        try {
            service.checkLead(wipExtendIdentification, barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        barcodeExpandDTO.setIsLead("HSF-S");
        try {
            service.checkLead(wipExtendIdentification, barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        wipExtendIdentification.setFormHbCode("4");
        service.checkLead(wipExtendIdentification, barcodeExpandDTO);
        wipExtendIdentification.setFormHbCode("5");
        try {
            service.checkLead(wipExtendIdentification, barcodeExpandDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    private static List<SysLookupValuesDTO> getSysLookupValuesDTOS() {
        List<SysLookupValuesDTO> lookupValues = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("HSF-S");
        sysLookupValuesDTO.setLookupCode(new BigDecimal("6523"));
        sysLookupValuesDTO.setAttribute1("10");
        sysLookupValuesDTO.setLookupMeaning("4");
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        sysLookupValuesDTO1.setDescriptionChin("HSF");
        sysLookupValuesDTO1.setLookupCode(new BigDecimal("6523"));
        sysLookupValuesDTO1.setAttribute1("1");
        SysLookupValuesDTO sysLookupValuesDTO2 = new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupCode(new BigDecimal("652303001"));
        sysLookupValuesDTO2.setLookupMeaning("Y");
        lookupValues.add(sysLookupValuesDTO2);
        lookupValues.add(sysLookupValuesDTO1);
        lookupValues.add(sysLookupValuesDTO);
        return lookupValues;
    }

    @Test
    public void getBarcodeExpandDTO() throws Exception {
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("test123");
        wipExtendIdentification.setFormHbCode("test123");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("test123");
        barcodeExpandDTO.setItemCode("test123");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        List<ProdBindingSettingDTO> bindSettingLists = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getBindingSettingList(any())).thenReturn(bindSettingLists);
        List<MtlRelatedItemsEntityDTO> resultDTOs = new ArrayList<>();
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO.setReplaceItemCode("test1");
        resultDTOs.add(mtlRelatedItemsEntityDTO);
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO1 = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO1.setReplaceItemCode("test");
        resultDTOs.add(mtlRelatedItemsEntityDTO1);
        try {
            service.getBarcodeExpandDTO(wipExtendIdentification,barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("test");
        bindSettingLists.add(prodBindingSettingDTO);
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(Mockito.anyList(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        try {
            service.getBarcodeExpandDTO(wipExtendIdentification,barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(Mockito.anyList(),Mockito.anyBoolean())).thenReturn(resultDTOs);
        try {
            service.getBarcodeExpandDTO(wipExtendIdentification,barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void unBindAssemblyRelationship() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        service.unBindAssemblyRelationship(new ArrayList<>(), "00286523");
        List<AssemblyRelationshipQueryDTO> assemblyRelationshipList = new ArrayList<>();
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setIdentiId("test123");
        assemblyRelationshipList.add(assemblyRelationshipQueryDTO);
        PowerMockito.when(assemblyOptRecordRepository.insertOptRecordFromWipExtend(Mockito.any())).thenReturn(1);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(wipInfoRepository).updateAssemblyIdentification(any());
        PowerMockito.when(repository.unBindAssemblyRelationship(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1, service.unBindAssemblyRelationship(assemblyRelationshipList, "00286523"));
        SysLookupValues valueByCode = new SysLookupValues();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Constant.LOOKU_VALUE_7503001)).thenReturn(valueByCode);
        Assert.assertEquals(1, service.unBindAssemblyRelationship(assemblyRelationshipList, "00286523"));
        valueByCode.setLookupMeaning("Y");
        Assert.assertEquals(1, service.unBindAssemblyRelationship(assemblyRelationshipList, "00286523"));
        assemblyRelationshipQueryDTO.setFormSn("123");
        List<String> receivedSnList = new ArrayList<>();
        PowerMockito.when(warehouseEntryDetailService.getReceivedSnList(Mockito.anyList())).thenReturn(receivedSnList);
        Assert.assertEquals(1, service.unBindAssemblyRelationship(assemblyRelationshipList, "00286523"));
        receivedSnList.add("123");
        try {
            service.unBindAssemblyRelationship(assemblyRelationshipList, "00286523");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_RECEIVED_CAN_NOT_UN_BIND, e.getMessage());
        }

    }

    @Test
    public void setHasSubSn() {
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setNeedHasSubSn(false);
        service.setHasSubSn(assemblyRelationshipQueryDTO, new ArrayList<>());
        assemblyRelationshipQueryDTO.setQueryType("0");
        assemblyRelationshipQueryDTO.setFormType("1");
        List<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormSn("test123");
        wipExtendIdentifications.add(wipExtendIdentification);
        PowerMockito.when(repository.queryBindingQtyBySnList(Mockito.anyList(),Mockito.anyString())).thenReturn(new ArrayList<>());
        assemblyRelationshipQueryDTO.setNeedHasSubSn(true);
        service.setHasSubSn(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        assemblyRelationshipQueryDTO.setQueryType("1");
        wipExtendIdentification.setSn("test123");
        service.setHasSubSn(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        PowerMockito.when(repository.queryBindingQtyBySnList(Mockito.anyList(),Mockito.anyString())).thenReturn(wipExtendIdentifications);
        service.setHasSubSn(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        assemblyRelationshipQueryDTO.setQueryType("0");
        service.setHasSubSn(assemblyRelationshipQueryDTO, wipExtendIdentifications);
        Assert.assertNull(assemblyRelationshipQueryDTO.getEndTime());
    }
}
