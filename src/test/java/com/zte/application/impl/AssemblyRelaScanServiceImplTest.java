package com.zte.application.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.*;
import com.zte.application.warehouse.MixWarehouseSubmitService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.warehouse.MixWarehouseSubmitDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

/**
 * ClassName: AssemblyRelaScanServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/6/8 下午2:52
 */
@PrepareForTest({RedisHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class, RedisCacheUtils.class, DatawbRemoteService.class,
        CrafttechRemoteService.class, RedisCacheUtils.class, MixWarehouseSubmitService.class})
public class AssemblyRelaScanServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    AssemblyRelaScanServiceImpl service;

    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    StandardModeCommonScanServiceImpl standardModeCommonScanService;

    @Mock
    BarcodeLockDetailRepository barcodeLockDetailRepository;

    @Mock
    ProdBindingSettingService prodBindingSettingService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    DatawbRemoteService datawbRemoteService;

    @Mock
    AssemblyRelaScanRecordInfoService assemblyRelaScanRecordInfoService;
    @Mock
    WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    MixWarehouseSubmitService mixWarehouseSubmitService;
    @Mock
    private CollectionCodeScanService collectionCodeScanService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;

    @Before
    public void init(){

        PowerMockito.mockStatic(HttpRemoteUtil.class);
    }

    @Test
    public void insertOptRecord() throws Exception {
        // 基础校验
        ComMachineAssemblyScanDTO comMacDTO = new ComMachineAssemblyScanDTO();
        List<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        service.insertOptRecord(new AssemblyRelaScanDTO(),wipExtendIdentifications);
        wipExtendIdentifications.add(new WipExtendIdentification());
        service.insertOptRecord(new AssemblyRelaScanDTO(),wipExtendIdentifications);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
        public void handleAfterInputMainBarcodeTest() throws Exception {
            // 基础校验
            ComMachineAssemblyScanDTO comMacDTO = new ComMachineAssemblyScanDTO();
            try {
                service.handleAfterInputMainBarcode(null);
            } catch (MesBusinessException e) {
                Assert.assertTrue(MessageId.PARAM_IS_NULL.equals(e.getExMsgId()));
            }
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.LINE_CODE_OF_COM_ASS_SCAN_NULL.equals(e.getExMsgId()));
        }
        comMacDTO.setLineCode("lineCode");
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PROCESS_CODE_OF_COM_ASS_SCAN_NULL.equals(e.getExMsgId()));
        }
        comMacDTO.setProcessCode("processCode");
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_OF_COM_ASS_SCAN_NULL.equals(e.getExMsgId()));
        }
        comMacDTO.setMainBarcode("mainSn");
        // 校验wip_info在制信息和主工序
        PowerMockito.when(psWipInfoService.getWipInfoBySn("mainSn")).thenReturn(null);
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO.equals(e.getExMsgId()));
        }
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setCraftSection(Constant.WAREHOUSE_ENTRY);
        wipInfoBySn.setAttribute2("taskNO");
        PowerMockito.when(psWipInfoService.getWipInfoBySn("mainSn")).thenReturn(wipInfoBySn);
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_CRAFT_SECTION_ILLEGAL.equals(e.getExMsgId()));
        }
        PowerMockito.when(psWipInfoService.getWipInfoBySn("mainSn")).thenReturn(wipInfoBySn);
        wipInfoBySn.setCraftSection("装配");
        wipInfoBySn.setItemNo("mainItem");
        // 校验是否按照装配规则扫描，主条码是否已经完成绑定,并返回查询的绑定清单信息
        // 数据字典为N
        SysLookupTypesDTO sysDTO = new SysLookupTypesDTO();
        sysDTO.setLookupMeaning(Constant.FLAG_N);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue
                (Constant.LOOKUP_TYPE_1003018, Constant.LOOK_UP_CODE_1003018001)).thenReturn(sysDTO);
        List<ProdBindingSettingDTO> bindingInfoList = new ArrayList<>();
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("mainItem");
        prodBindingSettingDTO.setItemCode("subItem1");
        prodBindingSettingDTO.setItemName("subItemName1");
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        prodBindingSettingDTO.setUsageCount(new BigDecimal("3"));
        prodBindingSettingDTO.setWorkStation("workStation");
        bindingInfoList.add(prodBindingSettingDTO);
        comMacDTO.setIfPassStationSwitch(false);
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ASSEMBLY_NOT_COMPLY_WITH_RULES.equals(e.getExMsgId()));
        }
        prodBindingSettingDTO.setWorkStation("");
        comMacDTO.setWorkStation("workStation");
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ASSEMBLY_NOT_COMPLY_WITH_RULES.equals(e.getExMsgId()));
        }
        prodBindingSettingDTO.setWorkStation("workStation1");
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }
        prodBindingSettingDTO.setWorkStation("workStation");
        // 校验跳转总数量
        comMacDTO.setSkipTotalQty(2);
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SKIP_TOTAL_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        // 数据字典为Y
        sysDTO.setLookupMeaning(Constant.FLAG_Y);
        // 设置绑定完成
        prodBindingSettingDTO.setBindedCount(new BigDecimal("3"));
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_HAVE_FINISHED_BIND.equals(e.getExMsgId()));
        }
        // 设置绑定未完成，使得逻辑继续
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        // 校验跳转总数量为正确，使得逻辑继续
        comMacDTO.setSkipTotalQty(3);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        wipInfoBySn.setAttribute1("prod");
        String workOrderNo = "workOrderNo";
        PowerMockito.when(PlanscheduleRemoteService.selectWorkNoOfComMachine(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new ArrayList<>());
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_NOT_HAVE_WORK_ORDER_NO, e.getExMsgId());
        }

        PowerMockito.when(PlanscheduleRemoteService.selectWorkNoOfComMachine(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new ArrayList<String>(){{add(workOrderNo);add(workOrderNo);}});
        try {
            service.handleAfterInputMainBarcode(comMacDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_HAVE_MORE_WORK_ORDER_NO.equals(e.getExMsgId()));
        }

        PowerMockito.when(PlanscheduleRemoteService.selectWorkNoOfComMachine(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(new ArrayList<String>(){{add(workOrderNo);}});
        PowerMockito.mockStatic(RedisCacheUtils.class);
        service.handleAfterInputMainBarcode(comMacDTO);
    }

    @Test
    public void handlerAfterInputSubBarcodeTest() throws Exception {
        ComMachineAssemblyScanDTO comMac = new ComMachineAssemblyScanDTO();
        comMac.setLineCode("lineCode");
        comMac.setProcessCode("process");
        comMac.setWorkStation("station");
        comMac.setSkipTotalQty(3);
        comMac.setIfPassStationSwitch(true);
        comMac.setMainBarcode("mainSn");
        comMac.setSubBarcode("subSn");
        comMac.setItemCode("itemNo");
        comMac.setItemName("itemName");
        comMac.setStandardModelTask("taskNo");
        comMac.setWorkOrderNo("workOrderNo");
        comMac.setBatchCodeQty(BigDecimal.ZERO);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(false);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_IS_BINDING_NOW.equals(e.getExMsgId()));
        }
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        // 条码中心无条码
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(null);
        // 材料代码转换为空
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(DatawbRemoteService.getItemCode(Mockito.anyList())).thenReturn(null);
        comMac.setAutoSubmitWarehouse(true);
        Assert.assertThrows(MessageId.SUBMIT_WAREHOUSE_IS_NULL, MesBusinessException.class, () -> service.handlerAfterInputSubBarcode(comMac));
        comMac.setMixWarehouseSubmit(new MixWarehouseSubmitDTO());
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO.equals(e.getExMsgId()));
        }
        PsWipInfo psWipInfo = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.anyString())).thenReturn(psWipInfo);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GET_SN_INFO.equals(e.getExMsgId()));
        }
        comMac.setAutoSubmitWarehouse(false);
        List<BaItem> baItems = new ArrayList<>();
        BaItem baitem = new BaItem();
        baitem.setErrMsg("error");
        baItems.add(baitem);
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(serviceData.getCode());
        serviceData.setBo(baItems);
        JsonNode treeNode = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData));
        PowerMockito.when(DatawbRemoteService.getItemCode(Mockito.anyList())).thenReturn(treeNode);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GET_SN_INFO.equals(e.getExMsgId()));
        }
        baitem.setSn("subSn");
        treeNode = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData));
        PowerMockito.when(DatawbRemoteService.getItemCode(Mockito.anyList())).thenReturn(treeNode);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ITEM_INFO_NOT_FOUND.equals(e.getExMsgId()));
        }
        baitem.setErrMsg("");
        treeNode = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData));
        PowerMockito.when(DatawbRemoteService.getItemCode(Mockito.anyList())).thenReturn(treeNode);
        // 条码中心有条码
        List<BarcodeExpandDTO> barcodeList = new ArrayList<>();
        BarcodeExpandDTO barcode = new BarcodeExpandDTO();
        barcode.setBarcode("subSn");
        barcode.setItemCode("123456789012ABC");
        barcode.setItemName("subItemName");
        barcode.setParentCategoryName("批次码");
        barcode.setParentCategoryCode("BATCH_CODE");
        barcodeList.add(barcode);
        BarcodeExpandQueryDTO expandQueryDTO = new BarcodeExpandQueryDTO();
        expandQueryDTO.setBarcodeList(new ArrayList() {{add("subSn");}});
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.eq(expandQueryDTO))).thenReturn(barcodeList);
        // PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(barcodeList);
        ArrayList<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setCategoryCode("SN_CODE");
        wipExtendIdentifications.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.anyMap())).thenReturn(wipExtendIdentifications);
        // 环保属性
        List<SysLookupValuesDTO> listSys = new ArrayList<>();
        SysLookupValuesDTO sys1 = new SysLookupValuesDTO();
        sys1.setDescriptionChin("有铅");
        sys1.setAttribute1("10");
        SysLookupValuesDTO sys2 = new SysLookupValuesDTO();
        sys2.setDescriptionChin("无铅");
        sys2.setAttribute1("20");
        listSys.add(sys1);
        listSys.add(sys2);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys);
        barcode.setRelatedSnBarcode("");
        // 验证环保属性
        // PsWipInfo psWipInfo = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.anyString())).thenReturn(psWipInfo);
        psWipInfo.setAttribute3("无铅1");
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ISLEAD_OF_MAIN_SN_NOT_FIND_DICTIONARY.equals(e.getExMsgId()));
        }
        psWipInfo.setAttribute3("无铅");
        barcode.setIsLead("有铅1");
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ISLEAD_OF_SUB_SN_NOT_FIND_DICTIONARY.equals(e.getExMsgId()));
        }
        barcode.setIsLead("有铅");
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.LEAD_FLAG_OF_SUB_SN_ILLEGAL.equals(e.getExMsgId()));
        }
        barcode.setIsLead("无铅");
        // 非单板条码
        PowerMockito.when(psWipInfoService.getWipInfoBySnList(Mockito.anyList())).thenReturn(new ArrayList<>());
        service.handlerAfterInputSubBarcode(comMac);
        List<CollectionCodeScanDTO> codeList = new ArrayList<>();
        CollectionCodeScanDTO scanDTO = new CollectionCodeScanDTO();
        scanDTO.setMasterSn("subSn");
        scanDTO.setSubSn("subSn111");
        scanDTO.setQuantity(1);
        codeList.add(scanDTO);
        PowerMockito.when(collectionCodeScanService.getList(Mockito.any())).thenReturn(codeList);
        List<BarcodeExpandDTO> barcodeList1 = new ArrayList<>();
        BarcodeExpandDTO barcode1 = new BarcodeExpandDTO();
        barcode1.setBarcode("subSn111");
        barcode1.setItemCode("123456789012ABC");
        barcode1.setItemName("subItemName11");
        barcode1.setParentCategoryName("序列码");
        barcode1.setParentCategoryCode("SEQUENCE_CODE");
        barcode1.setIsLead("无铅");
        barcodeList1.add(barcode1);
        List<ProdBindingSettingDTO> bindingInfoList = new ArrayList<>();
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setBarcodeList(new ArrayList() {{add("subSn111");}});
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.eq(barcodeExpandQueryDTO))).thenReturn(barcodeList1);
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }
        // 本身不在ERP清单，替代物料在
        barcode.setItemCode("123456789015ABC");
        // MOCK获取替代物料
        ServiceData serviceData1=new ServiceData();
        List<MtlRelatedItemsEntityDTO> entityDTOS = new ArrayList<>();
        MtlRelatedItemsEntityDTO dto1 = new MtlRelatedItemsEntityDTO();
        dto1.setInventoryItemCode("123456789015ABC");
        dto1.setRelatedItemCode("123456789013ABD");
        entityDTOS.add(dto1);
        serviceData1.setBo(entityDTOS);
        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", 1);
        serviceData1.setOther(map);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }
        // 本身不在替代物料也不在
        dto1.setRelatedItemCode("123456789014ABD");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }
        // 存在多个。替代物料都在任务清单，报错。
        dto1.setInventoryItemCode("123456789013ABD");
        dto1.setRelatedItemCode("123456789015ABC");
        MtlRelatedItemsEntityDTO dto2 = new MtlRelatedItemsEntityDTO();
        dto2.setInventoryItemCode("123456789012ABD");
        dto2.setRelatedItemCode("123456789015ABC");
        entityDTOS.add(dto2);
        serviceData1.setBo(entityDTOS);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        Assert.assertThrows(MessageId.ITEM_OR_REPLACE_ITEM_IN_ERP_TASK_MORE, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        // 设置非单板条码，验证非12位。
        comMac.setSubBarcodeStepFlag(false);
        PowerMockito.when(psWipInfoService.getWipInfoBySnList(Mockito.anyList())).thenReturn(new ArrayList<>());
        // 虽然前12位相同，但是，非单板，需要完整匹配
        barcode.setItemCode("123456789015ABC");
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.MAIN_BARCODE_NOT_HAVE_BINDING_SETTING.equals(e.getExMsgId()));
        }

        // 校验绑定清单
        barcode.setItemCode("123456789013ABC");
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("mainItem");
        prodBindingSettingDTO.setItemCode("123456789013ABC");
        prodBindingSettingDTO.setItemName("subItemName1");
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        prodBindingSettingDTO.setUsageCount(new BigDecimal("3"));
        prodBindingSettingDTO.setWorkStation("workStation");
        bindingInfoList.add(prodBindingSettingDTO);
        comMac.setIfPassStationSwitch(false);
        comMac.setWorkStation("workStation");
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        // 设置为批次码
        barcode.setParentCategoryName("批次码");
        comMac.setBatchCodeQty(new BigDecimal("3"));
        comMac.setNeedUsage(true);
        Assert.assertThrows(MessageId.INSUFFICIENT_QUANTITY_TO_BE_BOUND, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        comMac.setNeedUsage(false);
        // 不在ERP,也不在绑定清单，数据字典为N
        // 绑定清单必须在数据字典关闭
        barcode.setItemCode("123456789017ABC");
        prodBindingSettingDTO.setItemCode("123456789018ABC");
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        // 数字字典开
        SysLookupTypesDTO dto = new SysLookupTypesDTO();
        dto.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        WipExtendIdentification entity = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationRepository.selectEntityBySn(Mockito.anyString())).thenReturn(entity);
        dto.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_ERP_AND_BIND_LIST.equals(e.getExMsgId()));
        }

        // 在ERP清单，但是不在绑定清单，数据字典为Y
        // 绑定清单必须在数据字典关闭
        barcode.setItemCode("123456789013ABC");
        dto.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        prodBindingSettingDTO.setItemCode("123456789014ABC");
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        Assert.assertThrows(MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_BIND_LIST, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        // 非单板， 且本身不是
        prodBindingSettingDTO.setItemCode("123456789013ABC");
        barcode.setItemCode("123456789015ABC");
        Assert.assertThrows(MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_BIND_LIST, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        // 非但板，替代物料是
        dto1.setInventoryItemCode("123456789013ABC");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        Assert.assertThrows(MessageId.INSUFFICIENT_QUANTITY_TO_BE_BOUND, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        ProdBindingSettingDTO prodBindingSettingDTO1 = new ProdBindingSettingDTO();
        prodBindingSettingDTO1.setProductCode("mainItem");
        prodBindingSettingDTO1.setItemCode("123456789012ABD");
        prodBindingSettingDTO1.setItemName("subItemName1");
        prodBindingSettingDTO1.setBindedCount(new BigDecimal("1"));
        prodBindingSettingDTO1.setUsageCount(new BigDecimal("3"));
        prodBindingSettingDTO1.setWorkStation("workStation");
        bindingInfoList.add(prodBindingSettingDTO1);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ITEM_OR_REPLACE_ITEM_IN_BIND_LIST_MORE.equals(e.getExMsgId()));
        }
        bindingInfoList.remove(prodBindingSettingDTO1);
        PowerMockito.when(prodBindingSettingService.getBindingInfoByItemNew(Mockito.any())).thenReturn(bindingInfoList);
        // 单板条码
        List<PsWipInfo> subPsWipList = new ArrayList<>();
        PsWipInfo subPsWip = new PsWipInfo();
        subPsWip.setCraftSection("SMT");
        subPsWip.setSn("subSn111");
        subPsWipList.add(subPsWip);
        PowerMockito.when(psWipInfoService.getWipInfoBySnList(Mockito.anyList())).thenReturn(subPsWipList);
        // 设置为12为单板
        PowerMockito.when(psWipInfoService.getWipInfoBySnList(Mockito.anyList())).thenReturn(subPsWipList);
        dto1.setInventoryItemCode("123456789013ABF");
        // 只匹配一个
        dto2.setInventoryItemCode("123456789016ABD");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        Assert.assertThrows(MessageId.INSUFFICIENT_QUANTITY_TO_BE_BOUND,MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        // 设置本次绑定数量，使其不报错，逻辑继续
        comMac.setBatchCodeQty(new BigDecimal("2"));

        List<WipExtendIdentification> allChildProdBinding = new ArrayList<>();
        wipExtendIdentification.setSn("subSnSub");
        PowerMockito.when(wipExtendIdentificationRepository.getAllChildProdBinding(Mockito.anyString())).thenReturn(allChildProdBinding);
        // 技改关闭
        List<SysLookupTypesDTO> sysList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("OFF");
        sysList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(sysList);
        // 锁定单
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setAttribute1("prod1");
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setAttribute1("prod2");
        psWipInfos.add(psWipInfo2);
        psWipInfos.add(psWipInfo1);
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.anyList())).thenReturn(psWipInfos);
        List<BarcodeLockDetail> lockDetailList = new ArrayList<>();
        BarcodeLockDetail barcodeLockDetail = new BarcodeLockDetail();
        barcodeLockDetail.setBatchSn("prod1");
        barcodeLockDetail.setType(Constant.LOCK_TYPE_BATCH);
        lockDetailList.add(barcodeLockDetail);
        BarcodeLockDetail barcodeLockDetail1 = new BarcodeLockDetail();
        barcodeLockDetail1.setBatchSn("subSnSub");
        barcodeLockDetail1.setType(Constant.LOCK_TYPE_SN);
        lockDetailList.add(barcodeLockDetail1);
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.anyList(), Mockito.any())).thenReturn(lockDetailList);
        Assert.assertThrows(MessageId.SN_LOCK_NOT_ALLOW_BIND, MesBusinessException.class, () -> service.handlerAfterInputSubBarcode(comMac));
        // 技改开启
        sysLookupTypesDTO.setLookupMeaning("ON");
        PowerMockito.mockStatic(CrafttechRemoteService.class);

        List<BSProcess> bsProcesses = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setCraftSection("装配");
        ServiceData serviceData2 = new ServiceData();
        serviceData2.setCode(serviceData.getCode());
        serviceData2.setBo(bsProcesses);
        JsonNode treeNode2 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData2));
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(treeNode2);
        // 每次调用前，需要将数量重置为1.因为成功计算后，会相加
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        Assert.assertThrows(MessageId.PROCESS_DETAILS_NOT_FOUND, MesBusinessException.class, () -> service.handlerAfterInputSubBarcode(comMac));
        bsProcesses.add(bsProcess);
        serviceData2.setBo(bsProcesses);
        treeNode2 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData2));
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(treeNode2);
        String techCheckResult = "test";
        PowerMockito.when(standardModeCommonScanService.getTechnicalChangeBarcode(Mockito.anyList(), Mockito.anyString())).thenReturn(techCheckResult);
        // 每次调用前，需要将数量重置为1.因为成功计算后，会相加
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        Assert.assertThrows(MessageId.SN_TECHNICAL_CHANGE, MesBusinessException.class, () -> service.handlerAfterInputSubBarcode(comMac));
        // 技改通过，让逻辑继续
        PowerMockito.when(standardModeCommonScanService.getTechnicalChangeBarcode(Mockito.anyList(), Mockito.anyString())).thenReturn("");
        // 锁定通过，让逻辑继续
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.anyList(), Mockito.any())).thenReturn(new ArrayList<>());
        // 每次调用前，需要将数量重置为1.因为成功计算后，会相加
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        comMac.setNeedUsage(false);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SUB_BARCODE_CRAFT_SECTION_ILLEGAL.equals(e.getExMsgId()));
        }
        subPsWip.setCraftSection("入库");

        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ITEM_OR_REPLACE_ITEM_NOT_IN_BIND_LIST.equals(e.getExMsgId()));
        }
        MtlRelatedItemsEntityDTO dto3 = new MtlRelatedItemsEntityDTO();
        dto3.setInventoryItemCode("123456789013ABC");
        dto3.setRelatedItemCode("123456789012ABC");
        entityDTOS.add(dto3);
        serviceData1.setBo(entityDTOS);
        MtlRelatedItemsEntityDTO dto4 = new MtlRelatedItemsEntityDTO();
        dto4.setInventoryItemCode("123456789013ABC");
        dto4.setRelatedItemCode("123456789017ABC");
        dto1.setInventoryItemCode("123456789013ABC");
        entityDTOS.add(dto4);
        serviceData1.setBo(entityDTOS);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData1));
        // 绑定清单必须在数据字典关闭
        dto.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        Assert.assertThrows(NullPointerException.class, () -> service.handlerAfterInputSubBarcode(comMac));
        List<ItemListEntityDTO> itemListEntityDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getErpItemListByTaskNo(Mockito.anyString())).thenReturn(itemListEntityDTOList);
        Assert.assertThrows(MessageId.FAILED_TO_OBTAIN_MATERIAL_SUBSTITUTION_RELATIONSHIP, MesBusinessException.class,()->service.handlerAfterInputSubBarcode(comMac));
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setItemNo("123456789013ABC");
        itemListEntityDTOList.add(itemListEntityDTO);
        ItemListEntityDTO itemListEntityDTO2 = new ItemListEntityDTO();
        itemListEntityDTO2.setItemNo("123456789012");
        itemListEntityDTOList.add(itemListEntityDTO2);
        try {
            service.handlerAfterInputSubBarcode(comMac);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ITEM_CODE_HAS_BEEN_BOUND.equals(e.getExMsgId()));
        }
    }

    @Test
    public void subSnCheckFixBom () throws Exception{
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        PsWipInfo wipInfoByMainSn = new PsWipInfo();
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        List<FixBomDetailDTO> fixBomByTaskNo = new ArrayList<>();
        FixBomDetailDTO detailDTO = new FixBomDetailDTO();
        detailDTO.setItemNumber("1");
        fixBomByTaskNo.add(detailDTO);
        FixBomDetailDTO detailDTO1 = new FixBomDetailDTO();
        fixBomByTaskNo.add(detailDTO1);
        detailDTO1.setZteCode("123");
        detailDTO1.setUploadBySn("Y");
        detailDTO1.setRequireMaterialUpload("Y");
        detailDTO1.setFixBomRequired("Y");
        FixBomDetailDTO detailDTO2 = new FixBomDetailDTO();
        detailDTO2.setZteCode("321");
        detailDTO2.setRequireMaterialUpload("Y");
        detailDTO2.setItemType("成品料");
        fixBomByTaskNo.add(detailDTO2);
        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setBarcode("123123");
        dto.setItemCode("123");
        barcodeExpandDTOList.add(dto);
        PowerMockito.when(centerfactoryRemoteService.getFixBomByTaskNo(Mockito.any())).thenReturn(fixBomByTaskNo);
        try {
            Whitebox.invokeMethod(service, "subSnCheckFixBom", comMacAssScanDTO, wipInfoByMainSn, barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_ERR_CHECK, e.getMessage());
        }
        detailDTO1.setItemNumber("1");
        detailDTO2.setItemNumber("2");
        try {
            Whitebox.invokeMethod(service, "subSnCheckFixBom", comMacAssScanDTO, wipInfoByMainSn, barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BY_CONTAINER_ITEM_BOX_NO_EMPTY, e.getMessage());
        }
        dto.setRelatedContainerBarcode("123");
        try {
            Whitebox.invokeMethod(service, "subSnCheckFixBom", comMacAssScanDTO, wipInfoByMainSn, barcodeExpandDTOList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BY_SN_NOT_SEQUENCE_CODE, e.getMessage());
        }
        dto.setParentCategoryName("序列码");
        Whitebox.invokeMethod(service, "subSnCheckFixBom", comMacAssScanDTO, wipInfoByMainSn, barcodeExpandDTOList);
        dto.setItemCode("132");
        Whitebox.invokeMethod(service, "subSnCheckFixBom", comMacAssScanDTO, wipInfoByMainSn, barcodeExpandDTOList);
    }

    @Test
    public void resolveResultDTO () throws Exception {
        ComMachineAssemblyScanDTO seqResultDTO = null;
        ComMachineAssemblyScanDTO batchResultDTO = null;
        try {
            Whitebox.invokeMethod(service, "resolveResultDTO", seqResultDTO, batchResultDTO);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_MSGID, e.getMessage());
        }
        seqResultDTO = new ComMachineAssemblyScanDTO();
        seqResultDTO.setDetailDTOList(new ArrayList<>());
        Whitebox.invokeMethod(service, "resolveResultDTO", seqResultDTO, batchResultDTO);
        batchResultDTO = new ComMachineAssemblyScanDTO();
        batchResultDTO.setDetailDTOList(new ArrayList<>());
        Whitebox.invokeMethod(service, "resolveResultDTO", seqResultDTO, batchResultDTO);
        seqResultDTO = null;
        Whitebox.invokeMethod(service, "resolveResultDTO", seqResultDTO, batchResultDTO);
    }

    @Test
    public void handlerSeqReturnEntityAndSkip() throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<ProdBindingSettingDTO> bindingInfoList = new ArrayList<>();
        List<BaItem> seqCodeList = new ArrayList<>();
        List<CollectionCodeScanDTO> codeList = new ArrayList<>();
        CollectionCodeScanDTO codeScanDTO = new CollectionCodeScanDTO();
        codeScanDTO.setSubSn("123");
        codeScanDTO.setQuantity(1);
        codeList.add(codeScanDTO);
        Assert.assertNull(Whitebox.invokeMethod(service, "handlerSeqReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, seqCodeList, false));
        Assert.assertNull(Whitebox.invokeMethod(service, "handlerBatchReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, codeList, seqCodeList, false));
        BaItem item = new BaItem();
        item.setSn("123");
        seqCodeList.add(item);
        Whitebox.invokeMethod(service, "handlerSeqReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, seqCodeList, false);
        Whitebox.invokeMethod(service, "handlerBatchReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, codeList, seqCodeList, false);
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        bindingInfoList.add(dto);
        Whitebox.invokeMethod(service, "handlerSeqReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, seqCodeList, false);
        Whitebox.invokeMethod(service, "handlerBatchReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, codeList, seqCodeList, false);
        comMacAssScanDTO.setSkipTotalQty(0);
        Whitebox.invokeMethod(service, "handlerSeqReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, seqCodeList, true);
        Whitebox.invokeMethod(service, "handlerBatchReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, codeList, seqCodeList, true);
        comMacAssScanDTO.setSkipTotalQty(10);
        Whitebox.invokeMethod(service, "handlerSeqReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, seqCodeList, true);
        Whitebox.invokeMethod(service, "handlerBatchReturnEntityAndSkip", comMacAssScanDTO, bindingInfoList, codeList, seqCodeList, true);
    }

    @Test
    public void getNeedCheckSnsNew () throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<String> subBarcodeList = new ArrayList<>();
        subBarcodeList.add("1231");
        comMacAssScanDTO.setSubBarcodeList(subBarcodeList);
        List<WipExtendIdentification> bindingList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("123");
        bindingList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getAllChildSn(Mockito.anyList())).thenReturn(bindingList);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getNeedCheckSnsNew", comMacAssScanDTO));
    }

    @Test
    public void batchCodeUsageCheck() throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<BaItem> baItemInfoList = new ArrayList<>();
        comMacAssScanDTO.setNeedUsage(true);
        Assert.assertEquals(Whitebox.invokeMethod(service, "batchCodeUsageCheck", comMacAssScanDTO, baItemInfoList), true);
        comMacAssScanDTO.setNeedUsage(false);
        comMacAssScanDTO.setBatchCodeQty(new BigDecimal(1));
        Assert.assertEquals(Whitebox.invokeMethod(service, "batchCodeUsageCheck", comMacAssScanDTO, baItemInfoList), true);
    }

    @Test
    public void checkSubSnCategoryNew() throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setItemCode("123123");
        dto.setIsLead("有铅");
        dto.setCategoryCode(Constant.ZJ_CATEGORY_CODE);
        barcodeExpandDTOList.add(dto);
        PsWipInfo mainSnWipInfo = null;
        List<SysLookupValuesDTO> listSys = new ArrayList<>();
        SysLookupValuesDTO sys1 = new SysLookupValuesDTO();
        sys1.setDescriptionChin("有铅");
        sys1.setAttribute1("10");
        SysLookupValuesDTO sys2 = new SysLookupValuesDTO();
        sys2.setDescriptionChin("无铅");
        sys2.setAttribute1("20");
        listSys.add(sys1);
        listSys.add(sys2);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys);
        ArrayList<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setCategoryCode("SN_CODE");
        wipExtendIdentification.setItemNo("123123");
        wipExtendIdentifications.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.anyMap())).thenReturn(wipExtendIdentifications);
        try {
            Whitebox.invokeMethod(service, "checkSubSnCategoryNew", comMacAssScanDTO, barcodeExpandDTOList, mainSnWipInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_NOT_HAVE_WIP_INFO, e.getMessage());
        }
        mainSnWipInfo = new PsWipInfo();
        mainSnWipInfo.setAttribute3("有铅");
        try {
            Whitebox.invokeMethod(service, "checkSubSnCategoryNew", comMacAssScanDTO, barcodeExpandDTOList, mainSnWipInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, e.getMessage());
        }
        dto.setCategoryCode(Constant.SN_CODE);
        try {
            Whitebox.invokeMethod(service, "checkSubSnCategoryNew", comMacAssScanDTO, barcodeExpandDTOList, mainSnWipInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_CATEGORY_NOT_IN_BIND_LIST, e.getMessage());
        }
    }

    @Test
    public void subDeviceSnBindCheck() throws Exception {
        Map<String, String> snProductCodeMap = new HashMap<>();
        snProductCodeMap.put("123","123");
        List<String> deviceSnList = new ArrayList<>();
        List<String> deviceItemList = new ArrayList<>();
        Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        deviceSnList.add("123");
        List<ProdBindingSetting> settingList = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.queryList(Mockito.any())).thenReturn(settingList);
        Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        ProdBindingSetting setting = new ProdBindingSetting();
        setting.setProductCode("123");
        setting.setItemCode("123");
        setting.setUsageCount(new BigDecimal(2));
        settingList.add(setting);
        try {
            Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BIND_INFO_SN, e.getMessage());
        }
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormSn("321");
        wipExtendList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.queryDeviceBindingQtyBatch(Mockito.anyList())).thenReturn(wipExtendList);
        try {
            Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BIND_INFO_SN, e.getMessage());
        }
        wipExtendIdentification.setFormSn("123");
        wipExtendIdentification.setItemNo("321");
        wipExtendIdentification.setFormQty(new BigDecimal(1));
        try {
            Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_BIND_NO_END, e.getMessage());
        }
        wipExtendIdentification.setItemNo("123");
        try {
            Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_BIND_NO_END, e.getMessage());
        }
        setting.setUsageCount(new BigDecimal(1));
        Whitebox.invokeMethod(service, "subDeviceSnBindCheck", snProductCodeMap, deviceSnList, deviceItemList);
    }

    @Test
    public void stepItemInErpInfoCheck() throws Exception {
        List<BaItem> baItemInfoList = new ArrayList<>();
        BaItem item = new BaItem();
        item.setSn("123");
        item.setItemNo("321321321321");
        baItemInfoList.add(item);
        Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem = new HashMap<>();
        List<MtlRelatedItemsEntityDTO> relatedItemsEntityDTOS = new ArrayList<>();
        MtlRelatedItemsEntityDTO relatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        relatedItemsEntityDTO.setInventoryItemCode("123123123123");
        relatedItemsEntityDTOS.add(relatedItemsEntityDTO);
        replaceItem.put("111111111111", relatedItemsEntityDTOS);
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        Map<String, ItemListEntityDTO> stepItemMap = new HashMap<>();
        ItemListEntityDTO entityDTO = new ItemListEntityDTO();
        entityDTO.setItemNo("123123123123");
        stepItemMap.put("123", entityDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "stepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "noStepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        List<String> stepSnList = new ArrayList<>();
        stepSnList.add("123");
        comMacAssScanDTO.setStepSnList(stepSnList);
        comMacAssScanDTO.setNoStepSnList(stepSnList);
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "stepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "noStepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        replaceItem.put("321321321321", relatedItemsEntityDTOS);
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "stepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(service, "noStepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        stepItemMap.put("123123123123", entityDTO);
        Set<String> fixBomSet = new HashSet<>();
        comMacAssScanDTO.setFixBomSet(fixBomSet);
        Assert.assertTrue(CollectionUtils.isNotEmpty(Whitebox.invokeMethod(service, "stepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        Assert.assertTrue(CollectionUtils.isNotEmpty(Whitebox.invokeMethod(service, "noStepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap)));
        fixBomSet.add("123123123123");
        try {
            Whitebox.invokeMethod(service, "stepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, e.getMessage());
        }
        try {
            Whitebox.invokeMethod(service, "noStepItemInErpInfoCheck", baItemInfoList, replaceItem, comMacAssScanDTO, stepItemMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_CAN_NOT_BE_REPLACE, e.getMessage());
        }
    }

    @Test
    public void fixBomCheck() throws Exception {
        PsWipInfo wipInfoOfMainSn = new PsWipInfo();
        List<FixBomDetailDTO> fixBomByTaskNo = new ArrayList<>();
        FixBomDetailDTO detailDTO = new FixBomDetailDTO();
        detailDTO.setZteCode("312");
        detailDTO.setItemQty(1D);
        detailDTO.setRequireMaterialUpload("Y");
        fixBomByTaskNo.add(detailDTO);
        FixBomDetailDTO detailDTO1 = new FixBomDetailDTO();
        fixBomByTaskNo.add(detailDTO1);
        detailDTO1.setZteCode("123");
        detailDTO1.setItemQty(2D);
        detailDTO1.setUploadBySn("Y");
        detailDTO1.setRequireMaterialUpload("Y");
        detailDTO1.setFixBomRequired("Y");
        FixBomDetailDTO detailDTO2 = new FixBomDetailDTO();
        detailDTO2.setZteCode("321");
        detailDTO2.setItemQty(3D);
        detailDTO2.setRequireMaterialUpload("Y");
        detailDTO2.setItemType("成品料");
        fixBomByTaskNo.add(detailDTO2);
        FixBomDetailDTO detailDTO4 = new FixBomDetailDTO();
        detailDTO4.setZteCode("112");
        detailDTO4.setItemQty(1D);
        detailDTO4.setRequireMaterialUpload("N");
        fixBomByTaskNo.add(detailDTO4);
        PowerMockito.when(centerfactoryRemoteService.getFixBomByTaskNo(Mockito.any())).thenReturn(fixBomByTaskNo);
        try {
            Whitebox.invokeMethod(service, "fixBomCheck", wipInfoOfMainSn);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_BIND_INFO, e.getMessage());
        }
        List<ProdBindingSetting> prodBindingInfoList = new ArrayList<>();
        ProdBindingSetting setting = new ProdBindingSetting();
        setting.setItemCode("123");
        setting.setUsageCount(new BigDecimal(2));
        prodBindingInfoList.add(setting);
        ProdBindingSetting setting1 = new ProdBindingSetting();
        setting1.setItemCode("321");
        setting1.setUsageCount(new BigDecimal(2));
        prodBindingInfoList.add(setting1);
        PowerMockito.when(prodBindingSettingService.getUsageCountByProductCode(Mockito.anyMap())).thenReturn(prodBindingInfoList);
        try {
            Whitebox.invokeMethod(service, "fixBomCheck", wipInfoOfMainSn);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_BIND_INCOMPLETE, e.getMessage());
        }
        setting1.setUsageCount(new BigDecimal(3));
        ProdBindingSetting setting2 = new ProdBindingSetting();
        setting2.setItemCode("312");
        setting2.setUsageCount(new BigDecimal(1));
        prodBindingInfoList.add(setting2);
        Whitebox.invokeMethod(service, "fixBomCheck", wipInfoOfMainSn);
    }

    @Test
    public void checkBindListHaveTheItemNoStep() throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<String> noStepSnList = new ArrayList<>();
        noStepSnList.add("123");
        comMacAssScanDTO.setNoStepSnList(noStepSnList);
        Map<String, ProdBindingSettingDTO> noStepBindInfoMap = new HashMap<>();
        ProdBindingSettingDTO bindingSettingDTO = new ProdBindingSettingDTO();
        noStepBindInfoMap.put("123", bindingSettingDTO);
        List<BaItem> baItemInfoList = new ArrayList<>();
        BaItem baItem = new BaItem();
        baItem.setSn("123");
        baItem.setItemNo("123");
        baItemInfoList.add(baItem);
        Set<String> noStepErpSet = new HashSet<>();
        Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem = new HashMap<>();
        try {
            Whitebox.invokeMethod(service, "checkBindListHaveTheItemNoStep",
                    comMacAssScanDTO, noStepBindInfoMap, baItemInfoList, noStepErpSet, replaceItem);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_MATERIAL_STANDARD_USAGE, e.getMessage());
        }
    }

    @Test
    public void checkErpTaskHaveTheItemNew () throws Exception {
        PsWipInfo wipInfoByMainSn = new PsWipInfo();
        try {
            Whitebox.invokeMethod(service, "checkErpTaskHaveTheItemNew", new ComMachineAssemblyScanDTO(), new ArrayList<>(), wipInfoByMainSn, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_WIP_INFO_NOT_HAVE_TASK_NO, e.getMessage());
        }
        wipInfoByMainSn.setOriginalTask("123");
        try {
            Whitebox.invokeMethod(service, "checkErpTaskHaveTheItemNew", new ComMachineAssemblyScanDTO(), new ArrayList<>(), wipInfoByMainSn, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void checkHaveBindRelationNew () throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<BaItem> seqCodeList = new ArrayList<>();
        List<BaItem> batchCodeList = new ArrayList<>();
        List<WipExtendIdentification> entityList = new ArrayList<>();
        entityList.add(new WipExtendIdentification());
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnList(Mockito.anyList())).thenReturn(entityList);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndFormSn(Mockito.anyList(), Mockito.anyList())).thenReturn(entityList);
        seqCodeList.add(new BaItem());
        try {
            Whitebox.invokeMethod(service, "checkHaveBindRelationNew", comMacAssScanDTO, seqCodeList, batchCodeList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_BARCODE_HAVE_BIND_RELATION, e.getMessage());
        }
        seqCodeList.remove(0);
        batchCodeList.add(new BaItem());
        try {
            Whitebox.invokeMethod(service, "checkHaveBindRelationNew", comMacAssScanDTO, seqCodeList, batchCodeList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_BARCODE_HAVE_BOUND_IN_MAIN_BARCODE, e.getMessage());
        }
    }

    @Test
    public void checkProcessOfSubSnTest() throws Exception {
        PsWipInfo wipInfoOfMainSn = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.anyString())).thenReturn(wipInfoOfMainSn);

        Assert.assertEquals(Whitebox.invokeMethod(service, "checkProcessOfSubSn", "219"), wipInfoOfMainSn);
    }
    @Test
    public void handlerBindAndPassStationTest() throws Exception {
        ComMachineAssemblyScanDTO comMacAssScanDTO = new ComMachineAssemblyScanDTO();
        List<BaItem> baItemList = new ArrayList<>();
        BaItem baItem = new BaItem();
        baItem.setSn("123");
        baItemList.add(baItem);
        List<ProdBindingSettingDTO> bindingInfoList = new ArrayList<>();
        comMacAssScanDTO.setFactoryId("52");
        comMacAssScanDTO.setItemCode("123456789012");
        comMacAssScanDTO.setIfPassStationSwitch(true);
        comMacAssScanDTO.setWorkStationBindFlag(true);
        PsWipInfo wipInfoOfMainSn = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.anyString())).thenReturn(wipInfoOfMainSn);


        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("mainItem");
        prodBindingSettingDTO.setItemCode("123456789013ABC");
        prodBindingSettingDTO.setItemName("subItemName1");
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));
        prodBindingSettingDTO.setUsageCount(new BigDecimal("3"));
        prodBindingSettingDTO.setWorkStation("workStation");
        bindingInfoList.add(prodBindingSettingDTO);
        FlowControlInfoDTO flowResult = new FlowControlInfoDTO();
        flowResult.setResultType("FAIL");
        PowerMockito.when(standardModeCommonScanService.checkWorkorderAndFlow(Mockito.any())).thenReturn(flowResult);

        // 条码中心有条码
        List<BarcodeExpandDTO> barcodeList = new ArrayList<>();
        BarcodeExpandDTO barcode = new BarcodeExpandDTO();
        barcode.setBarcode("subSn");
        barcode.setItemCode("123456789012ABC");
        barcode.setItemName("subItemName");
        barcode.setParentCategoryName("批次码");
        barcodeList.add(barcode);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(barcodeList);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);

        prodBindingSettingDTO.setBindedCount(new BigDecimal("3"));
        comMacAssScanDTO.setIfPassStationSwitch(false);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        comMacAssScanDTO.setBatchCodeQty(new BigDecimal("1"));
        comMacAssScanDTO.setIfPassStationSwitch(true);

        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<BSProcess> bsProcesses = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setCraftSection("装配");
        ServiceData serviceData2 = new ServiceData();
        serviceData2.setCode(serviceData2.getCode());
        serviceData2.setBo(bsProcesses);
        JsonNode treeNode2 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData2));
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(treeNode2);
        // 每次调用前，需要将数量重置为1.因为成功计算后，会相加
        prodBindingSettingDTO.setBindedCount(new BigDecimal("1"));

        try {
            service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, e.getExMsgId());
        }
        bsProcesses.add(bsProcess);
        serviceData2.setBo(bsProcesses);
        treeNode2 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData2));
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(treeNode2);
        try {
            service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList,"", null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, e.getExMsgId());
        }

        comMacAssScanDTO.setSkipTotalQty(3);
        try {
            service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList,"", null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, e.getExMsgId());
        }

        flowResult.setResultType("SUCCESS");

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWorkOrderDTO> psWorkOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setCraftSection("装配");
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(serviceData.getCode());
        serviceData.setBo(psWorkOrderDTOS);
        JsonNode treeNode3 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(Mockito.anyMap())).thenReturn(treeNode3);
        try {
            service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList,"test", null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_NOT_HAVE_WORK_ORDER_NO, e.getExMsgId());
        }

        psWorkOrderDTOS.add(psWorkOrderDTO);
        serviceData.setBo(psWorkOrderDTOS);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(Mockito.anyMap())).thenReturn(treeNode2);
        bindingInfoList.get(NumConstant.NUM_ZERO).setBindedCount(NumConstant.BIG_THREE);
        comMacAssScanDTO.setSateSkipFlag(true);
        comMacAssScanDTO.setIfPassStationSwitch(true);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "test", null);
        comMacAssScanDTO.setAutoSubmitWarehouse(true);
        MixWarehouseSubmitDTO mixWarehouseSubmit = new MixWarehouseSubmitDTO();
        mixWarehouseSubmit.setWipSns(Collections.singletonList("123456"));
        comMacAssScanDTO.setMixWarehouseSubmit(mixWarehouseSubmit);
        ArrayList<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("123456");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(any())).thenReturn(psWipInfos);
        ArrayList<PsWorkOrderBasic> psWorkOrderBasics = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasics.add(psWorkOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any())).thenReturn(psWorkOrderBasics);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        ArrayList<CtRouteHead> ctRouteHeads = new ArrayList<>();
        CtRouteHead ctRouteHead = new CtRouteHead();
        ctRouteHeads.add(ctRouteHead);
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(anyList())).thenReturn(ctRouteHeads);

        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setWorkStation("123456");
        psWipInfo1.setLastProcess("N");
        psWipInfos.add(psWipInfo1);
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setWorkStation("123456");
        psWipInfo2.setLastProcess("N");
        psWipInfo2.setAttribute1("123456");
        psWipInfos.add(psWipInfo2);
        PsWipInfo psWipInfo3 = new PsWipInfo();
        psWipInfo3.setWorkStation("123456");
        psWipInfo3.setLastProcess("Y");
        psWipInfos.add(psWipInfo3);
        PsWipInfo psWipInfo4 = new PsWipInfo();
        psWipInfo4.setWorkStation("123456");
        psWipInfo4.setLastProcess("N");
        psWipInfos.add(psWipInfo4);
        PsWipInfo psWipInfo5 = new PsWipInfo();
        psWipInfo5.setWorkStation("0");
        psWipInfos.add(psWipInfo5);
        PsWipInfo psWipInfo6 = new PsWipInfo();
        psWipInfo6.setWorkStation("123456");
        psWipInfo6.setLastProcess("Y");
        psWipInfo6.setAttribute1("123456");
        psWipInfos.add(psWipInfo6);
        PsWipInfo psWipInfo7 = new PsWipInfo();
        psWipInfo7.setWorkStation("123456");
        psWipInfo7.setLastProcess("Y");
        psWipInfo7.setAttribute1("123456");
        psWipInfo7.setCurrProcessCode("123456");
        psWipInfos.add(psWipInfo7);

        psWorkOrderBasic.setSourceTask("123456");
        psWorkOrderBasic.setRouteId("123456");
        PsWorkOrderBasic psWorkOrderBasic1 = new PsWorkOrderBasic();
        psWorkOrderBasic1.setSourceTask("123456");
        psWorkOrderBasic1.setRouteId("123456");
        psWorkOrderBasics.add(psWorkOrderBasic1);
        PsWorkOrderBasic psWorkOrderBasic2 = new PsWorkOrderBasic();
        psWorkOrderBasic2.setRouteId("12345");
        psWorkOrderBasics.add(psWorkOrderBasic2);

        ctRouteHead.setRouteId("123456");
        ctRouteHead.setRouteCodeDetail("123456");
        CtRouteHead ctRouteHead1 = new CtRouteHead();
        ctRouteHead1.setRouteId("123456");
        ctRouteHead1.setRouteCodeDetail("123456");
        ctRouteHeads.add(ctRouteHead1);
        CtRouteHead ctRouteHead2 = new CtRouteHead();
        ctRouteHead2.setRouteId("12345");
        ctRouteHeads.add(ctRouteHead2);

        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        psWipInfos.clear();
        PsWipInfo psWipInfo8 = new PsWipInfo();
        psWipInfo8.setWorkStation("123456");
        psWipInfo8.setLastProcess("Y");
        psWipInfo8.setAttribute1("123456");
        psWipInfo8.setCurrProcessCode("123456");
        psWipInfos.add(psWipInfo8);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        PowerMockito.doThrow(new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.USAGE_COUNT_IS_NULL)).when(mixWarehouseSubmitService).submit(any(),anyString(),any());
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(null);
        comMacAssScanDTO.setPassStationSucFlag(false);
        Assert.assertThrows(MessageId.PROCESS_DETAILS_NOT_FOUND, MesBusinessException.class,()->service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null));
        comMacAssScanDTO.setSkipTotalQty(NumConstant.NUM_TWENTY);
        comMacAssScanDTO.setSateSkipFlag(false);
        service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null);
        comMacAssScanDTO.setSateSkipFlag(true);
        Assert.assertThrows(MessageId.PROCESS_DETAILS_NOT_FOUND, MesBusinessException.class,()->service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", null));
        BaItem baItem1 = new BaItem();
        baItem1.setSn("321");
        baItemList.add(baItem1);
        try {
            service.handlerBindAndPassStation(comMacAssScanDTO, baItemList, bindingInfoList, "", new PsWipInfo());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_DETAILS_NOT_FOUND, e.getMessage());
        }
    }
    @Test
    public void assemblyScanningSelectSettingCacheTest() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        String value = JSON.toJSONString(new ComMachineAssemblyScanDTO());
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(null);
        service.assemblyScanningSelectSettingCache("test", "test");
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(value);
        Assert.assertNotNull(service.assemblyScanningSelectSettingCache("test", "test"));
    }

    @Test
    public void insertWipExtend() {
        List<String> snList = new ArrayList<>();
        snList.add("test123");
        AssemblyRelaScanDTO dto= new AssemblyRelaScanDTO();
        dto.setSubSnList(snList);
        dto.setNotAutoPassWorkStaion(false);
        dto.setSource("");
        List<BaItem> itemInfoList = new ArrayList<>();
        BaItem baItem = new BaItem();
        baItem.setQty(new BigDecimal("1"));
        baItem.setReplaceItemNo("test");
        baItem.setRemark("");
        baItem.setSn("test123");
        itemInfoList.add(baItem);
        PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
        Map<String, String> replaceMap = new HashMap<>();
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(wipExtendIdentificationService).batchInsertOptRecord(Mockito.anyString(),Mockito.anyList());
       PowerMockito.when(wipExtendIdentificationRepository.insertWipExtendBatch(Mockito.anyList())).thenReturn(1);
        try{
            service.insertWipExtend(dto,itemInfoList,workOrder,replaceMap);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setNotAutoPassWorkStaion(true);
        dto.setSource("DQAS");
        try{
            service.insertWipExtend(dto,itemInfoList,workOrder,replaceMap);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        baItem.setSn("test124");
        try{
            service.insertWipExtend(dto,itemInfoList,workOrder,replaceMap);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}
