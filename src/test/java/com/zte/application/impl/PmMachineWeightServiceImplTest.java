package com.zte.application.impl;

import com.alibaba.excel.EasyExcel;
import com.zte.application.helper.MachineWeightValidateHelper;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.consts.CommonConst;
import com.zte.domain.DO.PmMachineWeightDO;
import com.zte.domain.DTO.PmMachineSearchWeightDTO;
import com.zte.domain.DTO.PmMachineWeightImportDTO;
import com.zte.domain.DTO.PmMachineWeightSearchDTO;
import com.zte.domain.model.*;
import com.zte.domain.vo.PmMachineSearchWeightVO;
import com.zte.domain.vo.PmMachineWeightVO;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({ MESHttpHelper.class, CommonUtils.class ,ExcelCommonUtils.class, EasyExcel.class})
public class PmMachineWeightServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    PmMachineWeightServiceImpl service;

    @Mock
    PmMachineWeightRepository repository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private MachineWeightValidateHelper machineWeightValidateHelper;

    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private InputStream inputStream;
    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletOutputStream outputStream;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemote;

    @Mock
    private HttpServletRequest httpRequest;

    @Test
    public void getExistSn() {
        List<String> snList = new ArrayList<>();
        List<String> result = service.getExistSn(snList);
        assertTrue(result.isEmpty());

        snList.add("test");
        result = service.getExistSn(snList);
        assertTrue(result.isEmpty());

        List<String> existSnList1 = new ArrayList<>();
        existSnList1.add("test");
        when(repository.getExistSn(anyList())).thenReturn(Collections.emptyList());
        List<List<String>> listOfList1 = new ArrayList<>();
        listOfList1.add(existSnList1);
        when(CommonUtils.splitList(eq(existSnList1))).thenReturn(listOfList1);
        result = service.getExistSn(snList);
        Assert.assertTrue(result.isEmpty());


        List<String> existSnList = new ArrayList<>();
        existSnList.add("test");
        when(repository.getExistSn(anyList())).thenReturn(existSnList);
        List<List<String>> listOfList = new ArrayList<>();
        listOfList.add(existSnList);
        when(CommonUtils.splitList(eq(existSnList))).thenReturn(listOfList);
        result = service.getExistSn(snList);
        Assert.assertFalse(result.isEmpty());
    }

    /* Started by AICoder, pid:taa21546a4lde89140450844d011a9477bd7a579 */
    @Test
    public void checkSnList() {
        List<String> snList = new ArrayList<>();
        service.checkSnList(snList);

        snList.add("1");
        try {
            service.checkSnList(snList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_NO_WEIGHT_INFO, e.getExMsgId());
        }

        List<String> existSnList = new ArrayList<>();
        existSnList.add("1");
        PowerMockito.when(repository.getExistSn(Mockito.anyList())).thenReturn(existSnList);

        List<List<String>> listOfList = new ArrayList<>();
        listOfList.add(existSnList);
        when(CommonUtils.splitList(eq(existSnList))).thenReturn(listOfList);

        service.checkSnList(snList);
        Assert.assertFalse(existSnList.isEmpty());
    }
    /* Ended by AICoder, pid:taa21546a4lde89140450844d011a9477bd7a579 */

    /* Started by AICoder, pid:zc7aefdc8a25e6714e7d0b2ae2d10b331ec3fc0a */

    @Before
    public void setUp() throws Exception{
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(ExcelCommonUtils.class);
        // 模拟静态HTTP上下文
        PowerMockito.mockStatic(MESHttpHelper.class);
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<String, String>(){{
            put("x-emp-no", "TEST001");
        }});
        // 新增对CommonUtils的静态方法模拟
        PowerMockito.mockStatic(CommonUtils.class);

        // 配置getLmbMessage的默认返回值
        when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("测试错误消息");
        // 设置empNo
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(Collections.singletonMap("x-emp-no", "testEmpNo"));

    }

    @Test
    public void testGetExistSn_EmptyList() {
        List<String> snList = new ArrayList<>();
        List<String> result = service.getExistSn(snList);
        assertTrue(result.isEmpty());
    }

    /* Started by AICoder, pid:060c134aec1442814fba093020bc7641a189b3ba */
    @Test
    public void testListByPage_NoStorageFlag() throws Exception {
        /** 测试没有存储标志的情况 */
        PmMachineWeightSearchDTO searchDTO = new PmMachineWeightSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        List<PmMachineWeightVO> machineWeightVOS = new ArrayList<>();
        PmMachineWeightVO vo = new PmMachineWeightVO();
        vo.setSn("SN123");
        machineWeightVOS.add(vo);

        when(repository.listByPage(any())).thenReturn(machineWeightVOS);
        when(machineWeightValidateHelper.validateSNIsStorage("SN123")).thenReturn(false);

        when(machineWeightValidateHelper.validateTimeRelation(any())).thenReturn("");

        when(centerfactoryRemote.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        PageRows<PmMachineWeightVO> result = service.listByPage(searchDTO);

        assertNotNull(result);
        assertEquals(1, result.getRows().size());
        assertEquals(CommonConst.ENABLE_FLAG_N, result.getRows().get(0).getStorageFlag());
        assertEquals(1, result.getCurrent());
        assertEquals(0, result.getTotal()); // 假设total默认为0
    }

    @Test
    public void testListByPage_WithStorageFlag() throws Exception {
        /** 测试有存储标志的情况 */
        PmMachineWeightSearchDTO searchDTO = new PmMachineWeightSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        List<PmMachineWeightVO> machineWeightVOS = new ArrayList<>();
        PmMachineWeightVO vo = new PmMachineWeightVO();
        vo.setSn("SN123");
        machineWeightVOS.add(vo);

        when(repository.listByPage(any())).thenReturn(machineWeightVOS);
        when(machineWeightValidateHelper.validateSNIsStorage("SN123")).thenReturn(true);

        when(centerfactoryRemote.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        PageRows<PmMachineWeightVO> result = service.listByPage(searchDTO);

        assertNotNull(result);
        assertEquals(1, result.getRows().size());
        assertEquals(CommonConst.ENABLE_FLAG_Y, result.getRows().get(0).getStorageFlag());
        assertEquals(1, result.getCurrent());
        assertEquals(0, result.getTotal()); // 假设total默认为0
    }

    @Test(expected = BusiException.class)
    public void testListByPage_WithException() throws Exception {
        /** 测试有异常的情况 */
        PmMachineWeightSearchDTO searchDTO = new PmMachineWeightSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        when(machineWeightValidateHelper.validateTimeRelation(any())).thenReturn("exception");

        service.listByPage(searchDTO);

    }
    /* Ended by AICoder, pid:060c134aec1442814fba093020bc7641a189b3ba */

    /* Started by AICoder, pid:39134v0dbe93506144c80a9890a33273fa557d1b */
    @Test
    public void testSave_ValidationFails() {
        /** 测试：验证失败时抛出异常 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn("Error");

        try {
            service.save(pmMachineWeightDO);
            fail("Expected BusiException");
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals("Error", e.getMessage());
        }
    }

    @Test
    public void testSave_UpdateExistingRecord() {
        /** 测试：更新已存在的记录 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn(null);
        List<PmMachineWeightDO> existingRecords = Arrays.asList(new PmMachineWeightDO());
        existingRecords.get(0).setEnabledFlag(CommonConst.ENABLE_FLAG_N);
        when(repository.selectByPrimaries(anyList())).thenReturn(existingRecords);

        String empNo = "EMP123";
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(Collections.singletonMap("x-emp-no", empNo));

        service.save(pmMachineWeightDO);
        verify(repository).updateByPrimaryKeySelective(any());
    }

    @Test
    public void testSave_InsertNewRecord() {
        /** 测试：插入新记录 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn(null);
        when(repository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("Task123");
        when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(psWipInfo);

        String empNo = "EMP123";
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(Collections.singletonMap("x-emp-no", empNo));

        service.save(pmMachineWeightDO);
        verify(repository).insertSelective(any());
    }

    @Test
    public void testSave_PsWipInfoAttribute2IsEmpty() {
        /** 测试：PsWipInfo的attribute2为空时设置为None */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn(null);
        when(repository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("");
        when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(psWipInfo);

        String empNo = "EMP123";
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(Collections.singletonMap("x-emp-no", empNo));

        service.save(pmMachineWeightDO);
        verify(repository).insertSelective(any());
    }
    /* Ended by AICoder, pid:39134v0dbe93506144c80a9890a33273fa557d1b */

    @Test
    public void testUpdate_ValidData() {
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN1");
        pmMachineWeightDO.setWeight("0.1");

        when(machineWeightValidateHelper.validateSNIsStorage(pmMachineWeightDO.getSn())).thenReturn(false);
        when(repository.updateByPrimaryKeySelective(pmMachineWeightDO)).thenReturn(1);

        when(machineWeightValidateHelper.isValidWeight(pmMachineWeightDO.getWeight())).thenReturn(true);

        int result = service.update(pmMachineWeightDO);

        assertEquals(1, result);
    }

    @Test(expected = BusiException.class)
    public void testUpdate_InvalidWeightData() {
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN1");
        pmMachineWeightDO.setWeight("0.1");

        when(machineWeightValidateHelper.validateSNIsStorage("SN1")).thenReturn(false);
        when(machineWeightValidateHelper.isValidWeight(pmMachineWeightDO.getWeight())).thenReturn(false);

        service.update(pmMachineWeightDO);
    }

    @Test(expected = BusiException.class)
    public void testUpdate_InvalidStorageData() {
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN1");

        when(machineWeightValidateHelper.validateSNIsStorage("SN1")).thenReturn(true);

        service.update(pmMachineWeightDO);
    }

    @Test
    public void testRemove_ValidData() {
        String sn = "SN1";

        when(machineWeightValidateHelper.validateSNIsStorage(sn)).thenReturn(false);
        when(repository.updateByPrimaryKeySelective(any())).thenReturn(1);

        int result = service.remove(sn);

        assertEquals(1, result);
    }

    @Test(expected = BusiException.class)
    public void testRemove_InvalidData() {
        String sn = "SN1";

        when(machineWeightValidateHelper.validateSNIsStorage(sn)).thenReturn(true);

        service.remove(sn);
    }

    /* Ended by AICoder, pid:zc7aefdc8a25e6714e7d0b2ae2d10b331ec3fc0a */

    /* Started by AICoder, pid:g721f89c81vd3f314a9d082f01b5ec23e1010acb */
    @Test
    public void testResolve_FileIsNull() throws IOException {
        try {
            service.resolve(null);
            fail("Expected BusiException to be thrown");
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void testResolve_ExcelResolveError() throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));

        ResultData resultData = new ResultData();
        resultData.setCode("error");

        PowerMockito.mockStatic(ExcelCommonUtils.class);
        when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        try {
            service.resolve(file);
            fail("Expected BusiException to be thrown");
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void testResolve_EmptyImportDTOS() throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));

        ResultData resultData = new ResultData();
        resultData.setCode(Constant.API_RESULT_CODE_SUCCESS);
        resultData.setData(Collections.emptyList());

        PowerMockito.mockStatic(ExcelCommonUtils.class);
        when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        try {
            service.resolve(file);
            fail("Expected BusiException to be thrown");
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void testResolve_SizeOverLimit() throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));

        ResultData resultData = new ResultData();
        resultData.setCode(Constant.API_RESULT_CODE_SUCCESS);
        resultData.setData(Collections.nCopies(1001, new PmMachineWeightImportDTO()));

        PowerMockito.mockStatic(ExcelCommonUtils.class);
        when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        try {
            service.resolve(file);
            fail("Expected BusiException to be thrown");
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void testResolve_ValidData() throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));

        PmMachineWeightImportDTO dto = new PmMachineWeightImportDTO();
        dto.setSn("SN123");
        dto.setWeight("10.5");

        ResultData resultData = new ResultData();
        resultData.setCode(Constant.API_RESULT_CODE_SUCCESS);
        resultData.setData(Collections.singletonList(dto));

        PowerMockito.mockStatic(ExcelCommonUtils.class);
        when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn(null);

        List<PmMachineWeightImportDTO> result = service.resolve(file);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("SN123", result.get(0).getSn());
        assertEquals("10.5", result.get(0).getWeight(), "10.5");
    }

    @Test
    public void testResolve_InvalidData() throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));

        PmMachineWeightImportDTO dto = new PmMachineWeightImportDTO();
        dto.setSn("SN123");
        dto.setWeight("10.5");

        ResultData resultData = new ResultData();
        resultData.setCode(Constant.API_RESULT_CODE_SUCCESS);
        resultData.setData(Collections.singletonList(dto));

        PowerMockito.mockStatic(ExcelCommonUtils.class);
        when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn("Validation Error");

        List<PmMachineWeightImportDTO> result = service.resolve(file);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("SN123", result.get(0).getSn());
        assertEquals("10.5", result.get(0).getWeight(), "10.5");
        assertEquals("Validation Error", result.get(0).getValidateMsg());
    }
    /* Ended by AICoder, pid:g721f89c81vd3f314a9d082f01b5ec23e1010acb */

    /* Started by AICoder, pid:l1b837c9bf43b2f148be0974d00a22839903d531 */
    @Test
    public void testDeviceSave_ValidationFails() {
        /** 测试设备保存时验证失败的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("testSn");

        when(machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO)).thenReturn("Validation Error");

        try {
            service.deviceSave(pmMachineWeightDO);
            fail("Expected BusiException to be thrown");
        } catch (BusiException e) {
            assertEquals("0005", e.getExCode());
            assertEquals("Validation Error", e.getMessage());
        }
    }

    @Test
    public void testDeviceSave_UpdateExistingRecord() {
        /** 测试设备保存时更新已存在记录的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("testSn");

        when(machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO)).thenReturn(null);

        PmMachineWeightDO existingRecord = new PmMachineWeightDO();
        existingRecord.setSn("testSn");
        existingRecord.setEnabledFlag("Y");

        List<PmMachineWeightDO> records = Arrays.asList(existingRecord);
        when(repository.selectByPrimaries(Collections.singletonList("testSn"))).thenReturn(records);

        when(repository.updateByPrimaryKeySelective(any(PmMachineWeightDO.class))).thenReturn(1);

        int result = service.deviceSave(pmMachineWeightDO);

        assertEquals(1, result);
        verify(repository).updateByPrimaryKeySelective(any(PmMachineWeightDO.class));
    }

    @Test
    public void testDeviceSave_InsertNewRecord() {
        /** 测试设备保存时插入新记录的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("testSn");

        when(machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO)).thenReturn(null);

        when(repository.selectByPrimaries(Collections.singletonList("testSn"))).thenReturn(Collections.emptyList());

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("taskNo");
        when(psWipInfoRepository.selectPsWipInfoBySn("testSn")).thenReturn(psWipInfo);

        when(repository.insertSelective(any(PmMachineWeightDO.class))).thenReturn(1);

        int result = service.deviceSave(pmMachineWeightDO);

        assertEquals(1, result);
        verify(repository).insertSelective(any(PmMachineWeightDO.class));
    }

    @Test
    public void testDeviceSave_PsWipInfoAttribute2IsEmpty() {
        /** 测试设备保存时PsWipInfo的attribute2为空的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("testSn");

        when(machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO)).thenReturn(null);

        when(repository.selectByPrimaries(Collections.singletonList("testSn"))).thenReturn(Collections.emptyList());

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("");
        when(psWipInfoRepository.selectPsWipInfoBySn("testSn")).thenReturn(psWipInfo);

        when(repository.insertSelective(any(PmMachineWeightDO.class))).thenReturn(1);

        int result = service.deviceSave(pmMachineWeightDO);

        assertEquals(1, result);
        verify(repository).insertSelective(any(PmMachineWeightDO.class));
    }
    /* Ended by AICoder, pid:l1b837c9bf43b2f148be0974d00a22839903d531 */

    /* Started by AICoder, pid:jbcc3j1115nfddf1481b088c007ff3972902541b */
    @Test(expected = BusiException.class)
    public void testSearchWeight_SnListSizeGreaterThan500() throws Exception {
        /** 测试当snList大小超过500时抛出BusiException */
        PmMachineSearchWeightDTO dto = new PmMachineSearchWeightDTO();
        dto.setSnList(Collections.nCopies(501, "SN"));
        service.searchWeight(dto);
    }

    @Test
    public void testSearchWeight_EmptySnList() throws Exception {
        /** 测试当snList */
        PmMachineSearchWeightDTO dto = new PmMachineSearchWeightDTO();
        dto.setSnList(Collections.emptyList());
        List<PmMachineSearchWeightVO> pmMachineSearchWeightVOS = service.searchWeight(dto);
        assertTrue(pmMachineSearchWeightVOS.isEmpty());
    }

    @Test
    public void testSearchWeight_ValidSnList() throws Exception {
        /** 测试有效的snList，验证返回结果是否正确 */
        PmMachineSearchWeightDTO dto = new PmMachineSearchWeightDTO();
        List<String> snList = Arrays.asList("SN1", "SN2");
        dto.setSnList(snList);

        PmMachineSearchWeightVO searchWeightVO = new PmMachineSearchWeightVO();
        searchWeightVO.setSn("SN1");
        searchWeightVO.setCreateBy("emp1");
        searchWeightVO.setLastUpdatedBy("emp2");
        PmMachineSearchWeightVO searchWeightVO2 = new PmMachineSearchWeightVO();
        searchWeightVO2.setSn("SN2");
        searchWeightVO2.setCreateBy("emp3");
        searchWeightVO2.setLastUpdatedBy("emp4");

        List<PmMachineSearchWeightVO> mockVos = Arrays.asList(searchWeightVO,searchWeightVO2);

        when(repository.listBySn(snList)).thenReturn(mockVos);

        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("name1");
        HrmPersonInfoDTO hrmPersonInfoDTO2 = new HrmPersonInfoDTO();
        hrmPersonInfoDTO2.setEmpName("name2");
        HrmPersonInfoDTO hrmPersonInfoDTO3 = new HrmPersonInfoDTO();
        hrmPersonInfoDTO3.setEmpName("name3");
        HrmPersonInfoDTO hrmPersonInfoDTO4 = new HrmPersonInfoDTO();
        hrmPersonInfoDTO4.setEmpName("name4");

        Map<String, HrmPersonInfoDTO> createMap = new HashMap<>();
        createMap.put("emp1", hrmPersonInfoDTO);
        createMap.put("emp3", hrmPersonInfoDTO3);

        Map<String, HrmPersonInfoDTO> updateMap = new HashMap<>();
        updateMap.put("emp2", hrmPersonInfoDTO2);
        updateMap.put("emp4", hrmPersonInfoDTO4);

        when(centerfactoryRemote.getHrmPersonInfo(anyList())).thenReturn(createMap).thenReturn(updateMap);

        List<PmMachineSearchWeightVO> result = service.searchWeight(dto);

        assertEquals(2, result.size());
        assertEquals("name1emp1", result.get(0).getCreateBy());
        assertEquals("name2emp2", result.get(0).getLastUpdatedBy());
        assertEquals("name3emp3", result.get(1).getCreateBy());
        assertEquals("name4emp4", result.get(1).getLastUpdatedBy());
    }
    /* Ended by AICoder, pid:jbcc3j1115nfddf1481b088c007ff3972902541b */

    /* Started by AICoder, pid:c6ec43fd0cm39b514e980bb74063298a86c55077 */
    @Test
    public void testBatchSubmit_ValidData() throws Exception {
        /** 测试批量提交有效数据的情况 */
        PmMachineWeightImportDTO dto = new PmMachineWeightImportDTO();
        dto.setSn("sn1");
        dto.setWeight("10.0");
        PmMachineWeightImportDTO dto2 = new PmMachineWeightImportDTO();
        dto2.setSn("sn2");
        dto2.setWeight("20.0");
        List<PmMachineWeightImportDTO> dtos = Arrays.asList(dto,dto2);

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn(null);
        when(repository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());
        when(psWipInfoRepository.getListByBatchSnList(anyList())).thenReturn(Collections.emptyList());
        when(repository.batchInsert(anyList())).thenReturn(2);

        Integer result = service.batchSubmit(dtos);

        assertEquals(2, result.intValue());
    }

    @Test(expected = BusiException.class)
    public void testBatchSubmit_InvalidData() throws Exception {
        /** 测试批量提交无效数据的情况 */
        PmMachineWeightImportDTO dto = new PmMachineWeightImportDTO();
        dto.setSn("sn1");
        dto.setWeight("10.0");
        List<PmMachineWeightImportDTO> dtos = Collections.singletonList(dto);

        when(machineWeightValidateHelper.validateMachineWeight(any())).thenReturn("ValidationError");

        service.batchSubmit(dtos);
    }

    @Test
    public void testSelectAndInsertOrUpdateData_UpdateOnly() {
        /** 测试只有更新的情况 */
        PmMachineWeightDO weightDO = new PmMachineWeightDO();
        weightDO.setSn("sn1");
        weightDO.setWeight("10.0");
        PmMachineWeightDO weightDO2 = new PmMachineWeightDO();
        weightDO2.setSn("sn2");
        weightDO2.setWeight("20.0");
        List<PmMachineWeightDO> passValidateList = Arrays.asList(weightDO,weightDO2);

        PmMachineWeightDO weightDO3 = new PmMachineWeightDO();
        weightDO3.setSn("sn1");
        weightDO3.setWeight("5.0");
        weightDO3.setEnabledFlag(CommonConst.ENABLE_FLAG_N);
        PmMachineWeightDO weightDO4 = new PmMachineWeightDO();
        weightDO4.setSn("sn2");
        weightDO4.setWeight("15.0");
        weightDO4.setEnabledFlag(CommonConst.ENABLE_FLAG_N);

        List<PmMachineWeightDO> existingRecords = Arrays.asList(weightDO3,weightDO4);

        when(repository.selectByPrimaries(anyList())).thenReturn(existingRecords);
        when(repository.batchUpdate(anyList())).thenReturn(2);

        int result = service.selectAndInsertOrUpdateData(passValidateList);

        assertEquals(2, result);
    }

    @Test
    public void testSelectAndInsertOrUpdateData_InsertOnly() {
        /** 测试只有插入的情况 */
        PmMachineWeightDO weightDO = new PmMachineWeightDO();
        weightDO.setSn("sn1");
        weightDO.setWeight("10.0");
        PmMachineWeightDO weightDO2 = new PmMachineWeightDO();
        weightDO2.setSn("sn2");
        weightDO2.setWeight("20.0");
        List<PmMachineWeightDO> passValidateList = Arrays.asList(weightDO,weightDO2);

        when(repository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());
        when(psWipInfoRepository.getListByBatchSnList(anyList())).thenReturn(Collections.emptyList());
        when(repository.batchInsert(anyList())).thenReturn(2);

        int result = service.selectAndInsertOrUpdateData(passValidateList);

        assertEquals(2, result);
    }

    @Test
    public void testSelectAndInsertOrUpdateData_NoOperation() {
        /** 测试没有更新也没有插入的情况 */
        PmMachineWeightDO weightDO = new PmMachineWeightDO();
        weightDO.setSn("sn1");
        weightDO.setWeight("10.0");
        PmMachineWeightDO weightDO2 = new PmMachineWeightDO();
        weightDO2.setSn("sn2");
        weightDO2.setWeight("20.0");
        List<PmMachineWeightDO> passValidateList = Arrays.asList(weightDO,weightDO2);

        PmMachineWeightDO weightDO3 = new PmMachineWeightDO();
        weightDO3.setSn("sn1");
        weightDO3.setWeight("5.0");
        weightDO3.setEnabledFlag(CommonConst.ENABLE_FLAG_N);
        PmMachineWeightDO weightDO4 = new PmMachineWeightDO();
        weightDO4.setSn("sn2");
        weightDO4.setWeight("15.0");
        weightDO4.setEnabledFlag(CommonConst.ENABLE_FLAG_N);

        List<PmMachineWeightDO> existingRecords = Arrays.asList(weightDO3,weightDO4);

        when(repository.selectByPrimaries(anyList())).thenReturn(existingRecords);

        int result = service.selectAndInsertOrUpdateData(passValidateList);

        assertEquals(0, result);
    }
    /* Ended by AICoder, pid:c6ec43fd0cm39b514e980bb74063298a86c55077 */

    /* Started by AICoder, pid:9c7d06436eg500314c790a8e107cd41aa217e0e3 */
    @Test
    public void testExportTemplate_Success() throws Exception {
        /**
         * 测试导出模板成功的情况
         */
        when(response.getOutputStream()).thenReturn(outputStream);

        service.exportTemplate(response);

        verify(response).setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-Disposition"), anyString());

        assertNotNull(outputStream);
    }
    /* Ended by AICoder, pid:9c7d06436eg500314c790a8e107cd41aa217e0e3 */
}
