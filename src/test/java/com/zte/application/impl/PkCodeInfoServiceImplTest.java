package com.zte.application.impl;

import com.zte.application.BSmtBomDetailService;
import com.zte.application.StItemBarcodeService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({PlanscheduleRemoteService.class,BasicsettingRemoteService.class})
public class PkCodeInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private PkCodeInfoServiceImpl pkCodeInfoServiceImpl;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;
	@Mock
	private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
	@Mock
	private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
	@Mock
	private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private StItemBarcodeService stItemBarcodeService;
	@Mock
	private IscpRemoteService iscpRemoteService;
	@Mock
	private DatawbRemoteService datawbRemoteService;

    @Test
    public void insertOrUpdate() throws Exception {
        PkCodeInfo info = new PkCodeInfo();
        info.setPkCode("12");
        PowerMockito.when(pkCodeInfoServiceImpl.selectPkCodeInfoById(Mockito.any())).thenReturn(info);
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(info);
        PowerMockito.when(pkCodeInfoServiceImpl.updatePkCodeInfoByIdSelective(Mockito.any())).thenReturn(1);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pkCodeHistoryRepository).insertPkCodeHistory(Mockito.any());
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setBomItemQty(new BigDecimal(52));
		Assert.assertNotNull(pkCodeInfoServiceImpl.insertOrUpdate(info));
    }

    @Test
    public void lateReelIdBindToLocal() throws Exception {
        PkCodeInfo info = new PkCodeInfo();
        info.setPkCode("12");
        PowerMockito.when(pkCodeInfoServiceImpl.selectPkCodeInfoById(Mockito.any())).thenReturn(info);
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(info);
        PowerMockito.when(pkCodeInfoServiceImpl.updatePkCodeInfoByIdSelective(Mockito.any())).thenReturn(1);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pkCodeHistoryRepository).insertPkCodeHistory(Mockito.any());
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setBomItemQty(new BigDecimal(52));
		Assert.assertNotNull(pkCodeInfoServiceImpl.lateReelIdBindToLocal(info));
    }


    @Test
    public void splitReelId() throws Exception {
        try {
            pkCodeInfoServiceImpl.splitReelId(null);
        } catch (Exception e) {
			Assert.assertEquals(MessageId.PARAMS_VALIDATE_ERROR, e.getMessage());
        }

        PowerMockito.when(pkCodeInfoRepository.updatePkCodeInfoByIdSelective(Mockito.any())).thenReturn(1);
        PowerMockito.when(pkCodeInfoRepository.insertPkCodeInfoSelective(Mockito.any())).thenReturn(1);
        SplitReelIdDTO dto = new SplitReelIdDTO();
        dto.setNewPkInfo(new PkCodeInfoDTO());
        dto.setOldPkInfo(new PkCodeInfoDTO());
        pkCodeInfoServiceImpl.splitReelId(dto);

        PkCodeInfo pkInfo = new PkCodeInfo();
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(pkInfo);
		Assert.assertNotNull(pkCodeInfoServiceImpl.splitReelId(dto));
    }

	@Test
	public void getStationByMaterialTry() throws Exception {
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setProductTask("test");
		pkCodeInfo.setItemCode("test");
		pkCodeInfo.setItemQty(new BigDecimal("1"));
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(pkCodeInfo);

		List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
		SmtMachineMaterialPrepare smtMachineMaterialPrepareDTO = new SmtMachineMaterialPrepare();
		smtMachineMaterialPrepareDTO.setObjectId("test");
		smtMachineMaterialPrepareDTO.setItemCode("test");
		smtMachineMaterialPrepareDTO.setLocationNo("2-39-2");
		smtMachineMaterialPrepareDTO.setMachineNo("(X4S-2)");
		smtMachineMaterialPrepareDTO.setLineCode("SMT-HY005");
		prepareList.add(smtMachineMaterialPrepareDTO);
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(prepareList);
		List<BSmtBomDetail> detailList = new ArrayList<>();
		BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
		bSmtBomDetail.setMaterialRack("2 x 8mm X");
		detailList.add(bSmtBomDetail);
		PowerMockito.when(bSmtBomDetailService.getBomDetailByCondition(Mockito.any())).thenReturn(detailList);
		Assert.assertNotNull(pkCodeInfoServiceImpl.getStationByMaterialTry(record));
	}

	@Test
	public void getStationByMaterialTryTwo() throws Exception {
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(1);
		try{
			pkCodeInfoServiceImpl.getStationByMaterialTry(record);
		}catch(Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.EXIST_SMT_MATERIAL_MOUTING_INFO);
		}
	}

    @Test
    public void getListWithoutAttrInfo() throws Exception {
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        Assert.assertNotNull(pkCodeInfoServiceImpl.getListWithoutAttrInfo(dto));
        dto.setPkCode("test");
        List<PkCodeInfo> list = new ArrayList<>();
        PowerMockito.when(pkCodeInfoRepository.getList(Mockito.any())).thenReturn(list);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(list)).thenReturn(list);
        Assert.assertNotNull(pkCodeInfoServiceImpl.getListWithoutAttrInfo(dto));
    }

	@Test
	public void getStationByMaterialTryThree() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PkCodeInfo record1 = new PkCodeInfo();
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(null);
		try{
			pkCodeInfoServiceImpl.getStationByMaterialTry(record);
		}catch(Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_INFO_IS_EMPTY);
		}
	}

	@Test
	public void getStationByMaterialTryFour() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setProductTask("test");
		pkCodeInfo.setItemCode("test");
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(pkCodeInfo);

		List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(prepareList);
		List<PsTaskBasicDTO> taskInfo = new ArrayList<>();
		PsTaskBasicDTO psTaskBasicDTO = new PsTaskBasicDTO();
		psTaskBasicDTO.setLineCode("SMT-HY005");
		psTaskBasicDTO.setCraftSection("SMT-A");
		taskInfo.add(psTaskBasicDTO);
		PowerMockito.when(PlanscheduleRemoteService.getLineCodeInfoBySourceTask(Mockito.any())).thenReturn(taskInfo);
		List<BSmtBomDetail> detailList = new ArrayList<>();
		BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
		bSmtBomDetail.setMaterialRack("2 x 8mm X");
		bSmtBomDetail.setLocationNo("2-39-2");
		bSmtBomDetail.setMachineNo("(X4S-2)");
		detailList.add(bSmtBomDetail);
		PowerMockito.when(bSmtBomDetailService.getBomDetailByCondition(Mockito.any())).thenReturn(detailList);
		pkCodeInfoServiceImpl.getStationByMaterialTry(record);

		try {
			List<PsTaskBasicDTO> taskInfoTest = new ArrayList<>();
			PowerMockito.when(PlanscheduleRemoteService.getLineCodeInfoBySourceTask(Mockito.any())).thenReturn(taskInfoTest);
			pkCodeInfoServiceImpl.getStationByMaterialTry(record);
		}catch (Exception e){
			Assert.assertEquals(MessageId.PK_CODE_INFO_IS_EMPTY, e.getMessage());
		}
	}


	@Test
	public void getStationByMaterialTryFive() throws Exception {
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setProductTask("test");
		pkCodeInfo.setItemCode("test");
		pkCodeInfo.setItemQty(new BigDecimal("1"));
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(pkCodeInfo);

		List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
		SmtMachineMaterialPrepare smtMachineMaterialPrepareDTO = new SmtMachineMaterialPrepare();
		smtMachineMaterialPrepareDTO.setObjectId("test");
		prepareList.add(smtMachineMaterialPrepareDTO);
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(prepareList);
		List<BSmtBomDetail> detailList = new ArrayList<>();
		PowerMockito.when(bSmtBomDetailService.getBomDetailByCondition(Mockito.any())).thenReturn(detailList);

		Assert.assertNotNull(pkCodeInfoServiceImpl.getStationByMaterialTry(record));
	}

	@Test
	public void getStationByMaterialTrySix() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setProductTask("test");
		pkCodeInfo.setItemCode("test");
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(pkCodeInfo);

		List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(prepareList);
		List<PsTaskBasicDTO> taskInfo = new ArrayList<>();
		PsTaskBasicDTO psTaskBasicDTO = new PsTaskBasicDTO();
		psTaskBasicDTO.setLineCode("SMT-HY005");
		psTaskBasicDTO.setCraftSection("SMT-A");
		taskInfo.add(psTaskBasicDTO);
		PowerMockito.when(PlanscheduleRemoteService.getLineCodeInfoBySourceTask(Mockito.any())).thenReturn(taskInfo);
		List<BSmtBomDetail> detailList = new ArrayList<>();
		PowerMockito.when(bSmtBomDetailService.getBomDetailByCondition(Mockito.any())).thenReturn(detailList);
		Assert.assertNotNull(pkCodeInfoServiceImpl.getStationByMaterialTry(record));
	}

	@Test
	public void getStationByMaterialTrySeven() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PkCodeInfo record = new PkCodeInfo();
		record.setPkCode("test");
		PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(Mockito.any())).thenReturn(0);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setItemCode("test");
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfo(Mockito.any())).thenReturn(pkCodeInfo);

		List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(prepareList);
		Assert.assertNotNull(pkCodeInfoServiceImpl.getStationByMaterialTry(record));
	}

    @Test
    public void getItemDirectionByPkCode() {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(null);
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.REELID_NOT_EXISTS);
		}
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(pkCodeInfo);
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("");
		pkCodeInfo.setSysLotCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setSupplerCode("test");
		List<PolarItemInfoDTO> polarItemInfoDTOS = new ArrayList<>();
		PowerMockito.when(BasicsettingRemoteService.getPolarItemInfoList(Mockito.any())).thenReturn(polarItemInfoDTOS);
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		PolarItemInfoDTO polarItemInfoDTO = new PolarItemInfoDTO();
		polarItemInfoDTO.setItemAngle("10");
		polarItemInfoDTOS.add(polarItemInfoDTO);
		try{
			pkCodeInfoServiceImpl.getItemDirectionByPkCode(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
    }

    @Test
    public void getItemDirection() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(null);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.REELID_NOT_EXISTS);
		}
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(pkCodeInfo);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setSupplerCode("test");
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("");
		pkCodeInfo.setSysLotCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		List<PolarItemInfoDTO> polarItemInfoDTOS = new ArrayList<>();
		PowerMockito.when(BasicsettingRemoteService.getPolarItemInfoList(Mockito.any())).thenReturn(polarItemInfoDTOS);
		List<ItemSplitInfoDTO> newItemSplitList =new ArrayList<>();
		PowerMockito.when(iscpRemoteService.getItemSplitInfo(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn(newItemSplitList);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		ItemSplitInfoDTO itemSplitInfoDTO = new ItemSplitInfoDTO();
		newItemSplitList.add(itemSplitInfoDTO);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		itemSplitInfoDTO.setBraidDirection("10");
		List<StCodeInfoDTO> stCodeInfoList = new ArrayList<>();
		PowerMockito.when(datawbRemoteService.getStCodeInfoList()).thenReturn(stCodeInfoList);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		StCodeInfoDTO stCodeInfoDTO = new StCodeInfoDTO();
		stCodeInfoDTO.setCode("10");
		stCodeInfoDTO.setCodeDesc("");
		stCodeInfoList.add(stCodeInfoDTO);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		stCodeInfoDTO.setCodeDesc("10度");
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		PolarItemInfoDTO polarItemInfoDTO = new PolarItemInfoDTO();
		polarItemInfoDTO.setItemAngle("10");
		polarItemInfoDTOS.add(polarItemInfoDTO);
		try{
			pkCodeInfoServiceImpl.getItemDirection(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
	}

    @Test
    public void updateOrInsertItemAngle() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(null);
		try{
			pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.REELID_NOT_EXISTS);
		}
		PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(Mockito.any())).thenReturn(pkCodeInfo);
		try{
			pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("");
		pkCodeInfo.setSysLotCode("test");
		try{
			pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setItemCode("test");
		try{
			pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.PK_CODE_PARAM_EMPTY);
		}
		pkCodeInfo.setSupplerCode("test");
		pkCodeInfoServiceImpl.updateOrInsertItemAngle(pkCodeInfo);
    }
}