package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, ExcelCommonUtils.class, CrafttechRemoteService.class,
		PlanscheduleRemoteService.class, BasicsettingRemoteService.class,RequestHeaderUtil.class,
		RedisLock.class,RedisHelper.class})
public class DailyWorkAmountStatServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private DailyWorkAmountStatServiceImpl service;

	@Mock
	private PsWipInfoService psWipInfoService;

	@Mock
	private DailyWorkAmountStatRepository dailyWorkAmountStatRepository;

	@Mock
	HttpServletRequest request;
	@Mock
	HttpServletResponse response;

	@Mock
	CenterfactoryRemoteService centerfactoryRemoteService;

	@Mock
	PsScanHistoryRepository psScanHistoryRepository;

	@Mock
	BSmtBomHeaderRepository bSmtBomHeaderRepository;

	@Mock
	private DatawbRemoteService datawbRemoteService;

	@Mock
	private RedisLock redisLock;

	@Test
	public void testPageList() throws Exception {
		PowerMockito.mockStatic(CrafttechRemoteService.class);
		DailyWorkAmountStatEntityDTO record = new DailyWorkAmountStatEntityDTO();
		record.setCreateBy("test");
		record.setLineCode("SMT7");
		record.setProcessCode("6");
		record.setProcessName("短插");
		record.setStatDate(new Date());
		record.setStartTimeStr("2022-06-27 00:00:00");
		record.setEndTimeStr("2022-07-20 23:59:59");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		record.setStatDate(sdf.parse("2022-07-22"));
		List<DailyWorkAmountStatEntityDTO> dailyInfoList = new ArrayList<>();
		dailyInfoList.add(record);
		PowerMockito.when(dailyWorkAmountStatRepository.pageList(Mockito.anyObject())).thenReturn(dailyInfoList);
		List<CFLine> lineInfoAll = new ArrayList<>();
		CFLine cfLine = new CFLine();
		cfLine.setLineName("SMT7名称");
		cfLine.setLineCode("SMT7");
		lineInfoAll.add(cfLine);
		PowerMockito.when(psWipInfoService.getLineInfo(Mockito.anyObject())).thenReturn(lineInfoAll);
		List<BSProcess> queryList = new ArrayList<>();
		BSProcess process = new BSProcess();
		process.setProcessCode("6");
		process.setProcessName("短插");
		PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.anyObject())).thenReturn(queryList);
		Assert.assertNotNull(service.pageList(record));
	}


	@Test
	public void testSetMbom() throws Exception {
		List<DailyWorkAmountStatEntityDTO> dailyWorkAmountStatlist = new ArrayList<>();
		DailyWorkAmountStatEntityDTO dailyWorkAmountStatEntityDTO = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatEntityDTO.setProdplanId("1234567");
		dailyWorkAmountStatlist.add(dailyWorkAmountStatEntityDTO);
		List<BProdBomChangeDetailDTO> bProdBomChangeDetail = new ArrayList<>();
		BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
		bProdBomChangeDetailDTO.setProdplanId("1234567");
		bProdBomChangeDetailDTO.setProductCode("139571751152ZTA");
		bProdBomChangeDetailDTO.setLastUpdatedBy("ZXV10 B860AV2D1PR STBAB");
		bProdBomChangeDetail.add(bProdBomChangeDetailDTO);
		PowerMockito.when(centerfactoryRemoteService.getBProdBomChangeDetail(Mockito.any())).thenReturn(new ArrayList<>());
		service.setMbom(dailyWorkAmountStatlist);
		PowerMockito.when(centerfactoryRemoteService.getBProdBomChangeDetail(Mockito.any())).thenReturn(bProdBomChangeDetail);
		List<DailyWorkAmountStatEntityDTO> dailyWorkAmountStatEntity = service.setMbom(dailyWorkAmountStatlist);
		Assert.assertEquals(dailyWorkAmountStatEntity.get(0).getmBomProductCode(), "139571751152ZTA");
	}

	@Test
	public void testExportWorkLoadInfoBatch() throws Exception {
		PowerMockito.mockStatic(ExcelCommonUtils.class);
		String[] headList = new String[]{"日期","线体","班组","车间","子工序","批次","料单代码","料单名称", "版本", "生产人力", "作业数量",
				"产品工时","产出工时", "产品大类", "产品小类"};
		String[] props = new String[]{"transformStat", "lineName", "groupName", "workshopName", "processName",
				"prodplanId", "itemNo","itemName", "verNo", "prodMan", "workAmount",
				"prodHour", "outputHour","externalType", "internalType"};
		String fileName = "工作量信息.xlsx";
		DailyWorkAmountStatEntityDTO record = new DailyWorkAmountStatEntityDTO();
		record.setCreateBy("test");
		record.setStartTimeStr("2022-06-27 00:00:00");
		record.setEndTimeStr("2022-07-20 23:59:59");
		record.setStatDate(new Date());
		List<DailyWorkAmountStatEntityDTO> dailyInfoList = new ArrayList<>();
		dailyInfoList.add(record);
		PowerMockito.when(dailyWorkAmountStatRepository.pageList(Mockito.anyObject())).thenReturn(dailyInfoList);

		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<CfGroupDTO> cfGroupDTOS = new ArrayList<>();
		PowerMockito.when(BasicsettingRemoteService.getWorkGroupBatch(any())).thenReturn(cfGroupDTOS);
		service.exportWorkLoadInfoBatch(request,response,record);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void regularStatisticsOfWorkload() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<PsScanHistory> hisList =new ArrayList<>();
		PsScanHistory psScanHistory =new PsScanHistory();
		psScanHistory.setLineCode("test123");
		psScanHistory.setAttribute1("test123");
		psScanHistory.setCurrProcessCode("test123");
		psScanHistory.setItemNo("test123");
		psScanHistory.setCnt("2");
		hisList.add(psScanHistory);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask =new PsTask();
		psTask.setProdplanId("test123");
		psTask.setItemNo("test123");
		psTask.setItemName("test123");
		psTask.setExternalType("test123");
		psTask.setInternalType("test123");
		psTask.setExternalType("test123");
		psTask.setSourceSys("STEP");
		psTaskList.add(psTask);

		Map<String,String> map =new HashMap<>();
		map.put("test123","test123");

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine=new CFLine();
		cfLine.setLineCode("test123");
		cfLine.setGroupCode("test123");
		cfLine.setGroupName("test123");
		cfLine.setWorkshopCode("test123");
		cfLine.setWorkshopName("test123");
		cfLines.add(cfLine);

		List<BManufactureCapacityDTO> capacityList = new ArrayList<>();
		BManufactureCapacityDTO bManufactureCapacityDTO=new BManufactureCapacityDTO();
		bManufactureCapacityDTO.setProdMan("test123");
		bManufactureCapacityDTO.setProdHour("1");
		bManufactureCapacityDTO.setLineCode("test123");
		bManufactureCapacityDTO.setItemCode("test123");
		bManufactureCapacityDTO.setProcessCode("test123");
		capacityList.add(bManufactureCapacityDTO);


		PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("52");
		PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO)).thenReturn("00286523");
		PowerMockito.when(psScanHistoryRepository.getWorkLoadWithSchedule(Mockito.anyMap())).thenReturn(hisList);
		PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPost(Mockito.anyMap())).thenReturn(psTaskList);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		PowerMockito.when(BasicsettingRemoteService.getBManufactureCapacityInfoByPost(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyString())).thenReturn(capacityList);
		PowerMockito.when(BasicsettingRemoteService.getVerNoInfoByItemNos(Mockito.anySet())).thenReturn(map);
		PowerMockito.when(dailyWorkAmountStatRepository.insertWorkLoadBatch(Mockito.any())).thenReturn(1);

		service.regularStatisticsOfWorkload(request);

		PowerMockito.when(psScanHistoryRepository.getWorkLoadWithSchedule(Mockito.anyMap())).thenReturn(null);
		service.regularStatisticsOfWorkload(request);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);

	}

	@Test
	public void initializationWorkload() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		Calendar calendar = Calendar.getInstance();
		Date start = calendar.getTime();
		List<PsScanHistory> hisList =new ArrayList<>();
		PsScanHistory psScanHistory =new PsScanHistory();
		psScanHistory.setLineCode("test123");
		psScanHistory.setAttribute1("test123");
		psScanHistory.setCurrProcessCode("test123");
		psScanHistory.setItemNo("test123");
		psScanHistory.setCnt("2");
		hisList.add(psScanHistory);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask =new PsTask();
		psTask.setProdplanId("test123");
		psTask.setItemNo("test123");
		psTask.setItemName("test123");
		psTask.setExternalType("test123");
		psTask.setInternalType("test123");
		psTask.setExternalType("test123");
		psTask.setSourceSys("STEP");
		psTaskList.add(psTask);

		Map<String,String> map =new HashMap<>();
		map.put("test123","test123");

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine=new CFLine();
		cfLine.setLineCode("test123");
		cfLine.setGroupCode("test123");
		cfLine.setGroupName("test123");
		cfLine.setWorkshopCode("test123");
		cfLine.setWorkshopName("test123");
		cfLines.add(cfLine);

		List<BManufactureCapacityDTO> capacityList = new ArrayList<>();
		BManufactureCapacityDTO bManufactureCapacityDTO=new BManufactureCapacityDTO();
		bManufactureCapacityDTO.setProdMan("test123");
		bManufactureCapacityDTO.setProdHour("1");
		bManufactureCapacityDTO.setLineCode("test123");
		bManufactureCapacityDTO.setItemCode("test123");
		bManufactureCapacityDTO.setProcessCode("test123");
		capacityList.add(bManufactureCapacityDTO);


		PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("52");
		PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_EMP_NO)).thenReturn("00286523");
		PowerMockito.when(psScanHistoryRepository.getWorkLoadWithSchedule(Mockito.anyMap())).thenReturn(hisList);
		PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPost(Mockito.anyMap())).thenReturn(psTaskList);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		PowerMockito.when(BasicsettingRemoteService.getBManufactureCapacityInfoByPost(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyString())).thenReturn(capacityList);
		PowerMockito.when(BasicsettingRemoteService.getVerNoInfoByItemNos(Mockito.anySet())).thenReturn(map);
		PowerMockito.when(dailyWorkAmountStatRepository.insertWorkLoadBatch(Mockito.any())).thenReturn(1);

		service.initializationWorkload(start,start,request );
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}


	@Test(expected = Exception.class)
	public void setGroupInfo() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.when(BasicsettingRemoteService.getWorkGroupBatch(any())).thenThrow(new Exception());

		List<DailyWorkAmountStatEntityDTO> dailyWorkAmountStatlist = new ArrayList<>();
		Whitebox.invokeMethod(service, "setGroupInfo", dailyWorkAmountStatlist);

		DailyWorkAmountStatEntityDTO dto = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto);
		Whitebox.invokeMethod(service, "setGroupInfo", dailyWorkAmountStatlist);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}



	@Test
	public void setGroupInfo1() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);

		List<DailyWorkAmountStatEntityDTO> dailyWorkAmountStatlist = new ArrayList<>();

		DailyWorkAmountStatEntityDTO dto = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto);

		List<CfGroupDTO> cfGroupDTOS = new ArrayList<>();
		PowerMockito.when(BasicsettingRemoteService.getWorkGroupBatch(any())).thenReturn(cfGroupDTOS);

		dto.setGroupCode("1111");
		Whitebox.invokeMethod(service, "setGroupInfo", dailyWorkAmountStatlist);

		DailyWorkAmountStatEntityDTO dto1 = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto1);
		dto1.setGroupCode("2222");
		DailyWorkAmountStatEntityDTO dto2 = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto2);

		CfGroupDTO cfGroupDTO = new CfGroupDTO();
		cfGroupDTOS.add(cfGroupDTO);
		cfGroupDTO.setGroupCode("2222");
		Whitebox.invokeMethod(service, "setGroupInfo", dailyWorkAmountStatlist);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void setTaskQty() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);

		List<DailyWorkAmountStatEntityDTO> dailyWorkAmountStatlist = new ArrayList<>();
		Whitebox.invokeMethod(service, "setTaskQty", dailyWorkAmountStatlist);

		DailyWorkAmountStatEntityDTO dto = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto);
		Whitebox.invokeMethod(service, "setTaskQty", dailyWorkAmountStatlist);

		dto.setProdplanId("1111");
		DailyWorkAmountStatEntityDTO dto1 = new DailyWorkAmountStatEntityDTO();
		dailyWorkAmountStatlist.add(dto1);
		dto1.setProdplanId("2222");

		Whitebox.invokeMethod(service, "setTaskQty", dailyWorkAmountStatlist);

		List<PsTask> taskBatch = new ArrayList<>();
		PsTask task = new PsTask();
		task.setProdplanId("1111");
		task.setTaskQty(BigDecimal.TEN);
		taskBatch.add(task);
		PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPost(any())).thenReturn(taskBatch);
		Whitebox.invokeMethod(service, "setTaskQty", dailyWorkAmountStatlist);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void reStatisticsOfWorkload() throws Exception {
		PowerMockito.mockStatic(RedisLock.class);
		PowerMockito.mockStatic(RedisHelper.class);
		DailyWorkAmountStatEntityDTO dto = new DailyWorkAmountStatEntityDTO();
		try {
			service.reStatisticsOfWorkload(dto);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.NEED_ONE_DAY_IN_THE_PAST, e.getMessage());
		}

		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		String str = "2099-01-01";
		Date date = df.parse(str);
		dto.setStatDate(date);
		try {
			service.reStatisticsOfWorkload(dto);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.NEED_ONE_DAY_IN_THE_PAST, e.getMessage());
		}

		Date date1 = df.parse("2022-01-01");
		dto.setStatDate(date1);
		PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
		PowerMockito.when(redisLock.lock()).thenReturn(true);
		PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
		PowerMockito.when(dailyWorkAmountStatRepository.deleteByStatDate(any())).thenReturn(1);
		try {
			service.reStatisticsOfWorkload(dto);
		} catch (Exception e) {
		}

		PowerMockito.when(redisLock.lock()).thenReturn(false);
		PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
		service.reStatisticsOfWorkload(dto);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}


	@Test
	public void transToMes() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		Whitebox.invokeMethod(service, "transToMes", null);

		List<SysLookupValuesDTO> valueByTypeCodes = new LinkedList<>();
		SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
		sysLookupValuesDTO.setLookupCode(Constant.DAILY_STAT_TRANS_MES);
		sysLookupValuesDTO.setLookupMeaning("200");
		valueByTypeCodes.add(sysLookupValuesDTO);
		PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.DAILY_STAT_SETTING))
				.thenReturn(valueByTypeCodes)
		;

		Whitebox.invokeMethod(service, "transToMes", null);

		sysLookupValuesDTO.setLookupMeaning("Y");
		Whitebox.invokeMethod(service, "transToMes", null);

		PowerMockito.when(datawbRemoteService.insertWmesMachineOnlineInfo(any(), anyBoolean())).thenReturn(1);

		Date now = new Date();
		List<DailyWorkAmountStatEntityDTO> dtos = new ArrayList<>();
		DailyWorkAmountStatEntityDTO dto1 = new DailyWorkAmountStatEntityDTO();
		dtos.add(dto1);
		dtos.add(dto1);
		dto1.setStatDate(now);
		dto1.setDepartment("Department");
		dto1.setWorkshopName("WorkshopName");
		dto1.setSixLevel("SixLevel");
		dto1.setGroupName("GroupName");
		dto1.setProdHour("22");

		DailyWorkAmountStatEntityDTO dto2 = new DailyWorkAmountStatEntityDTO();
		dtos.add(dto2);
		dto2.setStatDate(now);
		dto2.setDepartment("Department");
		dto2.setWorkshopName("WorkshopName");
		dto2.setSixLevel("SixLevel");
		dto2.setGroupName("GroupName");
		dto2.setProdHour("aa");

		DailyWorkAmountStatEntityDTO dto3 = new DailyWorkAmountStatEntityDTO();
		dtos.add(dto3);
		dto3.setStatDate(now);
		dto3.setDepartment("Department11");
		dto3.setWorkshopName("WorkshopName22");
		dto3.setSixLevel("SixLevel");
		dto3.setGroupName("GroupName");
		dto3.setProdHour("22");

		Whitebox.invokeMethod(service, "transToMes", dtos);

		List<DailyWorkAmountStatEntityDTO> dtos1200 = new ArrayList<>();
		for (int i = 0; i < 400; i++) {
			dtos1200.addAll(dtos);
		}
		Whitebox.invokeMethod(service, "transToMes", dtos1200);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void sendMail() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		Whitebox.invokeMethod(service, "sendMail", "111");

		List<SysLookupTypesDTO> valueByTypeCodes = new LinkedList<>();
		SysLookupTypesDTO sysLookupValuesDTO = new SysLookupTypesDTO();

		sysLookupValuesDTO.setLookupMeaning("111111;22222");
		valueByTypeCodes.add(sysLookupValuesDTO);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_1004095))
				.thenReturn(valueByTypeCodes)
		;

		Whitebox.invokeMethod(service, "sendMail", "111");

		sysLookupValuesDTO.setAttribute1(Constant.EMAIL);
		Whitebox.invokeMethod(service, "sendMail", "111");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}
}