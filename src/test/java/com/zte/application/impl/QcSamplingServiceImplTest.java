package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PsWipInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWipInfoSimpleEntiy;
import com.zte.domain.model.QcSamplingDetail;
import com.zte.domain.model.QcSamplingDetailRepository;
import com.zte.domain.model.QcSamplingHead;
import com.zte.domain.model.QcSamplingHeadRepository;
import com.zte.domain.model.QcSamplingDetailResult;
import com.zte.domain.model.QcSamplingDetailResultRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PcProcessTransferSimpleDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.QcSamplingDTO;
import com.zte.interfaces.dto.QcSamplingDetailDTO;
import com.zte.interfaces.dto.QcSamplingDetailSnCheckDTO;
import com.zte.interfaces.dto.QcSamplingDetailResultDTO;
import com.zte.interfaces.dto.ContainerContentInfoSnQueryDTO;
import com.zte.interfaces.dto.QcSamplingHeadDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.PcProcessTransferComDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

@PrepareForTest({PlanscheduleRemoteService.class, ProductionmgmtRemoteService.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class,CrafttechRemoteService.class})
public class QcSamplingServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    QcSamplingServiceImpl service;

    @Mock
    PsWipInfoRepository psWipInfoRepository;
    @Mock
    PsWipInfoService psWipInfoService;
    @Mock
    QcSamplingHeadRepository qcSamplingHeadRepository;
    @Mock
    QcSamplingDetailRepository qcSamplingDetailRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemote;

    @Mock
    QcSamplingDetailResultRepository qcSamplingDetailResultRepository;

    @Mock
    PlanscheduleRemoteService planscheduleRemoteService;

    @Test
    public void getHeadPage() {
        QcSamplingHeadDTO dto = new QcSamplingHeadDTO();
        List<QcSamplingHead> listRes = new ArrayList<>();
        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        listRes.add(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.getPageList(Mockito.any())).thenReturn(listRes);
        Page<QcSamplingHeadDTO> page = service.getHeadPage(dto);
        Assert.assertEquals(1, page.getRows().size());
    }

    @Test
    public void getDetailPage() {
        QcSamplingDetailDTO dto = new QcSamplingDetailDTO();
        List<QcSamplingDetail> listRes = new ArrayList<>();
        QcSamplingDetail qcSamplingHead = new QcSamplingDetail();
        listRes.add(qcSamplingHead);
        PowerMockito.when(qcSamplingDetailRepository.getPageList(Mockito.any())).thenReturn(listRes);
        Page<QcSamplingDetailDTO> page = service.getDetailPage(dto);
        Assert.assertEquals(1, page.getRows().size());
    }

    @Test
    public void deleteById() {
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(null);
        int res = service.deleteById("11", "222");
        Assert.assertEquals(0, res);

        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        int res1 = service.deleteById("11", "222");
        Assert.assertEquals(1, res1);
    }

    @Test
    public void abandon() {
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(null);
        int res = service.abandon("11", "222");
        Assert.assertEquals(0, res);

        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        int res1 = service.abandon("11", "222");
        Assert.assertEquals(1, res1);
    }


    @Test
    public void submit() throws Exception {
        QcSamplingDTO dto = new QcSamplingDTO();
        try {
            int res1 = service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.QC_SAMPLING_NOT_FOUND, e.getExMsgId());
        }

        QcSamplingHeadDTO head = new QcSamplingHeadDTO();
        dto.setHead(head);
        head.setSamplingDi("1");
        try {
            int res1 = service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.QC_SAMPLING_DETAIL_NOT_FOUND, e.getExMsgId());
        }

        List<String> sns = new ArrayList<>();
        sns.add("1111111111111");
        dto.setSns(sns);
        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(new ArrayList<>());
        PsWipInfo psWipInfoBySn = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(any())).thenReturn(psWipInfoBySn);
        PowerMockito.when(qcSamplingHeadRepository.insertSelective(any())).thenReturn(1);
        int res1 = service.submit(dto);
        Assert.assertEquals(1, res1);

        head.setSamplingDi("2");
        head.setTotalQty(3L);

        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        List<ContainerContentInfoDTO> ccl = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setEntityIdentification("111");
        ccl.add(containerContentInfoDTO);
        ContainerContentInfoDTO containerContentInfoDTO1 = new ContainerContentInfoDTO();
        containerContentInfoDTO1.setEntityIdentification("222");
        ccl.add(containerContentInfoDTO1);
        PowerMockito.when(ProductionmgmtRemoteService.queryContentInfo(any(), any())).thenReturn(ccl);

        List<PsWipInfo> wipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        wipInfos.add(psWipInfo);
        psWipInfo.setWorkOrderNo("WorkOrderNo");
        psWipInfo.setCurrProcessCode("CurrProcessCode");
        psWipInfo.setWorkStation("WorkStation");

        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos);

        try {
            res1 = service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.BOX_CONTENTS_HAS_CHANGED, e.getExMsgId());
        }

        head.setTotalQty(2L);
        res1 = service.submit(dto);
        Assert.assertEquals(1, res1);


        head.setSamplingDi("3");
        try {
            res1 = service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getExMsgId());
        }

        head.setWorkOrderNo("WorkOrderNo");
        PcProcessTransferSimpleDTO pcProcessTransferSimpleDTO = new PcProcessTransferSimpleDTO();
        List<PsWipInfoSimpleEntiy> wipList = new ArrayList<>();
        pcProcessTransferSimpleDTO.setWipList(wipList);
        PowerMockito.when(psWipInfoService.getPCProcessTransferPage(any())).thenReturn(pcProcessTransferSimpleDTO);
        try {
            res1 = service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_BALANCE_HAS_CHANGED, e.getExMsgId());
        }

        PsWipInfoSimpleEntiy psWipInfoSimpleEntiy = new PsWipInfoSimpleEntiy();
        psWipInfoSimpleEntiy.setSn("111111111");
        wipList.add(psWipInfoSimpleEntiy);
        PsWipInfoSimpleEntiy psWipInfoSimpleEntiy1 = new PsWipInfoSimpleEntiy();
        psWipInfoSimpleEntiy1.setSn("111111111");
        wipList.add(psWipInfoSimpleEntiy1);

        PcProcessTransferSimpleDTO pcProcessTransferSimpleDTO1 = new PcProcessTransferSimpleDTO();
        List<PsWipInfoSimpleEntiy> wipList1 = new ArrayList<>();
        pcProcessTransferSimpleDTO1.setWipList(wipList1);
        PowerMockito.when(psWipInfoService.getPCProcessTransferPage(any())).thenReturn(pcProcessTransferSimpleDTO)
                .thenReturn(pcProcessTransferSimpleDTO1);
        res1 = service.submit(dto);
        Assert.assertEquals(1, res1);

    }

    @Test
    public void getBySn() {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setSn("1111");
        dto.setWorkOrderNo("WorkOrderNo1");
        dto.setCurrProcessCode("CurrProcessCode1");
        dto.setWorkStation("WorkStation1");
        PowerMockito.when(psWipInfoService.getWipInfoBySn("1111")).thenReturn(null);
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SNS_IS_NOT_EXITS, e.getExMsgId());
        }

        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setWorkOrderNo("WorkOrderNo");
        wipInfoBySn.setCurrProcessCode("CurrProcessCode");
        wipInfoBySn.setWorkStation("WorkStation");
        PowerMockito.when(psWipInfoService.getWipInfoBySn("1111")).thenReturn(wipInfoBySn);
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_SAME, e.getExMsgId());
        }

        dto.setWorkOrderNo("WorkOrderNo");
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESSCODE_NOT_SAME, e.getExMsgId());
        }

        dto.setCurrProcessCode("CurrProcessCode");
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKSTATION_NOT_SAME, e.getExMsgId());
        }

        dto.setWorkStation("WorkStation");
        List<QcSamplingHead> samplingRecords = new ArrayList<>();
        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        samplingRecords.add(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.getSamplingRecord(anyList(), any())).thenReturn(samplingRecords);
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_EXISTS_ON_OTHER_QC, e.getExMsgId());
        }

        PowerMockito.when(qcSamplingHeadRepository.getSamplingRecord(anyList(), any())).thenReturn(null);
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(anyList()))
                .thenReturn(Lists.newArrayList(new ContainerContentInfoDTO() {{
                            setAttribute3("Warehouse");
                            setEntityIdentification("222");
                        }})
                );
        try {
            PsWipInfo res1 = service.getBySn(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_HAS_BEEN_PACKED, e.getExMsgId());
        }

        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(anyList())).thenReturn(null);
        PsWipInfo res1 = service.getBySn(dto);
        Assert.assertEquals("WorkOrderNo", res1.getWorkOrderNo());

        dto.setWorkOrderNo("");
        PsWipInfo res2 = service.getBySn(dto);
        Assert.assertEquals("WorkOrderNo", res2.getWorkOrderNo());
    }


    @Test
    public void getByLpn() throws Exception {
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);

        List<ContainerContentInfoDTO> ccl = new ArrayList<>();
        PowerMockito.when(ProductionmgmtRemoteService.queryContentInfo(any(), any())).thenReturn(ccl);
        try {
            List<String> res1 = service.getByLpn("lpn");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_NOT_FOUND_BY_LPN, e.getExMsgId());
        }

        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setEntityIdentification("111");
        ccl.add(containerContentInfoDTO);
        ContainerContentInfoDTO containerContentInfoDTO1 = new ContainerContentInfoDTO();
        containerContentInfoDTO1.setEntityIdentification("222");
        ccl.add(containerContentInfoDTO1);
        PowerMockito.when(ProductionmgmtRemoteService.queryContentInfo(any(), any())).thenReturn(ccl);

        List<PsWipInfo> wipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        wipInfos.add(psWipInfo);
        psWipInfo.setSn("111");
        psWipInfo.setWorkOrderNo("WorkOrderNo1");
        psWipInfo.setCurrProcessCode("CurrProcessCode1");
        psWipInfo.setWorkStation("WorkStation1");

        PsWipInfo psWipInfo1 = new PsWipInfo();
        wipInfos.add(psWipInfo1);
        psWipInfo1.setWorkOrderNo("WorkOrderNo");
        psWipInfo1.setCurrProcessCode("CurrProcessCode");
        psWipInfo1.setWorkStation("WorkStation");

        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos);
        try {
            List<String> res1 = service.getByLpn("lpn");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STATE_NOT_SAME, e.getExMsgId());
        }

        psWipInfo.setWorkOrderNo("WorkOrderNo");
        try {
            List<String> res1 = service.getByLpn("lpn");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STATE_NOT_SAME, e.getExMsgId());
        }

        psWipInfo.setCurrProcessCode("CurrProcessCode");
        try {
            List<String> res1 = service.getByLpn("lpn");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STATE_NOT_SAME, e.getExMsgId());
        }

        List<PsWipInfo> wipInfos1 = new ArrayList<>();
        psWipInfo.setWorkStation("WorkStation");
        wipInfos1.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos1);
        List<String> res1 = service.getByLpn("lpn");
        Assert.assertEquals(2, res1.size());


    }

    @Test
    public void checkSame() throws Exception {
        List<String> sns = new ArrayList<>();
        for (int i = 0; i < 1005; i++) {
            sns.add("1111111");
        }
        List<PsWipInfo> wipInfos1 = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("WorkOrderNo1");
        psWipInfo.setCurrProcessCode("CurrProcessCode1");
        psWipInfo.setWorkStation("WorkStation1");
        wipInfos1.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos1);
        Whitebox.invokeMethod(service, "checkSame", sns);

        List<PsWipInfo> wipInfos2 = new ArrayList<>();
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setWorkOrderNo("WorkOrderNo");
        psWipInfo2.setCurrProcessCode("CurrProcessCode");
        psWipInfo2.setWorkStation("WorkStation");
        wipInfos2.add(psWipInfo2);
        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos1).thenReturn(wipInfos2);
        try {
            Whitebox.invokeMethod(service, "checkSame", sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STATE_NOT_SAME, e.getExMsgId());
        }
    }

    @Test
    public void createByTransfer() throws Exception{
        QcSamplingHeadDTO dto = new QcSamplingHeadDTO();
        PowerMockito.when(centerfactoryRemote.createCfBizCode(Mockito.any())).thenReturn(new LinkedList<String>() {{
            add("123");
        }});
        dto.setWorkOrderNo("WorkOrderNo");
        PcProcessTransferSimpleDTO pcProcessTransferSimpleDTO = new PcProcessTransferSimpleDTO();
        List<PsWipInfoSimpleEntiy> wipList = new ArrayList<>();
        wipList.add(new PsWipInfoSimpleEntiy(){{setSn("111");}});
        pcProcessTransferSimpleDTO.setWipList(wipList);
        PowerMockito.when(psWipInfoService.getPCProcessTransferPage(any())).thenReturn(pcProcessTransferSimpleDTO);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypeList = new ArrayList<>();
        sysLookupTypeList.add(new SysLookupTypesDTO(){{setAttribute1(Constant.STR_DEFAULT);setLookupMeaning("6828");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypeList);

        List<SysLookupTypesDTO> aqlDetails = new ArrayList<>();
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("20");setAttribute1("5");setAttribute2("2");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(aqlDetails);
        PsWipInfo psWipInfoBySn = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(any())).thenReturn(psWipInfoBySn);
        PowerMockito.when(qcSamplingHeadRepository.insertSelective(any())).thenReturn(1);
        int total = service.createByTransfer(dto);
        Assert.assertEquals(1, total);
    }
    @Test
    public void getSamplingBySn() {
        PowerMockito.when(qcSamplingHeadRepository.getSamplingBySn(Mockito.any())).thenReturn(null);
        Assert.assertNull(service.getSamplingBySn("711635300011"));
    }

    @Test
    public void getSamplingByLpn() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        Page<ContainerContentInfoSnQueryDTO> pageContainerContentPage = new Page<>();
        pageContainerContentPage.setTotal(0);

        Page<ContainerContentInfoSnQueryDTO> contentInfoPage = new Page<>();
        contentInfoPage.setTotal(0);
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContentPage(Mockito.any())).thenReturn(contentInfoPage);
        Assert.assertTrue(0 == service.getSamplingByLpn("711635300011").getSnQty());

        pageContainerContentPage.setTotal(1);
        ContainerContentInfoSnQueryDTO contentInfoSnQueryDTO = new ContainerContentInfoSnQueryDTO();
        contentInfoSnQueryDTO.setEntityIdentification("sn");
        pageContainerContentPage.setRows(new ArrayList<ContainerContentInfoSnQueryDTO>(){{add(contentInfoSnQueryDTO);}});
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContentPage(Mockito.any())).thenReturn(pageContainerContentPage);
        QcSamplingHead samplingBySn = new QcSamplingHead();
        samplingBySn.setHeadId("RI2401180015");
        PowerMockito.when(qcSamplingHeadRepository.getSamplingBySn(Mockito.any())).thenReturn(samplingBySn);
        Assert.assertEquals("RI2401180015",service.getSamplingByLpn("711635300011").getHeadId());
    }

    @Test
    public void autoCreateSamplingByLpn() throws Exception {
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        QcSamplingHeadDTO head = new QcSamplingHeadDTO();
        List<ContainerContentInfoDTO> ccl = new ArrayList<>();
        PowerMockito.when(ProductionmgmtRemoteService.queryContentInfo(any(), any())).thenReturn(ccl);

        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setEntityIdentification("111");
        ccl.add(containerContentInfoDTO);
        ContainerContentInfoDTO containerContentInfoDTO1 = new ContainerContentInfoDTO();
        containerContentInfoDTO1.setEntityIdentification("222");
        ccl.add(containerContentInfoDTO1);
        PowerMockito.when(ProductionmgmtRemoteService.queryContentInfo(any(), any())).thenReturn(ccl);

        List<PsWipInfo> wipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        wipInfos.add(psWipInfo);
        psWipInfo.setSn("111");
        psWipInfo.setWorkOrderNo("WorkOrderNo1");
        psWipInfo.setCurrProcessCode("CurrProcessCode1");
        psWipInfo.setWorkStation("WorkStation1");

        PsWipInfo psWipInfo1 = new PsWipInfo();
        wipInfos.add(psWipInfo1);
        psWipInfo1.setWorkOrderNo("WorkOrderNo");
        psWipInfo1.setCurrProcessCode("CurrProcessCode");
        psWipInfo1.setWorkStation("WorkStation");

        psWipInfo.setWorkOrderNo("WorkOrderNo");

        psWipInfo.setCurrProcessCode("CurrProcessCode");

        List<PsWipInfo> wipInfos1 = new ArrayList<>();
        psWipInfo.setWorkStation("WorkStation");
        wipInfos1.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.checkSnSameState(any())).thenReturn(wipInfos1);
        List<String> res1 = service.getByLpn("lpn");
        Assert.assertEquals(2, res1.size());
        PowerMockito.when(centerfactoryRemote.createBillNo(anyString(),anyString(),anyInt(),anyString())).thenReturn("RI2401180015");

        PsWipInfo psWipInfoBySn = new PsWipInfo();
        psWipInfoBySn.setWorkOrderNo("workOrderNo");
        psWipInfoBySn.setAttribute1("prodplanid");
        psWipInfoBySn.setItemNo("itemNo");
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(any())).thenReturn(psWipInfoBySn);
        PowerMockito.when(qcSamplingHeadRepository.insertSelective(any())).thenReturn(1);
        List<SysLookupTypesDTO> sysLookupTypeList = new ArrayList<>();
        sysLookupTypeList.add(new SysLookupTypesDTO(){{setAttribute1(Constant.STR_DEFAULT);setLookupMeaning("6828");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypeList);

        List<SysLookupTypesDTO> aqlDetails = new ArrayList<>();
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("20");setAttribute1("5");setAttribute2("2");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(aqlDetails);

        Assert.assertTrue(1 == service.autoCreateSamplingByLpn(head));
    }

    @Test
    public void checkAndInsertSamplingDetail(){
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<QcSamplingHead> samplingRecords = new ArrayList<>();
        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        qcSamplingHead.setHeadId("123");
        samplingRecords.add(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.getSamplingRecord(any(), any())).thenReturn(samplingRecords);
        QcSamplingDetailSnCheckDTO dto = new QcSamplingDetailSnCheckDTO();
        dto.setHeadId("11");

        String empNo = "00260524";
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_EXISTS_ON_OTHER_QC);
        }
        qcSamplingHead.setHeadId("11");
        PowerMockito.when( qcSamplingHeadRepository.getSamplingRecord(any(), any())).thenReturn(samplingRecords);
        service.checkAndInsertSamplingDetail(dto,empNo);
        dto.setHeadId("12");
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_EXISTS_ON_OTHER_QC);
        }
        PowerMockito.when(qcSamplingHeadRepository.getSamplingRecord(any(), any())).thenReturn(null);
        dto.setSn("sn");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(null);
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SNS_IS_NOT_EXITS);
        }

        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setWorkOrderNo("workOrder");
        wipInfoBySn.setCurrProcessCode("1");
        wipInfoBySn.setWorkStation("S1");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(wipInfoBySn);
        dto.setWorkOrderNo("workOrder2");
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.WORKORDER_NOT_SAME);
        }

        dto.setWorkOrderNo("workOrder");
        dto.setCurrProcessCode("2");
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.PROCESSCODE_NOT_SAME);
        }
        dto.setCurrProcessCode("1");
        dto.setWorkStation("S2");
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.WORKSTATION_NOT_SAME);
        }
        dto.setWorkStation("S1");

        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(any())).thenReturn(null);

        PowerMockito.when(qcSamplingDetailRepository.insert(any())).thenReturn(1);
        PowerMockito.when(qcSamplingHeadRepository.updateTotalQtyByHeadId(any())).thenReturn(1);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
        service.checkAndInsertSamplingDetail(dto,empNo);

        List<ContainerContentInfoDTO> ccl  =new ArrayList<>();
        ContainerContentInfoDTO contentInfoDTO = new ContainerContentInfoDTO();
        contentInfoDTO.setLpn("123");
        ccl.add(contentInfoDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(any())).thenReturn(ccl);
        dto.setLpn("123");
        service.checkAndInsertSamplingDetail(dto,empNo);

        dto.setLpn("111");
        try{
            service.checkAndInsertSamplingDetail(dto,empNo);
        }catch(MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_HAS_BEEN_PACKED);
        }
    }

    @Test
    public void getErrorSamplingResult(){
        QcSamplingDetailResultDTO dto = new QcSamplingDetailResultDTO();
        List<QcSamplingDetailResult> qcRegulations = new ArrayList<>();
        PowerMockito.when(qcSamplingDetailResultRepository.getErrorSamplingResult(any())).thenReturn(qcRegulations);
        Assert.assertTrue(0 == service.getErrorSamplingResult(dto).size());
    }

    @Test
    public void getSamplingDetailOne(){
        PowerMockito.when(qcSamplingDetailRepository.getSamplingDetailOne(any())).thenReturn(null);
        Assert.assertNull(service.getSamplingDetailOne(null));
    }

    @Test
    public void getDetailResultPage(){
        QcSamplingDetailResultDTO dto = new QcSamplingDetailResultDTO();
        List<QcSamplingDetailResult> qcRegulations = new ArrayList<>();
        PowerMockito.when(qcSamplingDetailResultRepository.getDetailResultPage(any())).thenReturn(qcRegulations);
        Assert.assertTrue(0 == service.getDetailResultPage(dto).getTotal());
    }

    @Test
    public void submitSamplingResult() throws Exception {
        QcSamplingHeadDTO dto = new QcSamplingHeadDTO();
        String empNo = "00260524";
        dto.setHeadId("RI2401180015");
        PowerMockito.when(qcSamplingHeadRepository.countSampledByHeadId(any())).thenReturn(null);
        try{
            service.submitSamplingResult(dto, empNo);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.QC_SAMPLING_NOT_FOUND);
        }
        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        qcSamplingHead.setSampleQty(10l);
        qcSamplingHead.setSampledTotalQty(8l);
        qcSamplingHead.setTotalQty(20l);
        PowerMockito.when(qcSamplingHeadRepository.countSampledByHeadId(any())).thenReturn(qcSamplingHead);
        try{
            service.submitSamplingResult(dto, empNo);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.INSUFFICIENT_SAMPLING_QUANTITY);
        }

        qcSamplingHead.setSampledTotalQty(14l);
        qcSamplingHead.setUnqualifiedQty(3l);
        qcSamplingHead.setAcceptQty(2l);

        PowerMockito.when(qcSamplingHeadRepository.countSampledByHeadId(any())).thenReturn(qcSamplingHead);
        PowerMockito.when(qcSamplingHeadRepository.updateByPrimaryKeySelective(any())).thenReturn(1);
        Assert.assertEquals("4", service.submitSamplingResult(dto, empNo));

        qcSamplingHead.setUnqualifiedQty(0l);
        PowerMockito.when(qcSamplingHeadRepository.countSampledByHeadId(any())).thenReturn(qcSamplingHead);
        Assert.assertEquals("3", service.submitSamplingResult(dto, empNo));
    }

    @Test
    public void autoTransfer() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);

        QcSamplingHeadDTO samplingHead = new QcSamplingHeadDTO();;
        String empNo = "00260524";
        samplingHead.setHeadId("RI2401180015");

        QcSamplingHead head = new QcSamplingHead();
        head.setWorkOrderNo("No1");
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(any())).thenReturn(head);

        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        PsEntityPlanBasicDTO planBasicDTO1 = new PsEntityPlanBasicDTO();
        planBasicDTO1.setProcessGroup("");
        planBasicDTO1.setRouteId("RID1");
        psEntityPlanInfo.add(planBasicDTO1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanInfo);
        PowerMockito.when(CrafttechRemoteService.getScanProcess(any())).thenReturn(null);
        try{
            service.autoTransfer(samplingHead, empNo);
        }catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(),MessageId.PROCESS_CODE_IS_NOT_EXISTED);
        }
        planBasicDTO1.setProcessGroup("1");
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanInfo);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByQuery(any())).thenReturn(null);

        List<CtRouteInfoDTO> listRoute = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO detailDTO = new CtRouteDetailDTO();
        detailDTO.setNextProcess("2");
        listDetail.add(detailDTO);
        ctRouteInfoDTO.setListDetail(listDetail);
        listRoute.add(ctRouteInfoDTO);
        PowerMockito.when(CrafttechRemoteService.getScanProcess(any())).thenReturn(listRoute);
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessName("DIP");
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(bsProcess);

        try{
            service.autoTransfer(samplingHead, empNo);
        }catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(),MessageId.WORDER_ORDER_NOT_FOUND);
        }
        List<PsWorkOrderDTO> toWorkOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("WO1");
        psWorkOrderDTO.setLineCode("SMT-QC01");
        toWorkOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByQuery(any())).thenReturn(toWorkOrderList);
        PowerMockito.when(qcSamplingDetailRepository.countSamplingDetail(any())).thenReturn(502);
        List<QcSamplingDetail> qcRegulations = new ArrayList<>();
        QcSamplingDetail samplingDetail = new QcSamplingDetail();
        samplingDetail.setSn("SN1");
        qcRegulations.add(samplingDetail);
        PowerMockito.when(qcSamplingDetailRepository.getPageList(any())).thenReturn(qcRegulations);
        PowerMockito.when(psWipInfoService.submitPcProcessTransfer(any())).thenReturn(null);
        service.autoTransfer(samplingHead, empNo);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void setWoAndLine() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PcProcessTransferComDTO ptObj = new PcProcessTransferComDTO();
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        String[] toProcessCodeList = {"a"};
        PsEntityPlanBasicDTO planBasicDTO1 = new PsEntityPlanBasicDTO();
        planBasicDTO1.setItemNo("129571751204ZTA");
        planBasicDTO1.setSourceTask("2401171");
        psEntityPlanInfo.add(planBasicDTO1);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByQuery(any())).thenReturn(null);
        Whitebox.invokeMethod(service, "setWoAndLine", ptObj, psEntityPlanInfo,toProcessCodeList);

        List<PsWorkOrderDTO> toWorkOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("WO1");
        psWorkOrderDTO.setLineCode("SMT-QC01");
        toWorkOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByQuery(any())).thenReturn(toWorkOrderList);
        Whitebox.invokeMethod(service, "setWoAndLine", ptObj, psEntityPlanInfo,toProcessCodeList);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void getLastProcess() throws Exception {
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setProcessGroup("");
        psEntityPlanInfo.add(psEntityPlanBasicDTO);
        Whitebox.invokeMethod(service, "getLastProcess", psEntityPlanInfo);
        psEntityPlanBasicDTO.setProcessGroup("2$3(*)");
        Whitebox.invokeMethod(service, "getLastProcess", psEntityPlanInfo);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void getToProcess() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);

        PcProcessTransferComDTO ptObj = new PcProcessTransferComDTO();
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setRouteId("RI1");
        psEntityPlanInfo.add(psEntityPlanBasicDTO);
        String lastProcessCodeStr = "2";
        String[] lastProcessCodeList = {"1"};

        PowerMockito.when(CrafttechRemoteService.getScanProcess(any())).thenReturn(null);
        Whitebox.invokeMethod(service, "getToProcess", ptObj,psEntityPlanInfo,lastProcessCodeStr,lastProcessCodeList);

        List<CtRouteInfoDTO> listRoute = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        listRoute.add(ctRouteInfoDTO);
        PowerMockito.when(CrafttechRemoteService.getScanProcess(any())).thenReturn(listRoute);
        Whitebox.invokeMethod(service, "getToProcess", ptObj,psEntityPlanInfo,lastProcessCodeStr,lastProcessCodeList);

        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO detailDTO = new CtRouteDetailDTO();
        detailDTO.setNextProcess("2");
        listDetail.add(detailDTO);
        CtRouteDetailDTO detailDTO2 = new CtRouteDetailDTO();
        detailDTO2.setNextProcess("3");
        listDetail.add(detailDTO2);
        ctRouteInfoDTO.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.getScanProcess(any())).thenReturn(listRoute);
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(null);
        Whitebox.invokeMethod(service, "getToProcess", ptObj,psEntityPlanInfo,lastProcessCodeStr,lastProcessCodeList);

        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessName("DIP");
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(bsProcess);
        Whitebox.invokeMethod(service, "getToProcess", ptObj,psEntityPlanInfo,lastProcessCodeStr,lastProcessCodeList);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void getCtRouteInfoDTO() throws Exception {
        List<CtRouteInfoDTO> listRoute = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        listRoute.add(ctRouteInfoDTO);
        Whitebox.invokeMethod(service, "getCtRouteInfoDTO", listRoute);

        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO detailDTO = new CtRouteDetailDTO();
        detailDTO.setCurrProcess("0");
        listDetail.add(detailDTO);
        ctRouteInfoDTO.setListDetail(listDetail);
        Whitebox.invokeMethod(service, "getCtRouteInfoDTO", listRoute);

        CtRouteDetailDTO detailDTO2 = new CtRouteDetailDTO();
        detailDTO2.setCurrProcess("0");
        listDetail.add(detailDTO2);
        CtRouteDetailDTO detailDTO3 = new CtRouteDetailDTO();
        detailDTO3.setCurrProcess("3");
        listDetail.add(detailDTO3);
        Whitebox.invokeMethod(service, "getCtRouteInfoDTO", listRoute);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void setPropreties() throws Exception {
        PcProcessTransferComDTO ptObj = new PcProcessTransferComDTO();
        Whitebox.invokeMethod(service, "setPropreties", ptObj,"a",null);
        Whitebox.invokeMethod(service, "setPropreties", ptObj,null,"b");
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }

    @Test
    public void selectHeadInfoById(){
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(any())).thenReturn(null);
        Assert.assertNull(service.selectHeadInfoById("123"));
    }

    @Test
    public void updateSnSamplingResult(){
        QcSamplingDetailDTO dto = new QcSamplingDetailDTO();
        String empNo = "00260524";

        QcSamplingDetail detail = null;
        PowerMockito.when(qcSamplingDetailRepository.getSamplingDetailOne(any())).thenReturn(detail);
        try{
            service.updateSnSamplingResult(dto, empNo);
        }catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_EXISTS_ON_OTHER_QC);
        }

        PowerMockito.when(qcSamplingDetailResultRepository.geLastVersion(any())).thenReturn(0);
        detail = new QcSamplingDetail();
        detail.setSamplingResult(Constant.STR_2);
        PowerMockito.when(qcSamplingDetailRepository.getSamplingDetailOne(any())).thenReturn(detail);
        QcSamplingHead qcSamplingHead = new QcSamplingHead();
        qcSamplingHead.setStatus(Constant.STR_1);
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(any())).thenReturn(qcSamplingHead);
        PowerMockito.when(qcSamplingDetailRepository.updateSnSamplingResult(any())).thenReturn(1);
        dto.setSamplingResult(Constant.STR_0);
        service.updateSnSamplingResult(dto, empNo);

        dto.setSamplingResult(Constant.STR_0);
        detail.setSamplingResult(Constant.STR_1);
        qcSamplingHead.setStatus(Constant.STR_2);
        PowerMockito.when(qcSamplingHeadRepository.selectByPrimaryKey(any())).thenReturn(qcSamplingHead);
        PowerMockito.when(qcSamplingDetailRepository.getSamplingDetailOne(any())).thenReturn(detail);
        PowerMockito.when(qcSamplingDetailResultRepository.insertSelective(any())).thenReturn(1);
        service.updateSnSamplingResult(dto, empNo);

        dto.setSamplingResult(Constant.STR_1);
        detail.setSamplingResult(Constant.STR_2);
        List<QcSamplingDetailResultDTO> detailResultDTOS = new ArrayList<>();
        QcSamplingDetailResultDTO detailResult = new QcSamplingDetailResultDTO();
        detailResultDTOS.add(detailResult);
        dto.setDetailResults(detailResultDTOS);
        service.updateSnSamplingResult(dto, empNo);

        detail.setSamplingResult(Constant.STR_1);
        PowerMockito.when(qcSamplingDetailRepository.getSamplingDetailOne(any())).thenReturn(detail);
        dto.setDetailResults(new ArrayList<>());
        service.updateSnSamplingResult(dto, empNo);

        QcSamplingDetailResultDTO detail1 = new QcSamplingDetailResultDTO();
        detail1.setLocationNo("1");
        detail1.setErrorCode("A");
        detailResultDTOS.add(detail1);
        PowerMockito.when(qcSamplingDetailResultRepository.batchInsert(any())).thenReturn(1);
        dto.setDetailResults(detailResultDTOS);
        service.updateSnSamplingResult(dto, empNo);
    }

    @Test
    public void setQualityLimit() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        QcSamplingHeadDTO head = new QcSamplingHeadDTO();
        long totalQty = 0l;

        List<SysLookupTypesDTO> aqlDetails = new ArrayList<>();
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("20");setAttribute1("5");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(aqlDetails);
        try {
            Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);
        } catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SET_DEFAULT_QUALITY_LIMIT);
        }
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("CUSTOM");setAttribute1("default");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(aqlDetails);
        try {
            Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);
        } catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.AUTO_CREATION_NOT_SUPPORT_CUSTOM_AQL);
        }
        aqlDetails.clear();
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("COMPLETE");setAttribute1("default");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(aqlDetails);
        Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);

        aqlDetails.clear();
        aqlDetails.add(new SysLookupTypesDTO(){{setLookupMeaning("AQL0.65");setAttribute1("default");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(aqlDetails);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);
        } catch (MesBusinessException e){
            Assert.assertEquals(e.getExMsgId(), MessageId.SET_DEFAULT_QUALITY_LIMIT);
        }

        List<SysLookupTypesDTO> aqlDetails2 = new ArrayList<>();
        aqlDetails2.add(new SysLookupTypesDTO(){{setLookupMeaning("20");setAttribute1("5");setAttribute2("2");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(aqlDetails2);
        Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);
        totalQty = 22l;
        Whitebox.invokeMethod(service, "setQualityLimit", head, totalQty);
        String temp = "Y";
        Assert.assertEquals(temp,Constant.FLAG_Y);
    }
}
