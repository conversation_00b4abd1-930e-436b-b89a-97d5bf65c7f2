package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.ParentsnAndSn;
import com.zte.domain.model.ParentsnAndSnRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderSnAssign;
import com.zte.domain.model.PsWorkOrderSnAssignRepository;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.EmEqpScannerRecordDTO;
import com.zte.interfaces.dto.PsWorkOrderSnAssignDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

@PrepareForTest({PlanscheduleRemoteService.class})
public class PsWorkOrderSnAssignServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private PsWorkOrderSnAssignServiceImpl service;

    @Mock
    private PsWorkOrderSnAssignRepository psWorkOrderSnAssignRepository;
    @Mock
    private ParentsnAndSnRepository parentsnAndSnRepository;

    @Test
    public void insertPsWorkOrderSnAssign() {
        PsWorkOrderSnAssign record = new PsWorkOrderSnAssign();
        PowerMockito.when(psWorkOrderSnAssignRepository.insert(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1,service.insertPsWorkOrderSnAssign(record));
    }

    @Test
    public void selectByWorkOrderNo() {
        String workOrderNo = "111";
        PowerMockito.when(psWorkOrderSnAssignRepository.selectByWorkOrderNo(Mockito.any())).thenReturn(null);
        Assert.assertNull(service.selectByWorkOrderNo(workOrderNo));
    }


    @Test
    public void selectByParentSn() {
        String parentSn = "111";
        PowerMockito.when(psWorkOrderSnAssignRepository.selectByParentSn(Mockito.any())).thenReturn(null);
        Assert.assertNull(service.selectByParentSn(parentSn));
    }

    @Test
    public void selectBysn() {
        String sn = "111";
        PowerMockito.when(psWorkOrderSnAssignRepository.selectBysn(Mockito.any())).thenReturn(null);
        Assert.assertNull(service.selectBysn(sn));
    }

    @Test
    public void updatePsWorkOrderSnAssignById() {
        PsWorkOrderSnAssign record = new PsWorkOrderSnAssign();
        PowerMockito.when(psWorkOrderSnAssignRepository.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.updatePsWorkOrderSnAssignById(record));
    }

    @Test
    public void testGetPrintSnRecord() throws MesBusinessException {
        PsWorkOrderSnAssignDTO dto = new PsWorkOrderSnAssignDTO();
        try {
            service.getPrintSnRecord(dto);
        } catch (Exception e) {
        }
        dto.setProdplanId("aaaaa");
        dto.setSn("snsn");
        PowerMockito.when(psWorkOrderSnAssignRepository.getPrintSnRecordCount(Mockito.any())).thenReturn(10L);
        PowerMockito.when(psWorkOrderSnAssignRepository.getPrintSnRecord(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getPrintSnRecord(dto));
    }

    @Test
    public void getPageList() {
        EmEqpScannerRecordDTO dto = new EmEqpScannerRecordDTO();
        dto.setProdplanId("1");
        try {
            service.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_IS_NOT_EXITS, e.getMessage());
        }

        dto.setProdplanId("1234567");
        dto.setStation("printer");
        try {
            service.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }

        dto.setStartTimeCreate("2023-03-10 00:00:00");
        try {
            service.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }

        dto.setStartTimeCreate(null);
        dto.setEndTimeCreate("2023-08-10 00:00:00");
        try {
            service.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }

        dto.setStartTimeCreate("2023-03-10 00:00:00");
        dto.setEndTimeCreate("2023-08-10 00:00:00");
        try {
            service.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TIME_INTERVAL_ERROR, e.getMessage());
        }

        dto.setEndTimeCreate("2023-04-10 00:00:00");
        Page<EmEqpScannerRecordDTO> pageList = service.getPageList(dto);
        Assert.assertEquals(0, pageList.getRows().size());

        List<EmEqpScannerRecordDTO> infos = new ArrayList<>();
        EmEqpScannerRecordDTO emEqpScannerRecordDTO = new EmEqpScannerRecordDTO();
        infos.add(emEqpScannerRecordDTO);
        PowerMockito.when(psWorkOrderSnAssignRepository.getPageList(Mockito.any()))
                .thenReturn(infos);
        PowerMockito.when(parentsnAndSnRepository.getSnByParentSnList(Mockito.any()))
                .thenReturn(new ArrayList<>());
        pageList = service.getPageList(dto);
        Assert.assertEquals(1, pageList.getRows().size());
    }

    @Test
    public void getTaskNo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);

        List<EmEqpScannerRecordDTO> infos = new ArrayList<>();
        EmEqpScannerRecordDTO emEqpScannerRecordDTO = new EmEqpScannerRecordDTO();
        infos.add(emEqpScannerRecordDTO);
        Whitebox.invokeMethod(service, "getTaskNo", infos);
        Assert.assertEquals(null, infos.get(0).getTaskNo());

        emEqpScannerRecordDTO.setProdplanId("1234567");
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTaskInfoDTO = new PsTask();
        psTaskInfoDTO.setTaskNo("taskNo");
        psTaskInfoDTO.setProdplanId("1234567");
        psTaskList.add(psTaskInfoDTO);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(Mockito.any()))
                .thenReturn(psTaskList);

        Whitebox.invokeMethod(service, "getTaskNo", infos);
        Assert.assertEquals("taskNo", infos.get(0).getTaskNo());
    }

    @Test
    public void getBindingRelation() throws Exception {
        PowerMockito.when(parentsnAndSnRepository.getSnByParentSnList(Mockito.any()))
                .thenReturn(new ArrayList<>());
        List<EmEqpScannerRecordDTO> infos = new ArrayList<>();
        Whitebox.invokeMethod(service, "getBindingRelation", infos);
        Assert.assertEquals(0, infos.size());

        infos.add(new EmEqpScannerRecordDTO(){{
            setSn("111");
            setErrMsg("123");
        }});
        List<ParentsnAndSn> list = new ArrayList<>();
        list.add(new ParentsnAndSn(){{setSn("111");setParentSn("123");}});
        list.add(new ParentsnAndSn(){{setSn("222");setParentSn("123");}});
        list.add(new ParentsnAndSn(){{setSn("333");setParentSn("123");}});
        PowerMockito.when(parentsnAndSnRepository.getSnByParentSnList(Mockito.any()))
                .thenReturn(list);
        Whitebox.invokeMethod(service, "getBindingRelation", infos);
        Assert.assertEquals("123:111,222,333", infos.get(0).getBindingRelation());
    }

}