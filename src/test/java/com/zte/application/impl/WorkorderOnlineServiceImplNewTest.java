package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.ProcessPositionsCheckInDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({BasicsettingRemoteService.class})
public class WorkorderOnlineServiceImplNewTest extends PowerBaseTestCase {

    @InjectMocks
    WorkorderOnlineServiceImpl service;

    @Test
    public void verifyPersonJobMatching() {
        PsWorkOrderDTO wo = new PsWorkOrderDTO(){{setLineCode("1"); setWorkOrderNo("7777777-SMT-A");}};
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880001);}}));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880001);}},
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880002);}}
                ));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880001);}},
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880002);setAttribute1(FLAG_Y);}}
                ));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880001);}},
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880002);setAttribute1(FLAG_Y);setLookupMeaning("2");}}
                ));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880001);}},
                        new SysLookupValuesDTO(){{setLookupCode(LOOKUP_VALUE_6880002);setAttribute1(FLAG_Y);setLookupMeaning("1,2,3");}}
                ));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{
                            setLookupCode(LOOKUP_VALUE_6880001);
                            setLookupMeaning(FLAG_Y);
                        }}
                ));
        service.verifyPersonJobMatching(wo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO(){{
                            setLookupCode(LOOKUP_VALUE_6880001);
                            setLookupMeaning(FLAG_Y);
                        }},
                        new SysLookupValuesDTO(){{
                            setAttribute1(HJM_SECTION);
                            setDescriptionChin("1,2,3");
                            setLookupMeaning("SMT");
                            setLookupCode(BigDecimal.ZERO);
                        }}
                ));
        Assert.assertThrows(MesBusinessException.class, () -> service.verifyPersonJobMatching(wo));
        PowerMockito.when(BasicsettingRemoteService.getProcessPositionsCheckIn(anyString()))
                .thenReturn(Lists.newArrayList(
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("1");setEmpNo("2");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("4");setEmpNo("2");}}
                ));
        Assert.assertThrows(MesBusinessException.class, () -> service.verifyPersonJobMatching(wo));
        wo.setUserId("1");
        PowerMockito.when(BasicsettingRemoteService.getProcessPositionsCheckIn(anyString()))
                .thenReturn(Lists.newArrayList(
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("1");setEmpNo("2");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("2");setEmpNo("2");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("3");setEmpNo("2");}}
                ));
        Assert.assertThrows(MesBusinessException.class, () -> service.verifyPersonJobMatching(wo));

        PowerMockito.when(BasicsettingRemoteService.getProcessPositionsCheckIn(anyString()))
                .thenReturn(Lists.newArrayList(
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("1");setEmpNo("1");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("2");setEmpNo("1");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("3");setEmpNo("1");}}
                ));
        service.verifyPersonJobMatching(wo);

        PowerMockito.when(BasicsettingRemoteService.getProcessPositionsCheckIn(anyString()))
                .thenReturn(Lists.newArrayList(
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("1");setEmpNo("2");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("2");setEmpNo("2");}},
                        new ProcessPositionsCheckInDTO(){{setProcessPosition("3");setEmpNo("1");}}
                ));
        service.verifyPersonJobMatching(wo);
    }
}