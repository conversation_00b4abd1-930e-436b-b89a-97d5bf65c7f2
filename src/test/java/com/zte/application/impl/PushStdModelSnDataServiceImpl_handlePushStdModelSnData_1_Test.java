/*Started by AICoder, pid:y04d233041600ae14acc0839609f999057569f30*/
package com.zte.application.impl;
import com.google.common.collect.Lists;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.interfaces.dto.WarehouseEntryDetailExtDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest(RequestHeadValidationUtil.class)
public class PushStdModelSnDataServiceImpl_handlePushStdModelSnData_1_Test {

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;

    @Before
    public void setUp() {
        // Mockito will inject the mocks into the service before each test
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("12");
    }

    @Test
    public void testHandlePushStdModelSnData_NoData() {
        // Given
        PushStdModelSnDataHandleDTO handleDTO = new PushStdModelSnDataHandleDTO();
        handleDTO.setCurrProcess("testProcess");
        handleDTO.setTaskNos(Collections.singletonList("task1"));

        when(centerfactoryRemoteService.getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(null);

        // When
        boolean result = pushStdModelSnDataService.handlePushStdModelSnData(handleDTO);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, times(1)).getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_EmptyData() {
        // Given
        PushStdModelSnDataHandleDTO handleDTO = new PushStdModelSnDataHandleDTO();
        handleDTO.setCurrProcess("testProcess");
        handleDTO.setTaskNos(Collections.singletonList("task1"));

        when(centerfactoryRemoteService.getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(new PageRows<>());

        // When
        boolean result = pushStdModelSnDataService.handlePushStdModelSnData(handleDTO);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, times(1)).getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_WithData() {
        // Given
        PushStdModelSnDataHandleDTO handleDTO = new PushStdModelSnDataHandleDTO();
        handleDTO.setCurrProcess("testProcess");
        handleDTO.setTaskNos(Collections.singletonList("task1"));

        PushStdModelSnDataDTO snDataDTO = new PushStdModelSnDataDTO();
        snDataDTO.setSn("SN123");
        snDataDTO.setLastUpdatedDate(new Date());

        PageRows<PushStdModelSnDataDTO> pageRows = new PageRows<>();
        pageRows.setRows(Collections.singletonList(snDataDTO));

        when(centerfactoryRemoteService.getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(pageRows);

        // When
        boolean result = pushStdModelSnDataService.handlePushStdModelSnData(handleDTO);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, times(1)).getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_WithData1() {
        // Given
        PushStdModelSnDataHandleDTO handleDTO = new PushStdModelSnDataHandleDTO();
        handleDTO.setCurrProcess("testProcess");
        handleDTO.setTaskNos(Collections.singletonList("task1"));

        PushStdModelSnDataDTO snDataDTO = new PushStdModelSnDataDTO();
        snDataDTO.setSn("SN123");
        snDataDTO.setLastUpdatedDate(new Date());

        PageRows<PushStdModelSnDataDTO> pageRows = new PageRows<>();
        pageRows.setRows(Collections.singletonList(snDataDTO));

        when(centerfactoryRemoteService.getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(pageRows);
        when(warehouseEntryDetailRepository.selectExtByQuery(any())).thenReturn(Lists.newArrayList(new WarehouseEntryDetailExtDTO()));

        // When
        boolean result = pushStdModelSnDataService.handlePushStdModelSnData(handleDTO);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, times(1)).getPushStdModelSnData(any(PushStdModelSnDataQueryDTO.class));
    }
}
/*Ended by AICoder, pid:y04d233041600ae14acc0839609f999057569f30*/