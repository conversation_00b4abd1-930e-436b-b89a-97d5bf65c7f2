package com.zte.application.impl;

import com.zte.application.BSmtBomHeaderService;
import com.zte.application.PkCodeInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSmtBomHeader;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.TaskMaterialIssueSeqRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doReturn;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, PlanscheduleRemoteService.class})
public class TaskMaterialIssueSeqServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    TaskMaterialIssueSeqServiceImpl service;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private TaskMaterialIssueSeqRepository taskMaterialIssueSeqRepository;
    @Mock
    private StItemBarcodeServiceImpl stItemBarcodeService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private BSmtBomHeaderService bSmtBomHeaderService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class);
    }

    /* Started by AICoder, pid:oc3aecbc0cobe211468c0a69e0e893849bf68aa7 */
    @Test
    public void getIssuanceInfo() throws Exception {
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO taskMaterialIssueSeqEntityDTO = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO record = new TaskMaterialIssueSeqEntityDTO();
        record.setItemNo("test123");
        record.setReelId("test123");
        record.setSourceBatchCode("test123");
        record.setDirFlag("test123");
        record.setMultiBrand("Y");
        try {
            service.getIssuanceInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PRODPLANID_ID_NULL, e.getExMsgId());
        }
        record.setProdplanId("7777666");
        taskMaterialIssueSeqEntityDTO.setProdplanId("7777666");
        taskMaterialIssueSeqEntityDTO.setItemNo("test123");
        taskMaterialIssueSeqEntityDTO.setReelId("test123");
        taskMaterialIssueSeqEntityDTO.setSourceBatchCode("test123");
        taskMaterialIssueSeqEntityDTO.setDirFlag("test123");
        taskMaterialIssueSeqEntityDTO.setMultiBrand("Y");
        list.add(taskMaterialIssueSeqEntityDTO);
        TaskMaterialIssueSeqEntityDTO taskMaterialIssueSeqEntityDTO1 = new TaskMaterialIssueSeqEntityDTO();
        taskMaterialIssueSeqEntityDTO1.setProdplanId("7777666");
        taskMaterialIssueSeqEntityDTO1.setItemNo("test234");
        taskMaterialIssueSeqEntityDTO1.setReelId("test123");
        taskMaterialIssueSeqEntityDTO1.setSourceBatchCode("test123");
        taskMaterialIssueSeqEntityDTO1.setDirFlag("test123");
        taskMaterialIssueSeqEntityDTO1.setMultiBrand("N");
        list.add(taskMaterialIssueSeqEntityDTO1);
        PowerMockito.when(taskMaterialIssueSeqRepository.getList(Mockito.any())).thenReturn(null);
        service.getIssuanceInfo(record);

        List<PkCodeHistoryDTO> historyDTOList = new ArrayList<>();
        PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
        pkCodeHistoryDTO.setItemCode("test123");
        pkCodeHistoryDTO.setSourceBatchCode("test123");
        historyDTOList.add(pkCodeHistoryDTO);

        List<ZteBarcodeInfoDTO> zteBarcodeInfoDTOS = new ArrayList<>();
        ZteBarcodeInfoDTO zteBarcodeInfo = new ZteBarcodeInfoDTO();
        zteBarcodeInfo.setItemUuid("test123");
        zteBarcodeInfo.setSupplierNo("test123");
        zteBarcodeInfo.setBrandStyle("test123");
        zteBarcodeInfo.setSupplierName("test123");
        zteBarcodeInfoDTOS.add(zteBarcodeInfo);

        PowerMockito.when(taskMaterialIssueSeqRepository.getList(Mockito.any())).thenReturn(new ArrayList<>());
        service.getIssuanceInfo(record);
        PowerMockito.when(taskMaterialIssueSeqRepository.pageList(Mockito.any())).thenReturn(list);
        PowerMockito.when(taskMaterialIssueSeqRepository.getList(Mockito.any())).thenReturn(list);
        List<PolarItemInfo> polarItemInfoList= new ArrayList<>();
        PolarItemInfo polarItemInfo = new PolarItemInfo();
        polarItemInfo.setItemNo("test123");
        polarItemInfo.setMultiBrandFlag(true);
        PolarItemInfo polarItemInfo1 = new PolarItemInfo();
        polarItemInfo1.setItemNo("test234");
        polarItemInfo1.setMultiBrandFlag(false);
        polarItemInfoList.add(polarItemInfo);
        polarItemInfoList.add(polarItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.queryMultiBrandByItemNo(Mockito.any())).thenReturn(polarItemInfoList);
        PowerMockito.when(datawbRemoteService.getZteBarcodeInfoByUUids(Mockito.any())).thenReturn(zteBarcodeInfoDTOS);
        PowerMockito.when(datawbRemoteService.getSupplierNameBySupplerCode(Mockito.any())).thenReturn(zteBarcodeInfoDTOS);
        Assert.assertNotNull(service.getIssuanceInfo(record));
        taskMaterialIssueSeqEntityDTO1.setItemNo("test23");
        Assert.assertNotNull(service.getIssuanceInfo(record));
        record.setMultiBrand("N");
        Assert.assertNotNull(service.getIssuanceInfo(record));
        record.setMultiBrand(null);
        Assert.assertNotNull(service.getIssuanceInfo(record));
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTask.setProdplanNo("xxxx-z");
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(Mockito.anyString())).thenReturn(psTasks);

        List<BBomDetailDTO> bomDetailList = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        bBomDetailDTO.setItemCode("test123");
        bomDetailList.add(bBomDetailDTO);
        PowerMockito.when(BasicsettingRemoteService.getBomDetailTagNo(Mockito.anyString())).thenReturn(bomDetailList);

        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("test123");
        bsItemInfo.setItemName("物料名称123");
        bsItemInfo.setIsDir("Y");
        itemInfoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNoList(Mockito.anyList())).thenReturn(itemInfoList);
        Assert.assertNotNull(service.getIssuanceInfo(record));

        List<PolarItemInfoDTO> polarItemInfoDTOS = new ArrayList<>();
        PolarItemInfoDTO polarItemInfoDTO = new PolarItemInfoDTO();
        polarItemInfoDTO.setItemUuid("test123");
        polarItemInfoDTO.setItemNo("test123");
        polarItemInfoDTO.setItemAngle("180");
        polarItemInfoDTOS.add(polarItemInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getBrandItemDirection(Mockito.anyList())).thenReturn(polarItemInfoDTOS);
        record.setIsAngle("Y");
        record.setDirFlagSame("Y");
        Assert.assertNotNull(service.getIssuanceInfo(record));

        record.setIsAngle("N");
        record.setDirFlagSame("N");
        Assert.assertNotNull(service.getIssuanceInfo(record));
    }
    /* Ended by AICoder, pid:oc3aecbc0cobe211468c0a69e0e893849bf68aa7 */

    @Test
    public void updateDirection() throws Exception {
        PowerMockito.when(taskMaterialIssueSeqRepository.batchUpdateDirection(Mockito.any())).thenReturn(1);
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        Assert.assertEquals(1, service.updateDirection(list));
    }

    @Test
    public void updateIssuanceSeq() throws Exception {
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO taskMaterialIssueSeqEntityDTO = new TaskMaterialIssueSeqEntityDTO();
        taskMaterialIssueSeqEntityDTO.setProdplanId("test123");
        taskMaterialIssueSeqEntityDTO.setItemNo("test123");
        taskMaterialIssueSeqEntityDTO.setReelId("test123");
        taskMaterialIssueSeqEntityDTO.setSourceBatchCode("test123");
        list.add(taskMaterialIssueSeqEntityDTO);
        taskMaterialIssueSeqEntityDTO.setParamList(list);
        PowerMockito.when(taskMaterialIssueSeqRepository.batchUpdate(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.updateIssuanceSeq(taskMaterialIssueSeqEntityDTO));
    }

    @Test
    public void insert() throws Exception {
        List<BSmtBomHeader> attrList = new ArrayList<>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setAttr1("123");
        bSmtBomHeader.setCreateDate(new Date());
        BSmtBomHeader bSmtBomHeader1 = new BSmtBomHeader();
        bSmtBomHeader1.setAttr1("321");
        bSmtBomHeader1.setCreateDate(new Date());
        attrList.add(bSmtBomHeader);
        attrList.add(bSmtBomHeader1);
        List<String> attrExistList = new ArrayList<>();
        attrExistList.add("321");
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setSupplerCode("123");
        pkCodeInfo.setSysLotCode("123");
        pkCodeInfo.setItemCode("111222333");
        pkCodeInfo.setItemQty(new BigDecimal(10));
        pkCodeInfo.setProductTask("123");
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        pkCodeInfo1.setSupplerCode("123");
        pkCodeInfo1.setSysLotCode("321");
        pkCodeInfo1.setItemCode("111222333");
        pkCodeInfo1.setItemQty(new BigDecimal(10));
        pkCodeInfo1.setProductTask("123");
        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        pkCodeInfo2.setSupplerCode("123");
        pkCodeInfo2.setItemCode("111222333");
        pkCodeInfo2.setItemQty(new BigDecimal(10));
        pkCodeInfo2.setProductTask("123");
        PkCodeInfo pkCodeInfo3 = new PkCodeInfo();
        pkCodeInfo3.setSupplerCode("12ad3");
        pkCodeInfo3.setSysLotCode("31221");
        pkCodeInfo3.setItemCode("111222333");
        pkCodeInfo3.setItemQty(new BigDecimal(10));
        pkCodeInfo3.setProductTask("123");
        pkCodeInfoList.add(pkCodeInfo);
        pkCodeInfoList.add(pkCodeInfo1);
        pkCodeInfoList.add(pkCodeInfo2);
        pkCodeInfoList.add(pkCodeInfo3);
        List<TaskMaterialIssueSeqEntityDTO> datawbDto = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO task = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO task1 = new TaskMaterialIssueSeqEntityDTO();
        task.setSupplerCode("123");
        task.setSysLotCode("123");
        task.setBraidDirection("180度");
        task.setDirFlag("是");
        task.setItemNo("111222333");
        task1.setSupplerCode("123");
        task1.setSysLotCode("321");
        task1.setItemNo("111222333");
        datawbDto.add(task);
        datawbDto.add(task1);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2022-06-14 19:57:00");
        sysLookupTypesDTO.setAttribute1("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(bSmtBomHeaderService.getAttr1ByTime(Mockito.anyString())).thenReturn(attrList);
        PowerMockito.when(taskMaterialIssueSeqRepository.getRecordByAttrList(Mockito.any())).thenReturn(attrExistList);
        PowerMockito.when(pkCodeInfoService.getIssueByProductTask(Mockito.any())).thenReturn(new ArrayList<>());
        service.insertIssueSeq("52", "123");
        PowerMockito.when(pkCodeInfoService.getIssueByProductTask(Mockito.any())).thenReturn(pkCodeInfoList);
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(Mockito.any())).thenReturn(new ArrayList<>());
        service.insertIssueSeq("52", "123");
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(Mockito.any())).thenReturn(datawbDto);
        PowerMockito.when(taskMaterialIssueSeqRepository.batchInsert(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1, service.insertIssueSeq("52", "123"));


        sysLookupTypesDTO.setAttribute1("");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> service.insertIssueSeq("52", "123"));

        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTasks.add(psTask);
        psTask.setProdplanId("ProdplanId");
        psTask.setCreateDate(new Date());
        psTask.setLastUpdatedDate(new Date());
        PowerMockito.when(PlanscheduleRemoteService.getSchedulingTask(anyString())).thenReturn(psTasks);
        Assert.assertEquals(1,service.insertIssueSeq("52","123"));
    }

    @Test
    public void addIssueSeqRelationData() throws Exception {
        List<PkCodeInfo> itemList = new ArrayList<>();
        service.addIssueSeqRelationData(itemList);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        itemList.add(pkCodeInfo);
        pkCodeInfo.setSupplerCode("sdsd");
        List<TaskMaterialIssueSeqEntityDTO> packSpecList = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto = new TaskMaterialIssueSeqEntityDTO();
        dto.setSupplerCode("sdsd");
        packSpecList.add(dto);
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(Mockito.anyList())).thenReturn(packSpecList);
        service.addIssueSeqRelationData(itemList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getListByItemNoTest() {
        service.getListByItemNo("sdasd", "sdasdsad");
        Set<String> needCheckItemNoSet = new HashSet<>();
        Assert.assertNotNull(service.getEntityListByItemSet("sdasd", needCheckItemNoSet));
    }

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBom", null);
        Assert.assertTrue(1==1);
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO entity = new TaskMaterialIssueSeqEntityDTO();
        entity.setProdplanId("1234567");
        list.add(entity);
        TaskMaterialIssueSeqEntityDTO entity1 = new TaskMaterialIssueSeqEntityDTO();
        entity1.setProdplanId("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBom", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertNull(list.get(1).getMbom());
    }

    @Test
    public void setColorFlag() throws Exception {
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(service, "setColorFlag", list);
        Assert.assertTrue(list.size() == 0);
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("N");}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("Y");setBraidDirection("1");setBrandItemDirection("");}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("Y");setBraidDirection("");setBrandItemDirection("2");}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("Y");setBraidDirection("");setBrandItemDirection("");}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("Y");setBraidDirection("2");setBrandItemDirection("3");}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setIsDir("Y");setBraidDirection("3");setBrandItemDirection("3");}});
        Whitebox.invokeMethod(service, "setColorFlag", list);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void appendTagNo() throws Exception {
        BBomDetailDTO bomDetail = new BBomDetailDTO();
        Whitebox.invokeMethod(service, "appendTagNo", bomDetail);

        bomDetail.setPositionExt("1");
        bomDetail.setPlaceNoExt1("1");
        bomDetail.setPlaceNoExt2("1");
        bomDetail.setPlaceNoExt3("1");
        bomDetail.setPlaceNoExt4("1");
        bomDetail.setPlaceNoExt5("1");
        bomDetail.setPlaceNoExt6("1");
        bomDetail.setPlaceNoExt7("1");
        Whitebox.invokeMethod(service, "appendTagNo", bomDetail);
        Assert.assertEquals("1", bomDetail.getPositionExt());
    }

    @Test
    public void filterList() throws Exception {

        TaskMaterialIssueSeqEntityDTO record = new TaskMaterialIssueSeqEntityDTO();
        record.setItemNo("123");
        record.setIsAngle(Constant.FLAG_Y);
        record.setDirFlagSame(Constant.FLAG_Y);

        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto1 = new TaskMaterialIssueSeqEntityDTO();
        dto1.setBraidDirection("");
        dto1.setBrandItemDirection("");
        list.add(dto1);
        Whitebox.invokeMethod(service, "filterList", record, list);

        TaskMaterialIssueSeqEntityDTO dto2 = new TaskMaterialIssueSeqEntityDTO();
        dto2.setBraidDirection("1");
        dto2.setBrandItemDirection("1");
        list.add(dto2);

        TaskMaterialIssueSeqEntityDTO dto3 = new TaskMaterialIssueSeqEntityDTO();
        dto3.setBraidDirection("1");
        dto3.setBrandItemDirection("2");
        list.add(dto3);

        TaskMaterialIssueSeqEntityDTO dto4 = new TaskMaterialIssueSeqEntityDTO();
        dto4.setItemNo("123");
        dto4.setBraidDirection("1");
        dto4.setBrandItemDirection("");
        list.add(dto4);

        TaskMaterialIssueSeqEntityDTO dto5 = new TaskMaterialIssueSeqEntityDTO();
        dto5.setItemNo("456");
        dto5.setBraidDirection("");
        dto5.setBrandItemDirection("2");
        list.add(dto5);
        Whitebox.invokeMethod(service, "filterList", record, list);


        record.setIsAngle(Constant.FLAG_N);
        record.setDirFlagSame(Constant.FLAG_N);
        Whitebox.invokeMethod(service, "filterList", record, list);
        Assert.assertEquals(5, list.size());

        List<PolarItemInfo> polarItemInfos = new ArrayList<>();
        polarItemInfos.add(new PolarItemInfo(){{
            setItemNo("123");
            setMultiBrandDirFlag(true);
        }});
        polarItemInfos.add(new PolarItemInfo(){{
            setItemNo("456");
            setMultiBrandDirFlag(false);
        }});
        PowerMockito.when(BasicsettingRemoteService.queryDirectionByItemNo(Mockito.anyList())).thenReturn(polarItemInfos);
        Whitebox.invokeMethod(service, "filterList", record, list);
        Assert.assertEquals(5, list.size());
    }

    @Test
    public void filterList1() throws Exception {
        TaskMaterialIssueSeqEntityDTO record = new TaskMaterialIssueSeqEntityDTO();
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();

        record.setIsAngle(Constant.FLAG_Y);
        record.setDirFlagSame(Constant.FLAG_Y);
        list.clear();
        addTestData(list);
        Whitebox.invokeMethod(service, "filterList", record, list);

        record.setIsAngle(Constant.FLAG_Y);
        record.setDirFlagSame(Constant.FLAG_N);
        list.clear();
        addTestData(list);
        Whitebox.invokeMethod(service, "filterList", record, list);

        record.setIsAngle(Constant.FLAG_N);
        record.setDirFlagSame(Constant.FLAG_Y);
        list.clear();
        addTestData(list);
        Whitebox.invokeMethod(service, "filterList", record, list);

        record.setIsAngle(Constant.FLAG_N);
        record.setDirFlagSame(Constant.FLAG_N);
        list.clear();
        addTestData(list);
        Whitebox.invokeMethod(service, "filterList", record, list);

        Assert.assertEquals(5, list.size());
    }

    private void addTestData(List<TaskMaterialIssueSeqEntityDTO> list) {
        TaskMaterialIssueSeqEntityDTO dto1 = new TaskMaterialIssueSeqEntityDTO();
        dto1.setBraidDirection("");
        dto1.setBrandItemDirection("");
        list.add(dto1);

        TaskMaterialIssueSeqEntityDTO dto2 = new TaskMaterialIssueSeqEntityDTO();
        dto2.setBraidDirection("1");
        dto2.setBrandItemDirection("1");
        list.add(dto2);

        TaskMaterialIssueSeqEntityDTO dto3 = new TaskMaterialIssueSeqEntityDTO();
        dto3.setBraidDirection("1");
        dto3.setBrandItemDirection("2");
        list.add(dto3);

        TaskMaterialIssueSeqEntityDTO dto4 = new TaskMaterialIssueSeqEntityDTO();
        dto4.setBraidDirection("1");
        dto4.setBrandItemDirection("");
        list.add(dto4);

        TaskMaterialIssueSeqEntityDTO dto5 = new TaskMaterialIssueSeqEntityDTO();
        dto5.setBraidDirection("");
        dto5.setBrandItemDirection("2");
        list.add(dto5);
    }

    @Test
    public void filterNullAngle() throws Exception {
        TaskMaterialIssueSeqEntityDTO record = new TaskMaterialIssueSeqEntityDTO();
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setBrandItemDirection(Constant.NULL_ANGLE);}});
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setBrandItemDirection("123");}});

        record.setDirFlag("Y");
        record.setDirFlagSame("N");
        record.setMultiBrand("Y");
        record.setIsAngle("");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

        record.setDirFlag("N");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

        record.setDirFlag("Y");
        record.setDirFlagSame("Y");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

        record.setDirFlagSame("N");
        record.setMultiBrand("N");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

        record.setMultiBrand("Y");
        record.setIsAngle("Y");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

        record.setDirFlag("N");
        record.setDirFlagSame("Y");
        record.setMultiBrand("N");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "filterNullAngle", record, list));

    }

    @Test
    public void exportIssuanceInfo() throws Exception {
        TaskMaterialIssueSeqServiceImpl spy = PowerMockito.spy(new TaskMaterialIssueSeqServiceImpl());
        Page<TaskMaterialIssueSeqEntityDTO> page = new Page<>();
        page.setRows(new ArrayList<>());
        doReturn(page).when(spy).getIssuanceInfo(Mockito.any());
        try {
            spy.exportIssuanceInfo(new TaskMaterialIssueSeqEntityDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ISSUANCE_INFO_NULL, e.getMessage());
        }
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setProdplanId("123");}});
        page.setRows(list);
        try {
            spy.exportIssuanceInfo(new TaskMaterialIssueSeqEntityDTO());
        } catch (Exception e) {
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

        PowerMockito.doNothing().when(asyncExportFileCommonService).uploadFileThenClearLocalFileUpdateLog(Mockito.any(), Mockito.any());
        try {
            spy.exportIssuanceInfo(new TaskMaterialIssueSeqEntityDTO());
        } catch (Exception e) {
        }
    }

    @Test
    public void countExportTotal() throws Exception{
        try {
            service.countExportTotal(new TaskMaterialIssueSeqEntityDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_DATA_EXCEPTION, e.getMessage());
        }
        TaskMaterialIssueSeqServiceImpl spy = PowerMockito.spy(new TaskMaterialIssueSeqServiceImpl());
        Page<TaskMaterialIssueSeqEntityDTO> page = new Page<>();
        page.setRows(new ArrayList<>());
        doReturn(page).when(spy).getIssuanceInfo(Mockito.any());
        spy.countExportTotal(new TaskMaterialIssueSeqEntityDTO());

        doReturn(null).when(spy).getIssuanceInfo(Mockito.any());
        int result = spy.countExportTotal(new TaskMaterialIssueSeqEntityDTO());
        Assert.assertTrue(result == 0);
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setProdplanId("123");}});
        page.setRows(list);
        doReturn(page).when(spy).getIssuanceInfo(Mockito.any());
        int result1 = spy.countExportTotal(new TaskMaterialIssueSeqEntityDTO());
        Assert.assertTrue(result1 >= 0);
    }

    @Test
    public void queryExportData() throws Exception{
        try {
            service.queryExportData(new TaskMaterialIssueSeqEntityDTO(), 1, 10);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_DATA_EXCEPTION, e.getMessage());
        }
        TaskMaterialIssueSeqServiceImpl spy = PowerMockito.spy(new TaskMaterialIssueSeqServiceImpl());
        Page<TaskMaterialIssueSeqEntityDTO> page = new Page<>();
        page.setRows(new ArrayList<>());
        doReturn(page).when(spy).getIssuanceInfo(Mockito.any());
        try {
            spy.queryExportData(new TaskMaterialIssueSeqEntityDTO(), 1, 10);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ISSUANCE_INFO_NULL, e.getMessage());
        }
        List<TaskMaterialIssueSeqEntityDTO> list = new ArrayList<>();
        list.add(new TaskMaterialIssueSeqEntityDTO(){{setProdplanId("123");}});
        page.setRows(list);
        doReturn(page).when(spy).getIssuanceInfo(Mockito.any());
        Assert.assertNotNull(spy.queryExportData(new TaskMaterialIssueSeqEntityDTO(), 1, 10));
    }
}