package com.zte.application.impl;

import com.zte.application.PkCodeInfoService;
import com.zte.domain.model.PkCodeInfo;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.interfaces.dto.StCodeInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class ImesPDACommonServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ImesPDACommonServiceImpl service;

    @Mock
    private IscpRemoteService iscpRemoteService;

    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Test
    public void getItemBraidDirection() throws Exception {
        PkCodeInfo pkCodeResult = new PkCodeInfo();
        pkCodeResult.setSysLotCode("test123");
        pkCodeResult.setItemCode("test123");
        pkCodeResult.setSupplerCode("test123");
        List<ItemSplitInfoDTO> newItemSplitList = new ArrayList<>();
        PowerMockito.when(iscpRemoteService.getItemSplitInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(newItemSplitList);
        PowerMockito.when(pkCodeInfoService.getItemDirectionByPkCode(Mockito.any()))
                .thenReturn(pkCodeResult);
        service.getItemBraidDirection(pkCodeResult);
        ItemSplitInfoDTO itemSplitInfoDTO = new ItemSplitInfoDTO();
        newItemSplitList.add(itemSplitInfoDTO);
        service.getItemBraidDirection(pkCodeResult);
        itemSplitInfoDTO.setBraidDirection("0");
        List<StCodeInfoDTO> stCodeInfoList = new ArrayList<>();
        StCodeInfoDTO stCodeInfoDTO = new StCodeInfoDTO();
        PowerMockito.when(datawbRemoteService.getStCodeInfoList()).thenReturn(stCodeInfoList);
        service.getItemBraidDirection(pkCodeResult);
        stCodeInfoDTO.setCode("0");
        stCodeInfoDTO.setCodeDesc("0度");
        stCodeInfoList.add(stCodeInfoDTO);
        service.getItemBraidDirection(pkCodeResult);
        stCodeInfoDTO.setCode("1");
        service.getItemBraidDirection(pkCodeResult);
        pkCodeResult.setItemAngle("0");
        Assert.assertNotNull(service.getItemBraidDirection(pkCodeResult));
    }
}