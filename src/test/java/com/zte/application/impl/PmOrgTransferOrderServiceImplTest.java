package com.zte.application.impl;/* Started by AICoder, pid:kcfc0xaaaag2d101432c0910c58c6427371557af */

import com.zte.application.ZteLmsOrgTransInterService;
import com.zte.common.enums.OrgTransferOrderStatusEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.LinesideWarehouseInfo;
import com.zte.domain.model.MtlSecondaryInventories;
import com.zte.domain.model.PmOrgTransferOrder;
import com.zte.domain.model.PmOrgTransferOrderRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.erp.GetOnhandQtyDTO;
import com.zte.interfaces.dto.erp.GetOnhandQtyResultLineDTO;
import com.zte.interfaces.dto.storage.StoragePageDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({BasicsettingRemoteService.class, ProductionDeliveryRemoteService.class, CenterfactoryRemoteService.class, MESHttpHelper.class})
public class PmOrgTransferOrderServiceImplTest extends PowerBaseTestCase {
    @Mock
    ErpRemoteService erpRemoteService;

    @InjectMocks
    private PmOrgTransferOrderServiceImpl pmOrgTransferOrderService;

    @Mock
    private PmOrgTransferOrderRepository pmOrgTransferOrderRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZteLmsOrgTransInterService zteLmsOrgTransInterService;

    private static PmOrgTransferOrder getPmOrgTransferOrder(String number) {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");
        order.setSourceErpLocation("sourceErpLocation");
        order.setSourceErpOrg("10001");
        order.setTargetErpLocation("targetErpLocation");
        order.setTargetErpOrg("10002");
        order.setItemCode("itemCode");
        order.setTransferQuantity(new BigDecimal(number));
        return order;
    }

    private static List<LinesideWarehouseInfo> getLinesideWarehouseInfos() {
        List<LinesideWarehouseInfo> linesideWarehouseInfos = new ArrayList<>();
        LinesideWarehouseInfo sourceLinesideWarehouseInfo = new LinesideWarehouseInfo();
        sourceLinesideWarehouseInfo.setWarehouseCode("sourceWarehouse");
        linesideWarehouseInfos.add(sourceLinesideWarehouseInfo);
        LinesideWarehouseInfo targetLinesideWarehouseInfo = new LinesideWarehouseInfo();
        targetLinesideWarehouseInfo.setWarehouseName("targetWarehouse");
        linesideWarehouseInfos.add(targetLinesideWarehouseInfo);
        return linesideWarehouseInfos;
    }

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);


        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("billType");
        List<SysLookupTypesDTO> sysLookupTypesDTOS = Collections.singletonList(sysLookupTypesDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.ORG_TRANSFER_ORDER_TYPE)).thenReturn(sysLookupTypesDTOS);
    }

    @Test
    public void queryPageTest() throws Exception {

        Page<PmOrgTransferOrderDTO> page = new Page<>();

        PmOrgTransferOrder pmOrgTransferOrder = new PmOrgTransferOrder();
        List<PmOrgTransferOrder> rows = Collections.singletonList(pmOrgTransferOrder);
        when(pmOrgTransferOrderRepository.selectPage(page)).thenReturn(rows);

        List<SysLookupValuesDTO> sysLookupTypesDTOS = Collections.singletonList(new SysLookupValuesDTO());
        when(BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.ORG_TRANSFER_ORDER_TYPE)).thenReturn(sysLookupTypesDTOS);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = Collections.singletonList(new LinesideWarehouseInfo());
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = Collections.singletonMap("10349620", new HrmPersonInfoDTO());
        when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        PageRows<PmOrgTransferOrder> result = pmOrgTransferOrderService.queryPage(page);

        assertNotNull(result);
        assertEquals(1, result.getCurrent());
        assertEquals(0, result.getTotal());
        assertEquals(1, result.getRows().size());


        pmOrgTransferOrder.setCreatedBy("10349620");
        pmOrgTransferOrder.setLastUpdatedBy("10349620");
        pmOrgTransferOrder.setSubmitedBy("10349620");
        pmOrgTransferOrderService.queryPage(page);
    }

    @Test
    public void getByIdTest() {
        String id = "123";
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        when(pmOrgTransferOrderRepository.selectById(id)).thenReturn(order);

        PmOrgTransferOrder result = pmOrgTransferOrderService.getById(id);

        assertNotNull(result);
        assertEquals(order, result);
    }

    @Test
    public void addTest() throws Exception {
        PmOrgTransferOrder order = getPmOrgTransferOrder("10");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.singletonList(wmsReportStockDTO);
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO);

        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        pmOrgTransferOrderService.add(order);
        verify(centerfactoryRemoteService, times(0)).getHrmPersonInfo(anyList()); // 验证centerfactoryRemoteService的getHrmPersonInfo方法未被调用
    }

    @Test
    public void addTest_DuplicateOrderNumber() throws Exception {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(1);

        Assert.assertThrows(MessageId.DOCUMENT_NO_IS_EXISTED, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
    }

    @Test(expected = MesBusinessException.class)
    public void addTest_InvalidBillType() throws Exception {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("invalidBillType");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        pmOrgTransferOrderService.add(order);
    }

    @Test
    public void addTest_WarehouseNotFound() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        ArrayList<LinesideWarehouseInfo> linesideWarehouseInfos = new ArrayList<>();
        LinesideWarehouseInfo sourceLinesideWarehouseInfo = new LinesideWarehouseInfo();
        sourceLinesideWarehouseInfo.setWarehouseCode("sourceWarehouse");
        linesideWarehouseInfos.add(sourceLinesideWarehouseInfo);
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(Collections.emptyList()).thenReturn(null).thenReturn(linesideWarehouseInfos);

        Assert.assertThrows(MessageId.NEVER_FIND_STORAGE_NAME, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        Assert.assertThrows(MessageId.NEVER_FIND_STORAGE_NAME, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        Assert.assertThrows(MessageId.NEVER_FIND_STORAGE_NAME, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
    }

    @Test(expected = MesBusinessException.class)
    public void addTest_SameSourceAndTargetInventories() throws Exception {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");
        order.setSourceErpLocation("sourceErpLocation");
        order.setSourceErpOrg("10001");
        order.setTargetErpLocation("sourceErpLocation");
        order.setTargetErpOrg("10001");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        pmOrgTransferOrderService.add(order);
    }

    @Test
    public void addTest_SourceInventoryNotFound() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");
        order.setSourceErpLocation("sourceErpLocation");
        order.setSourceErpOrg("10001");
        order.setTargetErpLocation("targetErpLocation");
        order.setTargetErpOrg("10002");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(Collections.emptyList()).thenReturn(null);

        Assert.assertThrows(MessageId.ORG_ID_INCONSISTENT, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        Assert.assertThrows(MessageId.ORG_ID_INCONSISTENT, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
    }

    @Test
    public void addTest_TargetInventoryNotFound() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");
        order.setSourceErpLocation("sourceErpLocation");
        order.setSourceErpOrg("10001");
        order.setTargetErpLocation("targetErpLocation");
        order.setTargetErpOrg("10002");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> sourceSecondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(sourceInventories)).thenReturn(sourceSecondaryInventories);

        MtlSecondaryInventories targetInventories = new MtlSecondaryInventories();
        targetInventories.setSecondaryInventoryName("targetErpLocation");
        targetInventories.setOrganizationId(new BigDecimal("10002"));
        when(BasicsettingRemoteService.getMtlSecondaryInventories(targetInventories)).thenReturn(null).thenReturn(Collections.emptyList());

        Assert.assertThrows(MessageId.ORG_ID_INCONSISTENT, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        Assert.assertThrows(MessageId.ORG_ID_INCONSISTENT, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
    }

    @Test
    public void addTest_ItemCodeNotFound() throws Exception {
        PmOrgTransferOrder order = getPmOrgTransferOrder("10");
        order.setItemCode(null);

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);
//
//        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
//        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.emptyList();
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO).thenReturn(new StoragePageDTO<>());

        pmOrgTransferOrderService.add(order);
        order.setItemCode("itemCode");
        Assert.assertThrows(MessageId.STORAGE_CENTER_RETURN_NULL, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        Assert.assertThrows(MessageId.STORAGE_CENTER_RETURN_NULL, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        verify(centerfactoryRemoteService, times(0)).getHrmPersonInfo(anyList()); // 验证centerfactoryRemoteService的getHrmPersonInfo方法未被调用
    }

    @Test
    public void addTest_TransferQuantityExceedsStock() throws Exception {
        PmOrgTransferOrder order = getPmOrgTransferOrder("200");

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(0);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.singletonList(wmsReportStockDTO);
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<WmsReportStockDTO>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO);

        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        Assert.assertThrows(MessageId.QTY_ERROR, MesBusinessException.class, () -> pmOrgTransferOrderService.add(order));
        verify(centerfactoryRemoteService, times(0)).getHrmPersonInfo(anyList()); // 验证centerfactoryRemoteService的getHrmPersonInfo方法未被调用
    }

    @Test
    public void updateByIdTest() throws Exception {
        PmOrgTransferOrder order = getPmOrgTransferOrder("10");
        order.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());

        PmOrgTransferOrderDTO queryDTO = PmOrgTransferOrderDTO.builder().build();
        queryDTO.setTransferOrderNumber("orderNumber");
        when(pmOrgTransferOrderRepository.selectCount(any())).thenReturn(1);

        List<PmOrgTransferOrder> rows = Collections.singletonList(order);
        when(pmOrgTransferOrderRepository.selectPage(any())).thenReturn(rows);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.singletonList(wmsReportStockDTO);
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<WmsReportStockDTO>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO);

        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        pmOrgTransferOrderService.updateById(order);
        verify(centerfactoryRemoteService, times(0)).getHrmPersonInfo(anyList()); // 验证centerfactoryRemoteService的getHrmPersonInfo方法未被调用
    }

    @Test(expected = MesBusinessException.class)
    public void updateByIdTest_NonDraftStatus() throws Exception {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber("orderNumber");
        order.setBillStatus(OrgTransferOrderStatusEnum.SUBMITTED.getStatus());

        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(1);
        when(pmOrgTransferOrderRepository.selectPage(any(Page.class))).thenReturn(Collections.singletonList(order));

        PmOrgTransferOrder updatedOrder = new PmOrgTransferOrder();
        updatedOrder.setTransferOrderNumber("orderNumber");

        pmOrgTransferOrderService.updateById(updatedOrder);
    }

    @Test(expected = MesBusinessException.class)
    public void updateByIdTest_OrderNotFound() throws Exception {
        PmOrgTransferOrder updatedOrder = new PmOrgTransferOrder();
        updatedOrder.setTransferOrderNumber("orderNumber");

        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(0);

        pmOrgTransferOrderService.updateById(updatedOrder);
    }

    @Test
    public void deleteByIdsTest() {
        List<String> ids = Collections.singletonList("123");
        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);
        when(pmOrgTransferOrderRepository.deleteByIds(ids, "empNo")).thenReturn(1L);

        PmOrgTransferOrder pmOrgTransferOrder = new PmOrgTransferOrder();
        pmOrgTransferOrder.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());
        when(pmOrgTransferOrderRepository.selectById("123")).thenReturn(pmOrgTransferOrder);
        pmOrgTransferOrderService.deleteByIds(ids);
        pmOrgTransferOrder.setBillStatus(OrgTransferOrderStatusEnum.SUBMITTED.getStatus());

        Assert.assertThrows(MessageId.DATA_REPAIR_STATUS_IS_NOT_FICTION, MesBusinessException.class, () -> pmOrgTransferOrderService.deleteByIds(ids));

        verify(pmOrgTransferOrderRepository, times(1)).deleteByIds(ids, "empNo");
    }

    @Test
    public void createBillNoTest() throws Exception {
        CfBizSequenceDTO cfBizSequenceDTO = new CfBizSequenceDTO();
        cfBizSequenceDTO.setBizCode(Constant.XBZZZ);
        cfBizSequenceDTO.setInterval(StringUtils.EMPTY);
        cfBizSequenceDTO.setLength(4);
        cfBizSequenceDTO.setBillCount(1);

        List<String> cfBizCode = Collections.singletonList("billNo");
        when(centerfactoryRemoteService.createCfBizCode(cfBizSequenceDTO)).thenReturn(cfBizCode);

        String result = pmOrgTransferOrderService.createBillNo();

        assertEquals("billNo", result);
    }

    @Test
    public void submitTest() throws Exception {
        String transferOrderNumber = "orderNumber";

        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber(transferOrderNumber);
        order.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());
        order.setBillType("billType");
        order.setSourceWarehouse("sourceWarehouse");
        order.setTargetWarehouse("targetWarehouse");
        order.setSourceErpLocation("sourceErpLocation");
        order.setSourceErpOrg("10001");
        order.setTargetErpLocation("targetErpLocation");
        order.setTargetErpOrg("10002");
        order.setItemCode("itemCode");
        order.setTransferQuantity(new BigDecimal("10"));

        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(1);
        when(pmOrgTransferOrderRepository.selectPage(any(Page.class))).thenReturn(Collections.singletonList(order));

        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        when(pmOrgTransferOrderRepository.updateSelectiveById(any(PmOrgTransferOrder.class))).thenReturn(1L);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.singletonList(wmsReportStockDTO);
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO);

        pmOrgTransferOrderService.submit(transferOrderNumber);

        verify(pmOrgTransferOrderRepository, times(1)).updateSelectiveById(any(PmOrgTransferOrder.class));
        verify(zteLmsOrgTransInterService, times(1)).write(any(ZteLmsOrgTransInterDTO.class));
    }

    @Test
    public void submitTest_noItem() throws Exception {
        String transferOrderNumber = "orderNumber";

        PmOrgTransferOrder order = getPmOrgTransferOrder("10");
        order.setItemCode(null);
        order.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());
        order.setTransferQuantity(null);


        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(1);
        when(pmOrgTransferOrderRepository.selectPage(any(Page.class))).thenReturn(Collections.singletonList(order));

        Map<String, String> headers = Collections.singletonMap(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "empNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        when(pmOrgTransferOrderRepository.updateSelectiveById(any(PmOrgTransferOrder.class))).thenReturn(1L);

        List<LinesideWarehouseInfo> linesideWarehouseInfos = getLinesideWarehouseInfos();
        when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);

        MtlSecondaryInventories sourceInventories = new MtlSecondaryInventories();
        sourceInventories.setSecondaryInventoryName("sourceErpLocation");
        sourceInventories.setOrganizationId(new BigDecimal("10001"));
        List<MtlSecondaryInventories> secondaryInventories = Collections.singletonList(sourceInventories);
        when(BasicsettingRemoteService.getMtlSecondaryInventories(any(MtlSecondaryInventories.class))).thenReturn(secondaryInventories);

        WmsReportStockDTO wmsReportStockDTO = new WmsReportStockDTO();
        wmsReportStockDTO.setQty(new BigDecimal("20"));
        List<WmsReportStockDTO> wmsReportStockDTOS = Collections.singletonList(wmsReportStockDTO);
        StoragePageDTO<WmsReportStockDTO> resultDTO = new StoragePageDTO<>();
        resultDTO.setRecords(wmsReportStockDTOS);
        when(ProductionDeliveryRemoteService.queryStockByWarehouseId(any(StorageQueryPageDTO.class))).thenReturn(resultDTO);

        pmOrgTransferOrderService.submit(transferOrderNumber);

        resultDTO.setRecords(Collections.emptyList());
        order.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());
        Assert.assertThrows(MessageId.STORAGE_CENTER_RETURN_NULL, MesBusinessException.class, () -> pmOrgTransferOrderService.submit(transferOrderNumber));

        resultDTO.setRecords(null);
        order.setBillStatus(OrgTransferOrderStatusEnum.DRAFT.getStatus());
        Assert.assertThrows(MessageId.STORAGE_CENTER_RETURN_NULL, MesBusinessException.class, () -> pmOrgTransferOrderService.submit(transferOrderNumber));

//        verify(pmOrgTransferOrderRepository, times(1)).updateSelectiveById(any(PmOrgTransferOrder.class));
//        verify(zteLmsOrgTransInterService, times(1)).write(any(ZteLmsOrgTransInterDTO.class));
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_NonDraftStatus() throws Exception {
        String transferOrderNumber = "orderNumber";

        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setTransferOrderNumber(transferOrderNumber);
        order.setBillStatus(OrgTransferOrderStatusEnum.SUBMITTED.getStatus());

        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(1);
        when(pmOrgTransferOrderRepository.selectPage(any(Page.class))).thenReturn(Collections.singletonList(order));

        pmOrgTransferOrderService.submit(transferOrderNumber);
    }

    @Test(expected = MesBusinessException.class)
    public void submitTest_OrderNotFound() throws Exception {
        String transferOrderNumber = "orderNumber";

        when(pmOrgTransferOrderRepository.selectCount(any(PmOrgTransferOrderDTO.class))).thenReturn(0);

        pmOrgTransferOrderService.submit(transferOrderNumber);
    }

    @Test
    public void countExportTotalTest() {
        PmOrgTransferOrderDTO query = PmOrgTransferOrderDTO.builder().build();
        int expectedCount = 10;
        when(pmOrgTransferOrderRepository.selectCount(query)).thenReturn(expectedCount);

        Page<PmOrgTransferOrderDTO> page = new Page<>();
        page.setParams(query);
        int result = pmOrgTransferOrderService.countExportTotal(page);

        assertEquals(expectedCount, result);
    }

    @Test
    public void queryExportDataTest() {
        int pageNo = 1;
        int pageSize = 10;

        List<PmOrgTransferOrder> expectedOrders = Collections.singletonList(new PmOrgTransferOrder());
        when(pmOrgTransferOrderRepository.selectPage(any(Page.class))).thenReturn(expectedOrders);

        List<PmOrgTransferOrder> result = pmOrgTransferOrderService.queryExportData(new Page<>(), pageNo, pageSize);

        assertEquals(1, result.size());
    }

    @Test
    public void testGetOnhandQty() throws Exception {
        when(erpRemoteService.getOnhandQty(any(GetOnhandQtyDTO.class))).thenReturn(Collections.singletonList(new GetOnhandQtyResultLineDTO()));
        List<GetOnhandQtyResultLineDTO> result = pmOrgTransferOrderService.getOnhandQty(new GetOnhandQtyDTO());
        assertEquals(Collections.singletonList(new GetOnhandQtyResultLineDTO()), result);

    }
}

/* Ended by AICoder, pid:kcfc0xaaaag2d101432c0910c58c6427371557af */