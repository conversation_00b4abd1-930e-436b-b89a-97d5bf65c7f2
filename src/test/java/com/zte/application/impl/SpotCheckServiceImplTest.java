package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.interfaces.assembler.SpotCheckHeadAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.any;

@PrepareForTest({SpotCheckHeadAssembler.class})
public class SpotCheckServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    SpotCheckServiceImpl service;
    @Mock
    private SpotCheckHeadRepository spotCheckHeadRepository;
    @Mock
    private SpotCheckDetailRepository spotCheckDetailRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
	@Mock
	private HttpServletResponse response;
    @InjectMocks
    StandardModeCommonScanServiceImpl service1;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private WipScanHisExtraRepository wipScanHisExtraRepository;

    @Test
    public void insertPsScanHistoryByScan(){
        List<PsScanHistory> infoList = new ArrayList<>();
        PsScanHistory dto = new PsScanHistory();
        PsEntityPlanBasicDTO dto1 = new PsEntityPlanBasicDTO();
        PsWipInfo dto2 = new PsWipInfo();
        dto2.setSn("Ssss");
        dto1.setWorkOrderNo("@222");
        dto.setSn("24123");
        infoList.add(dto);
        FlowControlInfoDTO record = new FlowControlInfoDTO();
        record.setSourceImu(new BigDecimal(0));
        record.setEntityPlanBasic(dto1);
        record.setWipInfo(dto2);
        record.setIsWriteBackScan("N");
        PowerMockito.when(psScanHistoryRepository.insertPsScanHistorySelective(any())).thenReturn(0);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(any())).thenReturn(0);
        service1.insertPsScanHistoryByScan(record);
        record.setIsWriteBackScan("Y");
        Assert.assertNotNull(service1.insertPsScanHistoryByScan(record));
    }

    @Test
    public void saveWipInfoIsNotEmpty(){
        List<PsScanHistory> historyList = new ArrayList<>();
        List<PsWipInfo> wipList = new ArrayList<>();
        List<PsScanHistory> historyListForUpdate = new ArrayList<>();
        List<WipTestRecord> testRecodeList = new ArrayList<>();
        PsScanHistory dto = new PsScanHistory();
        dto.setSn("ssss");
        dto.setIsWriteBackScan("N");
        historyList.add(dto);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(any())).thenReturn(0);
        PowerMockito.when(psScanHistoryRepository.insertPsScanHistoryBatch(any())).thenReturn(0);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service1.saveWipInfoIsNotEmpty(wipList,historyList,historyListForUpdate,testRecodeList);
    }

    @Test
    public void getHeadPage() throws Exception {
        SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try {
            service.getHeadPage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }
        dto.setProdplanId("ProdplanId");
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHeadCount(Mockito.any())).thenReturn(1L);
        List<SpotCheckHead> spotCheckHeads = new ArrayList<>();
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        Assert.assertNotNull(service.getHeadPage(dto));
    }

    @Test
    public void getSpotCheck() throws Exception {
        SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try {
            service.getSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }
        dto.setHeadId("HeadId");
        SpotCheckHead spotCheckHead = new SpotCheckHead();
        List<SpotCheckDetail> spotCheckDetails = new ArrayList<>();
        PowerMockito.when(spotCheckHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(spotCheckHead);
        PowerMockito.when(spotCheckDetailRepository.selectByHeadId(Mockito.any())).thenReturn(spotCheckDetails);
        Assert.assertNotNull(service.getSpotCheck(dto));
    }

    @Test
    public void createSpotCheck() throws Exception {
        SpotCheckDTO dto = new SpotCheckDTO();
        try {
            service.createSpotCheck(dto);
        } catch (Exception e) {
            if(!RetCode.VALIDATIONERROR_MSGID.equals(e.getMessage())){
                throw e;
            }
        }
        SpotCheckHeadDTO head = new SpotCheckHeadDTO();
        head.setQty(11L);
        dto.setHead(head);
        List<SpotCheckDetailDTO> details = new ArrayList<>();
        SpotCheckDetailDTO spotCheckDetailDTO = new SpotCheckDetailDTO();
        spotCheckDetailDTO.setSn("1111");
        details.add(spotCheckDetailDTO);
        dto.setDetails(details);
        FlowControlConditionDTO flow = new FlowControlConditionDTO();
        dto.setFlow(flow);
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1111");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfos);
        service.createSpotCheck(dto);

        head.setQty(-1L);
        try {
            service.createSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOX_CONTENTS_CODE_QTY_ERROR, e.getMessage());
        }

        head.setQty(2L);
        PowerMockito.mockStatic(SpotCheckHeadAssembler.class);
        Assert.assertNotNull(service.createSpotCheck(dto));
    }

	@Test
	public void exportExcel() throws Exception {
		SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.CONDITION_NOT_FOUND, e.getMessage());
        }
		dto.setProdplanId("345");
		dto.setLineCode("23");

		List<SpotCheckDetail> spotCheckDetails = new LinkedList<>();
		SpotCheckDetail a1 = new SpotCheckDetail();
		spotCheckDetails.add(a1);
		PowerMockito.when(spotCheckDetailRepository.getSpotCheckDetail(Mockito.any()))
				.thenReturn(spotCheckDetails);

		service.exportExcel(response, dto);
	}

    @Test
    public void getQcByLpn() throws Exception {
        try {
            service.getQcByLpn("");
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHeadCount(Mockito.any())).thenReturn(1L);
        List<SpotCheckHead> spotCheckHeads = new ArrayList<>();
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        try {
            service.getQcByLpn("111");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOX_HAS_NOT_QC, e.getMessage());
        }

        SpotCheckHead spotCheckHead = new SpotCheckHead();
        spotCheckHeads.add(spotCheckHead);
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        try {
            service.getQcByLpn("");
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        spotCheckHead.setSamplingResult("0");
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        service.getQcByLpn("111");

        spotCheckHead.setSamplingResult("1");
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        try {
            service.getQcByLpn("11");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOX_QC_FAIL, e.getMessage());
        }
    }

}