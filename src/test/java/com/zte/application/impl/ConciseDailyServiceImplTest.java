package com.zte.application.impl;

import com.zte.application.TaskDailyStatDetailService;
import com.zte.application.TaskDailyStatHeadService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.CommonUtils;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.TaskDailyStatDetail;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.ConciseDailyDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class})
public class ConciseDailyServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ConciseDailyServiceImpl service;
    @Mock
    private WipScanHistoryService wipScanHistoryService;
    @Mock
    private TaskDailyStatDetailService taskDailyStatDetailService;
    @Mock
    private TaskDailyStatHeadService taskDailyStatHeadService;
//    @Mock
//    private PlanscheduleRemoteService planscheduleRemoteService;
    @Test
    public void handleScheduledTask() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.CONCISE_DAILY_SCHEDULED_TASK_FAILED);
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        Integer preDay = null;
        try{
            service.handleScheduledTask(conciseDailyDTO,preDay);
        }catch (Exception e){
            Assert.assertEquals( MessageId.CONCISE_DAILY_SCHEDULED_TASK_FAILED, e.getMessage());
        }

        preDay = -1;
        service.handleScheduledTask(conciseDailyDTO,preDay);
        preDay = 3;
        List<String> prodPlanIds = new ArrayList<>();
        prodPlanIds.add("123456798");
        prodPlanIds.add("123456798");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
        service.handleScheduledTask(conciseDailyDTO,preDay);
        List<PsTask> psTaskList = new ArrayList<>();
//        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDaily(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(psTaskList);
    }

    @Test
    public void computeOnhandQty() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        Map<String, Map<String, Long>> crossList = new HashMap<>();
        Map<String, Long> processList = new HashMap<>();
        processList.put("test123",0L);
        Map<String, Integer> prodToSubmitItemMap = new HashMap<>();
        prodToSubmitItemMap.put("test123",1);
        String[] params = new String[] { "P0003","TEST123" };
        conciseDailyDTO.setProdToCrossHistoryCountMap(crossList);
        conciseDailyDTO.setProdToWipInfoCountMap(crossList);
        conciseDailyDTO.setProdToSubmitItemMap(prodToSubmitItemMap);
        conciseDailyDTO.setProdFromScrapOrHistoryCountMap(crossList);

        Map<String, Set<String>> prodToFirstProcessSet = new HashMap<>();
        prodToFirstProcessSet.put("test123",new HashSet<>());
        conciseDailyDTO.setProdToFirstProcessSet(prodToFirstProcessSet);
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        crossList.put("test123",new HashMap<>());
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        Set<String> firstProcessSet = new HashSet<>();
        firstProcessSet.add("test123");
        prodToFirstProcessSet.put("test123",firstProcessSet);
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeOnhandQty(conciseDailyDTO,"test123",params,new HashMap<>());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void computeTurnOutQty() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        Map<String, Map<String, Long>> crossList = new HashMap<>();
        Map<String, Long> processList = new HashMap<>();
        processList.put("test123",0L);
        Map<String, Integer> prodToSubmitItemMap = new HashMap<>();
        prodToSubmitItemMap.put("test123",1);

        conciseDailyDTO.setProdToCrossHistoryCountMap(crossList);
        conciseDailyDTO.setProdToTransmitHistoryCountMap(crossList);
        conciseDailyDTO.setProdToSubmitItemMap(prodToSubmitItemMap);
        conciseDailyDTO.setProdFromScrapOrHistoryCountMap(crossList);
        conciseDailyDTO.setProdToScrapOrHistoryCountMap(crossList);

        Map<String, Set<String>> prodToFirstProcessSet = new HashMap<>();
        prodToFirstProcessSet.put("test123",new HashSet<>());
        conciseDailyDTO.setProdToLastProcessSet(prodToFirstProcessSet);
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","","","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","test123","test123","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        crossList.put("test123",new HashMap<>());
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","test123","test123","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        Set<String> firstProcessSet = new HashSet<>();
        firstProcessSet.add("test123");
        prodToFirstProcessSet.put("test123",firstProcessSet);
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","test123","test124","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","test234","test124","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","P0003","test124","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnOutQty(conciseDailyDTO,"test123","P0007","test124","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void computeTurnIntoQty() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        Map<String, Map<String, Long>> crossList = new HashMap<>();
        Map<String, Long> processList = new HashMap<>();
        processList.put("test123",0L);
        conciseDailyDTO.setProdToCrossHistoryCountMap(crossList);
        conciseDailyDTO.setProdToTransmitHistoryCountMap(crossList);
        conciseDailyDTO.setProdToScrapOrHistoryCountMap(crossList);

        Map<String, Set<String>> prodToFirstProcessSet = new HashMap<>();
        prodToFirstProcessSet.put("test123",new HashSet<>());
        conciseDailyDTO.setProdToFirstProcessSet(prodToFirstProcessSet);
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","","");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","test123","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        crossList.put("test123",new HashMap<>());
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","test123","test123");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        Set<String> firstProcessSet = new HashSet<>();
        firstProcessSet.add("test123");
        prodToFirstProcessSet.put("test123",firstProcessSet);
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","test123","test124");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","test234","test124");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.computeTurnIntoQty(conciseDailyDTO,"test123","P0003","test124");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void handleProdScrapOrMaintenceHistory() {
        List<WipScanHistoryDTO> turnList = new ArrayList<>();
        PowerMockito.when(wipScanHistoryService.countQtyForScrapOrMaintence(Mockito.any(),Mockito.anyList(),Mockito.anyBoolean())).thenReturn(turnList);
        try{
            service.handleProdScrapOrMaintenceHistory(new Date(),new ArrayList<>(),true);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        WipScanHistoryDTO wipScanHistoryDTO = new WipScanHistoryDTO();
        turnList.add(wipScanHistoryDTO);
        wipScanHistoryDTO.setAttribute1("tesst123");
        WipScanHistoryDTO wipScanHistoryDTO1 = new WipScanHistoryDTO();
        turnList.add(wipScanHistoryDTO1);
        wipScanHistoryDTO1.setAttribute1("tesst123");
        try{
            service.handleProdScrapOrMaintenceHistory(new Date(),new ArrayList<>(),true);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.handleProdScrapOrMaintenceHistory(new Date(),new ArrayList<>(),false);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void findFirstAndLastProcess() {
        String[] processArr = new String[]{"P0003","123","test123"};
        SysLookupTypesDTO sysEntity = new SysLookupTypesDTO();
        Map<String, Integer> craftBalanceMap = new HashMap<>();
        craftBalanceMap.put("test123",1);
        try{
            service.findFirstAndLastProcess(null,craftBalanceMap,sysEntity);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.findFirstAndLastProcess(processArr,craftBalanceMap,sysEntity);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void handleCraftBalance() {
        SysLookupTypesDTO sysEntity = new SysLookupTypesDTO();
        Map<String, String> craftBalanceMap = new HashMap<>();
        Map<String, Object> entityMap = new HashMap<>();
        sysEntity.setDescriptionEngV("smt");
        craftBalanceMap.put("smt","smtBalance");
        entityMap.put("smtBalance",0L);
        entityMap.put("smtD",0L);
        try{
            service.handleCraftBalance(sysEntity,craftBalanceMap,entityMap, "smt");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        try{
            service.handleCraftBalance(sysEntity,craftBalanceMap,entityMap, "dip");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        entityMap.remove("smtBalance");
        try{
            service.handleCraftBalance(sysEntity,craftBalanceMap,entityMap, "dip");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void setLineMatchProcess() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        conciseDailyDTO.setProdProcessToLineMap(new HashMap<>());
        String[] processArr = new String[]{"1"};
        try{
            service.setLineMatchProcess(conciseDailyDTO,"123",processArr,new TaskDailyStatDetail());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        Map<String, String> processMatchLineMap = new HashMap<>();
        processMatchLineMap.put("1","1");
        Map<String,Map<String, String>> processMatchMap = new HashMap<>();
        processMatchMap.put("123",processMatchLineMap);
        conciseDailyDTO.setProdProcessToLineMap(processMatchMap);
        try{
            service.setLineMatchProcess(conciseDailyDTO,"123",processArr,new TaskDailyStatDetail());
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void matchLineForProcessOfWorkList() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        try{
            service.matchLineForProcessOfWorkList(conciseDailyDTO,workOrderList);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        PsWorkOrderBasic entity = new PsWorkOrderBasic();
        entity.setProcessGroup("1");
        entity.setLineCode("123");
        entity.setSourceTask("123");
        workOrderList.add(entity);
        PsWorkOrderBasic entity1 = new PsWorkOrderBasic();
        entity1.setProcessGroup("");
        entity1.setLineCode("123");
        entity1.setSourceTask("123");
        workOrderList.add(entity1);
        try{
            service.matchLineForProcessOfWorkList(conciseDailyDTO,workOrderList);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void getLineNameFromCode() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<TaskDailyStatDetail> detailList = new ArrayList<>();
        try{
            service.getLineNameFromCode(detailList);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        TaskDailyStatDetail taskDailyStatDetail = new TaskDailyStatDetail();
        taskDailyStatDetail.setLineCode("");
        detailList.add(taskDailyStatDetail);
        try{
            service.getLineNameFromCode(detailList);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        TaskDailyStatDetail taskDailyStatDetail1 = new TaskDailyStatDetail();
        taskDailyStatDetail1.setLineCode("test123");
        detailList.add(taskDailyStatDetail1);

        PowerMockito.when( BasicsettingRemoteService.getLineNameByCodeList(Mockito.anyList())).thenReturn(new HashMap<>());
        try{
            service.getLineNameFromCode(detailList);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }

    }

    @Test
    public void putLineName() {
        TaskDailyStatDetail entity = new TaskDailyStatDetail();
        entity.setLineCode("test123,test124");
        Map<String, String> mapCodeToName = new HashMap<>();
        mapCodeToName.put("test123","test123");
        try{
            service.putLineName(entity,mapCodeToName,new HashMap<>(),"");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        entity.setLineCode("");
        try{
            service.putLineName(entity,mapCodeToName,new HashMap<>(),"");
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
}