package com.zte.application.impl;

import com.zte.application.ParentsnAndSnService;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.interfaces.dto.ParentsnAndSnDTO;
import com.zte.interfaces.dto.PmGenerateSnTransDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class WipEntityScanInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    WipEntityScanInfoServiceImpl service;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private ParentsnAndSnService parentsnAndSnService;

    @Test
    public void generateSnTrans() throws Exception {
        PmGenerateSnTransDTO dto = new PmGenerateSnTransDTO();
        dto.setFactoryId(new BigDecimal(52));
        List<PsWipInfoDTO> wipInfoDTOS = new ArrayList<>();
        PsWipInfoDTO psWipInfo = new PsWipInfoDTO();
        wipInfoDTOS.add(psWipInfo);
        dto.setWipInfoDTOS(wipInfoDTOS);
        List<ParentsnAndSnDTO> parentsnAndSnDTOS = new ArrayList<>();
        ParentsnAndSnDTO parentsnAndSn = new ParentsnAndSnDTO();
        parentsnAndSn.setSn("11");
        parentsnAndSnDTOS.add(parentsnAndSn);
        dto.setParentsnAndSnDTOS(parentsnAndSnDTOS);
        PowerMockito.when(psWipInfoRepository.insertPsWipInfoBatch(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(new ArrayList());
        PowerMockito.when(parentsnAndSnService.batchInsert(Mockito.anyList())).thenReturn(1);
        service.generateSnTrans(dto);

        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfos.add(psWipInfo1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(psWipInfos);
        service.generateSnTrans(dto);
        try{
            service.generateSnTrans(dto);
        }catch (Exception e){
        }

        psWipInfo1.setCurrProcessCode("CurrProcessCode11");
        psWipInfo.setCurrProcessCode("CurrProcessCode");
        service.generateSnTrans(dto);
        try{
            service.generateSnTrans(dto);
        }catch (Exception e){
        }

        psWipInfo1.setLastProcess("Y");
        try{
            Assert.assertNotNull(service.generateSnTrans(dto));
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}