package com.zte.application.impl;

import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.SqlUtils;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanscheduleRemoteService.class, SqlUtils.class, CrafttechRemoteService.class})
public class ProdBindingSettingServiceImplTest {
    @InjectMocks
    private ProdBindingSettingServiceImpl service;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Test
    public void checkSubSnLockInfo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,SqlUtils.class,CrafttechRemoteService.class);
        List<PsWipInfo> subWipList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        psWipInfo.setRouteId("test123");
        subWipList.add(psWipInfo);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setRouteId("test123");
        psWorkOrderBasic.setWorkOrderNo("test123");
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.anyMap())).thenReturn(workOrderList);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.anyList())).thenReturn(ctRouteDetailDTOList);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(flowControlCommonService).snLockControl(any());
        try{
            service.checkSubSnLockInfo(subWipList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        workOrderList.add(psWorkOrderBasic);
        try{
            service.checkSubSnLockInfo(subWipList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        try{
            service.checkSubSnLockInfo(subWipList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void updateUsageCountForTypeTwo()throws Exception {
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getTypeTwoBindingInfo(Mockito.any())).thenReturn(list);
        String productCode = "";
        service.updateUsageCountForTypeTwo(productCode);
        Assert.assertNotNull(list);
    }

    @Test
    public void updateUsageCountForTypeTwo2()throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("TEST");
        prodBindingSettingDTO.setItemCode("TEST");
        list.add(prodBindingSettingDTO);
        PowerMockito.when(prodBindingSettingRepository.getTypeTwoBindingInfo(Mockito.any())).thenReturn(list);
        List<CfInventoryDTO> usageCountList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getTaskAndItemUsageCountByProCodeAndItemNo(Mockito.any())).thenReturn(usageCountList);
        String productCode = "";
        service.updateUsageCountForTypeTwo(productCode);
        Assert.assertNotNull(prodBindingSettingDTO);
        CfInventoryDTO cfInventoryDTO = new CfInventoryDTO();
        cfInventoryDTO.setItemNo("TEST2");
        cfInventoryDTO.setUsageCount(Double.valueOf("2"));
        usageCountList.add(cfInventoryDTO);
        CfInventoryDTO cfInventoryDTO1 = new CfInventoryDTO();
        cfInventoryDTO1.setItemNo("TEST1");
        cfInventoryDTO1.setUsageCount(Double.valueOf("2"));
        usageCountList.add(cfInventoryDTO1);
        PowerMockito.when(PlanscheduleRemoteService.getTaskAndItemUsageCountByProCodeAndItemNo(Mockito.any())).thenReturn(usageCountList);
        PowerMockito.when(prodBindingSettingRepository.batchUpdateUsageCount(Mockito.anyList())).thenReturn(1);
        service.updateUsageCountForTypeTwo(productCode);
        Assert.assertNotNull(prodBindingSettingDTO);

        cfInventoryDTO.setItemNo("TEST");
        PowerMockito.when(PlanscheduleRemoteService.getTaskAndItemUsageCountByProCodeAndItemNo(Mockito.any())).thenReturn(usageCountList);
        service.updateUsageCountForTypeTwo(productCode);
        Assert.assertNotNull(prodBindingSettingDTO);

        ArrayList<ProdBindingSettingDTO> prodBindingSettingDTOS = new ArrayList<>(1000);
        for (int i = 0; i < 1000; i++) {
            prodBindingSettingDTOS.add(prodBindingSettingDTO);
        }
        ArrayList<ProdBindingSettingDTO> prodBindingSettingDTOS1 = new ArrayList<>(10);
        for (int i = 0; i < 10; i++) {
            prodBindingSettingDTOS1.add(prodBindingSettingDTO);
        }
        PowerMockito.when(prodBindingSettingRepository.getTypeTwoBindingInfo(any())).thenReturn(prodBindingSettingDTOS).thenReturn(prodBindingSettingDTOS1);
        service.updateUsageCountForTypeTwo(productCode);
    }


    @Test
    public void getList()throws Exception {
        ProdBindingSettingDTO dtoForm = new ProdBindingSettingDTO();
        try{
            service.getList(dtoForm);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.PRODUCT_CODE_AND_TIME_IS_EMPTY);
        }
        dtoForm.setCreateDateEnd("2024-05-30 00:00:00");
        try{
            service.getList(dtoForm);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.PRODUCT_CODE_AND_TIME_IS_EMPTY);
        }
        dtoForm.setProductCode("test");
        dtoForm.setCreateDateStart("2022-05-30 00:00:00");
        try{
            service.getList(dtoForm);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.SETTING_DATE_CAN_NOT_EXCEED_A_YEAR);
        }
        dtoForm.setCreateDateStart("2024-05-10 00:00:00");
        List<ProdBindingSetting> list = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.queryList(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getList(dtoForm));
    }
}