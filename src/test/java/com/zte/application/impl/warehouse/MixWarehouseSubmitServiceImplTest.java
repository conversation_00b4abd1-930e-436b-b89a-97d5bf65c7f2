package com.zte.application.impl.warehouse;

import com.google.common.collect.Lists;
import com.zte.application.PmMachineWeightService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.warehouse.MixWarehouseSubmitDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.config.MixWarehouseSubmitLimit;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class,
        CrafttechRemoteService.class, ProductionDeliveryRemoteService.class, DatawbRemoteService.class})
public class MixWarehouseSubmitServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    MixWarehouseSubmitServiceImpl service;

    @Mock
    PsWipInfoService wipInfoService;

    @Mock
    WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    PmMachineWeightService pmMachineWeightService;

    @Mock
    WarehouseEntryDetailRepository detailRepository;

    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Test
    public void scanLpn() {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit() {{
                setMaxTaskCnt(2);
                setMaxPlanCnt(2);
                setMaxWipCnt(2);
            }});
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            dto.setScannedLpn("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            dto.setOrgId("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(any()))
                    .thenReturn(null);
            dto.setStockType("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(any()))
                    .thenReturn(Lists.newArrayList(new ContainerContentInfoDTO(){{setInEntityIdentification("1");}}));
            dto.setStockType("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            dto.setStockType("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                    .thenReturn(Lists.newArrayList(new PsWipInfo()));
            dto.setStockType("1");
            service.scanLpn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            dto.setWipSns(Lists.newArrayList("1"));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            dto.setOrgId("1");
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            dto.setStockType("1");
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(new PsWipInfo(){{setSn("1");}}));
        try {
            dto.setWipSns(Lists.newArrayList("1", "2"));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setAttribute1("1");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("2");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("3");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("4");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("5");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("6");}},
                        new PsWipInfo(){{setSn("1");setAttribute1("51");}}
                ));
        try {
            dto.setWipSns(Lists.newArrayList("1"));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}},
                        new PsWipInfo(){{setSn("1");setItemNo("2");}}
                ));
        try {
            dto.setWipSns(Lists.newArrayList("1"));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(null);
        try {
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit1() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                    .thenReturn(Lists.newArrayList(
                            new PsWipInfo(){{setSn("1");setItemNo("1");}}
                    ));
            PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                    .thenReturn(Lists.newArrayList(new PsTask(){{setOrgId(new BigDecimal(2));}}
                            ,new PsTask(){{setOrgId(new BigDecimal(2));}},new PsTask(){{setOrgId(new BigDecimal(2));}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit2() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                    .thenReturn(Lists.newArrayList(
                            new PsWipInfo(){{setSn("1");setItemNo("1");}}
                    ));
            PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                    .thenReturn(Lists.newArrayList(new PsTask(){{setOrgId(new BigDecimal(2));}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit3() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("STEP");
                }}));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit4() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                    setProductType(MpConstant.TASK_TYPE_ONLINE_RETURN);
                }}));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit5() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(Lists.newArrayList("1"));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any())).thenReturn(null);
        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(null);
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit6() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any())).thenReturn(null);
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit7() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any()))
                .thenReturn(Lists.newArrayList(new CtRouteHead(){{setRouteId("1");setRouteCodeDetail("1");}}));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit8() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");setWorkStation("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any()))
                .thenReturn(Lists.newArrayList(new CtRouteHead(){{setRouteId("1");setRouteCodeDetail("1");}}));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");}}
                ));
        try {
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");setCurrProcessCode("1");}}
                ));
        try {
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit9() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any()))
                .thenReturn(Lists.newArrayList(new CtRouteHead(){{setRouteId("1");setRouteCodeDetail("1");}}));
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");setCurrProcessCode("1");}}
                ));
        try {
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit10() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setAttribute1("1");setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");setCurrProcessCode("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any()))
                .thenReturn(Lists.newArrayList(new CtRouteHead(){{setRouteId("1");setRouteCodeDetail("1");}}));
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(null);
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit11() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        PowerMockito.when(wipInfoService.getListByBatchSn(any()))
                .thenReturn(Lists.newArrayList(
                        new PsWipInfo(){{setAttribute1("1");setSn("1");setItemNo("1");setWorkStation("1");setLastProcess("Y");setCurrProcessCode("1");}}
                ));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any()))
                .thenReturn(Lists.newArrayList(new PsTask(){{
                    setOrgId(new BigDecimal(1));
                    setSourceSys("WMES");
                }}));
        PowerMockito.when(detailRepository.getSubmitWip(any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasic(){{setSourceTask("1");setRouteId("1");}}));
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(any()))
                .thenReturn(Lists.newArrayList(new CtRouteHead(){{setRouteId("1");setRouteCodeDetail("1");}}));
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(
                Lists.newArrayList(new BSProcess(){{setProcessName("1");setProcessCode("1");}})
        );
        try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit(){{setMaxTaskCnt(2);setMaxPlanCnt(2);setMaxWipCnt(2);}});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
			dto.setLocatorId(new BigDecimal(2093));
            dto.setStockType("1");PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setDescriptionChinV("1");}}));
            service.submit(dto, "", "");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
		try {
            PowerMockito.field(MixWarehouseSubmitServiceImpl.class, "limit").set(service, new MixWarehouseSubmitLimit() {{
                setMaxTaskCnt(2);
                setMaxPlanCnt(2);
                setMaxWipCnt(2);
            }});
            dto.setWipSns(Lists.newArrayList("1"));
            dto.setOrgId("1");
            dto.setStockType("1");
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                    .thenReturn(Lists.newArrayList(new SysLookupTypesDTO() {{
                        setLookupMeaning("1");
                        setDescriptionChinV("1");
                    }}));
            SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
            sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_N);
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_VALUE_7580026,
                    Constant.LOOKUP_TYPE_7580026001)).thenReturn(sysLookupTypesDTO);
            service.submit(dto, "", "");
        } catch (Exception e) {
			Assert.assertEquals(MessageId.LOCATOR_ID_IS_NULL, e.getMessage());
		}
    }

    @Test
    public void scanLimit() {
        try {
            service.scanLimit();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATOR_ID_IS_NULL, e.getMessage());
        }
    }

    @Test
    public void checkItemBindFinished() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CrafttechRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try {
            service.checkItemBindFinished(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<SysLookupValuesDTO> sysWhiteList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("test123");
        sysWhiteList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysWhiteList);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setTaskNo("test123");
        psWipInfo.setItemNo("test123");
        psWipInfo.setSn("test123");
        wipInfoList.add(psWipInfo);
        PowerMockito.when(prodBindingSettingRepository.getList(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting =new ProdBindingSetting();
        prodBindingSetting.setProcessCode("test123");
        prodBindingSetting.setProductCode("test123");
        prodBindingList.add(prodBindingSetting);
        List<BSProcess> process = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("test123");
        bsProcess.setProcessName("test123");
        process.add(bsProcess);
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("test123");
        wipExtendIdentification.setItemNo("test123");
        wipExtendList.add(wipExtendIdentification);
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.any())).thenReturn(process);
        PowerMockito.when(prodBindingSettingRepository.getList(Mockito.any())).thenReturn(prodBindingList);
        try {
            PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any())).thenReturn(wipExtendList);
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any())).thenReturn(wipExtendList);
            prodBindingSetting.setUsageCount(new BigDecimal("1"));
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        prodBindingSetting.setUsageCount(new BigDecimal("10"));
        try {
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any())).thenReturn(new ArrayList<>());
            service.checkItemBindFinished(wipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
            service.checkItemBindFinished(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        try {
            sysLookupTypesDTO.setLookupMeaning("N");
            service.checkItemBindFinished(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void unFinishedSnDetail() {
        List<String> whiteTasks = new ArrayList<>();
        whiteTasks.add("test123");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("test123");
        psWipInfo.setItemNo("test123");
        psWipInfo.setItemNo("test123");
        psWipInfo.setSn("test123");
        wipInfoList.add(psWipInfo);
        try {
            service.unFinishedSnDetail(wipInfoList,new ArrayList<>(),new HashMap<>(),new HashMap<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting =new ProdBindingSetting();
        prodBindingSetting.setProcessCode("test123");
        prodBindingSetting.setProductCode("test123");
        prodBindingSetting.setItemCode("test123");
        prodBindingSetting.setUsageCount(new BigDecimal("1"));
        prodBindingList.add(prodBindingSetting);
        Map<String, List<ProdBindingSetting>> prodBindMap = new HashMap<>();
        prodBindMap.put("test123",prodBindingList);
        try {
            service.unFinishedSnDetail(wipInfoList,new ArrayList<>(),prodBindMap,new HashMap<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("test123");
        wipExtendIdentification.setItemNo("test123");
        wipExtendIdentification.setFormQty(new BigDecimal("1"));
        wipExtendList.add(wipExtendIdentification);
        Map<String, List<WipExtendIdentification>> wipMap = new HashMap<>();
        wipMap.put("test123",wipExtendList);
        try {
            service.unFinishedSnDetail(wipInfoList,new ArrayList<>(),prodBindMap,wipMap);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            wipExtendIdentification.setFormQty(new BigDecimal("1"));
            service.unFinishedSnDetail(wipInfoList,new ArrayList<>(),new HashMap<>(),new HashMap<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            service.unFinishedSnDetail(wipInfoList,whiteTasks,new HashMap<>(),new HashMap<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void snUnBindDetail() {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("test123");
        psWipInfo.setItemNo("test123");
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting =new ProdBindingSetting();
        prodBindingSetting.setProcessCode("test123");
        prodBindingSetting.setProductCode("test123");
        prodBindingSetting.setItemCode("test123");
        prodBindingSetting.setUsageCount(new BigDecimal("1"));
        prodBindingList.add(prodBindingSetting);
        Map<String, List<ProdBindingSetting>> prodBindMap = new HashMap<>();
        prodBindMap.put("test123",prodBindingList);

        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("test123");
        wipExtendIdentification.setItemNo("test234");
        wipExtendIdentification.setFormQty(new BigDecimal("1"));
        wipExtendList.add(wipExtendIdentification);
        Map<String, List<WipExtendIdentification>> wipMap = new HashMap<>();
        wipMap.put("test123", wipExtendList);
        try {
            service.snUnBindDetail(new HashMap<>(), psWipInfo, prodBindingList, wipExtendList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    /* Started by AICoder, pid:a5b12x44e3ka48114e3b092491df563f4a72bdb1 */
    @Test
    public void checkStockLocAndWeight () throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        List<PsTask> tasks = new ArrayList<>();
        MixWarehouseSubmitDTO dto = new MixWarehouseSubmitDTO();
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        PsTask task = new PsTask();
        tasks.add(task);
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);

        ReflectionTestUtils.setField(service, "checkStockLocWeight", true);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> lookupValuesList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(Constant.LOOKUP_TYPE_7500)).thenReturn(lookupValuesList);
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("test");
        lookupValuesList.add(sysLookupValuesDTO);
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);

        PsTask psTask = new PsTask();
        psTask.setTaskNo("1");
        tasks.add(psTask);
        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("2");
        psTask2.setTaskType("123");
        psTask2.setCustomerName("customName");
        tasks.add(psTask2);
        PsTask psTask3 = new PsTask();
        psTask3.setTaskNo("3");
        psTask3.setTaskType("1111");
        psTask3.setCustomerName("test");
        tasks.add(psTask3);
        PsTask psTask4 = new PsTask();
        psTask4.setTaskNo("4");
        psTask4.setCustomerName("test");
        tasks.add(psTask4);
        PsTask psTask5 = new PsTask();
        psTask5.setTaskNo("5");
        psTask5.setTaskType("buffer");
        psTask5.setCustomerName("test");
        tasks.add(psTask5);
        PsTask psTask6 = new PsTask();
        psTask6.setTaskNo("6");
        psTask6.setTaskType("正式");
        psTask6.setCustomerName("test");
        tasks.add(psTask6);
        List<String> checkPassWeightSnList = new ArrayList<>();
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        List<PsTaskExtendedDTO> extendedList = new ArrayList<>();
        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setTaskNo("123");
        extendedList.add(extendedDTO);
        PowerMockito.when(centerfactoryRemoteService.getSpecificTaskExtended(Mockito.anyList())).thenReturn(extendedList);
        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        extendedDTO.setTaskNo("1");
        PsTaskExtendedDTO extendedDTO1 = new PsTaskExtendedDTO();
        extendedDTO1.setTaskNo("2");
        extendedList.add(extendedDTO1);
        PsTaskExtendedDTO extendedDTO3 = new PsTaskExtendedDTO();
        extendedDTO3.setTaskNo("5");
        extendedDTO3.setEntityClass("STD_UNIT_2");
        extendedList.add(extendedDTO3);
        PsTaskExtendedDTO extendedDTO4 = new PsTaskExtendedDTO();
        extendedDTO4.setTaskNo("6");
        extendedDTO4.setEntityClass("STD_UNIT_2");
        extendedDTO4.setConfirmationStatus("0");
        extendedList.add(extendedDTO4);

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setSn("1");
        wipInfoList.add(wipInfo);
        PsWipInfo wipInfo2 = new PsWipInfo();
        wipInfo2.setSn("222");
        wipInfo2.setAttribute2("2");
        wipInfoList.add(wipInfo2);
        PsWipInfo wipInfo3 = new PsWipInfo();
        wipInfo3.setSn("333");
        wipInfo3.setAttribute2("3");
        wipInfoList.add(wipInfo3);
        try {
            Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupTypesDTO> lookupValues = new ArrayList<>();
        SysLookupTypesDTO sysLookupValue = new SysLookupTypesDTO();
        sysLookupValue.setLookupMeaning("buffer工单");
        sysLookupValue.setAttribute1("STD_UNIT_2,FG_DISAS_2");
        sysLookupValue.setAttribute3("123123");
        lookupValues.add(sysLookupValue);
        SysLookupTypesDTO sysLookupValue1 = new SysLookupTypesDTO();
        sysLookupValue1.setLookupMeaning("123");
        sysLookupValue1.setAttribute1("STD_UNIT_2,FG_DISAS_2");
        sysLookupValue1.setAttribute3("321");
        lookupValues.add(sysLookupValue1);
        SysLookupTypesDTO sysLookupValue2 = new SysLookupTypesDTO();
        sysLookupValue2.setLookupMeaning("buffer");
        sysLookupValue2.setAttribute1("STD_UNIT_2,FG_DISAS_2");
        sysLookupValue2.setAttribute3("132");
        lookupValues.add(sysLookupValue2);
        SysLookupTypesDTO sysLookupValue3 = new SysLookupTypesDTO();
        sysLookupValue3.setLookupMeaning("正式");
        sysLookupValue3.setAttribute1("STD_UNIT_2");
        sysLookupValue3.setAttribute2("2,3,4");
        sysLookupValue3.setAttribute3("312");
        lookupValues.add(sysLookupValue3);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_7501)).thenReturn(lookupValues);
        try {
            Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupTypesDTO> warehouseValues = new ArrayList<>();
        SysLookupTypesDTO taskTypeValue = new SysLookupTypesDTO();
        taskTypeValue.setLookupMeaning("132");
        taskTypeValue.setLookupCode(new BigDecimal("132"));
        taskTypeValue.setAttribute1("132");
        taskTypeValue.setAttribute2("132");
        warehouseValues.add(taskTypeValue);
        SysLookupTypesDTO taskTypeValue1 = new SysLookupTypesDTO();
        taskTypeValue1.setLookupMeaning("312");
        taskTypeValue1.setLookupCode(new BigDecimal("312"));
        taskTypeValue1.setAttribute1("312");
        taskTypeValue1.setAttribute2("312");
        warehouseValues.add(taskTypeValue1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_7502)).thenReturn(warehouseValues);
        dto.setStockName("312");
        try {
            Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STOCK_AND_LOC_NOT_SAME_WITH_TASK, e.getExMsgId());
        }
        extendedDTO4.setConfirmationStatus("2");
        try {
            Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STOCK_AND_LOC_NOT_SAME_WITH_TASK, e.getExMsgId());
        }
        extendedList.remove(extendedDTO1);
        extendedList.remove(extendedDTO3);

        Whitebox.invokeMethod(service, "checkStockLocAndWeight", wipInfoList, tasks, dto);
    }
    /* Ended by AICoder, pid:a5b12x44e3ka48114e3b092491df563f4a72bdb1 */

    @Test
    public void testCheckAuxBindFinished() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class, DatawbRemoteService.class);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        psWipInfoList.add(psWipInfo);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_N);
        try {
            service.checkAuxBindFinished(psWipInfoList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TRACKING_OR_TASK_NO_NOT_EXIST);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_VALUE_7580026,
                Constant.LOOKUP_TYPE_7580026001)).thenReturn(sysLookupTypesDTO);
        service.checkAuxBindFinished(psWipInfoList);
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoList(any())).thenReturn(psTaskList);
        service.checkAuxBindFinished(psWipInfoList);
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting = new ProdBindingSetting();
        prodBindingList.add(prodBindingSetting);
        psTask.setOriginalTask("123A");
        PowerMockito.when(prodBindingSettingRepository.getList(any())).thenReturn(prodBindingList);
        service.checkAuxBindFinished(psWipInfoList);
        psTask.setOriginalTask(null);
        psTask.setTaskNo("123A");
        service.checkAuxBindFinished(psWipInfoList);

        psWipInfo.setAttribute2("123A");
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendList.add(wipExtendIdentification);
        List<WarehouseEntryInfoDTO> commitedQtyList = new ArrayList<>();
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        commitedQtyList.add(warehouseEntryInfoDTO);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(wipExtendList);
        PowerMockito.when(warehouseEntryInfoRepository.getCommitQtyByTaskNo(any(), any())).thenReturn(commitedQtyList);
        service.checkAuxBindFinished(psWipInfoList);
        List<ItemListEntityDTO> erpList = new ArrayList<>();
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        erpList.add(itemListEntityDTO);
        itemListEntityDTO.setTaskNo("123A");
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(any())).thenReturn(erpList);
        service.checkAuxBindFinished(psWipInfoList);
        psTask.setTaskQty(new BigDecimal(10));
        service.checkAuxBindFinished(psWipInfoList);
        itemListEntityDTO.setRequiredQuantity("20");
        service.checkAuxBindFinished(psWipInfoList);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("123");
//        Map<String, PsTask> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getTaskNo, i -> i, (k1, k2) -> k1));
//        PowerMockito.when(warehouseEntryInfoService.calculateAuxBindingCount(anyList(), any(), anyList(), any(), psTaskMap)).thenReturn(stringBuilder);
        try {
            service.checkAuxBindFinished(psWipInfoList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.MIX_STOCK_AUX_MATERIAL_UNBINDING);
        }
    }
}