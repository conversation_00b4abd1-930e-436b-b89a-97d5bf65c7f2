package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.domain.model.WipEntityInfo;
import com.zte.domain.model.WipEntityInfoRepository;
import com.zte.interfaces.dto.EntityMgmtRequsetDTO;
import com.zte.interfaces.dto.EntityMgmtRespDTO;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/12 16:57
 */
@PrepareForTest({EasyExcelFactory.class, ExcelCommonUtils.class})
public class EntityMgmtServiceImplTest extends PowerBaseTestCase {
    @Mock
    WipEntityInfoRepository wipEntityInfoRepository;
    @InjectMocks
    EntityMgmtServiceImpl entityMgmtServiceImpl;

    @Mock
    private CommonsMultipartFile file;
    @Mock
    ExcelReaderBuilder excelReaderBuilder;
    @Mock
    private HttpServletResponse response;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(ExcelCommonUtils.class);
    }

    /*Started by AICoder, pid:8cb16120d825416791a5f8f601036b35*/
    @Test
    public void testResolveExcel_EmptyInputStream() throws Exception {
        InputStream emptyInputStream = new ByteArrayInputStream(new byte[0]);
        when(file.getInputStream()).thenReturn(emptyInputStream);
        when(EasyExcelFactory.read(any(InputStream.class), any(), any()))
                .thenReturn(excelReaderBuilder);
        List<EntityMgmtRespDTO> respDTOS = entityMgmtServiceImpl.resolveExcel(file);
        Assert.assertTrue(CollectionUtils.isEmpty(respDTOS));
    }
    /*Ended by AICoder, pid:8cb16120d825416791a5f8f601036b35*/

    /* Started by AICoder, pid:2b40e77ea3944010b34e25ab1cfd37e3 */
    @Test(timeout = 8000)
    public void testExportExcel() throws IOException {
        entityMgmtServiceImpl.exportExcel(response);
        Assert.assertTrue(Objects.nonNull(response));
    }
    /* Ended by AICoder, pid:2b40e77ea3944010b34e25ab1cfd37e3 */

    /*Started by AICoder, pid:b198aa427444411bb650348d62a8e3d1*/
    @Test
    public void testUpdate_recordFound() {
        EntityMgmtRequsetDTO dto = new EntityMgmtRequsetDTO();
        dto.setEntityId("test");
        dto.setEntityType("type1");
        dto.setModelType("model1");
        dto.setStatus("status1");
        dto.setBoardNum("1");
        dto.setPeriod("2");
        dto.setUserId("user1");

        WipEntityInfo old = new WipEntityInfo();
        old.setEntityIdentification("test");
        old.setEntityType("type2");
        old.setEntityModel("model2");
        old.setEntityStatus("status2");
        old.setBoardNum((short) 3);
        old.setServiceCycle((short) 4);
        old.setLastUpdatedBy("user2");

        Mockito.when(wipEntityInfoRepository.qryWipEntityInfoByIdention("test"))
                .thenReturn(old);
        Mockito.when(wipEntityInfoRepository.updateWipEntityInfoByIdSelective(old))
                .thenReturn(1);
        int result = entityMgmtServiceImpl.update(dto);
        assertEquals(1, result);
    }

    @Test
    public void testUpdate_recordNotFound() {
        EntityMgmtRequsetDTO dto = new EntityMgmtRequsetDTO();
        dto.setEntityId("test");
        Mockito.when(wipEntityInfoRepository.qryWipEntityInfoByIdention("test"))
                .thenReturn(null);
        int result = entityMgmtServiceImpl.update(dto);
        assertEquals(0, result);
    }
    /*Ended by AICoder, pid:b198aa427444411bb650348d62a8e3d1*/

    /*Started by AICoder, pid:04c2e9078c224320b8da183ca2257917*/
    @Test(timeout = 8000)
    public void testGetEntityInfo_DefaultValues() {
        EntityMgmtRequsetDTO dto = new EntityMgmtRequsetDTO();

        when(wipEntityInfoRepository.getCount(any())).thenReturn(50L);
        when(wipEntityInfoRepository.getPage(any())).thenReturn(Collections.singletonList(new WipEntityInfo()));

        assertEquals(50L, entityMgmtServiceImpl.getEntityInfo(dto).getTotal());
        dto.setPage(-1);
        dto.setRows(-1);
        assertEquals(50L, entityMgmtServiceImpl.getEntityInfo(dto).getTotal());
    }
    /*Ended by AICoder, pid:04c2e9078c224320b8da183ca2257917*/

}
