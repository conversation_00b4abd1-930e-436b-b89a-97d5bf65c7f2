/*Started by AICoder, pid:e709au5116870bb1409a096c11349301a2b9e9fc*/
package com.zte.application.impl;

import com.zte.application.PmRepairRcvService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.CollectionCodeScanRepository;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.CollectionCodeScanDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class CollectionCodeScanServiceImplTest {

    @InjectMocks
    private CollectionCodeScanServiceImpl service;
    @Spy
    private CollectionCodeScanServiceImpl serviceSpy = new CollectionCodeScanServiceImpl();

    @Mock
    private CollectionCodeScanRepository collectionCodeScanrepository;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private HrmUserInfoService hrmUserInfoService;
    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testPageList() throws Exception {
        // Given
        CollectionCodeScanDTO record = new CollectionCodeScanDTO();
        record.setPage(1);
        record.setRows(10);

        Method setUserName = service.getClass().getDeclaredMethod("setUserName", List.class);
        setUserName.setAccessible(true);
        setUserName.invoke(service,  Collections.emptyList());

        Page<CollectionCodeScanDTO> result = service.pageList(record);

        assertEquals(0, result.getRows().size());
    }

    /* Started by AICoder, pid:s02b49fcceife201493e08b900c73a232d74eb29 */
    @Test
    public void testSave() throws Exception {
        // Mocking the snowflake ID generator
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");

        // Call the method to be tested
        CollectionCodeScanDTO result = service.save(new CollectionCodeScanDTO(), "testEmpNo");

        // Assertions
        assertEquals("1234567890", result.getCodeScanId());
        assertEquals(result.getCreatedDate(), result.getLastUpdatedDate());
    }
    /* Ended by AICoder, pid:s02b49fcceife201493e08b900c73a232d74eb29 */

    @Test
    public void testSave_NormalCase_NoLockNeeded() throws Exception {
        // 1. 准备数据
        CollectionCodeScanDTO record = new CollectionCodeScanDTO();
        record.setParentCategoryCode("OTHER_CODE"); // 非 SN_CODE
        String empNo = "EMP001";

        // 2. Mock ID 生成器
        when(idGenerator.snowFlakeIdStr()).thenReturn("SCAN001");

        // 3. 调用方法
        CollectionCodeScanDTO result = service.save(record, empNo);

        // 4. 验证结果
        Assert.assertNotNull(result);
        assertEquals("SCAN001", result.getCodeScanId());
    }

    @Test
    public void testSave_WithLockAndNoDuplicateSubSn() throws Exception {
        // 1. 准备数据
        CollectionCodeScanDTO record = new CollectionCodeScanDTO();
        record.setParentCategoryCode(MpConstant.SN_CODE);
        record.setSubSn("SUB123");
        String empNo = "EMP002";

        // 2. 创建 Spy 对象并覆盖 createRedisLock 方法
        CollectionCodeScanServiceImpl serviceSpy = spy(service);
        RedisLock mockLock = mock(RedisLock.class);
        when(mockLock.lock()).thenReturn(true);
        doReturn(mockLock).when(serviceSpy).createRedisLock(eq("SUB123")); // 关键：覆盖方法返回 Mock 锁

        // 3. Mock 数据库查询无重复
        when(collectionCodeScanrepository.getList(any())).thenReturn(Collections.emptyList());

        // 4. 调用方法
        CollectionCodeScanDTO result = serviceSpy.save(record, empNo);

        // 5. 验证
        Assert.assertNotNull(result);
        verify(mockLock).lock();
        verify(mockLock).unlock(); // 确保锁释放
    }

    @Test
    public void testSave_DuplicateSubSn_ThrowsException() {
        PowerMockito.mockStatic(CommonUtils.class);
        // 1. 准备数据
        CollectionCodeScanDTO record = new CollectionCodeScanDTO();
        record.setParentCategoryCode(MpConstant.SN_CODE);
        record.setSubSn("SUB456");
        String empNo = "EMP003";

        // 2. 创建 Spy 对象并覆盖锁方法
        CollectionCodeScanServiceImpl serviceSpy = spy(service);
        RedisLock mockLock = mock(RedisLock.class);
        when(mockLock.lock()).thenReturn(true);
        doReturn(mockLock).when(serviceSpy).createRedisLock(eq("SUB456"));

        // 3. Mock 数据库返回重复数据
        CollectionCodeScanDTO existingRecord = new CollectionCodeScanDTO();
        existingRecord.setMasterSn("MASTER123");
        when(collectionCodeScanrepository.getList(any())).thenReturn(Collections.singletonList(existingRecord));
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("test");

        // 4. 验证异常
        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> serviceSpy.save(record, empNo));

        // 5. 检查异常消息
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testSave_DuplicateSubSnFalse() {
        // 1. 准备数据
        CollectionCodeScanDTO record = new CollectionCodeScanDTO();
        record.setParentCategoryCode(MpConstant.SN_CODE);
        record.setSubSn("SUB456");
        String empNo = "EMP003";

        // 2. 创建 Spy 对象并覆盖锁方法
        CollectionCodeScanServiceImpl serviceSpy = spy(service);
        RedisLock mockLock = mock(RedisLock.class);
        when(mockLock.lock()).thenReturn(false);
        doReturn(mockLock).when(serviceSpy).createRedisLock(eq("SUB456"));

        // 3. Mock 数据库返回重复数据
        CollectionCodeScanDTO existingRecord = new CollectionCodeScanDTO();
        existingRecord.setMasterSn("MASTER123");

        // 4. 验证异常
        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> serviceSpy.save(record, empNo));

        // 5. 检查异常消息
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    /* Started by AICoder, pid:y2fafk99205613e146cc08c9805b8f168a5793bf */
    @Test
    public void testUpdateSuccess() throws Exception {
        // Mock the repository to return a specific value
        when(collectionCodeScanrepository.batchUpdate(anyList())).thenReturn(1);

        // Call the method to be tested
        int result = service.update(new CollectionCodeScanDTO(), "testEmpNo");

        // Assertions
        assertEquals(1, result);
    }
    /* Ended by AICoder, pid:y2fafk99205613e146cc08c9805b8f168a5793bf */

    /* Started by AICoder, pid:b4d0fz9a22icc6314c330882a033554e1d98047d */
    @Test
    public void testSetUserName() throws Exception {
        CollectionCodeScanDTO dto1 = new CollectionCodeScanDTO();
        dto1.setCreatedBy("user1");
        CollectionCodeScanDTO dto2 = new CollectionCodeScanDTO();
        dto2.setCreatedBy("user2");
        List<CollectionCodeScanDTO> list = Arrays.asList(dto1, dto2);
        // Mock the getUserNew method to return a predefined map
        Map<String, String> bsPubHrMap = new HashMap<>();
        bsPubHrMap.put("user1", "User One");
        bsPubHrMap.put("user2", "User Two");

        List<BsPubHrvOrgId> hrvOrgIdList = new ArrayList<>();
        BsPubHrvOrgId bsPubHrvOrgId = new BsPubHrvOrgId();
        bsPubHrvOrgId.setUserId("user1");
        bsPubHrvOrgId.setUserName("User One");
        hrvOrgIdList.add(bsPubHrvOrgId);
        when(pmRepairRcvService.getBsPubHrvInfo(any(StringBuilder.class))).thenReturn(hrvOrgIdList);

        Method getUserNew = service.getClass().getDeclaredMethod("getUserNew", List.class);
        getUserNew.setAccessible(true);
        getUserNew.invoke(service,  list);
        Method setUserName = service.getClass().getDeclaredMethod("setUserName", List.class);
        setUserName.setAccessible(true);
        setUserName.invoke(service,  list);

        // Assertions
        assertEquals("User Oneuser1", list.get(0).getCreatedByName());
    }

    @Test
    public void testSetUserNameWithEmptyList() throws Exception {
        // Test with an empty list
        List<CollectionCodeScanDTO> emptyList = Collections.emptyList();

        // Call the method to be tested
        Method setUserName = service.getClass().getDeclaredMethod("setUserName", List.class);
        setUserName.setAccessible(true);
        setUserName.invoke(service,  emptyList);

        verify(pmRepairRcvService, times(0)).getBsPubHrvInfo(any(StringBuilder.class));
    }

    @Test
    public void testSetUserNameWithNullCreatedBy() throws Exception {
        // Initialize a list with a null createdBy field
        CollectionCodeScanDTO dto = new CollectionCodeScanDTO();
        dto.setCreatedBy(null);
        List<CollectionCodeScanDTO> list = Collections.singletonList(dto);

        // Mock the getUserNew method to return a predefined map
        Map<String, String> bsPubHrMap = new HashMap<>();
        bsPubHrMap.put("user1", "User One");

        // Call the method to be tested
        Method setUserName = service.getClass().getDeclaredMethod("setUserName", List.class);
        setUserName.setAccessible(true);
        setUserName.invoke(service,  list);

        // Assertion
        Assert.assertNull(list.get(0).getCreatedByName());
    }
    /* Ended by AICoder, pid:b4d0fz9a22icc6314c330882a033554e1d98047d */

}
/*Ended by AICoder, pid:e709au5116870bb1409a096c11349301a2b9e9fc*/