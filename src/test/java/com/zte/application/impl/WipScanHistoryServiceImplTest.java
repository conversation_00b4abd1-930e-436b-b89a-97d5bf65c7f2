package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.LineCurrProcessCodeParamDTO;
import com.zte.interfaces.dto.ProcessProducesParamsDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.interfaces.dto.WipScanParamsDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class, BasicsettingRemoteService.class})
public class WipScanHistoryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WipScanHistoryServiceImpl service;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;

    @Test
    public void getWipScanHistoryListPage() throws Exception {
        WipScanHistoryDTO dto = new WipScanHistoryDTO();
        dto.setAttribute1("4444");
        List<WipScanHistoryDTO> list = new ArrayList<>();
        PowerMockito.when(wipScanHistoryRepository.getWipScanHistoryListPage(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getWipScanHistoryListPage(dto));
    }


    @Test
    public void countQtyForScrapOrMaintence() {
        try{
            service.countQtyForScrapOrMaintence(new Date(),new ArrayList<>(), true);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void getFirstDateOfProdplanId() {
        List<String> list = new ArrayList<>();
        Assert.assertTrue(CollectionUtils.isEmpty(service.getFirstDateOfProdplanId(list)));
        list.add("!23");
        Assert.assertTrue(CollectionUtils.isEmpty(service.getFirstDateOfProdplanId(list)));
    }

    @Test
    public void getLineProduceInfo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        WipScanParamsDTO dto = new WipScanParamsDTO();
        List<LineCurrProcessCodeParamDTO> dtoList = new ArrayList<>();
        LineCurrProcessCodeParamDTO paramDTO = new LineCurrProcessCodeParamDTO();
        paramDTO.setLineCode("123");
        dtoList.add(paramDTO);
        dto.setLineProcessList(dtoList);
        Assert.assertNotNull(service.getLineProduceInfo(dto));
        ProcessProducesParamsDTO producesParamsDTO = new ProcessProducesParamsDTO();
        producesParamsDTO.setLineProcessList(dtoList);
        try {
            service.getProductQtyByCraftAndLineCode(producesParamsDTO);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        WipScanParamsDTO scanParamsDTO = new WipScanParamsDTO();
        scanParamsDTO.setLineProcessList(dtoList);
        Assert.assertNotNull(service.getCurrentTaskOutPut(scanParamsDTO));

        paramDTO.setCurrProcessCode("'321123");
        Assert.assertNotNull(service.getLineProduceInfo(dto));
        try {
            service.getProductQtyByCraftAndLineCode(producesParamsDTO);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        paramDTO.setCurrProcessCode("321123");
        Assert.assertNotNull(service.getCurrentTaskOutPut(scanParamsDTO));

    }


}