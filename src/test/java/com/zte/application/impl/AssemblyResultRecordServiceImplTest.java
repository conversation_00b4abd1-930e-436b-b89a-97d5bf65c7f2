package com.zte.application.impl;

import com.zte.domain.model.AssemblyResultRecordRepository;
import com.zte.interfaces.dto.AssemblyResultRecordEntityDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class AssemblyResultRecordServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    AssemblyResultRecordServiceImpl service;

    @Mock
    private AssemblyResultRecordRepository assemblyResultRecordrepository;

    @Test
    public void batchInsert1() throws Exception {
        List<AssemblyResultRecordEntityDTO> list = new ArrayList<>();
        int i = service.batchInsert(list);
        Assert.assertTrue(i == 0);
    }

    @Test
    public void batchInsert2() throws Exception {
        List<AssemblyResultRecordEntityDTO> list = new ArrayList<>();
        AssemblyResultRecordEntityDTO p = new AssemblyResultRecordEntityDTO();
        list.add(p);
        PowerMockito.when(assemblyResultRecordrepository.batchInsert(list)).thenReturn(list.size());
        int i = service.batchInsert(list);
        Assert.assertTrue(i == 1);
    }
}


