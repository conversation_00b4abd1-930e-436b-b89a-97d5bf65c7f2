package com.zte.application.impl;

import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.redis.SnRedisLock;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({ProductionDeliveryRemoteService.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class})
public class PackageScanServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PackageScanServiceImpl packageScanService;
    @Mock
    private PsWipInfoServiceImpl psWipInfoService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private SnRedisLock snRedisLock;

    @Before
    public void init() {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class);
    }
    @Test
    public void verifyTheBarcodeForTheCurrentBatch() throws Exception {
        PackageScanDTO packageScanDTO = new PackageScanDTO();
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        packageScanDTO.setScanType(NumConstant.STR_ONE);
        ReflectUtil.setFieldValue(packageScanService,"packageScanSwitch","N");
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        ReflectUtil.setFieldValue(packageScanService,"packageScanSwitch","Y");
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        packageScanDTO.setStartSn("777888900111");
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        packageScanDTO.setSn("2");
        packageScanDTO.setStartSn("");
        try {
            packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_BARCODE_IS_NOT_FROM_THE_CURRENT_BATCH, e.getMessage());
        }
        Assert.assertNotNull(packageScanDTO);
        packageScanDTO.setStartSn("777888900111");
        try {
            packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_BARCODE_IS_NOT_FROM_THE_CURRENT_BATCH, e.getMessage());
        }
        packageScanDTO.setSn("");
        packageScanDTO.setStartSn("2");
        Assert.assertNotNull(packageScanDTO);

        packageScanDTO.setScanType(NumConstant.STR_ONE);
        packageScanDTO.setSn("777666700031");
        try {
            packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_BARCODE_IS_NOT_FROM_THE_CURRENT_BATCH, e.getMessage());
        }
        packageScanDTO.setProdplanId("7776667");
        packageScanDTO.setScanType(NumConstant.STR_TWO);
        packageScanDTO.setStartSn("777666700031");
        packageScanDTO.setEndSn("777666700033");
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(Arrays.asList(new PsWipInfo()));
        try {
            packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_BARCODE_IS_NOT_FROM_THE_CURRENT_BATCH, e.getMessage());
        }
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(Arrays.asList(new PsWipInfo(){{setAttribute1("7776667");}}));
        packageScanService.verifyTheBarcodeForTheCurrentBatch(packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        Whitebox.invokeMethod(packageScanService,"addIfNotNull",new ArrayList<>(), null);
    }
    @Test
    public void verifyWhetherTheScanningIsBarcode() throws Exception {
        PackageScanDTO packageScanDTO = new PackageScanDTO();
        Whitebox.invokeMethod(packageScanService,"verifyWhetherTheScanningIsBarcode",packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        packageScanDTO.setScanType("1");
        Whitebox.invokeMethod(packageScanService,"verifyWhetherTheScanningIsBarcode",packageScanDTO);
        Assert.assertNotNull(packageScanDTO);
        ContainerInfoDTO record = new ContainerInfoDTO();
        record.setLpn("TEST");
        record.setPage(1);
        record.setRows(100);
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoCountByLpnList(any())).thenReturn(1L);
        try {
            Whitebox.invokeMethod(packageScanService,"verifyWhetherTheScanningIsBarcode",packageScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCANNING_BLOCK_BY_BLOCK_CAN_ONLY_SCAN_BARCODES, e.getMessage());
        }

    }
    @Test
    public void testBlockByBlockPackageScan() throws Exception {
        ReflectUtil.setFieldValue(packageScanService,"packageScanSwitch","N");
        PackageScanDTO param = new PackageScanDTO();
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
        List<PsWorkOrderDTO> workOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderDTOS.add(psWorkOrderDTO);
        psWorkOrderDTO.setRemark("1");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(workOrderDTOS);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORDER_ORDER_NOT_FOUND_ROUTE, e.getMessage());
        }
        psWorkOrderDTO.setRouteId("1-a-c-d");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_LINE_CODE_EMPTY, e.getMessage());
        }
        psWorkOrderDTO.setLineCode("HYC");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_CARFT_NULL, e.getMessage());
        }
        List<CtRouteDetailDTO> ctRouteDetails = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetails.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.getProcessListByRouteId(any())).thenReturn(ctRouteDetails);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_CARFT_NULL, e.getMessage());
        }
        PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(any())).thenReturn(ctRouteDetails);
        param.setScanType("1");
        param.setSn("777123400001");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        wipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getWipInfoByBarCodeSection(any(), any())).thenReturn(wipInfoList);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }

        psWipInfo.setSn("777123400001");
        PowerMockito.when(psWipInfoRepository.getWipInfoByBarCodeSection(any(), any())).thenReturn(wipInfoList);
        packageScanService.blockByBlockPackageScan(param);
        param.setSn(null);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }
        param.setScanType("2");
        param.setEndSn("123");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }
        param.setStartSn("123");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_ITEM_LENGTH_NO_12, e.getMessage());
        }
        param.setEndSn("777123400002");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_ITEM_LENGTH_NO_12, e.getMessage());
        }
        param.setStartSn("777123400001");
        psWipInfo.setSn("777123400001");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WIPINFO_OF_SN_IS_NULL, e.getMessage());
        }
        ctRouteDetailDTO.setCurrProcess("0");
        ctRouteDetailDTO.setLastProcess("Y");
        FlowControlInfoDTO controlResult = new FlowControlInfoDTO();
        controlResult.setResultType("FAIL");
        PowerMockito.when(psWipInfoService.checkFlowControled(any())).thenReturn(controlResult);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, e.getMessage());
        }
        controlResult.setErrorMessage("FAIL");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_BARCODE_FLOW_CONTROL_NOT_PASS, e.getMessage());
        }
        PowerMockito.when(psWipInfoService.checkFlowControled(any())).thenReturn(new FlowControlInfoDTO());

        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        param.setScanType("1");
        param.setSn("777123400001");
        PowerMockito.when(psWipInfoRepository.getWipInfoByBarCodeSection(any(), any())).thenReturn(new ArrayList<>());
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(psWipInfoRepository.getWipInfoByBarCodeSection(any(), any())).thenReturn(wipInfoList);
        param.setScanList(wipInfoList);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(new ArrayList<>());
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FORM_SN_NOT_IN_WIP_INFO, e.getMessage());
        }
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(wipInfoList);
        packageScanService.blockByBlockPackageScan(param);
        List<ContainerContentInfoDTO> containerContentInfoBySn = new ArrayList<>();
        containerContentInfoBySn.add(new ContainerContentInfoDTO());
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(any())).thenReturn(containerContentInfoBySn);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IN_CONTAINER, e.getMessage());
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(any())).thenReturn(new ArrayList<>());

        param.setIsRepair("Y");
        ctRouteDetailDTO.setRemainTime(new BigDecimal(100));
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CAN_NOT_PACKAGE_SCAN, e.getMessage());
        }
        List<WipScanHistory> snDateTempList = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        snDateTempList.add(wipScanHistory);
        wipScanHistory.setSn("777123400002");
        PowerMockito.when(wipScanHistoryRepository.getBatchTimeIntervalBySnList(any(), any())).thenReturn(snDateTempList);
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CAN_NOT_PACKAGE_SCAN, e.getMessage());
        }
        wipScanHistory.setSn("777123400001");
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CAN_NOT_PACKAGE_SCAN, e.getMessage());
        }
        wipScanHistory.setCreateDate(new Date(10000));
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CAN_NOT_PACKAGE_SCAN, e.getMessage());
        }
        ctRouteDetailDTO.setRemainTime(new BigDecimal(3600));
        wipScanHistory.setCreateDate(new Date());
        try {
            packageScanService.blockByBlockPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CAN_NOT_PACKAGE_SCAN, e.getMessage());
        }
    }

    @Test
    public void testSnScanCheck() throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        CtRouteDetailDTO lastProcess = new CtRouteDetailDTO();
        CtRouteDetailDTO lastWorkStation = new CtRouteDetailDTO();
        List<CtRouteDetailDTO> lastLineBody = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("P1024");
        psWipInfo.setWorkStation("S2024");
        wipInfoList.add(psWipInfo);
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        lastLineBody.add(new CtRouteDetailDTO());
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        lastProcess.setCurrProcess("P1024");
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        List<CtRouteDetailDTO> listObj = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        lastProcess.setNextProcess("P1025");
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        ctRouteDetailDTO.setProcessCode("1234");
        lastLineBody.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(), any())).thenReturn(listObj);
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        ctRouteDetailDTO.setProcessCode("P1024");
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        psWipInfo.setLastProcess("Y");
        try {
            Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_SN_PROCESS_WRONG, e.getMessage());
        }
        listObj.add(ctRouteDetailDTO);
        lastProcess.setNextProcess("P1024");
        Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        lastWorkStation.setProcessCode("P1024");
        lastWorkStation.setNextProcess("S2024");
        Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        lastWorkStation.setNextProcess("123");
        lastWorkStation.setCurrProcess("S2024");
        Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        lastWorkStation.setCurrProcess("123");
        lastProcess.setCurrProcess("P1024");
        lastLineBody = new ArrayList<>();
        lastLineBody.add(ctRouteDetailDTO);
        Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
        lastLineBody.add(new CtRouteDetailDTO());
        Whitebox.invokeMethod(packageScanService, "snScanCheck", wipInfoList, lastProcess, lastLineBody, lastWorkStation);
    }
    @Test
    public void testGetNeedFlowWip() throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        CtRouteDetailDTO lastWorkStation = new CtRouteDetailDTO();
        wipInfoList.add(psWipInfo);
        Assert.assertEquals(Whitebox.invokeMethod(packageScanService, "getNeedFlowWip", wipInfoList, lastWorkStation), new ArrayList<>());
        psWipInfo.setWorkStation("1");
        Whitebox.invokeMethod(packageScanService, "getNeedFlowWip", wipInfoList, lastWorkStation);
        lastWorkStation.setNextProcess("1");
        Whitebox.invokeMethod(packageScanService, "getNeedFlowWip", wipInfoList, lastWorkStation);
        psWipInfo.setMappingProcess("1");
        Whitebox.invokeMethod(packageScanService, "getNeedFlowWip", wipInfoList, lastWorkStation);
        psWipInfo.setWorkStation("0");
        Whitebox.invokeMethod(packageScanService, "getNeedFlowWip", wipInfoList, lastWorkStation);
    }

    @Test
    public void testReelidPackageScan() throws Exception {
        PackageScanDTO param = new PackageScanDTO();
        try {
            packageScanService.reelidPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
        List<PsWorkOrderDTO> workOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderDTOS.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(workOrderDTOS);
        psWorkOrderDTO.setCraftSection("外协");
        param.setSn("12345@");
        try {
            packageScanService.reelidPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OUTSOURCE_REELID_FORMAT_ERROR, e.getMessage());
        }
        param.setSn("49841648996488955414984948");
        try {
            packageScanService.reelidPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OUTSOURCE_REELID_FORMAT_ERROR, e.getMessage());
        }
        param.setSn("1234");
        packageScanService.reelidPackageScan(param);
        List<PsWipInfo> scanList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        scanList.add(psWipInfo);
        param.setScanList(scanList);
        packageScanService.reelidPackageScan(param);
        psWorkOrderDTO.setCraftSection("SMT");
        try {
            packageScanService.reelidPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_LPN_WIP_NOT_EXIST, e.getMessage());
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(any())).thenReturn(new ArrayList<ContainerContentInfoDTO>() {{
            add(new ContainerContentInfoDTO() {{
                setProdPlanId("1234");
            }});
        }});
        packageScanService.reelidPackageScan(param);
        param.setProdplanId("1234");
        try {
            packageScanService.reelidPackageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PACKAGE_SCAN_LPN_WIP_NOT_EXIST, e.getMessage());
        }
    }

    @Test
    public void testPackageScan() throws Exception {
        PackageScanDTO param = new PackageScanDTO();
        packageScanService.packageScan(param);
        param.setScanType("1");
        try {
            packageScanService.packageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
        param.setScanType("2");
        try {
            packageScanService.packageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
        param.setScanType("4");
        try {
            packageScanService.packageScan(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
    }
}