package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.PrintSceneConfig;
import com.zte.domain.model.PrintSceneConfigRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PrintSceneConfigDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@PrepareForTest({BasicsettingRemoteService.class})
public class PrintSceneServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    PrintSceneServiceImpl service;
    @Mock
    PrintSceneConfigRepository printSceneConfigRepository;

    @Test
    public void getPrintScenePage() throws Exception {
        PrintSceneConfigDTO dto = new PrintSceneConfigDTO();
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setStartTime("2023-03-6 00:00:00");
        dto.setEndTime("2023-04-6 23:59:59");
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setPrintScene("1");
        PowerMockito.when(printSceneConfigRepository.getPrintSceneCount(any())).thenReturn(1l);
        List<PrintSceneConfig> resultList = new ArrayList<>();
        PrintSceneConfig printSceneConfig = new PrintSceneConfig();
        resultList.add(printSceneConfig);
        PowerMockito.when(printSceneConfigRepository.getPrintScene(any())).thenReturn(resultList);
        Assert.assertNotNull(service.getPrintScenePage(dto));
    }

    @Test
    public void setPrintScene() throws Exception {
        PrintSceneConfigDTO dto = new PrintSceneConfigDTO();
        dto.setPage(1);
        dto.setRows(10);
        List<PrintSceneConfig> resultList = new ArrayList<>();

        PowerMockito.when(printSceneConfigRepository.getPrintSceneCount(any())).thenReturn(1l);
        PowerMockito.when(printSceneConfigRepository.getPrintScene(any())).thenReturn(resultList);
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setVerNo("1111");
        PowerMockito.when(printSceneConfigRepository.getPrintSceneCount(any())).thenReturn(1l);
        PowerMockito.when(printSceneConfigRepository.getPrintScene(any())).thenReturn(resultList);
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        PowerMockito.when(printSceneConfigRepository.insertSelective(any())).thenReturn(1);
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.checkExistVerNo(any())).thenReturn(true);
        service.setPrintScene(dto);

        PrintSceneConfig record = new PrintSceneConfig();
        record.setPrintScene("11");
        resultList.add(record);
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setUpdate(false);
        PowerMockito.when(printSceneConfigRepository.updateByPrimaryKeySelective(any())).thenReturn(1);
        try {
            service.getPrintScenePage(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setUpdate(true);
        PowerMockito.when(printSceneConfigRepository.updateByPrimaryKeySelective(any())).thenReturn(1);
        Assert.assertNotNull(service.setPrintScene(dto));
    }

    @Test
    public void deletePrintScene() throws Exception {
        PowerMockito.when(printSceneConfigRepository.deleteByVerNo(any())).thenReturn(1);
        Assert.assertNotNull(service.deletePrintScene("111"));
    }
}

