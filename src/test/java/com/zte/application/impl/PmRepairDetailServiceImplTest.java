package com.zte.application.impl;

import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, RedisLock.class, ObtainRemoteServiceDataUtil.class, CommonUtils.class, BasicsettingRemoteService.class})
public class PmRepairDetailServiceImplTest {
    @InjectMocks
    private PmRepairDetailServiceImpl service;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private PmRepairDetailRepository pmRepairInfoRepository;


    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    WipExtendIdentificationService wipExtendIdentificationService;

    @Mock
    PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    AvlService avlService;

    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Mock
    WipScanHistoryService wipScanHistoryService;

    @Mock
    PmRepairRcvDetailServiceImpl pmRepairRcvDetailServiceImp;

    @Mock
    SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    IfisService ifisService;

    @Mock
    private RedisLock redisLock;

    @Mock
    ValueOperations<String, String> redisOpsValue;
	@Mock
	private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BasicsettingRemoteService basicsettingRemoteService;
    @Mock
    private PmRepairInfoRepository repairInfoRepository;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private RepairApprovalService repairApprovalService;

    @Mock
    HttpServletRequest request;

    @Before
    public void init() {

        PowerMockito.mockStatic(RedisLock.class);
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);

    }

    @Test
    public void postRepairInfoBatch() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(true);

        List<PmRepairDetailDTO> dtos = getDtos();
        try {
            service.postRepairInfoBatch(dtos);
        } catch (Exception e) {
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void iFisConsume() throws Exception {
        List<PmRepairDetailDTO> dtos = getDtos();
        List<BsPubHrvOrgId> bsPubHrvOrgIds = new ArrayList<>();
        BsPubHrvOrgId bsPubHrvOrgId = new BsPubHrvOrgId();
        bsPubHrvOrgIds.add(bsPubHrvOrgId);
        bsPubHrvOrgId.setUserName("XXX");
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo("10291234")).thenReturn(bsPubHrvOrgIds);
        PowerMockito.when(ifisService.consumeUserProvideMaterial(Mockito.any(), Mockito.any())).thenReturn(true);

        try {
            service.iFisConsume(dtos, "1111");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_IFIS_ERROR, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void prepareNosn() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(true);

        List<PmRepairDetailDTO> dtos = getDtos();
        List<RedisLock> redisLocks = new ArrayList<>();
        List<String> noSnRepairSns = new ArrayList<>();
        noSnRepairSns.add("sn1");
        PowerMockito.when(pmRepairRcvService.getNoSnRepairSns(any())).thenReturn(noSnRepairSns);
        service.prepareNosn(dtos, redisLocks);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    

    private List<PmRepairDetailDTO> getDtos() {
        List<PmRepairDetailDTO> dtos = new ArrayList<>();

        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setSn("725499800013");
        dto.setRcvProdplanId("7254998");
        dto.setAdverseType("adverseType");
        dto.setErrorCode("200");
        dto.setErrorDescription("摔坏111");
        dto.setIsLocationNo("isLocationNo");
        dto.setLocationNo("B11");
        dto.setRepairBy("10291234");
        dto.setReplaceSn("007900210913000082");
        dto.setFactoryId(new BigDecimal(52));
        dtos.add(dto);

        PmRepairDetailDTO dto1 = new PmRepairDetailDTO();
        dto1.setSn("725499800013");
        dto1.setRcvProdplanId("7254998");
        dto1.setAdverseType("adverseType");
        dto1.setErrorCode("100");
        dto1.setErrorDescription("摔坏");
        dto1.setIsLocationNo("isLocationNo");
        dto1.setLocationNo("B12");
        dto1.setRepairBy("10291234");
        dto1.setReplaceSn("007900210913000082");
        dto1.setFactoryId(new BigDecimal(53));
        dtos.add(dto1);
        return dtos;
    }

	@Test
	public void getStyleInfoOfZjByPtp() throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		PowerMockito.when(smtSnMtlTracingTService.judgeFromBarCodeCenter(any(),anyString())).thenReturn(true);
		Map<String, BarcodeExpandDTO> map=new HashMap<>();
		BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
		barcodeExpandDTO.setSupplierName("供应商");
		barcodeExpandDTO.setBrandName("品牌");
		barcodeExpandDTO.setSpecModel("品牌");
		List<BarcodeExpandDTO> expandDTOList = new ArrayList<>();
		expandDTOList.add(barcodeExpandDTO);
		PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(expandDTOList);
		service.getStyleInfoOfZjByPtp(dto, "132456789");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void getStyleInfoOfZjByPtpTwo() throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		PowerMockito.when(smtSnMtlTracingTService.judgeFromBarCodeCenter(any(),anyString())).thenReturn(false);
		service.getStyleInfoOfZjByPtp(dto, "132456789");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void getStyleInfoByPtp()throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		dto.setIsSub("N");
		dto.setFromStation("整机生产维修");
        Assert.assertEquals(0,service.getStyleInfoByPtp(dto, "1"));
	}

	@Test
	public void getStyleInfoByPtpTwo()throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		dto.setIsSub("Y");
		dto.setFromStation("整机生产维修");
        Assert.assertEquals(0,service.getStyleInfoByPtp(dto, "1"));
	}

	@Test
	public void getStyleInfoByPtpThree()throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		dto.setIsSub("N");
		dto.setFromStation("整机维修");
        Assert.assertEquals(0,service.getStyleInfoByPtp(dto, "1"));
	}

	@Test
	public void getStyleInfoByPtpFour()throws Exception {
		PmRepairDetailDTO dto = new PmRepairDetailDTO();
		dto.setIsSub("Y");
		dto.setFromStation("整机维修");
        Assert.assertEquals(0,service.getStyleInfoByPtp(dto, "1"));
	}

    @Test
    public void getSupplyInfo() throws Exception {
        PmRepairDetailDTO dto =new PmRepairDetailDTO();
        dto.setItemSn("730638900030");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("730638900030");
        barcodeExpandDTO.setBrandName("730638900030");
        barcodeExpandDTO.setSupplierName("730638900030");
        barcodeExpandDTO.setSpecModel("730638900030");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        Assert.assertEquals(dto,service.getSupplyInfo(dto));
    }

    @Test
    public void obtainItemCode() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setIsZjSub("N");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("730638900030");
        barcodeExpandDTO.setBrandName("730638900030");
        barcodeExpandDTO.setSupplierName("730638900030");
        barcodeExpandDTO.setSpecModel("730638900030");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        try{
            service.obtainItemCode(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(new ArrayList<>());
        try{
            service.obtainItemCode(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void handlingBarcodeReplacement() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        try{
            service.handlingBarcodeReplacement(dto,1);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setReplaceSn("test123");
        dto.setMainItemNo(null);
        dto.setIsZjSub("N");
        try{
            service.handlingBarcodeReplacement(dto,1);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setMainItemNo("12123143252w354");
        dto.setSn("test123");
        dto.setItemSn("test123");
        dto.setRepairSn("test123");
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(new ArrayList<>());
        try{
            service.handlingBarcodeReplacement(dto,1);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(wipExtendList);
        PowerMockito.when(wipExtendIdentificationRepository.updateSnByRepairSnBatch(any())).thenReturn(1);
        try{
            service.handlingBarcodeReplacement(dto,1);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setIsZjSub("Y");
        try{
            service.handlingBarcodeReplacement(dto,1);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getItemSnAndSupInfo() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setSubSn("test123");
        dto.setFactoryId(new BigDecimal("55"));
        WipScanHistory timeDto= new WipScanHistory();
        timeDto.setTimeLimitLeftStr("2024-02-25 00:00:00");
        timeDto.setTimeLimitRightStr("2024-02-25 12:00:00");
        PowerMockito.when(wipScanHistoryService.getTimeRange(any())).thenReturn(timeDto);
        PowerMockito.when(smtSnMtlTracingTService.getTraceDataBySubSn(any())).thenReturn(new ArrayList<>());
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("test123");
        wipExtendList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("errpr");
        try{
            service.getItemSnAndSupInfo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(wipExtendList);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(new ArrayList<>());
        try{
            service.getItemSnAndSupInfo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<BarcodeExpandDTO> expandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        expandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(expandDTOList);
        try{
            service.getItemSnAndSupInfo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<SmtSnMtlTracingT> itemSnList= new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        itemSnList.add(smtSnMtlTracingT);
        PowerMockito.when(smtSnMtlTracingTService.getTraceDataBySubSn(any())).thenReturn(itemSnList);
        try{
            service.getItemSnAndSupInfo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(wipScanHistoryService.getTimeRange(any())).thenReturn(null);
        try{
            service.getItemSnAndSupInfo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void verifyItemCodeStep() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setItemSnType("2");
        dto.setItemCode("test123");
        dto.setMainItemNo("123456789012AAA");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(any())).thenReturn(null);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        barcodeExpandDTO.setItemCode("test123");
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setItemCode("test");
        StItemMessage stItemMessage = new StItemMessage();
        stItemMessage.setItemNo("test");
        PowerMockito.when(datawbRemoteService.queryMaterialMessage(any())).thenReturn(stItemMessage);
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        stItemMessage.setItemNo("test123");
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setMainItemNo("123456789012");
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(datawbRemoteService.queryItemNoAndName(any())).thenReturn(stItemMessage);
        try{
            service.verifyItemCodeStep("test123",dto,true);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void updateRepairHistoryType() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("52");
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REQUEST_XIAN_FACTORY, e.getMessage());
        }
        PowerMockito.when(request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("56");
        PowerMockito.when(BasicsettingRemoteService.getErrorCodeInfoList()).thenReturn(null);
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_RESULT_IS_NULL, e.getMessage());
        }
        List<BsErrorCodeInfoDTO> errorCodeInfoList = new ArrayList<>();
        BsErrorCodeInfoDTO bsErrorCodeInfoDTO = new BsErrorCodeInfoDTO();
        bsErrorCodeInfoDTO.setErrorCode("35");
        errorCodeInfoList.add(bsErrorCodeInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getErrorCodeInfoList()).thenReturn(errorCodeInfoList);
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_RESULT_IS_NULL, e.getMessage());
        }
        bsErrorCodeInfoDTO.setType(new BigDecimal(1));
        bsErrorCodeInfoDTO.setErrorCode("35");
        errorCodeInfoList.add(bsErrorCodeInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getErrorCodeInfoList()).thenReturn(errorCodeInfoList);
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_RESULT_IS_NULL, e.getMessage());
        }
        bsErrorCodeInfoDTO.setErrorCode("35");
        bsErrorCodeInfoDTO.setType(new BigDecimal(0));
        BsErrorCodeInfoDTO bsErrorCodeInfoDTO2 = new BsErrorCodeInfoDTO();
        bsErrorCodeInfoDTO2.setErrorCode("36");
        bsErrorCodeInfoDTO2.setType(null);
        errorCodeInfoList.add(bsErrorCodeInfoDTO);
        errorCodeInfoList.add(bsErrorCodeInfoDTO2);
        PowerMockito.when(BasicsettingRemoteService.getErrorCodeInfoList()).thenReturn(errorCodeInfoList);
        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateListCount(Mockito.any())).thenReturn(0);
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_DATA_TO_BE_UPDATED, e.getMessage());
        }

        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateListCount(Mockito.any())).thenReturn(1001);
        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateList(Mockito.any())).thenReturn(null);
        try {
            service.updateRepairHistoryType(dto, request);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_DATA_TO_BE_UPDATED, e.getMessage());
        }

        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateListCount(Mockito.any())).thenReturn(1001);
        List<PmRepairDetail> relReadyToUpdateList = new ArrayList<>();
        PmRepairDetail pmRepairDetail = new PmRepairDetail();
        pmRepairDetail.setRepairDetailId("123");
        pmRepairDetail.setReasonCode("");
        relReadyToUpdateList.add(pmRepairDetail);
        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateList(Mockito.any())).thenReturn(relReadyToUpdateList);
        PowerMockito.when(pmRepairInfoRepository.updatePmRepairDetailProductTypeBatch(Mockito.any())).thenReturn(0);
        Assert.assertNotNull(service.updateRepairHistoryType(dto, request));

        pmRepairDetail.setRepairDetailId("");
        pmRepairDetail.setReasonCode("A1");
        relReadyToUpdateList.add(pmRepairDetail);
        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateList(Mockito.any())).thenReturn(relReadyToUpdateList);
        PowerMockito.when(pmRepairInfoRepository.updatePmRepairDetailProductTypeBatch(Mockito.any())).thenReturn(0);
        Assert.assertNotNull(service.updateRepairHistoryType(dto, request));

        pmRepairDetail.setRepairDetailId("123");
        pmRepairDetail.setReasonCode("35");
        relReadyToUpdateList.add(pmRepairDetail);
        PmRepairDetail pmRepairDetail1 = new PmRepairDetail();
        pmRepairDetail1.setRepairDetailId("1233");
        pmRepairDetail1.setReasonCode("3533");
        relReadyToUpdateList.add(pmRepairDetail1);
        PowerMockito.when(pmRepairInfoRepository.getRelReadyToUpdateList(Mockito.any())).thenReturn(relReadyToUpdateList);
        PowerMockito.when(pmRepairInfoRepository.updatePmRepairDetailProductTypeBatch(Mockito.any())).thenReturn(0);
        Assert.assertNotNull(service.updateRepairHistoryType(dto, request));

    }

    /* Started by AICoder, pid:n05c002d28lb9fb142cb0ad610ee78570d530bc9 */
    @Test
    public void testGetRepairInfo_EmptyList() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairSn("123456");
        PowerMockito.when(pmRepairInfoRepository.getRepairInfo(any())).thenReturn(Collections.emptyList());

        List<PmRepairDetailDTO> result = service.getRepairInfo(dto);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetRepairInfo_NoReplaceSns() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairSn("123456");
        PmRepairDetailDTO repair1 = new PmRepairDetailDTO();
        repair1.setReplaceSn(null);

        PowerMockito.when(pmRepairInfoRepository.getRepairInfo(any())).thenReturn(Collections.singletonList(repair1));

        List<PmRepairDetailDTO> result = service.getRepairInfo(dto);

        assertEquals(1, result.size());
        assertNull(result.get(0).getCombineFlag());
    }

    @Test
    public void testGetRepairInfo_ReplaceSns() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairSn("123456");
        PmRepairDetailDTO repair1 = new PmRepairDetailDTO();
        repair1.setReplaceSn(null);
        PmRepairDetailDTO repair2 = new PmRepairDetailDTO();
        repair2.setReplaceSn("SN1");

        PowerMockito.when(pmRepairInfoRepository.getRepairInfo(any())).thenReturn(Arrays.asList(repair1, repair2));

        List<PmRepairDetailDTO> result = service.getRepairInfo(dto);

        assertEquals(2, result.size());
        assertNull(result.get(0).getCombineFlag());
    }

    @Test
    public void testGetRepairInfo_WithReplaceSns_NoCombine() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairSn("123456");
        PmRepairDetailDTO repair1 = new PmRepairDetailDTO();
        repair1.setReplaceSn("SN1");

        PowerMockito.when(pmRepairInfoRepository.getRepairInfo(any())).thenReturn(Arrays.asList(repair1));
        PowerMockito.when(wipExtendIdentificationRepository.getList(anyMap())).thenReturn(Collections.emptyList());

        List<PmRepairDetailDTO> result = service.getRepairInfo(dto);

        assertEquals(1, result.size());
        assertNull(result.get(0).getCombineFlag());
    }

    @Test
    public void testGetRepairInfo_WithReplaceSns_WithCombine() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairSn("123456");
        PmRepairDetailDTO repair1 = new PmRepairDetailDTO();
        repair1.setReplaceSn("SN1");

        WipExtendIdentification combine1 = new WipExtendIdentification();
        combine1.setSn("SN1");

        PowerMockito.when(pmRepairInfoRepository.getRepairInfo(any())).thenReturn(Arrays.asList(repair1));
        PowerMockito.when(wipExtendIdentificationRepository.getList(anyMap())).thenReturn(Arrays.asList(combine1));

        List<PmRepairDetailDTO> result = service.getRepairInfo(dto);

        assertEquals(1, result.size());
        assertEquals(Constant.FLAG_Y, result.get(0).getCombineFlag());
    }
    /* Ended by AICoder, pid:n05c002d28lb9fb142cb0ad610ee78570d530bc9 */

    /* Started by AICoder, pid:hedae870cd725a5147b709d220b9538dcac306d4 */
    @Test
    public void testRepairQuantityControl_BomCodeAndLocationNoBlank() {
        assertDoesNotThrow(() -> service.repairQuantityControl(new PmRepairDetailDTO()));

        PmRepairDetailDTO dto1 = new PmRepairDetailDTO();
        dto1.setLocationNo("locationNo");
        assertDoesNotThrow(() -> service.repairQuantityControl(dto1));

        PmRepairDetailDTO dto2 = new PmRepairDetailDTO();
        dto2.setBomCode("123456789012345678901234");
        assertDoesNotThrow(() -> service.repairQuantityControl(dto2));
    }

    @Test
    public void testRepairQuantityControl_RepairDetailIdNoNull() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setRepairDetailId("1");

        PmRepairDetail repairDetail = new PmRepairDetail();
        repairDetail.setLocationNo("locationNo");
        PowerMockito.when(pmRepairInfoRepository.selectPmRepairDetailById(Mockito.any())).thenReturn(repairDetail);

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));

        PowerMockito.when(pmRepairInfoRepository.selectPmRepairDetailById(Mockito.any())).thenReturn(new PmRepairDetail());
        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_BomCodeAndLocationNoNotBlank() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("customerName");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(customerItemsDTO));

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        sysLookupTypesDTO.setAttribute1("customerNumber");
        List<SysLookupTypesDTO> sysLookupTypesList = Collections.singletonList(sysLookupTypesDTO);
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(sysLookupTypesList);

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName");
        sysLookupTypesDTO1.setAttribute2("customerNumber1");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300)).thenReturn(Collections.singletonList(sysLookupTypesDTO1));

        PowerMockito.when(repairInfoRepository.getRelOneCount(any())).thenReturn(3L);
        PowerMockito.when(mdsRemoteService.getRepairInfoByPartCode(any())).thenReturn(Collections.emptyList());
        PowerMockito.when(repairApprovalService.selectCount(any())).thenReturn(0);

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_OverLimitWithoutApproval() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("customerName");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(customerItemsDTO));

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        sysLookupTypesDTO.setAttribute1("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(Collections.singletonList(sysLookupTypesDTO));

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName");
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300)).thenReturn(Collections.singletonList(sysLookupTypesDTO1));

        PowerMockito.when(repairInfoRepository.getRelOneCount(any())).thenReturn(5L);
        PowerMockito.when(mdsRemoteService.getRepairInfoByPartCode(any())).thenReturn(Collections.emptyList());

        Exception exception = assertThrows(Exception.class, () -> {
            service.repairQuantityControl(dto);
        });

        assertNull(exception.getCause());
    }

    @Test
    public void testRepairQuantityControl_OverLimitWithApproval() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("customerName");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(customerItemsDTO));

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        sysLookupTypesDTO.setAttribute1("customerNumber");
        List<SysLookupTypesDTO> sysLookupTypesList = Collections.singletonList(sysLookupTypesDTO);
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(sysLookupTypesList);

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName");
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300)).thenReturn(Collections.singletonList(sysLookupTypesDTO1));

        PowerMockito.when(repairInfoRepository.getRelOneCount(any())).thenReturn(4L);
        PowerMockito.when(mdsRemoteService.getRepairInfoByPartCode(any())).thenReturn(Collections.emptyList());
        PowerMockito.when(repairApprovalService.selectCount(any())).thenReturn(1);

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_LimitControlQuantity() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("customerName");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(customerItemsDTO));

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        sysLookupTypesDTO.setAttribute1("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(Collections.singletonList(sysLookupTypesDTO));

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName");
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName2");
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300)).thenReturn(Arrays.asList(sysLookupTypesDTO1, sysLookupTypesDTO2));

        PowerMockito.when(repairInfoRepository.getRelOneCount(any())).thenReturn(4L);
        PowerMockito.when(mdsRemoteService.getRepairInfoByPartCode(any())).thenReturn(Collections.emptyList());
        PowerMockito.when(repairApprovalService.selectCount(any())).thenReturn(1);

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_NoCustomerItems() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.emptyList());

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_EmptySysLookupTypes() {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(new CustomerItemsDTO()));

        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(Collections.emptyList());

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }

    @Test
    public void testRepairQuantityControl_LimitWithApproval() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setLocationNo("locationNo");
        dto.setBomCode("123456789012345678901234");
        dto.setSn("sn");
        dto.setRepairDetailId("1");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("customerName");
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(any())).thenReturn(Collections.singletonList(customerItemsDTO));

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        sysLookupTypesDTO.setAttribute1("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_TYPES_TAGS_REPAIR_RECORDS)).thenReturn(Arrays.asList(sysLookupTypesDTO, new SysLookupTypesDTO()));

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("customerName");
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_7300)).thenReturn(Arrays.asList(sysLookupTypesDTO1, new SysLookupTypesDTO()));

        PowerMockito.when(pmRepairInfoRepository.selectPmRepairDetailById(Mockito.any())).thenReturn(null);
        PowerMockito.when(repairInfoRepository.getRelOneCount(any())).thenReturn(5L);
        PowerMockito.when(mdsRemoteService.getRepairInfoByPartCode(any())).thenReturn(Collections.emptyList());
        PowerMockito.when(repairApprovalService.selectCount(any())).thenReturn(1);

        assertDoesNotThrow(() -> service.repairQuantityControl(dto));
    }
    /* Ended by AICoder, pid:hedae870cd725a5147b709d220b9538dcac306d4 */

}
