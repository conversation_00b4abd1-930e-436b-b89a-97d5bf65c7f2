package com.zte.application.impl;/* Started by AICoder, pid:4206b20f9c85488eaa1e47667836ae7c */

import com.alibaba.fastjson.JSONObject;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PackingInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.ReelIdAndGoodDieInfoDTO;
import com.zte.interfaces.dto.TubeCoreDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, ProductionDeliveryRemoteService.class, DatawbRemoteService.class, PlanscheduleRemoteService.class,
        BarcodeCenterRemoteService.class, RedisCacheUtils.class})
public class TubeCoreServiceImplTest {

    @InjectMocks
    private TubeCoreServiceImpl tubeCoreServiceImpl;

    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;

    @Mock
    private WipInfoServiceImpl wipInfoService;

    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private ProcessControlServiceImpl processControlService;

    @Mock
    private PsScanHistoryServiceImpl psScanHistoryServiceImpl;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WipScanHisExtraRepository wipScanHisExtraRepository;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.mockStatic(RedisCacheUtils.class);

    }

    @Test
    public void packingScanTest() throws Exception {
        TubeCoreDTO tubeCoreDTO = new TubeCoreDTO();
        tubeCoreDTO.setProdPlanId("7123456");
        TubeCoreDTO tube = tubeCoreServiceImpl.packingScan(null);
        Assert.assertTrue(tube == null);

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.FAILED_TO_GET_REDIS_LOCK));
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        PowerMockito.when(technicalChangeDetailRepository.selectTechDetailByProdId(Mockito.anyString())).thenReturn(technicalChangeDetailDTO);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TUBE_CORE_PROD_HAVE_TECH));
        }
        PowerMockito.when(technicalChangeDetailRepository.selectTechDetailByProdId(Mockito.anyString())).thenReturn(null);
        String sn = "712345700010";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TUBE_CORE_SN_ILLEGAL));
        }
        sn = "***********";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TUBE_CORE_SN_ILLEGAL));
        }
        // 无装箱信息，无条码信息，且是查询，
        sn = "";
        PowerMockito.when(ProductionDeliveryRemoteService.selectPackInfoOfTubeCore(Mockito.anyString())).thenReturn(null);
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        // 无装箱信息，且最大条码有值
        sn = "712345600010";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        // 无装箱信息，且最大条码有值
        List<PackingInfoDTO> resultList = new ArrayList<>();
        PowerMockito.when(ProductionDeliveryRemoteService.selectPackInfoOfTubeCore(Mockito.anyString())).thenReturn(resultList);
        // 报错，不匹配
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WIP_INFO_NOT_MATCH_WITH_PACK_INFO));
        }
        // 无装箱，条码为空，则说明新的装箱场景
        sn = null;
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        // 查询批次信息，
        PsTask pstask = new PsTask();
        pstask.setTaskNo("taskNo");
        pstask.setProdplanId("7123456");
        PowerMockito.when(planscheduleRemoteService.getPstaskByProdId(Mockito.anyString())).thenReturn(null);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TUBE_CORE_PROD_NOT_EXIST));
        }
        PowerMockito.when(planscheduleRemoteService.getPstaskByProdId(Mockito.anyString())).thenReturn(pstask);
        List<ReelIdAndGoodDieInfoDTO> reelIdAndGoodDieQtyInfo = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.getReelIdAndGoodDieQtyInfo(Mockito.anyString())).thenReturn(reelIdAndGoodDieQtyInfo);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.INFOR_NOT_HAVE_MATERIAL_ISSUANCE_DETAILS));
        }
        ReelIdAndGoodDieInfoDTO reelIdAndGoodDieInfoDTO = new ReelIdAndGoodDieInfoDTO();
        reelIdAndGoodDieInfoDTO.setGoodDieQty(10);
        reelIdAndGoodDieInfoDTO.setTaskNo("taskNo");
        reelIdAndGoodDieInfoDTO.setReelId("reelId1");
        reelIdAndGoodDieQtyInfo.add(reelIdAndGoodDieInfoDTO);
        PowerMockito.when(DatawbRemoteService.getReelIdAndGoodDieQtyInfo(Mockito.anyString())).thenReturn(reelIdAndGoodDieQtyInfo);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.GOOD_DIE_QTY_NOT_EQUAL_TASK_QTY));
        }
        // 任务数量填充
        pstask.setTaskQty(new BigDecimal("12"));
        ReflectionTestUtils.setField(tubeCoreServiceImpl, "maxSnOfLpn", 9);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.GOOD_DIE_QTY_TOO_LARGER));
        }
        ReflectionTestUtils.setField(tubeCoreServiceImpl, "maxSnOfLpn", 12);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.GOOD_DIE_QTY_NOT_EQUAL_TASK_QTY));
        }
        pstask.setTaskQty(new BigDecimal("10"));
        List<String> barcodeList = new ArrayList<>();
        PowerMockito.when(barcodeCenterRemoteService.barcodeGenerate(Mockito.any())).thenReturn(barcodeList);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.BARCODE_GENERATE_TUBE_CORE_LPN_ERROR));
        }
        barcodeList.add("lpn");
        barcodeList.add("lpn1");
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.BARCODE_GENERATE_TUBE_CORE_LPN_ERROR));
        }
        barcodeList.remove(barcodeList.size() - 1);
        ReflectionTestUtils.setField(tubeCoreServiceImpl, "ifAsyncProcess", false);

        List<PsWorkOrderDTO> workOrders = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.anyString())).thenReturn(workOrders);
        TubeCoreDTO tubeCoreDTO1 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO1.getPackSuccessList().size() == 0);
        workOrders.add(null);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.anyString())).thenReturn(workOrders);
        TubeCoreDTO tubeCoreDTO2 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO2.getPackSuccessList().size() == 0);
        workOrders.clear();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrders.add(psWorkOrderDTO);
        TubeCoreDTO tubeCoreDTO3 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO3.getPackSuccessList().size() == 0);
        psWorkOrderDTO.setProcessGroup("1");
        psWorkOrderDTO.setLineCode("line1");
        psWorkOrderDTO.setSourceTask("7123456");
        List<CtRouteDetailDTO> nextCanScanWorkStationList = new ArrayList<>();
        PowerMockito.when(processControlService.getNextCanScanWorkStationList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(nextCanScanWorkStationList);
        TubeCoreDTO tubeCoreDTO4 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO4.getPackSuccessList().size() == 0);
        nextCanScanWorkStationList.add(null);
        TubeCoreDTO tubeCoreDTO5 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO5.getPackSuccessList().size() == 0);
        nextCanScanWorkStationList.clear();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        nextCanScanWorkStationList.add(ctRouteDetailDTO);
        TubeCoreDTO tubeCoreDTO6 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO6.getPackSuccessList().size() == 0);
        // 有装箱信息
        // 条码有值
        sn = "712345600010";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        PackingInfoDTO packingInfoDTO = new PackingInfoDTO();
        packingInfoDTO.setProdPlanId("7123456");
        packingInfoDTO.setCapacity(10);
        packingInfoDTO.setPackOrder(1);
        packingInfoDTO.setPackedQty(10);
        packingInfoDTO.setReelId("reelId2");
        resultList.add(packingInfoDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.selectPackInfoOfTubeCore(Mockito.anyString())).thenReturn(resultList);
        // 完全匹配情况，即装箱和条码过站信息对应
        // 新加一个GOOD_DIE 信息
        ReelIdAndGoodDieInfoDTO reelIdAndGoodDieInfoDTO2 = new ReelIdAndGoodDieInfoDTO();
        reelIdAndGoodDieInfoDTO2.setGoodDieQty(10);
        reelIdAndGoodDieInfoDTO2.setTaskNo("taskNo");
        reelIdAndGoodDieInfoDTO2.setReelId("reelId3");
        reelIdAndGoodDieQtyInfo.add(reelIdAndGoodDieInfoDTO2);
        PowerMockito.when(DatawbRemoteService.getReelIdAndGoodDieQtyInfo(Mockito.anyString())).thenReturn(reelIdAndGoodDieQtyInfo);
        // 任务数量填充
        pstask.setTaskQty(new BigDecimal("20"));
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.GOOD_DIE_QTY_NOT_EQUAL_TASK_QTY));
        }
        reelIdAndGoodDieInfoDTO2.setReelId("reelId2");
        TubeCoreDTO tubeCoreDTO7 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO7.getPackSuccessList().size() == 1);
        // 不完全匹配，差一个过站信息。
        sn = "";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        // 新加一个GOOD_DIE 信息
        ReelIdAndGoodDieInfoDTO reelIdAndGoodDieInfoDTO3 = new ReelIdAndGoodDieInfoDTO();
        reelIdAndGoodDieInfoDTO3.setGoodDieQty(20);
        reelIdAndGoodDieInfoDTO3.setTaskNo("taskNo");
        reelIdAndGoodDieInfoDTO3.setReelId("reelId3");
        reelIdAndGoodDieQtyInfo.add(reelIdAndGoodDieInfoDTO3);
        // 新增一个装箱信息 ，现在reelID2  和 reelID3 装箱信息存在数量，10，20， gooddie存在 reelID1,2,3 数量 10，10，20
        PackingInfoDTO packingInfoDTO1 = new PackingInfoDTO();
        packingInfoDTO1.setProdPlanId("7123456");
        packingInfoDTO1.setCapacity(20);
        packingInfoDTO1.setPackOrder(2);
        packingInfoDTO1.setPackedQty(20);
        packingInfoDTO1.setReelId("reelId3");
        resultList.add(packingInfoDTO1);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WIP_INFO_NOT_MATCH_WITH_PACK_INFO));
        }
        sn = "712345600020";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WIP_INFO_NOT_MATCH_WITH_PACK_INFO));
        }
        // 装箱了reel2，数量10
        sn = "712345600010";
        PowerMockito.when(wipInfoService.selectMaxSnOfProd(Mockito.anyString())).thenReturn(sn);
        ReflectionTestUtils.setField(tubeCoreServiceImpl, "maxSnOfLpn", 20);
        try {
            tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.GOOD_DIE_QTY_NOT_EQUAL_TASK_QTY));
        }
        pstask.setTaskQty(new BigDecimal("40"));
        TubeCoreDTO tubeCoreDTO8 = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO8.getPackSuccessList().size() == 1);
        ReflectionTestUtils.setField(tubeCoreServiceImpl, "ifAsyncProcess", true);
        // 重新补充一个，因为上面的调试，会把最后一个装箱信息删除
        resultList.add(packingInfoDTO1);
        TubeCoreDTO result = tubeCoreServiceImpl.packingScan(tubeCoreDTO);
        Assert.assertTrue(result.getTaskId().equals(tubeCoreDTO.getTaskId()));
    }

    @Test
    public void handlerDataOfLpnTest() throws Exception {
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        List<PsScanHistory> psScanHistoryList = new ArrayList<>();
        PackingInfoDTO entity = new PackingInfoDTO();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfoList.add(psWipInfo);
        boolean ifUseBarcodeCenterFlag = false;
        PowerMockito.when(BarcodeCenterRemoteService.checkIfUseBarcodeCenter(Mockito.anyString())).thenReturn(ifUseBarcodeCenterFlag);
        tubeCoreServiceImpl.handlerDataOfLpn(psWipInfoList, psScanHistoryList, entity);
        Assert.assertTrue(psWipInfoList.size() != 0);
        ifUseBarcodeCenterFlag = true;
        PowerMockito.when(BarcodeCenterRemoteService.checkIfUseBarcodeCenter(Mockito.anyString())).thenReturn(ifUseBarcodeCenterFlag);

        tubeCoreServiceImpl.handlerDataOfLpn(psWipInfoList, psScanHistoryList, entity);
        Assert.assertTrue(psWipInfoList.size() != 0);
        psWipInfo.setItemNo("123456789123abc");
        tubeCoreServiceImpl.handlerDataOfLpn(psWipInfoList, psScanHistoryList, entity);
        Assert.assertTrue(psWipInfoList.size() != 0);
        psScanHistoryList.add(new PsScanHistory());
        tubeCoreServiceImpl.handlerDataOfLpn(psWipInfoList, psScanHistoryList, entity);
        Assert.assertTrue(psWipInfoList.size() != 0);
    }

    @Test
    public void handlerDataAsyncWayTest() throws Exception {
        PackingInfoDTO beginLpnInfo = new PackingInfoDTO();
        PsTask pstask = new PsTask();
        Queue<PackingInfoDTO> needPackInfo = new ArrayDeque<>();
        TubeCoreDTO tubeCoreDTO = new TubeCoreDTO();
        String maxSn = new String();
        tubeCoreDTO.setPackSuccessList(new ArrayList<>());
        Whitebox.invokeMethod(tubeCoreServiceImpl, "handlerDataAsyncWay", beginLpnInfo, pstask, needPackInfo, tubeCoreDTO, maxSn);
        Assert.assertTrue(1==1);
    }

    @Test
    public void tryGetPackResultTest() {
        TubeCoreDTO tubeCoreDTO = new TubeCoreDTO();
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(null);
        TubeCoreDTO tubeCoreDTO1 = tubeCoreServiceImpl.tryGetPackResult(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO1 == null);
        TubeCoreDTO temp = new TubeCoreDTO();
        temp.setAsyncHandleFlag(true);
        String jsonString = JSONObject.toJSONString(temp);
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(jsonString);
        TubeCoreDTO tubeCoreDTO2 = tubeCoreServiceImpl.tryGetPackResult(tubeCoreDTO);
        Assert.assertTrue(tubeCoreDTO2.isAsyncHandleFlag());
    }
}
/* Ended by AICoder, pid:4206b20f9c85488eaa1e47667836ae7c */