package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.WipDailyStatisticReport;
import com.zte.domain.model.WipDailyStatisticReportRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WipDailyStatisticQueryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyString;

@PrepareForTest({BasicsettingRemoteService.class})
public class WipDailyStatisticServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	WipDailyStatisticServiceImpl service;
	@Mock
	private WipDailyStatisticReportRepository repository;

	@Test
	public void setLeadFlag() throws Exception {
		WipDailyStatisticReport wipDailyStatisticReport=new WipDailyStatisticReport();
		Map<String, String> leadMap = new HashMap<>();
		leadMap.put("HFS","2");
		Whitebox.invokeMethod(service,"setLeadFlag", leadMap,new PsTask(){{setLeadFlag("HFS");}},wipDailyStatisticReport);
		Whitebox.invokeMethod(service,"setLeadFlag", leadMap,new PsTask(),wipDailyStatisticReport);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}
	@Test
	public void transformCode() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<WipDailyStatisticReport> list = new ArrayList<>();
		WipDailyStatisticReport dto = new WipDailyStatisticReport();
		dto.setIsComplete("Y");
		dto.setIsScheduled("Y");
		dto.setOrgId(2347);
		list.add(dto);
		List<SysLookupTypesDTO> sysLookupTypeList = new ArrayList<>();
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("2347");
		sysLookupTypesDTO.setDescriptionChinV("test");
		sysLookupTypesDTO.setDescriptionEngV("test");
		sysLookupTypeList.add(sysLookupTypesDTO);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypeList);
		service.transformCode(list);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void selectRealTimePage() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
		SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
		sysLookupValuesDTO.setLookupCode(new BigDecimal(6001040));
		lookupValueList.add(sysLookupValuesDTO);
		PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
		List<String> planIds = new ArrayList<>();
		planIds.add("7777803");
		PowerMockito.when(repository.selectRealTimePage(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
		Assert.assertNull(service.selectRealTimePage(planIds,true));
	}

	@Test
	public void getBigExcelProcesserTest() throws Exception {
		WipDailyStatisticQueryDTO dto = new WipDailyStatisticQueryDTO();
		dto.setTitle(new String[] {"test"});
		Page<WipDailyStatisticReport> firstPage = new Page<>();
		firstPage.setTotalPage(4);
		ReflectionTestUtils.setField(service, "exportMultiThreadFlag", true);
		try {
			Whitebox.invokeMethod(service, "getBigExcelProcesser", dto, firstPage);
		} catch (Exception e) {
			Assert.assertTrue(e instanceof java.util.concurrent.ExecutionException);
		}
		ReflectionTestUtils.setField(service, "exportMultiThreadFlag", false);
		try {
			Whitebox.invokeMethod(service, "getBigExcelProcesser", dto, firstPage);
		} catch (Exception e) {
		Assert.assertTrue(e instanceof java.lang.RuntimeException);
		}
	}

}