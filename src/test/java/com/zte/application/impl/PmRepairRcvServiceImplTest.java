package com.zte.application.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BarcodeLockDetailRepository;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.PmRepairInfo;
import com.zte.domain.model.PmRepairRcv;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.domain.model.*;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.mds.MdsRepairRecordDTO;
import com.zte.interfaces.dto.middle.PmRepairCommonDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.spy;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

@PrepareForTest({CommonUtils.class,RedisHelper.class, MessageId.class, CollectionUtils.class,
        BasicsettingRemoteService.class, HttpClientUtil.class, CrafttechRemoteService.class,
        RequestHeadValidationUtil.class,
        PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class,ExcelCommonUtils.class,
        SpringContextUtil.class,FileUtils.class,EasyExcel.class,EasyExcelFactory.class,CenterfactoryRemoteService.class})
public class PmRepairRcvServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    PmRepairRcvServiceImpl service;
    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    MdsRemoteService mdsRemoteService;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private PmRepairRcvServiceRecodeImpl pmRepairRcvServiceRecode;
    @Mock
    private PsScanHistoryServiceImpl scanHistoryServiceImpl;
    @Mock
    private InputStream inputStream;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private HrmUserInfoService hrmUserInfoService;
    @Mock
    private PmRepairInfoService pmRepairInfoService;
    @Mock
    private PmRepairRcvService pmRepairRcvService;
    @Mock
    private HttpServletResponse response;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    private PmRepairRcvRecodeService pmRepairRcvRecodeService;
    @Mock
    private RepairApprovalService repairApprovalService;

    @Mock
    ExcelWriter writer;
    @Mock
    WriteSheet build;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    ExcelWriterBuilder writerBuilder;

    @Mock
    private MaintenanceMaterialLineRepository maintenanceMaterialLineRepository;
    @Before
    public void init() throws Exception {
        mockStatic(RedisHelper.class);
        mockStatic(ExcelCommonUtils.class);
        mockStatic(SpringContextUtil.class);
        mockStatic(CrafttechRemoteService.class);
        mockStatic(PlanscheduleRemoteService.class);
        mockStatic(ProductionDeliveryRemoteService.class);
        mockStatic(RequestHeadValidationUtil.class);
        mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
        mockStatic(FileUtils.class);
        mockStatic(EasyExcel.class);
        mockStatic(EasyExcelFactory.class);
        when(FileUtils.createFilePathAndCheck(anyString())).thenReturn("124");
        when(EasyExcelFactory.write(response.getOutputStream(), PmRepairRcvDetailDTO.class))
                .thenReturn(writerBuilder);
        PowerMockito.when(writerBuilder.excelType(Mockito.any())).thenReturn(writerBuilder);
        when(EasyExcelFactory.writerSheet(any(), any())).thenReturn(excelWriterSheetBuilder);
        when(excelWriterSheetBuilder.build()).thenReturn(build);
        when(EasyExcel.write(anyString(), any())).thenReturn(writerBuilder);
        when(writerBuilder.build()).thenReturn(writer);
    }

    @Test
    public void test_batchReject_fail() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        when(maintenanceMaterialLineRepository.getListByReceptionDetailIds(any())).thenReturn(Collections.emptyList());
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(null);
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setReceptionDetailId("1");
        dto.setLastUpdatedBy("10351947");
        service.batchReject(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_batchReject() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("N");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setReceptionDetailId("1");
        dto.setLastUpdatedBy("10351947");
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setReceptionDetailId("1");
        pmRepairRcvVo.setStatus("10560006");
        list.add(pmRepairRcvVo);
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(list);
        List<SysLookupValuesDTO> lookUpList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("10351947");
        lookUpList.add(sysLookupValuesDTO);
        when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(lookUpList);
        when(pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(any())).thenReturn(1);
        service.batchReject(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_updateSnAndInsertHistory() throws Exception {
        Whitebox.invokeMethod(service, "updateSnAndInsertHistory",Collections.emptyList());
        Assert.assertTrue(true);
    }

    @Test
    public void test_batchReceived_fail() throws Exception {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setStatus("'10560006'");
        dto.setReceptionDetailId("1");
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(null);
        service.batchReceived(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_batchReceived2() throws Exception {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setStatus("'10560006'");
        dto.setReceptionDetailId("1");
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setReceptionDetailId("1");
        pmRepairRcvVo.setStatus("10560006");
        pmRepairRcvVo.setFromStation("单板生产");
        list.add(pmRepairRcvVo);
        Map<String, String> processMap = new HashMap<>();
        processMap.put("craftSection","维修");
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(list);
        when(pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(any())).thenReturn(1);
        when(pmRepairRcvServiceRecode.getProcessMap()).thenReturn(processMap);
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo());
        when(psWipInfoService.updatePsWipInfoByScanBatch(any())).thenReturn(1);
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(any(), anyInt())).thenReturn(1);
        service.batchReceived(dto);
        Assert.assertTrue(true);
    }


    @Test
    public void test_batchReceived1() throws Exception {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setStatus("'10560006'");
        dto.setReceptionDetailId("1");
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setReceptionDetailId("1");
        pmRepairRcvVo.setStatus("10560006");
        pmRepairRcvVo.setFromStation("整机生产维修");
        list.add(pmRepairRcvVo);
        Map<String, String> processMap = new HashMap<>();
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(list);
        when(pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(any())).thenReturn(1);
        when(pmRepairRcvServiceRecode.getProcessMap()).thenReturn(processMap);
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo());
        when(psWipInfoService.updatePsWipInfoByScanBatch(any())).thenReturn(1);
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(any(), anyInt())).thenReturn(1);
        service.batchReceived(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_batchReceived4() throws Exception {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setStatus("'10560006'");
        dto.setReceptionDetailId("1");
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setReceptionDetailId("1");
        pmRepairRcvVo.setStatus("10560006");
        pmRepairRcvVo.setFromStation("整机单板送修");
        list.add(pmRepairRcvVo);
        Map<String, String> processMap = new HashMap<>();
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(list);
        when(pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(any())).thenReturn(1);
        when(pmRepairRcvServiceRecode.getProcessMap()).thenReturn(processMap);
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo());
        when(psWipInfoService.updatePsWipInfoByScanBatch(any())).thenReturn(1);
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(any(), anyInt())).thenReturn(1);
        service.batchReceived(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_batchReceived5() throws Exception {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setStatus("'10560006'");
        dto.setReceptionDetailId("1");
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setReceptionDetailId("1");
        pmRepairRcvVo.setStatus("10560006");
        list.add(pmRepairRcvVo);
        Map<String, String> processMap = new HashMap<>();
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(list);
        when(pmRepairRcvRepository.updatePmRePairRcvDetailStatusByParams(any())).thenReturn(1);
        when(pmRepairRcvServiceRecode.getProcessMap()).thenReturn(processMap);
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo());
        when(psWipInfoService.updatePsWipInfoByScanBatch(any())).thenReturn(1);
        when(scanHistoryServiceImpl.insertPsScanHistoryBatch(any(), anyInt())).thenReturn(1);
        service.batchReceived(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void test_selectPmRepairRcvDTOBySnNew1() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setSn("788888811111");
        record.setFromStation("单板生产");
        record.setPageType(new BigDecimal("1"));
        when(pmRepairRcvRepository.getPmRepairRcvDetail(any())).thenReturn(Collections.emptyList());
        when(pmRepairRcvRepository.getRelOneCount(any())).thenReturn(0L);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setItemNo("788888811111ABC");
        wipInfoList.add(psWipInfo);
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(wipInfoList);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("alibaba");
        customerItemsDTOS.add(customerItemsDTO);
        when(CenterfactoryRemoteService.getCustomerItemsInfo(any())).thenReturn(customerItemsDTOS);
        List<SysLookupTypesDTO> lookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("alibaba");
        sysLookupTypesDTO.setLookupMeaning("1");
        lookupTypesDTOS.add(sysLookupTypesDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2754)).thenReturn(lookupTypesDTOS);

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setAttribute2("customerNumber");
        sysLookupTypesDTO1.setLookupMeaning("alibaba");
        when(BasicsettingRemoteService.getSysLookUpValue((eq(Constant.LookUpKey.LOOK_7300)))).thenReturn(Collections.singletonList(sysLookupTypesDTO1));
        when(repairApprovalService.getByCondition(any())).thenReturn(new ArrayList<>());

        List<PmRepairInfo> pmRepairInfoList = new ArrayList<>();
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfoList.add(pmRepairInfo);
        when(pmRepairInfoService.getRelOneDetailList(any())).thenReturn(pmRepairInfoList);
        List<MdsRepairRecordDTO> mdsRepairRecordDTOS = new ArrayList<>();
        MdsRepairRecordDTO mdsRepairRecordDTO = new MdsRepairRecordDTO();
        mdsRepairRecordDTO.setRepairCount(0);
        mdsRepairRecordDTOS.add(mdsRepairRecordDTO);
        when(mdsRemoteService.getRepairCountFromMDS(any())).thenReturn(mdsRepairRecordDTOS);
        service.selectPmRepairRcvDTOBySnNew(record,"10351947");
        Assert.assertTrue(true);
    }

    @Test
    public void test_selectPmRepairRcvDTOBySnNew2() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setSn("788888811111");
        record.setFromStation("单板生产");
        record.setPageType(new BigDecimal("1"));
        when(pmRepairRcvRepository.getPmRepairRcvDetail(any())).thenReturn(Collections.emptyList());
        when(pmRepairRcvRepository.getRelOneCount(any())).thenReturn(0L);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setItemNo("788888811111");
        psWipInfo.setCurrProcessCode("N");
        wipInfoList.add(psWipInfo);
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(wipInfoList);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("alibaba");
        customerItemsDTOS.add(customerItemsDTO);
        when(CenterfactoryRemoteService.getCustomerItemsInfo(any())).thenReturn(customerItemsDTOS);
        List<SysLookupTypesDTO> lookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("alibaba");
        sysLookupTypesDTO.setLookupMeaning("1");
        lookupTypesDTOS.add(sysLookupTypesDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOS);
        when(repairApprovalService.getByCondition(any())).thenReturn(new ArrayList<>());
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setAttribute1("N");
        sysLookupTypesDTO1.setLookupMeaning("1");
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setAttribute1("N");
        sysLookupTypesDTO2.setLookupMeaning("1");
        lookupTypesDTOList.add(sysLookupTypesDTO1);
        lookupTypesDTOList.add(sysLookupTypesDTO2);
        when(BasicsettingRemoteService.getListByLookupType(anyMap())).thenReturn(lookupTypesDTOList);
        service.selectPmRepairRcvDTOBySnNew(record,"10351947");
        Assert.assertTrue(true);
    }

    @Test
    public void test_sentControl1() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("市场返还");
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        boolean sentControl = Whitebox.invokeMethod(service, "sentControl", record, pmRepairRcvDTO, wipInfoList);
        Assert.assertFalse(sentControl);
    }

    @Test
    public void test_sentControl2() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("整机单板送修");
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setItemNo("788888811111ABC");
        wipInfoList.add(psWipInfo);
        when(CenterfactoryRemoteService.getCustomerItemsInfo(any())).thenReturn(Collections.emptyList());
        boolean sentControl = Whitebox.invokeMethod(service, "sentControl", record, pmRepairRcvDTO, wipInfoList);
        Assert.assertFalse(sentControl);
    }

    @Test
    public void test_sentControl3() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("整机生产维修");
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setItemNo("788888811111ABC");
        wipInfoList.add(psWipInfo);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("alibaba");
        customerItemsDTOS.add(customerItemsDTO);
        when(CenterfactoryRemoteService.getCustomerItemsInfo(any())).thenReturn(customerItemsDTOS);
        List<SysLookupTypesDTO> lookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setAttribute1("customerNumber1");
        sysLookupTypesDTO1.setLookupMeaning("1");
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setAttribute1("customerNumber");
        sysLookupTypesDTO2.setLookupMeaning("1");
        lookupTypesDTOS.add(sysLookupTypesDTO1);
        lookupTypesDTOS.add(sysLookupTypesDTO2);
        lookupTypesDTOS.add(new SysLookupTypesDTO());
        when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_2754))).thenReturn(lookupTypesDTOS);

        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setAttribute2("customerNumber");
        sysLookupTypesDTO3.setLookupMeaning("alibaba");
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupMeaning("tenCent");
        SysLookupTypesDTO sysLookupTypesDTO5 = new SysLookupTypesDTO();
        sysLookupTypesDTO5.setAttribute2("customerNumber");
        when(BasicsettingRemoteService.getSysLookUpValue((eq(Constant.LookUpKey.LOOK_7300)))).thenReturn(Arrays.asList(sysLookupTypesDTO3, sysLookupTypesDTO4, sysLookupTypesDTO5));

        boolean sentControl = Whitebox.invokeMethod(service, "sentControl", record, pmRepairRcvDTO, wipInfoList);
        Assert.assertFalse(sentControl);
    }

    @Test
    public void test_deviceSentValid() throws Exception {
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setCurrProcessCode("Y");
        psWipInfo.setItemNo("788888811111ABC");
        wipInfoList.add(psWipInfo);
        List<SysLookupTypesDTO> lookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setAttribute1("N");
        sysLookupTypesDTO1.setLookupMeaning("1");
        lookupTypesDTOS.add(sysLookupTypesDTO1);
        when(BasicsettingRemoteService.getListByLookupType(anyMap())).thenReturn(lookupTypesDTOS);
        boolean deviceSentValid = Whitebox.invokeMethod(service, "deviceSentValid", pmRepairRcvDTO,1,1, wipInfoList);
        Assert.assertFalse(deviceSentValid);
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setAttribute1("Y");
        sysLookupTypesDTO2.setLookupMeaning("1");
        lookupTypesDTOS.add(sysLookupTypesDTO2);
        when(BasicsettingRemoteService.getListByLookupType(anyMap())).thenReturn(lookupTypesDTOS);
        boolean deviceSentValid2 = Whitebox.invokeMethod(service, "deviceSentValid", pmRepairRcvDTO,1,1, wipInfoList);
        Assert.assertFalse(deviceSentValid2);
    }

    @Test
    public void test_boardSentValid() throws Exception {
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("788888811111");
        psWipInfo.setCurrProcessCode("Y");
        psWipInfo.setItemNo("788888811111ABC");
        wipInfoList.add(psWipInfo);
        List<PmRepairInfo> pmRepairInfoList = new ArrayList<>();
        when(pmRepairInfoService.getRelOneDetailList(any())).thenReturn(pmRepairInfoList);
        List<MdsRepairRecordDTO> records = new ArrayList<>();
        MdsRepairRecordDTO mdsRepairRecordDTO = new MdsRepairRecordDTO();
        mdsRepairRecordDTO.setRepairCount(2);
        when(mdsRemoteService.getRepairCountFromMDS(any())).thenReturn(records);
        boolean deviceSentValid = Whitebox.invokeMethod(service, "boardSentValid", pmRepairRcvDTO, 1, 1, wipInfoList);
        Assert.assertFalse(deviceSentValid);
    }

    /* Started by AICoder, pid:g0387622decc5f014845084af1bbb31c60610eab */
    @Test
    public void updatePmRepairRcvDetailBatch(){
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno()).thenReturn(Pair.of("1", "2"));
        List<PmRepairRcvDetailDTO> recordList = new LinkedList<>();
        service.updatePmRepairRcvDetailBatch(recordList);
        for (int i = 0; i <3; i++) {
            PmRepairRcvDetailDTO a1 = new PmRepairRcvDetailDTO();
            a1.setSn(String.valueOf(i));
            a1.setProcessCode(String.valueOf(i));
            recordList.add(a1);
        }
        try {
            service.updatePmRepairRcvDetailBatch(recordList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_UNLOCK, e.getMessage());
        }

        when(psWipInfoRepository.getListByBatchSn(any()))
                .thenReturn(new LinkedList<>());
        when(RedisHelper.setnx(any(), any(), Mockito.anyInt()))
                .thenReturn(true);
        try {
            service.updatePmRepairRcvDetailBatch(recordList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_RECORD_NOT_FOUND, e.getMessage());
        }

        List<BSProcessDTO> bsProcessForWorkStation = new LinkedList<>();
        BSProcessDTO b2 = new BSProcessDTO();
        b2.setProcessCode("3");
        b2.setProcessName("3");
        b2.setProcessName(MpConstant.PROCESS_NAME_DIP_DOWNLINE);
        b2.setProcessCode("5");
        bsProcessForWorkStation.add(b2);
        BSProcessDTO b3 = new BSProcessDTO();
        b3.setProcessName(MpConstant.PROCESS_NAME_ASSY_BIND);
        b3.setProcessCode("6");
        bsProcessForWorkStation.add(b3);
        BSProcessDTO b4 = new BSProcessDTO();
        b4.setProcessName(MpConstant.PROCESS_NAME_ASSY_UPLINE);
        b4.setProcessCode("8");
        bsProcessForWorkStation.add(b4);
        when(CrafttechRemoteService.queryProcessInfo(any())).thenReturn(bsProcessForWorkStation);
        List<PmRepairInfo> mRepairRcvList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            PmRepairInfo a1 = new PmRepairInfo();
            a1.setSn(String.valueOf(i));
            a1.setProcessCode(String.valueOf(i));
            mRepairRcvList.add(a1);
        }
        when(pmRepairInfoService.getRelOnePageNoChange(any())).thenReturn(mRepairRcvList);
        try {
            service.updatePmRepairRcvDetailBatch(recordList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST_ACCEPT, e.getMessage());
        }

        List<PmRepairRcvDetail> tempList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            PmRepairRcvDetail a1 = new PmRepairRcvDetail();
            a1.setSn(String.valueOf(i));
            a1.setReceptionDetailId(String.valueOf(i));
            tempList.add(a1);
        }
        when(pmRepairRcvDetailService.getRelOneList(any()))
                .thenReturn(tempList);
        try {
            service.updatePmRepairRcvDetailBatch(recordList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            PsWipInfo a1 = new PsWipInfo();
            a1.setSn(String.valueOf(i));
            psWipInfoList.add(a1);
        }
        when(psWipInfoService.getListByBatchSn(any()))
                .thenReturn(psWipInfoList);
        PsScanHistory temp = new PsScanHistory();
        when(pmRepairInfoService.creatPsScanHistory(any(), any()))
                .thenReturn(temp);
        try {
            service.updatePmRepairRcvDetailBatch(recordList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:g0387622decc5f014845084af1bbb31c60610eab */

    /* Started by AICoder, pid:q2e0ex4ce9l6d76140c409c6a1e5d991b9d5200c */
    @Test
    public void getRepairRcvListBySnList() {
        List<PsWorkOrderBasic> basicList = new LinkedList<>();
        mockStatic(BasicsettingRemoteService.class);
        when(PlanscheduleRemoteService.getWorkBasicByTask(any(), any(), any()))
                .thenReturn(basicList);
        List<CtRouteDetailDTO> ctRouteDetailByRouteIds = new LinkedList<>();
        when(CrafttechRemoteService.getCtRouteDetailByRouteIds(any())).thenReturn(ctRouteDetailByRouteIds);

        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        dto.setFactoryId(new BigDecimal(52));
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getMessage());
        }

        List<PmRepairRcvDetailDTO> repairRcvDetailList = new LinkedList<>();
        dto.setRepairRcvDetailList(repairRcvDetailList);
        for (int i = 0; i < 2; i++) {
            PmRepairRcvDetailDTO temp = new PmRepairRcvDetailDTO();
            temp.setProcessCode(String.valueOf(i));
            repairRcvDetailList.add(temp);
        }
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }

        repairRcvDetailList.get(0).setSn("123");
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }

        repairRcvDetailList.forEach(item -> item.setSn(item.getProcessCode()));
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_RECORD_NOT_FOUND, e.getMessage());
        }

        List<PmRepairRcv> repairRcvList = new LinkedList<>();
        PmRepairRcv a1 = new PmRepairRcv();
        a1.setSn("1");
        repairRcvList.add(a1);
        when(pmRepairRcvRepository.getRelOneCountList(any()))
                .thenReturn(repairRcvList);
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_SCRAPPED, e.getMessage());
        }

        when(pmRepairRcvRepository.getRelOneCountList(any()))
                .thenReturn(null);
        List<PmRepairInfo> mRepairRcvList = new LinkedList<>();
        PmRepairInfo b1 = new PmRepairInfo();
        b1.setSn("0");
        b1.setProcessCode("0");
        mRepairRcvList.add(b1);
        when(pmRepairInfoService.getRelOnePageNoChange(any())).thenReturn(mRepairRcvList);
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_RECORD_NOT_FOUND, e.getMessage());
        }

        PmRepairInfo b2 = new PmRepairInfo();
        b2.setSn("1");
        b2.setProcessCode("1");
        mRepairRcvList.add(b2);
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_REPAIR_TWO, e.getMessage());
        }

        mRepairRcvList.forEach(item -> {
            item.setProcessCode("123");
        });
        repairRcvDetailList.forEach(item -> item.setProcessCode("123"));
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_REPAIR_TWO, e.getMessage());
        }

        b1.setStatus(new BigDecimal("3"));
        mRepairRcvList.forEach(item -> item.setProcessCode("123"));
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any()))
                .thenReturn(sysLookupTypesDTO);
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_REPAIR_TWO, e.getMessage());
        }

        b1.setStatus(new BigDecimal("3"));
        mRepairRcvList.forEach(item -> item.setFromStation(Constant.FROM_STATION_MACHINE));
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_REPAIR_TWO, e.getMessage());
        }

        sysLookupTypesDTO.setLookupMeaning("Y");
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_ZS_VAILDATE_FAIL, e.getMessage());
        }

        mRepairRcvList.forEach(item -> item.setStatus(new BigDecimal(3)));
        when(pmRepairRcvService.repairDQASCheck(any(), any()))
                .thenReturn(true);
        service.getRepairRcvListBySnList(dto);

        when(BasicsettingRemoteService.getSysLookupValues(any()))
                .thenReturn(null);
        service.getRepairRcvListBySnList(dto);

        List<SysLookupValuesDTO> sysLookupValues = new LinkedList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        sysLookupValues.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setAttribute1("Y");
        c2.setLookupMeaning("单板生产");
        sysLookupValues.add(c2);
        when(BasicsettingRemoteService.getSysLookupValues(any()))
                .thenReturn(sysLookupValues);
        List<BSProcessDTO> prList = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            BSProcessDTO d1 = new BSProcessDTO();
            d1.setProcessCode(String.valueOf(i));
            d1.setProcessName(String.valueOf(i));
            prList.add(d1);
            PsWorkOrderBasic temp = new PsWorkOrderBasic();
            temp.setProdPlanId(String.valueOf(i));
            temp.setRouteId(String.valueOf(i));
            basicList.add(temp);
            for (int j = 0; j < i + 1; j++) {
                CtRouteDetailDTO detail = new CtRouteDetailDTO();
                detail.setNextProcess(String.valueOf(j));
                detail.setProcessSeq(new BigDecimal(j));
                detail.setRouteId(String.valueOf(i));
                ctRouteDetailByRouteIds.add(detail);
            }
        }
        when(CrafttechRemoteService.queryProcessInfo(any()))
                .thenReturn(prList);
        b2.setFromStation("单板生产");
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        PmRepairInfo b3 = new PmRepairInfo();
        b3.setSn("0");
        mRepairRcvList.add(b3);
        PmRepairInfo b4 = new PmRepairInfo();
        b4.setSn("3");
        mRepairRcvList.add(b4);
        mRepairRcvList.forEach(item -> item.setRcvProdplanId(item.getSn()));
        try {
            service.getRepairRcvListBySnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:q2e0ex4ce9l6d76140c409c6a1e5d991b9d5200c */


    /* Started by AICoder, pid:q2e0ex4ce9l6d76140c409c6a1e5d991b9d5200c */
    @Test
    public void whitBoxTest() throws Exception {
        mockStatic(BasicsettingRemoteService.class);
        List<PmRepairRcvDetailDTO> repairRcvDetailList = new LinkedList<>();
        List<PmRepairInfo> returnList = new LinkedList<>();
        Map<String, String> processCodeMap = new HashMap<>();
        List<SysLookupValuesDTO> sysLookupValues = new LinkedList<>();
        List<BSProcessDTO> processList = new LinkedList<>();
        List<CtRouteDetailDTO> routeDetailList = new LinkedList<>();
        List<PsWorkOrderBasic> basicList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            String s = String.valueOf(i);
            SysLookupValuesDTO a1 = new SysLookupValuesDTO();
            a1.setAttribute1("Y");
            a1.setLookupMeaning(s);
            sysLookupValues.add(a1);
            BSProcessDTO b1 = new BSProcessDTO();
            b1.setProcessCode(s);
            b1.setProcessName(s);
            processList.add(b1);
            PmRepairInfo c1 = new PmRepairInfo();
            c1.setSn(s);
            c1.setRcvProdplanId(s);
            c1.setProcessCode(s);
            c1.setFromStation(s);
            returnList.add(c1);
            for (int j = 0; j < i + 2; j++) {
                CtRouteDetailDTO d1 = new CtRouteDetailDTO();
                d1.setRouteId(s);
                d1.setNextProcess(String.valueOf(j));
                d1.setProcessSeq(new BigDecimal(j));
                routeDetailList.add(d1);
            }
            PmRepairRcvDetailDTO f1 = new PmRepairRcvDetailDTO();
            f1.setSn(s);
            f1.setProcessCode(String.valueOf(i + 1));
            f1.setFromStation(s);
            repairRcvDetailList.add(f1);
            PsWorkOrderBasic e1 = new PsWorkOrderBasic();
            e1.setProdPlanId(s);
            e1.setRouteId(s);
            basicList.add(e1);
        }
        when(BasicsettingRemoteService.getSysLookupValues(any()))
                .thenReturn(sysLookupValues);
        when(CrafttechRemoteService.queryProcessInfo(any())).thenReturn(processList);
        when(CrafttechRemoteService.getCtRouteDetailByRouteIds(any())).thenReturn(routeDetailList);
        when(PlanscheduleRemoteService.getWorkBasicByTask(any(), any(), any()))
                .thenReturn(basicList);
        try {
            Whitebox.invokeMethod(service, "validProcessCode", repairRcvDetailList, returnList, processCodeMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        repairRcvDetailList.forEach(item -> item.setProcessCode(item.getProcessCode()));
        try {
            Whitebox.invokeMethod(service, "validProcessCode", repairRcvDetailList, returnList, processCodeMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        repairRcvDetailList.forEach(item -> item.setProcessCode("S"));
        try {
            Whitebox.invokeMethod(service, "validProcessCode", repairRcvDetailList, returnList, processCodeMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        repairRcvDetailList.forEach(item -> item.setProcessCode(null));
        try {
            Whitebox.invokeMethod(service, "validProcessCode", repairRcvDetailList, returnList, processCodeMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        PmRepairRcvDetailDTO rcvDetailDTO = new PmRepairRcvDetailDTO();
        List<CtRouteDetailDTO> routeDetail = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            CtRouteDetailDTO a1 = new CtRouteDetailDTO();
            a1.setProcessSeq(null);
            routeDetail.add(a1);
        }
        Whitebox.invokeMethod(service, "getUpdateProcessList", processCodeMap, rcvDetailDTO, routeDetail);

        List<PmRepairRcvDetail> list = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            PmRepairRcvDetail a1 = new PmRepairRcvDetail();
            a1.setFromStation(Constant.REPAIR_FROM_STATION);
            list.add(a1);
        }
        PsWipInfo psWipInfo = new PsWipInfo();
        String weProcessCode = null;
        String weCraftSection = null;
        try {
            Whitebox.invokeMethod(service, "validWeProcessCode", list, psWipInfo, weProcessCode, weCraftSection);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CRAFT_SECTION_NOT_LOADED,e.getMessage());
        }
        weProcessCode = "2";
        try {
            Whitebox.invokeMethod(service, "validWeProcessCode", list, psWipInfo, weProcessCode, weCraftSection);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CRAFT_SECTION_NOT_LOADED,e.getMessage());
        }
        weCraftSection = "3";
        try {
            Whitebox.invokeMethod(service, "validWeProcessCode", list, psWipInfo, weProcessCode, weCraftSection);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CRAFT_SECTION_NOT_LOADED,e.getMessage());
        }

        List<PmRepairRcvDetailDTO> excelInfoList = new LinkedList<>();
        List<BsPubHrvOrgId> existingUsers = new LinkedList<>();
        BsPubHrvOrgId a1 = new BsPubHrvOrgId();
        a1.setUserId("1");
        existingUsers.add(a1);
        when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyList())).thenReturn(existingUsers);
        for (int i = 0; i < 4; i++) {
            PmRepairRcvDetailDTO temp = new PmRepairRcvDetailDTO();
            temp.setReceptionBy(String.valueOf(i));
            excelInfoList.add(temp);
        }
        try {
            Whitebox.invokeMethod(service, "validEmpNo", excelInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EMPLOYEE_IS_NOT_EXIST,e.getMessage());
        }
    }
    /* Ended by AICoder, pid:7251bwd63d43c9f141b1082ae217a4058760ab07 */

    /* Started by AICoder, pid:e1bcbwc136j3ea614c5e0a1f40029210c646b186 */
    @Test
    public void whiteBoxTest2() throws Exception {
        List<PmRepairRcvDetailDTO> record = new LinkedList<>();
        PmRepairCommonDTO commonDTO = new PmRepairCommonDTO();
        List<PmRepairInfo> returnList = new LinkedList<>();
        Map<String, String> processCodeMap = new HashMap<>();
        List<String> snList = new LinkedList<>();
        List<PsWorkOrderBasic> workOrderBasicList = new LinkedList<>();
        List<CtRouteDetailDTO> routeDetailList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            String s = String.valueOf(i);
            snList.add(s);
            for (int j = 0; j < 3; j++) {
                CtRouteDetailDTO b1 = new CtRouteDetailDTO();
                b1.setRouteId(s);
                b1.setProcessSeq(new BigDecimal(j));
                b1.setNextProcess(String.valueOf(j));
                routeDetailList.add(b1);
                PsWorkOrderBasic a1 = new PsWorkOrderBasic();
                a1.setSourceTask(s);
                a1.setRouteId(s);
                a1.setProcessGroup(String.valueOf(j));
                a1.setWorkOrderNo(s);
                workOrderBasicList.add(a1);
            }
            processCodeMap.put(s, s);
            PmRepairRcvDetailDTO c1 = new PmRepairRcvDetailDTO();
            c1.setSn(s);
            c1.setProcessCode(s);
            c1.setRcvProdplanId(s);
            record.add(c1);
        }
        commonDTO.setSnList(snList);
        commonDTO.setRouteDetailList(routeDetailList);
        commonDTO.setWorkOrderBasicList(workOrderBasicList);
        Whitebox.invokeMethod(service, "buildUpdateDataByRule", record, commonDTO, returnList, processCodeMap);

        String productType = "";
        Whitebox.invokeMethod(service, "pushDataToDqs", record, productType);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any()))
                .thenReturn(sysLookupTypesDTO);
        Whitebox.invokeMethod(service, "pushDataToDqs", record, productType);
        sysLookupTypesDTO.setLookupMeaning("Y");
        try {
            Whitebox.invokeMethod(service, "pushDataToDqs", record, productType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_ZS_VAILDATE_FAIL, e.getMessage());
        }

        when(pmRepairRcvRecodeService.sendRepairDQAS(any())).thenReturn(true);
        Whitebox.invokeMethod(service, "pushDataToDqs", record, productType);
        try {
            Whitebox.invokeMethod(service, "getStationMap");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKSTATION_ERROR, e.getMessage());
        }
        PsWipInfo psWipInfo = new PsWipInfo();
        PmRepairRcvDetailDTO rcvDetailDTO = new PmRepairRcvDetailDTO();
        rcvDetailDTO.setUpdateProcess(true);
        List<PmRepairRcvDetail> list = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            PmRepairRcvDetail c1 = new PmRepairRcvDetail();
            list.add(c1);
        }
        Whitebox.invokeMethod(service, "setSomeProperties", psWipInfo, rcvDetailDTO, list);
    }
    @Test
    public void exportExcel() throws Exception {
        service.exportExcel(response);
        when(writerBuilder.build()).thenReturn(null);
        try {
            service.exportExcel(response);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

    }

    /* Ended by AICoder, pid:za883sab16y2b85144c50bd7a004bf74e6f47622 */


    /* Started by AICoder, pid:7e4d6u2af1hd89314d1b08cfe01de374eca83c3b */
    @Test
    public void resolveExcel() throws Exception {

        try {
            Whitebox.invokeMethod(service,"checkReadData",null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getMessage());
        }

        List<PmRepairRcvDetailDTO> excelInfoList = new LinkedList<>();
        PmRepairRcvDetailDTO a1 = new PmRepairRcvDetailDTO();
        excelInfoList.add(a1);
        try {
            Whitebox.invokeMethod(service,"checkReadData",excelInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        for (int i = 0; i < 12; i++) {
            PmRepairRcvDetailDTO temp = new PmRepairRcvDetailDTO();
            temp.setReceptionBy("1");
            temp.setReturnedBy("1");
            temp.setSn(String.valueOf(i));
            temp.setProcessName("smt");
            excelInfoList.add(temp);
        }
        a1.setSn("1");
        a1.setProcessName("sk");
        a1.setReturnedBy("4");
        a1.setReceptionBy("1");
        try {
            Whitebox.invokeMethod(service,"checkReadData",excelInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_REPEAT, e.getMessage());
        }

        excelInfoList.remove(a1);
        try {
            Whitebox.invokeMethod(service,"checkReadData",excelInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EMPLOYEE_IS_NOT_EXIST, e.getMessage());
        }

        List<BsPubHrvOrgId> existingUsers = new LinkedList<>();
        for (int i = 0; i < 11; i++) {
            BsPubHrvOrgId b1 = new BsPubHrvOrgId();
            b1.setUserId(String.valueOf(i));
            existingUsers.add(b1);
        }
        when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyList()))
                .thenReturn(existingUsers);
        try {
            Whitebox.invokeMethod(service,"checkReadData",excelInfoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EMPLOYEE_IS_NOT_EXIST, e.getMessage());
        }

        BsPubHrvOrgId b2 = new BsPubHrvOrgId();
        b2.setUserId(String.valueOf(11));
        existingUsers.add(b2);
        Whitebox.invokeMethod(service,"checkReadData",excelInfoList);
    }
    /* Ended by AICoder, pid:7e4d6u2af1hd89314d1b08cfe01de374eca83c3b */

    @Test
    public void selectPmRepairRcvDTOBySnNew() throws Exception {
        mockStatic(CommonUtils.class, PlanscheduleRemoteService.class);
        PmRepairRcv record = new PmRepairRcv();
        String empNo = "00286569";
        record.setSn("701032200012");
        record.setFromStation("单板测试段");
        record.setAutoGetErrDec("N");
        record.setPageType(new BigDecimal("1"));
        when(pmRepairRcvRepository.getRelOneCount(any())).thenReturn(0L);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7010322");
        psWipInfo.setSn("701032200012");
        psWipInfo.setItemNo("129571751071ZTAe");
        wipInfoList.add(psWipInfo);
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(wipInfoList);
        Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));
        List<PsWipInfo> wipInfoList1 = new ArrayList<>();
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setAttribute1("7010322");
        psWipInfo1.setSn("701032200012");
        psWipInfo1.setItemNo("129571751071ZTA");
        wipInfoList1.add(psWipInfo1);
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(wipInfoList1);
        Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));

        record.setAutoGetErrDec("Y");
        record.setProStageForRepair("1");
        List<PsTask> psTaskList = new ArrayList<>();
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(psTaskList);
        Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));
        PsTask psTask = new PsTask();
        psTask.setExternalType("210087830405");
        psTaskList.add(psTask);
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(psTaskList);
        when(mdsRemoteService.getMdsErrorMsg(any(), any())).thenReturn("");
        Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));
        when(mdsRemoteService.getMdsErrorMsg(any(), any())).thenReturn("1231");
        Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));
    }


    @Test
    public void getErrorDesFromMds() throws Exception {
        mockStatic(CommonUtils.class, PlanscheduleRemoteService.class);
        PmRepairRcv record = new PmRepairRcv();
        record.setSn("701032200012");
        record.setFromStation("单板测试段");
        record.setAutoGetErrDec("Y");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7010322");
        psWipInfo.setSn("701032200012");
        psWipInfo.setItemNo("129571751071ZTAE");
        wipInfoList.add(psWipInfo);
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesFromMds", record, pmRepairRcvDTO, wipInfoList));
        record.setProStageForRepair("2");
        PsTask psTask = new PsTask();
        when(PlanscheduleRemoteService.getPsTaskByTaskNo(any())).thenReturn(null);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesFromMds", record, pmRepairRcvDTO, wipInfoList));
        psTask.setExternalType("210087830405");
        when(PlanscheduleRemoteService.getPsTaskByTaskNo(any())).thenReturn(psTask);
        when(mdsRemoteService.getMdsErrorMsg(any(), any())).thenReturn("1231");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesFromMds", record, pmRepairRcvDTO, wipInfoList));
        when(mdsRemoteService.getMdsErrorMsg(any(), any())).thenReturn(":");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesFromMds", record, pmRepairRcvDTO, wipInfoList));
    }

    @Test
    public void getErrorDesFromMdsTwo() throws Exception {
        mockStatic(CommonUtils.class, PlanscheduleRemoteService.class);
        PmRepairRcv record = new PmRepairRcv();
        record.setSn("701032200012");
        record.setFromStation("单板测试段");
        record.setAutoGetErrDec("Y");
        record.setProStageForRepair("2");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7010322");
        psWipInfo.setSn("701032200012");
        psWipInfo.setItemNo("129571751071ZTA");
        wipInfoList.add(psWipInfo);
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<WipExtendIdentification> extendList = new ArrayList<>();

        when(wipExtendIdentificationRepository.getList(any())).thenReturn(extendList);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesFromMds", record, pmRepairRcvDTO, wipInfoList));
    }

    @Test
    public void getErrorDesForStageZj() throws Exception {
        mockStatic(CommonUtils.class, PlanscheduleRemoteService.class);
        PmRepairRcvDTO pmRepairRcvDTO = new PmRepairRcvDTO();
        List<WipExtendIdentification> extendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormSn("12314546465456");
        extendList.add(wipExtendIdentification);

        List<PsWipInfo> formSnWipInfo = new ArrayList<>();
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(formSnWipInfo);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesForStageZj", pmRepairRcvDTO, extendList, "12314546465456"));

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7010322");
        psWipInfo.setSn("701032200012");
        psWipInfo.setItemNo("129571751071ZTA");
        formSnWipInfo.add(psWipInfo);
        when(psWipInfoService.getWipInfoJoinTestBySn(any())).thenReturn(formSnWipInfo);

        PsTask psTask = new PsTask();
        when(PlanscheduleRemoteService.getPsTaskByTaskNo(any())).thenReturn(null);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesForStageZj", pmRepairRcvDTO, extendList, "701032200012"));
        psTask.setExternalType("210087830405");
        when(PlanscheduleRemoteService.getPsTaskByTaskNo(any())).thenReturn(psTask);
        when(mdsRemoteService.getMdsErrorMsg(any(), any())).thenReturn("1231");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getErrorDesForStageZj", pmRepairRcvDTO, extendList, "701032200012"));
    }

    @Test
    public void deletePmRepairRcvDeatil() {
        PmRepairRcvVo repairRcvVo = new PmRepairRcvVo();
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        dto.setReceptionDetailId("test123");
        PsWipInfo currWip = new PsWipInfo();
        when(pmRepairRcvRepository.getRepairPcvAndDeatilByDeatilId(any())).thenReturn(repairRcvVo);
        try {
            service.deletePmRepairRcvDeatil(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_REPAIR_STATUS_IS_NOT_FICTION, e.getMessage());
        }
        repairRcvVo.setStatus("10560005");
        repairRcvVo.setSn("test123");
        when(psWipInfoService.getWipInfoBySn(any())).thenReturn(currWip);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pmRepairRcvServiceRecode).addPsWipScanHistory(any(), any(), any());
        when(pmRepairRcvRepository.deletePmRePairRcvDetailByDetailId(any())).thenReturn(1);
        try {
            service.deletePmRepairRcvDeatil(dto);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        repairRcvVo.setFromStation("整机生产维修");
        repairRcvVo.setDeviceBarCode("test123");
        try {
            service.deletePmRepairRcvDeatil(dto);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
    /* Started by AICoder, pid:71a9df7279p792b141810a42107b822580621fbc */

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBom", null);
        Assert.assertTrue(1 == 1);
        List<PmRepairRcv> list = new ArrayList<>();
        PmRepairRcv entity = new PmRepairRcv();
        entity.setRcvProdplanId("1234567");
        list.add(entity);
        PmRepairRcv entity1 = new PmRepairRcv();
        entity1.setRcvProdplanId("12345671");
        entity1.setItemCode("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBom", list);
        Assert.assertTrue(list.get(0).getMBom().equals("test"));
        Assert.assertTrue(list.get(1).getMBom().equals("itemNo"));
    }

    /* Ended by AICoder, pid:71a9df7279p792b141810a42107b822580621fbc */
    /* Started by AICoder, pid:z4c9cp8579u02e8144f50a3290051d28247248d8 */
    @Test
    public void setMBomOfVoTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBomOfVo", null);
        Assert.assertTrue(1 == 1);
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo entity = new PmRepairRcvVo();
        entity.setRcvProdplanId("1234567");
        list.add(entity);
        PmRepairRcvVo entity1 = new PmRepairRcvVo();
        entity1.setRcvProdplanId("12345671");
        entity1.setItemCode("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBomOfVo", list);
        Assert.assertTrue(list.get(0).getMBom().equals("test"));
        Assert.assertTrue(list.get(1).getMBom().equals("itemNo"));
    }
    /* Ended by AICoder, pid:z4c9cp8579u02e8144f50a3290051d28247248d8 */

    @Test
    public void setMBomOfPmRepairRcvDTOTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBomOfPmRepairRcvDTO", null);
        Assert.assertTrue(1 == 1);
        PmRepairRcvDTO entity = new PmRepairRcvDTO();
        entity.setRcvProdplanId("1234567");
        entity.setItemCode("itemCode");
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBomOfPmRepairRcvDTO", entity);
        Assert.assertTrue(entity.getMbom().equals("test"));

        dto.setProdplanId("12345671");
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBomOfPmRepairRcvDTO", entity);
        Assert.assertTrue(entity.getMbom().equals("itemCode"));
    }

    /* Started by AICoder, pid:u2c6f5edb9sf566143f40aa091f41a16e7157c86 */
    @Test
    public void testSetUserNameWithEmptyList() {
        List<PmRepairRcvVo> list = new ArrayList<>();
        service.setUserName(list);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testSetUserNameWithNonEmptyList() {
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo vo1 = new PmRepairRcvVo();
        vo1.setReturnedBy("user1");
        vo1.setReturnedTo("user2");
        vo1.setDeliveryBy("user3");
        vo1.setReceptionBy("user4");

        PmRepairRcvVo vo2 = new PmRepairRcvVo();
        vo2.setReturnedBy("user5");
        vo2.setReturnedTo("user6");
        vo2.setDeliveryBy("user7");
        vo2.setReceptionBy("user8");

        list.add(vo1);
        list.add(vo2);

        Map<String, String> userMap = new HashMap<>();
        userMap.put("user1", "Name1");
        userMap.put("user2", "Name2");
        userMap.put("user3", "Name3");
        userMap.put("user4", "Name4");
        userMap.put("user5", "Name5");
        userMap.put("user6", "Name6");
        userMap.put("user7", "Name7");
        userMap.put("user8", "Name8");

        PmRepairRcvServiceImpl spy = spy(new PmRepairRcvServiceImpl());
        doReturn(userMap).when(spy).getUserNew(anyList());

        spy.setUserName(list);

        Assert.assertEquals("Name1user1", vo1.getReturnedByName());
        Assert.assertEquals("Name2user2", vo1.getReturnedToName());
        Assert.assertEquals("Name3user3", vo1.getDeliveryByName());
        Assert.assertEquals("Name4user4", vo1.getReceptionByName());

        Assert.assertEquals("Name5user5", vo2.getReturnedByName());
        Assert.assertEquals("Name6user6", vo2.getReturnedToName());
        Assert.assertEquals("Name7user7", vo2.getDeliveryByName());
        Assert.assertEquals("Name8user8", vo2.getReceptionByName());
    }

    @Test
    public void testSetUserNameWithNullValues() {
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo vo = new PmRepairRcvVo();
        vo.setReturnedBy(null);
        vo.setReturnedTo(null);
        vo.setDeliveryBy(null);
        vo.setReceptionBy(null);

        list.add(vo);

        Map<String, String> userMap = new HashMap<>();
        userMap.put("user1", "Name1");

        // 模拟getUserNew方法返回值
        PmRepairRcvServiceImpl spy = spy(new PmRepairRcvServiceImpl());
        doReturn(userMap).when(spy).getUserNew(anyList());

        spy.setUserName(list);

        Assert.assertNull(vo.getReturnedByName());
        Assert.assertNull(vo.getReturnedToName());
        Assert.assertNull(vo.getDeliveryByName());
        Assert.assertNull(vo.getReceptionByName());
    }

    @Test
    public void testSetUserNameWithMissingUserNames() {
        List<PmRepairRcvVo> list = new ArrayList<>();
        PmRepairRcvVo vo = new PmRepairRcvVo();
        vo.setReturnedBy("user1");
        vo.setReturnedTo("user2");
        vo.setDeliveryBy("user3");
        vo.setReceptionBy("user4");

        list.add(vo);

        Map<String, String> userMap = new HashMap<>();
        userMap.put("user1", "Name1");

        // 模拟getUserNew方法返回值
        PmRepairRcvServiceImpl spy = spy(new PmRepairRcvServiceImpl());
        doReturn(userMap).when(spy).getUserNew(anyList());

        spy.setUserName(list);

        Assert.assertEquals("Name1user1", vo.getReturnedByName());
        Assert.assertNull(vo.getReturnedToName());
        Assert.assertNull(vo.getDeliveryByName());
        Assert.assertNull(vo.getReceptionByName());
    }
    /* Ended by AICoder, pid:u2c6f5edb9sf566143f40aa091f41a16e7157c86 */
}
