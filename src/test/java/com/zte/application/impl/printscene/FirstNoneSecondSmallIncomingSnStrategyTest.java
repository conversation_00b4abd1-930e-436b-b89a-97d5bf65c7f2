package com.zte.application.impl.printscene;

import com.zte.application.impl.printscene.getUnboundSn.GetIncomingUnboundSnStrategy;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class FirstNoneSecondSmallIncomingSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    FirstNoneSecondSmallIncomingSnStrategy service;

    @Mock
    GetIncomingUnboundSnStrategy getIncomingUnboundSnStrategy;

    @Test
    public void getSceneType() throws Exception {
        Assert.assertEquals("4", service.getSceneType());
    }

    @Test
    public void generateSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setProdplanId("ProdplanId");
        dto.setQty(1);
        dto.setSnType("ProdplanId");
        dto.setLine("001");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals("workorder.not.find", e.getMessage());
        }
    }
}