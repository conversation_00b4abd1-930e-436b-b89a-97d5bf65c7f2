package com.zte.application.impl.printscene;

import com.zte.application.PsWorkOrderSnAssignService;
import com.zte.application.PsWorkOrderSnService;
import com.zte.application.SnCollectInfoService;
import com.zte.application.WipEntityScanInfoService;
import com.zte.application.impl.printscene.getParentSn.GetVirtualParentSnStrategy;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PsWorkOrderSn;
import com.zte.domain.model.PsWorkOrderSnAssign;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.Constant.DOING;

@PrepareForTest({PlanscheduleRemoteService.class})
public class FirstSmallSecondNoneStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    FirstSmallSecondNoneStrategy service;

    @Mock
    SnCollectInfoService snCollectInfoService;

    @Mock
    private PsWorkOrderSnService psWorkOrderSnService;

    @Mock
    private PsWorkOrderSnAssignService psWorkOrderSnAssignService;
    @Mock
    WipEntityScanInfoService wipEntityScanInfoService;
    @Mock
    GetVirtualParentSnStrategy getVirtualParentSnStrategy;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }


    @Test
    public void generateSn() throws Exception {

        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setLine("Line");
        dto.setProdplanId("ProdplanId");
        dto.setQty(2);
        dto.setSnType("1");
        dto.setIncomingSn("");


        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("WorkOrderNo");
        psWorkOrderDTO.setLineCode("Line");
        psWorkOrderDTO.setWorkOrderStatus(DOING);
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.findBasicWorkOrderInfo(Mockito.anyObject())).thenReturn(workOrderList);

        List<PsWorkOrderSn> psWorkOrderSns = new ArrayList<>();
        PowerMockito.when(psWorkOrderSnService.getListByworkOrderNos(Mockito.anyObject())).thenReturn(psWorkOrderSns);
        PowerMockito.when(psWorkOrderSnService.insertPsWorkOrderSnSelective(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(psWorkOrderSnAssignService.insertPsWorkOrderSnAssign(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(psWorkOrderSnAssignService.updatePsWorkOrderSnAssignById(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(wipEntityScanInfoService.generateSnTrans(Mockito.anyObject())).thenReturn(1);


        PsWorkOrderSn psWorkOrderSn = new PsWorkOrderSn();
        psWorkOrderSn.setRemark("P");
        psWorkOrderSn.setWorkOrderNo("WorkOrderNo");
        psWorkOrderSns.add(psWorkOrderSn);
        PowerMockito.when(psWorkOrderSnService.getListByworkOrderNos(Mockito.anyObject())).thenReturn(psWorkOrderSns);

        List<PsWorkOrderSnAssign> psWorkOrderSnAssigns = new ArrayList<>();
        PsWorkOrderSnAssign psWorkOrderSnAssign = new PsWorkOrderSnAssign();
        psWorkOrderSnAssigns.add(psWorkOrderSnAssign);

        PowerMockito.when(psWorkOrderSnAssignService.selectByWorkOrderNo(Mockito.anyObject())).thenReturn(psWorkOrderSnAssigns);
        psWorkOrderSnAssign.setEnabledFlag("Y");

        psWorkOrderDTO.setAttribute1("AMT-B");
        psWorkOrderDTO.setCraftSection("AMT-B");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_SMT_CRAFT, e.getMessage());
        }

        psWorkOrderDTO.setAttribute1(null);
        psWorkOrderDTO.setCraftSection("SMT-B");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_CARFT_NULL, e.getMessage());
        }

        psWorkOrderDTO.setAttribute1("SMT-A");
        psWorkOrderDTO.setCraftSection("SMT-B");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CRAFT_NOT_CONTAIN_CURRENT, e.getMessage());
        }

        psWorkOrderDTO.setAttribute1("SMT-A");
        psWorkOrderDTO.setCraftSection("SMT-A");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BIG_SN_NOT_ALLOWED_IN_FIRST_INSTRUCTION, e.getMessage());
        }

        psWorkOrderDTO.setAttribute1("SMT-B -> SMT-A ");
        psWorkOrderDTO.setCraftSection("SMT-A");
        dto.setSnType("2");
        dto.setIncomingSn("");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAXIMUM_OF_BATCHES_REACHED, e.getMessage());
        }

        psWorkOrderSn.setRemark("");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }

        psWorkOrderDTO.setAttribute1("SMT-A -> SMT-B");
        psWorkOrderDTO.setCraftSection("SMT-A");
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }

        dto.setSnType("2");
        PowerMockito.when(getVirtualParentSnStrategy.getParentSn(Mockito.anyObject())).thenReturn("income111");
        psWorkOrderDTO.setTaskQty(BigDecimal.ZERO);
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAXIMUM_OF_BATCHES_REACHED, e.getMessage());
        }


        psWorkOrderDTO.setTaskQty(BigDecimal.valueOf(1));
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAXIMUM_OF_BATCHES_REACHED_PARTLY, e.getMessage());
        }

        psWorkOrderDTO.setTaskQty(BigDecimal.TEN);
        PowerMockito.when(getVirtualParentSnStrategy.getParentSn(Mockito.anyObject())).thenReturn("");
        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.anyList())).thenReturn(new ArrayList<>());
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }

        PsWorkOrderBasicDTO basicDTO = new PsWorkOrderBasicDTO();
        basicDTO.setItemNo("");
        List<PsWorkOrderBasicDTO> itemNoByWorkOrderNoList= new ArrayList<>();
        itemNoByWorkOrderNoList.add(basicDTO);
        PowerMockito.when(getVirtualParentSnStrategy.getParentSn(Mockito.anyObject())).thenReturn("");
        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.anyList())).thenReturn(itemNoByWorkOrderNoList);

        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }

        basicDTO.setItemNo("123123");
        itemNoByWorkOrderNoList.remove(0);
        itemNoByWorkOrderNoList.add(basicDTO);
        PowerMockito.when(getVirtualParentSnStrategy.getParentSn(Mockito.anyObject())).thenReturn("");
        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.anyList())).thenReturn(itemNoByWorkOrderNoList);
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }

        basicDTO.setItemNo("1231231231231");
        PowerMockito.when(centerfactoryRemoteService.createCfBizCode(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }
        List<String> cfBizCode = new ArrayList<>();
        cfBizCode.add("20250220-000001");
        PowerMockito.when(centerfactoryRemoteService.createCfBizCode(Mockito.any())).thenReturn(cfBizCode);
        try {
            service.generateSn(dto);
        } catch (Exception e) {
        }
    }
}