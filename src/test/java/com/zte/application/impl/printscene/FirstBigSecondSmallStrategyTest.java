package com.zte.application.impl.printscene;

import com.zte.application.PsWorkOrderSnAssignService;
import com.zte.application.PsWorkOrderSnService;
import com.zte.application.WipEntityScanInfoService;
import com.zte.application.impl.printscene.getUnboundSn.GetBigUnboundSnStrategy;
import com.zte.domain.model.PsWorkOrderSn;
import com.zte.domain.model.PsWorkOrderSnAssign;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.Constant.DOING;
import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({PlanscheduleRemoteService.class})
public class FirstBigSecondSmallStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    FirstBigSecondSmallStrategy service;

    @Mock
    GetBigUnboundSnStrategy getBigUnboundSnStrategy;
    @Mock
    WipEntityScanInfoService wipEntityScanInfoService;
    @Mock
    private PsWorkOrderSnService psWorkOrderSnService;
    @Mock
    private PsWorkOrderSnAssignService psWorkOrderSnAssignService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void getSceneType() throws Exception {
        Assert.assertNotNull(service.getSceneType());
    }

    //    @Test
    public void generateSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setProdplanId("ProdplanId");
        dto.setQty(1);
        dto.setSnType("ProdplanId");
        dto.setLine("001");
        Assert.assertNotNull(service.generateSn(dto));
    }


    @Test
    public void generateSn1() throws Exception {
        PowerMockito.when(getBigUnboundSnStrategy.generateSnFirst()).thenReturn(true);

        //=============大板码=============
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setLine("Line");
        dto.setProdplanId("ProdplanId");
        dto.setQty(1);
        dto.setSnType("1");

        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("WorkOrderNo");
        psWorkOrderDTO.setLineCode("Line");
        psWorkOrderDTO.setWorkOrderStatus(DOING);
        psWorkOrderDTO.setAttribute1("SMT-A -> SMT-B ");
        psWorkOrderDTO.setCraftSection("SMT-A");
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.findBasicWorkOrderInfo(Mockito.anyObject())).thenReturn(workOrderList);


        List<PsWorkOrderSn> psWorkOrderSns = new ArrayList<>();
        PowerMockito.when(psWorkOrderSnService.getListByworkOrderNos(Mockito.anyObject())).thenReturn(psWorkOrderSns);
        PowerMockito.when(psWorkOrderSnService.insertPsWorkOrderSnSelective(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(psWorkOrderSnAssignService.insertPsWorkOrderSnAssign(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(psWorkOrderSnAssignService.updatePsWorkOrderSnAssignById(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(wipEntityScanInfoService.generateSnTrans(Mockito.anyObject())).thenReturn(1);
        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        psWorkOrderDTO.setTaskQty(BigDecimal.ZERO);
        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        psWorkOrderDTO.setTaskQty(BigDecimal.TEN);
        service.generateSn(dto, getBigUnboundSnStrategy);

        PsWorkOrderSn psWorkOrderSn = new PsWorkOrderSn();
        psWorkOrderSn.setRemark("P");
        psWorkOrderSn.setWorkOrderNo("WorkOrderNo");
        psWorkOrderSns.add(psWorkOrderSn);
        PowerMockito.when(psWorkOrderSnService.getListByworkOrderNos(Mockito.anyObject())).thenReturn(psWorkOrderSns);
        service.generateSn(dto, getBigUnboundSnStrategy);

        List<PsWorkOrderSnAssign> psWorkOrderSnAssigns = new ArrayList<>();
        PsWorkOrderSnAssign psWorkOrderSnAssign = new PsWorkOrderSnAssign();
        psWorkOrderSnAssigns.add(psWorkOrderSnAssign);
        psWorkOrderSnAssign.setEnabledFlag("N");

        PowerMockito.when(psWorkOrderSnAssignService.selectByWorkOrderNo(Mockito.anyObject())).thenReturn(psWorkOrderSnAssigns);
        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        psWorkOrderSnAssign.setEnabledFlag("Y");
        service.generateSn(dto, getBigUnboundSnStrategy);


        //=============小板码=============
        PowerMockito.when(getBigUnboundSnStrategy.getUnboundSn(any())).thenReturn("111");
        dto.setQty(2);
        dto.setSnType("2");
        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        psWorkOrderDTO.setCraftSection("SMT-B");
        try {
            service.generateSn(dto, getBigUnboundSnStrategy);
        } catch (Exception e) {
        }

        psWorkOrderSnAssign.setEnabledFlag("Y");
        psWorkOrderSn.setRemark("");

        Assert.assertNotNull(service.generateSn(dto, getBigUnboundSnStrategy));
    }
}