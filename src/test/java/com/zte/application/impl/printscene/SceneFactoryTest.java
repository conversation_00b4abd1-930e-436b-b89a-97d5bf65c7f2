package com.zte.application.impl.printscene;

import com.zte.common.model.MessageId;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

public class SceneFactoryTest extends PowerBaseTestCase {
    @InjectMocks
    SceneFactory service;

    @Test
    public void getSceneStrategy() throws Exception {
        try {
            service.getSceneStrategy(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRINT_SCENES_NOT_GET, e.getMessage());
        }
        service.getSceneStrategy("1");
        service.getSceneStrategy("2");
        service.getSceneStrategy("3");
        service.getSceneStrategy("4");
        try {
            service.getSceneStrategy("-1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRINT_SCENES_UNSUPPORTED, e.getMessage());
        }
    }

}