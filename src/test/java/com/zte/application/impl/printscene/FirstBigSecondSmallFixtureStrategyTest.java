package com.zte.application.impl.printscene;

import com.zte.application.PsWorkOrderSnAssignService;
import com.zte.application.PsWorkOrderSnService;
import com.zte.application.WipEntityScanInfoService;
import com.zte.application.impl.printscene.getUnboundSn.GetBigUnboundSnStrategy;
import com.zte.application.impl.printscene.getUnboundSn.GetFixtureUnboundSnStrategy;
import com.zte.common.model.MessageId;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

@PrepareForTest({PlanscheduleRemoteService.class})
public class FirstBigSecondSmallFixtureStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    FirstBigSecondSmallFixtureStrategy service;

    @Mock
    GetFixtureUnboundSnStrategy getFixtureUnboundSnStrategy;
    @Mock
    GetBigUnboundSnStrategy getBigUnboundSnStrategy;
    @Mock
    WipEntityScanInfoService wipEntityScanInfoService;
    @Mock
    private PsWorkOrderSnService psWorkOrderSnService;
    @Mock
    private PsWorkOrderSnAssignService psWorkOrderSnAssignService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void getSceneType() throws Exception {
        Assert.assertEquals("3",service.getSceneType());
    }

    @Test
    public void generateSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setProdplanId("ProdplanId");
        dto.setQty(1);
        dto.setSnType("ProdplanId");
        dto.setLine("001");
        try {
            service.generateSn(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.GET_CURRENT_WORKORDER_ERROR,e.getMessage());
        }
    }
}