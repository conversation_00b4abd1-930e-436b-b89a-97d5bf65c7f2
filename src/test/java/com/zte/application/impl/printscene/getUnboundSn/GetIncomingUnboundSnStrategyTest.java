package com.zte.application.impl.printscene.getUnboundSn;

import com.zte.application.SnCollectInfoService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ParentsnAndSnRepository;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.ArgumentMatchers.any;

public class GetIncomingUnboundSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    GetIncomingUnboundSnStrategy service;
    @Mock
    private ParentsnAndSnRepository parentsnAndSnRepository;

    @Test
    public void getUnboundSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        try {
            service.getUnboundSn(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.INCOMING_SN_NULL, e.getMessage());
        }

        dto.setIncomingSn("111111");
        PowerMockito.when(parentsnAndSnRepository.getCountByParentSn(any())).thenReturn(1);
        try {
            service.getUnboundSn(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.HAS_PRINTED_CANNOT_REPEAT, e.getMessage());
        }

        PowerMockito.when(parentsnAndSnRepository.getCountByParentSn(any())).thenReturn(0);
        Assert.assertNotNull(service.getUnboundSn(dto));
    }

    @Test
    public void generateSnFirst() {
        Assert.assertFalse(service.generateSnFirst());
    }
}