package com.zte.application.impl.printscene.getUnboundSn;

import com.zte.application.SnCollectInfoService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.ArgumentMatchers.any;

public class GetBigUnboundSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    GetBigUnboundSnStrategy service;
    @Mock
    private SnCollectInfoService snCollectInfoService;

    @Test
    public void getUnboundSn() throws Exception {
        PowerMockito.when(snCollectInfoService.getUnboundSn("001")).thenReturn("");
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setLine("001");
        try {
            service.getUnboundSn(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.NOT_FIND_UNBOUND_BIG_SN, e.getMessage());
        }
        PowerMockito.when(snCollectInfoService.getUnboundSn("001")).thenReturn("1111111");
        PowerMockito.when(snCollectInfoService.boundSn("1111111")).thenReturn(1);
        Assert.assertNotNull(service.getUnboundSn(dto));
    }

    @Test
    public void generateSnFirst() {
        Assert.assertTrue(service.generateSnFirst());
    }
}