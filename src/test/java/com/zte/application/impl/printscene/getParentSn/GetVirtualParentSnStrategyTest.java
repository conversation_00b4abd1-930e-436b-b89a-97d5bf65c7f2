package com.zte.application.impl.printscene.getParentSn;

import com.zte.application.ParentsnAndSnService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

public class GetVirtualParentSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    GetVirtualParentSnStrategy service;

    @Mock
    ParentsnAndSnService parentsnAndSnService;


    @Test
    public void getParentSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setIncomingSn("111");
        try {
            service.getParentSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INCOMING_SN_NOT_NULL, e.getMessage());
        }

        dto.setIncomingSn(null);
        Assert.assertNull(service.getParentSn(dto));
    }

    @Test
    public void secondBoard() {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setIncomingSn("");
        try {
            service.secondBoard(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SECOND_SIDE_NEED_SMALL_SN, e.getMessage());
        }

        dto.setIncomingSn("111");
        dto.setSnType("1");
        try {
            service.secondBoard(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SECOND_SIDE_NEED_SMALL_SN, e.getMessage());
        }
        dto.setSnType("2");
        PowerMockito.when(parentsnAndSnService.getParentsn(Mockito.any())).thenReturn("");
        try {
            service.secondBoard(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_SN_NULL, e.getMessage());
        }

        PowerMockito.when(parentsnAndSnService.getParentsn(Mockito.any())).thenReturn("123");
        Assert.assertNotNull(service.secondBoard(dto));
    }


}