package com.zte.application.impl.printscene.getUnboundSn;

import com.zte.application.SnCollectInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.ArgumentMatchers.any;

public class GetFixtureUnboundSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    GetFixtureUnboundSnStrategy service;
    @Mock
    WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    SnCollectInfoService snCollectInfoService;

    @Test
    public void getUnboundSn() throws Exception {
        PowerMockito.when(snCollectInfoService.getUnboundSn("001")).thenReturn("1111111");
        PowerMockito.when(snCollectInfoService.boundSn("1111111")).thenReturn(1);
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSn("1111111")).thenReturn(null);
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        dto.setLine("001");
        try {
            service.getUnboundSn(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.NOT_FIND_UNBOUND_BIG_SN_BY_FIXTURE, e.getMessage());
        }

        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSn("1111111")).thenReturn(wipExtendIdentification);
        try {
            service.getUnboundSn(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.NOT_FIND_UNBOUND_BIG_SN_BY_FIXTURE, e.getMessage());
        }

        wipExtendIdentification.setSn("2222");
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSn("1111")).thenReturn(wipExtendIdentification);
        Assert.assertNotNull(service.getUnboundSn(dto));
    }
}