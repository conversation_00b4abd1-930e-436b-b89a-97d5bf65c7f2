package com.zte.application.impl.printscene.getParentSn;

import com.zte.common.model.MessageId;
import com.zte.domain.model.ParentsnAndSnRepository;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.ArgumentMatchers.any;

public class GetIncomingParentSnStrategyTest extends PowerBaseTestCase {
    @InjectMocks
    GetIncomingParentSnStrategy service;
    @Mock
    private ParentsnAndSnRepository parentsnAndSnRepository;

    @Test
    public void getParentSn() throws Exception {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        try {
            service.getParentSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INCOMING_SN_NULL, e.getMessage());
        }

        dto.setIncomingSn("111111");
        PowerMockito.when(parentsnAndSnRepository.getCountByParentSn(any())).thenReturn(1);
        try {
            service.getParentSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HAS_PRINTED_CANNOT_REPEAT, e.getMessage());
        }

        PowerMockito.when(parentsnAndSnRepository.getCountByParentSn(any())).thenReturn(0);
        Assert.assertNotNull(service.getParentSn(dto));
    }

    @Test
    public void secondBoard() {
        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        try {
            service.secondBoard(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_ALLOWED_IN_SECOND_INSTRUCTION, e.getMessage());
        }
    }


}