/*Started by AICoder, pid:ad6154319ehb24714c4a08dee1786831d4333b41*/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BaItem;
import com.zte.domain.model.PrintInfo;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.UrlConfig;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.*;
import com.zte.springbootframe.util.doc.WordProcesser;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@PrepareForTest({ImageIO.class,PDDocument.class,BarCodeUtils.class,
        ZplConverterUtils.class,PrintServiceImpl.class,BasicsettingRemoteService.class,ProductionDeliveryRemoteService.class,
        PlanscheduleRemoteService.class,DateUtil.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class, BarcodeCenterRemoteService.class})
public class PrintServiceImplTest extends PowerBaseTestCase  {

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @InjectMocks
    private PrintServiceImpl printServiceImpl;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private UrlConfig urlConfig;

    @Mock
    private WordProcesser wordProcesser;

    @Mock
    PDDocument pdDocument;
    @Mock
    File file;
    @Mock
    PDFRenderer pdfRenderer;
    @Mock
    BufferedImage bufferedImage;
    @Mock
    private IepmsRemoteService iepmsRemoteService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void setup() throws Exception{
        PowerMockito.mockStatic(ImageIO.class,PDDocument.class,BarCodeUtils.class,BasicsettingRemoteService.class,
                ZplConverterUtils.class,
                ProductionDeliveryRemoteService.class,PlanscheduleRemoteService.class,DateUtil.class);
        PowerMockito.field(PrintServiceImpl.class, "printImageDip").set(printServiceImpl, 55);
        PowerMockito.field(PrintServiceImpl.class, "printImageBlack").set(printServiceImpl, 55);
        PowerMockito.field(PrintServiceImpl.class, "printImageType").set(printServiceImpl, "55");
        PowerMockito.field(PrintServiceImpl.class, "printImageZpl").set(printServiceImpl, "%s");
    }

    @Test
    public void testVeneerPackagingPrint_Success() throws Exception {
        PrintDTO printDTO = new PrintDTO();
        try {
            String result = printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        try {
            printDTO.setLpn("LPN123");
            String result = printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PRINTTYPE_OR_PRINTSCENE_IS_NULL, e.getMessage());
        }
        try {
            printDTO.setPrintType("TypeA");
            String result = printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PRINTTYPE_OR_PRINTSCENE_IS_NULL, e.getMessage());
        }


        // Setup test data
        printDTO.setLpn("LPN123");
        printDTO.setPrintType("TypeA");
        printDTO.setPrintScene("SceneB");
        printDTO.setEmpNo("EMP001");
        printDTO.setSmallBatches("是");

        PsTask psTask = new PsTask();
        psTask.setProdAddress("Prod Address");
        psTask.setStock("STOCK1");
        psTask.setLeadFlag("LEADFLAG1");
        psTask.setItemNo("ITEMNO1");
        psTask.setItemName("Item Name");
        psTask.setTaskNo("TASKNO1");
        psTask.setProdplanId("PRODPLANID1");
        psTask.setExternalType("EXTTYPE1");


        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setAttribute1("ATTR1");

        List<ContainerContentInfoDTO> containerContentInfoList = Arrays.asList(containerContentInfoDTO);

        PrintTemplateInfoDTO printTemplateInfoDTO = new PrintTemplateInfoDTO();
        printTemplateInfoDTO.setPrintTemplateId("TEMPLATEID1");
        printTemplateInfoDTO.setAttribute1("ATTR1,ATTR2,ATTR3");

        List<PrintTemplateInfoDTO> printTemplateInfoList = Arrays.asList(printTemplateInfoDTO);
        when(centerfactoryRemoteService.queryTemplateInfoByTypeAndScene(any())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_QUERY_THE_PRINT_TEMPLATE_INFORMATION, e.getMessage());
        }
        when(centerfactoryRemoteService.queryTemplateInfoByTypeAndScene(any())).thenReturn(printTemplateInfoList);

        when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(anyList())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.BOX_CONTENTS_ARE_EMPTY, e.getMessage());
        }

        when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(anyList())).thenReturn(containerContentInfoList);
        List<PsTask> psTaskList = Arrays.asList(psTask);
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, e.getMessage());
        }
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);

        BaItem baItem = new BaItem();
        baItem.setItemNo("ITEMNO1");
        when(datawbRemoteService.getTheRegion(anyString(), anyString())).thenReturn(baItem);

        when(DateUtil.convertDateToString(any(), anyString())).thenReturn("2022-01-01 00:00:00");

        List<SysLookupValuesDTO> sysLookupValuesList = new ArrayList<>();
        when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("DOCID1");
        when(cloudDiskHelper.getFileDownloadUrl(anyString(), any(), anyString())).thenReturn("FILEURL1");

        // Call the method under test
        try {
            String result = printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesList.add(new SysLookupValuesDTO());
        sysLookupValuesList.add(new SysLookupValuesDTO(){{setDescriptionChin("2");setLookupMeaning("2");}});
        when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);

        PowerMockito.whenNew(WordProcesser.class).withAnyArguments().thenReturn(wordProcesser);
        PowerMockito.whenNew(File.class).withAnyArguments().thenReturn(file);

        PowerMockito.when(PDDocument.load(file)).thenReturn(pdDocument);
        PowerMockito.whenNew(PDFRenderer.class).withAnyArguments().thenReturn(pdfRenderer);
        PowerMockito.when(pdDocument.getNumberOfPages()).thenReturn(2);
        //PowerMockito.when(pdfRenderer.renderImageWithDPI(anyInt(),any(),any())).thenReturn(bufferedImage);
        doNothing().when(wordProcesser).init(any());

        printDTO.setPrintScene(NumConstant.STR_TWO);
        printDTO.setSourceBatchCode("2");
        PowerMockito.when(datawbRemoteService.getItemInfo(anyString())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_THE_MATERIAL_CODE_FOR_THE_LOT, e.getMessage());
        }
        printDTO.setSourceBatchCode("");
        PowerMockito.when(datawbRemoteService.getItemInfo(anyString())).thenReturn(new BaItem());
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_THE_MATERIAL_CODE_FOR_THE_LOT, e.getMessage());
        }
        PowerMockito.when(datawbRemoteService.getItemInfo(anyString())).thenReturn(new BaItem(){{setItemNo("itemNo");}});

        psTask.setStock(Constant.POWER_MODULES_TO_RAW_MATERIALS);
        PowerMockito.when(datawbRemoteService.getStockInfo(anyString())).thenReturn(null);

        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.OBTAIN_BATCH_220_INFORMATION, e.getMessage());
        }

        psTask.setStock(Constant.POWER_MODULES_TO_RAW_MATERIALS);
        PowerMockito.when(datawbRemoteService.getStockInfo(anyString())).thenReturn(new BaItem());

        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.OBTAIN_BATCH_220_INFORMATION, e.getMessage());
        }

        psTask.setStock(Constant.POWER_MODULES_TO_RAW_MATERIALS+"()");
        PowerMockito.when(datawbRemoteService.getStockPowerModuleIncluded(anyString(),anyString())).thenReturn(null);

        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.OBTAIN_BATCH_220_INFORMATION, e.getMessage());
        }
        PowerMockito.when(datawbRemoteService.getStockPowerModuleIncluded(anyString(),anyString())).thenReturn(new BaItem());

        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(null);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.OBTAIN_BATCH_220_INFORMATION, e.getMessage());
        }
        psTask.setStock("");
        List<String> attrList =new ArrayList<>();
        attrList.add("");
        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(attrList);
        String result = printServiceImpl.veneerPackagingPrint(printDTO);

        PowerMockito.field(PrintServiceImpl.class,"iepmsConvertPdfEnable").set(printServiceImpl,true);
        printDTO.setPrintScene(NumConstant.STR_THREE);
        PowerMockito.when(datawbRemoteService.getTheRegion(any(),anyString())).thenReturn(null);
         result = printServiceImpl.veneerPackagingPrint(printDTO);

        printDTO.setPrintScene(NumConstant.STR_THREE);
        psTask.setProdAddress("1");
        PowerMockito.when(datawbRemoteService.getTheRegion(any(),anyString())).thenReturn(new BaItem());
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        psTask.setProdAddress("1");
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        psTask.setProdAddress("7");
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        psTask.setProdAddress("6");
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        printDTO.setPrintScene(NumConstant.STR_FOUR);
        PowerMockito.when(datawbRemoteService.getMpnByItembarcode(any())).thenReturn("");
        printDTO.setSourceBatchCode("2");
        result = printServiceImpl.veneerPackagingPrint(printDTO);


        printDTO.setSourceBatchCode("");
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        printDTO.setPrintScene(NumConstant.STR_FIVE);
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        printDTO.setZplPrint(true);
        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }

        printDTO.setZplPrint(false);
        printTemplateInfoDTO.setAttribute1("lpn$100$20$1,materialCode$100$20$1,mpn$100$20$1,qty$100$20$1,aggregate$100$100$2");
        printTemplateInfoList = Arrays.asList(printTemplateInfoDTO);
        when(centerfactoryRemoteService.queryTemplateInfoByTypeAndScene(any())).thenReturn(printTemplateInfoList);
        printDTO.setLpn("lpn");
        Map<String,Object> map = new HashMap<>();
        map.put("lpn","2");
        map.put("mpn","2");
        PowerMockito.when(BarCodeUtils.entityToMap(any())).thenReturn(map);
        result = printServiceImpl.veneerPackagingPrint(printDTO);

        printDTO.setPrintScene(NumConstant.STR_TWENTY_TWO);

        try {
            printServiceImpl.veneerPackagingPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }

    }
    /*Ended by AICoder, pid:ad6154319ehb24714c4a08dee1786831d4333b41*/
    @Test
    public void setQtyForDiskCode() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        PrintDTO printDTO = new PrintDTO();
        printDTO.setLpn("LPN123");
        printDTO.setPrintType("TypeA");
        printDTO.setPrintScene("SceneB");
        printDTO.setEmpNo("EMP001");
        printDTO.setSmallBatches("是");

        PsTask psTask = new PsTask();
        psTask.setProdAddress("Prod Address");
        psTask.setStock("STOCK1");
        psTask.setLeadFlag("LEADFLAG1");
        psTask.setItemNo("ITEMNO1");
        psTask.setItemName("Item Name");
        psTask.setTaskNo("TASKNO1");
        psTask.setProdplanId("PRODPLANID1");
        psTask.setExternalType("EXTTYPE1");
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setAttribute1("ATTR1");

        List<ContainerContentInfoDTO> containerContentInfoList = Arrays.asList(containerContentInfoDTO);

        PrintTemplateInfoDTO printTemplateInfoDTO = new PrintTemplateInfoDTO();
        printTemplateInfoDTO.setPrintTemplateId("TEMPLATEID1");
        printTemplateInfoDTO.setAttribute1("ATTR1,ATTR2,ATTR3");
        ReflectUtil.setFieldValue(printServiceImpl,"qtySetSwitch","N");
        Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        Assert.assertNotNull(psTask);
        ReflectUtil.setFieldValue(printServiceImpl,"qtySetSwitch","Y");
        Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        Assert.assertNotNull(psTask);
        psTask.setStock(Constant.POWER_MODULES_TO_RAW_MATERIALS);
        Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        Assert.assertNotNull(psTask);
        printDTO.setPackageType("1");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList((SysLookupValuesDTO) any())).thenReturn(sysLookupValuesDTOList);
        try {
            Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesDTOList.add(new SysLookupValuesDTO());
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList((SysLookupValuesDTO) any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("result");
        List<SkuReelIdOutputDTO> skuReelIdOutputDTOS = new ArrayList<>();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(skuReelIdOutputDTOS));
        try {
            Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DISK_CODE_CONTROL, e.getMessage());
        }
        skuReelIdOutputDTOS.add(new SkuReelIdOutputDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(skuReelIdOutputDTOS));
        try {
            Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        skuReelIdOutputDTOS.clear();
        skuReelIdOutputDTOS.add(new SkuReelIdOutputDTO(){{setReelidProcess("1");}});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(skuReelIdOutputDTOS));
        Whitebox.invokeMethod(printServiceImpl,"setQtyForDiskCode",printDTO,psTask,containerContentInfoList,new BaItem(),new PrintInfo());
        Assert.assertNotNull(printDTO);
    }

    @Test
    public void packagingBartenderPrint() throws Exception {
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PrintDTO printDTO = new PrintDTO();
        printDTO.setLpn("LPN123");
        printDTO.setPrintType("TypeA");
        printDTO.setLpn("LPN123");
        printDTO.setPrintType("TypeA");
        printDTO.setPrintScene("SceneB");
        printDTO.setEmpNo("EMP001");
        printDTO.setSmallBatches("是");
        PsTask psTask = new PsTask();
        psTask.setProdAddress("Prod Address");
        psTask.setStock("STOCK1");
        psTask.setLeadFlag("LEADFLAG1");
        psTask.setItemNo("ITEMNO1");
        psTask.setItemName("Item Name");
        psTask.setTaskNo("TASKNO1");
        psTask.setProdplanId("PRODPLANID1");
        psTask.setExternalType("EXTTYPE1");
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setAttribute1("ATTR1");
        List<ContainerContentInfoDTO> containerContentInfoList = Arrays.asList(containerContentInfoDTO);
        when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(anyList())).thenReturn(null);
        try {
            printServiceImpl.packagingBartenderPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.BOX_CONTENTS_ARE_EMPTY, e.getMessage());
        }

        when(ProductionDeliveryRemoteService.getContainerContentInfoBatch(anyList())).thenReturn(containerContentInfoList);
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(null);
        try {
            printServiceImpl.packagingBartenderPrint(printDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, e.getMessage());
        }

        List<PsTask> psTaskList = Arrays.asList(psTask);
        when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        BaItem baItem = new BaItem();
        baItem.setItemNo("ITEMNO1");
        when(datawbRemoteService.getTheRegion(anyString(), anyString())).thenReturn(baItem);
        when(DateUtil.convertDateToString(any(), anyString())).thenReturn("2022-01-01 00:00:00");
        List<SysLookupValuesDTO> sysLookupValuesList = new ArrayList<>();
        when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        sysLookupValuesList.add(new SysLookupValuesDTO());
        sysLookupValuesList.add(new SysLookupValuesDTO(){{setDescriptionChin("2");setLookupMeaning("2");}});
        when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        printDTO.setPrintScene(NumConstant.STR_TWO);
        printDTO.setSourceBatchCode("");
        PowerMockito.when(datawbRemoteService.getItemInfo(anyString())).thenReturn(new BaItem(){{setItemNo("itemNo");}});
        psTask.setStock(Constant.POWER_MODULES_TO_RAW_MATERIALS);
        PowerMockito.when(datawbRemoteService.getStockInfo(anyString())).thenReturn(new BaItem());
        psTask.setStock("");
        List<String> attrList =new ArrayList<>();
        attrList.add("");
        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(anyString(),any())).thenReturn(attrList);
        doNothing().when(barcodeCenterRemoteService).serverTemplatePrint(Mockito.any());
        printServiceImpl.packagingBartenderPrint(printDTO);
        Assert.assertTrue(true);
    }
}
