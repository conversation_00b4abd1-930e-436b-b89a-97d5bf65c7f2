package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.WorkorderOnlineService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CollectionUtils.class, MicroServiceRestUtil.class, JsonConvertUtil.class, ExcelUtil.class, Constant.class,
        ExcelName.class, BasicsettingRemoteService.class, RedisHelper.class, ObtainRemoteServiceDataUtil.class,
        SpringContextUtil.class, PlanscheduleRemoteService.class, CommonUtils.class, HttpRemoteService.class, Double.class, JacksonJsonConverUtil.class})
public class BSmtBomHeaderServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private BSmtBomHeaderServiceImpl bSmtBomHeaderServiceImpl;
    @Mock
    private BSmtBomHeaderRepository bSmtBomHeaderRepository;
    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;
    @Mock
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;
    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Mock
    private IscpRemoteService iscpRemoteService;
    @Mock
    WorkorderOnlineService workorderOnlineService;

    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private ExcelUtil excelUtil;
    @Mock
    private XSSFWorkbook wb;
    @Mock
    private Sheet sheet;
    @Mock
    private Cell cell;

    @Mock
    private Row row;
    @Mock
    private Cell cellB;

    @Mock
    private Row rowB;
    @Mock
    private HttpServletResponse httpServletResponse;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ExcelUtil.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(JsonConvertUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.spy(JsonConvertUtil.class);
    }

    @Test
    public void exportOriginalFile() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class);
        List<BSmtBomHeader> dataList = new ArrayList<BSmtBomHeader>();
        BSmtBomHeader dataListDetail = new BSmtBomHeader();
        dataListDetail.setFactoryId(new BigDecimal(52));
        dataListDetail.setCfgHeaderId("1616e9c2-b57b-4a4f-8bf0-a8652b96c6a4");
        dataListDetail.setCraftSection("SMT-B");
        dataListDetail.setCreateUser("00286523");
        dataListDetail.setVerNo("test123");
        BSmtBomHeader dataListDetail1 = new BSmtBomHeader();
        dataListDetail1.setFactoryId(new BigDecimal(55));
        dataListDetail1.setCfgHeaderId("1616e9c2-b57b-4a4f-8bf0-a8652b96c6a45");
        dataListDetail1.setCraftSection("SMT-A");
        dataListDetail1.setCreateUser("00286523");
        dataListDetail1.setVerNo("test123");
        dataList.add(dataListDetail1);
        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(dataList);
        PsWorkOrderSmt smtOrder = new PsWorkOrderSmt();
        smtOrder.setPcbQty(new BigDecimal("2"));
        when(PlanscheduleRemoteService.getPcbQty(Mockito.anyString())).thenReturn(smtOrder);
        List<BSmtBomDetail> details = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setMachineNo("test123");
        bSmtBomDetail.setItemCode("test123");
        bSmtBomDetail.setModuleNo("test123");
        bSmtBomDetail.setLocationNo("test123");
        bSmtBomDetail.setAmQty(new BigDecimal("1"));
        bSmtBomDetail.setBmQty(new BigDecimal("1"));
        bSmtBomDetail.setQty(new BigDecimal("1"));
        details.add(bSmtBomDetail);
        when(bSmtBomDetailRepository.getList(Mockito.anyMap())).thenReturn(details);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("test123");
        bsItemInfoList.add(bsItemInfo);

        ObjectMapper mapper = new ObjectMapper();
        String jsonNew = mapper.writeValueAsString("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"shrehe\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsItemInfoController@getInfoList\",\"code\":\"0000\",\"costTime\":\"1710ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Oct 29 21:31:09 CST 2019\",\"tag\":\"查询物料信息通用方法\",\"serviceName\":\"zte-mes-manufactureshare-basicsettingsysczjj\",\"userId\":null}}");
        JsonNode json = mapper.readTree(jsonNew);
        String bo = "[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"locationNo\":\"test123\",\"moduleNo\":\"test123\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}]";
        String getresult = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"shrehe\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsItemInfoController@getInfoList\",\"code\":\"0000\",\"costTime\":\"1710ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Oct 29 21:31:09 CST 2019\",\"tag\":\"查询物料信息通用方法\",\"serviceName\":\"zte-mes-manufactureshare-basicsettingsysczjj\",\"userId\":null}}";
        when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(getresult);

        Map<String, String> headerParamsMap = new HashMap<>();
        Map<String, Object> mapConquery = new HashMap<String, Object>();
        mapConquery.put("factoryId", 55);
        mapConquery.put("attr1", "test123");
        mapConquery.put("lineCode", "test123");
        mapConquery.put("pbNum", 2);
        mapConquery.put("verNo", "2");
        headerParamsMap.put("X-Factory-Id", "52");
        List<List<BSmtBomDetail>> exportList = new ArrayList<>();
        exportList.add(details);
        bSmtBomHeaderServiceImpl.exportOriginalFile(httpServletResponse, "", "", "name");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getList() throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<BSmtBomHeader> dataList = new ArrayList<BSmtBomHeader>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setLineCode("test123");
        dataList.add(bSmtBomHeader);
        List<CFLine> cfLineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineList.add(cfLine);

        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(dataList);
        when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLineList);
        Assert.assertEquals(dataList, bSmtBomHeaderServiceImpl.getList(map, "", ""));
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(Collections.singletonList(new BProdBomHeaderDTO()));
        bSmtBomHeaderServiceImpl.getList(map, "", "");
    }

    @Test
    public void getPage() throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<BSmtBomHeader> dataList = new ArrayList<BSmtBomHeader>();
        List<BSmtBomHeader> list = new ArrayList<BSmtBomHeader>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setLineCode("test123");
        dataList.add(bSmtBomHeader);
        List<CFLine> cfLineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineList.add(cfLine);

        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(dataList);
        when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLineList);
        Assert.assertEquals(list, bSmtBomHeaderServiceImpl.getPage(map, "", "", new Long("1"), new Long("1")));
    }

    @Test
    public void deleteFeedingHistory() {
        when(smtMachineMTLHistoryLRepository.getLineCount(Mockito.any())).thenReturn(0L);
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        bSmtBomHeaderServiceImpl.deleteFeedingHistory(record);

        when(smtMachineMTLHistoryLRepository.getLineCount(Mockito.any())).thenReturn(1L);
        bSmtBomHeaderServiceImpl.deleteFeedingHistory(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void generateHeadInfo() {
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        BSmtBomHeader headInfo = new BSmtBomHeader();
        List<PsWorkOrderSmt> headIdList = new ArrayList<>();
        bSmtBomHeaderServiceImpl.generateHeadInfo(record, workOrder, headInfo, headIdList);
        Assert.assertEquals("Y", headInfo.getStatus());

        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        headIdList.add(psWorkOrderSmt);
        bSmtBomHeaderServiceImpl.generateHeadInfo(record, workOrder, headInfo, headIdList);
        Assert.assertEquals("Y", headInfo.getStatus());

        psWorkOrderSmt.setCfgHeaderId("123");
        List<BSmtBomDetail> list = new ArrayList<>();
        when(bSmtBomDetailRepository.selectBSmtBomDetailListById(Mockito.any())).thenReturn(list);
        bSmtBomHeaderServiceImpl.generateHeadInfo(record, workOrder, headInfo, headIdList);
        Assert.assertEquals("Y", headInfo.getStatus());

        list.add(new BSmtBomDetail());
        bSmtBomHeaderServiceImpl.generateHeadInfo(record, workOrder, headInfo, headIdList);
        Assert.assertEquals("123", headInfo.getCfgHeaderId());
    }

    @Test
    public void testBomImportOpenApi() throws Exception {
        BomUploadTempDTO dto = new BomUploadTempDTO();
        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("eightIdResult", Constant.FAIL);
        resultMap.put("eightIdReason", Constant.EMP_NO_NULL);
        Assert.assertEquals(resultMap, bSmtBomHeaderServiceImpl.bomImportOpenApi(dto, bSmtBomIDTO));
        dto.setUploader("12345678");
        resultMap.put("eightIdReason", Constant.EMP_NO_NOT_EXIST);
        Assert.assertEquals(resultMap, bSmtBomHeaderServiceImpl.bomImportOpenApi(dto, bSmtBomIDTO));
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        hrmPersonInfoDTOMap.put("12345678", new HrmPersonInfoDTO());
        when(centerfactoryRemoteService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        MultipartFile multipartFile2 = new MockMultipartFile("test", "tes", "aaaa", "".getBytes());
        dto.setFile(multipartFile2);
        bSmtBomHeaderServiceImpl.bomImportOpenApi(dto, bSmtBomIDTO);
        when(bSmtBomHeaderRepository.getList(any())).thenReturn(new ArrayList<BSmtBomHeader>() {{
            add(new BSmtBomHeader());
        }});
        bSmtBomHeaderServiceImpl.bomImportOpenApi(dto, bSmtBomIDTO);
    }

    @Test
    public void testBomQueryOpenApi() throws Exception {
        Assert.assertNull(bSmtBomHeaderServiceImpl.bomQueryOpenApi(""));
        try {
            bSmtBomHeaderServiceImpl.bomQueryOpenApi("7654321-SMT-A5501");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORDER_ORDER_NOT_FOUND, e.getMessage());
        }
        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setLineCode("123");
        when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(psEntityPlanBasic);
        when(bSmtBomDetailService.getbsmtBomDetailInfoByWorkOrderNo(any())).thenReturn(new BSmtBomHeader());
        when(BasicsettingRemoteService.getLine(Mockito.anyString())).thenReturn(null);
        bSmtBomHeaderServiceImpl.bomQueryOpenApi("7654321-SMT-A5501");
        CFLine cfLine = new CFLine();
        cfLine.setCreateBy("1111");
        when(BasicsettingRemoteService.getLine(Mockito.anyString())).thenReturn(cfLine);
        bSmtBomHeaderServiceImpl.bomQueryOpenApi("7654321-SMT-A5501");
    }

    @Test
    public void testSetProgramNameForExport() throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<BSmtBomHeader> dataList = new ArrayList<>();
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setProgramNameForExport", map, dataList);
        BSmtBomHeader bomHeader1 = new BSmtBomHeader();
        BSmtBomHeader bomHeader2 = new BSmtBomHeader();
        bomHeader1.setCraftSection("SMT-A");
        bomHeader2.setCraftSection("SMT-B");
        bomHeader1.setProgramName("A");
        bomHeader2.setProgramName("B");
        dataList.add(bomHeader1);
        dataList.add(bomHeader2);
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setProgramNameForExport", map, dataList);
        Assert.assertEquals("A", map.get("programNameA"));
    }

    @Test
    public void testCheckProgramName() throws Exception {
        when(sheet.getRow(1)).thenReturn(row);
        when(sheet.getRow(2)).thenReturn(rowB);
        when(row.getCell(Mockito.anyInt())).thenReturn(cell);
        when(rowB.getCell(Mockito.anyInt())).thenReturn(cellB);
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "checkProgramName", sheet, new BSmtBomIDTO());
        when(cellB.toString()).thenReturn("B面程序");
        when(cell.toString()).thenReturn("A面程序");
        int num = Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "checkProgramName", sheet, new BSmtBomIDTO());
        Assert.assertEquals(3, num);
        when(cellB.toString()).thenReturn("");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "checkProgramName", sheet, new BSmtBomIDTO());
        when(cellB.toString()).thenReturn("B面程序");
        when(cell.toString()).thenReturn("");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "checkProgramName", sheet, new BSmtBomIDTO());
    }

    @Test
    public void testGenerateHeaderDTO() throws Exception {
        BSmtBomIDTO bomDTO = new BSmtBomIDTO();
        bomDTO.setCraftSection("SMT-A");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "generateHeaderDTO", bomDTO);
        bomDTO.setCraftSection("SMT-B");
        bomDTO.setProgramNameOfB("B");
        BSmtBomHeaderDTO headerDTO = Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "generateHeaderDTO", bomDTO);
        Assert.assertEquals("B", headerDTO.getProgramName());
        bomDTO.setCraftSection("");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "generateHeaderDTO", bomDTO);
        bomDTO.setPcbQty("123");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "generateHeaderDTO", bomDTO);
    }

    @Test
    public void getBomWithAbcType() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);

        try {
            when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(new ArrayList<>());
            bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BOM_DETAIL, e.getMessage());
        }
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);

        List<SmtLocationInfoDTO> locationInfo = new ArrayList<>();
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);

        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("045020100092");
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        try {
            when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(new ArrayList<>());
            bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BASIC_MATERIAL_INFO_IS_NOT_MAINTAINED, e.getMessage());
        }
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
        List<BsItemInfo> itemList2 = new ArrayList<>();
        BsItemInfo bsItemInfo2 = new BsItemInfo();
        bsItemInfo2.setItemNo("045020100092");
        bsItemInfo2.setAbcType("A");
        itemList2.add(bsItemInfo2);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList2);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeTwo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);

        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("04502010002");
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
        when(iscpRemoteService.getCivControlInfo((Mockito.anyList()))).thenReturn(new ArrayList<>());
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeThree() throws Exception {
        // 重新触发
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("04502010002");
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        List<CivControlInfoDTO> civControlList = new ArrayList<>();
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("045020100092");
        civControlList.add(civControlInfoDTO);
        when(iscpRemoteService.getCivControlInfo((Mockito.anyList()))).thenReturn(civControlList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeFour() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("04502010002");
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        List<CivControlInfoDTO> civControlList = new ArrayList<>();
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("045020100093");
        civControlList.add(civControlInfoDTO);
        when(iscpRemoteService.getCivControlInfo((Mockito.anyList()))).thenReturn(civControlList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeFive() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeSix() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        bSmtBomInfoDetailDTO.setLocationNo("test");
        bSmtBomInfoDetailDTO.setMachineNo("test");
        bSmtBomInfoDetailDTO.setModuleNo("test");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);

        List<SmtLocationInfoDTO> locationInfo = new ArrayList<>();
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLocationNo("test");
        smtLocationInfoDTO.setMachineNo("test");
        smtLocationInfoDTO.setModuleNo("test");
        locationInfo.add(smtLocationInfoDTO);
        //触发
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void getBomWithAbcTypeSeven() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);

        List<SmtLocationInfoDTO> locationInfo = new ArrayList<>();
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo);
        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);

        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setAbcType("C");
        itemList.add(bsItemInfo);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A"));
    }

    @Test
    public void excludeBomCode() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        bSmtBomInfoDetailDTO.setLocationNo("test");
        bSmtBomInfoDetailDTO.setMachineNo("test");
        bSmtBomInfoDetailDTO.setModuleNo("test");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);

        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(new ArrayList<>());

        List<BsBomHierarchicalDetail> hierarchicalDetail = new ArrayList<>();
        BsBomHierarchicalDetail bsBomHierarchicalDetail = new BsBomHierarchicalDetail();
        bsBomHierarchicalDetail.setItemNo("045020100092");
        hierarchicalDetail.add(bsBomHierarchicalDetail);
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(hierarchicalDetail);
        try {
            when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
            bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<BsBomHierarchicalDetail> hierarchicalDetail1 = new ArrayList<>();
        bsBomHierarchicalDetail.setTypeCode("test");
        hierarchicalDetail1.add(bsBomHierarchicalDetail);
        try {
            Page<BsBomHierarchicalDetail> result1 = new Page<>();
            result1.setRows(hierarchicalDetail1);
            when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result1);
            bSmtBomHeaderServiceImpl.getBomWithAbcType("SMT-HY006", "7019127", "SMT-A");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

    }

    @Test
    public void excludeBomCodeTwo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100092");
        bSmtBomInfoDetailDTO.setAbcType("A");
        bSmtBomInfoDetailDTO.setLocationNo("test");
        bSmtBomInfoDetailDTO.setMachineNo("test1");
        bSmtBomInfoDetailDTO.setModuleNo("test");
        detailList.add(bSmtBomInfoDetailDTO);

        List<SmtLocationInfoDTO> locationInfo = new ArrayList<>();
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLocationNo("test");
        smtLocationInfoDTO.setMachineNo("test");
        smtLocationInfoDTO.setModuleNo("test");
        locationInfo.add(smtLocationInfoDTO);
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo);
        Page<BsBomHierarchicalDetail> result = new Page<>();
        result.setRows(new ArrayList<>());
        when(PlanscheduleRemoteService.queryPreProcessInfo(Mockito.any(), Mockito.any())).thenReturn(result);
        Assert.assertNotNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "excludeBomCode", detailList, "SMT-HY006", "7019127"));

        List<SmtLocationInfoDTO> locationInfo1 = new ArrayList<>();
        SmtLocationInfoDTO smtLocationInfoDTO1 = new SmtLocationInfoDTO();
        smtLocationInfoDTO1.setLocationNo("test1");
        smtLocationInfoDTO1.setMachineNo("test1");
        smtLocationInfoDTO1.setModuleNo("test");
        locationInfo1.add(smtLocationInfoDTO1);
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo1);
        Assert.assertNotNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "excludeBomCode", detailList, "SMT-HY006", "7019127"));

        List<SmtLocationInfoDTO> locationInfo2 = new ArrayList<>();
        SmtLocationInfoDTO smtLocationInfoDTO2 = new SmtLocationInfoDTO();
        smtLocationInfoDTO2.setLocationNo("test");
        smtLocationInfoDTO2.setMachineNo("test1");
        smtLocationInfoDTO2.setModuleNo("test1");
        locationInfo1.add(smtLocationInfoDTO2);
        when(BasicsettingRemoteService.getLocationInfoByLineCode(Mockito.any())).thenReturn(locationInfo2);
        Assert.assertNotNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "excludeBomCode", detailList, "SMT-HY006", "7019127"));
    }

    @Test
    public void setABCType() throws Exception {
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("04502010002");
        detailList.add(bSmtBomInfoDetailDTO);
        Map<String, BsItemInfo> itemInfoMap = new HashMap<>();
        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("04502010002");
        bsItemInfo.setAbcType("");
        itemInfoMap.put("04502010002", bsItemInfo);
        Assert.assertNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setABCType", detailList, itemInfoMap));
    }

    @Test
    public void testSetProgramNameIsChange() throws Exception {
        BSmtBomHeaderDTO headerDTO = new BSmtBomHeaderDTO();
        BSmtBomHeader header = new BSmtBomHeader();
        List<BSmtBomHeader> list = new ArrayList<>();
        list.add(header);
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setProgramNameIsChange", list, headerDTO);
        headerDTO.setProgramName("A");
        header.setProgramName("A");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setProgramNameIsChange", list, headerDTO);
        Assert.assertEquals("N", headerDTO.getNameIsChange());
        header.setProgramName("B");
        Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "setProgramNameIsChange", list, headerDTO);
    }

    @Test
    public void getBomDetailWithAbcType() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        BSmtBomHeaderDTO dto = new BSmtBomHeaderDTO();
        dto.setLineCode("SMT-HY006");
        dto.setAttr1("7019127");
        dto.setCraftSection("SMT-A");
        try {
            when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(new ArrayList<>());
            bSmtBomHeaderServiceImpl.getBomDetailWithAbcType(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BOM_DETAIL, e.getMessage());
        }
        List<BSmtBomInfoDetailDTO> detailList = new ArrayList<>();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.setItemCode("045020100091");
        detailList.add(bSmtBomInfoDetailDTO);
        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);
        try {
            when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(new ArrayList<>());
            bSmtBomHeaderServiceImpl.getBomDetailWithAbcType(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BASIC_MATERIAL_INFO_IS_NOT_MAINTAINED, e.getMessage());
        }

        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("045020100092");
        bsItemInfo.setAbcType("C");
        BsItemInfo bsItemInfo2 = new BsItemInfo();
        bsItemInfo2.setItemNo("045020100093");
        bsItemInfo2.setAbcType("A");
        BsItemInfo bsItemInfo3 = new BsItemInfo();
        bsItemInfo3.setItemNo("045020100094");
        bsItemInfo3.setAbcType("B");
        itemList.add(bsItemInfo);
        itemList.add(bsItemInfo2);
        itemList.add(bsItemInfo3);
        itemList.add(null);
        when(BasicsettingRemoteService.getStyleInfoPost(Mockito.anyString())).thenReturn(itemList);
        Assert.assertEquals(0, bSmtBomHeaderServiceImpl.getBomDetailWithAbcType(dto).size());

        detailList.clear();
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO1 = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO1.setItemCode("045020100092");
        bSmtBomInfoDetailDTO1.setAbcType("C");
        BSmtBomInfoDetailDTO bSmtBomInfoDetailDTO2 = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO2.setItemCode("045020100093");
        bSmtBomInfoDetailDTO2.setAbcType("A");
        detailList.add(bSmtBomInfoDetailDTO1);
        detailList.add(bSmtBomInfoDetailDTO2);

        when(bSmtBomDetailRepository.getListByCond(Mockito.anyMap())).thenReturn(detailList);
        when(iscpRemoteService.getCivControlInfo(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomDetailWithAbcType(dto));

        List<CivControlInfoDTO> civControlList = new ArrayList<>();
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("045020100093");
        civControlList.add(civControlInfoDTO);
        when(iscpRemoteService.getCivControlInfo(Mockito.any())).thenReturn(civControlList);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getBomDetailWithAbcType(dto));
    }

    /* Started by AICoder, pid:3007fl022d0c55614d0f0bec91e5c68b57678689 */
    @Test
    public void getExportBomTest() {
        // 准备测试数据
        Map<String, Integer> cellNameMap = new HashMap<>();
        cellNameMap.put(Constant.LOCATION_COLUMN, 0);
        cellNameMap.put(Constant.ITEMCODE_COLUMN, 1);
        cellNameMap.put("物料型号", 2);
        cellNameMap.put("表面标识", 3);
        cellNameMap.put(MpConstant.STEP_DISTANCE, 4);
        cellNameMap.put(MpConstant.MATERIAL_RACK, 5);
        cellNameMap.put(MpConstant.PATCH_PROGRAM_DIRECTION, 6);
        cellNameMap.put(MpConstant.PATCH_SUCTION_NOZZLE, 7);
        cellNameMap.put(MpConstant.PATCH_PRESSURE, 8);
        cellNameMap.put(MpConstant.CHIP_PROGRAMMING_ID, 8);
        cellNameMap.put(MpConstant.DIRECTION, 9);
        cellNameMap.put(Constant.AM_COLUMN, 10);
        cellNameMap.put(Constant.BM_COLUMN, 11);

        Map<String, SmtLocationInfo> locationInfoMap = new HashMap<>();
        SmtLocationInfo smtLocationInfo = new SmtLocationInfo();
        smtLocationInfo.setMachineId("123");
        smtLocationInfo.setSupplierLoc("456");
        smtLocationInfo.setToSupplierLoc("789");
        smtLocationInfo.setToSupplierMachine("012");
        locationInfoMap.put("(AIMEX4S-2)2-31-1", smtLocationInfo);
        locationInfoMap.put("", new SmtLocationInfo());

        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTOA = new ArrayList<>();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTOB = new ArrayList<>();
        bSmtBomIDTO.setBSmtBomInfoDetailDTOOfA(bSmtBomInfoDetailDTOA);
        bSmtBomIDTO.setBSmtBomInfoDetailDTOOfB(bSmtBomInfoDetailDTOB);
        bSmtBomIDTO.setTransferIndex(0);

        // 模拟 sheet 的行为
        when(sheet.getLastRowNum()).thenReturn(10);

        Row row1 = PowerMockito.mock(Row.class);
        when(row1.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(0).toString()).thenReturn(Constant.LOCATION_COLUMN);
        when(row1.getCell(1)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(1).toString()).thenReturn("c046030200050");
        when(row1.getCell(2)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(2).toString()).thenReturn("物料型号");
        when(row1.getCell(3)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(3).toString()).thenReturn("步距");
        when(row1.getCell(4)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(4).toString()).thenReturn("料架");
        when(row1.getCell(5)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(5).toString()).thenReturn("贴片程序方向");
        when(row1.getCell(6)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(6).toString()).thenReturn("贴片吸嘴");
        when(row1.getCell(7)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(7).toString()).thenReturn("贴片压力");
        when(row1.getCell(8)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(8).toString()).thenReturn("方向");
        when(row1.getCell(9)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(9).toString()).thenReturn("5.2");
        when(row1.getCell(10)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(10).toString()).thenReturn("15");
        when(row1.getCell(11)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(11).toString()).thenReturn("10");

        Row row2 = PowerMockito.mock(Row.class);
        when(row2.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(0).toString()).thenReturn("(AIMEX4S-2) 2-31-1");
        when(row2.getCell(1)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(1).toString()).thenReturn("c046040200091");
        when(row2.getCell(2)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(2).toString()).thenReturn("");
        when(row2.getCell(3)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(3).toString()).thenReturn("");
        when(row2.getCell(4)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(4).toString()).thenReturn("12");
        when(row2.getCell(5)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(5).toString()).thenReturn("16mm XN");
        when(row2.getCell(6)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(6).toString()).thenReturn("0");
        when(row2.getCell(7)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(7).toString()).thenReturn("0");
        when(row2.getCell(8)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(8).toString()).thenReturn("0");
        when(row2.getCell(9)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(9).toString()).thenReturn("0.3");
        when(row2.getCell(10)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(10).toString()).thenReturn("4");
        when(row2.getCell(11)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(11).toString()).thenReturn("600");


        Row row3 = PowerMockito.mock(Row.class);
        when(row3.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(0).toString()).thenReturn(")( MEX-/");
        when(row3.getCell(1)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(1).toString()).thenReturn("C046050200038");
        when(row3.getCell(2)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(2).toString()).thenReturn("Step");
        when(row3.getCell(3)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(3).toString()).thenReturn("Rack");
        when(row3.getCell(4)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(4).toString()).thenReturn("Direction");
        when(row3.getCell(5)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(5).toString()).thenReturn("Nozzle");
        when(row3.getCell(6)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(6).toString()).thenReturn("Pressure");
        when(row3.getCell(7)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(7).toString()).thenReturn("Dir");
        when(row3.getCell(8)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(8).toString()).thenReturn("2");
        when(row3.getCell(9)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(9).toString()).thenReturn(null);
        when(row3.getCell(10)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(10).toString()).thenReturn("-20");
        when(row3.getCell(11)).thenReturn(PowerMockito.mock(Cell.class));
        when(row3.getCell(11).toString()).thenReturn("-10");

        Row row5 = PowerMockito.mock(Row.class);
        when(row5.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        // 配置模拟的Cell对象，使其在调用getCellType()时返回CellType.BLANK
        when(row5.getCell(0).getCellType()).thenReturn(CellType.BLANK);
        Row row6 = PowerMockito.mock(Row.class);
        when(row6.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        // 配置模拟的Cell对象，使其在调用getCellType()时返回CellType.BLANK
        when(row6.getCell(0)).thenReturn(null);

        when(sheet.getRow(1)).thenReturn(row1);
        when(sheet.getRow(2)).thenReturn(row2);
        when(sheet.getRow(3)).thenReturn(row3);
        when(sheet.getRow(5)).thenReturn(row5);
        when(sheet.getRow(6)).thenReturn(row6);

        // 执行测试方法
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap, locationInfoMap, true, bSmtBomIDTO);

        // 验证结果
        Assert.assertFalse(bSmtBomInfoDetailDTOA.isEmpty());
        Assert.assertFalse(bSmtBomInfoDetailDTOB.isEmpty());

        // 验证其他条件和分支的覆盖情况
        Mockito.verify(row1, Mockito.times(4)).getCell(0);
        Mockito.verify(row1, Mockito.times(1)).getCell(1);
        Mockito.verify(row1, Mockito.times(1)).getCell(2);
        Mockito.verify(row1, Mockito.times(1)).getCell(3);
        Mockito.verify(row1, Mockito.times(1)).getCell(4);
        Mockito.verify(row1, Mockito.times(1)).getCell(5);
        Mockito.verify(row1, Mockito.times(1)).getCell(6);
        Mockito.verify(row1, Mockito.times(1)).getCell(7);
        Mockito.verify(row1, Mockito.times(1)).getCell(8);
        Mockito.verify(row1, Mockito.times(1)).getCell(9);

        // 准备测试数据
        Map<String, Integer> cellNameMap1 = new HashMap<>();
        cellNameMap1.put(Constant.LOCATION_COLUMN, 0);
        cellNameMap1.put(Constant.ITEMCODE_COLUMN, 1);
        cellNameMap1.put("物料型号", 2);
        cellNameMap1.put("表面标识", 3);
        cellNameMap1.put(MpConstant.STEP_DISTANCE, 4);
        cellNameMap1.put(MpConstant.MATERIAL_RACK, 5);
        cellNameMap1.put(MpConstant.DIRECTION, 6);
        cellNameMap1.put(Constant.AM_COLUMN, 7);
        cellNameMap1.put(Constant.BM_COLUMN, 8);

        Row row4 = PowerMockito.mock(Row.class);
        when(row4.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(0).toString()).thenReturn("(X4S-2) 2-33-1");
        when(row4.getCell(1)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(1).toString()).thenReturn("046050200038");
        when(row4.getCell(2)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(2).toString()).thenReturn(null);
        when(row4.getCell(3)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(3).toString()).thenReturn(null);
        when(row4.getCell(4)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(4).toString()).thenReturn("12");
        when(row4.getCell(5)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(5).toString()).thenReturn("16mm XN");
        when(row4.getCell(6)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(6).toString()).thenReturn("0");
        when(row4.getCell(7)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(7).toString()).thenReturn("0");
        when(row4.getCell(8)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(8).toString()).thenReturn("4");
        when(row4.getCell(9)).thenReturn(PowerMockito.mock(Cell.class));
        when(row4.getCell(9).toString()).thenReturn("600");
        when(sheet.getRow(1)).thenReturn(row4);
        when(sheet.getRow(2)).thenReturn(null);
        when(sheet.getRow(3)).thenReturn(null);
        // 执行测试方法
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap1, locationInfoMap, false, bSmtBomIDTO);

        // 验证结果
        Assert.assertFalse(bSmtBomInfoDetailDTOA.isEmpty());
        Assert.assertFalse(bSmtBomInfoDetailDTOB.isEmpty());
        when(row4.getCell(0).toString()).thenReturn("X4S-2 2-33-1");
        // 执行测试方法
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap1, locationInfoMap, false, bSmtBomIDTO);
        when(row4.getCell(0).toString()).thenReturn("(X4S-2 2-33-1");
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap1, locationInfoMap, false, bSmtBomIDTO);
        when(row4.getCell(0).toString()).thenReturn("X4S-2) 2-33-1");
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap1, locationInfoMap, false, bSmtBomIDTO);
    }
    /* Ended by AICoder, pid:3007fl022d0c55614d0f0bec91e5c68b57678689 */

    /* Started by AICoder, pid:rc53593ff0ja611145610814c05c6282a822928f */
    @Test
    public void generateBomDetailDTOListTest() {
        // 准备测试数据
        BSmtBomIDTO bomDTO = new BSmtBomIDTO();
        List<BSmtBomInfoDetailDTO> bomInfoDetailDTOList = new ArrayList<>();

        BSmtBomInfoDetailDTO detailDTO1 = new BSmtBomInfoDetailDTO();
        detailDTO1.setCfgIndex(BigDecimal.valueOf(1));
        detailDTO1.setCreateUser("user1");
        detailDTO1.setEntityId(BigDecimal.valueOf(1L));
        detailDTO1.setFactoryId(BigDecimal.valueOf(2L));
        detailDTO1.setFeederNo("feeder1");
        detailDTO1.setFeederPitch(BigDecimal.valueOf(1.0));
        detailDTO1.setFeederSize("size1");
        detailDTO1.setItemCode("item1");
        detailDTO1.setItemName("name1");
        detailDTO1.setLastUpdatedBy("updatedBy1");
        detailDTO1.setLocationCode("locationCode1");
        detailDTO1.setLocationNo("locationNo1");
        detailDTO1.setMachineNo("machineNo1");
        detailDTO1.setModuleNo("moduleNo1");
        detailDTO1.setModuleCode("moduleCode1");
        detailDTO1.setTrackNo("trackNo1");
        detailDTO1.setQty(BigDecimal.valueOf(10));
        detailDTO1.setMachineId("machineId1");
        detailDTO1.setSupplierLoc("supplierLoc1");
        detailDTO1.setToSupplierMachine("toSupplierMachine1");
        detailDTO1.setToSupplierLoc("toSupplierLoc1");
        detailDTO1.setStepDistance("stepDistance1");
        detailDTO1.setMaterialRack("materialRack1");
        detailDTO1.setPatchProgramDirection("patchProgramDirection1");
        detailDTO1.setPatchSuctionNozzle("patchSuctionNozzle1");
        detailDTO1.setPatchPressure("patchPressure1");
        detailDTO1.setVirtualFlag("virtualFlag1");
        detailDTO1.setDirection("direction1");

        BSmtBomInfoDetailDTO detailDTO2 = new BSmtBomInfoDetailDTO();
        detailDTO2.setCfgIndex(BigDecimal.valueOf(2));
        detailDTO2.setCreateUser("user2");
        detailDTO2.setEntityId(BigDecimal.valueOf(2L));
        detailDTO2.setFactoryId(BigDecimal.valueOf(3L));
        detailDTO2.setFeederNo("feeder2");
        detailDTO2.setFeederPitch(BigDecimal.valueOf(2.0));
        detailDTO2.setFeederSize("size2");
        detailDTO2.setItemCode("item2");
        detailDTO2.setItemName("name2");
        detailDTO2.setLastUpdatedBy("updatedBy2");
        detailDTO2.setLocationCode("locationCode2");
        detailDTO2.setLocationNo("locationNo2");
        detailDTO2.setMachineNo("machineNo2");
        detailDTO2.setModuleNo("moduleNo2");
        detailDTO2.setModuleCode("moduleCode2");
        detailDTO2.setTrackNo("trackNo2");
        detailDTO2.setQty(BigDecimal.valueOf(20));
        detailDTO2.setMachineId("machineId2");
        detailDTO2.setSupplierLoc("supplierLoc2");
        detailDTO2.setToSupplierMachine("toSupplierMachine2");
        detailDTO2.setToSupplierLoc("toSupplierLoc2");
        detailDTO2.setStepDistance("stepDistance2");
        detailDTO2.setMaterialRack("materialRack2");
        detailDTO2.setPatchProgramDirection("patchProgramDirection2");
        detailDTO2.setPatchSuctionNozzle("patchSuctionNozzle2");
        detailDTO2.setPatchPressure("patchPressure2");
        detailDTO2.setVirtualFlag("virtualFlag2");
        detailDTO2.setDirection("direction2");

        bomInfoDetailDTOList.add(detailDTO1);
        bomInfoDetailDTOList.add(detailDTO2);

        bomDTO.setBSmtBomInfoDetailDTO(bomInfoDetailDTOList);

        String cfgHeaderId = "headerId";

        // 执行测试方法
        List<BSmtBomDetailDTO> result = bSmtBomHeaderServiceImpl.generateBomDetailDTOList(bomDTO, cfgHeaderId);

        // 断言结果
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());

        BSmtBomDetailDTO detail1 = result.get(0);
        Assert.assertEquals(cfgHeaderId, detail1.getCfgHeaderId());
    }
    /* Ended by AICoder, pid:rc53593ff0ja611145610814c05c6282a822928f */


    /* Started by AICoder, pid:u8f34yf5d9u4f8e14f570b24a1f3777bf1d80398 */
    @Test
    public void deleteOrupdateOrInsertBsmtInfo() throws Exception {
        BSmtBomHeaderDTO dto = new BSmtBomHeaderDTO();
        dto.setWorkOrderNo("777888512-DIP444");
        PowerMockito.mockStatic(RedisHelper.class);
        when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto));
        when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        when(PlanscheduleRemoteService.getProductCodeByWorkNo(Mockito.any())).thenReturn(workOrder);
        List<PsWorkOrderSmt> headIdList = new ArrayList<>();

        when(PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(Mockito.any())).thenReturn(headIdList);
        bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        List<BSmtBomDetail> detailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setOperateType("delete");
        bSmtBomDetail.setItemCode("nihao");
        bSmtBomDetail.setQty(new BigDecimal(2));
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        bSmtBomDetail1.setOperateType("");
        detailList.add(bSmtBomDetail);
        detailList.add(bSmtBomDetail1);
        dto.setTransferList(detailList);
        Page<BBomDetail> bomPage = new Page<>();
        when(BasicsettingRemoteService.getBomListofDIP(Mockito.any())).thenReturn(bomPage);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        psWorkOrderSmt.setCreateBy("456");
        headIdList.add(psWorkOrderSmt);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        psWorkOrderSmt.setCfgHeaderId("456");
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        when(bSmtBomDetailRepository.selectBSmtBomDetailListById(Mockito.any())).thenReturn(null);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        when(bSmtBomDetailRepository.selectBSmtBomDetailListById(Mockito.any())).thenReturn(detailList);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        bSmtBomDetail.setOperateType("delet");
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_LIST_IS_NULL, e.getMessage());
        }
        List<BBomDetail> list = new ArrayList<>();
        BBomDetail bBomDetail = new BBomDetail();
        bBomDetail.setItemCode("nihao");
        bBomDetail.setUsageCount(new BigDecimal(2));
        list.add(bBomDetail);
        bomPage.setRows(list);
        BSmtBomHeader headInfo = new BSmtBomHeader();
        List<BSmtBomDetail> totalList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail2 = new BSmtBomDetail();
        bSmtBomDetail2.setItemCode("nihao");
        bSmtBomDetail2.setCfgHeaderId("456");
        totalList.add(bSmtBomDetail2);
        headInfo.setTransferList(totalList);
        when(bSmtBomDetailService.getBomHeaderAndDetail(Mockito.any(), Mockito.any())).thenReturn(headInfo);
        bSmtBomDetail1.setItemCode("nihao");
        bSmtBomDetail1.setStationName("station");
        bSmtBomDetail2.setMachineNo("station");
        bSmtBomDetail1.setQty(new BigDecimal(2));
        when(smtMachineMaterialMoutingRepository.deleteSmtMachineMaterialMoutingById(Mockito.any())).thenReturn(1);
        when(bSmtBomHeaderRepository.updateOrInsertBsmtHeadInfo(Mockito.any())).thenReturn(1);
        when(bSmtBomDetailService.getBomHeaderAndDetail(Mockito.any(), Mockito.any())).thenReturn(null);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        dto.setScanByStation("1");
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        dto.setScanByStation("0");
        dto.setPreManue("Y");
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        dto.setScanByStation("1");
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        BSmtBomDetail bSmtBomDetail3 = new BSmtBomDetail();
        bSmtBomDetail3.setOperateType("");
        List<BSmtBomDetail> detailList1 = new ArrayList<>();
        detailList1.add(bSmtBomDetail3);
        dto.setTransferList(detailList1);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        bSmtBomDetail3.setQty(new BigDecimal(3));
        bSmtBomDetail3.setItemCode("nihao");
        bSmtBomDetail3.setStationName("station");
        when(SpringContextUtil.getBean(Mockito.anyString())).thenReturn(lmb);
        bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        bSmtBomDetail3.setQty(new BigDecimal(2));
        BSmtBomDetail bSmtBomDetail4 = new BSmtBomDetail();
        bSmtBomDetail4.setOperateType("delete");
        bSmtBomDetail4.setItemCode("nihao");
        bSmtBomDetail4.setStationName("station");
        bSmtBomDetail4.setQty(new BigDecimal(2));
        detailList1.add(bSmtBomDetail4);
        detailList1.remove(bSmtBomDetail3);
        when(bSmtBomDetailRepository.selectBSmtBomDetailListById(Mockito.any())).thenReturn(null);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        when(bSmtBomDetailService.getBomHeaderAndDetail(Mockito.any(), Mockito.any())).thenReturn(headInfo);
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(json);
        try {
            bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:u8f34yf5d9u4f8e14f570b24a1f3777bf1d80398 */

    /* Started by AICoder, pid:h6ef395711g6dca14c8d0826f1d33c4ef8c6bc6e */
    @Test
    public void asmImportBomTest() throws Exception {
        List<BSmtBomASMDTO> uploadList = new ArrayList<>();
        String factoryId = "52";
        String empNo = "15207203785";
        String entityId = "2";
        BSmtBomASMDTO bSmtBomASMDTO = new BSmtBomASMDTO();
        bSmtBomASMDTO.setSmtLocationNo("123");
        bSmtBomASMDTO.setProgramName("456");
        uploadList.add(bSmtBomASMDTO);
        when(ObtainRemoteServiceDataUtil.getSmtLocationInfoList(Mockito.any())).thenReturn(new ArrayList<>());
        String[] params = new String[]{"123"};
        when(SpringContextUtil.getBean(Mockito.anyString())).thenReturn(lmb);
        RetCode result = bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        // 断言结果
        Assert.assertEquals(MessageId.LOCATION_INFO_LACKING, result.getMsgId());
        List<SmtLocationInfo> locationInfoList = new ArrayList<>();
        SmtLocationInfo smtLocationInfo = new SmtLocationInfo();
        locationInfoList.add(smtLocationInfo);
        when(ObtainRemoteServiceDataUtil.getSmtLocationInfoList(Mockito.any())).thenReturn(locationInfoList);
        RetCode result1 = bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        // 断言结果
        Assert.assertEquals(MessageId.LOCATION_INFO_LACKING, result1.getMsgId());
        smtLocationInfo.setLineCode("789");
        when(BasicsettingRemoteService.getLine(Mockito.anyString())).thenReturn(null);
        when(ObtainRemoteServiceDataUtil.getSmtLocationInfoList(Mockito.any())).thenReturn(locationInfoList);
        RetCode result2 = bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        // 断言结果
        Assert.assertEquals(MessageId.LINE_INFO_LACKING, result2.getMsgId());
        CFLine cfLine = new CFLine();
        when(BasicsettingRemoteService.getLine(Mockito.anyString())).thenReturn(cfLine);
        RetCode result3 = bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        // 断言结果
        Assert.assertEquals(MessageId.LINE_NOT_SUPPORT_DEVICE_OR_NOT_ASM, result3.getMsgId());
        cfLine.setSmtDeviceSupport("Y");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        cfLine.setSmtdevicetype(BusinessConstant.DEVICE_TYPE_ASM);
        when(workorderOnlineService.getWorkorderOnline(Mockito.any())).thenReturn(null);
        RetCode result4 = bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        // 断言结果
        Assert.assertEquals(MessageId.NO_ONLINE_WORK_ORDER, result4.getMsgId());
        List<WorkorderOnline> onLineWorkOrderList = new ArrayList<>();
        WorkorderOnline workorderOnline = new WorkorderOnline();
        workorderOnline.setWorkOrder("7006866");
        onLineWorkOrderList.add(workorderOnline);
        when(workorderOnlineService.getWorkorderOnline(Mockito.any())).thenReturn(onLineWorkOrderList);
        bSmtBomASMDTO.setProgramName("part1_Apart2(part3-Tpart4——part5）part6");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_Bpart2(part3-Bpart4——part5）part6");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_Apart2(part3)");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_part2(part3-Tpart4）");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_part2(part3）");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_part2(part3-Bpart4)");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        bSmtBomASMDTO.setProgramName("part1_part2(）");
        workorderOnline.setItemNo("");
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        workorderOnline.setcraftsection("");
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemName("nihao");
        bsItemInfo.setItemEnName("wohao");
        bsItemInfo.setVersion("2");

        ServiceData serviceData = new ServiceData();
        serviceData.setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
            setWorkOrderNo("1234567");
        }}));

        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

        when(json.get(Mockito.anyString())).thenReturn(json);
        when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(null);
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(bsItemInfo);
        serviceData.setBo("1234567");
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        when(json.toString()).thenReturn("0");
        try {
            bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        } catch (Exception e) {
            Assert.assertEquals(CommonUtils.getLmbMessage(MessageId.SMT_WORKORDER_UPDATE_FAIL), e.getMessage());
        }
        when(json.toString()).thenReturn("2");
        List<BSmtBomHeader> list = new ArrayList<>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setProgramName("part1_part2(）");
        list.add(bSmtBomHeader);
        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(list);
        try {
            bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        } catch (Exception e) {
            Assert.assertEquals((locationInfoList.get(NumConstant.NUM_ZERO).getLineCode() + "-" + onLineWorkOrderList.get(NumConstant.NUM_ZERO).getWorkOrder().substring(0, 7) + ":" + CommonUtils.getLmbMessage(MessageId.HEADER_TABLE_INSERT_FAIL)), e.getMessage());
        }
        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(list);
        bSmtBomHeader.setProgramName(null);
        try {
            bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        } catch (Exception e) {
            Assert.assertEquals((locationInfoList.get(NumConstant.NUM_ZERO).getLineCode() + "-" + onLineWorkOrderList.get(NumConstant.NUM_ZERO).getWorkOrder().substring(0, 7) + ":" + CommonUtils.getLmbMessage(MessageId.HEADER_TABLE_INSERT_FAIL)), e.getMessage());
        }
        when(bSmtBomHeaderRepository.getList(Mockito.anyMap())).thenReturn(null);
        when(bSmtBomHeaderRepository.insertBSmtBomHeaderSelective(any())).thenReturn(1);
        smtLocationInfo.setSupplierLoc("123");
        when(bSmtBomDetailRepository.insertBSmtBomDetailBatch(any())).thenReturn(1);
        bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        smtLocationInfo.setSupplierLoc("");
        try {
            bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_INFO_LACKING, e.getMessage());
        }
        smtLocationInfo.setSupplierLoc("123");
        when(bSmtBomDetailRepository.insertBSmtBomDetailBatch(any())).thenReturn(0);
        try {
            bSmtBomHeaderServiceImpl.asmImportBom(uploadList, factoryId, empNo, entityId);
        } catch (Exception e) {
            Assert.assertEquals((locationInfoList.get(NumConstant.NUM_ZERO).getLineCode() + "-" + onLineWorkOrderList.get(NumConstant.NUM_ZERO).getWorkOrder().substring(0, 7) + ":" + CommonUtils.getLmbMessage(MessageId.DETAIL_TABLE_INSERT_FAIL)), e.getMessage());
        }
    }
    /* Ended by AICoder, pid:h6ef395711g6dca14c8d0826f1d33c4ef8c6bc6e */

    /* Started by AICoder, pid:u4c05l4b6f48cce14f9b0b40a065db498048eb2d */
    @Test
    public void TestdeleteOrInsertOrUpdateMethod() throws Exception {
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        List<BSmtBomDetail> detailDeleteList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        detailDeleteList.add(bSmtBomDetail);
        List<BSmtBomDetail> detailUpdateOrInsertList = new ArrayList<>();
        BSmtBomHeader headInfo = new BSmtBomHeader();
        headInfo.setCfgHeaderId("456");
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        try {
            bSmtBomHeaderServiceImpl.deleteOrInsertOrUpdateMethod(record, detailDeleteList, detailUpdateOrInsertList, headInfo, workOrder);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        detailDeleteList.remove(bSmtBomDetail);
        try {
            bSmtBomHeaderServiceImpl.deleteOrInsertOrUpdateMethod(record, detailDeleteList, detailUpdateOrInsertList, headInfo, workOrder);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        detailUpdateOrInsertList.add(bSmtBomDetail1);
        try {
            bSmtBomHeaderServiceImpl.deleteOrInsertOrUpdateMethod(record, detailDeleteList, detailUpdateOrInsertList, headInfo, workOrder);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
        record.setPreManue(Constant.FLAG_Y);
        List<BSmtBomDetail> detailListAfterDelete = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail2 = new BSmtBomDetail();
        detailListAfterDelete.add(bSmtBomDetail2);
        when(bSmtBomDetailRepository.selectBSmtBomDetailListById(Mockito.any())).thenReturn(detailListAfterDelete);
        BSmtBomHeader detailInfo = new BSmtBomHeader();
        when(bSmtBomDetailService.getBomHeaderAndDetail(Mockito.any(), Mockito.any())).thenReturn(detailInfo);
        try {
            bSmtBomHeaderServiceImpl.deleteOrInsertOrUpdateMethod(record, detailDeleteList, detailUpdateOrInsertList, headInfo, workOrder);
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.INSERT_OR_UPDATE_SMT, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:u4c05l4b6f48cce14f9b0b40a065db498048eb2d */


    /* Started by AICoder, pid:h2761s51ada9eae14ea00a0920403953c276e2c3 */
    @Test
    public void TestcompareQty() throws Exception {
        ServiceData ret = new ServiceData();
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        TreeMap<String, BigDecimal> deleteSumMap = new TreeMap<>();
        record.setDeleteSumMap(deleteSumMap);
        TreeMap<String, BigDecimal> totalSumMap = new TreeMap<>();
        record.setTotalSumMap(totalSumMap);
        List<BBomDetail> bbomQtyList = new ArrayList<>();
        BBomDetail bBomDetail = new BBomDetail();
        bBomDetail.setItemCode("123");
        bbomQtyList.add(bBomDetail);
        record.setBbomQtyList(bbomQtyList);
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        totalSumMap.put("123", new BigDecimal(2));
        List<BSmtBomDetail> bsmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("123");
        bSmtBomDetail.setQty(new BigDecimal(2));
        bsmtBomDetailList.add(bSmtBomDetail);
        record.setBsmtBomDetailList(bsmtBomDetailList);
        bBomDetail.setUsageCount(new BigDecimal(2));
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        totalSumMap.put("456", null);
        BBomDetail bBomDetail1 = new BBomDetail();
        bBomDetail1.setItemCode("456");
        bbomQtyList.add(bBomDetail1);
        bBomDetail1.setUsageCount(new BigDecimal(2));
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        record.setDeleteSumMap(null);
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        deleteSumMap.put("123", new BigDecimal(2));
        record.setDeleteSumMap(deleteSumMap);
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        deleteSumMap.put("789", null);
        BBomDetail bBomDetail2 = new BBomDetail();
        bBomDetail2.setItemCode("789");
        bBomDetail2.setUsageCount(new BigDecimal(2));
        bbomQtyList.add(bBomDetail2);
        totalSumMap.put("789", new BigDecimal(3));
        when(SpringContextUtil.getBean(Mockito.anyString())).thenReturn(lmb);
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        bSmtBomDetail1.setItemCode("1011");
        bSmtBomDetail1.setQty(new BigDecimal(2));
        bsmtBomDetailList.add(bSmtBomDetail1);
        bSmtBomHeaderServiceImpl.compareQty(ret, record);
        bBomDetail.setItemCode(null);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.compareQty(ret, record));
    }
    /* Ended by AICoder, pid:h2761s51ada9eae14ea00a0920403953c276e2c3 */

    /* Started by AICoder, pid:4d172j94feuf3c414f7909d6d0d47b31bf654704 */
    @Test
    public void TestoperateMethod() throws Exception {
        TreeMap<String, BigDecimal> deleteSumMap = new TreeMap<>();
        deleteSumMap.put("123", new BigDecimal(2));
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        record.setDeleteSumMap(deleteSumMap);
        List<BSmtBomDetail> detailDeleteList = new ArrayList<>();
        record.setDetailDeleteList(detailDeleteList);
        List<BSmtBomDetail> detailUpdateOrInsertList = new ArrayList<>();
        record.setDetailUpdateOrInsertList(detailUpdateOrInsertList);
        ServiceData ret = new ServiceData();
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        BSmtBomHeader headInfo = new BSmtBomHeader();
        TreeMap<String, BigDecimal> totalSumMap = new TreeMap<>();
        totalSumMap.put("123", new BigDecimal(2));
        record.setTotalSumMap(totalSumMap);
        List<BSmtBomDetail> bsmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("123");
        bSmtBomDetail.setQty(new BigDecimal(2));
        bsmtBomDetailList.add(bSmtBomDetail);
        record.setBsmtBomDetailList(bsmtBomDetailList);
        List<BBomDetail> bbomQtyList = new ArrayList<>();
        BBomDetail bBomDetail = new BBomDetail();
        bBomDetail.setItemCode("123");
        bBomDetail.setUsageCount(new BigDecimal(2));
        bbomQtyList.add(bBomDetail);
        record.setBbomQtyList(bbomQtyList);
        when(SpringContextUtil.getBean(Mockito.anyString())).thenReturn(lmb);
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(json);
        bSmtBomHeaderServiceImpl.operateMethod(record, ret, workOrder, headInfo);
        totalSumMap.clear();
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.operateMethod(record, ret, workOrder, headInfo));
    }
    /* Ended by AICoder, pid:4d172j94feuf3c414f7909d6d0d47b31bf654704 */

    @Test
    public void getExportBomTest1() throws Exception {
        Map<String, Integer> cellNameMap = new HashMap<>();
        cellNameMap.put(Constant.LOCATION_COLUMN, 0);
        cellNameMap.put(Constant.ITEMCODE_COLUMN, 1);
        cellNameMap.put("物料型号", 2);
        cellNameMap.put("表面标识", 3);
        cellNameMap.put(MpConstant.STEP_DISTANCE, 4);
        cellNameMap.put(MpConstant.MATERIAL_RACK, 5);
        cellNameMap.put(MpConstant.PATCH_PROGRAM_DIRECTION, 6);
        cellNameMap.put(MpConstant.PATCH_SUCTION_NOZZLE, 7);
        cellNameMap.put(MpConstant.PATCH_PRESSURE, 8);
        cellNameMap.put(MpConstant.CHIP_PROGRAMMING_ID, null);
        cellNameMap.put(MpConstant.DIRECTION, 9);
        cellNameMap.put(Constant.AM_COLUMN, 10);
        cellNameMap.put(Constant.BM_COLUMN, 11);

        Map<String, SmtLocationInfo> locationInfoMap = new HashMap<>();
        SmtLocationInfo smtLocationInfo = new SmtLocationInfo();
        smtLocationInfo.setMachineId("123");
        smtLocationInfo.setSupplierLoc("456");
        smtLocationInfo.setToSupplierLoc("789");
        smtLocationInfo.setToSupplierMachine("012");
        locationInfoMap.put("(AIMEX4S-2)2-31-1", smtLocationInfo);
        locationInfoMap.put("", new SmtLocationInfo());

        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTOA = new ArrayList<>();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTOB = new ArrayList<>();
        bSmtBomIDTO.setBSmtBomInfoDetailDTOOfA(bSmtBomInfoDetailDTOA);
        bSmtBomIDTO.setBSmtBomInfoDetailDTOOfB(bSmtBomInfoDetailDTOB);
        bSmtBomIDTO.setTransferIndex(0);

        when(sheet.getLastRowNum()).thenReturn(10);
        Row row1 = PowerMockito.mock(Row.class);
        when(row1.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(0).toString()).thenReturn("/");
        when(row1.getCell(10)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(10).toString()).thenReturn("/");
        when(row1.getCell(11)).thenReturn(PowerMockito.mock(Cell.class));
        when(row1.getCell(11).toString()).thenReturn("1");
        when(sheet.getRow(1)).thenReturn(row1);

        Row row2 = PowerMockito.mock(Row.class);
        when(row2.getCell(0)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(0).toString()).thenReturn("/");
        when(row2.getCell(10)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(10).toString()).thenReturn("1");
        when(row2.getCell(11)).thenReturn(PowerMockito.mock(Cell.class));
        when(row2.getCell(11).toString()).thenReturn("/");
        when(sheet.getRow(2)).thenReturn(row2);
        bSmtBomHeaderServiceImpl.getExportBom(sheet, cellNameMap, locationInfoMap, true, bSmtBomIDTO);
        Assert.assertEquals(1,bSmtBomInfoDetailDTOA.size());
        Assert.assertEquals(1,bSmtBomInfoDetailDTOB.size());
        Assert.assertNotNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "dealNum", ""));
        Assert.assertNotNull(Whitebox.invokeMethod(bSmtBomHeaderServiceImpl, "dealNum", "1"));
    }

}