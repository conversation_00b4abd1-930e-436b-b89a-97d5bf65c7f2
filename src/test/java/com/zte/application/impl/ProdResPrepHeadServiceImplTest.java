package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.AssertionErrors.fail;

/**
 * 生产资源准备信息头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-30 09:07:37
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MpConstant.class, CrafttechRemoteService.class, PlanscheduleRemoteService.class, MESHttpHelper.class, MpConstant.class,
        CloudDiskHelper.class, BasicsettingRemoteService.class, BeanUtils.class, HttpServletResponse.class, HttpClientUtil.class,
        ServiceDataBuilderUtil.class, ImesExcelUtil.class, HttpRemoteUtil.class})
public class ProdResPrepHeadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ProdResPrepHeadServiceImpl service;
    @Spy
    private ProdResPrepHeadServiceImpl service1 = new ProdResPrepHeadServiceImpl();
    @Mock
    private ProdResPrepHeadRepository repository;
    @Mock
    private ProdResPrepDetailServiceImpl prodResPrepDetailService;
    @Mock
    private ProdResPrepDetailRepository prodResPrepDetailRepository;
    @Mock
    private PrepareItemConfigRepository prepareItemConfigRepository;
    @Mock
    private HttpServletResponse response;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private PrepareItemDataObtainServiceImpl prepareItemDataObtainService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ProdResPrepHeadPageQueryDTO query;

    @Mock
    private Page<Map<String, Object>> prepareData;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
    }

    @Test
    public void testGetUnfinishedRecord_NULL() throws Exception {
        List<ProdResPrepHeadPageQueryDTO> res = new ArrayList<>();
        Boolean showUnfinished = null;
        List<String> dynamicProps =new ArrayList<>();
        Whitebox.invokeMethod(service, "reverseNumToString", res, showUnfinished, dynamicProps);
        Boolean showUnfinished1 = true;
        Whitebox.invokeMethod(service, "reverseNumToString", res, showUnfinished1, dynamicProps);
        Assert.assertTrue(true);
    }


    @Test
    public void testReverseNumToString() throws Exception {
        List<ProdResPrepHeadPageQueryDTO> res = new ArrayList<>();
        ProdResPrepHeadPageQueryDTO dto1 = new ProdResPrepHeadPageQueryDTO();
        dto1.setLineSideReceive("0");
        dto1.setFixture("1");
        dto1.setPrepareStatus("2");
        res.add(dto1);
        ProdResPrepHeadPageQueryDTO dto2 = new ProdResPrepHeadPageQueryDTO();
        dto2.setLineSideReceive("1");
        dto2.setFixture("3");
        dto2.setPrepareStatus("0");
        res.add(dto2);
        Boolean showUnfinished = true;
        List<String> dynamicProps = Arrays.asList("lineSideReceive", "fixture");
        Whitebox.invokeMethod(service, "reverseNumToString", res, showUnfinished, dynamicProps);
        assertEquals("未准备完成", dto1.getLineSideReceive());
        assertEquals("准备完成", dto1.getFixture());
        assertEquals("准备中", dto1.getPrepareStatus());
        assertEquals("准备完成", dto2.getLineSideReceive());
        assertEquals("不涉及", dto2.getFixture());
        assertEquals("未准备完成", dto2.getPrepareStatus());
        assertEquals(1, res.size());
        assertEquals("未准备完成", res.get(0).getLineSideReceive());
    }

    @Test
    public void testReverseNumToString_ShowUnfinishedFalse() throws Exception {
        List<ProdResPrepHeadPageQueryDTO> res = new ArrayList<>();
        ProdResPrepHeadPageQueryDTO dto1 = new ProdResPrepHeadPageQueryDTO();
        dto1.setLineSideReceive("0");
        dto1.setFixture("1");
        dto1.setPrepareStatus("2");
        res.add(dto1);
        Boolean showUnfinished = false;
        List<String> dynamicProps = Arrays.asList("lineSideReceive", "fixture");
        Whitebox.invokeMethod(service, "reverseNumToString", res, showUnfinished, dynamicProps);
        assertEquals("未准备完成", dto1.getLineSideReceive());
        assertEquals("准备完成", dto1.getFixture());
        assertEquals("准备中", dto1.getPrepareStatus());

        // 4. 验证筛选未完成记录
        assertEquals(1, res.size()); // 未开启筛选，所有记录保留
    }

    @Test
    public void testReverseNumToString_EmptyDynamicProps() throws Exception {
        List<ProdResPrepHeadPageQueryDTO> res = new ArrayList<>();
        ProdResPrepHeadPageQueryDTO dto1 = new ProdResPrepHeadPageQueryDTO();
        dto1.setLineSideReceive("0");
        dto1.setFixture("1");
        dto1.setPrepareStatus("2");
        res.add(dto1);
        Boolean showUnfinished = true;
        List<String> dynamicProps = new ArrayList<>();
        Whitebox.invokeMethod(service, "reverseNumToString", res, showUnfinished, dynamicProps);
        assertEquals(1, res.size());
    }

    @Test
    public void testGetUnfinishedRecord() throws Exception {
        List<ProdResPrepHeadPageQueryDTO> res = new ArrayList<>();
        ProdResPrepHeadPageQueryDTO dto1 = new ProdResPrepHeadPageQueryDTO();
        dto1.setLineSideReceive("准备完成");
        dto1.setFixture("不涉及");
        res.add(dto1);
        ProdResPrepHeadPageQueryDTO dto2 = new ProdResPrepHeadPageQueryDTO();
        dto2.setLineSideReceive("未完成");
        dto2.setFixture("准备完成");
        res.add(dto2);
        Boolean showUnfinished = true;
        List<String> dynamicProps = Arrays.asList("lineSideReceive", "fixture");
        Whitebox.invokeMethod(service, "getUnfinishedRecord", res, showUnfinished, dynamicProps);
        assertEquals(1, res.size());
        assertEquals("未完成", res.get(0).getLineSideReceive());
    }

    @Test
    public void testGetFieldValue() throws Exception {
        ProdResPrepHeadPageQueryDTO dto = new ProdResPrepHeadPageQueryDTO();
        dto.setLineSideReceive("准备完成");
        String value = Whitebox.invokeMethod(service, "getFieldValue", dto, "lineSideReceive");
        assertEquals("准备完成", value);
    }

    @Test
    public void testGetFieldValue_FieldNotFound() throws Exception {
        ProdResPrepHeadPageQueryDTO dto = new ProdResPrepHeadPageQueryDTO();
        try {
            Whitebox.invokeMethod(service, "getFieldValue", dto, "nonExistentField");
            fail("Expected MesBusinessException was not thrown");
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void testCapitalize() throws Exception {
        String result = Whitebox.invokeMethod(service, "capitalize", "test");
        assertEquals("Test", result);
    }

    @Test
    public void testConvertFieldValue() throws Exception {
        String prop = "lineSideReceive";
        String value = null;
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value);
        String value1 = "0";
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value1);
        String value2 = "1";
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value2);
        String value3 = "2";
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value3);
        String value4 = "3";
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value4);
        String value5 = "4";
        Whitebox.invokeMethod(service, "convertFieldValue", prop, value5);
        String value6 = "dd";
        try {
            Whitebox.invokeMethod(service, "convertFieldValue", prop, value6);
        } catch (Exception e) {
            assertTrue(true);
        }
        assertTrue(true);
    }

    @Test
    public void testGetSpecialStatus() throws Exception {
        String prop = "lineSideReceive";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop);
        String prop2 = "firstPreparation";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop2);
        String prop3 = "fixture";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop3);
        String prop4 = "solderPaste";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop4);
        String prop5 = "steelMesh";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop5);
        String prop6 = "labelPCBs";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop6);
        String prop7 = "program";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop7);
        String prop8 = "1";
        Whitebox.invokeMethod(service, "getSpecialStatus", prop8);
        assertTrue(true);
    }

    @Test
    public void testGetBigDecimalValue() throws Exception {
        Map<String, Object> row = new HashMap<>();
        row.put("1", new BigDecimal("1"));
        String key = "1";
        Whitebox.invokeMethod(service, "getBigDecimalValue", row, key);
        Map<String, Object> row1 = new HashMap<>();
        row1.put("1", "11");
        String key1 = "1";
        Whitebox.invokeMethod(service, "getBigDecimalValue", row1, key1);
        assertTrue(true);
    }

    @Test
    public void testGetDateValue() throws Exception {
        Map<String, Object> row = new HashMap<>();
        row.put("1", new Date());
        String key = "1";
        Whitebox.invokeMethod(service, "getDateValue", row, key);
        Map<String, Object> row1 = new HashMap<>();
        row1.put("1", "2003-04-22 00:00:00");
        String key1 = "1";
        Whitebox.invokeMethod(service, "getDateValue", row1, key1);
        Map<String, Object> row2 = new HashMap<>();
        row2.put("1", "dd");
        String key2 = "1";
        try {
            Whitebox.invokeMethod(service, "getDateValue", row2, key2);
        } catch (Exception e) {
            assertTrue(true);
        }
    }

    @Test
    public void testExport_WithData2() throws Exception {
        doReturn(null).when(service1).getPrepareData(any(ProdResPrepHeadPageQueryDTO.class));
        service1.export(response, query);
        assertTrue(true);
    }

    @Test
    public void testExport_WithData() throws Exception {
        List<Map<String, Object>> rows = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("lineName", "Line1");
        row.put("workOrderNo", "WO001");
        row.put("prepareStatus", "1");
        rows.add(row);
        when(prepareData.getRows()).thenReturn(rows);
        doReturn(prepareData).when(service1).getPrepareData(any(ProdResPrepHeadPageQueryDTO.class));
        when(query.getSelectColumns()).thenReturn(Arrays.asList("线边仓收料", "工装夹具"));
        when(query.getShowUnfinished()).thenReturn(false);
        service1.export(response, query);
        PowerMockito.verifyStatic(ImesExcelUtil.class, times(1));
        ImesExcelUtil.exportExcelModel(
                eq(Constant.PROD_RES_PREP_HEAD),
                any(String[].class),
                any(List.class),
                any(String[].class),
                eq(MpConstant.ProdResPrepHead.SHEETNAME),
                eq(response)
        );
        assertTrue(true);
    }


    @Test
    public void regularCalProdPre() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class, PlanscheduleRemoteService.class,
                CloudDiskHelper.class, BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> lookup286518 = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("1");
        sysLookupValuesDTO.setDescriptionChin("SMT-A");
        lookup286518.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(lookup286518);
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoWithPsTask(Mockito.any())).thenReturn(workOrderList);
        service.regularCalProdPre(new ArrayList<>(), "00286523", false);
        List<String> order = new ArrayList<>();
        order.add("test123");
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("test123");
        psWorkOrderDTO.setLineCode("SMT-NJ001");
        psWorkOrderDTO.setProcessGroup("");
        psWorkOrderDTO.setRouteId("21324");
        workOrderList.add(psWorkOrderDTO);
        PsWorkOrderDTO psWorkOrderDTO1 = new PsWorkOrderDTO();
        psWorkOrderDTO1.setWorkOrderNo("8888888-SMT-A5801");
        psWorkOrderDTO1.setLineCode("SMT-NJ001");
        psWorkOrderDTO1.setProcessGroup("1");
        psWorkOrderDTO1.setRouteId("21324");
        workOrderList.add(psWorkOrderDTO1);
        service.regularCalProdPre(order, "00286523", false);
        psWorkOrderDTO.setProcessGroup("1");
        service.regularCalProdPre(order, "00286523", false);
        List<String> skipRoutes = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.routeContainProcess(Mockito.any(), Mockito.anyList())).thenReturn(skipRoutes);
        service.regularCalProdPre(order, "00286523", false);
        skipRoutes.add("1");
        service.regularCalProdPre(order, "00286523", false);
        List<ProdResPrepHead> isPreparedOrder = new ArrayList<>();
        PowerMockito.when(repository.selectProdPreHeadList(Mockito.any())).thenReturn(isPreparedOrder);
        service.regularCalProdPre(order, "00286523", false);
        ProdResPrepHead resPrepHead = new ProdResPrepHead();
        resPrepHead.setWorkOrderNo("test123");
        isPreparedOrder.add(resPrepHead);
        service.regularCalProdPre(order, "00286523", false);
        service.regularCalProdPre(order, "00286523", true);
        List<PrepareItemConfig> prepareItemConfigList = new ArrayList<>();
        PowerMockito.when(prepareItemConfigRepository.selectPreConfig(Mockito.any())).thenReturn(prepareItemConfigList);
        PrepareItemConfig prepareItemConfig = new PrepareItemConfig();
        prepareItemConfig.setAccessWay(1);
        prepareItemConfig.setPrepareItemCode("人工确认");
        prepareItemConfigList.add(prepareItemConfig);
        List<ProdResPrepDetail> detailList = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setWorkOrderNo("8888888-SMT-A5801");
        detailList.add(prepDetail);
        PowerMockito.when(prodResPrepDetailRepository.selectProdPreDetailList(Mockito.any())).thenReturn(detailList);
        Assert.assertNotNull(service.regularCalProdPre(order, "00286523", false));
    }

    @Test
    public void approveWorkOrderList() {
        ProdPrepBuildDTO prepBuildDTO = new ProdPrepBuildDTO();
        List<ProdResPrepHead> insertHeadList = new ArrayList<>();
        List<ProdResPrepHead> updateHeadList = new ArrayList<>();
        service.approveWorkOrderList(new ArrayList<>(), prepBuildDTO);
        ProdResPrepHead prepHead = new ProdResPrepHead();
        prepHead.setWorkOrderNo("8888888-SMT-A5801");
        prepHead.setPrepareStatus("0");
        insertHeadList.add(prepHead);
        updateHeadList.add(prepHead);
        prepBuildDTO.setInsertHeadList(insertHeadList);
        prepBuildDTO.setUpdateHeadList(updateHeadList);
        service.approveWorkOrderList(new ArrayList<>(), prepBuildDTO);

        prepHead.setPrepareStatus("1");
        service.approveWorkOrderList(new ArrayList<>(), prepBuildDTO);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateOrInsertPrepData() {
        ProdPrepBuildDTO prepBuildDTO = new ProdPrepBuildDTO();
        List<ProdResPrepHead> insertHeadList = new ArrayList<>();
        List<ProdResPrepDetail> updateDetailList = new ArrayList<>();
        ProdResPrepHead prepHead = new ProdResPrepHead();
        prepHead.setWorkOrderNo("8888888-SMT-A5801");
        prepHead.setPrepareStatus("0");
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setWorkOrderNo("8888888-SMT-A5801");
        prepDetail.setPrepareStatus("0");
        insertHeadList.add(prepHead);
        updateDetailList.add(prepDetail);
        prepBuildDTO.setInsertHeadList(new ArrayList<>());
        prepBuildDTO.setUpdateHeadList(new ArrayList<>());
        prepBuildDTO.setInsertDetailList(new ArrayList<>());
        prepBuildDTO.setUpdateDetailList(new ArrayList<>());
        service.updateOrInsertPrepData(prepBuildDTO);
        prepBuildDTO.setInsertHeadList(insertHeadList);
        prepBuildDTO.setUpdateHeadList(insertHeadList);
        prepBuildDTO.setInsertDetailList(updateDetailList);
        prepBuildDTO.setUpdateDetailList(updateDetailList);
        PowerMockito.when(repository.batchInsertProdResPrepHeadList(Mockito.any())).thenReturn(1);
        PowerMockito.when(repository.batchUpdateProdResPrepHeadList(Mockito.any())).thenReturn(1);
        PowerMockito.when(prodResPrepDetailRepository.batchInsertProdResPrepDetailList(Mockito.any())).thenReturn(1);
        PowerMockito.when(prodResPrepDetailRepository.batchUpdateProdResPrepDetailList(Mockito.any())).thenReturn(1);
        service.updateOrInsertPrepData(prepBuildDTO);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void prepItemForUpdate() {
        ProdPrepBuildDTO dto = new ProdPrepBuildDTO();
        try {
            service.prepItemForUpdate("00286523", new HashMap<>(), new HashMap<>(), new ArrayList<>(), new ProdPrepBuildDTO());
        } catch (Exception e) {
            e.printStackTrace();
        }
        PowerMockito.when(idGenerator.snowFlakeId()).thenReturn(new Long("123456789"));
        List<ProdResPrepDetail> existPrepInfoList = new ArrayList<>();
        ProdResPrepDetail prodResPrepDetail = new ProdResPrepDetail();
        prodResPrepDetail.setWorkOrderNo("8888888-SMT-A5801");
        List<ProdResPrepDetail> prodResPrepDetails = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prodResPrepDetails.add(prepDetail);
        prepDetail.setWorkOrderNo("8888888-SMT-A5801");
        prepDetail.setPrepareStatus("0");
        prepDetail.setPrepareItemCode("test123");
        prepDetail.setHeadId(new Long("123456"));
        prepDetail.setPrepareCompleteWay(Constant.ProdPreConfig.PRE_COMPLETE_USER);
        Map<String, Map<String, List<PrepareItemDataObtainDTO>>> preCheckMap = new HashMap<>();
        Map<String, List<PrepareItemDataObtainDTO>> preCheckDetailMap = new HashMap<>();
        List<PrepareItemDataObtainDTO> prepareItemDataObtainDTOS = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTOS.add(prepareItemDataObtainDTO);
        prepareItemDataObtainDTO.setPrepareItemCode("test123");
        prepareItemDataObtainDTO.setPrepareStatus("1");
        prepareItemDataObtainDTO.setWorkOrderNo("8888888-SMT-A5801");
        prepareItemDataObtainDTO.setLineCode("SMT-NJ001");
        preCheckDetailMap.put("test123", prepareItemDataObtainDTOS);
        preCheckMap.put("8888888-SMT-A5801", preCheckDetailMap);
        Map<Integer, List<String>> preItemMap = new HashMap<>();
        List<String> sysItem = new ArrayList<>();
        sysItem.add("test123");
        sysItem.add("test456");
        preItemMap.put(0, sysItem);
        preItemMap.put(1, sysItem);

        try {
            service.prepItemForUpdate("00286523", preItemMap, preCheckMap, prodResPrepDetails, new ProdPrepBuildDTO());
        } catch (Exception e) {
            e.printStackTrace();
        }
        prepDetail.setPrepareCompleteWay(null);
        prepareItemDataObtainDTO.setPrepareItemCode("test789");
        try {
            assertEquals(dto, service.prepItemForUpdate("00286523", preItemMap, preCheckMap, prodResPrepDetails, new ProdPrepBuildDTO()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void updateSysPreDetailWhenHeadExist() {
        List<PrepareItemDataObtainDTO> checkItemForPreList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO.setPrepareStatus("1");
        checkItemForPreList.add(prepareItemDataObtainDTO);
        List<String> itemPrepOKStatus = new ArrayList<>();
        itemPrepOKStatus.add("1");
        ProdResPrepDetail currExistItemDetail = new ProdResPrepDetail();
        currExistItemDetail.setPrepareStatus("0");
        service.updateSysPreDetailWhenHeadExist(itemPrepOKStatus, new ArrayList<>(), checkItemForPreList, currExistItemDetail);
        currExistItemDetail.setPrepareStatus("1");
        service.updateSysPreDetailWhenHeadExist(itemPrepOKStatus, new ArrayList<>(), checkItemForPreList, currExistItemDetail);
        prepareItemDataObtainDTO.setPrepareStatus("0");
        service.updateSysPreDetailWhenHeadExist(itemPrepOKStatus, new ArrayList<>(), checkItemForPreList, currExistItemDetail);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void createSysPreItemWithExistHead() {
        PowerMockito.when(idGenerator.snowFlakeId()).thenReturn(new Long("123456789"));
        List<PrepareItemDataObtainDTO> checkItemForPreList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO.setPrepareStatus("1");
        checkItemForPreList.add(prepareItemDataObtainDTO);
        List<String> itemPrepOKStatus = new ArrayList<>();
        itemPrepOKStatus.add("1");
        ProdResPrepDetail currExistItemDetail = new ProdResPrepDetail();
        currExistItemDetail.setHeadId(new Long("0"));
        service.createSysPreItemWithExistHead(itemPrepOKStatus, "test123", checkItemForPreList);
        prepareItemDataObtainDTO.setPrepareStatus("0");
        Assert.assertNotNull(service.createSysPreItemWithExistHead(itemPrepOKStatus, "test123", checkItemForPreList));
    }

    @Test
    public void updateUserPreDetailWithExistHead() {
        service.updateUserPreDetailWithExistHead(new HashMap<>(), new HashMap<>(), new ArrayList<>(), new ArrayList<>());
        Map<Integer, List<String>> preItemMap = new HashMap<>();
        List<String> sysItem = new ArrayList<>();
        sysItem.add("test123");
        sysItem.add("test456");
        preItemMap.put(0, sysItem);
        preItemMap.put(1, sysItem);
        List<ProdResPrepDetail> insertDetailTempList = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setPrepareItemCode("1");
        insertDetailTempList.add(prepDetail);
        Map<String, List<ProdResPrepDetail>> preItemExistMap = new HashMap<>();
        preItemExistMap.put("test123", new ArrayList<>());
        service.updateUserPreDetailWithExistHead(preItemMap, preItemExistMap, new ArrayList<>(), new ArrayList<>());
        preItemExistMap.put("test123", insertDetailTempList);
        service.updateUserPreDetailWithExistHead(preItemMap, preItemExistMap, new ArrayList<>(), new ArrayList<>());
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkUpdatePreHead() {
        List<String> itemPrepOKStatus = new ArrayList<>();
        itemPrepOKStatus.add("1");
        List<ProdResPrepDetail> insertDetailTempList = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setPrepareStatus("1");
        insertDetailTempList.add(prepDetail);
        List<ProdResPrepDetail> updateDetailTempList = new ArrayList<>();
        ProdResPrepDetail prepDetail1 = new ProdResPrepDetail();
        prepDetail1.setPrepareStatus("1");
        updateDetailTempList.add(prepDetail1);
        ProdResPrepHead prepHead = new ProdResPrepHead();
        prepHead.setPrepareStatus("0");
        service.checkUpdatePreHead(prepHead, insertDetailTempList, updateDetailTempList);
        prepHead.setPrepareStatus("1");
        service.checkUpdatePreHead(prepHead, insertDetailTempList, updateDetailTempList);
        prepDetail.setPrepareStatus("0");
        service.checkUpdatePreHead(prepHead, insertDetailTempList, updateDetailTempList);
        prepDetail.setPrepareStatus("1");
        prepDetail1.setPrepareStatus("0");
        service.checkUpdatePreHead(prepHead, insertDetailTempList, updateDetailTempList);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void mdsCheckResultMap() throws Exception {
        Map<Integer, List<String>> preItemMap = new HashMap() {{
            put(0, Lists.newArrayList(Constant.ProdPreConfig.EQP_MAINTENANCE));
        }};
        Assert.assertNull(service.allItemCheckResultMap(preItemMap, Lists.newArrayList()));
    }

    @Test
    public void allItemCheckResultMap() throws Exception {
        PowerMockito.mockStatic(BeanUtils.class);
        Map<Integer, List<String>> preItemMap = new HashMap<>();
        List<String> sysItem = new ArrayList<>();
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_ALLOCATE_BILL);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_LINESIDE_RECEIVE);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_PROGRAM);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_FIRST_PREPARETION);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_COMP_MATERIAL_PREPARATION);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_STOVE_TEMP);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_FIXTURE);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_STEEL_MESH);
        sysItem.add(Constant.ProdPreConfig.PRE_ITEM_SOLDER_PASTE);
        sysItem.add(Constant.ProdPreConfig.SMT_BOM_STATUS);
        sysItem.add(Constant.ProdPreConfig.LABEL_PCBs);
        sysItem.add("test123");
        preItemMap.put(0, sysItem);
        List<PrepareItemDataObtainDTO> checkAllocateBillDataList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO.setPrepareStatus("0");
        prepareItemDataObtainDTO.setPrepareItemCode(Constant.ProdPreConfig.PRE_ITEM_ALLOCATE_BILL);
        prepareItemDataObtainDTO.setWorkOrderNo("8888888-SMT-A5801");
        checkAllocateBillDataList.add(prepareItemDataObtainDTO);
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setLineCode("SMT-NJ001");
        psWorkOrderDTO.setWorkOrderNo("8888888-SMT-A5801");
        workOrderList.add(psWorkOrderDTO);

        PowerMockito.when(prepareItemDataObtainService.itemInfoExeObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.warehouseMaterialReceivingObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.firstStandbyObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.writeFilmObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.temperatureObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.fixtureObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.stencilObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.solderObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        PowerMockito.when(prepareItemDataObtainService.labelPCBsObtain(Mockito.anyList())).thenReturn(checkAllocateBillDataList);
        Assert.assertNotNull(service.allItemCheckResultMap(preItemMap, workOrderList));
    }

    @Test
    public void prepItemForInsert() {
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setLineCode("SMT-NJ001");
        psWorkOrderDTO.setWorkOrderNo("8888888-SMT-A5801");
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(idGenerator.snowFlakeId()).thenReturn(new Long("123456789"));
        service.prepItemForInsert(workOrderList, new HashMap<>(), "00286523", new HashMap<>());
        Map<String, Map<String, List<PrepareItemDataObtainDTO>>> preCheckMap = new HashMap<>();
        Map<String, List<PrepareItemDataObtainDTO>> preCheckDetailMap = new HashMap<>();
        List<PrepareItemDataObtainDTO> prepareItemDataObtainDTOS = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTOS.add(prepareItemDataObtainDTO);
        prepareItemDataObtainDTO.setPrepareItemCode("test123");
        prepareItemDataObtainDTO.setPrepareStatus("1");
        prepareItemDataObtainDTO.setWorkOrderNo("8888888-SMT-A5801");
        prepareItemDataObtainDTO.setLineCode("SMT-NJ001");
        preCheckDetailMap.put("test123", prepareItemDataObtainDTOS);
        preCheckMap.put("8888888-SMT-A5801", preCheckDetailMap);
        Map<Integer, List<String>> preItemMap = new HashMap<>();
        List<String> sysItem = new ArrayList<>();
        sysItem.add("test123");
        sysItem.add("test456");
        preItemMap.put(0, sysItem);
        preItemMap.put(1, sysItem);
        service.prepItemForInsert(workOrderList, preItemMap, "00286523", preCheckMap);

        prepareItemDataObtainDTO.setPrepareStatus("0");
        service.prepItemForInsert(workOrderList, preItemMap, "00286523", preCheckMap);
        preCheckMap.put("8888888-SMT-A5801", new HashMap<>());
        Assert.assertNotNull(service.prepItemForInsert(workOrderList, preItemMap, "00286523", preCheckMap));
    }

    @Test
    public void checkInsertPreHeadStatus() {
        service.checkInsertPreHeadStatus(new ArrayList<>(), new ProdResPrepHead(), new ArrayList<>());
        List<ProdResPrepDetail> insertDetailTempList = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setPrepareStatus("0");
        insertDetailTempList.add(prepDetail);
        List<String> itemPrepOKStatus = new ArrayList<>();
        itemPrepOKStatus.add("1");
        service.checkInsertPreHeadStatus(itemPrepOKStatus, new ProdResPrepHead(), insertDetailTempList);

        prepDetail.setPrepareStatus("1");
        service.checkInsertPreHeadStatus(itemPrepOKStatus, new ProdResPrepHead(), insertDetailTempList);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void testGetPrepareData() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class);
        ProdResPrepHeadPageQueryDTO prodResPrepHeadPageQueryDTO = new ProdResPrepHeadPageQueryDTO();
        Assert.assertNull(service.getPrepareData(prodResPrepHeadPageQueryDTO));
        List<String> lineCodeList = new ArrayList<>();
        lineCodeList.add("123");
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        prodResPrepHeadPageQueryDTO.setLineCodeList(lineCodeList);
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        List<PsWorkOrderDTO> workOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderDTOList.add(psWorkOrderDTO);
        Page<PsWorkOrderDTO> page = new Page<>();
        page.setRows(workOrderDTOList);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoWithPsTaskPage(any())).thenReturn(page);
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        List<ProdResPrepHeadPageQueryDTO> queryList = new ArrayList<>();
        queryList.add(prodResPrepHeadPageQueryDTO);
        PowerMockito.when(repository.getPrepareData(any())).thenReturn(queryList);
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        List<SysLookupValuesDTO> listSys = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        listSys.add(sysLookupValuesDTO);
        List<CFLine> cfLineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyMap())).thenReturn(listSys);
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyMap())).thenReturn(new ArrayList<>());
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyMap())).thenReturn(listSys);
        psWorkOrderDTO.setLeadFlag("1");
        sysLookupValuesDTO.setLookupMeaning("1");
        sysLookupValuesDTO.setDescriptionChin("1");
        service.getPrepareData(prodResPrepHeadPageQueryDTO);
        psWorkOrderDTO.setWorkOrderNo("1234");
        psWorkOrderDTO.setSourceTask("1234");
        prodResPrepHeadPageQueryDTO.setWorkOrderNo("1234");
        prodResPrepHeadPageQueryDTO.setPrepareItemCode("1234");
        prodResPrepHeadPageQueryDTO.setDetailPrepareStatus("0");
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(mBomList);

        Assert.assertNotNull(service.getPrepareData(prodResPrepHeadPageQueryDTO));
    }

    @Test
    public void testManualConfirm() {
        ProdResPrepHeadPageQueryDTO prodResPrepHeadPageQueryDTO = new ProdResPrepHeadPageQueryDTO();
        service.manualConfirm(prodResPrepHeadPageQueryDTO);
        prodResPrepHeadPageQueryDTO.setWorkOrderNo("123");
        prodResPrepHeadPageQueryDTO.setActivity("Y");
        service.manualConfirm(prodResPrepHeadPageQueryDTO);
        List<ProdResPrepDetail> detailList = new ArrayList<>();
        ProdResPrepDetail prodResPrepDetail = new ProdResPrepDetail();
        detailList.add(prodResPrepDetail);
        prodResPrepDetail.setId(123L);
        PowerMockito.when(prodResPrepDetailRepository.getUnfinishedPrepareItem(any())).thenReturn(detailList);
        service.manualConfirm(prodResPrepHeadPageQueryDTO);
        prodResPrepHeadPageQueryDTO.setActivity("N");
        try {
            service.manualConfirm(prodResPrepHeadPageQueryDTO);
        } catch (Exception e) {
            assertEquals(e.getMessage(), MessageId.ACTIVITY_IS_N_AND_DATA_NULL);
        }
        prodResPrepHeadPageQueryDTO.setUnfinishedPrepareItem(detailList);
        service.manualConfirm(prodResPrepHeadPageQueryDTO);
    }

    @Test
    public void testGetUnfinishedPrepareItem() {
        ProdResPrepHeadPageQueryDTO prodResPrepHeadPageQueryDTO = new ProdResPrepHeadPageQueryDTO();
        service.getUnfinishedPrepareItem(prodResPrepHeadPageQueryDTO);
        List<PrepareItemConfig> itemList = new ArrayList<>();
        List<ProdResPrepDetail> list = new ArrayList<>();
        PrepareItemConfig prepareItemConfig = new PrepareItemConfig();
        itemList.add(prepareItemConfig);
        ProdResPrepDetail prodResPrepDetail = new ProdResPrepDetail();
        ProdResPrepDetail prodResPrepDetail1 = new ProdResPrepDetail();
        list.add(prodResPrepDetail);
        list.add(prodResPrepDetail1);
        prepareItemConfig.setPrepareItemCode("1234");
        prodResPrepDetail.setPrepareItemCode("1234");
        PowerMockito.when(prepareItemConfigRepository.selectPage(any())).thenReturn(itemList);
        PowerMockito.when(prodResPrepDetailRepository.getUnfinishedPrepareItem(any())).thenReturn(list);
        service.getUnfinishedPrepareItem(prodResPrepHeadPageQueryDTO);
        prepareItemConfig.setActivity("Y");
        Assert.assertNotNull(service.getUnfinishedPrepareItem(prodResPrepHeadPageQueryDTO));
    }

    @Test
    public void selectProdPreWorkList() {
        assertTrue(service.selectProdPreWorkList(null).isEmpty());
        assertTrue(service.selectProdPreWorkList(new ProdResPrepHeadPageQueryDTO()).isEmpty());
        ProdResPrepHeadPageQueryDTO dto = new ProdResPrepHeadPageQueryDTO();
        List<String> list = new ArrayList<>();
        list.add("121");
        dto.setWorkOrderNoList(list);
        assertTrue(service.selectProdPreWorkList(dto).isEmpty());
    }
}

