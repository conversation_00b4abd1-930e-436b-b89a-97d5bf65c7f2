/*Started by AICoder, pid:78cce7af753b49478612e2f145fcb19f*/
package com.zte.application.impl;

import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@RunWith(PowerMockRunner.class)
public class WarehouseEntryDetailServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    WarehouseEntryDetailServiceImpl service;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Test
    public void getCompletedQty() {
        PowerMockito.when(warehouseEntryDetailRepository.getCompletedQty(Mockito.anyList())).thenReturn(new ArrayList<>());
        service.getCompletedQty(new ArrayList<>());
        List<String> stringList =new ArrayList<>();
        stringList.add("test123");
        Assert.assertNotNull(service.getCompletedQty(stringList));
    }

    @Test(timeout = 8000)
    public void deleteWarehouseEntryDetail_withValidBillNo_shouldCallRepositoryMethod() {
        String billNo = "validBillNo";
        service.deleteWarehouseEntryDetail(billNo);
        verify(warehouseEntryDetailRepository, times(1)).deleteWarehouseEntryDetail(billNo);
    }
}
/*Ended by AICoder, pid:78cce7af753b49478612e2f145fcb19f*/