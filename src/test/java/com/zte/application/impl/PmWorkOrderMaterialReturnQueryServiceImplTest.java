package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.QueryAndSetPropertyLineNameService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.PmWorkOrderMaterialReturnQuery;
import com.zte.domain.model.PmWorkOrderMaterialReturnQueryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmWorkOrderMaterialReturnQueryDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.export.WarehouseEntryExportDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class, RedisHelper.class,RedisLock.class,
		EasyExcelFactory.class,ImesExcelUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,ObtainRemoteServiceDataUtil.class,PlanscheduleRemoteService.class})
public class PmWorkOrderMaterialReturnQueryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PmWorkOrderMaterialReturnQueryServiceImpl service;

    @Mock
    PmWorkOrderMaterialReturnQueryRepository pmWorkOrderMaterialReturnQueryRepository;
	@Mock
	QueryAndSetPropertyLineNameService queryAndSetPropertyLineNameService;
	@Mock
	HttpServletRequest request;
	@Mock
	HttpServletResponse response;
	@Mock
	private ExcelWriter excelWriter;
	@Mock
	private WriteSheet build;
	@Mock
	private ExcelWriterSheetBuilder excelWriterSheetBuilder;
	@Mock
	private ExcelWriterBuilder write;
	@Mock
	private ExcelWriterBuilder excelWriterBuilder;
	@Mock
	private CloudDiskHelper cloudDiskHelper;
	@Mock
	EmailUtils emailUtils;
	@Mock
	private CenterfactoryRemoteService centerfactoryRemoteService;
    @Test
    public void updateForCenter() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
        PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
        pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
        pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
        ServiceData serviceData = new ServiceData();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));

        Whitebox.invokeMethod(service, "setItemNoAndProdplanNo", pmWorkOrderMaterialReturnQueries);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

    @Test
    public void getPage() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
        PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
        pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
        pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
        ServiceData serviceData = new ServiceData();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));

        PmWorkOrderMaterialReturnQueryDTO dto = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1"); setSourceTask("1");
		}}));
        PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getPage(Mockito.anyObject()))
                .thenReturn(pmWorkOrderMaterialReturnQueries);
        service.getPage(dto);
		dto.setSourceTask("1");
		Assert.assertEquals(pmWorkOrderMaterialReturnQueries,service.getPage(dto));

		List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
		BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
		BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
		headerDTO2.setProdplanId("1");
		headerDTO1.setProdplanId("7654321");
		headerDTO1.setProductCode("7654321");
		headerDTO2.setProductCode("7654321");
		mBomList.add(headerDTO1);
		mBomList.add(headerDTO2);
		PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(mBomList);
		service.getPage(dto);

	}

	@Test
	public void exportMaterialReturnInfo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-12 00:00:00");
		record.setTimeStart("2023-04-14 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery.setStatus(new BigDecimal(0));
		pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);

		Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries);

		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		record.setSourceTask("1");
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		PmWorkOrderMaterialReturnQueryDTO record1 = new PmWorkOrderMaterialReturnQueryDTO();
		record1.setLineCode("TEST");
		record1.setFileNamePrefix("TEST+");
		try {
			service.exportMaterialReturnInfo(request,response,record1,"52","00286569");
		} catch (Exception e) {
			Assert.assertEquals(MessageId.TIME_CAN_NOT_BE_NULL, e.getMessage());
		}
		record1.setTimeEnd("2023-04-22 00:00:00");
		record1.setTimeStart("2023-04-12 00:00:00");
		try {
			service.exportMaterialReturnInfo(request,response,record1,"52","00286569");
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SPAN_WHITIN_7DAYS, e.getMessage());
		}
	}

	@Test
	public void exportMaterialReturnInfoTwo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");

		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries2 = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery2 = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery2.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery2.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery2.setStatus(new BigDecimal(2));
		pmWorkOrderMaterialReturnQueries2.add(pmWorkOrderMaterialReturnQuery2);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries2);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);

		Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries2);
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void exportMaterialReturnInfoThree() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries2 = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery2 = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery2.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery2.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery2.setStatus(new BigDecimal(1));
		pmWorkOrderMaterialReturnQueries2.add(pmWorkOrderMaterialReturnQuery2);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries2);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);
		Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries2);
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void exportMaterialReturnInfoFour() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class,RedisHelper.class, RedisLock.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(50001);

		PowerMockito.when(RedisHelper.setnx(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(true);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void exportMaterialReturnInfoFive() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(0);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void getInfoCountForExport() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		service.getInfoCountForExport(record);
		record.setSourceTask("1");
		Assert.assertNotNull(service.getInfoCountForExport(record));
	}

	@Test
	public void getCount() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getCount(Mockito.any()))
				.thenReturn(1L);
		service.getCount(record);
		record.setSourceTask("1");
		Assert.assertNotNull(service.getCount(record));
	}

	@Test
	public void getCountTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		record.setSourceTask("1");
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList());
		Assert.assertNotNull(service.getCount(record));
	}

	@Test
	public void setMbomTest() throws Exception {
		List<PmWorkOrderMaterialReturnQuery> list1 = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery dto = new PmWorkOrderMaterialReturnQuery();
		dto.setSourceTask("7654321");
		List<PmWorkOrderMaterialReturnQuery> list = new ArrayList<>();
		List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
		BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
		BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
		headerDTO2.setProdplanId("1234567");
		headerDTO1.setProdplanId("7654321");
		headerDTO1.setProductCode("7654321");
		headerDTO2.setProductCode("7654321");
		mBomList.add(headerDTO1);
		mBomList.add(headerDTO2);
		list.add(dto);
		PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
		Whitebox.invokeMethod(service, "setMBomProductCode", list);
		dto.setSourceTask("76543211");
		Assert.assertEquals("7654321", list.get(0).getMBomProductCode());
		Whitebox.invokeMethod(service, "setMBomProductCode", list1);
	}
}
