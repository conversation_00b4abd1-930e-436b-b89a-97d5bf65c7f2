package com.zte.application.impl;

import com.zte.application.AuxMaterialMountingService;
import com.zte.application.AuxMaterialTracingService;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsWipInfoService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.WipOutputInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.RedisUtil;
import com.zte.domain.model.AuxMaterialMouting;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.infrastructure.feign.CenterFactoryFeignService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.assembler.PsScanHistoryAssembler;
import com.zte.interfaces.assembler.PsWipInfoAssembler;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.EmEqpSpiBoardDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.OfflineExportDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsScanHistoryDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SMTScanDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

@PrepareForTest({MpConstant.class, CrafttechRemoteService.class, FileUtils.class, ProductionDeliveryRemoteService.class, PlanscheduleRemoteService.class,
        CloudDiskHelper.class, RedisHelper.class, RedisLock.class, ConstantInterface.class, RedisUtil.class, CommonUtils.class, BasicsettingRemoteService.class,
        ServiceDataBuilderUtil.class, SXSSFWorkbook.class,PsWipInfoAssembler.class, PsScanHistoryAssembler.class,MesBusinessException.class,ImesExcelUtil.class,HttpRemoteUtil.class})
public class PsScanHistoryServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    PsScanHistoryServiceImpl service;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private WipOutputInfoService wipOutputInfoService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    HttpServletResponse response;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private RedisTemplate<String, Object> redisTemplateExport;
    @Mock   
    private RedisUtil redisUtil;
    @Mock
    private CenterFactoryFeignService centerFactoryFeignService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Mock
    private RedisLock redisLock;
    @Mock
    private AuxMaterialMountingService auxMaterialMountingService;
    @Mock
    private AuxMaterialTracingService auxMaterialTracingService;
    @Mock
    private HttpServletRequest request;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void writeSolderPasteTracing() throws Exception {
        SMTScanDTO smtScanDTO = new SMTScanDTO();
        smtScanDTO.setSn("sn");
        smtScanDTO.setWorkOrderNo("workOrder");
        smtScanDTO.setLineCode("line");
        List<AuxMaterialMouting> auxMaterialMoutingList = new ArrayList<>();
        AuxMaterialMouting auxMaterialMouting = new AuxMaterialMouting();
        auxMaterialMoutingList.add(auxMaterialMouting);
        PowerMockito.when(auxMaterialMountingService.getListByLineAndWorkOrder(any(),any())).thenReturn(auxMaterialMoutingList);
        Whitebox.invokeMethod(service,"writeSolderPasteTracing",smtScanDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void exportScanHistoryEmail() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class, ProductionDeliveryRemoteService.class, PsWipInfoAssembler.class, MesBusinessException.class,
                BasicsettingRemoteService.class, FileUtils.class, MpConstant.class, ServiceDataBuilderUtil.class, PsScanHistoryAssembler.class);
        PsWipInfoDTO dto =new PsWipInfoDTO();
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MpConstant.RESOURCE_SERVICE_NAME);
        try {
            service.exportScanHistoryEmail(response, dto);
        }catch (Exception returnMessage){
            Assert.assertEquals( MpConstant.RESOURCE_SERVICE_NAME, returnMessage.getMessage());
        }

        List<SysLookupValuesDTO> lookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType(new BigDecimal("1004041"));
        sysLookupValuesDTO.setLookupCode(new BigDecimal("1004041005"));
        sysLookupValuesDTO.setLookupMeaning("1");
        sysLookupValuesDTO.setAttribute1("5");
        lookupValuesDTOList.add(sysLookupValuesDTO);
        PsWipInfoDTO param = new PsWipInfoDTO();
        param.setAttribute1("8886523");
        param.setSort("currProcessName");
        param.setFactoryId(new BigDecimal("52"));
        param.setEmail("<EMAIL>");
        param.setEmpNo("00286523");
        param.setAttribute1("8886523");
        param.setRouteId("test123");
        param.setWorkStation("test123");
        param.setCurrProcessCode("test123");
        List<PsWipInfoDTO> psWipInfoDTOS = new ArrayList<>();
        psWipInfoDTOS.add(param);
        List<PsWipInfo> snList = new ArrayList<>();
        PsWipInfo dto1 = new PsWipInfo();
        dto1.setAttribute1("8886523");
        dto1.setRouteId("test123");
        dto1.setWorkStation("test123");
        dto1.setCurrProcessCode("test123");
        snList.add(dto1);
        List<PsScanHistory> psScanHistoryList = new ArrayList<>();
        PsScanHistory psScanHistory = new PsScanHistory();
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.EXPORT_SCAN_HISTORY_MISS_PARAMS);
        try {
            service.exportScanHistoryEmail(response, dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_SCAN_HISTORY_MISS_PARAMS, e.getMessage());
        }

        psScanHistory.setAttribute1("8886523");
        psScanHistory.setRouteId("test123");
        psScanHistory.setWorkStation("test123");
        psScanHistory.setCurrProcessCode("test123");
        psScanHistory.setLineCode("test123");
        psScanHistoryList.add(psScanHistory);

        PowerMockito.when(psWipInfoRepository.getCount(Mockito.anyMap())).thenReturn(1L);

        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        PsScanHistoryDTO psScanHistoryDTO = new PsScanHistoryDTO();
        psScanHistoryDTO.setAttribute1("8886523");
        psScanHistoryDTO.setRouteId("test123");
        psScanHistoryDTO.setWorkStation("test123");
        psScanHistoryDTO.setCurrProcessCode("test123");
        psScanHistoryDTO.setLineCode("test123");
        psScanHistorySort.add(psScanHistoryDTO);
        PageRows<PsWipInfoDTO> psWipInfoDTOPageRows = new PageRows<>();
        psWipInfoDTOPageRows.setRows(psWipInfoDTOS);
        psWipInfoDTOPageRows.setTotal(snList.size());
        List<BSProcessDTO> listProcess = new ArrayList<>();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("test123");
        bsProcessDTO.setProcessName("test123");
        listProcess.add(bsProcessDTO);

        List<CFLine> cfLineListAll = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineListAll.add(cfLine);

        List<PsScanHistory> snHisList = new ArrayList<>();

        ServiceData serviceData = ServiceDataBuilderUtil.success("");
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyMap())).thenReturn(lookupValuesDTOList);
        PowerMockito.when(redisTemplateExport.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisTemplateExport.opsForValue().setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(true);
        PowerMockito.when(centerFactoryFeignService.offlineExportSave(Mockito.any())).thenReturn(serviceData);
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContent(Mockito.any())).thenReturn(psWipInfoDTOPageRows);
        PowerMockito.when(psWipInfoRepository.getCount(Mockito.anyMap())).thenReturn((long) snList.size());
        PowerMockito.when(PsWipInfoAssembler.toPsWipInfoDTOList(Mockito.anyList())).thenReturn(psWipInfoDTOS);
        PowerMockito.when(psScanHistoryRepository.getList(Mockito.anyMap())).thenReturn(psScanHistoryList);
        PowerMockito.when(PsScanHistoryAssembler.toPsScanHistoryDTOList(Mockito.anyList())).thenReturn(psScanHistorySort);
        PowerMockito.when(psWipInfoService.getProcessInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(listProcess);
        PowerMockito.when(psWipInfoService.getLineInfo(Mockito.anyString())).thenReturn(cfLineListAll);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("filepath");
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn("filepath");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("filepath");
        PowerMockito.when(emailUtils.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        PowerMockito.when(redisTemplateExport.delete(Mockito.anyString())).thenReturn(true);
        PowerMockito.when(centerFactoryFeignService.offlineExportUpdate(Mockito.any())).thenReturn(serviceData);

        param.setLpn("test123");
        Assert.assertNull(service.exportScanHistoryEmail(response, param));
    }

    @Test
    public void exportScanHistoryEmailTest() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class, ProductionDeliveryRemoteService.class, PsWipInfoAssembler.class,
                BasicsettingRemoteService.class, FileUtils.class, MpConstant.class, ServiceDataBuilderUtil.class, PsScanHistoryAssembler.class);
        List<SysLookupValuesDTO> lookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType(new BigDecimal("1004041"));
        sysLookupValuesDTO.setLookupCode(new BigDecimal("1004041005"));
        sysLookupValuesDTO.setLookupMeaning("1");
        sysLookupValuesDTO.setAttribute1("5");
        lookupValuesDTOList.add(sysLookupValuesDTO);
        PsWipInfoDTO param = new PsWipInfoDTO();
        param.setAttribute1("8886523");
        param.setSort("currProcessName");
        param.setFactoryId(new BigDecimal("52"));
        param.setEmpNo("00286523");
        param.setAttribute1("8886523");
        param.setRouteId("test123");
        param.setWorkStation("test123");
        param.setCurrProcessCode("test123");
        param.setLpn(null);
        List<PsWipInfoDTO> psWipInfoDTOS = new ArrayList<>();
        psWipInfoDTOS.add(param);
        List<PsWipInfo> snList = new ArrayList<>();
        PsWipInfo dto = new PsWipInfo();
        dto.setAttribute1("8886523");
        dto.setRouteId("test123");
        dto.setWorkStation("test123");
        dto.setCurrProcessCode("test123");
        snList.add(dto);
        List<PsScanHistory> psScanHistoryList = new ArrayList<>();
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistory.setAttribute1("8886523");
        psScanHistory.setRouteId("test123");
        psScanHistory.setWorkStation("test123");
        psScanHistory.setCurrProcessCode("test123");
        psScanHistory.setLineCode("test123");
        psScanHistoryList.add(psScanHistory);

        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        PsScanHistoryDTO psScanHistoryDTO = new PsScanHistoryDTO();
        psScanHistoryDTO.setAttribute1("8886523");
        psScanHistoryDTO.setRouteId("test123");
        psScanHistoryDTO.setWorkStation("test123");
        psScanHistoryDTO.setCurrProcessCode("test123");
        psScanHistoryDTO.setLineCode("test123");
        psScanHistorySort.add(psScanHistoryDTO);
        PageRows<PsWipInfoDTO> psWipInfoDTOPageRows = new PageRows<>();
        psWipInfoDTOPageRows.setRows(psWipInfoDTOS);
        psWipInfoDTOPageRows.setTotal(snList.size());
        List<BSProcessDTO> listProcess = new ArrayList<>();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("test123");
        bsProcessDTO.setProcessName("test123");
        listProcess.add(bsProcessDTO);

        List<CFLine> cfLineListAll = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineListAll.add(cfLine);

        List<PsScanHistory> snHisList = new ArrayList<>();

        ServiceData serviceData = ServiceDataBuilderUtil.success("");
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyMap())).thenReturn(lookupValuesDTOList);
        PowerMockito.when(redisTemplateExport.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisTemplateExport.opsForValue().setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(true);
        PowerMockito.when(centerFactoryFeignService.offlineExportSave(Mockito.any())).thenReturn(serviceData);
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContent(Mockito.any())).thenReturn(null);
        PowerMockito.when(PsWipInfoAssembler.toPsWipInfoDTOList(Mockito.anyList())).thenReturn(psWipInfoDTOS);
        PowerMockito.when(psScanHistoryRepository.getList(Mockito.anyMap())).thenReturn(psScanHistoryList);
        PowerMockito.when(PsScanHistoryAssembler.toPsScanHistoryDTOList(Mockito.anyList())).thenReturn(psScanHistorySort);
        PowerMockito.when(psWipInfoService.getProcessInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(listProcess);
        PowerMockito.when(psWipInfoService.getLineInfo(Mockito.anyString())).thenReturn(cfLineListAll);;
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("filepath");
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn("filepath");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("filepath");
        PowerMockito.when(emailUtils.sendMail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        PowerMockito.when(redisTemplateExport.delete(Mockito.anyString())).thenReturn(true);
        PowerMockito.when(centerFactoryFeignService.offlineExportUpdate(Mockito.any())).thenReturn(serviceData);
        Assert.assertNull(service.exportScanHistoryEmail(response, param));
    }

    @Test
    public void getScanCountByGroup1() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class, PlanscheduleRemoteService.class, MpConstant.class, PsScanHistoryAssembler.class);
        List<BSProcess> queryProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessName("test");
        bsProcess.setProcessCode("test");
        queryProcessList.add(bsProcess);
        PsScanHistory dto = new PsScanHistory();
        dto.setProcessName("test");
        dto.setAttribute1("123");
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.any())).thenReturn(queryProcessList);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(null);
        service.getScanCountByGroup(dto);

        dto.setCnt("1");
        List<PsScanHistory> re = new ArrayList<>();
        re.add(dto);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(re);
        service.getScanCountByGroup(dto);
        dto.setProcessName("");
        try {
            service.getScanCountByGroup(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.PARAMS_MISSING.equals(e.getMessage()));
        }
    }

    @Test
    public void testExportScanHistory() throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class);

        PsWipInfoDTO param = new PsWipInfoDTO();
        param.setAttribute1("8886523");
        param.setLpn(null);

        param.setSort("currProcessName");
        param.setFactoryId(new BigDecimal("52"));
        param.setEmpNo("00286523");
        param.setAttribute1("8886523");
        param.setRouteId("test123");
        param.setWorkStation("test123");
        param.setCurrProcessCode("test123");
        PowerMockito.when(psWipInfoRepository.getCount(Mockito.anyMap())).thenReturn(1L);
        List<PsScanHistory> snHisList = new ArrayList<>();
        PsScanHistory dto = new PsScanHistory();
        dto.setWorkStation("test");
        snHisList.add(dto);

        List<PsWipInfoDTO> psWipInfoDTOS = new ArrayList<>();
        psWipInfoDTOS.add(param);
        List<PsWipInfo> snList = new ArrayList<>();
        PsWipInfo dto10 = new PsWipInfo();
        dto10.setAttribute1("8886523");
        dto10.setRouteId("test123");
        dto10.setWorkStation("test123");
        dto10.setCurrProcessCode("test123");
        snList.add(dto10);

        List<BSProcessDTO> listProcess = new ArrayList<>();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("test123");
        bsProcessDTO.setProcessName("test123");
        listProcess.add(bsProcessDTO);
        PowerMockito.when(psWipInfoService.getProcessInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(listProcess);

        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        PsScanHistoryDTO psScanHistoryDTO = new PsScanHistoryDTO();
        psScanHistoryDTO.setAttribute1("8886523");
        psScanHistoryDTO.setRouteId("test123");
        psScanHistoryDTO.setWorkStation("test123");
        psScanHistoryDTO.setCurrProcessCode("test123");
        psScanHistoryDTO.setLineCode("test123");
        psScanHistorySort.add(psScanHistoryDTO);
        PageRows<PsWipInfoDTO> psWipInfoDTOPageRows = new PageRows<>();
        psWipInfoDTOPageRows.setRows(psWipInfoDTOS);
        psWipInfoDTOPageRows.setTotal(snList.size());

        List<CFLine> cfLineListAll = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineListAll.add(cfLine);
        PowerMockito.when(psWipInfoService.getLineInfo(Mockito.anyString())).thenReturn(cfLineListAll);

        try {
            service.exportScanHistory(request,response,param);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testExportScanHistoryTwo() throws Exception{
        PowerMockito.mockStatic(ImesExcelUtil.class,ConstantInterface.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class,ProductionDeliveryRemoteService.class);

        PsWipInfoDTO param = new PsWipInfoDTO();
        param.setAttribute1("8886523");
        param.setLpn("test123");
        param.setSort("currProcessName");
        param.setFactoryId(new BigDecimal("52"));
        param.setEmpNo("00286523");
        param.setAttribute1("8886523");
        param.setRouteId("test123");
        param.setWorkStation("test123");
        param.setCurrProcessCode("test123");
        List<PsWipInfoDTO> psWipInfoDTOS = new ArrayList<>();
        psWipInfoDTOS.add(param);
        PowerMockito.when(psWipInfoRepository.getCount(Mockito.anyMap())).thenReturn(1L);

        List<PsWipInfo> snList = new ArrayList<>();
        PsWipInfo dto = new PsWipInfo();
        dto.setAttribute1("8886523");
        dto.setRouteId("test123");
        dto.setWorkStation("test123");
        dto.setCurrProcessCode("test123");
        snList.add(dto);
        PageRows<PsWipInfoDTO> psWipInfoDTOPageRows = new PageRows<>();
        psWipInfoDTOPageRows.setRows(psWipInfoDTOS);
        psWipInfoDTOPageRows.setTotal(snList.size());
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContent(Mockito.any())).thenReturn(psWipInfoDTOPageRows);

        List<PsScanHistory> psScanHistoryList = new ArrayList<>();
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistory.setAttribute1("8886523");
        psScanHistory.setRouteId("test123");
        psScanHistory.setWorkStation("test123");
        psScanHistory.setCurrProcessCode("test123");
        psScanHistory.setLineCode("test123");
        psScanHistoryList.add(psScanHistory);
        PowerMockito.when(psScanHistoryRepository.getList(Mockito.anyMap())).thenReturn(psScanHistoryList);

        List<CFLine> cfLineListAll = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test123");
        cfLine.setLineName("test123");
        cfLineListAll.add(cfLine);
        PowerMockito.when(psWipInfoService.getLineInfo(Mockito.anyString())).thenReturn(cfLineListAll);

        try {
            service.exportScanHistory(request,response,param);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testExportScanHistoryThree() throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class,ConstantInterface.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class,ProductionDeliveryRemoteService.class);
        PsWipInfoDTO dto = new PsWipInfoDTO();
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.EXPORT_SCAN_HISTORY_MISS_PARAMS);
        try {
            service.exportScanHistory(request,response,dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_SCAN_HISTORY_MISS_PARAMS, e.getMessage());
        }
        dto.setSn("8886523");
        List<PsWipInfoDTO> psWipInfoDTOS = new ArrayList<>();
        psWipInfoDTOS.add(dto);
        PowerMockito.when(psScanHistoryRepository.getCount(Mockito.anyMap())).thenReturn(1000000L);
        try {
            service.exportScanHistory(request,response,dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_EXPORTED_DATA_CANNOT_BE_LARGER_THAN_100000, e.getMessage());
        }


        List<PsScanHistory> snHisList = new ArrayList<>();
        Map<String, Object> recordLpn = new HashMap<>();
        recordLpn.put("orderField", "getScanHis");
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistory.setWorkStation("23");
        psScanHistory.setSn("123");
        psScanHistory.setFactoryId(new BigDecimal(52));

        snHisList.add(psScanHistory);
        PowerMockito.when(psScanHistoryRepository.getPage(Mockito.anyMap())).thenReturn(snHisList);
        try {
            service.exportScanHistory(request,response,dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.THE_EXPORTED_DATA_CANNOT_BE_LARGER_THAN_100000, e.getMessage());
        }
        List<PsScanHistoryDTO> psScanHistorySort = new ArrayList<>();
        PowerMockito.when(psScanHistoryRepository.getCount(Mockito.anyMap())).thenReturn(0l);
        try {
            service.exportScanHistory(request,response,dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void getScanCountByGroup() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,PlanscheduleRemoteService.class, MpConstant.class, PsScanHistoryAssembler.class);
        List<BSProcess> queryProcessList =new ArrayList<>();
        BSProcess bsProcess =new BSProcess();
        bsProcess.setProcessName("test");
        bsProcess.setProcessCode("test");
        queryProcessList.add(bsProcess);
        PsScanHistory dto = new PsScanHistory();
        dto.setProcessName("test");
        dto.setAttribute1("123");
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.any())).thenReturn(queryProcessList);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(null);
        service.getScanCountByGroup(dto);

        dto.setCnt("1");
        List<PsScanHistory> re =new ArrayList<>();
        re.add(dto);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(re);
        service.getScanCountByGroup(dto);
        dto.setProcessName("");
        try{
            service.getScanCountByGroup(dto);
        }catch(Exception e) {
            Assert.assertTrue(MessageId.PARAMS_MISSING.equals(e.getMessage()));
        }
    }

    @Test
    public void insertScanHisByAuxScan() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,PlanscheduleRemoteService.class, MpConstant.class,BasicsettingRemoteService.class, RedisHelper.class);
        List<BSProcess> queryProcessList =new ArrayList<>();
        BSProcess bsProcess =new BSProcess();
        bsProcess.setProcessName("test");
        bsProcess.setProcessCode("test");
        queryProcessList.add(bsProcess);
        List<PsTask> psTaskList= new ArrayList<>();
        PsTask psTask =new PsTask();
        psTask.setProdplanId("123");
        psTask.setItemName("123");
        psTask.setItemNo("123");
        psTask.setTaskQty(new BigDecimal("123"));
        psTaskList.add(psTask);
        List<SysLookupValuesDTO> listSys  = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("123");
        sysLookupValuesDTO.setAttribute1("123");
        listSys.add(sysLookupValuesDTO);
        PsScanHistory dto = new PsScanHistory();
        dto.setProcessName("test");
        dto.setAttribute1("123");
        dto.setAuxScanAmount(2);
        dto.setLineCode("123");
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.any())).thenReturn(queryProcessList);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(Mockito.any())).thenReturn(psTaskList);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(listSys);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(null);
        PowerMockito.when(psScanHistoryRepository.getLastSnByAuxScan(Mockito.any())).thenReturn(dto);
        PowerMockito.when(psScanHistoryRepository.insertPsScanHistoryBatch(Mockito.any())).thenReturn(1);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        service.insertScanHisByAuxScan(dto);
        dto.setCnt("1");
        List<PsScanHistory> re =new ArrayList<>();
        re.add(dto);
        PowerMockito.when(psScanHistoryRepository.getScanCountByGroup(Mockito.any())).thenReturn(re);
        service.insertScanHisByAuxScan(dto);
        dto.setSn("888877700001");
        service.insertScanHisByAuxScan(dto);
        dto.setLineCode("234");
        try{
            service.insertScanHisByAuxScan(dto);
        }catch(Exception e) {
            Assert.assertTrue(MessageId.GET_AUX_SYS_FAILED.equals(e.getMessage()));
        }
        psTask.setTaskQty(new BigDecimal("1"));
        try{
            service.insertScanHisByAuxScan(dto);
        }catch(Exception e) {
            Assert.assertTrue(MessageId.AUX_QTY_IS_EXCEED.equals(e.getMessage()));
        }
        dto.setProcessName("");
        psTask.setTaskQty(new BigDecimal("12"));
        try{
            service.insertScanHisByAuxScan(dto);
        }catch(Exception e) {
            Assert.assertTrue(MessageId.PARAMS_MISSING.equals(e.getMessage()));
        }
    }

    @Test
    public void withBox() throws Exception{
        PsWipInfoDTO dto = new PsWipInfoDTO();
        int maxNumber = 2;
        OfflineExportDTO offlineExportDTO = new OfflineExportDTO();
        Whitebox.invokeMethod(service,"buildScanHistoryData",
                response,dto,2,offlineExportDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test

    public void insertScanHistoryBySpi() {
        String factoryId = "53";
        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        service.insertScanHistoryBySpi(factoryId, spiList);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.isNull())).thenReturn(null);
        PsEntityPlanBasic psWorkOrder = new PsEntityPlanBasic();
        psWorkOrder.setSourceTask("7777666");
        psWorkOrder.setWorkOrderNo("7777666-SMT-B5301");
        psWorkOrder.setProcessGroup("2$*");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.anyString())).thenReturn(psWorkOrder);

        EmEqpSpiBoardDTO dto = new EmEqpSpiBoardDTO();
        dto.setWorkOrderNo("7777666-SMT-B5301");
        spiList.add(dto);
        service.insertScanHistoryBySpi(factoryId, spiList);

        psWorkOrder.setCraftSection("SMT-B");
        dto.setBarcode("777766600001");
        service.insertScanHistoryBySpi(factoryId, spiList);

        List<String> snAndParentSnList = new ArrayList<>();
        PowerMockito.when(psScanHistoryRepository.getPassSpiScanHistory(Mockito.anySet(), Mockito.anySet(), Mockito.anyString(), Mockito.anyString())).thenReturn(snAndParentSnList);
        dto.setBoardBarcode("P777766600001");
        service.insertScanHistoryBySpi(factoryId, spiList);

        dto.setIsPass(BigDecimal.ZERO);
        service.insertScanHistoryBySpi(factoryId, spiList);

        dto.setIsPass(new BigDecimal("2"));
        service.insertScanHistoryBySpi(factoryId, spiList);

        dto.setIsPass(new BigDecimal("4"));
        service.insertScanHistoryBySpi(factoryId, spiList);

        dto.setIsPass(BigDecimal.ONE);
        service.insertScanHistoryBySpi(factoryId, spiList);


        snAndParentSnList.add("777766600001P777766600001");
        service.insertScanHistoryBySpi(factoryId, spiList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);


    }

    @Test
    public void createScanHistoryEntity() {
        FlowControlInfoDTO record = new FlowControlInfoDTO();
        record.setCurrProcessCode("test123");
        PsEntityPlanBasicDTO ps = new PsEntityPlanBasicDTO();
        ps.setRouteId("test123");
        ps.setItemNo("test123");
        ps.setItemName("test123");
        ps.setWorkOrderNo("test123");
        record.setEntityPlanBasic(ps);
        record.setErrorCode("test123");
        record.setLineCode("test123");
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkStation("test123");
        record.setWipInfo(psWipInfo);
        record.setReScanFlag(true);
        try{
            service.createScanHistoryEntity(record);
        }catch(Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void setMbomTest() throws Exception {
        PsScanHistoryDTO dto = new PsScanHistoryDTO();
        dto.setSn("765432100001");
        List<PsScanHistoryDTO> list = new ArrayList<>();
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234567");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        list.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
        Assert.assertEquals("7654321", list.get(0).getMBomProductCode());
        dto.setSn("765432200001");
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
    }
}

