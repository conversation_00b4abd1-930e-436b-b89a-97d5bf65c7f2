package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.QcRegulation;
import com.zte.domain.model.QcRegulationRepository;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.QcRegulationCheckDTO;
import com.zte.interfaces.dto.QcRegulationDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@PrepareForTest({PlanscheduleRemoteService.class, CrafttechRemoteService.class})
public class QcRegulationServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    QcRegulationServiceImpl service;
    @Mock
    QcRegulationRepository qcRegulationRepository;

    @Test
    public void getList() throws Exception {
        QcRegulationDTO dto = new QcRegulationDTO();
        dto.setDimension("111");
        dto.setRegulationType("222");
        List<QcRegulation> listRes = new ArrayList<>();
        QcRegulation qcRegulation = new QcRegulation();
        listRes.add(qcRegulation);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(listRes);
        List<QcRegulationDTO> list = service.getList(dto);
        Assert.assertEquals(1, list.size());
    }


    @Test
    public void getPage() throws Exception {
        QcRegulationDTO dto = new QcRegulationDTO();
        dto.setDimension("111");
        dto.setRegulationType("222");
        List<QcRegulation> listRes = new ArrayList<>();
        QcRegulation qcRegulation = new QcRegulation();
        listRes.add(qcRegulation);
        PowerMockito.when(qcRegulationRepository.getPageList(Mockito.any())).thenReturn(listRes);
        Page<QcRegulationDTO> page = service.getPage(dto);
        Assert.assertEquals(1, page.getRows().size());
    }

    @Test
    public void submit() {
        QcRegulationDTO dto = new QcRegulationDTO();
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null);
        PowerMockito.when(qcRegulationRepository.insertSelective(Mockito.any())).thenReturn(1);
        int submit = service.submit(dto);
        Assert.assertEquals(1, submit);

        List<QcRegulation> listRes = new ArrayList<>();
        QcRegulation qcRegulation = new QcRegulation();
        listRes.add(qcRegulation);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(listRes);
        PowerMockito.when(qcRegulationRepository.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        int submit1 = service.submit(dto);
        Assert.assertEquals(1, submit1);
        try {
            listRes.clear();
            listRes.add(null);
            PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(listRes);
            service.submit(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_MSGID, e.getExMsgId());
        }
    }

    @Test
    public void deleteById() {
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null);
        int deleteById = service.deleteById("111", "222");
        Assert.assertEquals(0, deleteById);

        List<QcRegulation> listRes = new ArrayList<>();
        QcRegulation qcRegulation = new QcRegulation();
        listRes.add(qcRegulation);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(listRes);
        PowerMockito.when(qcRegulationRepository.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        int deleteById1 = service.deleteById("111", "222");
        Assert.assertEquals(1, deleteById1);
    }

    @Test
    public void whetherNeedQc() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);

        QcRegulationCheckDTO dto = new QcRegulationCheckDTO();

        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.LINE_IS_NOT_NULL, e.getExMsgId());
        }

        dto.setLineCode("LineCode");
        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PRODPLANID_ID_NULL, e.getExMsgId());
        }

        dto.setProdplanId("ProdplanId");
        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_IS_NOT_NULL, e.getExMsgId());
        }

        dto.setProcessCode("ProcessCode");

        List<QcRegulation> listRes = new ArrayList<>();
        QcRegulation qcRegulation = new QcRegulation();
        listRes.add(qcRegulation);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(listRes);
        String whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);


        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.anyMap())).thenReturn(null);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, listRes);
        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.TASK_NOT_HAVE_DETAILS, e.getExMsgId());
        }

        List<PsTask> psTaskList = new LinkedList<>();
        PsTask t1 = new PsTask();
        t1.setPartsPlanno("45");
        t1.setItemNo("ItemNo");
        psTaskList.add(t1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.anyMap())).thenReturn(psTaskList);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        // 需检
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, null);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        qcRegulation.setDimensionValue("SMT");
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(null);
        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_DETAILS_NOT_FOUND, e.getExMsgId());
        }

        List<BSProcess> bsProcesses = new ArrayList<>();
        BSProcess bsProcess1 = new BSProcess();
        BSProcess bsProcess2 = new BSProcess();
        BSProcess bsProcess3 = new BSProcess();
        BSProcess bsProcess4 = new BSProcess();
        BSProcess bsProcess5 = new BSProcess();
        BSProcess bsProcess6 = new BSProcess();
        bsProcesses.add(bsProcess1);
        bsProcesses.add(bsProcess2);
        bsProcesses.add(bsProcess3);
        bsProcesses.add(bsProcess4);
        bsProcesses.add(bsProcess5);
        bsProcesses.add(bsProcess6);
        bsProcess1.setProcessCode("1");
        bsProcess1.setCraftSection("SMT-A");
        bsProcess2.setProcessCode("2");
        bsProcess2.setCraftSection("SMT-B");
        bsProcess3.setProcessCode("P2016");
        bsProcess3.setCraftSection("DIP");
        bsProcess4.setProcessCode("P2017");
        bsProcess4.setCraftSection(null);
        bsProcess5.setProcessCode("N");
        bsProcess5.setCraftSection("入库");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        try {
            service.whetherNeedQc(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIP_PROCESS_NOT_IN_ROUTE, e.getExMsgId());
        }

        dto.setProcessCode("1");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        dto.setProcessCode("2");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("Y", whetherNeedQc);

        dto.setProcessCode("N");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        dto.setProcessCode("P2016");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("N", whetherNeedQc);

        dto.setProcessCode("P2016");
        QcRegulation qcRegulation1 = new QcRegulation();
        listRes.add(qcRegulation1);
        qcRegulation1.setDimensionValue("入库");
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString())).thenReturn(bsProcesses);
        PowerMockito.when(qcRegulationRepository.getList(Mockito.any())).thenReturn(null, null, null, listRes);
        whetherNeedQc = service.whetherNeedQc(dto);
        Assert.assertEquals("Y", whetherNeedQc);
    }

}