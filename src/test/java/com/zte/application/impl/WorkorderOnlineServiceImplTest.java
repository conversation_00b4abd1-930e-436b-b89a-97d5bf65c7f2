package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ThreadLocalUtil;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.ProdResPrepDetail;
import com.zte.domain.model.ProdResPrepDetailRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;

@PrepareForTest({MicroServiceRestUtil.class, ThreadLocalUtil.class,BasicsettingRemoteService.class, CrafttechRemoteService.class})
public class WorkorderOnlineServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    WorkorderOnlineServiceImpl service;
    @Mock
    ProdResPrepHeadServiceImpl prodResPrepHeadService;
    @Mock
    ProdResPrepDetailRepository prodResPrepDetailRepository;

    @Test
    public void setActualStartDate() {
        service.setActualStartDate(new PsWorkOrderDTO(), new Date());
        service.setActualStartDate(new PsWorkOrderDTO(){{setWorkOrderStatus((Constant.IS_SUBMITTED));}}, new Date());
        service.setActualStartDate(new PsWorkOrderDTO(){{setWorkOrderStatus((Constant.IS_SUBMITTED));setActualStartDate(new Date());}}, new Date());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void updateTaskGetDate() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ThreadLocalUtil.class);
        try {
            PowerMockito.when(ThreadLocalUtil.get("childTasks")).thenReturn(Lists.newArrayList("2"));
            PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                    .thenReturn(JSON.toJSONString(new ServiceData(){{setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));}}));
            service.updateTaskGetDate("1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_ERROR, e.getMessage());
        }
        try {
            PowerMockito.when(ThreadLocalUtil.get("childTasks")).thenReturn(Lists.newArrayList("2"));
            PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                    .thenReturn(JSON.toJSONString(new ServiceData()));
            service.updateTaskGetDate("1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_ERROR, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkProcessGroup() throws Exception {
    	//增量UT
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        Assert.assertEquals(false, service.checkProcessGroup(null,null));

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        CFLine lineInfo = new CFLine();
        Assert.assertEquals(false, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        lineInfo.setFeedFlag("N");
        psWorkOrderDTO.setProcessGroup("1$2");
        Assert.assertEquals(true, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        List<BSProcess> bsProcesses = new ArrayList<>();
        lineInfo.setFeedFlag("Y");
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(anyList())).thenReturn(bsProcesses);
        Assert.assertEquals(false, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("3");
        bsProcess.setFeedFlag("N");
        bsProcesses.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.anyList())).thenReturn(bsProcesses);
        Assert.assertEquals(false, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        BSProcess bsProcess1 = new BSProcess();
        bsProcess1.setProcessCode("1");
        bsProcess1.setFeedFlag("N");
        bsProcesses.add(bsProcess1);
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.anyList())).thenReturn(bsProcesses);
        Assert.assertEquals(false, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        BSProcess bsProcess2 = new BSProcess();
        bsProcess2.setProcessCode("2");
        bsProcess2.setFeedFlag("Y");
        bsProcesses.add(bsProcess2);
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.anyList())).thenReturn(bsProcesses);
        Assert.assertEquals(false, service.checkProcessGroup(psWorkOrderDTO,lineInfo));

        bsProcess2.setFeedFlag("N");
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.anyList())).thenReturn(bsProcesses);
        Assert.assertEquals(true, service.checkProcessGroup(psWorkOrderDTO,lineInfo));
    }

    @Test
    public void checkProdPrepStatus() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(null);
        service.checkProdPrepStatus(new PsWorkOrderDTO());
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(new SysLookupTypesDTO() {{
            setLookupCode(new BigDecimal("286520001"));
            setLookupMeaning("N");
        }});
        service.checkProdPrepStatus(new PsWorkOrderDTO());
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(new SysLookupTypesDTO() {{
            setLookupCode(new BigDecimal("286520001"));
            setLookupMeaning("Y");
        }});
        List<SysLookupValuesDTO> lineSwitchList =new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        lineSwitchList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes((String) Mockito.any())).thenReturn(lineSwitchList);
        PsWorkOrderDTO ps = new PsWorkOrderDTO();
        ps.setLineCode("SMT-NJ001");
        ps.setWorkOrderNo("TEST_123");
        ps.setUserId("TEST_123");
        try{
            service.checkProdPrepStatus(ps);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysLookupValuesDTO.setLookupMeaning("SMT-NJ001");
        sysLookupValuesDTO.setAttribute1("SMT-NJ001");
        try{
            service.checkProdPrepStatus(ps);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysLookupValuesDTO.setAttribute1("SMT-NJ002");
        try{
            service.checkProdPrepStatus(ps);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(prodResPrepHeadService.regularCalProdPre(Mockito.any(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        try{
            service.checkProdPrepStatus(ps);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<String> workOrderNoList = new ArrayList<>();
        workOrderNoList.add("TEST_123");
        PowerMockito.when(prodResPrepHeadService.regularCalProdPre(Mockito.any(),Mockito.anyString(),Mockito.anyBoolean())).thenReturn(workOrderNoList);
        List<ProdResPrepDetail> detailList = new ArrayList<>();
        ProdResPrepDetail prepDetail = new ProdResPrepDetail();
        prepDetail.setPrepareStatus("2");
        prepDetail.setPrepareItemName("2");
        detailList.add(prepDetail);
        PowerMockito.when(prodResPrepDetailRepository.selectProdPreDetailList(Mockito.any())).thenReturn(detailList);
        try{
            service.checkProdPrepStatus(ps);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}