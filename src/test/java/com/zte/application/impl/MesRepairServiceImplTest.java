package com.zte.application.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.zte.application.impl.MesRepairServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PmRepairMesErrorLog;
import com.zte.domain.model.PmRepairMesErrorLogRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.RepairRestoreDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.*;

@PrepareForTest({RedisLock.class, MesRepairServiceImpl.class, DatawbRemoteService.class, BasicsettingRemoteService.class})
public class MesRepairServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private MesRepairServiceImpl service;

    @Mock
    private PmRepairMesErrorLogRepository pmRepairMesErrorLogRepository;

    @Mock
    private RedisLock redisLock;

    @Test
    public void repairRestore() throws Exception {

        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();
        // 测试1
        try {
            service.repairRestore(repairRestoreDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MES_REPAIR_RESTORE_ERROR, e.getMessage());
        }

        // 测试2
        SysLookupTypesDTO sysLookupTypesDTO = null;
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString()))
                .thenReturn(sysLookupTypesDTO);

        RepairRestoreDTO repairRestoreDTO = new RepairRestoreDTO();
        repairRestoreDTOS.add(repairRestoreDTO);
        repairRestoreDTO.setSn("1111");
        repairRestoreDTO.setResult("2222");
        try {
            service.repairRestore(repairRestoreDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MES_REPAIR_RESTORE_ERROR, e.getMessage());
        }

        //测试3
        sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString()))
                .thenReturn(sysLookupTypesDTO);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(DatawbRemoteService.mesRepair(anyList())).thenReturn(null);
        try {
            service.repairRestore(repairRestoreDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MES_REPAIR_RESTORE_ERROR, e.getMessage());
        }

        //测试4
        JsonNode jsonNode = new ArrayNode(new JsonNodeFactory(true));
        PowerMockito.when(DatawbRemoteService.mesRepair(anyList())).thenReturn(jsonNode);
        try {
            service.repairRestore(repairRestoreDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MES_REPAIR_RESTORE_ERROR, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void logError() throws Exception {

        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();
        service.logError(repairRestoreDTOS);
        // 测试1

        // 测试2
        PowerMockito.when(pmRepairMesErrorLogRepository.saveLog(any()))
                .thenReturn(1);

        RepairRestoreDTO repairRestoreDTO = new RepairRestoreDTO();
        repairRestoreDTOS.add(repairRestoreDTO);
        repairRestoreDTO.setSn("1111");
        repairRestoreDTO.setResult("2222");
        service.logError(repairRestoreDTOS);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void retry() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();
        // 测试1
        service.retry();

        // 测试2
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("3");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString()))
                .thenReturn(sysLookupTypesDTO);

        List<PmRepairMesErrorLog> pmRepairMesErrorLogs = new ArrayList<>();
        PmRepairMesErrorLog pmRepairMesErrorLog = new PmRepairMesErrorLog();
        pmRepairMesErrorLogs.add(pmRepairMesErrorLog);
        pmRepairMesErrorLog.setId("11");
        pmRepairMesErrorLog.setStatus("0");
        pmRepairMesErrorLog.setRetryCount(0);
        pmRepairMesErrorLog.setSn("1111");
        pmRepairMesErrorLog.setDetail("2222");
        PowerMockito.when(pmRepairMesErrorLogRepository.getRetryList(anyInt()))
                .thenReturn(pmRepairMesErrorLogs);
        PowerMockito.when(pmRepairMesErrorLogRepository.update(any()))
                .thenReturn(1);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(DatawbRemoteService.mesRepair(anyList())).thenReturn(null);
        service.retry();
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void retryDo() throws Exception {
        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();
        // 测试1
        service.retryDo();

        // 测试2
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("3");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString()))
                .thenReturn(sysLookupTypesDTO);

        List<PmRepairMesErrorLog> pmRepairMesErrorLogs = new ArrayList<>();
        PmRepairMesErrorLog pmRepairMesErrorLog = new PmRepairMesErrorLog();
        pmRepairMesErrorLogs.add(pmRepairMesErrorLog);
        pmRepairMesErrorLog.setId("11");
        pmRepairMesErrorLog.setStatus("0");
        pmRepairMesErrorLog.setRetryCount(0);
        pmRepairMesErrorLog.setSn("1111");
        pmRepairMesErrorLog.setDetail("2222");
        PowerMockito.when(pmRepairMesErrorLogRepository.getRetryList(anyInt()))
                .thenReturn(pmRepairMesErrorLogs);
        PowerMockito.when(pmRepairMesErrorLogRepository.update(any()))
                .thenReturn(1);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(DatawbRemoteService.mesRepair(anyList())).thenReturn(null);
        service.retryDo();
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
