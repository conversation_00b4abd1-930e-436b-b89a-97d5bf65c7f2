package com.zte.application.impl;

import com.zte.application.PsCommonScanService;
import com.zte.application.PsWipInfoService;
import com.zte.application.RProcessRequestDealTService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/23 17:02
 */
public class ProcessTransferServiceImplTest {
    @Mock
    PsWipInfoRepository psWipInfoRepository;
    @Mock
    PsWipInfoService psWipInfoService;
    @Mock
    RProcessRequestDealTService rProcessRequestDealTService;
    @Mock
    PsCommonScanService psCommonScanService;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;
    @InjectMocks
    ProcessTransferServiceImpl processTransferServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void processCSFactory() throws Exception {
        List<ContainerContentInfoDTO > containers = new ArrayList<>();

        when(centerfactoryRemoteService.isHomeTerminal(anyString())).thenReturn(true);
        Whitebox.invokeMethod(processTransferServiceImpl, "processCSFactory", new PsWipInfo(),containers,
                new ProcessTransferModel(){{setWorkOrder("test");setCraftSection(MpConstant.CRAFT_SECTION_IN_WH);}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
