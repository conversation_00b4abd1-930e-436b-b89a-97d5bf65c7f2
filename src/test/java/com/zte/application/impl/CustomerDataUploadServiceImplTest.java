package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.WipScanHistoryService;
import com.zte.common.ConstantInterface;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.WipScanHistory;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyInt;

@PrepareForTest({ConstantInterface.class, DatawbRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class})
public class CustomerDataUploadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private CustomerDataUploadServiceImpl service;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Mock
    private PsWipInfoService wipInfoService;
    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    private WipScanHistoryService wipScanHistoryService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Test
    public void testCustomerTestDataFeedback() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,ConstantInterface.class,CrafttechRemoteService.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
        customerDataUploadParamDTO.setSchedulingType("0");
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setSn("77788890002");
        customerDataLogDTOList.add(customerDataLogDTO);
        customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(wipInfoService.getListByBatchSn(any())).thenReturn(psWipInfoList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        psWipInfo.setAttribute1("7778889");
        psWipInfo.setSn("77788890002");
        psWipInfo.setItemNo("123456789012");
        psWipInfoList.add(psWipInfo);
        PsWipInfo psWipInfo1 = new PsWipInfo();

        psWipInfo1.setAttribute1("7778889");
        psWipInfo1.setSn("77788890003");
        psWipInfo1.setItemNo("123456789013");
        psWipInfoList.add(psWipInfo1);
        PowerMockito.when(wipInfoService.getListByBatchSn(any())).thenReturn(psWipInfoList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<PsTaskDTO> psTaskList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("7778889");
        psTaskList.add(psTaskDTO);
        PowerMockito.when(centerfactoryRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(), any())).thenReturn(customerItemsDTOList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        customerItemsDTOList =new ArrayList<>();
        customerItemsDTO.setZteCode("123456789012");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(), any())).thenReturn(customerItemsDTOList);
        try {
            service.customerTestDataFeedback(customerDataUploadParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Test");
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<BSProcess> bsProcessList = new ArrayList<>();
        List<CtBasicRouteDTO> ctBasicRouteList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setCraftSection("ICT");
        bsProcess.setProcessCode("ICT");
        bsProcess.setProcessName("测试");
        bsProcessList.add(bsProcess);
        CtBasicRouteDTO ctBasicRouteDTO = new CtBasicRouteDTO();
        ctBasicRouteDTO.setItemNo("123456789012");
        ctBasicRouteDTO.setRouteDetail("贴片A面 -> 贴片B面 -> 短插 -> 测试 -> 入库");
        ctBasicRouteDTO.setRoutePathCode("2$1$T$+$N");
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("123456789013");
        ctBasicRouteDTO1.setRouteDetail("贴片A面 -> 贴片B面 -> 入库");
        ctBasicRouteDTO1.setRoutePathCode("2$1$N");
        ctBasicRouteList.add(ctBasicRouteDTO);
        ctBasicRouteList.add(ctBasicRouteDTO1);
        PowerMockito.when(CrafttechRemoteService.getBsProcessListByTestCraft(any())).thenReturn(bsProcessList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);
        PowerMockito.when(CrafttechRemoteService.getBsProcessListByTestCraft(any())).thenReturn(null);
        PowerMockito.when(CrafttechRemoteService.getRouteAndSeqByItemNos(any())).thenReturn(ctBasicRouteList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);
        PowerMockito.when(CrafttechRemoteService.getBsProcessListByTestCraft(any())).thenReturn(bsProcessList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);


        List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("77788890002");
        wipScanHistory.setCraftSection("ICT");
        wipScanHistoryList.add(wipScanHistory);
        wipScanHistory.setSn("77788890003");
        wipScanHistory.setCraftSection("ICT");
        wipScanHistoryList.add(wipScanHistory);
        PowerMockito.when(wipScanHistoryService.getWipScanHistoryForTestCraftSection(any())).thenReturn(wipScanHistoryList);
        try {
            service.customerTestDataFeedback(customerDataUploadParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_MDS_TOKEN_ERROR, e.getMessage());
        }
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        wipScanHistory.setSn("77788890002");
        wipScanHistoryList.add(wipScanHistory);
        WipScanHistory wipScanHistory1 = new WipScanHistory();
        wipScanHistory1.setSn("77788890005");
        wipScanHistory.setCraftSection("ICT");
        List<WipScanHistory> repairDataList = new ArrayList<>();
        repairDataList.add(wipScanHistory);
        repairDataList.add(wipScanHistory1);
        repairDataList.add(new WipScanHistory());
        PowerMockito.when(pmRepairRcvDetailRepository.getTestRepairDataForPush(any(),any())).thenReturn(repairDataList);
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        PowerMockito.when(centerfactoryRemoteService.getPushErrorData(any())).thenReturn(customerDataLogDTOList);
        try {
            service.customerTestDataFeedback(customerDataUploadParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO1);
        PowerMockito.when(centerfactoryRemoteService.getPushedTestProcessSnScanData(any())).thenReturn(customerDataLogDTOList);

        service.customerTestDataFeedback(customerDataUploadParamDTO);

        sysLookupTypesDTO1.setLookupMeaning("2023-05-01 00:00:00");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_3412, Constant.LOOKUP_TYPE_3412001)).thenReturn(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.STR_10000, Constant.STR_10000001)).thenReturn(null);
        service.customerTestDataFeedback(customerDataUploadParamDTO);
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.STR_10000, Constant.STR_10000001)).thenReturn(sysLookupTypesDTO2);
        service.customerTestDataFeedback(customerDataUploadParamDTO);
        PowerMockito.when(centerfactoryRemoteService.getPsTaskByItemNoList(any())).thenReturn(psTaskList);
        PowerMockito.when(wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(anyList(),any(), any(), anyInt())).thenReturn(new ArrayList<>());
        service.customerTestDataFeedback(customerDataUploadParamDTO);

        List<String> tempProdPlanIdList = new ArrayList<>();
        tempProdPlanIdList.add("7778889");
        Date lastExecDate = StringUtils.isEmpty("2023-05-01 00:00:00") ? null : DateUtils.addHours(DateUtils.parseDate("2023-05-01 00:00:00", Constant.DATE_TIME_FORMATE_FULL), NumConstant.NUM_NEGATIVE_ONE);
        Date maxDate = new Date();
        PowerMockito.when(wipInfoService.getMaxSnDate(any(), anyList())).thenReturn(maxDate);
        PowerMockito.when(wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(tempProdPlanIdList, lastExecDate, maxDate,1)).thenReturn(psWipInfoList);
        PowerMockito.when(wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(tempProdPlanIdList, lastExecDate, maxDate,2)).thenReturn(new ArrayList<>());
        customerDataUploadParamDTO.setSchedulingType("1");
        service.customerTestDataFeedback(customerDataUploadParamDTO);
        List<WipScanHistory> repairDataList1 = new ArrayList<>();
        WipScanHistory wipScanHistory2 = new WipScanHistory();
        wipScanHistory2.setSn("77788890009");
        wipScanHistory2.setCraftSection("repair123FT");
        repairDataList1.add(wipScanHistory2);
        repairDataList1.add(wipScanHistory);
        PowerMockito.when(pmRepairRcvDetailRepository.getTestRepairDataForPush(any(),any())).thenReturn(repairDataList1);
        PowerMockito.when(mdsRemoteService.getStationLogFromMDS(any(),any())).thenReturn("");
        sysLookupTypesDTO2.setLookupMeaning("100");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.STR_10000, Constant.STR_10000001)).thenReturn(sysLookupTypesDTO2);

        service.customerTestDataFeedback(customerDataUploadParamDTO);
        Whitebox.invokeMethod(service, "transferPushList", new ArrayList<>(), repairDataList, customerDataUploadParamDTO);
    }
}