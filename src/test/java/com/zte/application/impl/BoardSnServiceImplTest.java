package com.zte.application.impl;

import com.zte.application.PrintSceneService;
import com.zte.application.PsWorkOrderSnAssignService;
import com.zte.application.impl.printscene.FirstBigSecondSmallStrategy;
import com.zte.application.impl.printscene.SceneFactory;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderSnAssign;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PmCarvingFeedbackDTO;
import com.zte.interfaces.dto.PmGenerateSnDTO;
import com.zte.interfaces.dto.PrintSceneConfigDTO;
import com.zte.interfaces.dto.PsTaskExtraDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.mockito.Matchers.*;

@PrepareForTest({RedisLock.class, RedisHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class})
public class BoardSnServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    BoardSnServiceImpl service;
    @Mock
    private PsWorkOrderSnAssignService psWorkOrderSnAssignService;
    @Mock
    private PrintSceneService printSceneService;
    @Mock
    private SceneFactory sceneFactory;
    @Mock
    private RedisLock redisLock;
    @Mock
    private FirstBigSecondSmallStrategy firstBigSecondSmallStrategy;

    @Test
    public void generateSn() throws Exception {
        PowerMockito.mockStatic(RedisLock.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);

        PmGenerateSnDTO dto = new PmGenerateSnDTO();
        test(dto);
        dto.setProdplanId("ProdplanId");
        test(dto);
        dto.setQty(-1);
        test(dto);
        dto.setQty(1);
        test(dto);
        dto.setSnType("-1");
        test(dto);
        dto.setSnType("1");
        test(dto);
        dto.setLine("line");
        test(dto);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        test(dto);
        List<PsTaskExtraDTO> printScenes = new ArrayList<>();
        PsTaskExtraDTO psTaskExtraDTO = new PsTaskExtraDTO();
        psTaskExtraDTO.setExtraValue("1");
        printScenes.add(psTaskExtraDTO);
        List<PsTask> psTaskList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList);

        PowerMockito.when(sceneFactory.getSceneStrategy("1")).thenReturn(firstBigSecondSmallStrategy);
        PowerMockito.when(firstBigSecondSmallStrategy.generateSn(dto)).thenReturn(Arrays.asList("111"));
        test(dto);

        PsTask psTask = new PsTask();
        psTask.setItemNo("");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList);
        test(dto);

        psTask.setItemNo("ItemNo11");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList);
        test(dto);

        Map<String, String> verNoMap = null;
        PowerMockito.when(BasicsettingRemoteService.getVerNoInfoByItemNos(anySet())).thenReturn(verNoMap);
        test(dto);

        verNoMap = new HashMap<>();
        PowerMockito.when(BasicsettingRemoteService.getVerNoInfoByItemNos(anySet())).thenReturn(verNoMap);
        test(dto);

        verNoMap.put("ItemNo11","210100_A");

        Assert.assertThrows(NullPointerException.class, () -> test(dto));

        PageRows<PrintSceneConfigDTO> printScenePage = new PageRows<>();
        List<PrintSceneConfigDTO> list = new ArrayList<>();
        printScenePage.setRows(list);
        PowerMockito.when(printSceneService.getPrintScenePage(any())).thenReturn(printScenePage);
        test(dto);
        PrintSceneConfigDTO printSceneConfigDTO = new PrintSceneConfigDTO();
        printSceneConfigDTO.setPrintScene("1");
        list.add(printSceneConfigDTO);
        PowerMockito.when(printSceneService.getPrintScenePage(any())).thenReturn(printScenePage);
        Assert.assertNotNull(service.generateSn(dto));
    }

    private void test(PmGenerateSnDTO dto) {
        try {
            service.generateSn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void carvingFeedback() throws Exception {
        PmCarvingFeedbackDTO dto = new PmCarvingFeedbackDTO();
        try {
            service.carvingFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setSn("202008061101");
        try {
            service.carvingFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setStatus("OK");
        try {
            service.carvingFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_FOUND_IN_SN_ASSIGN_TABLE, e.getMessage());
        }

        List<PsWorkOrderSnAssign> psWorkOrderSnAssigns = new ArrayList<>();
        PowerMockito.when(psWorkOrderSnAssignService.selectBysn("202008061101")).thenReturn(psWorkOrderSnAssigns);
        PowerMockito.when(psWorkOrderSnAssignService.updatePsWorkOrderSnAssignById(any())).thenReturn(1);
        try {
            service.carvingFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_FOUND_IN_SN_ASSIGN_TABLE, e.getMessage());
        }

        PsWorkOrderSnAssign psWorkOrderSnAssign = new PsWorkOrderSnAssign();
        psWorkOrderSnAssigns.add(psWorkOrderSnAssign);
        service.carvingFeedback(dto);

        dto.setSn("P202008061101");
        psWorkOrderSnAssign.setRemark("P");
        PowerMockito.when(psWorkOrderSnAssignService.selectByParentSn("P202008061101")).thenReturn(psWorkOrderSnAssigns);
        Assert.assertEquals(1,service.carvingFeedback(dto));
    }
}
