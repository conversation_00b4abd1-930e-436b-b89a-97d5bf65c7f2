package com.zte.application.impl;

import com.zte.application.BarcodeCenterService;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.CommonScanDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * ClassName: ProcessControlServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/12/12 下午4:33
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CrafttechRemoteService.class,BasicsettingRemoteService.class, BarcodeCenterRemoteService.class, MESHttpHelper.class,
        PlanscheduleRemoteService.class,ObtainRemoteServiceDataUtil.class,ProductionmgmtRemoteService.class,HttpRemoteService.class})
public class ProcessControlServiceImplTest {
    @InjectMocks
    ProcessControlServiceImpl service;

    @Mock
    PlanscheduleRemoteService planscheduleRemoteService;

    @Mock
    PsCommonScanServiceImpl psCommonScanService;

    @Mock
    PsWipInfoRepository psWipInfoRepository;

    @Mock
    FactoryConfig factoryConfig;
    @Mock
    BarcodeCenterService barcodeCenterService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    FlowControlCommonService flowControlCommonService;

    @Mock
    PsWipInfoServiceImpl psWipInfoService;

    @Mock
    PsScanHistoryServiceImpl psScanHistoryServiceImpl;

    @Mock
    WipScanHisExtraRepository wipScanHisExtraRepository;


    @Test
    public void checkAndGetProcessInfoTest() {
        String routeId = "";
        String scanProcessCode = "testScanProcess";
        String isProcessControl = "Y";
        PsWipInfo wipInfo = new PsWipInfo();
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ROUTE_ID_IS_NULL.equals(e.getExMsgId()));
        }
        routeId = "testRoutId";
        scanProcessCode = "";
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_IS_NULL.equals(e.getExMsgId()));
        }
        scanProcessCode = "1";
        wipInfo = null;
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getProcessListByRouteId(Mockito.anyString())).thenReturn(routeDetailList);
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }

        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setCurrProcess("2");
        ctRouteDetailDTO1.setNextProcess("1");
        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        ctRouteDetailDTO2.setCurrProcess("1");
        ctRouteDetailDTO2.setNextProcess("2");
        PowerMockito.when(CrafttechRemoteService.getProcessListByRouteId(Mockito.anyString())).thenReturn(routeDetailList);
        routeDetailList.add(ctRouteDetailDTO1);
        routeDetailList.add(ctRouteDetailDTO2);
        PowerMockito.when(CrafttechRemoteService.getProcessListByRouteId(Mockito.anyString())).thenReturn(routeDetailList);
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        ctRouteDetailDTO1.setCurrProcess("0");
        ctRouteDetailDTO1.setNextProcess(null);
        ctRouteDetailDTO1.setDoOrNotFlag("Y");
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        ctRouteDetailDTO1.setNextProcess("1");
        ctRouteDetailDTO1.setDoOrNotFlag("N");
        CtRouteDetailDTO ctRouteDetailDTO = service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        Assert.assertTrue(ctRouteDetailDTO.getNextProcess().equals("1"));
        wipInfo = new PsWipInfo();
        ctRouteDetailDTO1.setDoOrNotFlag("Y");
        wipInfo.setCurrProcessCode("1");
        scanProcessCode = "2";
        wipInfo.setLastProcess("N");
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_THE_SAME_IN_THE_WIP.equals(e.getExMsgId()));
        }
        wipInfo.setLastProcess("Y");
        scanProcessCode = "1";
        isProcessControl = "Y";
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        isProcessControl = "N";
        try {
            service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        scanProcessCode = "2";
        CtRouteDetailDTO ctRouteDetailDTO3 = service.checkAndGetProcessInfo(routeId, scanProcessCode, isProcessControl, wipInfo);
        Assert.assertTrue(ctRouteDetailDTO3.getNextProcess().equals("2"));
    }

    @Test
    public void checkAndGetWorkStationInfoTest() throws Exception {
        String lineCode = "";
        Pair<String, String> processCode = Pair.of("1", "1");
        String scanWorkStation = "testScanProcess";
        String isProcessControl = "Y";
        PsWipInfo wipInfo = null;
        CtRouteDetailDTO ctRouteDetailDTO = service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        Assert.assertTrue(ctRouteDetailDTO != null);

        isProcessControl = "N";
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_SETTING_LINE_CODE_IS_NULL.equals(e.getExMsgId()));
        }
        lineCode = "testLineCode";
        processCode = null;
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_IS_NULL.equals(e.getExMsgId()));
        }
         processCode = Pair.of("", "1");
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_IS_NULL.equals(e.getExMsgId()));
        }
        processCode = Pair.of("1", "1");
        scanWorkStation = "";
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_STATION_IS_NULL.equals(e.getExMsgId()));
        }
        scanWorkStation = "10";

        wipInfo = null;
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.listWorkStationByLineAndProcess(Mockito.anyString(), Mockito.anyString())).thenReturn(routeDetailList);
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_STATION_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_STATION_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setCurrProcess("20");
        ctRouteDetailDTO1.setNextProcess("10");
        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        ctRouteDetailDTO2.setCurrProcess("10");
        ctRouteDetailDTO2.setNextProcess("11");
        routeDetailList.add(ctRouteDetailDTO1);
        routeDetailList.add(ctRouteDetailDTO2);
        PowerMockito.when(CrafttechRemoteService.listWorkStationByLineAndProcess(Mockito.anyString(), Mockito.anyString())).thenReturn(routeDetailList);
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_STATION_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        ctRouteDetailDTO1.setCurrProcess("0");
        ctRouteDetailDTO1.setNextProcess(null);
        ctRouteDetailDTO1.setIsUnnecessary("Y");
        try {
            service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_STATION_NOT_IN_NEXT_CAN_SCAN_LIST.equals(e.getExMsgId()));
        }
        ctRouteDetailDTO1.setNextProcess("10");
        ctRouteDetailDTO1.setIsUnnecessary("N");
        CtRouteDetailDTO ctRouteDetailDTO3 = service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        Assert.assertTrue(ctRouteDetailDTO3.getNextProcess().equals("10"));
        wipInfo = new PsWipInfo();
        ctRouteDetailDTO1.setIsUnnecessary("Y");
        wipInfo.setCurrProcessCode("2");
        scanWorkStation = "10";
        CtRouteDetailDTO ctRouteDetailDTO4 = service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        Assert.assertTrue(ctRouteDetailDTO4.getNextProcess().equals("10"));

        wipInfo.setCurrProcessCode("1");
        wipInfo.setWorkStation("10");
        scanWorkStation = "11";
        CtRouteDetailDTO ctRouteDetailDTO5 = service.checkAndGetWorkStationInfo(lineCode, processCode, scanWorkStation, wipInfo, isProcessControl);
        Assert.assertTrue(ctRouteDetailDTO5.getNextProcess().equals("11"));
    }

    @Test
    public void checkAndGetWorkOrderInfoTest() throws Exception {
        String lineCode = "";
        String scanProcessCode = "";
        String scanWorkOrderNo = "";
        PsWipInfo wipInfo = null;
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_SETTING_LINE_CODE_IS_NULL.equals(e.getExMsgId()));
        }
        lineCode = "testLineCode";
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_PROCESS_CODE_IS_NULL.equals(e.getExMsgId()));
        }
        scanProcessCode = "1";
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCAN_WORK_ORDER_NO_IS_NULL.equals(e.getExMsgId()));
        }
        scanWorkOrderNo = "testWorkNo";
        List<PsEntityPlanBasicDTO> workOrders = new ArrayList<>();
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString())).thenReturn(null);
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WIP_WORKORDERNO_NOT_EXIST.equals(e.getExMsgId()));
        }
        PsEntityPlanBasicDTO work1 = new PsEntityPlanBasicDTO();
        work1.setWorkOrderNo("testWorkNo");
        workOrders.add(work1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString())).thenReturn(workOrders);
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORDER_ORDER_NOT_FOUND_ROUTE.equals(e.getExMsgId()));
        }
        work1.setRouteId("testRouteId");

        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_LINE_NOT_THE_SAME_WITH_SCAN_LINE.equals(e.getExMsgId()));
        }
        work1.setLineCode("testLineCode1");
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_LINE_NOT_THE_SAME_WITH_SCAN_LINE.equals(e.getExMsgId()));
        }
        work1.setLineCode("testLineCode");
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_PROCESS_GROUP_NOT_EXIST.equals(e.getExMsgId()));
        }
        work1.setProcessGroup("2(*)");
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_PROCESS_GROUP_NOT_CONTAIN_SCAN_PROCESS_CODE.equals(e.getExMsgId()));
        }
        work1.setProcessGroup("1$2(*)");
        List<SysLookupValuesDTO> sysLookupValueList = BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_SN_COMMON_SCAN_WORKSTATUS);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(sysLookupValueList);
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        Assert.assertTrue(psEntityPlanBasicDTO.getWorkOrderNo().equals("testWorkNo"));

        wipInfo = new PsWipInfo();
        wipInfo.setWorkOrderNo("testWorkNo1");
        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WIP_CURRENT_PROCESS_NOT_COMPLETED.equals(e.getExMsgId()));
        }
        wipInfo.setLastProcess("Y");


        PsEntityPlanBasicDTO psEntityPlanBasicDTO2 = service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        Assert.assertTrue(psEntityPlanBasicDTO2.getWorkOrderNo().equals("testWorkNo"));

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("开工");
        sysLookupValueList.add(sysLookupValuesDTO);

        try {
            service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_STATUS_NOT_ALLOW_TO_SCAN.equals(e.getExMsgId()));
        }
        work1.setWorkOrderStatus("开工");
        PsEntityPlanBasicDTO psEntityPlanBasicDTO1 = service.checkAndGetWorkOrderInfo(lineCode, scanProcessCode, scanWorkOrderNo, wipInfo);
        Assert.assertTrue(psEntityPlanBasicDTO1.getWorkOrderNo().equals("testWorkNo"));
    }

    @Test
    public void setFirstAndLastProcessFLagTest() {
        String scanProcessCode = "1";
        CtRouteDetailDTO routeDetail = null;
        String processGroup = "1$2";
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        CtRouteDetailDTO ctRouteDetailDTO = service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO != null);

        routeDetail = new CtRouteDetailDTO();
        routeDetail.setRouteId("testId");
        scanProcessCode = "";
        CtRouteDetailDTO ctRouteDetailDTO1 = service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO1 != null);

        scanProcessCode = "1";
        processGroup = "1(*)$2(*)";
        try {
            service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_PROCESS_GROUP_NOT_HAVE_MUST_SCAN_PROCESS.equals(e.getExMsgId()));
        }
        processGroup = "1(*)$2";
        CtRouteDetailDTO ctRouteDetailDTO2 = service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        Assert.assertTrue(!ctRouteDetailDTO2.getFirstProcessOfWorkOrderFlag());
        Assert.assertTrue(!ctRouteDetailDTO2.getLastProcessOfWorkOrderFlag());

        processGroup = "1$2(*)";
        CtRouteDetailDTO ctRouteDetailDTO3 = service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        Assert.assertTrue("testId".equals(ctRouteDetailDTO3.getRouteId()));
        Assert.assertTrue(ctRouteDetailDTO3.getFirstProcessOfWorkOrderFlag());
        Assert.assertTrue(ctRouteDetailDTO3.getLastProcessOfWorkOrderFlag());

        List<FlowControlInfoDTO> processInfoList = new ArrayList<>();
        FlowControlInfoDTO flowControlInfoDTO = new FlowControlInfoDTO();
        flowControlInfoDTO.setCurrProcessCode("1");
        processInfoList.add(flowControlInfoDTO);
        FlowControlInfoDTO flowControlInfoDTO1 = new FlowControlInfoDTO();
        flowControlInfoDTO1.setCurrProcessCode("2");
        processInfoList.add(flowControlInfoDTO1);
        commonScanDTO.setProcessInfoList(processInfoList);
        CtRouteDetailDTO ctRouteDetailDTO4 = service.setFirstAndLastProcessFLag(scanProcessCode, routeDetail, processGroup, commonScanDTO);
        Assert.assertTrue("testId".equals(ctRouteDetailDTO4.getRouteId()));
        Assert.assertTrue(ctRouteDetailDTO3.getFirstProcessOfWorkOrderFlag());
        Assert.assertTrue(ctRouteDetailDTO3.getLastProcessOfWorkOrderFlag());

    }

    @Test
    public void setFirstAndLastWorkStationFLagTest() throws Exception {
        String lineCode = "";
        String isProcessControl = "";
        CtRouteDetailDTO routeDetail = null;
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        CtRouteDetailDTO ctRouteDetailDTO = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO != null);
        routeDetail = new CtRouteDetailDTO();
        CtRouteDetailDTO ctRouteDetailDTO1 = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO1 != null);
        commonScanDTO.setScanWorkStation("11");

        List<FlowControlInfoDTO> processInfoList = new ArrayList<>();
        FlowControlInfoDTO flowControlInfoDTO = new FlowControlInfoDTO();
        flowControlInfoDTO.setCurrProcessCode("1");
        processInfoList.add(flowControlInfoDTO);
        commonScanDTO.setProcessInfoList(processInfoList);
        CtRouteDetailDTO ctRouteDetailDTO2 = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO2.getFirstWorkStationFlag());
        Assert.assertTrue(ctRouteDetailDTO2.getLastWorkStationFlag());

        commonScanDTO.setProcessInfoList(null);

        isProcessControl = "Y";
        CtRouteDetailDTO ctRouteDetailDTO3 = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO3.getFirstWorkStationFlag());
        Assert.assertTrue(ctRouteDetailDTO3.getLastWorkStationFlag());

        commonScanDTO.setActualProcessCodeOfLineModel("1");
        lineCode = "line1";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        isProcessControl = "N";
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        CtRouteDetailDTO ctRoute1 = new CtRouteDetailDTO();
        ctRoute1.setCurrProcess("1");
        ctRoute1.setNextProcess("");
        CtRouteDetailDTO ctRoute2 = new CtRouteDetailDTO();
        ctRoute2.setCurrProcess("0");
        ctRoute2.setNextProcess("1");
        ctRoute2.setIsUnnecessary("Y");
        CtRouteDetailDTO ctRoute3 = new CtRouteDetailDTO();
        ctRoute3.setCurrProcess("1");
        ctRoute3.setNextProcess("2");
        routeDetailList.add(ctRoute1);
        routeDetailList.add(ctRoute2);
        routeDetailList.add(ctRoute3);
        PowerMockito.when(CrafttechRemoteService.listWorkStationByLineAndProcess(Mockito.anyString(), Mockito.anyString())).thenReturn(routeDetailList);
        commonScanDTO.setScanWorkStation("2");
        routeDetail = new CtRouteDetailDTO();
        CtRouteDetailDTO ctRouteDetailDTO4 = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO4.getFirstWorkStationFlag());
        Assert.assertTrue(!ctRouteDetailDTO4.getLastWorkStationFlag());
        routeDetail = new CtRouteDetailDTO();
        routeDetail.setLastProcess("Y");
        routeDetail.setIsUnnecessary("N");
        CtRouteDetailDTO ctRouteDetailDTO5 = service.setFirstAndLastWorkStationFLag(lineCode, isProcessControl, routeDetail, commonScanDTO);
        Assert.assertTrue(ctRouteDetailDTO5.getFirstWorkStationFlag());
        Assert.assertTrue(ctRouteDetailDTO5.getLastWorkStationFlag());
    }

    @Test
    public void saveWipInfoTest() throws Exception {
        PsWipInfo wipInfo = new PsWipInfo();
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        List<FlowControlInfoDTO> processInfoList = new ArrayList<>();
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setCurrProcessCode("1");
        processInfoList.add(flow);
        commonScanDTO.setProcessInfoList(processInfoList);


        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        CtRouteDetailDTO ctRoute1 = new CtRouteDetailDTO();
        ctRoute1.setCurrProcess("1");
        ctRoute1.setNextProcess("");
        CtRouteDetailDTO ctRoute2 = new CtRouteDetailDTO();
        ctRoute2.setCurrProcess("0");
        ctRoute2.setNextProcess("1");
        ctRoute2.setIsUnnecessary("Y");
        CtRouteDetailDTO ctRoute3 = new CtRouteDetailDTO();
        ctRoute3.setCurrProcess("1");
        ctRoute3.setNextProcess("2");
        routeDetailList.add(ctRoute1);
        routeDetailList.add(ctRoute2);
        routeDetailList.add(ctRoute3);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.listWorkStationByLineAndProcess(Mockito.anyString(), Mockito.anyString())).thenReturn(routeDetailList);

        List<CtRouteDetailDTO> routeDetailList2 = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setCurrProcess("2");
        ctRouteDetailDTO1.setNextProcess("1");
        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        ctRouteDetailDTO2.setCurrProcess("1");
        ctRouteDetailDTO2.setNextProcess("2");
        routeDetailList2.add(ctRouteDetailDTO1);
        routeDetailList2.add(ctRouteDetailDTO2);
        PowerMockito.when(CrafttechRemoteService.getProcessListByRouteId(Mockito.anyString())).thenReturn(routeDetailList2);
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        wipInfo.setOpeTimes(new BigDecimal("1"));
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        routeDetailDTO = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        routeDetailDTO = new CtRouteDetailDTO();
        workOrderNo = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        workOrderNo = new PsEntityPlanBasicDTO();
        commonScanDTO = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);


        wipInfo = null;
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        commonScanDTO = new CommonScanDTO();
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.when(BarcodeCenterRemoteService.checkIfUseBarcodeCenter(Mockito.anyString())).thenReturn(false);
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        commonScanDTO.setFactoryId("52");
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);


        PowerMockito.when(BarcodeCenterRemoteService.checkIfUseBarcodeCenter(Mockito.anyString())).thenReturn(true);
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);


        workOrderNo.setItemNo("itemNo");
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        workOrderNo.setItemNo("123456789012ABC");
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        routeDetailDTO = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);


        routeDetailDTO = new CtRouteDetailDTO();
        workOrderNo = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);

        workOrderNo = new PsEntityPlanBasicDTO();
        commonScanDTO = null;
        service.saveWipInfo(wipInfo, routeDetailDTO, workOrderNo, commonScanDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void controlByPilotScaleTest() throws Exception {
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsWipInfo wipInfo = new PsWipInfo();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        routeDetailDTO.setLastProcessOfWorkOrderFlag(true);
        service.controlByPilotScale(routeDetailDTO, wipInfo, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        routeDetailDTO.setLastProcessOfWorkOrderFlag(false);
        service.controlByPilotScale(routeDetailDTO, wipInfo, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO = null;
        service.controlByPilotScale(routeDetailDTO, wipInfo, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO = new CtRouteDetailDTO();
        routeDetailDTO.setLastProcessOfWorkOrderFlag(false);
        commonScanDTO = null;
        service.controlByPilotScale(routeDetailDTO, wipInfo, workOrderNo, commonScanDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void handlerConvertParamsTest() {
        PmScanConditionDTO origin = new PmScanConditionDTO();
        CommonScanDTO commonScanDTO = service.handlerConvertParams(null);
        Assert.assertTrue(commonScanDTO != null);

        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<String, String>() {{put("X-Emp-No", "10307315");}});

        commonScanDTO = service.handlerConvertParams(origin);
        Assert.assertTrue(commonScanDTO != null);

        origin.setaProcessCode("1");
        commonScanDTO = service.handlerConvertParams(origin);
        Assert.assertTrue(commonScanDTO != null);

        List<PmScanConditionDTO> processInfoList = new ArrayList<>();
        PmScanConditionDTO flow = new PmScanConditionDTO();
        processInfoList.add(flow);
        origin.setProcessInfoList(processInfoList);
        commonScanDTO = service.handlerConvertParams(origin);
        Assert.assertTrue(commonScanDTO != null);

        flow.setProcessGroup("1$2");
        commonScanDTO = service.handlerConvertParams(origin);
        Assert.assertTrue(commonScanDTO != null);

        flow.setCurrProcessCode("1");
        Assert.assertNotNull(service.handlerConvertParams(origin));
        Assert.assertTrue(commonScanDTO != null);
    }

    @Test
    public void getWipInfoBySnTest() {
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(Mockito.anyString())).thenReturn(new PsWipInfo());
        PsWipInfo test = service.getWipInfoBySn("test");
        Assert.assertTrue(test != null);
    }

    @Test
    public void checkInputAndOutPutTest() {
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        service.checkInputAndOutPut(null,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);
        service.checkInputAndOutPut(routeDetailDTO,null,commonScanDTO);
        Assert.assertTrue(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,null);
        Assert.assertTrue(true);

        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_ORDER_RELATION_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        workOrderNo.setWorkOrderQty(new BigDecimal("10"));
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_ORDER_RELATION_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        workOrderNo.setInputQty(new BigDecimal("1"));
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_ORDER_RELATION_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        workOrderNo.setOutputQty(new BigDecimal("5"));
        commonScanDTO.setSn("701212100015");
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_TOO_BIGGER.equals(e.getExMsgId()));
        }

        commonScanDTO.setSn("701212100000");
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_MUST_BE_GREATER.equals(e.getExMsgId()));
        }

        commonScanDTO.setSn("701212100001");
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setFirstProcessOfWorkOrderFlag(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setFirstProcessOfWorkOrderFlag(false);
        routeDetailDTO.setFirstWorkStationFlag(true);

        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setFirstProcessOfWorkOrderFlag(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);


        workOrderNo.setInputQty(new BigDecimal("19"));
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.IN_QTY_BIG_THAN_WO_QTY.equals(e.getExMsgId()));
        }

        workOrderNo.setInputQty(new BigDecimal("1"));
        routeDetailDTO.setLastProcessOfWorkOrderFlag(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setLastProcessOfWorkOrderFlag(false);
        routeDetailDTO.setLastWorkStationFlag(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setLastProcessOfWorkOrderFlag(true);
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);

        workOrderNo.setOutputQty(new BigDecimal("19"));
        try {
            service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.OUT_QTY_BIG_THAN_WO_QTY.equals(e.getExMsgId()));
        }

        workOrderNo.setOutputQty(new BigDecimal("1"));
        service.checkInputAndOutPut(routeDetailDTO,workOrderNo,commonScanDTO);
        Assert.assertTrue(true);
    }

    @Test
    public void handlerFirstProcessTest() throws Exception {
        int qty = 1;
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        service.handlerFirstProcess(qty, null, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        service.handlerFirstProcess(qty, routeDetailDTO, null, commonScanDTO);
        Assert.assertTrue(true);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, null);
        Assert.assertTrue(true);


        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setFirstWorkStationFlag(true);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setFirstWorkStationFlag(false);
        routeDetailDTO.setFirstProcessOfWorkOrderFlag(true);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);


        routeDetailDTO.setFirstWorkStationFlag(true);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        workOrderNo.setCraftSection(MpConstant.CRAFT_SECTION_SMTB);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        workOrderNo.setCraftSection(MpConstant.PROCESS_NAME_DIP);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        workOrderNo.setCraftSection(MpConstant.CRAFT_SECTION_SMTA);
        service.handlerFirstProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);

    }

    @Test
    public void handlerLastProcessTest() throws Exception {
        int qty = 1;
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        service.handlerLastProcess(qty, null, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        service.handlerLastProcess(qty, routeDetailDTO, null, commonScanDTO);
        Assert.assertTrue(true);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, null);
        Assert.assertTrue(true);


        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setLastWorkStationFlag(true);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        routeDetailDTO.setLastWorkStationFlag(false);
        routeDetailDTO.setLastProcessOfWorkOrderFlag(true);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);


        routeDetailDTO.setLastWorkStationFlag(true);
        try {
            service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_ORDER_RELATION_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        workOrderNo.setWorkOrderQty(new BigDecimal("10"));
        try {
            service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WORK_ORDER_ORDER_RELATION_QTY_ILLEGAL.equals(e.getExMsgId()));
        }
        workOrderNo.setOutputQty(new BigDecimal("5"));
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        workOrderNo.setOutputQty(new BigDecimal("9"));
        workOrderNo.setCraftSection(MpConstant.CRAFT_SECTION_SMTB);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        workOrderNo.setCraftSection(MpConstant.PROCESS_NAME_DIP);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);
        workOrderNo.setCraftSection(MpConstant.CRAFT_SECTION_SMTA);
        service.handlerLastProcess(qty, routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

    }

    @Test
    public void saveScanHistoryTest() {
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        PsWipInfo psWipInfo = new PsWipInfo();

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        service.saveScanHistory(routeDetailDTO, workOrderNo, commonScanDTO, psWipInfo);
        Assert.assertTrue(true);

        commonScanDTO.setFactoryId("52");
        psWipInfo.setWorkStation("0");
        service.saveScanHistory(routeDetailDTO, workOrderNo, commonScanDTO, psWipInfo);
        Assert.assertTrue(true);

        List<FlowControlInfoDTO> processInfoList = new ArrayList<>();
        processInfoList.add(new FlowControlInfoDTO());
        commonScanDTO.setProcessInfoList(processInfoList);
        service.saveScanHistory(routeDetailDTO, workOrderNo, commonScanDTO, psWipInfo);
        Assert.assertTrue(true);

        service.saveScanHistory(routeDetailDTO, workOrderNo, null, psWipInfo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void updateLatestDeliveryDateTest() throws Exception {
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        service.updateLatestDeliveryDate(workOrderNo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void deleteSnInContainTest() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        service.deleteSnInContain("sn");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void updateTimePeriodOutTest() throws Exception {
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");

        service.updateTimePeriodOut(routeDetailDTO, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        service.updateTimePeriodOut(null, workOrderNo, commonScanDTO);
        Assert.assertTrue(true);

        service.updateTimePeriodOut(routeDetailDTO, null, commonScanDTO);
        Assert.assertTrue(true);

        service.updateTimePeriodOut(routeDetailDTO, workOrderNo, null);
        Assert.assertTrue(true);

        PowerMockito.when(psWipInfoService.writeOutPut(Mockito.any(), Mockito.anyString())).thenReturn("error");
        try {
            service.updateTimePeriodOut(routeDetailDTO, workOrderNo, null);
        } catch (Exception e) {
            Assert.assertTrue(e != null);
        }
    }

    @Test
    public void lockControlTest() throws Exception {
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        service.lockControl(dto);
        Assert.assertTrue(true);

        dto.setaProcessCode("1");
        service.lockControl(dto);
        Assert.assertTrue(true);

        List<FlowControlInfoDTO> processInfoList = new ArrayList<>();
        FlowControlInfoDTO flowControlInfoDTO1 = new FlowControlInfoDTO();
        flowControlInfoDTO1.setaProcessCode("1");
        FlowControlInfoDTO flowControlInfoDTO2 = new FlowControlInfoDTO();
        processInfoList.add(flowControlInfoDTO1);
        processInfoList.add(flowControlInfoDTO2);
        dto.setProcessInfoList(processInfoList);
        service.lockControl(dto);
        Assert.assertTrue(true);

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenReturn("error");

        try {
            service.lockControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }
    }

    @Test
    public void bindingControlTest() throws Exception {
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        service.bindingControl(dto);
        Assert.assertTrue(true);

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenReturn("error");

        try {
            service.bindingControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenThrow(new Exception("error"));
        try {
            service.bindingControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }
    }

    @Test
    public void testControlTest() throws Exception {
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        service.testControl(dto);
        Assert.assertTrue(true);

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenReturn("error");

        try {
            service.testControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenThrow(new Exception("error"));
        try {
            service.testControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }
    }

    @Test
    public void techChangeControlTest() throws Exception {
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        service.techChangeControl(dto);
        Assert.assertTrue(true);

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenReturn("error");

        try {
            service.techChangeControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }

        PowerMockito.when(ProductionmgmtRemoteService.snControl(Mockito.any())).thenThrow(new Exception("error"));
        try {
            service.techChangeControl(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }
    }
    @Test
    public void getControlEntityTest() {
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        commonScanDTO.setSn("sn");
        FlowControlInfoDTO controlEntity = service.getControlEntity(commonScanDTO);
        Assert.assertTrue(controlEntity.getSn().equals("sn"));

        commonScanDTO.setActualProcessCodeOfLineModel("1");
        FlowControlInfoDTO controlEntity1 = service.getControlEntity(commonScanDTO);
        Assert.assertTrue(controlEntity1.getCurrProcessCode().equals("1"));
    }

    @Test
    public void flowMainTest() throws Exception {
        PmScanConditionDTO dto = new PmScanConditionDTO();
        PowerMockito.mockStatic(HttpRemoteService.class);
        service.flowMain(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getRouteDetailRelationInfoTest() throws Exception {
        String isProcessControl = "Y";
        CtRouteDetailDTO processRouteDetail = new CtRouteDetailDTO();
        processRouteDetail.setCurrProcess("1");
        CtRouteDetailDTO workStationRouteDetail = new CtRouteDetailDTO();
        workStationRouteDetail.setCurrProcess("10");
        CtRouteDetailDTO routeDetailRelationInfo = service.getRouteDetailRelationInfo(isProcessControl, processRouteDetail, workStationRouteDetail);
        Assert.assertTrue(routeDetailRelationInfo.getCurrProcess().equals("1"));
        isProcessControl = "N";
        CtRouteDetailDTO routeDetailRelationInfo1 = service.getRouteDetailRelationInfo(isProcessControl, processRouteDetail, workStationRouteDetail);
        Assert.assertTrue(routeDetailRelationInfo1.getCurrProcess().equals("10"));
    }

    @Test
    public void handlerUpdate() throws Exception {
        CtRouteDetailDTO routeDetailDTO = new CtRouteDetailDTO();
        PsEntityPlanBasicDTO workOrderNo = new PsEntityPlanBasicDTO();
        CommonScanDTO commonScanDTO = new CommonScanDTO();
        PsWipInfo wipInfo = null;
        Assert.assertNull(Whitebox.invokeMethod(service, "handlerUpdate", wipInfo,routeDetailDTO,workOrderNo, commonScanDTO));
        wipInfo = new PsWipInfo();
        wipInfo.setAttribute1("1233");
        Assert.assertNull(Whitebox.invokeMethod(service, "handlerUpdate", wipInfo,routeDetailDTO,workOrderNo, commonScanDTO));
    }
}
