package com.zte.application.impl;

import com.zte.application.AssemblyOptRecordService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.DTO.TaskReconfigurationRecordDTO;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.interfaces.dto.scan.PickListResultDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.consts.CommonConst.FORM_SN;
import static com.zte.consts.CommonConst.FORM_TYPE;
import static org.mockito.ArgumentMatchers.*;

/**
 * 任务改配表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-01 14:38:39
 */
@PrepareForTest({CommonUtils.class,DatawbRemoteService.class,PlanscheduleRemoteService.class,BasicsettingRemoteService.class})
public class TaskReconfigurationRecordServiceImplTest  extends PowerBaseTestCase {

    @InjectMocks
    private TaskReconfigurationRecordServiceImpl service;
    @Mock
    private TaskReconfigurationRecordRepository repository;
    @Mock
    private AssemblyOptRecordService assemblyOptRecordService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Before
    public void init() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
    }

    @Test
    public void testCheckTaskNo() {
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setEntityClass(null);
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(anyList()))
                .thenReturn(Collections.singletonList(taskDTO));
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.checkTaskNo("123");
        });
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(anyList()))
                .thenReturn(null);
        PowerMockito.when(CommonUtils.getLmbMessage(any()))
                .thenReturn(any());
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.checkTaskNo("123");
        });

        taskDTO.setEntityClass("REWORK_2");
        // 1. 创建Mock对象

// 2. 设置Mock行为
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(anyList()))
                .thenReturn(Collections.singletonList(taskDTO));  // expectedResult是你期望返回的对象

        PsTaskExtendedDTO result = service.checkTaskNo("123");

        Assert.assertNotNull(RetCode.SUCCESS_CODE, result);

    }

    @Test
    public void checkSn() {
        PowerMockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(anyString()))
                .thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.checkSn("123","123");
        });
// 2. 设置Mock行为
        PickListResultDTO pickListResultDTO = new PickListResultDTO();
        pickListResultDTO.setTaskNo("dad");
        PowerMockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(anyString()))
                .thenReturn(Collections.singletonList(pickListResultDTO));
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.checkSn("123","123");
        });
        pickListResultDTO.setSnList("123");
        PowerMockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(anyString()))
                .thenReturn(Collections.singletonList(pickListResultDTO));
        String resultss = service.checkSn("123","123");

        Assert.assertEquals("true", resultss);

    }
    private static final String NEGATIVE_NUMBER = "-\\d+";
    @Test
    public void testCheckSn(){
        PickListResultDTO pickListResultDTO = new PickListResultDTO();
        pickListResultDTO.setTaskNo("dad");
        PowerMockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(anyString()))
                .thenReturn(Collections.singletonList(pickListResultDTO));
        pickListResultDTO.setSnList("1233");
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.checkSn("123","123");
        });
    }

    @Test
    public void testLocalMethods() throws Exception {
        Assert.assertTrue(service.areSumZero(BigDecimal.ZERO,BigDecimal.ZERO));
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ZERO);
        wipExtendIdentification.setItemNo("123");
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        Assert.assertNotNull(service.getWipExtendIdentificationList("123"));
        QueryTaskNoAndSNDataDto queryModel =new QueryTaskNoAndSNDataDto();
        List<TaskReconfigurationRecordDTO> taskReconfigurationRecords = new ArrayList<>();
        String empNo="123";
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTOs= new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTOs.setOperationType(1);
        taskReconfigurationRecordDTOs.setQuantity(BigDecimal.ZERO);
        taskReconfigurationRecordDTOs.setItemBarcode("123");
        taskReconfigurationRecordDTOs.setItemCode("123");
        PowerMockito.when(repository.selectWithConditions(any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTOs));

        PsTaskExtendedDTO ps = new PsTaskExtendedDTO();
        ps.setTaskQty(BigDecimal.ONE);
        ps.setItemNo("123");
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(ps));
        Assert.assertNotNull(service.getClickData(queryModel,taskReconfigurationRecords,empNo));
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(null));
        Assert.assertThrows(NullPointerException.class,()->service.getClickData(queryModel,taskReconfigurationRecords,empNo));
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        Map<String, List<TaskReconfigurationRecordDTO>> taskMap = Collections.singletonList(taskReconfigurationRecordDTOs).stream().collect(Collectors.groupingBy(TaskReconfigurationRecordDTO::getItemBarcode));
        Map<String, String> collect = Collections.singletonList(itemListEntityDTO).stream().filter(item -> item.getRequiredQuantity().matches(NEGATIVE_NUMBER)).collect(Collectors.toMap(ItemListEntityDTO::getItemNo, ItemListEntityDTO::getRequiredQuantity, (o1, o2) -> o1));
        queryModel.setTaskNo("123");
        Map<String, BigDecimal> itemNoNumMap = new HashMap<>();
        Assert.assertNotNull(service.getOperateNum(new HashMap<>(),wipExtendIdentification,collect,queryModel,itemNoNumMap));
        Assert.assertNotNull(service.getOperateNum(taskMap,wipExtendIdentification,collect,queryModel,itemNoNumMap));
        Assert.assertNotNull(service.getOperateNum(null,wipExtendIdentification,collect,queryModel,itemNoNumMap));
        wipExtendIdentification.setFormQty(BigDecimal.TEN);
        Assert.assertNotNull(service.getOperateNum(null,wipExtendIdentification,collect,queryModel,itemNoNumMap));
        wipExtendIdentification.setFormQty(BigDecimal.ZERO);
        collect.put("123","10");
        Assert.assertNotNull(service.getOperateNum(null,wipExtendIdentification,collect,queryModel,itemNoNumMap));
        Assert.assertNotNull(service.getNegativeItems(queryModel));
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO.setOperationType(2);
        PowerMockito.when(repository.selectWithConditions(any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO));

        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setParentCategoryCode(SN_CODE);
        queryModel.setOperationType(1);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO, "12345");
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,request);
        });
        queryModel.setOperationType(3);
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,request);
        });
        queryModel.setOperationType(2);
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,request);
        });
        itemListEntityDTO.setRequiredQuantity("-2");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any()))
                .thenReturn(null);
        dto.setRelatedContainerBarcode("123");
        Assert.assertThrows(Exception.class, () -> {
            service.checkAssemble("123",queryModel,dto);
        });
        itemListEntityDTO.setRequiredQuantity("2e");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        Assert.assertThrows(Exception.class, () -> {
            service.checkAssemble("123",queryModel,dto);
        });

        itemListEntityDTO.setRequiredQuantity("2");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        Assert.assertThrows(Exception.class, () -> {
            service.checkAssemble("123",queryModel,dto);
        });
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setSn("123");
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification1));
        Assert.assertThrows(Exception.class, () -> {
            service.checkAssemble("123",queryModel,dto);
        });
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any()))
                .thenReturn(null);
        Assert.assertThrows(Exception.class, () -> {
            service.checkAssemble("123",queryModel,dto);
        });
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("123");
        sysLookupValuesDTO.setAttribute1("1");
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any()))
                .thenReturn(Collections.singletonList(sysLookupValuesDTO));
        PsTask psTask = new PsTask();
        psTask.setIsLead("123");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(queryModel.getTaskNo()))
                .thenReturn(psTask);
        dto.setIsLead("123");
        service.checkAssemble("123",queryModel,dto);
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        sysLookupValuesDTO1.setDescriptionChin("1234");
        sysLookupValuesDTO1.setAttribute1("11");
        dto.setIsLead("123");
        PsTask psTas = new PsTask();
        psTas.setIsLead("12341");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(queryModel.getTaskNo()))
                .thenReturn(psTas);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any()))
                .thenReturn(Arrays.asList(sysLookupValuesDTO1,sysLookupValuesDTO));
        try {
            service.checkAssemble("123", queryModel, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ENV_CHECK_FAILED, e.getExMsgId());
        }
        psTas.setIsLead("1234");
        dto.setIsLead(null);
        try {
            service.checkAssemble("123", queryModel, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ENV_CHECK_FAILED, e.getExMsgId());
        }
        dto.setIsLead("321");
        try {
            service.checkAssemble("123", queryModel, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ENV_CHECK_FAILED, e.getExMsgId());
        }
        dto.setIsLead("123");
        try {
            service.checkAssemble("123", queryModel, dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ENV_CHECK_FAILED, e.getExMsgId());
        }
        Assert.assertThrows(Exception.class, () -> {
                    service.checkAssemble("123", queryModel, dto);
                });
        Assert.assertThrows(Exception.class, () -> {
            service.checkDismantle(Collections.singletonList(wipExtendIdentification),queryModel);
        });
        Assert.assertThrows(Exception.class, () -> {
            service.checkDismantle(null,queryModel);
        });


        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(queryModel.getTaskNo()))
                .thenReturn(null);
        service.checkAssemble("123",queryModel,dto);

        Assert.assertThrows(Exception.class, () -> {
            service.checkDismantle(Collections.singletonList(wipExtendIdentification),queryModel);
        });
        service.getItemErp("123","123");

        /*SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("123");
        sysLookupValuesDTO.setAttribute1("1");
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any()))
                .thenReturn(Collections.singletonList(sysLookupValuesDTO));*/

        SubmitTaskNoAndSNDataDto query = new SubmitTaskNoAndSNDataDto();
        query.setSn("123");
        query.setTaskNo("123");
        query.setOperationType(2);
        query.setOperationNum(BigDecimal.ZERO);
        PowerMockito.when(repository.batchInsert(any()))
                .thenReturn(1);
        PowerMockito.when(wipExtendIdentificationService.bindingItems(any()))
                .thenReturn(1);
        taskReconfigurationRecordDTO.setQuantity(BigDecimal.ZERO);
        service.submmitReconfiguration(Collections.singletonList(taskReconfigurationRecordDTO),query,itemListEntityDTO,"123","123");
        //  service.submmitReconfiguration()
    }
    @Test
    public void getDataBySNasdAndTaskNo() throws Exception {
        SubmitTaskNoAndSNDataDto query = new SubmitTaskNoAndSNDataDto();
        query.setSn("123");
        query.setTaskNo("123");
        query.setOperationType(2);
        query.setOperationNum(BigDecimal.ZERO);
        Map<String, Object> wipExtendIdentificationMap = new HashMap<>(16);
        wipExtendIdentificationMap.put("sn", "123");
        wipExtendIdentificationMap.put(FORM_SN,"1321");
        wipExtendIdentificationMap.put(FORM_TYPE, "2");
        PowerMockito.when(wipExtendIdentificationRepository.getList(wipExtendIdentificationMap)).thenReturn(new ArrayList<>());
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("123");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(null).thenReturn(Collections.singletonList(barcodeExpandDTO));
        Assert.assertThrows(MesBusinessException.class,()->{
            Whitebox.invokeMethod(service, "geBarcodeExpandDTO",query);
        });

        Assert.assertNotNull(Whitebox.invokeMethod(service, "geBarcodeExpandDTO", query));
    }
    @Test
    public void getDataBySNAndTaskNo() {
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("2");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO.setOperationType(3);
        PowerMockito.when(repository.selectWithConditions(any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO));

        QueryTaskNoAndSNDataDto queryTaskNoAndSNDataDto = new QueryTaskNoAndSNDataDto();
        queryTaskNoAndSNDataDto.setTaskNo("123");
        queryTaskNoAndSNDataDto.setSn("123");
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskQty(BigDecimal.ONE);
        psTaskExtendedDTO.setItemNo("123");
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(psTaskExtendedDTO));
        service.getDataBySNAndTaskNo(queryTaskNoAndSNDataDto);
        taskReconfigurationRecordDTO.setOperationType(2);
        PowerMockito.when(repository.selectWithConditions(any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO));
        itemListEntityDTO.setRequiredQuantity("-2");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        service.getDataBySNAndTaskNo(queryTaskNoAndSNDataDto);
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTOs= new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTOs.setOperationType(1);
        taskReconfigurationRecordDTOs.setQuantity(BigDecimal.TEN);
        taskReconfigurationRecordDTOs.setItemCode("123");
        PowerMockito.when(repository.selectWithConditions(any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTOs));
        itemListEntityDTO.setRequiredQuantity("2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        service.getDataBySNAndTaskNo(queryTaskNoAndSNDataDto);
        itemListEntityDTO.setRequiredQuantity("-2");
        ItemListEntityDTO itemListEntityDTO1 = new ItemListEntityDTO();
        itemListEntityDTO1.setItemNo("123");
        itemListEntityDTO1.setRequiredQuantity("-2");
        List<ItemListEntityDTO> list = Arrays.asList(itemListEntityDTO, itemListEntityDTO1);
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(list);
        //service.getDataBySNAndTaskNo(queryTaskNoAndSNDataDto);
        TaskNoAndSNDataDto dataBySNAndTaskNo = service.getDataBySNAndTaskNo(queryTaskNoAndSNDataDto);
        Assert.assertNotNull(dataBySNAndTaskNo);

    }

    @Test
    public void oneClickUnbinding() throws Exception {
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        QueryTaskNoAndSNDataDto queryModel=new QueryTaskNoAndSNDataDto();
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO, "12345");
        service.oneClickUnbinding(queryModel,request);

        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Mockito.anyList());
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.oneClickUnbinding(queryModel,request);
        });
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskQty(BigDecimal.ONE);
        psTaskExtendedDTO.setItemNo("123");
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(psTaskExtendedDTO));
        itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        service.oneClickUnbinding(queryModel,request);

        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO1 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO1.setItemBarcode("123");
        taskReconfigurationRecordDTO1.setQuantity(BigDecimal.ZERO);
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO1));
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("123");
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);
        WipExtendIdentification wipExtendIdenti = new WipExtendIdentification();
        wipExtendIdenti.setItemNo("123");
        wipExtendIdenti.setSn("123");
        wipExtendIdenti.setFormQty(BigDecimal.ONE);

        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Arrays.asList(wipExtendIdentification,wipExtendIdenti));
        PowerMockito.when(wipExtendIdentificationService.unBindAssemblyRelationship(anyList(),anyString()))
                .thenReturn(anyInt());
        String oneClickUnbinding = service.oneClickUnbinding(queryModel, request);
        Assert.assertEquals("true",oneClickUnbinding);

    }

    private static final String SN_CODE = "SN_CODE";

    @Test
    public void checkUnbindingOrReconfiguration()throws Exception {
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("123");
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);

        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));


        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(null);
        QueryTaskNoAndSNDataDto queryModel = new QueryTaskNoAndSNDataDto();
        queryModel.setSn("123");
        queryModel.setTaskNo("123");
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });

        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setParentCategoryCode(SN_CODE);
        dto.setItemCode("123");
        queryModel.setOperationType(1);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });

        queryModel.setOperationType(2);
        dto = new BarcodeExpandDTO();
        dto.setParentCategoryCode("sde");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));

        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO1 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO1.setItemBarcode("123");
        taskReconfigurationRecordDTO1.setItemCode("123");
        taskReconfigurationRecordDTO1.setQuantity(BigDecimal.ZERO);
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO1));
        itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        queryModel.setItemSn("123");
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });

        itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(null);
        PsTask psTask = new PsTask();
        psTask.setIsLead("123");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(any()))
                .thenReturn(psTask);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("123");
        sysLookupValuesDTO.setAttribute1("1");
        dto.setIsLead("123");
        dto.setRelatedContainerBarcode("123");
        dto.setParentCategoryCode(SN_CODE);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any()))
                .thenReturn(Collections.singletonList(sysLookupValuesDTO));
        itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        Assert.assertThrows(Exception.class, () -> {
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });
        dto.setParentCategoryCode("SNs_CODE");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        Assert.assertThrows(Exception.class, () -> {
                    service.checkUnbindingOrReconfiguration(queryModel, any());
                });

        dto.setIsLead("123");
        dto.setRelatedContainerBarcode("123");
        dto.setParentCategoryCode("133");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        dto.setParentCategoryCode("SN_CODE");
        dto.setItemCode("123");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        Assert.assertThrows(Exception.class,()->{
            service.checkUnbindingOrReconfiguration(queryModel,any());
        });
        //getRelatedContainerBarcode

    }

    @Test
    public void submitDismantlingOrReconfiguration()throws Exception {
        PowerMockito.when(assemblyOptRecordService.batchInsert(anyList()))
                .thenReturn(1);
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskQty(BigDecimal.ONE);
        psTaskExtendedDTO.setItemNo("123");
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(psTaskExtendedDTO));
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO, "12345");
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("123");
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);

        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO1 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO1.setItemBarcode("123");
        taskReconfigurationRecordDTO1.setItemCode("123");
        taskReconfigurationRecordDTO1.setQuantity(BigDecimal.ZERO);
        taskReconfigurationRecordDTO1.setOperationType(1);

        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO12 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO12.setItemBarcode("1234");
        taskReconfigurationRecordDTO12.setItemCode("1234");
        taskReconfigurationRecordDTO12.setQuantity(BigDecimal.ZERO);
        taskReconfigurationRecordDTO12.setOperationType(2);

        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Arrays.asList(taskReconfigurationRecordDTO1,taskReconfigurationRecordDTO12));

        PowerMockito.when(CommonUtils.transformStrToBigDecimal(Mockito.any()))
                .thenReturn(BigDecimal.ZERO);
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setParentCategoryCode(SN_CODE);
        dto.setItemCode("123");
        dto.setRelatedContainerBarcode(SN_CODE);
        SubmitTaskNoAndSNDataDto queryModel = new SubmitTaskNoAndSNDataDto();
        queryModel.setSn("123");
        queryModel.setTaskNo("123");
        queryModel.setOperationType(1);
        queryModel.setOperationNum(BigDecimal.ZERO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(null);
        Assert.assertThrows(Exception.class,()->service.submitDismantlingOrReconfiguration(queryModel,request));
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(Collections.singletonList(dto));
        service.submitDismantlingOrReconfiguration(queryModel,request);
        queryModel.setOperationType(3);
        service.submitDismantlingOrReconfiguration(queryModel,request);
        queryModel.setOperationType(1);
/*        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.anyMap())).thenReturn(null);
        service.submitDismantlingOrReconfiguration(queryModel,request);*/
        queryModel.setOperationNum(BigDecimal.TEN);
        Assert.assertThrows(Exception.class,()->service.submitDismantlingOrReconfiguration(queryModel,request));
        queryModel.setOperationNum(BigDecimal.ZERO);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        taskReconfigurationRecordDTO1.setOperationType(2);
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO1));
        queryModel.setOperationType(2);
        itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));
        PowerMockito.when(repository.batchInsert(any()))
                .thenReturn(0);
        PowerMockito.when(wipExtendIdentificationService.bindingItems(any()))
                .thenReturn(0);
        psTaskExtendedDTO.setTaskQty(BigDecimal.ONE);
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(psTaskExtendedDTO));
        String string = service.submitDismantlingOrReconfiguration(queryModel, request);
        Assert.assertEquals("true",string);


    }
    @Test
    public void getOneClickUnbindingData() {
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskQty(BigDecimal.ONE);
        psTaskExtendedDTO.setItemNo("123");
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(any()))
                .thenReturn(Collections.singletonList(psTaskExtendedDTO));
        QueryTaskNoAndSNDataDto queryModel=new QueryTaskNoAndSNDataDto();
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO, "12345");


        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("-2");
        itemListEntityDTO.setItemNo("123");
        PowerMockito.when(DatawbRemoteService.getListByTaskList(Mockito.anyList()))
                .thenReturn(Collections.singletonList(itemListEntityDTO));

        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO1 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO1.setItemBarcode("123");
        taskReconfigurationRecordDTO1.setQuantity(BigDecimal.ZERO);
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO1));
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("123");
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);

        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));

        Assert.assertNotNull(service.getOneClickUnbindingData(queryModel,request));
    }
    @Test
    public void testQueryPage() {
        PowerMockito.when(repository.selectSumQuantityPage(any())).thenReturn(new ArrayList<TaskReconfigurationRecord>());
        Assert.assertTrue(service.selectSumQuantityPage(new TaskReconfigurationRecordPageQueryDTO()).getRows().isEmpty());
    }

    @Test
    public void selectDismantleAndBind() {
        TaskReconfigurationRecordDTO taskReconfigurationRecordDTO1 = new TaskReconfigurationRecordDTO();
        taskReconfigurationRecordDTO1.setItemBarcode("123");
        taskReconfigurationRecordDTO1.setQuantity(BigDecimal.ZERO);
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(null);
        service.selectDismantleAndBind("123");
        PowerMockito.when(repository.selectWithConditions(Mockito.any()))
                .thenReturn(Collections.singletonList(taskReconfigurationRecordDTO1));
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("123");
        wipExtendIdentification.setSn("123");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(Collections.singletonList(wipExtendIdentification));
        service.selectDismantleAndBind("123");
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(null);
        Assert.assertNotNull(service.selectDismantleAndBind("123"));
    }

    @Test
    public void testGetById() {
        PowerMockito.when(repository.selectById(any())).thenReturn(new TaskReconfigurationRecord());
        Assert.assertNull(service.getById(null).getTaskNo());
    }

    @Test
    public void testAdd() {
        PowerMockito.when(repository.insert(any())).thenReturn(1L);
        TaskReconfigurationRecord reconfigurationRecord = new TaskReconfigurationRecord();
        service.add(reconfigurationRecord);
        Assert.assertNull(reconfigurationRecord.getTaskNo());
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(repository.updateById(any())).thenReturn(1L);
        TaskReconfigurationRecord reconfigurationRecord = new TaskReconfigurationRecord();
        service.updateById(reconfigurationRecord);
        Assert.assertNull(reconfigurationRecord.getTaskNo());
    }

    @Test
    public void testDeleteByIds() {
        PowerMockito.when(repository.deleteByIds(any())).thenReturn(1L);
        Assert.assertNotNull(service.deleteByIds(new ArrayList()));
    }

}


