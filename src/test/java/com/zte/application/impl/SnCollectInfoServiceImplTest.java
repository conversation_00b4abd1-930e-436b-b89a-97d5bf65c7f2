package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.SnCollectInfo;
import com.zte.domain.model.SnCollectInfoRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

public class SnCollectInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private SnCollectInfoServiceImpl service;

    @Mock
    private SnCollectInfoRepository snCollectInfoRepository;

    @Test
    public void getUnboundSn() {
        String line = "111";
        PowerMockito.when(snCollectInfoRepository.getUnboundSn(Mockito.any())).thenReturn(null);
        service.getUnboundSn(line);
        SnCollectInfo snCollectInfo = new SnCollectInfo();
        PowerMockito.when(snCollectInfoRepository.getUnboundSn(Mockito.any())).thenReturn(snCollectInfo);
        service.getUnboundSn(line);
        snCollectInfo.setBindingFlag("N");
        Assert.assertNull(service.getUnboundSn(line));
    }

    @Test
    public void boundSn() {
        String line = "111";
        PowerMockito.when(snCollectInfoRepository.boundSn(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.boundSn(line));
    }

    @Test
    public void addRecord() {

        service.addRecord("","","");

        service.addRecord("SMT-CS4","","");

        service.addRecord("SMT-CS4","P77776660001","");

        service.addRecord("SMT-CS4","P77776660001","10266925");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}