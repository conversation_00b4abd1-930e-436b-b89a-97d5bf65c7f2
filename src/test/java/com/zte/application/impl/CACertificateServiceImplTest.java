package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.domain.model.PsWipInfo;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.CACertificateImportResultDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: CACertificateServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/6/20 上午9:45
 */
@PrepareForTest({PlanscheduleRemoteService.class})
public class CACertificateServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private CACertificateServiceImpl service;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Test
    public void checkCACertificateInLocalTest() throws Exception {
        List<CACertificateImportResultDTO> caCertImpList = new ArrayList<>();
        service.checkCACertificateInLocal(caCertImpList);
        CACertificateImportResultDTO dto1 = new CACertificateImportResultDTO();
        caCertImpList.add(dto1);
        dto1.setSn("sn1");
        CACertificateImportResultDTO dto2 = new CACertificateImportResultDTO();
        caCertImpList.add(dto2);
        dto2.setSn("sn2");
        CACertificateImportResultDTO dto3 = new CACertificateImportResultDTO();
        caCertImpList.add(dto3);
        dto3.setSn("sn3");

        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfos.add(psWipInfo1);
        psWipInfo1.setSn("sn2");
        psWipInfo1.setAttribute2("taskNo2");
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfos.add(psWipInfo2);
        psWipInfo2.setSn("sn3");
        psWipInfo2.setAttribute2("taskNo3");
        PowerMockito.when(psWipInfoService.listSnAndTaskNoBySns(Mockito.anyList())).thenReturn(psWipInfos);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsTaskDTO> psTaskDTOS = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setTaskNo("taskNo3");
        psTaskDTO.setItemNo("itemNo");
        psTaskDTO.setItemName("itemName");
        psTaskDTOS.add(psTaskDTO);
        PowerMockito.when(PlanscheduleRemoteService.selectPsTaskOfWmesByTaskNos(Mockito.anySet())).thenReturn(psTaskDTOS);
        Assert.assertNotNull(service.checkCACertificateInLocal(caCertImpList));
    }
}
