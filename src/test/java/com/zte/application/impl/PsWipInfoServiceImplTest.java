package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.HighTempSamplingService;
import com.zte.application.PsBarcodeControlInfoService;
import com.zte.application.PsCommonScanService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.mds.MdsPackScanDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.model.MessageId.WORKORDER_NOT_FIND;
import static com.zte.common.utils.Constant.LOOKUP_VALUE_6691;
import static com.zte.common.utils.Constant.LOOKUP_VALUE_6691001;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({DatawbRemoteService.class, CrafttechRemoteService.class, SpringUtil.class, ProductionmgmtRemoteService.class,CommonUtils.class,
        MicroServiceRestUtil.class, RedisHelper.class, ConstantInterface.class, HttpRemoteUtil.class,JacksonJsonConverUtil.class,MessageId.class,
        CommonUtils.class,BasicsettingRemoteService.class,PlanscheduleRemoteService.class,ProductionDeliveryRemoteService.class, MESHttpHelper.class,
        BarcodeCenterRemoteService.class,HrmUserInfoService.class, MicroServiceDiscoveryInvoker.class,HttpServletResponse.class,ObtainRemoteServiceDataUtil.class})
public class PsWipInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private PsWipInfoServiceImpl service;

    @Mock
    private PmRepairMesErrorLogRepository pmRepairMesErrorLogRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Mock
    private PsBarcodeControlInfoService psBarcodeControlInfoService;
    @Mock
    private PsCommonScanService psCommonScanService;
    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
	@Mock
	private PlanscheduleRemoteService planscheduleRemoteService;
	@Mock
	private PsScanHistoryService psScanHistoryService;
	@Mock
	private FlowControlCommonService flowControlCommonService;
    @Mock
    private HighTempSamplingService highTempSamplingService;
    @Mock
    private WipScanHisExtraRepository wipScanHisExtraRepository;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private HrmUserInfoService hrmUserInfoService;
    @Mock
    private HttpServletResponse response;
    @Mock
    ObjectMapper mapperInstance;
    @Mock
    JsonNode json;
    @Mock
    WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    PsWorkOrderSnAssignRepository psWorkOrderSnAssignRepository;
    @Before
    public void before() throws Exception {
        FactoryConfig factoryConfig = new FactoryConfig();
        FieldUtils.writeField(factoryConfig, "commonEntityId", "56", true);
        FieldUtils.writeField(service, "factoryConfig", factoryConfig, true);
        PowerMockito.mockStatic(HrmUserInfoService.class,HttpServletResponse.class);
    }

    @Test
    public void testGetStringObjectMap() throws Exception {
        PsWipInfoQueryDTO dto = new PsWipInfoQueryDTO();
        Whitebox.invokeMethod(service, "getStringObjectMap", dto);
        Assert.assertTrue(true);
    }

    @Test
    public void testCheckSnList() throws Exception {
        PsWipInfoDTO dto = new PsWipInfoDTO();
        ArrayList<String> snList = new ArrayList<>();
        snList.add("709012300001");
        snList.add("709012300002");
        snList.add("709012300003");
        dto.setSnList(snList);
        Map<String, BasBarcodeInfo> basBarcodeInfoMap = new HashMap<>();
        basBarcodeInfoMap.put("709012300003",new BasBarcodeInfo());
        basBarcodeInfoMap.put("709012300004",new BasBarcodeInfo());
        BasBarcodeInfo basBarcodeInfo = new BasBarcodeInfo();
        basBarcodeInfo.setEntityName("111");
        basBarcodeInfoMap.put("709012300002",basBarcodeInfo);
        Map<String, PsWipInfo> psWipInfoMap = new HashMap<>();
        psWipInfoMap.put("709012300003",new PsWipInfo());
        psWipInfoMap.put("709012300004",new PsWipInfo());
        when(centerfactoryRemoteService.queryByTaskNos(anyList())).thenReturn(Collections.emptyList());
        Whitebox.invokeMethod(service, "checkSnList", dto,basBarcodeInfoMap,psWipInfoMap);
        Assert.assertTrue(true);
    }

    @Test
    public void testBarcodeVerificationForReworkTask() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,ObtainRemoteServiceDataUtil.class,CommonUtils.class,MessageId.class);
        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setItemNo("129206751186AJB");
        ArrayList<String> snList = new ArrayList<>();
        snList.add("709012300001");
        dto.setSnList(snList);
        Map<String, Map<String, String>> mountTypeMap = new HashMap<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("lookupMeaning","1");
        mountTypeMap.put("1",map);
        when(ObtainRemoteServiceDataUtil.getLookupTypeByType(anyString(),anyString())).thenReturn(mountTypeMap);
        List<PsWipInfo> psWipList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("709012300001");
        psWipInfo.setItemNo("129206751186AJB");
        psWipInfo.setAttribute2("ZZZNJ-5G-WS20250411119-MK");
        psWipList.add(psWipInfo);
        List<List<Object>> splitList = new ArrayList<>();
        splitList.add(Collections.singletonList("709012300001"));
        when(CommonUtils.splitList(anyList(), anyInt())).thenReturn(splitList);
        when(psWipInfoRepository.getListByBatchSn(anyList())).thenReturn(psWipList);
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = new ArrayList<>();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("ZZZNJ-5G-WS20250411119-MK");
        psTaskExtendedDTO.setCustomerNo("111");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        when(centerfactoryRemoteService.queryByTaskNos(anyList())).thenReturn(psTaskExtendedDTOS);
        List<SysLookupTypesDTO> typesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("111");
        typesDTOList.add(sysLookupTypesDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(typesDTOList);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.ALIBABA_CUSTOMER_TASKS_NOT_ALLOW_IN_IMES);
        try {
            service.barcodeVerificationForReworkTask(dto);
        }catch (MesBusinessException e){
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void testBarcodeVerificationForReworkTask2() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,ObtainRemoteServiceDataUtil.class,CommonUtils.class,MessageId.class);
        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setItemNo("129206751186AJB");
        ArrayList<String> snList = new ArrayList<>();
        snList.add("709012300001");
        dto.setSnList(snList);
        Map<String, Map<String, String>> mountTypeMap = new HashMap<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("lookupMeaning","1");
        mountTypeMap.put("1",map);
        when(ObtainRemoteServiceDataUtil.getLookupTypeByType(anyString(),anyString())).thenReturn(mountTypeMap);
        List<PsWipInfo> psWipList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("709012300001");
        psWipInfo.setItemNo("129206751186AJB");
        psWipInfo.setAttribute2("ZZZNJ-5G-WS20250411119-MK");
        psWipList.add(psWipInfo);
        List<List<Object>> splitList = new ArrayList<>();
        splitList.add(Collections.singletonList("709012300001"));
        when(CommonUtils.splitList(anyList(), anyInt())).thenReturn(splitList);
        when(psWipInfoRepository.getListByBatchSn(anyList())).thenReturn(psWipList);
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = new ArrayList<>();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("ZZZNJ-5G-WS20250411119-MK");
        psTaskExtendedDTO.setCustomerNo("111");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        when(centerfactoryRemoteService.queryByTaskNos(anyList())).thenReturn(psTaskExtendedDTOS);
        List<SysLookupTypesDTO> typesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("11");
        typesDTOList.add(sysLookupTypesDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(typesDTOList);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.ALIBABA_CUSTOMER_TASKS_NOT_ALLOW_IN_IMES);
        try {
            service.barcodeVerificationForReworkTask(dto);
        }catch (MesBusinessException e){
            Assert.assertNotNull(e);
        }
    }

    /* Started by AICoder, pid:y8e29773c14e39d14964087620a6a26c7a3907eb */
    @Test
    public void getExternalType() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        String sn = "";
        String externalType = service.getExternalType(sn);
        Assert.assertTrue(StringUtils.isBlank(externalType));
        sn = "123";
        externalType = service.getExternalType(sn);
        List<PsWipInfo> listWip = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setWorkOrderNo("123");
        listWip.add(a1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any()))
                .thenReturn(listWip);
        externalType = service.getExternalType(sn);

        PsWorkOrderDTO basic = new PsWorkOrderDTO();
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(Mockito.anyString()))
                .thenReturn(basic);
        service.getExternalType(sn);

        basic.setExternalType("123");
        service.getExternalType(sn);

    }
    /* Ended by AICoder, pid:y8e29773c14e39d14964087620a6a26c7a3907eb */

    @Test
    public void testSelectWipInfoByBatch(){
        Set<String> prodPlanIds = new HashSet<>();
        service.selectWipInfoByBatch(prodPlanIds);
        prodPlanIds.add("1");
        when(psWipInfoRepository.selectWipInfoByBatch(any())).thenReturn(null);
        service.selectWipInfoByBatch(prodPlanIds);
        Assert.assertTrue(true);
    }

    @Test
    public void checkList() throws Exception {
        List list = new ArrayList();
        service.checkList(null);
        Assert.assertNotNull(list);
        service.checkList(list);
        Assert.assertNotNull(list);
        for (int i = 0; i < 2002; i++) {
            list.add("1");
        }
        service.checkList(list);
        Assert.assertNotNull(list);
    }
    @Test
    public void checkSnWarehouse() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setAttribute3("Warehouse");
        List<String> sns = new ArrayList<>();
        dto.setSnList(sns);
        service.checkSnWarehouse(dto);

        sns.add("111111111111111111111111111111");
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoBySn(anyList()))
                .thenReturn(Lists.newArrayList(new ContainerContentInfoDTO() {{
                            setAttribute3("Warehouse");
                            setEntityIdentification("222");
                        }})
                );
        try {
            service.checkSnWarehouse(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_NOT_IN_WAREHOUSE);
        }
    }

    @Test
    public void testCheckzsAccess() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class);
        when(centerfactoryRemoteService.isHomeTerminal(anyString())).thenReturn(true);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO() {{
                            setLookupMeaning("Y");
                            setDescriptionChin("s");
                            setLookupCode(new BigDecimal(MpConstant.LOOKUP_VALUE_DIP_FINISH));
                        }}));
        String getresult = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{}],\"other\":{\"msg\":\"操作成功\"," +
                "\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.PsWipInfoController@snQuery\",\"code\":\"0000\",\"costTime\":\"73ms\"," +
                "\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Dec 29 11:03:08 CST 2020\",\"tag\":\"报表查询-条码追溯中查询条码在制信息\"," +
                "\"serviceName\":\"zte-mes-manufactureshare-productionmgmtsys\",\"userId\":\"10238004\"}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString()
                ,Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyMap())).thenReturn(getresult);
        Pair<Boolean, String> result = service.checkzsAccess("factoryId", "workOrderNo");

        when(centerfactoryRemoteService.isHomeTerminal(anyString())).thenReturn(false);
        Assert.assertNotNull(service.checkzsAccess("factoryId", "workOrderNo"));
    }

    @Test
    public void checkSnTaskRel() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);

        PsWipInfoDTO dto = new PsWipInfoDTO();
        service.checkSnTaskRel(dto);
        dto.setWorkOrderNo("WorkOrderNo");
        dto.setAttribute4("WorkOrderNo");
        service.checkSnTaskRel(dto);
        List<String> sns = new ArrayList<>();
        dto.setSnList(sns);
        sns.add("111111111111111111111111111111");
        PowerMockito.when(PlanscheduleRemoteService.getSnTaskRel(anyList()))
                .thenReturn(Lists.newArrayList(new SnTaskRelDTO() {{
                            setSn("111111111111111111111111111111");
                            setTaskNo("222");
                        }})
                );
        try {
            service.checkSnTaskRel(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_HAS_REL_TASK);
        }
    }

    @Test
    public void snDeduplication() {
        service.snDeduplication(new PcProcessTransferDTO());
        service.snDeduplication(new PcProcessTransferDTO(){{setWipList(Lists.newArrayList(new PsWipInfo(){{setSn("1");}}));}});
        service.snDeduplication(new PcProcessTransferDTO(){{setWipList(Lists.newArrayList(new PsWipInfo(){{setSn("1");}},new PsWipInfo(){{setSn("1");}}));}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void batchSelectTaskNoWhetherHasSnTest () {
        List<String> hasInfoExeProdList = new ArrayList<>();
        service.batchSelectTaskNoWhetherHasSn(hasInfoExeProdList);
        hasInfoExeProdList.add("test");

        Set<String> resutlSet = new HashSet<>();
        PowerMockito.when(psWipInfoRepository.batchSelectTaskNoWhetherHasSn(Mockito.anyList())).thenReturn(resutlSet);
        Assert.assertNotNull(service.batchSelectTaskNoWhetherHasSn(hasInfoExeProdList));
    }

    @Test
    public void updatePsWipInfoBySnBatch() {
        List<PsWipInfo> psWipInfos=new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setFactoryId(new BigDecimal("52"));
        psWipInfo.setSn("888877700001");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.updatePsWipInfoBySnBatch(Mockito.anyList())).thenReturn(2);

        Assert.assertEquals(2,service.updatePsWipInfoBySnBatch(psWipInfos));
    }

    @Test
    public void updatePsWipInfoByScanBatch() {
        List<PsWipInfo> psWipInfos=new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setFactoryId(new BigDecimal("52"));
        psWipInfo.setSn("888877700001");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.updatePsWipInfoByScanBatch(Mockito.anyList())).thenReturn(2);
        Assert.assertNotNull(service.updatePsWipInfoByScanBatch(psWipInfos));
    }

    @Test
    public void getCurrProcessCodeBySourceTask() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<String> prodplanIds = new ArrayList<>();
        Assert.assertNotNull(service.getCurrProcessCodeBySourceTask(prodplanIds));
        prodplanIds.add("test");
        List<SysLookupTypesDTO> a1List = new ArrayList<>();
        SysLookupTypesDTO a1 = new SysLookupTypesDTO();
        a1.setLookupMeaning("P0007");
        a1List.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(a1List);
        PowerMockito.when(psWipInfoRepository.getCurrProcessCodeBySourceTask(Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getCurrProcessCodeBySourceTask(prodplanIds));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getCurrProcessCodeBySourceTask(prodplanIds));
    }

    @Test
    public void getWorkOrderList() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,MicroServiceRestUtil.class,JacksonJsonConverUtil.class);
        List<PsWorkOrderDTO> psentityplanbasic = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO1 =new PsWorkOrderDTO();
        psWorkOrderDTO1.setRouteId("test123");
        psWorkOrderDTO1.setRemark("1");
        psWorkOrderDTO1.setCreateBy("test123");
        psWorkOrderDTO1.setItemName("test123");
        psWorkOrderDTO1.setItemNo("test123");
        psWorkOrderDTO1.setWorkOrderId("test123");
        psWorkOrderDTO1.setWorkOrderNo("test123");
        psWorkOrderDTO1.setWorkOrderQty(new BigDecimal("1"));
        psWorkOrderDTO1.setInputQty(new BigDecimal("1"));
        psWorkOrderDTO1.setOutputQty(new BigDecimal("1"));
        psWorkOrderDTO1.setProdplanId("test123");
        psWorkOrderDTO1.setTaskNo("test123");
        psWorkOrderDTO1.setWorkOrderStatus("2");
        psWorkOrderDTO1.setCraftSection("test124");
        PsWorkOrderDTO psWorkOrderDTO2 =new PsWorkOrderDTO();
        psWorkOrderDTO2.setRouteId("test123");
        psWorkOrderDTO2.setCreateBy("test123");
        psWorkOrderDTO2.setItemName("test123");
        psWorkOrderDTO2.setItemNo("test123");
        psWorkOrderDTO2.setWorkOrderId("test123");
        psWorkOrderDTO2.setWorkOrderNo("test123");
        psWorkOrderDTO2.setWorkOrderQty(new BigDecimal("1"));
        psWorkOrderDTO2.setInputQty(new BigDecimal("1"));
        psWorkOrderDTO2.setOutputQty(new BigDecimal("1"));
        psWorkOrderDTO2.setProdplanId("test123");
        psWorkOrderDTO2.setTaskNo("test123");
        psWorkOrderDTO2.setWorkOrderStatus("2");
        psWorkOrderDTO2.setCraftSection("test123");
        psentityplanbasic.add(psWorkOrderDTO1);
        psentityplanbasic.add(psWorkOrderDTO2);
        List<CtRouteDetailDTO> ctRouteDetailList =new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO =new CtRouteDetailDTO();
        ctRouteDetailDTO.setCraftSection("test123");
        ctRouteDetailDTO.setCurrProcess("test123");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailList.add(ctRouteDetailDTO);

        List<CtRouteHead> ctRouteHeads =new ArrayList<>();
        CtRouteHead ctRouteHead =new CtRouteHead();
        ctRouteHead.setCraftSection("test123");
        ctRouteHead.setRouteId("test123");
        ctRouteHead.setRouteDetail("test123");
        ctRouteHeads.add(ctRouteHead);
        String getresult = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[],\"other\":{\"msg\":\"操作成功\"," +
                "\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.PsWipInfoController@snQuery\",\"code\":\"0000\",\"costTime\":\"73ms\"," +
                "\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Dec 29 11:03:08 CST 2020\",\"tag\":\"报表查询-条码追溯中查询条码在制信息\"," +
                "\"serviceName\":\"zte-mes-manufactureshare-productionmgmtsys\",\"userId\":\"10238004\"}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any()
                ,Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyMap())).thenReturn(getresult);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("[]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),Mockito.any())).thenReturn(ctRouteHeads);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.anyList())).thenReturn(ctRouteDetailList);
        Assert.assertNotNull(service.getWorkOrderList(psentityplanbasic));
    }

    @Test
    public void setWipProcessName() {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(
                Lists.newArrayList(new BSProcess(){{setProcessName("1");setProcessCode("1");}}));
        service.setWipProcessName(Lists.newArrayList(new PsWipInfo(){{setCurrProcessCode("1");}}));
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(null);
        service.setWipProcessName(Lists.newArrayList(new PsWipInfo(){{setCurrProcessCode("1");}}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void  queryWipSnBatch(){
        List<String> snList = new LinkedList<>();
        snList.add("123");
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        list.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList()))
                .thenReturn(list);
        service.queryWipSnBatch(snList);

        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList()))
                .thenReturn(null);
        Assert.assertNotNull(service.queryWipSnBatch(snList));
    }

    @Test
    public void getUnStoredVirtuallySns() {
        PowerMockito.when(psWipInfoRepository.getUnStoredVirtuallySns(any(), any(), any())).thenReturn(null);
        VirtuallySnQueryDTO dto = new VirtuallySnQueryDTO();
        Assert.assertNull(service.getUnStoredVirtuallySns(dto));
    }


    @Test
    public void snInfoExport () throws Exception {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class,CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        PsWipInfoSnQueryDTO dtoForm = new PsWipInfoSnQueryDTO();
        dtoForm.setSn("123");
        List<PsWipInfo> list = new ArrayList<>();
        PsWipInfo info = new PsWipInfo();
        info.setSn("123");
        list.add(info);
        List<PsWipInfoSnQueryDTO> returnList = new ArrayList<>();
        returnList.add(dtoForm);
        PowerMockito.when(psWipInfoRepository.getPage(Mockito.any())).thenReturn(returnList);
        List<BsPubHrvOrgId> tempList = new ArrayList<>();
        BsPubHrvOrgId ermInfo = new BsPubHrvOrgId();
        ermInfo.setOrgCnName("gy");
        tempList.add(ermInfo);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyString())).thenReturn(tempList);

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);

        List<ContainerContentInfoDTO> contentList = new ArrayList<ContainerContentInfoDTO>();
        ContainerContentInfoDTO contentInfoDTO = new ContainerContentInfoDTO();
        contentInfoDTO.setAttribute1("133");
        contentList.add(contentInfoDTO);
        try {
            service.snInfoExport(dtoForm, null);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
    /* Started by AICoder, pid:01e2e635ecbd41c14f200887e02e47467c211418 */
    @Test
    public void snInfoEmailExport2 () throws Exception {
        ReflectionTestUtils.setField(service, "barcodeExportSwitch", "Y");
        PsWipInfoSnQueryDTO psWipInfoSnQueryDTO = new PsWipInfoSnQueryDTO();
        Whitebox.invokeMethod(service,"snInfoEmailExport",psWipInfoSnQueryDTO,new RedisLock("2"));
        Assert.assertNotNull(psWipInfoSnQueryDTO);
        ReflectionTestUtils.setField(service, "barcodeExportSwitch", "N");
        Whitebox.invokeMethod(service,"snInfoEmailExport",psWipInfoSnQueryDTO,new RedisLock("2"));
        Assert.assertNotNull(psWipInfoSnQueryDTO);

    }
    /* Ended by AICoder, pid:01e2e635ecbd41c14f200887e02e47467c211418 */
    @Test
    public void snInfoEmailExport () throws Exception {
        ReflectionTestUtils.setField(service, "barcodeExportSwitch", "Y");
        PsWipInfoSnQueryDTO dtoForm = new PsWipInfoSnQueryDTO();
        PowerMockito.mockStatic(RedisHelper.class, MESHttpHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(false);
        try {
            service.snInfoEmailExport(dtoForm, "10313234");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.Z_MAIL_EXPORT_SN_ING.equals(e.getMessage()));
        }
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Map<String,String>  headerMap=new HashMap<>();
        headerMap.put("X-Factory-Id","55");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        dtoForm.setSn("123");
        List<PsWipInfo> list = new ArrayList<>();
        PsWipInfo info = new PsWipInfo();
        info.setSn("123");
        list.add(info);
        PowerMockito.when(psWipInfoRepository.getPage(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            dtoForm.setEmailUrl("<EMAIL>");
            service.snInfoEmailExport(dtoForm, null);
        } catch (Exception e) {}
    }

    @Test
    public void checkSn4UnLinkSnAndTask() throws Exception {
        List<String> snList = new ArrayList<>();
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.add("11");
        snList.addAll(snList);
        snList.addAll(snList);
        snList.addAll(snList);
        Whitebox.invokeMethod(service, "checkSn4UnLinkSnAndTask", snList);
        snList.clear();
        snList.add("11");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "checkSn4UnLinkSnAndTask", snList));
    }

    @Test
    public void batchPassTypeThree() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        entity.setBoxNoOrContent("test123");
        List<ContainerContentInfoDTO> containerContentInfoDTOS = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setLpn("test222");
        containerContentInfoDTO.setEntityIdentification("test123");
        containerContentInfoDTOS.add(containerContentInfoDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.anyMap())).thenReturn(containerContentInfoDTOS);
        List<PsWipInfo> listByBatchSn = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        listByBatchSn.add(psWipInfo);
        try {
            service.batchPassTypeThree(entity, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.any())).thenReturn(listByBatchSn);
        try {
            service.batchPassTypeThree(entity, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfo.setSn("test343");
        try {
            service.batchPassTypeThree(entity, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void allContainContent() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        entity.setBoxNoOrContent("test123");
        List<ContainerContentInfoDTO> containerContentInfoDTOS = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setLpn("test222");
        containerContentInfoDTOS.add(containerContentInfoDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.anyMap())).thenReturn(new ArrayList<>()).thenReturn(containerContentInfoDTOS);
        try {
            service.allContainContent(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.anyMap())).thenReturn(new ArrayList<>()).thenReturn(new ArrayList<>());
        try {
            service.allContainContent(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.anyMap())).thenReturn(containerContentInfoDTOS).thenReturn(new ArrayList<>());
        try {
            service.allContainContent(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.anyMap())).thenReturn(containerContentInfoDTOS).thenReturn(containerContentInfoDTOS);
        try {
            service.allContainContent(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getPsWipInfo() {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        entity.setBatchPassType("3");
        try {
            service.getPsWipInfo(entity,new ArrayList<>(),new ArrayList<>(),new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

	@Test
	public void pmBatchContinuousPassScan() throws Exception {
        ReflectionTestUtils.setField(service, "qcSamplingFlag", false);
		PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class);
		PmSubmitConditionDTO pmSubmitConditionDTO = new PmSubmitConditionDTO();
		List<String> sns = new ArrayList<>();
		sns.add("701115300004");
		List<CtRouteDetailDTO> ctRouteDetailInfo = new ArrayList<>();
		CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
		ctRouteDetailDTO.setItemNo("ICT-HY2");
		ctRouteDetailDTO.setNextProcess("198");
		ctRouteDetailDTO.setSourceImu(new BigDecimal("505"));
		ctRouteDetailDTO.setSourceBimu(new BigDecimal("11"));
		ctRouteDetailDTO.setColName("D3");
		ctRouteDetailDTO.setSourceSysName("在线测试-HY2");
		ctRouteDetailDTO.setProcessCode("C");
		ctRouteDetailInfo.add(ctRouteDetailDTO);

		pmSubmitConditionDTO.setBatchPassType("4");
		pmSubmitConditionDTO.setSourceSys(Constant.AUTO_BATCH_PASS);
		pmSubmitConditionDTO.setCreateBy("00286569");
		pmSubmitConditionDTO.setLastUpdatedBy("00286569");
		pmSubmitConditionDTO.setWorkOrderNo("7011153-ICT5504");
		pmSubmitConditionDTO.setLineCode("ICT-HY2");
		pmSubmitConditionDTO.setCtRouteDetailList(ctRouteDetailInfo);
		pmSubmitConditionDTO.setWorkStation("198");
		pmSubmitConditionDTO.setSourceImu(new BigDecimal("505"));
		pmSubmitConditionDTO.setSourceBimu(new BigDecimal("11"));
		pmSubmitConditionDTO.setColName("D3");
		pmSubmitConditionDTO.setSourceSysName("在线测试-HY2");
		pmSubmitConditionDTO.setCurrProcessCode("C");
		pmSubmitConditionDTO.setSnList(sns);

		List<PsEntityPlanBasicDTO> psEntityPlanInfo = new LinkedList<>();
		PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
		a1.setAttribute1("122");
		a1.setSourceTask("77011153");
		a1.setWorkOrderNo("77011153-ICT5504");
		a1.setCraftSection("ICT");
		a1.setProcessGroup("C");
		a1.setWorkOrderStatus("已提交");
		a1.setWorkOrderQty(new BigDecimal(20));
		psEntityPlanInfo.add(a1);
		PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
				.thenReturn(psEntityPlanInfo);
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("已提交");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(LOOKUP_VALUE_6691, LOOKUP_VALUE_6691001))
				.thenReturn(sysLookupTypesDTO);
		try{
			service.pmBatchContinuousPassScan(pmSubmitConditionDTO);
		}catch(Exception e){
			Assert.assertNull(e.getMessage());
		}
		try{
			PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList()))
					.thenReturn(null);
			service.pmBatchContinuousPassScan(pmSubmitConditionDTO);
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}

		try{
			List<PsWipInfo> listByBatchSn = new ArrayList<>();
			PsWipInfo ps = new PsWipInfo();
			ps.setSn("701115300004");
			listByBatchSn.add(ps);
			PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList()))
					.thenReturn(listByBatchSn);
			service.pmBatchContinuousPassScan(pmSubmitConditionDTO);
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}
	}

	@Test
	public void batchSaveWipInfo(){
		List<FlowControlConditionDTO> list = new ArrayList<>();
		FlowControlConditionDTO flowControlConditionDTO = new FlowControlConditionDTO();
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setOpeTimes(new BigDecimal(1));
		flowControlConditionDTO.setWipInfo(wipInfo);
		flowControlConditionDTO.setBatchPassType("4");
		list.add(flowControlConditionDTO);
		FlowControlInfoDTO controlResult = new FlowControlInfoDTO();
		controlResult.setWipInfo(wipInfo);
		try {
			Whitebox.invokeMethod(service, "batchSaveWipInfo", list,controlResult,100);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void batchSaveWipInfoForPassScan() {
		List<FlowControlConditionDTO> list = new ArrayList<>();
		FlowControlConditionDTO flowControlConditionDTO = new FlowControlConditionDTO();
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setOpeTimes(new BigDecimal(1));
		flowControlConditionDTO.setWipInfo(wipInfo);
		list.add(flowControlConditionDTO);
		FlowControlInfoDTO controlResult = new FlowControlInfoDTO();
		PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
		a1.setAttribute1("122");
		a1.setSourceTask("77011153");
		a1.setWorkOrderNo("77011153-ICT5504");
		a1.setCraftSection("ICT");
		a1.setProcessGroup("C");
		a1.setWorkOrderStatus("已提交");
		a1.setWorkOrderQty(new BigDecimal(20));
		a1.setInputQty(new BigDecimal(10));
		a1.setOutputQty(new BigDecimal(10));
		controlResult.setWipInfo(wipInfo);
		List<CtRouteDetailDTO> ctRouteDetailInfo = new ArrayList<>();
		CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
		ctRouteDetailDTO.setItemNo("ICT-HY2");
		ctRouteDetailDTO.setNextProcess("505");
		ctRouteDetailDTO.setSourceImu(new BigDecimal("505"));
		ctRouteDetailDTO.setSourceBimu(new BigDecimal("11"));
		ctRouteDetailDTO.setColName("D3");
		ctRouteDetailDTO.setSourceSysName("在线测试-HY2");
		ctRouteDetailDTO.setProcessCode("C");
		ctRouteDetailDTO.setLastProcess("N");
		ctRouteDetailInfo.add(ctRouteDetailDTO);
		controlResult.setCtRouteDetailList(ctRouteDetailInfo);
		controlResult.setOperation("UPDATE");
		PowerMockito.when(flowControlCommonService.checkZLLastProcess(any(), any())).thenReturn(false);
		PowerMockito.when(psWipInfoRepository.updatePsWipInfoByScanBatch(Mockito.anyList())).thenReturn(1);
		PowerMockito.when(psScanHistoryService.insertPsScanHistoryByScanBatch(any(), anyInt())).thenReturn(1);
		try {
			Whitebox.invokeMethod(service, "batchSaveWipInfoForPassScan", list,controlResult,100);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		controlResult.setEntityPlanBasic(a1);

		try {
			Whitebox.invokeMethod(service, "batchSaveWipInfoForPassScan", list,controlResult,100);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

    @Test
    public void saveWip() {
        PowerMockito.mockStatic(CommonUtils.class,MessageId.class);
        try {
            service.saveWip(new ArrayList<>(),1,new SnCheckControlResultDTO(),null,new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        FlowControlConditionDTO flowControlConditionDTO = new FlowControlConditionDTO();
        flowControlConditionDTO.setSn("test123");
        flowControlConditionDTO.setReScanFlag(true);
        List<FlowControlConditionDTO> list = new ArrayList<>();
        list.add(flowControlConditionDTO);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString(),(String)Mockito.any())).thenReturn("error");
        try {
            service.saveWip(new ArrayList<>(),1,new SnCheckControlResultDTO(),new FlowControlInfoDTO(),list);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void needReFollowControl() {
        try {
            service.needReFollowControl(true,false);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void setSourceSysName() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getLineBodyModelingData(Mockito.any(),Mockito.any())).thenReturn(ctRouteDetailDTOList);
        try {
            service.setSourceSysName(new PcProcessTransferDTO(),new FlowControlInfoDTO());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setSourceSysName("test123");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        try {
            service.setSourceSysName(new PcProcessTransferDTO(),new FlowControlInfoDTO());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void filterRetentionList() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,CrafttechRemoteService.class);
        try {
            service.filterRetentionList(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        psWipInfo.setWorkOrderNo("test123");
        psWipInfoList.add(psWipInfo);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("test123");
        psWorkOrderBasic.setRouteId("test123");
        workOrderList.add(psWorkOrderBasic);

        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("N");
        ctRouteDetailDTO.setRouteId("test123");
        ctRouteDetailDTO.setCurrProcess("last");
        ctRouteDetailDTO.setRemainTime(new BigDecimal("1"));
        ctRouteDetailDTOList.add(ctRouteDetailDTO);

        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setNextProcess("Y");
        ctRouteDetailDTO1.setRouteId("test123");
        ctRouteDetailDTO1.setCurrProcess("now");
        ctRouteDetailDTO1.setRemainTime(new BigDecimal("1"));
        ctRouteDetailDTOList.add(ctRouteDetailDTO1);

        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        ctRouteDetailDTO2.setNextProcess("N");
        ctRouteDetailDTO2.setRouteId("test123");
        ctRouteDetailDTO2.setCurrProcess("last");
        ctRouteDetailDTO2.setRemainTime(null);
        ctRouteDetailDTOList.add(ctRouteDetailDTO2);

        CtRouteDetailDTO ctRouteDetailDTO3 = new CtRouteDetailDTO();
        ctRouteDetailDTO3.setNextProcess("last");
        ctRouteDetailDTO3.setRouteId("test123");
        ctRouteDetailDTO3.setCurrProcess("123");
        ctRouteDetailDTO3.setRemainTime(new BigDecimal("1"));
        ctRouteDetailDTOList.add(ctRouteDetailDTO3);

        List<WipScanHistory> snDateTempList = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("test123");
        long oneHour = 3 * 60 * 60 * 1000;
        Date oneHourAgo = new Date(new Date().getTime() - oneHour);
        wipScanHistory.setCreateDate(oneHourAgo);
        snDateTempList.add(wipScanHistory);

        WipScanHistory wipScanHistory1 = new WipScanHistory();
        wipScanHistory1.setSn("test123");
        wipScanHistory1.setCreateDate(null);
        snDateTempList.add(wipScanHistory1);

        WipScanHistory wipScanHistory2 = new WipScanHistory();
        wipScanHistory2.setSn("test123");
        wipScanHistory2.setCreateDate(new Date());
        snDateTempList.add(wipScanHistory2);

        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.anyMap())).thenReturn(workOrderList);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.any())).thenReturn(ctRouteDetailDTOList);
        PowerMockito.when(wipScanHistoryRepository.getBatchTimeIntervalBySnList(Mockito.anyList(),Mockito.anyString())).thenReturn(snDateTempList);

        try {
            service.filterRetentionList(psWipInfoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.anyMap())).thenReturn(null);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.any())).thenReturn(ctRouteDetailDTOList);
        PowerMockito.when(wipScanHistoryRepository.getBatchTimeIntervalBySnList(Mockito.anyList(),Mockito.anyString())).thenReturn(snDateTempList);

        try {
            service.filterRetentionList(psWipInfoList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),WORKORDER_NOT_FIND);
        }

    }

  	@Test
  	public void getPackScanBussDTO() throws Exception {
	  PowerMockito.mockStatic(CrafttechRemoteService.class);
	  PackScanBussDTO dto = new PackScanBussDTO();
	  dto.setDhomeFlag(false);
	  dto.setIsFisrtScan("N");
	  dto.setFistScanWorkorderNo("TEST1");
	  List<PsWipInfo> list = new ArrayList<>();
	  PsWipInfo psWipInfo = new PsWipInfo();
	  psWipInfo.setCurrProcessCode("P0238");
	  psWipInfo.setWorkOrderNo("TEST2");
	  list.add(psWipInfo);
	  BSProcess bsProcess = new BSProcess();
	  bsProcess.setProcessName("DIP");
	  bsProcess.setProcessCode("P0237");
	  PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(bsProcess);
	  Assert.assertNotNull(service.getPackScanBussDTO(dto, list));
	  BSProcess bsProcess1 = new BSProcess();
	  bsProcess1.setProcessName("DIP");
	  bsProcess1.setProcessCode("P0238");
	  PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(bsProcess1);
	  Assert.assertNotNull(service.getPackScanBussDTO(dto, list));
  	}
    @Test
    public void packScanOfNoStartWithCT() throws Exception {
        PackScanBussDTO packScanBussDTO = new PackScanBussDTO();
        packScanBussDTO.setAssemblyFlag(true);
        packScanBussDTO.setBarcode("BTL001");
        Assert.assertNotNull(service.packScanOfNoStartWithCT(packScanBussDTO));
        packScanBussDTO.setAssemblyFlag(true);
        packScanBussDTO.setBarcode("BT001");
        Assert.assertNotNull(service.packScanOfNoStartWithCT(packScanBussDTO));
        packScanBussDTO.setAssemblyFlag(false);
        packScanBussDTO.setBarcode("BT001");
        Assert.assertNotNull(service.packScanOfNoStartWithCT(packScanBussDTO));
        packScanBussDTO.setAssemblyFlag(false);
        packScanBussDTO.setBarcode("BT001");
        Assert.assertNotNull(service.packScanOfNoStartWithCT(packScanBussDTO));
    }
    @Test
    public void packScanWithFlowControlCheck() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class,CommonUtils.class);
        MdsPackScanDTO param = new MdsPackScanDTO();
        param.setBarcode("788829000011");
        param.setSelectProcessCode("P0238");
        param.setWorkStation("P0234c");
        param.setWorkorderNo("7888290-DIP5203");
        param.setLineCode("DIP6");
        param.setFactoryId("52");
        param.setLastUpdatedBy("00286569");

        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO() {{
                            setLookupMeaning("http://server.hemans.zte.com.cn:42001/api/bll/dip/scan");
                            setDescriptionChin("http://server.hemans.zte.com.cn:42001/api/bll/dip/scan");
                            setLookupCode(new BigDecimal("1028"));
                        }}));

        ServiceData serviceData = new ServiceData<>();
        List<PsWorkOrderDTO> list = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("7888290-DIP5203");
        psWorkOrderDTO.setItemNo("129571751052ZRD");
        psWorkOrderDTO.setCraftSection("DIP");
        psWorkOrderDTO.setExternalType("DHOME");
        list.add(psWorkOrderDTO);
        serviceData.setBo(list);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(centerfactoryRemoteService.isHomeTerminal(Mockito.anyString())).thenReturn(true);

        try{
            List<SysLookupValuesDTO> valueByTypeCodes1 = new LinkedList<>();
            SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
            sysLookupValuesDTO1.setLookupMeaning("DHOME");
            sysLookupValuesDTO1.setDescriptionEng("123");
            valueByTypeCodes1.add(sysLookupValuesDTO1);
            PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_PRODUCT_BIG_TYPE))
                    .thenReturn(valueByTypeCodes1);
            PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.EXTER_TYPE_ERR);
            service.packScanWithFlowControlCheck(param);
        } catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.EXTER_TYPE_ERR);
        }

        List<SysLookupValuesDTO> valueByTypeCodes = new LinkedList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("DHOME");
        sysLookupValuesDTO.setDescriptionEng("DHOME");
        valueByTypeCodes.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_PRODUCT_BIG_TYPE))
                .thenReturn(valueByTypeCodes);

        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(new ArrayList<>());
        try{
            service.packScanWithFlowControlCheck(param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.WORDER_ORDER_NOT_FOUND);
        }
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setTaskNo("139571750000ZTA");
        a1.setItemNo("129571751052ZRD");
        a1.setWorkOrderNo("7888290-DIP5203");
        psEntityPlanInfo.add(a1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(psEntityPlanInfo);
        HighTempQueryDTO highTempQueryDTO = new HighTempQueryDTO();
        PowerMockito.when(highTempSamplingService.getSnValidInfo(Mockito.any()))
                .thenReturn(highTempQueryDTO);
        Assert.assertNotNull(service.packScanWithFlowControlCheck(param));
        highTempQueryDTO.setSn("788829000011");
        PowerMockito.when(highTempSamplingService.getSnValidInfo(Mockito.any()))
                .thenReturn(highTempQueryDTO);
        Assert.assertNotNull(service.packScanWithFlowControlCheck(param));
        highTempQueryDTO.setEnabledFlag("Y");
        PowerMockito.when(highTempSamplingService.getSnValidInfo(Mockito.any()))
                .thenReturn(highTempQueryDTO);
        Assert.assertNotNull(service.packScanWithFlowControlCheck(param));
    }

    @Test
    public void packScanWithFlowControlCheckTwo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class,CommonUtils.class);
        MdsPackScanDTO param = new MdsPackScanDTO();
        param.setBarcode("788829000011");
        param.setSelectProcessCode("P0238");
        param.setWorkStation("P0234c");
        param.setWorkorderNo("7888290-DIP5203");
        param.setLineCode("DIP6");
        param.setFactoryId("52");
        param.setLastUpdatedBy("00286569");

        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO() {{
                            setLookupMeaning("http://server.hemans.zte.com.cn:42001/api/bll/dip/scan");
                            setDescriptionChin("http://server.hemans.zte.com.cn:42001/api/bll/dip/scan");
                            setLookupCode(new BigDecimal("1028"));
                        }}));

        ServiceData serviceData = new ServiceData<>();
        List<PsWorkOrderDTO> list = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("7888290-DIP5203");
        psWorkOrderDTO.setItemNo("129571751052ZRD");
        psWorkOrderDTO.setCraftSection("DIP");
        psWorkOrderDTO.setExternalType("DHOME");
        list.add(psWorkOrderDTO);
        serviceData.setBo(list);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(centerfactoryRemoteService.isHomeTerminal(Mockito.anyString())).thenReturn(true);

        List<SysLookupValuesDTO> valueByTypeCodes1 = new LinkedList<>();
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupMeaning("DHOME");
        sysLookupValuesDTO1.setDescriptionEng("CPE");
        sysLookupValuesDTO1.setDescriptionChin("CPE");
        valueByTypeCodes1.add(sysLookupValuesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_PRODUCT_BIG_TYPE))
                .thenReturn(valueByTypeCodes1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(new ArrayList<>());
        try{
            service.packScanWithFlowControlCheck(param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.WORDER_ORDER_NOT_FOUND);
        }
    }

    @Test
    public void createWipInfoBatch() throws Exception {
        //触发
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo");
        psTask.setTaskNo("test");
        psTask.setTaskQty(new BigDecimal(NumConstant.NUM_100));
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);

        List<SysLookupTypesDTO> list = new ArrayList<>();
        // 查询忽略不注册物料代码
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_UP_1290)).thenReturn(list);
        // 查询条码是否已存在
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);
        // 查询已注册条码数
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);
        CreateWipInfoBatchDTO dto = new CreateWipInfoBatchDTO();
        dto.setProdPlanId("7777666");
        dto.setPrintQty(NumConstant.NUM_TEN);
        dto.setStartNum(NumConstant.NUM_ONE);
        dto.setFactoryId("52");
        try{
            PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(), any())).thenReturn(new ArrayList<>());
            service.createWipInfoBatch(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_OF_PRDOPLANID_NOT_EXIST, e.getMessage());
        }
        // 校验批次对应指令是否存在
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("7777666-SMT-B5501");
        psWorkOrderDTO.setRemark("0");
        psWorkOrderDTO.setWorkOrderStatus(Constant.IS_SUBMITTED);
        workOrderList.add(psWorkOrderDTO);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(), any())).thenReturn(workOrderList);
        try{
            PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(any())).thenReturn(new ArrayList<>());
            service.createWipInfoBatch(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CT_ROUTE_DETAIL_OF_ITEMNO_NOT_EXIST, e.getMessage());
        }
    }

    @Test
    public void createWipInfoBatchTwo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo");
        psTask.setTaskNo("test-w");
        psTask.setTaskQty(new BigDecimal(NumConstant.NUM_100));
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);

        List<SysLookupTypesDTO> list = new ArrayList<>();
        // 查询忽略不注册物料代码
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_UP_1290)).thenReturn(list);
        // 查询条码是否已存在
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);
        // 查询已注册条码数
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);
        CreateWipInfoBatchDTO dto = new CreateWipInfoBatchDTO();
        dto.setProdPlanId("7777666");
        dto.setPrintQty(NumConstant.NUM_TEN);
        dto.setStartNum(NumConstant.NUM_ONE);
        dto.setFactoryId("52");
        PowerMockito.when(psWipInfoRepository.insertPsWipInfoBatch(anyObject())).thenReturn(1);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(anyObject())).thenReturn(1);
        service.createWipInfoBatch(dto);
        Assert.assertEquals("52", dto.getFactoryId());
    }

    @Test
    public void exceptionCases() {
        PackScanBussDTO resulDto = new PackScanBussDTO();
        MdsPackScanDTO param = new MdsPackScanDTO();
        try{
            PackScanBussDTO resulDto1 =null;
            Whitebox.invokeMethod(service, "exceptionCases", resulDto1, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.SN_VERIFICATION_FAILED);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_FIVE);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.SN_HAS_BEEN_PACKED_AND_CAN_NOT_BE_CHANGED);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_ONE);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.SN_IS_NOT_EXIST);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_TWO);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.CANNOT_MIX_PACKAGING);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_SEVEN);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.CURRENT_FACTORY_IS_NOT_CHANGSHA_FACTORY);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_ELEVEN);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.NOT_TESTED_OR_TEST_FAILED);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_TWELVE);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.NOT_TESTED_OR_TEST_FAILED);
        }
        resulDto.setScanMsg(MpConstant.SCANMSG_TEN);
        try{
            Whitebox.invokeMethod(service, "exceptionCases", resulDto, param);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.UNKNOWN_ERROR_FOR_SCAN);
        }
    }

    @Test
    public void getTaskNoBySns() throws Exception {
        try{
            service.getTaskNoBySns(new ArrayList<>());
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.SN_LIST_IS_NULL);
        }
        List<String> snList = new ArrayList<>();
        snList.add("121");
        PowerMockito.field(PsWipInfoServiceImpl.class, "snListMax").set(service, 1);
        try{
            service.getTaskNoBySns(snList);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.SN_LIST_EXCEED_MAX);
        }
    }

    @Test
    public void getMaxSnByProdplanId() {
        PowerMockito.when(psWipInfoRepository.checkExistOfProdplanId(Mockito.anyString())).thenReturn(0);
        int maxSnNum = service.getMaxSnByProdplanId("7777666");
        assert maxSnNum == 0;

        PowerMockito.when(psWipInfoRepository.checkExistOfProdplanId(Mockito.anyString())).thenReturn(1);
        PowerMockito.when(psWipInfoRepository.getMaxSnOfProdplanId(Mockito.anyString())).thenReturn("777766600011");
        maxSnNum = service.getMaxSnByProdplanId("7777666");
        assert maxSnNum == 11;
    }

    @Test
    public void checkRouteByWipInfo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("tese111");
        entity.setWipInfo(psWipInfo);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        try{
            service.checkRouteByWipInfo(entity);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException);
        }
        psWorkOrderBasic.setRouteId("test123");
        try{
            service.checkRouteByWipInfo(entity);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getList() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWipInfo> list = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        list.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(list);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("test123");
        psWorkOrderBasic.setRouteId("test123");
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.anyMap())).thenReturn(workOrderList);
        Map<String, Object> record = new HashMap<>();
        try{
            service.getList(record);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        workOrderList.add(psWorkOrderBasic);
        try{
            service.getList(record);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void setMbomTest() throws Exception {
        WorkOrderInfo dto = new WorkOrderInfo();
        dto.setProdplanId("7654321");
        List<WorkOrderInfo> list = new ArrayList<>();
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234567");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        list.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
        Assert.assertEquals("7654321", list.get(0).getMBomProductCode());
        dto.setProdplanId("7654311");
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
    }

    @Test
    public void bulkQueriesByTaskNos() throws Exception {
        //第一个if
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = service.bulkQueriesByTaskNos(Arrays.asList("880542130022240124701170600029"));
        Assert.assertNotNull(psTaskExtendedDTOS);
        //第二个if
        List<PsWipInfoDTO> psWipInfoBySns = new ArrayList<>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("123123");
        psWipInfoBySns.add(psWipInfoDTO);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(Mockito.any()))
                .thenReturn(psWipInfoBySns);

        List<PsTaskExtendedDTO> psTaskExtendedS = service.bulkQueriesByTaskNos(Arrays.asList("880542130022240124701170600029"));
        Assert.assertNotNull(psTaskExtendedS);

        // 成功返回
        List<PsTaskExtendedDTO> stringPsTaskExtendedDTOMap = new ArrayList<>();
        PsTaskExtendedDTO psTaskExtendedDTO1 = new PsTaskExtendedDTO();
        psTaskExtendedDTO1.setSn("123123");
        stringPsTaskExtendedDTOMap.add(psTaskExtendedDTO1);
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(Mockito.any()))
                .thenReturn(stringPsTaskExtendedDTOMap);
        // 准备测试数据
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("snList", Arrays.asList("880542130022240124701170600029"));

        // 模拟 ProductionmgmtRemoteService 的行为
        psWipInfoBySns.clear();
        psWipInfoDTO.setAttribute2("123123");
        psWipInfoDTO.setSn("123123");
        psWipInfoBySns.add(psWipInfoDTO);
        List<PsTaskExtendedDTO> psTaskExtendedDTO = service.bulkQueriesByTaskNos(Arrays.asList("880542130022240124701170600029"));

        // 断言结果
        assertNotNull(psTaskExtendedDTO);
    }

    @Test
    public void checkWorkOrderSnAssign() throws Exception {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        entity.setWorkOrderNo("1234567-SMT-A5201");
        List<PsEntityPlanBasicDTO> workOrderList = new ArrayList<>();
        PowerMockito.when(psWorkOrderSnAssignRepository.getPrintSnRecordCount(Mockito.any())).thenReturn(0L);
        Whitebox.invokeMethod(service, "checkWorkOrderSnAssign", entity, workOrderList, 10);
        Assert.assertEquals("1234567-SMT-A5201", entity.getWorkOrderNo());

        PowerMockito.when(psWorkOrderSnAssignRepository.getPrintSnRecordCount(Mockito.any())).thenReturn(10L);
        workOrderList.add(new PsEntityPlanBasicDTO(){{
            setInputQty(new BigDecimal(5));
        }});
        Whitebox.invokeMethod(service, "checkWorkOrderSnAssign", entity, workOrderList, 5);
        Assert.assertEquals(5, workOrderList.get(0).getInputQty().intValue());
        try{
            Whitebox.invokeMethod(service, "checkWorkOrderSnAssign", entity, workOrderList, 10);
        }catch(Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.BATCH_SCAN_LIMIT_EXCEED);
        }
    }
}
