package com.zte.application.impl;

import com.zte.application.DocFilePropertiesService;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.ApprovalConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.domain.model.DocFilePropertiesRepository;
import com.zte.domain.model.ScrapBillHeaderRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.DocFilePropertiesEntityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ScrapBillHeaderEntityDTO;
import com.zte.interfaces.dto.approval.ScrapFlowStartDTO;
import com.zte.iss.approval.sdk.bean.TaskVo;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ConstantInterface.class, RedisHelper.class, MicroServiceRestUtil.class,ApprovalFlowClient.class,
        MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class,RedisKeyConstant.class,
        ApprovalTaskClient.class, InterfaceEnum.class, ApprovalConstant.class,RedisLock.class,RedisLock.class,RedisHelper.class,CenterfactoryRemoteService.class})
public class ScrapBillHeaderServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ScrapBillHeaderServiceImpl service;

    @Mock
    private DocFilePropertiesServiceImpl docFilePropertiesService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ScrapBillHeaderRepository scrapBillHeaderrepository;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ConstantInterface constantInterface;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.mockStatic(ApprovalTaskClient.class);
        PowerMockito.mockStatic(RedisKeyConstant.class);
        PowerMockito.mockStatic(InterfaceEnum.class);
        PowerMockito.mockStatic(ApprovalConstant.class);
        PowerMockito.mockStatic(RedisLock.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
    }

    @Test
    public void setAttachment() throws Exception {
        DocFilePropertiesEntityDTO docFileTemp = new DocFilePropertiesEntityDTO();
        docFileTemp.setHeadId("test123");
        docFileTemp.setDocId("testId");
        docFileTemp.setDocName("testName");
        List<DocFilePropertiesEntityDTO> docFileList = new ArrayList<>();
        docFileList.add(docFileTemp);
        PowerMockito.when(docFilePropertiesService.getList(Mockito.any())).thenReturn(docFileList);
        Whitebox.invokeMethod(service,"setAttachment",new ScrapBillHeaderEntityDTO(),new ScrapFlowStartDTO(),new ArrayList<>(),"");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void reApprovalByBillNo() throws Exception {
        ScrapBillHeaderEntityDTO record = new ScrapBillHeaderEntityDTO();
        record.setBillNo("test123");
        record.setBillStatus("已拒绝");
        record.setCurrApprover("00286523");
        record.setEmpNo("00286523");
        record.setReApprovalNodeKey("reApprovalKey");
        record.setScrapType("1");
        record.setImportFileId("testDoc");
        record.setImportFileName("testDoc");
        ApprovalProcessInfoEntityDTO tempApprovalInfo = new ApprovalProcessInfoEntityDTO();
        tempApprovalInfo.setBillNo("test123");
        tempApprovalInfo.setSeq(0);
        tempApprovalInfo.setNodeCode("test");
        tempApprovalInfo.setApproverId("00286523");
        List<ApprovalProcessInfoEntityDTO> approvalProcessList = new ArrayList<>();
        approvalProcessList.add(tempApprovalInfo);
        List<String> empNoList = new ArrayList<>();
        empNoList.add(record.getEmpNo());
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("hl");
        Map<String, HrmPersonInfoDTO> hrmMap = new HashMap<>();
        hrmMap.put("00286523",hrmPersonInfoDTO);
        DocFilePropertiesEntityDTO docFileTemp = new DocFilePropertiesEntityDTO();
        docFileTemp.setHeadId("test123");
        docFileTemp.setDocId("testId");
        docFileTemp.setDocName("testName");
        List<DocFilePropertiesEntityDTO> docFileList = new ArrayList<>();
        docFileList.add(docFileTemp);
        PowerMockito.when(scrapBillHeaderrepository.getInfoByBillNo(Mockito.any())).thenReturn(record);
        PowerMockito.when(centerfactoryRemoteService.getApprovalProInfoList(Mockito.any())).thenReturn(approvalProcessList);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.any())).thenReturn(hrmMap);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("zte-mes-manufactureshare-externalserv/file/download");
        PowerMockito.when(docFilePropertiesService.getList(Mockito.any())).thenReturn(docFileList);
        PowerMockito.when(scrapBillHeaderrepository.updateStatusByBillNo(Mockito.any())).thenReturn(1);
        PowerMockito.when(ApprovalFlowClient.startByNode(Mockito.any())).thenReturn("");
        service.reApprovalByBillNo(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void changeApprovalByBillNo() throws Exception {
        ScrapBillHeaderEntityDTO record = new ScrapBillHeaderEntityDTO();
        record.setBillNo("test123");
        record.setCurrApprover("00286523");
        record.setReApprovalNodeKey("reApprovalKey");
        record.setBillStatus("审批中");
        record.setCurrApprover("00286523");
        record.setEmpNo("00286523");
        record.setChangedApprover("00286523");
        record.setReApprovalNodeKey("reApprovalKey");
        record.setScrapType("1");
        record.setImportFileId("testDoc");
        record.setImportFileName("testDoc");
        ApprovalProcessInfoEntityDTO tempApprovalInfo = new ApprovalProcessInfoEntityDTO();
        tempApprovalInfo.setBillNo("test123");
        tempApprovalInfo.setSeq(0);
        tempApprovalInfo.setNodeCode("test");
        tempApprovalInfo.setApproverId("00286523");
        List<ApprovalProcessInfoEntityDTO> approvalProcessList = new ArrayList<>();
        approvalProcessList.add(tempApprovalInfo);

        PowerMockito.when(scrapBillHeaderrepository.getInfoByBillNo(Mockito.any())).thenReturn(record);
        service.changeApprovalByBillNo(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}