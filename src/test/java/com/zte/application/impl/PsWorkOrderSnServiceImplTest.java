package com.zte.application.impl;

import com.zte.domain.model.PsWorkOrderSn;
import com.zte.domain.model.PsWorkOrderSnRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;


public class PsWorkOrderSnServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWorkOrderSnServiceImpl service;

    @Mock
    private PsWorkOrderSnRepository psWorkOrderSnRepository;


    @Test
    public void getListByworkOrderNos() {
        List<String> workOrderNos = new ArrayList<>();
        workOrderNos.add("111");
        PowerMockito.when(psWorkOrderSnRepository.getListByworkOrderNos(workOrderNos)).thenReturn(null);
        Assert.assertNull(service.getListByworkOrderNos(workOrderNos));
    }

    @Test
    public void insertPsWorkOrderSnSelective() {
        PsWorkOrderSn record = new PsWorkOrderSn();
        PowerMockito.when(psWorkOrderSnRepository.insertSelective(record)).thenReturn(1);
        Assert.assertNotNull(service.insertPsWorkOrderSnSelective(record));
    }

    @Test
    public void updateByWorkOrderNoSelective() {
        PsWorkOrderSn record = new PsWorkOrderSn();
        PowerMockito.when(psWorkOrderSnRepository.updateByPrimaryKeySelective(record)).thenReturn(1);
        Assert.assertNotNull(service.updateByWorkOrderNoSelective(record));
    }
}
