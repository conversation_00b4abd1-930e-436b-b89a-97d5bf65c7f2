package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PrepareItemConfig;
import com.zte.domain.model.PrepareItemConfigRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PrepareItemConfigPageQueryDTO;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工序准备项配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-10 10:12:30
 */
@RunWith(PowerMockRunner.class)
public class PrepareItemConfigServiceImplTest {

    @InjectMocks
    private PrepareItemConfigServiceImpl service;
    @Mock
    private PrepareItemConfigRepository repository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private IdGenerator idGenerator;

    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<PrepareItemConfig>());
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        PrepareItemConfig prepareItemConfig = new PrepareItemConfig();
        List<PrepareItemConfig> list = new ArrayList<>();
        list.add(prepareItemConfig);
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(list);
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        prepareItemConfig.setActivity("T");
        prepareItemConfig.setAccessWay(2);
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        prepareItemConfig.setActivity("Y");
        prepareItemConfig.setAccessWay(0);
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        prepareItemConfig.setActivity("N");
        prepareItemConfig.setAccessWay(1);
        prepareItemConfig.setCreateBy("10337580");
        prepareItemConfig.setLastUpdatedBy("10337580");
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.any())).thenReturn(null);
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.any())).thenReturn(map);
        service.queryPage(new PrepareItemConfigPageQueryDTO());
        map.put("10337580", new HrmPersonInfoDTO());
        Assert.assertNotNull(service.queryPage(new PrepareItemConfigPageQueryDTO()));
    }

    @Test
    public void testAdd() {
        PowerMockito.when(repository.insert(Mockito.any())).thenReturn(1L);
        service.addPrepareItem(new PrepareItemConfig());
        PrepareItemConfig prepareItemConfig = new PrepareItemConfig();
        List<PrepareItemConfig> list = new ArrayList<>();
        list.add(prepareItemConfig);
        PowerMockito.when(repository.checkData(Mockito.any())).thenReturn(list);
        prepareItemConfig.setSeq(1);
        prepareItemConfig.setPrepareItemName("1");
        prepareItemConfig.setPrepareItemCode("1");
        try {
            service.addPrepareItem(new PrepareItemConfig());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.PREPARE_ITEM_EDIT_PARAM_REPEAT);
        }
        try {
            service.addPrepareItem(prepareItemConfig);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.PREPARE_ITEM_EDIT_PARAM_REPEAT);
        }
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(repository.updateById(Mockito.any())).thenReturn(1L);
        service.updatePrepareItemById(new PrepareItemConfig());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testDeleteById() {
        PowerMockito.when(repository.deleteById(Mockito.any())).thenReturn(1L);
        service.deletePrepareItemById(123L);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.countExportTotal(new PrepareItemConfigPageQueryDTO()));
    }

    @Test
    public void testQueryExportData() {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<PrepareItemConfig>());
        Assert.assertNotNull(service.queryExportData(new PrepareItemConfigPageQueryDTO(), 1, 10));

    }
}

