package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmRepairDisplaceDTO;
import com.zte.interfaces.dto.PmRepairDisplaceRcvDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({CommonUtils.class, MessageId.class, CollectionUtils.class,PlanscheduleRemoteService.class})
public class PmRepairRcvDetailServiceImplTest  extends PowerBaseTestCase {
    @InjectMocks
    PmRepairRcvDetailServiceImpl pmRepairRcvDetailService;
    @Mock
    private PsWipInfoServiceImpl psWipInfoService;

    @Mock
    private PsWipInfoService wipInfoService;
    @Mock
    private PmRepairDetailServiceImpl pmRepairDetailService;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvRepository;

    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private PmRepairRcvRepository prrRepository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    private BarcodeLockTempRepository barcodeLockTempRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    /* Started by AICoder, pid:t90c6u8140s414c141670b3da0eee106c989b858 */
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    /* Ended by AICoder, pid:t90c6u8140s414c141670b3da0eee106c989b858 */


    @Test
    public void determineTheConsistencyOfProcessStations()throws Exception {
        pmRepairRcvDetailService.determineTheConsistencyOfProcessStations(new PsWipInfo(){{setCraftSection("5");setProcessCode("5");setWorkStation("5");}}, new PmRepairRcv(){{setCraftSection("5");setProcessCode("5");setWorkStation("5");}});
        Assert.assertTrue(pmRepairRcvDetailService.determineTheConsistencyOfProcessStations(new PsWipInfo(){{setCraftSection("4");setProcessCode("4");setWorkStation("5");}}, new PmRepairRcv(){{setCraftSection("5");setWorkStation("4");}}));
    }
    @Test
    public void updatePmRepairRcvDetailBySn()throws Exception {
       try{ pmRepairRcvDetailService.updatePmRepairRcvDetailBySn(new PmRepairDisplaceRcvDTO(),"2");
           String runNormal = "Y";
           Assert.assertEquals(Constant.STR_Y, runNormal);}catch (Exception e){}
    }


    @Test
    public void batchInsertOptRecord()throws Exception {
        List<PmRepairDisplaceDTO> pmRepairDisplaceDTOS = new ArrayList<>();
        pmRepairDisplaceDTOS.add(new PmRepairDisplaceDTO());
        pmRepairRcvDetailService.batchInsertOptRecord(pmRepairDisplaceDTOS);
        pmRepairDisplaceDTOS.add(new PmRepairDisplaceDTO(){{setRepairSn("22");}});
        pmRepairRcvDetailService.batchInsertOptRecord(pmRepairDisplaceDTOS);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void updateSnByRepairSn() throws Exception {
        pmRepairRcvDetailService.updateSnByRepairSn(new HashMap<>());
        WipExtendIdentification entity = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationRepository.selectEntityBySn(Mockito.any())).thenReturn(entity);
        Assert.assertNotNull(pmRepairRcvDetailService.updateSnByRepairSn(new HashMap<>()));
    }
    @Test
    public void checkReturnSn() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(MessageId.class);

        PmRepairDisplaceRcvDTO pmRepairDisplaceRcvDTO =new PmRepairDisplaceRcvDTO();
        pmRepairDisplaceRcvDTO.setRepairSn("test123");
        pmRepairDisplaceRcvDTO.setReturnSn("test234");
        pmRepairDisplaceRcvDTO.setItemSnCode("123456789789");
        PowerMockito.when(pmRepairRcvRepository.getList(Mockito.any())).thenReturn(new ArrayList<>());
        PsWipInfo returnPsWipInfo = new PsWipInfo();
        returnPsWipInfo.setItemNo("123456789789");
        returnPsWipInfo.setAttribute1("1234567");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.any())).thenReturn(returnPsWipInfo);
        PowerMockito.when(prrRepository.getPmRepairRcvBySnAndStatus(Mockito.any())).thenReturn(null);
        PsTask psTask=new PsTask();
        psTask.setProdplanId("1234567");
        psTask.setEnvAttr1(new BigDecimal("10"));
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(Mockito.anyString())).thenReturn(psTask);
        List<BarcodeLockDetail> lockDetailList = new ArrayList<>();
        BarcodeLockDetail barcodeLockDetail =new BarcodeLockDetail();
        barcodeLockDetail.setAttribute1("1234567");
        barcodeLockDetail.setBillNo("1234567");
        lockDetailList.add(barcodeLockDetail);

        List<BarcodeLockTemp> barcodeLockTemps = new ArrayList<>();
        BarcodeLockTemp barcodeLockTemp =new BarcodeLockTemp();
        barcodeLockTemp.setBillNo("1234567");
        barcodeLockTemp.setSn("1234567");
        barcodeLockTemps.add(barcodeLockTemp);
        PowerMockito.when(barcodeLockTempRepository.selectUnLockInfo(Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.any(),Mockito.any())).thenReturn(lockDetailList);
        pmRepairRcvDetailService.checkReturnSn(pmRepairDisplaceRcvDTO);
        PowerMockito.when(barcodeLockTempRepository.selectUnLockInfo(Mockito.any(),Mockito.any())).thenReturn(barcodeLockTemps);
        pmRepairRcvDetailService.checkReturnSn(pmRepairDisplaceRcvDTO);
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.any(),Mockito.any())).thenReturn(null);
        String msg = pmRepairRcvDetailService.checkReturnSn(pmRepairDisplaceRcvDTO);
        Assert.assertNull(msg);
    }

    /*Started by AICoder, pid:1131dl2317x91be1479f0baeb048c64b38e2db2f*/
    @Test(timeout = 8000)
    public void testGetTaskEnvAttrBySn_PsWipInfoNull() {
        PowerMockito.when(wipInfoService.getWipInfoBySn(any()))
                .thenReturn(new PsWipInfo(){{setWipId("1");setAttribute1("1");}});
        assertNull(pmRepairRcvDetailService.getTaskEnvAttrBySn("test"));
    }

    @Test(timeout = 8000)
    public void testGetTaskEnvAttrBySn_PsTaskNull() {
        PowerMockito.when(wipInfoService.getWipInfoBySn(any()))
                .thenReturn(new PsWipInfo(){{setWipId("1");setAttribute2("2");}});
        assertNull(pmRepairRcvDetailService.getTaskEnvAttrBySn("test"));
    }

    @Test(timeout = 8000)
    public void testGetTaskEnvAttrBySn_PsTaskEnvAttr1NotNull() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(anyString())).thenReturn(null);
        PowerMockito.when(wipInfoService.getWipInfoBySn(any()))
                .thenReturn(new PsWipInfo(){{setWipId("1");setAttribute1("1");}});
        BigDecimal result = pmRepairRcvDetailService.getTaskEnvAttrBySn("test");
        assertNull(result);
    }
    /*Ended by AICoder, pid:1131dl2317x91be1479f0baeb048c64b38e2db2f*/

    @Test(timeout = 8000)
    public void testGetTaskEnvAttrBySn() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(anyString())).thenReturn(new PsTask());
        PowerMockito.when(wipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo(){{setWipId("1");setAttribute1("1");setAttribute2("1");}});
        BigDecimal result = pmRepairRcvDetailService.getTaskEnvAttrBySn("test");
        assertNull(result);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(anyString())).thenReturn(new PsTask(){{setEnvAttr1(BigDecimal.ONE);}});
         result = pmRepairRcvDetailService.getTaskEnvAttrBySn("test");
        assertNotNull(result);
    }

    /* Started by AICoder, pid:c90c6y81405414c141670b3da0eee166c988b858 */
    @Test
    public void testGetToRepairRecInfoList_SnExceedsLimit() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSnType(MpConstant.SN_TYPE_NO_SN);
        dto.setSn(StringUtils.repeat("1\n", 101));
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);
        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });
        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }

    @Test
    public void testGetToRepairRecInfoList_NoSnRecordForNoSnType() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSnType(MpConstant.SN_TYPE_NO_SN);
        dto.setSn("1234567\n8901234");
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);

        Mockito.when(pmRepairRcvRepository.getProdplanSnListRecordCount(anyList(), anyString())).thenReturn(1);

        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }

    @Test
    public void testGetToRepairRecInfoList_NoSnRecordForSnType() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSn("1234567\n8901234");
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);

        Mockito.when(pmRepairRcvRepository.getProdplanSnListRecordCount(anyList(), anyString())).thenReturn(1);

        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }

    @Test
    public void testGetToRepairRecInfoList_NoSnRecordForHasSnType() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSnType(MpConstant.SN_TYPE_NO_SN);
        dto.setSn("1234567\n8901234");
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);
        when(pmRepairRcvRepository.getProdplanSnListRecordCount(anyList(), anyString())).thenReturn(1);

        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }

    @Test
    public void testGetToRepairRecInfoList_EmptyResult() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSnType(MpConstant.SN_TYPE_NO_SN);
        dto.setSn(StringUtils.repeat("1\n", 10));
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);
        when(pmRepairRcvRepository.getToRepairRecInfoList(anyList())).thenReturn(Collections.emptyList());

        Exception exception = assertThrows(Exception.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });

        Mockito.verify(pmRepairRcvRepository,Mockito.times(1)).getToRepairRecInfoList(anyList());
    }

    @Test
    public void testGetToRepairRecInfoList_DiffPageType() {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setSnType(MpConstant.SN_TYPE_NO_SN);
        dto.setSn(StringUtils.repeat("1\n", 10));
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);
        when(pmRepairRcvRepository.getToRepairRecInfoList(anyList())).thenReturn(Collections.emptyList());

        Exception exception = assertThrows(Exception.class, () -> {
            pmRepairRcvDetailService.getToRepairRecInfoList(dto);
        });

        Mockito.verify(pmRepairRcvRepository,Mockito.times(1)).getToRepairRecInfoList(anyList());
    }

    @Test
    public void testGetToRepairRecInfoList_Success() throws Exception {
        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setPageType(MpConstant.PAGE_TYPE_TERMINAL);
        dto.setSn("1234567\n8901234");
        List<String> snList = Optional.ofNullable(dto.getSn())
                .map(s -> Arrays.stream(s.split("\\r?\\n")))
                .orElseGet(Stream::empty)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        dto.setSnList(snList);

        PmRepairRcvDetailDTO pmRepairRcvDetailDTO1 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO1.setRcvProdplanId("prodplan1");
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO2 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO2.setRcvProdplanId("prodplan2");
        List<PmRepairRcvDetailDTO> mockRcvList = Arrays.asList(
                pmRepairRcvDetailDTO1,
                pmRepairRcvDetailDTO2
        );

        when(pmRepairRcvRepository.getToRepairRecInfoList(anyList())).thenReturn(mockRcvList);

        BProdBomHeaderDTO bProdBomHeaderDTO1 = new BProdBomHeaderDTO();
        bProdBomHeaderDTO1.setProdplanId("prodplan1");
        bProdBomHeaderDTO1.setProductCode("product1");
        BProdBomHeaderDTO bProdBomHeaderDTO2 = new BProdBomHeaderDTO();
        bProdBomHeaderDTO2.setProdplanId("prodplan2");
        bProdBomHeaderDTO2.setProductCode("product2");

        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(
                Arrays.asList(
                        bProdBomHeaderDTO1,
                        bProdBomHeaderDTO2
                )
        );

        List<PmRepairRcvDetailDTO> result = pmRepairRcvDetailService.getToRepairRecInfoList(dto);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("product1", result.get(0).getMbom());
        assertEquals("product2", result.get(1).getMbom());
    }
    /* Ended by AICoder, pid:c90c6y81405414c141670b3da0eee166c988b858 */

}