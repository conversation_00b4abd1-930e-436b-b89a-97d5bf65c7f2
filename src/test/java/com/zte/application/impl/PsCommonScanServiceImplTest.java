package com.zte.application.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.application.QcRegulationService;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MpConstant.class, CrafttechRemoteService.class,  PlanscheduleRemoteService.class,StringUtils.class,
        CloudDiskHelper.class,  BasicsettingRemoteService.class, BeanUtils.class, CommonUtils.class, ObtainRemoteServiceDataUtil.class,
        ServiceDataBuilderUtil.class, MesBusinessException.class, ImesExcelUtil.class, HttpRemoteUtil.class})
public class PsCommonScanServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private PsCommonScanServiceImpl service;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PsScanHistoryService psScanHistoryService;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Value("${check.work.remain.time.flag:Y}")
    private String checkWorkOrRemain;
    @Value("${pmScan.check.snStatus.flag:true}")
    private boolean checkSnStatus;
    @Mock
    private QcRegulationService qcRegulationService;
    @Mock
    private QcSamplingHeadRepository qcSamplingHeadRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Test
    public void pmScan3() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class,CrafttechRemoteService.class);
        ReflectionTestUtils.setField(service, "checkSnStatus", true);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setLineCode("SMT-HY001");
        entity.setCurrProcessCode("1");
        entity.setSn("788888800001");
        entity.setWorkOrderNo("7888888SMT-A1001");
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setIsAccept(new BigDecimal("1"));
        pmRepairRcvDetails.add(pmRepairRcvDetail);
        PowerMockito.when(pmRepairRcvDetailRepository.selectPmRepairRcvDetailBatch(any())).thenReturn(pmRepairRcvDetails);
        String mockJson = "{\"bo\":[{\"processCode\":\"1\",\"processName\":\"Process 1\"}]}";
        JsonNode mockNode = new ObjectMapper().readTree(mockJson);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(anyMap())).thenReturn(mockNode);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(null);
        try {
            service.pmScan(entity);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.WIP_WORKORDERNO_NOT_EXIST,e.getMessage());
        }
    }

    @Test
    public void pmScan2() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class,CrafttechRemoteService.class);
        ReflectionTestUtils.setField(service, "checkSnStatus", true);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setLineCode("SMT-HY001");
        entity.setCurrProcessCode("1");
        entity.setSn("788888800001");
        entity.setWorkOrderNo("7888888SMT-A1001");
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setIsAccept(new BigDecimal("1"));
        pmRepairRcvDetails.add(pmRepairRcvDetail);
        PowerMockito.when(pmRepairRcvDetailRepository.selectPmRepairRcvDetailBatch(any())).thenReturn(pmRepairRcvDetails);
        String mockJson = "{\"bo\":[{\"processCode\":\"1\",\"processName\":\"Process 1\"}]}";
        JsonNode mockNode = new ObjectMapper().readTree(mockJson);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(anyMap())).thenReturn(mockNode);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setStatus("N");
        wipInfoBySn.setSn("123");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(wipInfoBySn);
        try {
            service.pmScan(entity);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.SN_STATUS_IS_POOR_AND_NO_STOPOVER_IS_ALLOWED,e.getMessage());
        }
    }

    @Test
    public void pmScan1() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class,CrafttechRemoteService.class);
        ReflectionTestUtils.setField(service, "checkSnStatus", false);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setLineCode("SMT-HY001");
        entity.setCurrProcessCode("1");
        entity.setSn("788888800001");
        entity.setWorkOrderNo("7888888SMT-A1001");
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setIsAccept(new BigDecimal("1"));
        pmRepairRcvDetails.add(pmRepairRcvDetail);
        PowerMockito.when(pmRepairRcvDetailRepository.selectPmRepairRcvDetailBatch(any())).thenReturn(pmRepairRcvDetails);
        String mockJson = "{\"bo\":[{\"processCode\":\"1\",\"processName\":\"Process 1\"}]}";
        JsonNode mockNode = new ObjectMapper().readTree(mockJson);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(anyMap())).thenReturn(mockNode);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setStatus("Y");
        wipInfoBySn.setSn("123");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(wipInfoBySn);
        try {
            service.pmScan(entity);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.WIP_WORKORDERNO_NOT_EXIST,e.getMessage());
        }
    }

    @Test
    public void pmScan() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class,CrafttechRemoteService.class);
        ReflectionTestUtils.setField(service, "checkSnStatus", true);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setLineCode("SMT-HY001");
        entity.setCurrProcessCode("1");
        entity.setSn("788888800001");
        entity.setWorkOrderNo("7888888SMT-A1001");
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetail.setIsAccept(new BigDecimal("1"));
        pmRepairRcvDetails.add(pmRepairRcvDetail);
        PowerMockito.when(pmRepairRcvDetailRepository.selectPmRepairRcvDetailBatch(any())).thenReturn(pmRepairRcvDetails);
        String mockJson = "{\"bo\":[{\"processCode\":\"1\",\"processName\":\"Process 1\"}]}";
        JsonNode mockNode = new ObjectMapper().readTree(mockJson);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(anyMap())).thenReturn(mockNode);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setStatus("Y");
        wipInfoBySn.setSn("123");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(wipInfoBySn);
        try {
            service.pmScan(entity);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.WIP_WORKORDERNO_NOT_EXIST,e.getMessage());
        }
    }

    @Test
    public void checkParams() throws Exception {
        PmScanConditionDTO entity = new PmScanConditionDTO();
        try {
            service.checkParams(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.LINE_CODE_IS_NULL,e.getMessage());
        }
        entity.setLineCode("123");
        try {
            service.checkParams(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PROCESS_CODE_IS_NULL,e.getMessage());
        }
        entity.setCurrProcessCode("123");
        try {
            service.checkParams(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SN_NULL_VALID,e.getMessage());
        }
        entity.setSn("123");
        try {
            service.checkParams(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.WORK_ORDER_IS_NULL,e.getMessage());
        }
        entity.setWorkOrderNo("123456789");
        try {
            service.checkParams(entity);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }
    @Test
    public void checkResult() throws Exception {
        StringBuilder msgBuilder = new StringBuilder();

        List<String> errorList = new ArrayList<>();
        errorList.add("111111");
        List<Future<String>> futureList = new ArrayList<>();
        service.checkResult(futureList);
        Future<String> list= new Future<String>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return false;
            }

            @Override
            public String get() throws InterruptedException, ExecutionException {
                return null;
            }

            @Override
            public String get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
        futureList.add(list);
        service.checkResult(futureList);
    }

    @Test
    public void checkWorkOrRemainTime() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CrafttechRemoteService.class);
        ReflectionTestUtils.setField(service, "checkWorkOrRemain", "N");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        List<PsEntityPlanBasicDTO> psList = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setWorkOrderNo("test123");
        psEntityPlanBasicDTO.setRouteId("test123");
        psList.add(psEntityPlanBasicDTO);
        try{
            service.checkWorkOrRemainTime(flow,psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ReflectionTestUtils.setField(service, "checkWorkOrRemain", "Y");
        try{
            PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString())).thenReturn(psList);
            service.checkWorkOrRemainTime(flow,psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        flow.setEntityPlanBasic(psEntityPlanBasicDTO);
        try{
            service.checkWorkOrRemainTime(flow,psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkWorkAndRescan() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CrafttechRemoteService.class);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("test123");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setLineCode("test123");
        flow.setCurrProcessCode("test123");
        List<CtRouteDetail> currList = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(currList);
        try{
            service.checkWorkAndRescan(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        currList.add(ctRouteDetail);
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setSourceSysName("烘烤上线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.getLineBodyModelingData(Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
        try{
            service.checkWorkAndRescan(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(CrafttechRemoteService.getLineBodyModelingData(Mockito.anyString(),Mockito.anyString())).thenReturn(ctRouteDetailDTOList);
        try{
            service.checkWorkAndRescan(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setProcessSeq(new BigDecimal("2"));
        ctRouteDetailDTO1.setSourceSysName("烘烤下线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO1);
        try{
            service.checkWorkAndRescan(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkIsNeedReScan() {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("test123");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setLineCode("test123");
        flow.setCurrProcessCode("test123");
        flow.setSourceSysName("烘烤上线");
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setSourceSysName("烘烤上线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setProcessSeq(new BigDecimal("2"));
        ctRouteDetailDTO1.setSourceSysName("烘烤下线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO1);
        try{
            service.checkIsNeedReScan(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setRedoRule("1");
        ctRouteDetail.setRemainTime(new BigDecimal("-1"));
        PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(null);
        try{
            service.checkIsNeedReScan(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfo.setLastProcess("Y");
        PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(null);
        try{
            service.checkIsNeedReScan(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkWorkTime() {
        PowerMockito.mockStatic(CommonUtils.class,CrafttechRemoteService.class);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        psWipInfo.setCurrProcessCode("test123");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setLineCode("test123");
        flow.setCurrProcessCode("test123");
        flow.setSourceSysName("烘烤上线");
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setSourceSysName("烘烤上线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setProcessSeq(new BigDecimal("2"));
        ctRouteDetailDTO1.setSourceSysName("烘烤下线");
        ctRouteDetailDTOList.add(ctRouteDetailDTO1);
        PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(null);
        try{
            service.checkWorkTime(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setWorkTime(new BigDecimal("1"));
        try{
            service.checkWorkTime(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setWorkTime(new BigDecimal("3"));
        flow.setSourceSysName("烘烤下线");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any(),(String[])Mockito.any())).thenReturn("error");
        try{
            service.checkWorkTime(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            long oneHour = 60 * 60 * 1000;
            Date oneHourAgo = new Date(new Date().getTime() - oneHour);
            PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(oneHourAgo);
            service.checkWorkTime(flow,psWipInfo,ctRouteDetail,ctRouteDetailDTOList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkRemainTime() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class,CrafttechRemoteService.class);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        psWipInfo.setCurrProcessCode("test123");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setLineCode("test123");
        flow.setCurrProcessCode("test123");
        flow.setSourceSysName("烘烤上线");
        List<CtRouteDetail> currList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        currList.add(ctRouteDetail);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
        try{
            service.checkRemainTime(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(currList);
        PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(null);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any(),(String[])Mockito.any())).thenReturn("error");
        try{
            service.checkRemainTime(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        psWipInfo.setLastProcess("Y");
        try{
            service.checkRemainTime(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setRemainTime(new BigDecimal("-1"));
        try{
            service.checkRemainTime(flow,psWipInfo,"test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void reScanWipSave() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setSn("test123");
        PsEntityPlanBasicDTO entityPlanBasicDTO = new PsEntityPlanBasicDTO();
        entityPlanBasicDTO.setWorkOrderNo("test123");
        flow.setEntityPlanBasic(entityPlanBasicDTO);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(psWipInfoServiceImpl).updateLastDeliveryDateInPsTask(any());
        PsWipInfo wipInfo = new PsWipInfo();
        PowerMockito.when(psWipInfoServiceImpl.getPsWipInfo(Mockito.any())).thenReturn(wipInfo);
        PsScanHistory scanHistory =new PsScanHistory();
        PowerMockito.when(psScanHistoryService.createScanHistoryEntity(Mockito.any())).thenReturn(scanHistory);
        PowerMockito.when(psWipInfoRepository.updatePsWipInfoByScan(Mockito.any())).thenReturn(1);
        PowerMockito.when(psScanHistoryService.insertPsScanHistoryByScan(Mockito.any())).thenReturn(1);
        PowerMockito.when(ObtainRemoteServiceDataUtil.deleteWholeContainerContentBySn(Mockito.anyString())).thenReturn("error");
        PowerMockito.when(psWipInfoServiceImpl.writeOutPut(Mockito.any(),Mockito.anyString())).thenReturn("test123").thenReturn("");
        PowerMockito.when(psWipInfoServiceImpl.updatePsWorkorderQty(Mockito.any())).thenReturn("test123");
        try{
            service.reScanWipSave(flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            service.reScanWipSave(flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void doAgainScanned() {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setReScanFlag(true);
        try{
            service.doAgainScanned(flow,new ArrayList<>());
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getHourDiff() {
        PowerMockito.when(wipScanHistoryRepository.getTimeInterval(Mockito.any())).thenReturn(new Date());
        try{
            service.getHourDiff(new WipScanHistoryDTO());
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkQcRegulation() throws Exception{
        ReflectionTestUtils.setField(service, "qcSamplingFlag", false);
        PsWipInfo psWipInfo = new PsWipInfo();
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        ReflectionTestUtils.setField(service, "qcSamplingFlag", true);
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        psWipInfo.setLastProcess(Constant.FLAG_Y);
        psWipInfo.setSn("111");
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfo.setLineCode("lineCode");
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfo.setAttribute1("attr1");
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfo.setProcessCode("1");
        PowerMockito.when(qcRegulationService.whetherNeedQc(any())).thenReturn(Constant.FLAG_N);
        try{
            service.checkQcRegulation(psWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(qcRegulationService.whetherNeedQc(any())).thenReturn(Constant.FLAG_Y);
        List<QcSamplingHeadDTO> samplingHeadInfo = new ArrayList<>();
        PowerMockito.when(qcSamplingHeadRepository.getSamplingHeadInfo(any(),any())).thenReturn(samplingHeadInfo);
        try {
            service.checkQcRegulation(psWipInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_VALID_SAMPLING_RECORD, e.getMessage());
        }

        samplingHeadInfo.add(new QcSamplingHeadDTO(){{setHeadId("id");setStatus("1");}});
        try {
            service.checkQcRegulation(psWipInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PASS_SAMPLING_RECORD, e.getMessage());
        }
        samplingHeadInfo.remove(0);
        samplingHeadInfo.add(new QcSamplingHeadDTO(){{setHeadId("id");setStatus("3");}});
        try {
            service.checkQcRegulation(psWipInfo);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}
