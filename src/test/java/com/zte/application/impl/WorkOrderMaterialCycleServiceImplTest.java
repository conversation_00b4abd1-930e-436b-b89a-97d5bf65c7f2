package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.StorageCenterRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.storage.StorageDetailsDTO;
import com.zte.interfaces.dto.storage.StoragePageDTO;
import com.zte.interfaces.dto.warehouse.InforDemandDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.matchers.Any;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;

/**
 * 指令生产物料周期信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-10 17:01:55
 */
@PrepareForTest({BasicsettingRemoteService.class, ProductionDeliveryRemoteService.class, HttpClientUtil.class,
        ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, MESHttpHelper.class, PlanscheduleRemoteService.class})
public class WorkOrderMaterialCycleServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WorkOrderMaterialCycleServiceImpl service;
    @Mock
    private WorkOrderMaterialCycleRepository repository;
    @Mock
    private SmtMachineMTLHistoryLServiceImpl smtMachineMTLHistoryLService;
	@Mock
	private PkCodeHistoryServiceImpl pkCodeHistoryService;
	@Mock
	private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private PmWorkOrderMaterialReturnQueryRepository pmWorkOrderMaterialReturnQueryRepository;
    @Mock
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Mock
	private PkCodeHistoryRepository pkCodeHistoryRepository;
	@Mock
	private StorageCenterRemoteService storageCenterRemoteService;

    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        WorkOrderMaterialCyclePageQueryDTO query = new WorkOrderMaterialCyclePageQueryDTO();
        try {
            service.queryPage(query);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TIME_IS_NULL);
        }
        query.setQryLastUpdatedDateStart("2023-01-01 00:00:00");
        query.setQryLastUpdatedDateEnd("2024-02-01 00:00:00");
        try {
            service.queryPage(query);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPAN_WITHIN_180DAYS);
        }
        query.setQryLastUpdatedDateStart("2024-01-01 00:00:00");
        query.setLineSideWarehouseCode("123");
        try {
            service.queryPage(query);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.LINE_INFO_IS_NULL);
        }
        List<CFLine> lineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        lineList.add(cfLine);
        WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
        List<WorkOrderMaterialCycle> workOrderMaterialCycles = new ArrayList<>();
        workOrderMaterialCycles.add(workOrderMaterialCycle);
        workOrderMaterialCycles.add(new WorkOrderMaterialCycle());
        workOrderMaterialCycle.setLineCode("111");
        PowerMockito.when(BasicsettingRemoteService.queryLineInWarehouseCode(any())).thenReturn(lineList);
        PowerMockito.when(repository.selectPage(any())).thenReturn(workOrderMaterialCycles);
        service.queryPage(query);
        Map<String, String> lineNameMap = new HashMap<>();
        lineNameMap.put("123", "123");
        cfLine.setLineCode("123");
        cfLine.setLineName("123");
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(lineNameMap);
        service.queryPage(query);
    }

    @Test
    public void testGetById() {
        PowerMockito.when(repository.selectById(any())).thenReturn(new WorkOrderMaterialCycle());
		Assert.assertNotNull(service.getById(null));
    }

    @Test
    public void testAdd() {
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setWorkOrderNo("test");
        PowerMockito.when(repository.insert(any())).thenReturn(1L);
        service.add(workOrderMaterialCycle);
		Assert.assertNotNull(workOrderMaterialCycle);
    }

    @Test
    public void testUpdateById() {
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setWorkOrderNo("test");
        PowerMockito.when(repository.updateById(any())).thenReturn(1L);
        service.updateById(workOrderMaterialCycle);
		Assert.assertNotNull(workOrderMaterialCycle);
    }

    @Test
    public void testCalMaterialUsageCycle() throws Exception {
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        Assert.assertEquals(new ArrayList<>(), service.calMaterialUsageCycle(workOrderList));
        workOrderList.add(psWorkOrderDTO);
        Assert.assertEquals(new ArrayList<>(), service.calMaterialUsageCycle(workOrderList));
        psWorkOrderDTO.setWorkOrderNo("123");
        Assert.assertEquals(new ArrayList<>(), service.calMaterialUsageCycle(workOrderList));
        List<SmtMachineMTLHistoryL> smtMachineMTLHistoryLList = new ArrayList<>();
        SmtMachineMTLHistoryL smtMachineMTLHistoryL = new SmtMachineMTLHistoryL();
        smtMachineMTLHistoryL.setCreateDate(new Date());
        smtMachineMTLHistoryL.setObjectId("abc");
        smtMachineMTLHistoryL.setAttr2("asd");
        smtMachineMTLHistoryL.setWorkOrder("123");
        smtMachineMTLHistoryLList.add(smtMachineMTLHistoryL);
        SmtMachineMTLHistoryL smtMachineMTLHistoryL1 = new SmtMachineMTLHistoryL();
        smtMachineMTLHistoryL1.setCreateDate(new Date());
        smtMachineMTLHistoryL1.setWorkOrder("123");
        smtMachineMTLHistoryL1.setObjectId("asd");
        smtMachineMTLHistoryL1.setAttr2("asd");
        smtMachineMTLHistoryLList.add(smtMachineMTLHistoryL1);
        PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisList(any())).thenReturn(smtMachineMTLHistoryLList);
        service.calMaterialUsageCycle(workOrderList);
        smtMachineMTLHistoryL.setCreateDate(null);
        psWorkOrderDTO.setActualEndDate(new Date());
        service.calMaterialUsageCycle(workOrderList);
    }

    @Test
    public void testCalSupplyIssueCycle() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class,
                HttpClientUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, MESHttpHelper.class);
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderList.add(psWorkOrderDTO);
        Assert.assertEquals(new ArrayList<>(), service.calSupplyIssueCycle(workOrderList));
        psWorkOrderDTO.setWorkOrderNo("123");
        service.calSupplyIssueCycle(workOrderList);
        List<PdRequirementHeaderDTO> pdRequirementHeaderList = new ArrayList<>();
        PdRequirementHeaderDTO pdRequirementHeaderDTO = new PdRequirementHeaderDTO();
        pdRequirementHeaderList.add(pdRequirementHeaderDTO);
        pdRequirementHeaderList.add(new PdRequirementHeaderDTO());
		PowerMockito.when(ProductionDeliveryRemoteService.getPdRequirementHead(any())).thenReturn(pdRequirementHeaderList);
		PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisInfo(any(),any())).thenReturn(new ArrayList() {{
			add(new SmtMachineMTLHistoryL() {{
				setWorkOrder("123");
				setObjectId("456");
			}});
		}});

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        service.calSupplyIssueCycle(workOrderList);
        sysLookupTypesDTO.setLookupMeaning("123");
        pdRequirementHeaderDTO.setReqNo("bill1");
        service.calSupplyIssueCycle(workOrderList);
        List<WorkOrderMaterialCycle> workOrderMaterialCycles = new ArrayList<>();
        workOrderMaterialCycles.add(new WorkOrderMaterialCycle());
        WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
        WorkOrderMaterialCycle workOrderMaterialCycle1 = new WorkOrderMaterialCycle();
        workOrderMaterialCycles.add(workOrderMaterialCycle);
        workOrderMaterialCycles.add(workOrderMaterialCycle1);
        workOrderMaterialCycle.setBillNo("bill1");
        workOrderMaterialCycle1.setBillNo("321");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(workOrderMaterialCycles);
        pdRequirementHeaderDTO.setWorkOrderNo("123");
        service.calSupplyIssueCycle(workOrderList);
        workOrderMaterialCycle.setEditDate(new Date());
        service.calSupplyIssueCycle(workOrderList);
		PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisInfo(any(),any())).thenReturn(new ArrayList<>());
		service.calSupplyIssueCycle(workOrderList);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenThrow(new RuntimeException());
        service.calSupplyIssueCycle(workOrderList);
    }
    @Test
    public void testCalReturnCycle() {
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderList.add(psWorkOrderDTO);
        workOrderList.add(new PsWorkOrderDTO());
        Assert.assertEquals(new ArrayList<>(), service.calReturnCycle(workOrderList));
        psWorkOrderDTO.setWorkOrderNo("123");
        psWorkOrderDTO.setActualEndDate(new Date());
        Assert.assertEquals(new ArrayList<>(), service.calReturnCycle(workOrderList));
        List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueryList = new ArrayList<>();
        pmWorkOrderMaterialReturnQueryList.add(new PmWorkOrderMaterialReturnQuery());
        PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
        pmWorkOrderMaterialReturnQueryList.add(pmWorkOrderMaterialReturnQuery);
        PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getSmtMachineMaterialReturnForCal(any())).thenReturn(pmWorkOrderMaterialReturnQueryList);
        pmWorkOrderMaterialReturnQuery.setWorkOrder("123");
        pmWorkOrderMaterialReturnQuery.setCreateDate(new Date());
        service.calReturnCycle(workOrderList);
        pmWorkOrderMaterialReturnQuery.setReturnTime(new Date());
        pmWorkOrderMaterialReturnQuery.setRecoveryTime(new Date());
        service.calReturnCycle(workOrderList);
    }

	@Test
	public void productionMaterialCycleCal() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setLineCode("SMT-CS5");
		psWorkOrderDTO.setWorkOrderNo("7788990-SMT-B5201");
		psWorkOrderDTO.setWorkOrderStatus("已开工");
		psWorkOrderDTO.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkWorkInfoForCal(Mockito.any())).thenReturn(workOrderList);
		List<WmsQueryReturnDTO> warehouseList = new ArrayList<>();
		WmsQueryReturnDTO wmsQueryReturnDTO = new WmsQueryReturnDTO();
		wmsQueryReturnDTO.setWarehouseId("CS0181");
		warehouseList.add(wmsQueryReturnDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(warehouseList);

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine=new CFLine();
		cfLine.setLineCode("SMT-CS5");
		cfLine.setWorkshopCode("test123");
		cfLines.add(cfLine);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);

		List<PsWorkOrderDTO> workOrderInfo = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO1 = new PsWorkOrderDTO();
		workOrderInfo.add(psWorkOrderDTO1);
		PowerMockito.when(PlanscheduleRemoteService.getInfoByWorkOrderNo(Mockito.any())).thenReturn(workOrderInfo);
		PkCodeHistory firstHandOver = new PkCodeHistory();
		PowerMockito.when(pkCodeHistoryRepository.getLastMaterialHandoverInfo(Mockito.any())).thenReturn(firstHandOver);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(new ArrayList<>());

		PkCodeHistory firstHandOver1 = new PkCodeHistory();

		PowerMockito.when(pkCodeHistoryRepository.getLastMaterialHandoverInfo(Mockito.any())).thenReturn(firstHandOver1);
		service.productionMaterialCycleCal();
		String runNormal = psWorkOrderDTO.getLineCode();
		Assert.assertEquals("SMT-CS5", runNormal);
	}

	@Test
	public void productionMaterialCycleCalTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setLineCode("SMT-CS5");
		psWorkOrderDTO.setWorkOrderNo("7788990-SMT-B5201");
		psWorkOrderDTO.setWorkOrderStatus("已完工");
		psWorkOrderDTO.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkWorkInfoForCal(Mockito.any())).thenReturn(workOrderList);
		List<WmsQueryReturnDTO> warehouseList = new ArrayList<>();
		WmsQueryReturnDTO wmsQueryReturnDTO = new WmsQueryReturnDTO();
		wmsQueryReturnDTO.setWarehouseId("CS0181");
		warehouseList.add(wmsQueryReturnDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(warehouseList);

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine=new CFLine();
		cfLine.setLineCode("SMT-CS5");
		cfLine.setWorkshopCode("test123");
		cfLines.add(cfLine);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);

		List<PsWorkOrderDTO> workOrderInfo = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO1 = new PsWorkOrderDTO();
		workOrderInfo.add(psWorkOrderDTO1);
		PowerMockito.when(PlanscheduleRemoteService.getInfoByWorkOrderNo(Mockito.any())).thenReturn(workOrderInfo);
		List<OutboundOrderLineDTO> lowPositionList = new ArrayList<>();
		OutboundOrderLineDTO outboundOrderLineDTO = new OutboundOrderLineDTO();
		outboundOrderLineDTO.setCreateDate(sdf.parse("2024-01-10 13:27:16"));
		outboundOrderLineDTO.setLastUpdatedDate(sdf.parse("2024-01-10 14:27:16"));
		lowPositionList.add(outboundOrderLineDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getLowPositionInfoByWorkOrder(Mockito.any())).thenReturn(lowPositionList);
		List<SmtMachineMTLHistoryL> machineMTLHistoryL = new ArrayList<>();
		SmtMachineMTLHistoryL historyL = new SmtMachineMTLHistoryL();
		historyL.setAttr2("TEST");
		historyL.setCreateDate(sdf.parse("2024-01-10 14:00:16"));
		historyL.setWorkOrder("7788990-SMT-B5201");
		machineMTLHistoryL.add(historyL);
		PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisList(Mockito.any())).thenReturn(machineMTLHistoryL);
		List<StorageDetailsDTO> storageDetailsDTOListAll = new ArrayList<>();
		StorageDetailsDTO storageDetailsDTO = new StorageDetailsDTO();
		storageDetailsDTO.setReelid("TEST");
		storageDetailsDTO.setJournalDate(sdf.parse("2024-01-10 14:20:16"));
		storageDetailsDTOListAll.add(storageDetailsDTO);
		StoragePageDTO<StorageDetailsDTO> partStorageInfo = new StoragePageDTO<>();
		partStorageInfo.setRecords(storageDetailsDTOListAll);
		PowerMockito.when(storageCenterRemoteService.getStockFlowAndErp(Mockito.any())).thenReturn(partStorageInfo);
		service.productionMaterialCycleCal();
		String runNormal = psWorkOrderDTO.getLineCode();
		Assert.assertEquals("SMT-CS5", runNormal);

	}

	@Test
	public void calMaterialPreCycle() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<String> warehouseIds = new ArrayList<>();
		warehouseIds.add("CS0181");
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO dto = new PsWorkOrderDTO();
		dto.setLineCode("SMT-CS5");
		dto.setWorkOrderNo("7788990-SMT-B5201");
		dto.setWorkOrderStatus("已开工");
		dto.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(dto);
		List<CFLine> cfLineEmpty =new ArrayList<>();
		try{
			PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLineEmpty);
			service.calMaterialPreCycle(workOrderList,warehouseIds);
		}catch (Exception e){
			Assert.assertEquals(MessageId.LINE_NOT_FOUND, e.getMessage());
		}
		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine=new CFLine();
		cfLine.setLineCode("test123");
		cfLine.setWorkshopCode("test123");
		cfLines.add(cfLine);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		Assert.assertNotNull(service.calMaterialPreCycle(workOrderList,warehouseIds));
		CFLine cfLine1=new CFLine();
		cfLine1.setLineCode("SMT-CS5");
		cfLine1.setWarehouseCode("CS0181");
		cfLines.add(cfLine1);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);

		List<PsWorkOrderDTO> workOrderInfo = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setWorkOrderNo("SMT-CS5");
		psWorkOrderDTO.setCraftSection("SMT-B");
		psWorkOrderDTO.setReleaseDate(sdf.parse("2024-01-09 15:27:16"));
		psWorkOrderDTO.setSourceTask("7788990");
		psWorkOrderDTO.setPreparedDate(sdf.parse("2024-01-10 09:27:16"));
		psWorkOrderDTO.setPreparedStartDate(sdf.parse("2024-01-10 05:27:16"));
		workOrderInfo.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getInfoByWorkOrderNo(Mockito.any())).thenReturn(new ArrayList<>());
		service.calMaterialPreCycle(workOrderList,warehouseIds);
		PowerMockito.when(PlanscheduleRemoteService.getInfoByWorkOrderNo(Mockito.any())).thenReturn(workOrderInfo);

		PkCodeHistory firstHandOver = new PkCodeHistory();
		firstHandOver.setWorkOrder("7788990-SMT-B5201");
		firstHandOver.setCreateDate(sdf.parse("2024-01-10 08:27:16"));
		PowerMockito.when(pkCodeHistoryRepository.getLastMaterialHandoverInfo(Mockito.any())).thenReturn(null);
		service.calMaterialPreCycle(workOrderList,warehouseIds);
		PowerMockito.when(pkCodeHistoryRepository.getLastMaterialHandoverInfo(Mockito.any())).thenReturn(firstHandOver);

		List<WmsReqHeadDTO> partWmsList = new ArrayList<>();
		WmsReqHeadDTO wmsReqHeadDTO = new WmsReqHeadDTO();
		wmsReqHeadDTO.setCreatedDate("2024-01-10 08:27:16");
		wmsReqHeadDTO.setLastUpdatedDate("2024-01-10 09:27:16");
		partWmsList.add(wmsReqHeadDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(partWmsList);

		Assert.assertNotNull(service.calMaterialPreCycle(workOrderList,warehouseIds));

	}

	@Test
	public void setOtherValue() throws Exception {
		WorkOrderMaterialCycle report= new WorkOrderMaterialCycle();
		report.setWorkOrderNo("test");
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setPartReturnMaxCyc(new BigDecimal(0.22));
		List<WorkOrderMaterialCycle> materialUsageCyc = new ArrayList<>();
		List<WorkOrderMaterialCycle> supplyIssyeCyc = new ArrayList<>();
		List<WorkOrderMaterialCycle> returnCyc = new ArrayList<>();
		String workOrderNo = report.getWorkOrderNo();
		materialUsageCyc.add(report);
		supplyIssyeCyc.add(report);
		returnCyc.add(report);
		Map<String, WorkOrderMaterialCycle> materialUsageMap = materialUsageCyc.stream()
				.collect(Collectors.toMap(WorkOrderMaterialCycle::getWorkOrderNo, e -> e, (a, b) -> a));
		Map<String, WorkOrderMaterialCycle> supplyIssyeMap= supplyIssyeCyc.stream()
				.collect(Collectors.toMap(WorkOrderMaterialCycle::getWorkOrderNo, e -> e, (a, b) -> a));
		Map<String, WorkOrderMaterialCycle> returnMap = returnCyc.stream()
				.collect(Collectors.toMap(WorkOrderMaterialCycle::getWorkOrderNo, e -> e, (a, b) -> a));

		Whitebox.invokeMethod(service, "setOtherValue",report,materialUsageMap,supplyIssyeMap,returnMap);
		Assert.assertEquals("test", workOrderNo);
	}

	@Test
	public void setPartValue() throws Exception {
		WorkOrderMaterialCycle report= new WorkOrderMaterialCycle();
		report.setWorkOrderNo("test");
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setPartReturnMaxCyc(new BigDecimal(0.22));
		List<WorkOrderMaterialCycle> materialIssuance = new ArrayList<>();
		List<WorkOrderMaterialCycle> materialPre = new ArrayList<>();

		String workOrderNo = report.getWorkOrderNo();
		materialIssuance.add(report);
		materialPre.add(report);
		Map<String, WorkOrderMaterialCycle> materialPreMap = materialIssuance.stream()
				.collect(Collectors.toMap(WorkOrderMaterialCycle::getWorkOrderNo, e -> e, (a, b) -> a));
		Map<String, WorkOrderMaterialCycle> materialIssuanceMap= materialPre.stream()
				.collect(Collectors.toMap(WorkOrderMaterialCycle::getWorkOrderNo, e -> e, (a, b) -> a));

		Whitebox.invokeMethod(service, "setPartValue",report,materialPreMap,materialIssuanceMap);
		Assert.assertEquals("test", workOrderNo);
	}

	@Test
	public void setValueForMaterialCycle() throws Exception {
		List<WorkOrderMaterialCycle> updateWorkOrder = new ArrayList<>();
		WorkOrderMaterialCycle report= new WorkOrderMaterialCycle();
		report.setWorkOrderNo("test");
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setPartReturnMaxCyc(new BigDecimal(0.22));
		updateWorkOrder.add(report);

		String workOrderNo = report.getWorkOrderNo();
		List<WorkOrderMaterialCycle> queryList = new ArrayList<>();
		PowerMockito.when(repository.selectByWorkOrders(Mockito.any())).thenReturn(queryList);
		PowerMockito.when(repository.batchInsert(Mockito.any())).thenReturn(1);
		Whitebox.invokeMethod(service, "setValueForMaterialCycle",updateWorkOrder);
		Assert.assertEquals("test", workOrderNo);
	}

	@Test
	public void storageResult() throws Exception {
		List<WorkOrderMaterialCycle> updateWorkOrder = new ArrayList<>();
		List<WorkOrderMaterialCycle> allWorkOrder = new ArrayList<>();
		Whitebox.invokeMethod(service, "storageResult",updateWorkOrder,allWorkOrder);
		WorkOrderMaterialCycle report= new WorkOrderMaterialCycle();
		report.setWorkOrderNo("test");
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setPartReturnMaxCyc(new BigDecimal(0.22));
		String workOrderNo = report.getWorkOrderNo();
		updateWorkOrder.add(report);
		allWorkOrder.add(report);
		Whitebox.invokeMethod(service, "storageResult",updateWorkOrder,allWorkOrder);
		Assert.assertEquals("test", workOrderNo);
	}

	@Test
	public void setValueForMaterialCycleTwo() throws Exception {
		List<WorkOrderMaterialCycle> updateWorkOrder = new ArrayList<>();
		WorkOrderMaterialCycle report= new WorkOrderMaterialCycle();
		report.setWorkOrderNo("test");
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setSupplyIssueMaxCyc(new BigDecimal(0.22));
		report.setPartReturnMaxCyc(new BigDecimal(0.22));
		updateWorkOrder.add(report);

		String workOrderNo = report.getWorkOrderNo();
		List<WorkOrderMaterialCycle> queryList = new ArrayList<>();
		queryList.add(report);
		PowerMockito.when(repository.selectByWorkOrders(Mockito.any())).thenReturn(queryList);

		Whitebox.invokeMethod(service, "setValueForMaterialCycle",updateWorkOrder);
		Assert.assertEquals("test", workOrderNo);
	}

	@Test
	public void calMaterialPreCycleTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<String> warehouseIds = new ArrayList<>();
		warehouseIds.add("CS0181");
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO dto = new PsWorkOrderDTO();
		dto.setLineCode("SMT-CS5");
		dto.setWorkOrderNo("7788990-SMT-B5201");
		dto.setWorkOrderStatus("已开工");
		dto.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(dto);

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine1=new CFLine();
		cfLine1.setLineCode("SMT-CS5");
		cfLine1.setWarehouseCode("CS0181");
		cfLines.add(cfLine1);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		List<WorkOrderMaterialCycle> queryList = new ArrayList<>();
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setWorkOrderNo("7788990-SMT-B5201");
		workOrderMaterialCycle.setLinesideLowMaxCyc(new BigDecimal("0.22"));
		workOrderMaterialCycle.setProdLineLowMaxCyc(new BigDecimal("0.22"));
		queryList.add(workOrderMaterialCycle);
		PowerMockito.when(repository.selectByWorkOrders(Mockito.anyList())).thenReturn(queryList);
		Assert.assertNotNull(service.calMaterialPreCycle(workOrderList,warehouseIds));
	}

	@Test
	public void calMaterialIssuanceCyc() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<String> warehouseIds = new ArrayList<>();
		warehouseIds.add("CS0181");
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO dto = new PsWorkOrderDTO();
		dto.setLineCode("SMT-CS5");
		dto.setWorkOrderNo("7788990-SMT-B5201");
		dto.setWorkOrderStatus("已完工");
		dto.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(dto);

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine1=new CFLine();
		cfLine1.setLineCode("SMT-CS5");
		cfLine1.setWarehouseCode("CS0181");
		cfLines.add(cfLine1);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		List<WorkOrderMaterialCycle> queryList = new ArrayList<>();
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setWorkOrderNo("7788990-SMT-B5201");
		workOrderMaterialCycle.setLinesideLowMaxCyc(new BigDecimal("0.22"));
		workOrderMaterialCycle.setProdLineLowMaxCyc(new BigDecimal("0.22"));
		queryList.add(workOrderMaterialCycle);
		PowerMockito.when(repository.selectByWorkOrders(Mockito.anyList())).thenReturn(queryList);
		Assert.assertNotNull(service.calMaterialIssuanceCyc(workOrderList,warehouseIds));
	}

	@Test
	public void calMaterialIssuanceCycTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
		List<String> warehouseIds = new ArrayList<>();
		warehouseIds.add("CS0181");
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO dto = new PsWorkOrderDTO();
		dto.setLineCode("SMT-CS5");
		dto.setWorkOrderNo("7788990-SMT-B5201");
		dto.setWorkOrderStatus("已完工");
		dto.setActualStartDate(sdf.parse("2024-01-10 12:27:16"));
		workOrderList.add(dto);

		List<CFLine> cfLines =new ArrayList<>();
		CFLine cfLine1=new CFLine();
		cfLine1.setLineCode("SMT-CS5");
		cfLine1.setWarehouseCode("CS0181");
		cfLines.add(cfLine1);
		PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyMap())).thenReturn(cfLines);
		List<WorkOrderMaterialCycle> queryList = new ArrayList<>();
		PowerMockito.when(repository.selectByWorkOrders(Mockito.anyList())).thenReturn(queryList);

		List<OutboundOrderLineDTO> lowPositionList = new ArrayList<>();
		OutboundOrderLineDTO outboundOrderLineDTO = new OutboundOrderLineDTO();
		outboundOrderLineDTO.setCreateDate(sdf.parse("2024-01-10 13:27:16"));
		outboundOrderLineDTO.setLastUpdatedDate(sdf.parse("2024-01-10 14:27:16"));
		lowPositionList.add(outboundOrderLineDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getLowPositionInfoByWorkOrder(Mockito.any())).thenReturn(lowPositionList);

		List<StorageDetailsDTO> storageDetailsDTOListAll1 = new ArrayList<>();
		StoragePageDTO<StorageDetailsDTO> partStorageInfo1 = new StoragePageDTO<>();
		partStorageInfo1.setRecords(storageDetailsDTOListAll1);
		PowerMockito.when(storageCenterRemoteService.getStockFlowAndErp(Mockito.any())).thenReturn(partStorageInfo1);

		List<SmtMachineMTLHistoryL> machineMTLHistoryL = new ArrayList<>();
		SmtMachineMTLHistoryL historyL = new SmtMachineMTLHistoryL();
		historyL.setAttr2("TEST");
		historyL.setCreateDate(sdf.parse("2024-01-10 14:00:16"));
		historyL.setWorkOrder("7788990-SMT-B5201");
		machineMTLHistoryL.add(historyL);
		PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisList(Mockito.any())).thenReturn(machineMTLHistoryL);

		Assert.assertNotNull(service.calMaterialIssuanceCyc(workOrderList,warehouseIds));

		List<StorageDetailsDTO> storageDetailsDTOListAll = new ArrayList<>();
		StorageDetailsDTO storageDetailsDTO = new StorageDetailsDTO();
		storageDetailsDTO.setReelid("TEST");
		storageDetailsDTO.setJournalDate(sdf.parse("2024-01-10 14:20:16"));
		storageDetailsDTOListAll.add(storageDetailsDTO);
		StoragePageDTO<StorageDetailsDTO> partStorageInfo = new StoragePageDTO<>();
		partStorageInfo.setRecords(storageDetailsDTOListAll);
		PowerMockito.when(storageCenterRemoteService.getStockFlowAndErp(Mockito.any())).thenReturn(partStorageInfo);

		PowerMockito.when(smtMachineMTLHistoryLService.getSmtMachineHisList(Mockito.any())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(service.calMaterialIssuanceCyc(workOrderList,warehouseIds));
	}

	@Test
	public void preMaterialForRedPoint() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		try{
			service.preMaterialForRedPoint("");
		}catch(Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.LINE_NAME_NOT_NULL);
		}
		try{
			PowerMockito.when(BasicsettingRemoteService.getLineByName(Mockito.any())).thenReturn(null);
			service.preMaterialForRedPoint("SMT-长沙1");
		}catch(Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.LINE_NOT_FOUND);
		}
		CFLine cfLineEntity = new CFLine();
		cfLineEntity.setLineCode("SMT-CS1");
		PowerMockito.when(BasicsettingRemoteService.getLineByName(Mockito.any())).thenReturn(cfLineEntity);
		Assert.assertNotNull(service.preMaterialForRedPoint("SMT-长沙1"));
		cfLineEntity.setWarehouseCode("CS1265");
		PowerMockito.when(BasicsettingRemoteService.getLineByName(Mockito.any())).thenReturn(cfLineEntity);

		PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any())).thenReturn("");
		Assert.assertNotNull(service.preMaterialForRedPoint("SMT-长沙1"));

		PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any())).thenReturn("'已提交','已开工'");
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByLineCode(Mockito.any())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(service.preMaterialForRedPoint("SMT-长沙1"));
		List<PsWorkOrderDTO> psTaskList = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setWorkOrderNo("7500321-SMT-B5203");
		psWorkOrderDTO.setWorkOrderStatus("已提交");
		psWorkOrderDTO.setStockStatus("首备中");
		psTaskList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByLineCode(Mockito.any())).thenReturn(psTaskList);

		List<WmsQueryReturnDTO> warehouseList = new ArrayList<>();
		WmsQueryReturnDTO wmsQueryReturnDTO = new WmsQueryReturnDTO();
		wmsQueryReturnDTO.setWarehouseId("CS0181");
		warehouseList.add(wmsQueryReturnDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(warehouseList);

		List<WmsReqHeadDTO> partWmsList = new ArrayList<>();
		WmsReqHeadDTO wmsReqHeadDTO = new WmsReqHeadDTO();
		wmsReqHeadDTO.setCreatedDate("2024-01-10 08:27:16");
		wmsReqHeadDTO.setLastUpdatedDate("2024-01-10 09:27:16");
		wmsReqHeadDTO.setStatusCode("0020");
		partWmsList.add(wmsReqHeadDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(partWmsList);
		PowerMockito.when(pkCodeHistoryRepository.queryHandoverQtyByWorkOrders(Mockito.any())).thenReturn(new ArrayList<>());
		PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(Mockito.any())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(service.preMaterialForRedPoint("SMT-长沙1"));
	}

	@Test
	public void preMaterialForRedPointTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);

		CFLine cfLineEntity = new CFLine();
		cfLineEntity.setLineCode("SMT-CS1");
		cfLineEntity.setWarehouseCode("CS1265");
		PowerMockito.when(BasicsettingRemoteService.getLineByName(Mockito.any())).thenReturn(cfLineEntity);
		PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any())).thenReturn("'已提交','已开工'");
		List<PsWorkOrderDTO> psTaskList = new ArrayList<>();
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setWorkOrderNo("7500321-SMT-B5203");
		psWorkOrderDTO.setWorkOrderStatus("已提交");
		psWorkOrderDTO.setStockStatus("首备完成");
		psTaskList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByLineCode(Mockito.any())).thenReturn(psTaskList);
		List<WmsQueryReturnDTO> warehouseList = new ArrayList<>();
		WmsQueryReturnDTO wmsQueryReturnDTO = new WmsQueryReturnDTO();
		wmsQueryReturnDTO.setWarehouseId("CS0181");
		warehouseList.add(wmsQueryReturnDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(warehouseList);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(new ArrayList<>());
		List<PkCodeHistoryDTO> handOverInfo = new ArrayList<>();
		PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
		pkCodeHistoryDTO.setWorkOrder("7500321-SMT-B5203");
		pkCodeHistoryDTO.setCurrentQty(new BigDecimal("2"));
		handOverInfo.add(pkCodeHistoryDTO);
		PowerMockito.when(pkCodeHistoryRepository.queryHandoverQtyByWorkOrders(Mockito.any())).thenReturn(handOverInfo);
		List<SmtMachineMTLHistoryH> smtMachineMTLHistoryHList = new ArrayList<>();
		SmtMachineMTLHistoryH smtMachineMTLHistoryH = new SmtMachineMTLHistoryH();
		smtMachineMTLHistoryH.setWorkOrder("7500321-SMT-B5203");
		smtMachineMTLHistoryH.setPickStatus("1");
		smtMachineMTLHistoryHList.add(smtMachineMTLHistoryH);
		PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(Mockito.any())).thenReturn(smtMachineMTLHistoryHList);
		Assert.assertNotNull(service.preMaterialForRedPoint("SMT-长沙1"));
	}

	@Test
	public void setPsWorkOrderValue() throws Exception {
		List<PsWorkOrderDTO> psTaskList = new ArrayList<>();
		PsWorkOrderDTO dto = new PsWorkOrderDTO();
		dto.setWorkOrderNo("7500321-SMT-B5203");
		dto.setSourceTask("7500321");
		psTaskList.add(dto);
		String workOrderNo = dto.getWorkOrderNo();
		List<WorkOrderMaterialCycleResultDTO> resultDTOList=new ArrayList<>();
		Whitebox.invokeMethod(service, "setPsWorkOrderValue",psTaskList,resultDTOList,"SMT-CS1");
		List<PsWorkOrderDTO> psTaskList1 = new ArrayList<>();
		PsWorkOrderDTO dto1 = new PsWorkOrderDTO();
		dto1.setWorkOrderNo("7500321-SMT-B5203");
		dto1.setSourceTask("7500321");
		dto1.setStockStatus("首备中");
		psTaskList1.add(dto1);
		Whitebox.invokeMethod(service, "setPsWorkOrderValue",psTaskList1,resultDTOList,"SMT-CS1");
		List<PsWorkOrderDTO> psTaskList2 = new ArrayList<>();
		PsWorkOrderDTO dto2 = new PsWorkOrderDTO();
		dto2.setWorkOrderNo("7500321-SMT-B5203");
		dto2.setSourceTask("7500321");
		dto2.setStockStatus("首备完成");
		psTaskList2.add(dto2);
		Whitebox.invokeMethod(service, "setPsWorkOrderValue",psTaskList2,resultDTOList,"SMT-CS1");
		Assert.assertEquals("7500321-SMT-B5203", workOrderNo);
	}

	@Test
	public void assembleAndConvertResult() throws Exception {
		List<WorkOrderMaterialCycleResultDTO> resultDTOList = new ArrayList<>();
		WorkOrderMaterialCycleResultDTO workOrderMaterialCycleResultDTO = new WorkOrderMaterialCycleResultDTO();
		workOrderMaterialCycleResultDTO.setWorkOrderNo("7500321-SMT-B5203");
		resultDTOList.add(workOrderMaterialCycleResultDTO);
		String workOrderNo = workOrderMaterialCycleResultDTO.getWorkOrderNo();
		Map<String, PkCodeHistoryDTO> handOverMap=new HashMap<>();
		Map<String, SmtMachineMTLHistoryH> historyDTOMap=new HashMap<>();
		Whitebox.invokeMethod(service, "assembleAndConvertResult",resultDTOList,handOverMap,historyDTOMap);

		List<PkCodeHistoryDTO> handOverInfo = new ArrayList<>();
		PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
		pkCodeHistoryDTO.setWorkOrder("7500321-SMT-B5203");
		pkCodeHistoryDTO.setCurrentQty(new BigDecimal("2"));
		handOverInfo.add(pkCodeHistoryDTO);
		handOverMap=handOverInfo.stream().collect(Collectors.toMap(PkCodeHistoryDTO::getWorkOrder, e -> e, (a, b) -> a));
		List<SmtMachineMTLHistoryH> smtMachineMTLHistoryHList = new ArrayList<>();
		SmtMachineMTLHistoryH smtMachineMTLHistoryH = new SmtMachineMTLHistoryH();
		smtMachineMTLHistoryH.setWorkOrder("7500321-SMT-B5203");
		smtMachineMTLHistoryH.setPickStatus("1");
		smtMachineMTLHistoryHList.add(smtMachineMTLHistoryH);
		historyDTOMap = smtMachineMTLHistoryHList.stream()
				.collect(Collectors.toMap(SmtMachineMTLHistoryH::getWorkOrder, e -> e, (a, b) -> a));
		Whitebox.invokeMethod(service, "assembleAndConvertResult",resultDTOList,handOverMap,historyDTOMap);
		List<SmtMachineMTLHistoryH> smtMachineMTLHistoryHList1 = new ArrayList<>();
		smtMachineMTLHistoryH.setPickStatus("2");
		smtMachineMTLHistoryHList1.add(smtMachineMTLHistoryH);
		historyDTOMap = smtMachineMTLHistoryHList.stream()
				.collect(Collectors.toMap(SmtMachineMTLHistoryH::getWorkOrder, e -> e, (a, b) -> a));
		Whitebox.invokeMethod(service, "assembleAndConvertResult",resultDTOList,handOverMap,historyDTOMap);
		Assert.assertEquals("7500321-SMT-B5203", workOrderNo);
	}

	@Test
	public void getLineSideReqStatus() throws Exception {
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		List<WorkOrderMaterialCycleResultDTO> resultDTOList = new ArrayList<>();
		WorkOrderMaterialCycleResultDTO workOrderMaterialCycleResultDTO = new WorkOrderMaterialCycleResultDTO();
		workOrderMaterialCycleResultDTO.setWorkOrderNo("7500321-SMT-B5203");
		workOrderMaterialCycleResultDTO.setProdplanId("7500321");
		resultDTOList.add(workOrderMaterialCycleResultDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(new ArrayList<>());
		Whitebox.invokeMethod(service, "getLineSideReqStatus",resultDTOList);

		List<WmsReqHeadDTO> partWmsList = new ArrayList<>();
		WmsReqHeadDTO wmsReqHeadDTO = new WmsReqHeadDTO();
		wmsReqHeadDTO.setStatusCode("0030");
		partWmsList.add(wmsReqHeadDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(partWmsList);
		Whitebox.invokeMethod(service, "getLineSideReqStatus",resultDTOList);

		List<WmsReqHeadDTO> partWmsList1 = new ArrayList<>();
		wmsReqHeadDTO.setStatusCode("0040");
		partWmsList1.add(wmsReqHeadDTO);
		PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(Mockito.any())).thenReturn(partWmsList1);
		Assert.assertNull(Whitebox.invokeMethod(service, "getLineSideReqStatus",resultDTOList));
	}

	@Test
    public void testReturnMaterialForRedPoint() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.LINE_NAME_NOT_NULL, e.getMessage());
		}
		workOrderMaterialCycle.setLineName("SMT-长沙1");
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.LINE_NOT_FOUND, e.getMessage());
		}
		CFLine cfLine = new CFLine();
		PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.LINE_NOT_FOUND, e.getMessage());
		}
		cfLine.setLineCode("SMT-CS1");
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("已开工");
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		SysLookupValues sysLookupValues = new SysLookupValues();
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(any())).thenReturn(sysLookupValues);
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_PARAM_SETTING_ERROR_OR_OVER_RANGE, e.getMessage());
		}
		sysLookupValues.setAttribute1("2");
		sysLookupValues.setAttribute2("1");
		sysLookupValues.setAttribute3("1");
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_PARAM_SETTING_ERROR_OR_OVER_RANGE, e.getMessage());
		}
		sysLookupValues.setAttribute2("4");
		try {
			service.returnMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_PARAM_SETTING_ERROR_OR_OVER_RANGE, e.getMessage());
		}
		sysLookupValues.setAttribute3("4");
		service.returnMaterialForRedPoint(workOrderMaterialCycle);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		workOrderList.add(new PsWorkOrderDTO());
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setWorkOrderNo("7654321-SMT-A5501");
		workOrderList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByLineCode(any())).thenReturn(workOrderList);
		service.returnMaterialForRedPoint(workOrderMaterialCycle);
		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueryList = new ArrayList<>();
		pmWorkOrderMaterialReturnQueryList.add(new PmWorkOrderMaterialReturnQuery());
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQueryList.add(pmWorkOrderMaterialReturnQuery);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getSmtMachineMaterialReturnForCal(any())).thenReturn(pmWorkOrderMaterialReturnQueryList);
		service.returnMaterialForRedPoint(workOrderMaterialCycle);
		pmWorkOrderMaterialReturnQuery.setWorkOrder("7654321-SMT-A5501");
		service.returnMaterialForRedPoint(workOrderMaterialCycle);
		pmWorkOrderMaterialReturnQuery.setStatus(BigDecimal.valueOf(1));
		service.returnMaterialForRedPoint(workOrderMaterialCycle);
		pmWorkOrderMaterialReturnQuery.setStatus(BigDecimal.valueOf(2));
		service.returnMaterialForRedPoint(workOrderMaterialCycle);}

	@Test
    public void testIssueMaterialForRedPoint() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class,
				ProductionDeliveryRemoteService.class, JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class);
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setLineName("SMT-长沙1");
		CFLine cfLine = new CFLine();
		PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
		cfLine.setLineCode("SMT-CS1");
		try {
			service.issueMaterialForRedPoint(workOrderMaterialCycle);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("已完工,零星板挂起");
		service.issueMaterialForRedPoint(workOrderMaterialCycle);

		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		workOrderList.add(new PsWorkOrderDTO());
		PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
		psWorkOrderDTO.setWorkOrderNo("7654321-SMT-A5501");
		workOrderList.add(psWorkOrderDTO);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByLineCode(any())).thenReturn(workOrderList);
		service.issueMaterialForRedPoint(workOrderMaterialCycle);

		List<PdRequirementHeaderDTO> pdRequirementHeaderList = new ArrayList<>();
		PdRequirementHeaderDTO pdRequirementHeaderDTO = new PdRequirementHeaderDTO();
		pdRequirementHeaderList.add(pdRequirementHeaderDTO);
		pdRequirementHeaderList.add(new PdRequirementHeaderDTO());
		PowerMockito.when(ProductionDeliveryRemoteService.getPdReHeadForRedPoint(any())).thenReturn(pdRequirementHeaderList);

		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
		service.issueMaterialForRedPoint(workOrderMaterialCycle);
		sysLookupTypesDTO.setLookupMeaning("123");
		pdRequirementHeaderDTO.setReqNo("bill1");
		service.issueMaterialForRedPoint(workOrderMaterialCycle);
		List<WorkOrderMaterialCycle> workOrderMaterialCycles = new ArrayList<>();
		workOrderMaterialCycles.add(new WorkOrderMaterialCycle());
		WorkOrderMaterialCycle workOrderMaterialCycle1 = new WorkOrderMaterialCycle();
		workOrderMaterialCycles.add(workOrderMaterialCycle);
		workOrderMaterialCycles.add(workOrderMaterialCycle1);
		workOrderMaterialCycle.setBillNo("bill1");
		workOrderMaterialCycle1.setBillNo("321");
		PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(workOrderMaterialCycles);
		pdRequirementHeaderDTO.setWorkOrderNo("123");
		service.issueMaterialForRedPoint(workOrderMaterialCycle);
		workOrderMaterialCycle.setEditDate(new Date());
		service.issueMaterialForRedPoint(workOrderMaterialCycle);

		PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenThrow(new RuntimeException());
		service.issueMaterialForRedPoint(workOrderMaterialCycle);
	}

	@Test
	public void calCenterIssueCyc() throws Exception {
		Whitebox.invokeMethod(service, "calCenterIssueCyc",new WorkOrderMaterialCycle(),new ArrayList<>());
		WorkOrderMaterialCycle cycle = new WorkOrderMaterialCycle();
		cycle.setWorkOrderNo("123");
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class,BasicsettingRemoteService.class,HttpClientUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		List<PdRequirementHeaderDTO> resultList = new ArrayList<>();
		PdRequirementHeaderDTO dto1 = new PdRequirementHeaderDTO();
		dto1.setReqNo("123");
		resultList.add(dto1);
		PowerMockito.when(ProductionDeliveryRemoteService.getPdRequirementHead(any())).thenReturn(resultList);
		List<WmsReqHeadDTO> partWmsList = new ArrayList() {{
			add(new WmsReqHeadDTO() {{
				setAttribute1("1");
			}});
		}};
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("127.0.0.1");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
		List<WorkOrderMaterialCycle> workOrderMaterialCycles = new ArrayList<>();
		WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
		workOrderMaterialCycle.setEditDate(null);
		workOrderMaterialCycles.add(workOrderMaterialCycle);
		workOrderMaterialCycles.add(new WorkOrderMaterialCycle());
		PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(new ArrayList<>());
		Whitebox.invokeMethod(service, "calCenterIssueCyc",cycle,partWmsList);
		PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(workOrderMaterialCycles);
		workOrderMaterialCycle.setEditDate(new Date());
		PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(workOrderMaterialCycles);
		Whitebox.invokeMethod(service, "calCenterIssueCyc",cycle,partWmsList);
		workOrderMaterialCycles.clear();
		workOrderMaterialCycles.add(new WorkOrderMaterialCycle());
		workOrderMaterialCycles.add(workOrderMaterialCycle);
		PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(workOrderMaterialCycles);
		Assert.assertNull(Whitebox.invokeMethod(service, "calCenterIssueCyc",cycle,partWmsList));
	}

	@Test
	public void calSupplyApproveAndReceiveCyc() throws Exception {
		Whitebox.invokeMethod(service, "calSupplyApproveAndReceiveCyc",new WorkOrderMaterialCycle(),new PdRequirementHeaderDTO(),new HashMap<>());
		PdRequirementHeaderDTO pdRequirementHeaderDTO = new PdRequirementHeaderDTO();
		pdRequirementHeaderDTO.setApproveResult("Y");
		pdRequirementHeaderDTO.setApproveDate(new Date());
		pdRequirementHeaderDTO.setSubmitDate(new Date());
		pdRequirementHeaderDTO.setWorkOrderNo("CS");
		Whitebox.invokeMethod(service, "calSupplyApproveAndReceiveCyc",new WorkOrderMaterialCycle(),pdRequirementHeaderDTO,new HashMap<>());
		WorkOrderMaterialCycle inforBill = new WorkOrderMaterialCycle();
		inforBill.setAddDate(new Date());
		inforBill.setReelId("111111");
		Map<String, SmtMachineMTLHistoryL> reelIdAndWorkOrderMap = new HashMap<>();
		reelIdAndWorkOrderMap.put("CS111111",null);
		Whitebox.invokeMethod(service, "calSupplyApproveAndReceiveCyc",inforBill,pdRequirementHeaderDTO,reelIdAndWorkOrderMap);
		SmtMachineMTLHistoryL historyL = new SmtMachineMTLHistoryL();
		historyL.setCreateDate(new Date());
		reelIdAndWorkOrderMap.put("CS111111",historyL);
		Assert.assertNull(Whitebox.invokeMethod(service, "calSupplyApproveAndReceiveCyc",inforBill,pdRequirementHeaderDTO,reelIdAndWorkOrderMap));
	}

    @Test
    public void calCenterIssueCycTwo() throws Exception {
        WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
        workOrderMaterialCycle.setWorkOrderNo("1");
        List<WmsReqHeadDTO> partWmsList = new ArrayList() {{
            add(new WmsReqHeadDTO() {{
                setWorkOrderNo("1");
                setSrcBillNo("test");
            }});
        }};
        List<InforDemandDTO> inforBillList = new ArrayList<>();
        InforDemandDTO inforDemandDTO = new InforDemandDTO();
        inforDemandDTO.setExternalorderkey2("test");
        inforDemandDTO.setEditdate(new Date());
        inforBillList.add(inforDemandDTO);
        PowerMockito.when(storageCenterRemoteService.getInforDemandInfo(Mockito.any())).thenReturn(inforBillList);
        Assert.assertNull(Whitebox.invokeMethod(service, "calCenterIssueCyc",workOrderMaterialCycle,partWmsList));
    }

    @Test
    public void calCenterIssueCycThree() throws Exception {
        WorkOrderMaterialCycle workOrderMaterialCycle = new WorkOrderMaterialCycle();
        workOrderMaterialCycle.setWorkOrderNo("1");
        List<WmsReqHeadDTO> partWmsList = new ArrayList() {{
            add(new WmsReqHeadDTO() {{
                setWorkOrderNo("1");
                setSrcBillNo("test");
            }});
        }};
        List<InforDemandDTO> inforBillList = new ArrayList<>();
        InforDemandDTO inforDemandDTO = new InforDemandDTO();
        inforDemandDTO.setExternalorderkey2("test");
        inforBillList.add(inforDemandDTO);
        PowerMockito.when(storageCenterRemoteService.getInforDemandInfo(Mockito.any())).thenReturn(inforBillList);
        Assert.assertNull(Whitebox.invokeMethod(service, "calCenterIssueCyc",workOrderMaterialCycle,partWmsList));
    }

	@Test
	public void calSupplyIssueCycleByWorkOrder() throws Exception {
		List<WorkOrderMaterialCycle> inforBillList = new ArrayList<>();
		WorkOrderMaterialCycle cycle = new WorkOrderMaterialCycle();
		cycle.setWorkOrderNo("123");
		cycle.setSupplyIssueCyc(new BigDecimal("12"));
		cycle.setSupplyApproveCyc(new BigDecimal("12"));
		cycle.setSupplyReceiveCyc(new BigDecimal("12"));
		inforBillList.add(cycle);
		Assert.assertNotNull(Whitebox.invokeMethod(service, "calSupplyIssueCycleByWorkOrder",inforBillList));
	}
}

