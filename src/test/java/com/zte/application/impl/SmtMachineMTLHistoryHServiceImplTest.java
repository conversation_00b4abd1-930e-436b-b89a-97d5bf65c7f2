package com.zte.application.impl;

import com.zte.application.QueryAndSetPropertyLineNameService;
import com.zte.common.HttpClientUtils;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class, HttpClientUtils.class,
        ServiceDataUtil.class, PlanscheduleRemoteService.class, HttpClientUtil.class})
public class SmtMachineMTLHistoryHServiceImplTest  extends PowerBaseTestCase {
    @InjectMocks
    private  SmtMachineMTLHistoryHServiceImpl service;
    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;
    @Mock
    private QueryAndSetPropertyLineNameService queryAndSetPropertyLineNameService;
    @Test
    public void downExcel(){
        Map<String, Object> map = new HashMap<>();

        PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(any())).thenReturn(new ArrayList<>());
        try{
            service.downExcel(map,"58");
        }catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
        List<SmtMachineMTLHistoryH> dataList = new ArrayList<>();
        SmtMachineMTLHistoryH smtMachineMTLHistoryH = new SmtMachineMTLHistoryH();
        dataList.add(smtMachineMTLHistoryH);
        PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(any())).thenReturn(dataList);
        PowerMockito.when(smtMachineMTLHistoryLRepository.getListByHeaderId(any())).thenReturn(new ArrayList<>());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(queryAndSetPropertyLineNameService).setPropertyLineNameForHistoryH(any(),any());
        try{
            service.downExcel(map,"58");
        }catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
    }
}