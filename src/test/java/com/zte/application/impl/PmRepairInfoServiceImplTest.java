package com.zte.application.impl;

import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.PmRepairInfo;
import com.zte.domain.model.PmRepairInfoRepository;
import com.zte.domain.model.PsOutputInfo;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsWipInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.BsWorkTimeSectionDTO;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static java.util.Collections.emptyMap;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Matchers.any;

@PrepareForTest({ExcelCommonUtils.class, BasicsettingRemoteService.class, Constant.class, ObtainRemoteServiceDataUtil.class, PmRepairInfoServiceImpl.class})
public class PmRepairInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    PmRepairInfoServiceImpl service;
    @Mock
    private PmRepairRcvServiceImpl pmRepairRcvServiceImpl;
    @Mock
    private PmRepairInfoRepository pmRepairInfoRepository;
    @Mock
    BsWorkTimeSectionService bsWorkTimeSectionService;
    @Mock
    PsOutputInfoService psOutputInfoService;

    @Mock
    PsWipInfoService psWipInfoService;
    @Mock
    private PsScanHistoryService psScanHistoryService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(ExcelCommonUtils.class, BasicsettingRemoteService.class
                , Constant.class, ObtainRemoteServiceDataUtil.class);
    }

    /* Started by AICoder, pid:i090cl0c3a2ea0c14e5409966006f238dbb32b9e */
    @Test
    public void oneDimensional(){
        List<PsWipInfo> psWipInfos = new LinkedList<>();
        List<PsScanHistory> psScanHistorys = new LinkedList<>();
        int i = service.oneDimensional(psWipInfos, psScanHistorys);
        assertEquals(0, i);
        for (int j = 0; j < 4; j++) {
            PsWipInfo a1 = new PsWipInfo();
            PsScanHistory b1 = new PsScanHistory();
            psWipInfos.add(a1);
            psScanHistorys.add(b1);
        }
        service.oneDimensional(psWipInfos, psScanHistorys);
    }
    /* Ended by AICoder, pid:i090cl0c3a2ea0c14e5409966006f238dbb32b9e */

    @Test
    public void setOutPutInfoEntity() throws Exception {
        service.setOutPutInfoEntity(new Date(), new SimpleDateFormat(), new PsWipInfoDTO() {{
            setGroupQty(5L);
        }}, new BsWorkTimeSectionDTO(), new PsOutputInfo());
        List<BSProcessDTO> bsProcessDTOList = new ArrayList<>();
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        psWipInfos.add(new PsWipInfo());
        service.setOutPutInfoEntity("", "", bsProcessDTOList, new PsWipInfo(), new PsOutputInfo());
        bsProcessDTOList.add(new BSProcessDTO());
        service.setOutPutInfoEntity("", "", bsProcessDTOList, new PsWipInfo(), new PsOutputInfo());

        service.getPsOutputInfos("", new Date(), new SimpleDateFormat(), new PsWipInfoDTO() {{
            setGroupQty(5L);
        }}, new BsWorkTimeSectionDTO());

        service.getBsWorkTimeSectionDTO(new PsWipInfo());

        List<PmRepairInfo> dataList = new ArrayList<>();
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setApplicationDepartment("1");
        pmRepairInfo.setApplicationSection("1");
        dataList.add(pmRepairInfo);
        Assert.assertNotNull(service.getExcelParamsDTO("", new String[]{}, new String[]{}, "", dataList));
    }

    @Test
    public void trans() throws Exception {
        List<PmRepairInfo> dataList = new ArrayList<>();
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setApplicationDepartment("1");
        pmRepairInfo.setApplicationSection("1");
        dataList.add(pmRepairInfo);
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        PowerMockito.when(pmRepairRcvServiceImpl.getLookupTypesAll(any(), any())).thenReturn(Collections.singletonList(new HashMap() {{
            put("lookupCode", "1");
            put("descriptionChinV", "1");
        }}));
        service.trans(record, dataList);
        String runNormal = "Y";
        assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void countTest() throws Exception {
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setInSns("'889822000005','889822000004','889822000006','889822000007'");
        List<PmRepairInfo> list = new LinkedList<>();
        PmRepairInfo a1 = new PmRepairInfo();
        a1.setApplicationSection("2020");
        list.add(a1);
        PowerMockito.when(pmRepairInfoRepository.getRelOneCountRecent(Mockito.any())).thenReturn(1L);
        PowerMockito.when(pmRepairInfoRepository.getRelOneCountRecentTerminal(Mockito.any())).thenReturn(1L);
        PowerMockito.when(pmRepairInfoRepository.getRelCountRecent(Mockito.any())).thenReturn(1L);
        PowerMockito.when(pmRepairInfoRepository.getRelOneCount(Mockito.any())).thenReturn(1L);
        PowerMockito.when(pmRepairInfoRepository.getRelCount(Mockito.any())).thenReturn(1L);
        service.getRelCount(record);
        service.getRelOneCount(record);
        service.getRelOneCountRecent(record);
        service.getRelOneCountRecentTerminal(record);
        service.getRelCountRecent(record);
        PmRepairInfoDTO record1 = new PmRepairInfoDTO();
        record1.setProdplanId("test");
        Assert.assertNotNull(service.getRelCount(record1));
    }

    @Test
    public void creatPsScanHistory() {
        try {
            service.creatPsScanHistory(new PsWipInfo(), "00286523");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    /* Started by AICoder, pid:o96e4s8d89t2596146cd0a6e20053d20b5d76fbf */
    @Test
    public void testSetUserNameWithAllFields() {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setDeliveryBy("user1");
        pro.setReceptionBy("user2");
        pro.setLastUpdatedBy("user3");
        pro.setReturnedBy("user4");
        pro.setReturnedTo("user5");
        pro.setRepairBy("user6");

        Map<String, String> bsPubHrMap = new HashMap<>();
        bsPubHrMap.put("user1", "Name1");
        bsPubHrMap.put("user2", "Name2");
        bsPubHrMap.put("user3", "Name3");
        bsPubHrMap.put("user4", "Name4");
        bsPubHrMap.put("user5", "Name5");
        bsPubHrMap.put("user6", "Name6");

        PmRepairInfo result = service.setUserName(pro, bsPubHrMap);

        assertEquals("Name1user1", result.getDeliveryByName());
        assertEquals("Name2user2", result.getReceptionByName());
        assertEquals("Name3", result.getLastUpdatedByName());
        assertEquals("Name4user4", result.getReturnedByName());
        assertEquals("Name5user5", result.getReturnedToName());
        assertEquals("Name6user6", result.getRepairByName());
    }
    /* Ended by AICoder, pid:o96e4s8d89t2596146cd0a6e20053d20b5d76fbf */

    @Test
    public void testChang_Success() throws Exception {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setFactoryId(new BigDecimal("1"));
        pmRepairInfo.setWorkStation("WS_001");
        pmRepairInfo.setRepairTeam("TEAM_001");

        List<PmRepairInfo> inputList = new ArrayList<>();
        inputList.add(pmRepairInfo);
        // 模拟远程服务返回的数据
        Map<String, String> workStationMap = new HashMap<>();
        workStationMap.put("WS_001", "WorkStation 1");

        PmRepairInfoServiceImpl serviceSpy = PowerMockito.spy(new PmRepairInfoServiceImpl());
        // 模拟方法调用
        PowerMockito.doReturn(workStationMap).when(serviceSpy, "getWorkStation", anyMap());

        PowerMockito.when(serviceSpy.getUserNew(anyList())).thenReturn(new HashMap<>());
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLineAll(any(BigDecimal.class))).thenReturn(new HashMap<>());
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(anyString(), anyString())).thenReturn(new HashMap<>());

        // 执行测试方法
        List<PmRepairInfo> result = serviceSpy.chang(inputList);

        // 验证结果
        assertEquals(1, result.size());
    }

    @Test
    public void testChang_Exception() throws Exception {

        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setFactoryId(new BigDecimal("1"));
        pmRepairInfo.setWorkStation("WS_001");
        pmRepairInfo.setRepairTeam("TEAM_001");

        List<PmRepairInfo> inputList = new ArrayList<>();
        inputList.add(pmRepairInfo);
        // 模拟远程服务返回的数据
        Map<String, String> workStationMap = new HashMap<>();
        workStationMap.put("WS_001", "WorkStation 1");

        Map<String, String> bsPubHrMap = new HashMap<>();
        bsPubHrMap.put("USER_001", "John Doe");

        PmRepairInfoServiceImpl serviceSpy = PowerMockito.spy(new PmRepairInfoServiceImpl());
        // 模拟方法调用
        PowerMockito.doReturn(workStationMap).when(serviceSpy, "getWorkStation", anyMap());

        PowerMockito.when(serviceSpy.getUserNew(anyList())).thenReturn(bsPubHrMap);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLineAll(any(BigDecimal.class))).thenThrow(new BusiException("TEST-001", "服务不可用"));
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(eq(Constant.LOOK_UP_REPAIR_STATUS), eq(Constant.FIELD_LOOKUP_CODE))).thenThrow(new BusiException("TEST-001", "服务不可用"));
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(eq(Constant.LOOK_UP_BUILD), eq(Constant.FIELD_LOOKUP_CODE))).thenThrow(new BusiException("TEST-001", "服务不可用"));
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(eq(Constant.LOOK_UP_SCRAP), eq(Constant.FIELD_LOOKUP_CODE))).thenThrow(new BusiException("TEST-001", "服务不可用"));
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(eq(Constant.lookupType.VALUE_22000), eq(Constant.FIELD_LOOKUP_CODE))).thenThrow(new BusiException("TEST-001", "服务不可用"));

        // 执行测试方法
        List<PmRepairInfo> result = serviceSpy.chang(inputList);

        // 验证结果
        assertEquals(1, result.size());
    }

    /* Started by AICoder, pid:m3e6725506wfad714739082ec08fce9aa066c063 */
    @Test
    public void testSetRepairTeam_EmptyRepairTeamMap() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam("originalTeam");
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        assertEquals("originalTeam", pro.getRepairTeam());
    }

    @Test
    public void testSetRepairTeam_ProRepairTeamIsNull() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam(null);
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();
        repairTeamMap.put("team1", new HashMap<>());

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        Assert.assertNull(pro.getRepairTeam());
    }

    @Test
    public void testSetRepairTeam_RepairTeamNotInMap() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam("nonExistentTeam");
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();
        repairTeamMap.put("team1", new HashMap<>());

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        assertEquals("nonExistentTeam", pro.getRepairTeam());
    }

    @Test
    public void testSetRepairTeam_DataMapWithoutDescriptionKey() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam("team1");
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("otherKey", "value");
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();
        repairTeamMap.put("team1", dataMap);

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        assertEquals("team1", pro.getRepairTeam());
    }

    @Test
    public void testSetRepairTeam_DataMapHasDescriptionKeyWithNullValue() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam("team1");
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(Constant.FIELD_DESCRIPTION_CHIN, null);
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();
        repairTeamMap.put("team1", dataMap);

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        assertEquals("team1", pro.getRepairTeam());
    }

    @Test
    public void testSetRepairTeam_ValidRepairTeamAndDescription() throws Exception {
        PmRepairInfo pro = new PmRepairInfo();
        pro.setRepairTeam("team1");
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(Constant.FIELD_DESCRIPTION_CHIN, "维修团队一");
        Map<String, Map<String, String>> repairTeamMap = new HashMap<>();
        repairTeamMap.put("team1", dataMap);

        Method method = PmRepairInfoServiceImpl.class.getDeclaredMethod("setRepairTeam", PmRepairInfo.class, Map.class);
        method.setAccessible(true);
        method.invoke(PmRepairInfoServiceImpl.class.newInstance(), pro, repairTeamMap);

        assertEquals("维修团队一", pro.getRepairTeam());
    }
    /* Ended by AICoder, pid:m3e6725506wfad714739082ec08fce9aa066c063 */

}