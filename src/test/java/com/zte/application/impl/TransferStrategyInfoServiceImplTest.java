package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.assembler.TransferStrategyInfoAssembler;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.ImesExcelUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, PlanscheduleRemoteService.class,
        CenterfactoryRemoteService.class, EasyExcelFactory.class, RedisHelper.class,
        ImesExcelUtil.class, CommonUtils.class, TransferStrategyInfoAssembler.class})
public class TransferStrategyInfoServiceImplTest {

    @InjectMocks
    private TransferStrategyInfoServiceImpl transferStrategyInfoService;
    @Mock
    private TransferStrategyInfoRepository transferStrategyInfoRepository;
    @Mock
    private BSmtBomHeaderService bSmtBomHeaderService;
    @Mock
    private SmtMachineMaterialPrepareService machineMaterialPrepareService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;

    @Mock
    private WriteSheet build;

    @Mock
    private ExcelWriterBuilder write;

    @Mock
    private ExcelWriter excelWriter;

    @Mock
    private RedisLock redisLock;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(TransferStrategyInfoAssembler.class);
    }

    @Test
    public void pageList() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setPage(1);
        List<TransferStrategyInfo> transferStrategyInfos = new ArrayList<>();

        PowerMockito.when(transferStrategyInfoRepository.pageList(any())).thenReturn(transferStrategyInfos);
        Page<TransferStrategyInfo> result = transferStrategyInfoService.pageList(dto);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getRows()));

        TransferStrategyInfo info = new TransferStrategyInfo();
        transferStrategyInfos.add(info);
        info.setLineCode("SMT-TZ002");
        info.setLastUpdatedBy("00260524");

        Map<String, String> lineMap = null;
        PowerMockito.when(transferStrategyInfoRepository.pageList(any())).thenReturn(transferStrategyInfos);
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(lineMap);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        result = transferStrategyInfoService.pageList(dto);
        Assert.assertTrue(result.getRows().size() == 1);

        info.setCreateBy("10331519");
        PowerMockito.when(transferStrategyInfoRepository.pageList(any())).thenReturn(transferStrategyInfos);
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(new HashMap<>());
        HrmPersonInfoDTO hrm = new HrmPersonInfoDTO();
        hrm.setEmpName("张文武");
        hrmPersonInfoDTOMap.put("10331519", hrm);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        result = transferStrategyInfoService.pageList(dto);
        Assert.assertTrue(result.getRows().size() == 1);
    }

    @Test
    public void countExportTotal() {
        PowerMockito.when(transferStrategyInfoRepository.getCountQuery(any())).thenReturn(1);
        Assert.assertTrue(1 == transferStrategyInfoService.countExportTotal(new TransferStrategyInfoDTO()));
    }

    @Test
    public void queryExportData() throws Exception {
        PowerMockito.when(transferStrategyInfoRepository.pageList(any())).thenReturn(null);
        Assert.assertTrue(CollectionUtils.isEmpty(transferStrategyInfoService.queryExportData(new TransferStrategyInfoDTO(), 1, 10)));

        List<TransferStrategyInfo> transferStrategyInfos = new ArrayList<>();
        TransferStrategyInfo info = new TransferStrategyInfo();
        transferStrategyInfos.add(info);
        info.setLineCode("SMT-TZ002");
        info.setLastUpdatedBy("00260524");

        Map<String, String> lineMap = null;
        PowerMockito.when(transferStrategyInfoRepository.pageList(any())).thenReturn(transferStrategyInfos);
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(lineMap);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        Assert.assertTrue(1 == transferStrategyInfoService.queryExportData(new TransferStrategyInfoDTO(), 1, 10).size());
    }

    @Test
    public void handleAfterInputProdId() {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");
        PowerMockito.when(PlanscheduleRemoteService.getLineCodeBySourceTask(any())).thenReturn(null);
        List<CFLine> list = new ArrayList<>();

        list = transferStrategyInfoService.handleAfterInputProdId(dto);
        Assert.assertEquals(0, list.size());

        List<String> lineCodeList = new ArrayList<>();
        lineCodeList.add("SMT-TZ002");
        PowerMockito.when(PlanscheduleRemoteService.getLineCodeBySourceTask(any())).thenReturn(lineCodeList);

        List<CFLine> lineList = new ArrayList<>();
        CFLine line = new CFLine();
        line.setWarehouseCode("111");
        lineList.add(line);
        PowerMockito.when(BasicsettingRemoteService.getLinesByCodeList(any())).thenReturn(lineList);
        transferStrategyInfoService.handleAfterInputProdId(dto);

        line.setWarehouseCode(null);
        line.setLineCode("SMT-TZ002");
        PowerMockito.when(BasicsettingRemoteService.getLinesByCodeList(any())).thenReturn(lineList);
        list = transferStrategyInfoService.handleAfterInputProdId(dto);
        Assert.assertEquals(1, list.size());
    }

    @Test
    public void handleAfterChooseLineCode() {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        TransferStrategyInfo entity = new TransferStrategyInfo();
        entity.setProdplanId("8899855");
        entity.setLineCode("SMT-TZ002");
        entity.setPreProdplanId("8899856");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyOne(any())).thenReturn(entity);

        try {
            transferStrategyInfoService.handleAfterChooseLineCode(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.PRODPLAN_ID_HAS_MAINTAINED_TRANSFER_STRATEGY);
        }
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyOne(any())).thenReturn(null);
        List<PsEntityPlanBasic> psEntityPlanBasics = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getSourceTaskByLineCode(any())).thenReturn(null);
        try {
            transferStrategyInfoService.handleAfterChooseLineCode(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NOT_FOUND_OTHER_TASK_BY_LINE_CODE);
        }

        PsEntityPlanBasic psEntityPlanBasic1 = new PsEntityPlanBasic();
        psEntityPlanBasic1.setSourceTask("8899855");
        PsEntityPlanBasic psEntityPlanBasic2 = new PsEntityPlanBasic();
        psEntityPlanBasic2.setSourceTask("8899345");
        psEntityPlanBasics.add(psEntityPlanBasic1);
        psEntityPlanBasics.add(psEntityPlanBasic2);
        PowerMockito.when(PlanscheduleRemoteService.getSourceTaskByLineCode(any())).thenReturn(psEntityPlanBasics);
        psEntityPlanBasics = transferStrategyInfoService.handleAfterChooseLineCode(dto);
        Assert.assertEquals(1, psEntityPlanBasics.size());
    }

    @Test
    public void handleAfterChangePreProdplanId() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        TransferStrategyInfo entity = new TransferStrategyInfo();
        entity.setProdplanId("8899855");
        entity.setLineCode("SMT-TZ002");
        entity.setPreProdplanId("8899856");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyOne(any())).thenReturn(entity);

        try {
            transferStrategyInfoService.handleAfterChangePreProdplanId(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.PRE_PRODPLAN_ID_HAS_MAINTAINED_TRANSFER_STRATEGY);
        }
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyOne(any())).thenReturn(null);
        String prodplanIdPath = "8899856>8899855";
        PowerMockito.when(transferStrategyInfoRepository.getProdplanIdPath(any())).thenReturn(prodplanIdPath);
        try {
            transferStrategyInfoService.handleAfterChangePreProdplanId(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.BATCH_FORMATION_LOOP);
        }
        PowerMockito.when(transferStrategyInfoRepository.getProdplanIdPath(any())).thenReturn("8899855>8899856");
        try {
            transferStrategyInfoService.handleAfterChangePreProdplanId(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NOT_FOUND_WORK_ORDER_BY_PRODPLAN_ID);
        }
        PowerMockito.when(transferStrategyInfoRepository.getProdplanIdPath(any())).thenReturn("8899856>8899856");

        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setCraftSection("SMT-A");
        ps1.setLineCode("SMT-TZ002");
        ps1.setSourceTask("88998888");
        psWorkOrderDTOList.add(ps1);
        psWorkOrderDTOList.add(ps1);
        ps1.setSourceTask("88998888");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(any())).thenReturn(psWorkOrderDTOList);

        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder2 = new PsWorkOrderSmtDTO();
        workOrder2.setWorkOrderNo("8899855-B");
        workOrder2.setCraftSection("SMT-B");
        workOrder2.setCfgHeaderId("2");
        workOrder2.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        PsWorkOrderSmtDTO workOrder4 = new PsWorkOrderSmtDTO();
        workOrder4.setWorkOrderNo("8899856-B");
        workOrder4.setCraftSection("SMT-B");
        workOrder4.setCfgHeaderId("4");
        workOrder4.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder2);
        psWorkOrderSmts.add(workOrder3);
        psWorkOrderSmts.add(workOrder4);
        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail2 = new BSmtBomDetail();
        detail2.setCfgHeaderId("2");
        detail2.setMachineNo("(X4S-2)");
        detail2.setLocationNo("2-31-1");
        detail2.setModuleNo("(X4S-2)2");
        detail2.setItemCode("046030200050");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");

        BSmtBomDetail detail4 = new BSmtBomDetail();
        detail4.setCfgHeaderId("4");
        detail4.setMachineNo("(X4S-2)");
        detail4.setLocationNo("2-31-1");
        detail4.setModuleNo("(X4S-2)2");
        detail4.setItemCode("046030200050");

        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail2);
        bSmtBomDetails.add(detail3);
        bSmtBomDetails.add(detail4);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(bSmtBomDetails);
        entity = null;
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyOne(any())).thenReturn(entity);
        transferStrategyInfoService.handleAfterChangePreProdplanId(dto);
        PowerMockito.when(transferStrategyInfoRepository.getProdplanIdPath(any())).thenReturn(null);
        transferStrategyInfoService.handleAfterChangePreProdplanId(dto);
    }

    @Test
    public void insert() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");
        try {
            transferStrategyInfoService.insert(dto, "55");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TRANSFER_STRATEGY_INFO_REDIS_LOCKED);
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        TransferStrategyInfo entity = new TransferStrategyInfo();
        PowerMockito.when(TransferStrategyInfoAssembler.toEntity(any())).thenReturn(entity);
        PowerMockito.when(transferStrategyInfoRepository.insert(entity)).thenReturn(1);

        PowerMockito.when(PlanscheduleRemoteService.getLineCodeBySourceTask(any())).thenReturn(null);

        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setCraftSection("SMT-A");
        ps1.setLineCode("SMT-TZ002");
        ps1.setSourceTask("88998888");
        psWorkOrderDTOList.add(ps1);
        psWorkOrderDTOList.add(ps1);
        ps1.setSourceTask("88998888");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(any())).thenReturn(psWorkOrderDTOList);

        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder3);

        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();
        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");

        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail3);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(bSmtBomDetails);
        List<PsEntityPlanBasic> psEntityPlanBasics = new ArrayList<>();
        PsEntityPlanBasic psEntityPlanBasic1 = new PsEntityPlanBasic();
        psEntityPlanBasic1.setSourceTask("8899855");
        PsEntityPlanBasic psEntityPlanBasic2 = new PsEntityPlanBasic();
        psEntityPlanBasic2.setSourceTask("8899345");
        psEntityPlanBasics.add(psEntityPlanBasic1);
        psEntityPlanBasics.add(psEntityPlanBasic2);

        PowerMockito.when(PlanscheduleRemoteService.getSourceTaskByLineCode(any())).thenReturn(psEntityPlanBasics);
        int num = transferStrategyInfoService.insert(dto, "55");
        Assert.assertEquals(1, num);
    }

    @Test
    public void update() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");
        try {
            transferStrategyInfoService.update(dto, "55");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TRANSFER_STRATEGY_INFO_REDIS_LOCKED);
        }

        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        TransferStrategyInfo info = new TransferStrategyInfo();
        info.setUsedFlag("Y");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(null);
        try {
            transferStrategyInfoService.update(dto, "55");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NOT_FOUND_TRANSFER_STRATEGY);
        }

        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(info);
        try {
            transferStrategyInfoService.update(dto, "55");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.STRATEGY_HAS_USED_NOT_UPDATE);
        }

        TransferStrategyInfo entity = new TransferStrategyInfo();
        PowerMockito.when(TransferStrategyInfoAssembler.toEntity(any())).thenReturn(entity);
        PowerMockito.when(transferStrategyInfoRepository.update(entity)).thenReturn(1);

        PowerMockito.when(PlanscheduleRemoteService.getLineCodeBySourceTask(any())).thenReturn(null);

        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setCraftSection("SMT-A");
        ps1.setLineCode("SMT-TZ002");
        ps1.setSourceTask("88998888");
        psWorkOrderDTOList.add(ps1);
        psWorkOrderDTOList.add(ps1);
        ps1.setSourceTask("88998888");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(any())).thenReturn(psWorkOrderDTOList);

        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder3);

        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();
        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");

        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail3);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(bSmtBomDetails);

        info.setUsedFlag("N");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(info);
        PowerMockito.when(transferStrategyInfoRepository.update(any())).thenReturn(1);

        List<PsEntityPlanBasic> psEntityPlanBasics = new ArrayList<>();
        PsEntityPlanBasic psEntityPlanBasic1 = new PsEntityPlanBasic();
        psEntityPlanBasic1.setSourceTask("8899855");
        PsEntityPlanBasic psEntityPlanBasic2 = new PsEntityPlanBasic();
        psEntityPlanBasic2.setSourceTask("8899345");
        psEntityPlanBasics.add(psEntityPlanBasic1);
        psEntityPlanBasics.add(psEntityPlanBasic2);

        PowerMockito.when(PlanscheduleRemoteService.getSourceTaskByLineCode(any())).thenReturn(psEntityPlanBasics);
        Assert.assertEquals(1, transferStrategyInfoService.update(dto, "55"));
        ;
    }

    @Test
    public void getTransferStrategyInfoById() {
        TransferStrategyInfo info = new TransferStrategyInfo();
        String transferStrategyId = "1";
        info.setTransferStrategyId(transferStrategyId);
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(info);
        TransferStrategyInfo result = transferStrategyInfoService.getTransferStrategyInfoById(transferStrategyId);
        Assert.assertEquals(info.getTransferStrategyId(), result.getTransferStrategyId());
    }

    @Test
    public void deleteById() {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(null);
        Assert.assertEquals(0, transferStrategyInfoService.deleteById(dto));

        TransferStrategyInfo transferStrategyInfo = new TransferStrategyInfo();
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(transferStrategyInfo);
        Assert.assertEquals(0, transferStrategyInfoService.deleteById(dto));

        transferStrategyInfo.setUsedFlag("Y");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(transferStrategyInfo);
        try {
            transferStrategyInfoService.deleteById(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.STRATEGY_HAS_USED_NOT_DELETE);
        }

        transferStrategyInfo.setUsedFlag("N");
        PowerMockito.when(transferStrategyInfoRepository.getTransferStrategyInfoById(any())).thenReturn(transferStrategyInfo);
        PowerMockito.when(transferStrategyInfoRepository.deleteById(any())).thenReturn(1);
        Assert.assertEquals(1, transferStrategyInfoService.deleteById(dto));
    }

    @Test
    public void checkSupportTransferStrategy() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        PowerMockito.when(PlanscheduleRemoteService.getLineCodeBySourceTask(any())).thenReturn(null);

        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setCraftSection("SMT-A");
        ps1.setLineCode("SMT-TZ002");
        ps1.setSourceTask("88998888");
        psWorkOrderDTOList.add(ps1);
        psWorkOrderDTOList.add(ps1);
        ps1.setSourceTask("88998888");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(any())).thenReturn(psWorkOrderDTOList);

        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder2 = new PsWorkOrderSmtDTO();
        workOrder2.setWorkOrderNo("8899855-B");
        workOrder2.setCraftSection("SMT-B");
        workOrder2.setCfgHeaderId("2");
        workOrder2.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        PsWorkOrderSmtDTO workOrder4 = new PsWorkOrderSmtDTO();
        workOrder4.setWorkOrderNo("8899856-B");
        workOrder4.setCraftSection("SMT-B");
        workOrder4.setCfgHeaderId("4");
        workOrder4.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder2);
        psWorkOrderSmts.add(workOrder3);
        psWorkOrderSmts.add(workOrder4);

        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail2 = new BSmtBomDetail();
        detail2.setCfgHeaderId("2");
        detail2.setMachineNo("(X4S-2)");
        detail2.setLocationNo("2-31-1");
        detail2.setModuleNo("(X4S-2)2");
        detail2.setItemCode("046030200050");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");

        BSmtBomDetail detail4 = new BSmtBomDetail();
        detail4.setCfgHeaderId("4");
        detail4.setMachineNo("(X4S-2)");
        detail4.setLocationNo("2-31-1");
        detail4.setModuleNo("(X4S-2)2");
        detail4.setItemCode("046030200050");

        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail2);
        bSmtBomDetails.add(detail3);
        bSmtBomDetails.add(detail4);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(bSmtBomDetails);
        transferStrategyInfoService.checkSupportTransferStrategy(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkPreTaskPrepareInfo() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        List<PsWorkOrderDTO> psWorkOrderDTOS = new ArrayList<>();
        Whitebox.invokeMethod(transferStrategyInfoService, "checkPreTaskPrepareInfo", dto, psWorkOrderDTOS, MessageId.WORK_ORDER_HAVE_BEEN_PREPARED);

        PsWorkOrderDTO workOrderDTO1 = new PsWorkOrderDTO();
        workOrderDTO1.setSourceTask("8899856");
        workOrderDTO1.setLineCode("SMT-TZ002");
        workOrderDTO1.setWorkOrderNo("8899856-A8899001");
        psWorkOrderDTOS.add(workOrderDTO1);

        PowerMockito.when(machineMaterialPrepareService.getPrepareOrderByWorkOrderStr(any())).thenReturn("xxx");
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "checkPreTaskPrepareInfo", dto, psWorkOrderDTOS, MessageId.WORK_ORDER_HAVE_BEEN_PREPARED);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.WORK_ORDER_HAVE_BEEN_PREPARED);
        }

        PowerMockito.when(machineMaterialPrepareService.getPrepareOrderByWorkOrderStr(any())).thenReturn(null);
        Whitebox.invokeMethod(transferStrategyInfoService, "checkPreTaskPrepareInfo", dto, psWorkOrderDTOS, MessageId.WORK_ORDER_HAVE_BEEN_PREPARED);
    }

    @Test
    public void compareSmtBomDetail() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        List<String> workOrderNoList = new ArrayList<>();
        workOrderNoList.add("8899855-A");

        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO psWorkOrderSmtDTO = new PsWorkOrderSmtDTO();
        psWorkOrderSmtDTO.setCfgHeaderId("1");
        psWorkOrderSmts.add(psWorkOrderSmtDTO);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(new ArrayList<>());
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareSmtBomDetail", dto, workOrderNoList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.WORK_ORDER_NO_CFG_ID);
        }
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);

        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetails.add(bSmtBomDetail);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareSmtBomDetail", dto, workOrderNoList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.WORK_ORDER_NO_CFG_ID);
        }

        psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder3);

        bSmtBomDetails = new ArrayList<>();

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");
        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail3);
        PowerMockito.when(PlanscheduleRemoteService.getByWorkOrders(any())).thenReturn(psWorkOrderSmts);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomDetailListById(any())).thenReturn(bSmtBomDetails);
        Whitebox.invokeMethod(transferStrategyInfoService, "compareSmtBomDetail", dto, workOrderNoList);
    }

    @Test
    public void compareBomDetails() {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");
        List<PsWorkOrderSmtDTO> psWorkOrderSmts = new ArrayList<>();
        PsWorkOrderSmtDTO workOrder1 = new PsWorkOrderSmtDTO();
        workOrder1.setWorkOrderNo("8899855-A");
        workOrder1.setCraftSection("SMT-A");
        workOrder1.setCfgHeaderId("1");
        workOrder1.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder2 = new PsWorkOrderSmtDTO();
        workOrder2.setWorkOrderNo("8899855-B");
        workOrder2.setCraftSection("SMT-B");
        workOrder2.setCfgHeaderId("2");
        workOrder2.setSourceTask("8899855");

        PsWorkOrderSmtDTO workOrder3 = new PsWorkOrderSmtDTO();
        workOrder3.setWorkOrderNo("8899856-A");
        workOrder3.setCraftSection("SMT-A");
        workOrder3.setCfgHeaderId("3");
        workOrder3.setSourceTask("8899856");

        PsWorkOrderSmtDTO workOrder4 = new PsWorkOrderSmtDTO();
        workOrder4.setWorkOrderNo("8899856-B");
        workOrder4.setCraftSection("SMT-B");
        workOrder4.setCfgHeaderId("4");
        workOrder4.setSourceTask("8899856");

        psWorkOrderSmts.add(workOrder1);
        psWorkOrderSmts.add(workOrder2);
        psWorkOrderSmts.add(workOrder3);
        psWorkOrderSmts.add(workOrder4);


        List<BSmtBomDetail> bSmtBomDetails = new ArrayList<>();

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgHeaderId("1");
        detail1.setMachineNo("(X4S-2)");
        detail1.setLocationNo("1-39-1");
        detail1.setModuleNo("(X4S-2)1");
        detail1.setItemCode("046050200038");

        BSmtBomDetail detail2 = new BSmtBomDetail();
        detail2.setCfgHeaderId("2");
        detail2.setMachineNo("(X4S-2)");
        detail2.setLocationNo("2-31-1");
        detail2.setModuleNo("(X4S-2)2");
        detail2.setItemCode("046030200050");

        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgHeaderId("3");
        detail3.setMachineNo("(X4S-2)");
        detail3.setLocationNo("1-39-1");
        detail3.setModuleNo("(X4S-2)1");
        detail3.setItemCode("046050200038");

        BSmtBomDetail detail4 = new BSmtBomDetail();
        detail4.setCfgHeaderId("4");
        detail4.setMachineNo("(X4S-2)");
        detail4.setLocationNo("2-31-1");
        detail4.setModuleNo("(X4S-2)2");
        detail4.setItemCode("046040200091");

        BSmtBomDetail detail5 = new BSmtBomDetail();
        detail4.setCfgHeaderId("5");
        detail4.setMachineNo("(X4S-2)");
        detail4.setLocationNo("2-31-1");
        detail4.setModuleNo("(X4S-2)2");
        detail4.setItemCode("046040200091");

        bSmtBomDetails.add(detail1);
        bSmtBomDetails.add(detail2);
        bSmtBomDetails.add(detail3);
        bSmtBomDetails.add(detail4);
        bSmtBomDetails.add(detail5);

        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareBomDetails", dto, psWorkOrderSmts, bSmtBomDetails);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SMT_MATERIAL_INCONSISTENT);
        }
        detail4.setItemCode("046030200050");
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareBomDetails", dto, psWorkOrderSmts, bSmtBomDetails);
        } catch (Exception e) {
        }
    }

    @Test
    public void compareLineBySameCraftSection() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setProdplanId("8899855");
        dto.setLineCode("SMT-TZ002");
        dto.setPreProdplanId("8899856");

        List<PsWorkOrderDTO> workOrderDTOS = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(any())).thenReturn(workOrderDTOS);
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareLineBySameCraftSection", dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NOT_FOUND_WORK_ORDER_BY_PRODPLAN_ID);
        }

        PsWorkOrderDTO workOrder1 = new PsWorkOrderDTO();
        workOrder1.setLineCode("SMT-TZ002");
        workOrder1.setCraftSection("SMT-A");

        PsWorkOrderDTO workOrder2 = new PsWorkOrderDTO();
        workOrder2.setLineCode("SMT-TZ002");
        workOrder2.setCraftSection("SMT-B");

        PsWorkOrderDTO workOrder3 = new PsWorkOrderDTO();
        workOrder3.setLineCode("SMT-TZ002");
        workOrder3.setCraftSection("SMT-A");

        PsWorkOrderDTO workOrder4 = new PsWorkOrderDTO();
        workOrder4.setLineCode("SMT-TZ001");
        workOrder4.setCraftSection("SMT-B");

        workOrderDTOS.add(workOrder1);
        workOrderDTOS.add(workOrder2);
        workOrderDTOS.add(workOrder3);
        workOrderDTOS.add(workOrder4);

        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareLineBySameCraftSection", dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.CRAFT_SECTION_NOT_SCHEDULED_SAME_LINE);
        }

        workOrder4.setLineCode("SMT-TZ002");
        try {
            Whitebox.invokeMethod(transferStrategyInfoService, "compareLineBySameCraftSection", dto);
        } catch (Exception e) {
        }
    }

    @Test
    public void getList() throws Exception {
        List<TransferStrategyInfo> list = new ArrayList<>();
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(list);
        List<TransferStrategyInfo> listRes = transferStrategyInfoService.getList(null);
        Assert.assertEquals(0, listRes.size());
    }

    @Test
    public void getAndCheck() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        dto.setLineCode("LineCode");
        dto.setProdplanId("ProdplanId");
        List<TransferStrategyInfo> transferStrategyInfos = new ArrayList<>();
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(transferStrategyInfos);
        TransferStrategyInfoDTO andCheck = transferStrategyInfoService.getAndCheck(dto);
        Assert.assertNull(andCheck);

        TransferStrategyInfo transferStrategyInfo = new TransferStrategyInfo();
        transferStrategyInfos.add(transferStrategyInfo);
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(transferStrategyInfos);

        try {
            transferStrategyInfoService.getAndCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TRANSFER_STRATEGY_CONFIGURATION_LINE_MISMATCH, e.getMessage());
        }

        transferStrategyInfo.setLineCode("LineCode");
        transferStrategyInfo.setUsedFlag("Y");
        try {
            transferStrategyInfoService.getAndCheck(dto);
        } catch (Exception e) {
//            Assert.assertEquals("null", e.getMessage());
        }

        TransferStrategyInfoDTO dto1 = new TransferStrategyInfoDTO();
        PowerMockito.when(TransferStrategyInfoAssembler.toDTO(any())).thenReturn(dto1);
        dto1.setUsedFlag("Y");
        dto1.setLineCode("LineCode");
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(transferStrategyInfos);
        transferStrategyInfoService.getAndCheck(dto);
    }

    @Test
    public void markUsed() throws Exception {
        try {
            int a = transferStrategyInfoService.markUsed(null,null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_FOUND_TRANSFER_STRATEGY, e.getExMsgId());
        }

        try {
            int a = transferStrategyInfoService.markUsed("111",null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_FOUND_TRANSFER_STRATEGY, e.getExMsgId());
        }

        List<TransferStrategyInfo> transferStrategyInfos = new ArrayList<>();
        TransferStrategyInfo transferStrategyInfo = new TransferStrategyInfo();
        transferStrategyInfo.setLineCode("lineCode1");
        transferStrategyInfos.add(transferStrategyInfo);
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(transferStrategyInfos);
        PowerMockito.when(transferStrategyInfoRepository.update(any())).thenReturn(1);
        int i = transferStrategyInfoService.markUsed("111","lineCode1");
        transferStrategyInfoService.markUsed("111","lineCode2");
        Assert.assertEquals(1, i);
    }

    @Test
    public void checkCompositePrepareTransferStrategy() throws Exception {
        TransferStrategyInfoDTO dto = new TransferStrategyInfoDTO();
        transferStrategyInfoService.checkCompositePrepareTransferStrategy(dto);
        Assert.assertEquals(null, dto.getPreProdplanId());

        List<TransferStrategyInfo> list = new ArrayList<>();
        list.add(new TransferStrategyInfo() {{
            setLineCode("line");
            setProdplanId("prodPlanId");
            setPreProdplanId("preProdPlanId");
        }});
        PowerMockito.when(transferStrategyInfoRepository.getList(any())).thenReturn(list);
        try {
            transferStrategyInfoService.checkCompositePrepareTransferStrategy(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.STRATEGY_HAS_BEEN_MAINTAINED);
        }
    }
}