package com.zte.application.impl;

import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.springbootframe.common.redis.RedisLock;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PmRepairDetailServiceImpl.class,ObtainRemoteServiceDataUtil.class,CommonUtils.class})
public class PmRepairDetailServiceImpl1Test {
    @InjectMocks
    private PmRepairDetailServiceImpl service;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private PmRepairDetailRepository pmRepairInfoRepository;

    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    AvlService avlService;

    @Mock
    WipScanHistoryService wipScanHistoryService;

    @Mock
    PmRepairRcvDetailServiceImpl pmRepairRcvDetailServiceImp;

    @Mock
    SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    IfisService ifisService;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ValueOperations<String, String> redisOpsValue;

    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Before
    public void init() {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void postRepairInfoBatch() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(any(), any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(true);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(null);

        List<PmRepairDetailDTO> dtos = getDtos();
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.WIP_INFO_DATA_NOT_FOUND);
        try {
            Assert.assertNotNull(service.postRepairInfoBatch(dtos));
        } catch (Exception returnMessage) {
            Assert.assertEquals(MessageId.WIP_INFO_DATA_NOT_FOUND, returnMessage.getMessage());
        }
    }

    @Test
    public void postRepairInfoBatchNoSn() throws Exception {
        service.postRepairInfoBatch(null);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);


        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        Boolean fooBar = true;
        PowerMockito.when(redisOpsValue.setIfAbsent(any(), any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(fooBar);

        List<PmRepairDetailDTO> dtos = getDtos();
        dtos.forEach(p -> p.setSnType("2"));

        PowerMockito.when(pmRepairRcvService.getNoSnRepairSns(any())).thenReturn(Arrays.asList("725499800017"));
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(null);
        PowerMockito.when(pmRepairRcvDetailRepository.getToRepairRecInfo(any())).thenReturn(null);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.RCV_INFO_ERROR);
        try {
            service.postRepairInfoBatch(dtos);
        } catch (Exception returnMessage) {
            Assert.assertEquals(MessageId.RCV_INFO_ERROR, returnMessage.getMessage());
        }

    }

    @Test
    public void iFisConsume() throws Exception {
        List<PmRepairDetailDTO> dtos = getDtos();
        List<BsPubHrvOrgId> bsPubHrvOrgIds = new ArrayList<>();
        BsPubHrvOrgId bsPubHrvOrgId = new BsPubHrvOrgId();
        bsPubHrvOrgIds.add(bsPubHrvOrgId);
        bsPubHrvOrgId.setUserName("XXX");
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo("10291234")).thenReturn(bsPubHrvOrgIds);
        PowerMockito.when(ifisService.consumeUserProvideMaterial(any(), any())).thenReturn(false);

        try {
            service.iFisConsume(dtos, "1111");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_IFIS_ERROR, e.getMessage());
        }
    }

    private List<PmRepairDetailDTO> getDtos() {
        List<PmRepairDetailDTO> dtos = new ArrayList<>();

        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setSn("725499800013");
        dto.setRcvProdplanId("7254998");
        dto.setAdverseType("adverseType");
        dto.setErrorCode("200");
        dto.setErrorDescription("摔坏111");
        dto.setIsLocationNo("isLocationNo");
        dto.setLocationNo("B11");
        dto.setRepairBy("10291234");
        dto.setReplaceSn("007900210913000082");
        dto.setFactoryId(new BigDecimal(52));
        dtos.add(dto);

        PmRepairDetailDTO dto1 = new PmRepairDetailDTO();
        dto1.setSn("725499800013");
        dto1.setRcvProdplanId("7254998");
        dto1.setAdverseType("adverseType");
        dto1.setErrorCode("100");
        dto1.setErrorDescription("摔坏");
        dto1.setIsLocationNo("isLocationNo");
        dto1.setLocationNo("B12");
        dto1.setRepairBy("10291234");
        dto1.setReplaceSn("007900210913000082");
        dto1.setFactoryId(new BigDecimal(53));
        dtos.add(dto1);
        return dtos;
    }

    @Test
    public void setReplaceSnInfo() throws Exception {
        List<PmRepairDetailDTO> dtos = new ArrayList<>();
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dtos.add(dto);
        dto.setReplaceSn("1111");
        PmRepairDetailDTO dto1 = new PmRepairDetailDTO();
        dtos.add(dto1);
        dto1.setReplaceSn(MpConstant.ZTEXN + "2222");
        List<BarcodeExpandDTO> expandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("1111");
        barcodeExpandDTO.setSupplierName("SupplierName");
        barcodeExpandDTO.setBrandName("setBrandName");
        barcodeExpandDTO.setSpecModel("setSpecModel");
        barcodeExpandDTO.setItemCode("setItemCode");
        expandDTOList.add(barcodeExpandDTO);

        BarcodeExpandDTO barcodeExpandDTO1 = new BarcodeExpandDTO();
        barcodeExpandDTO1.setBarcode("2222");
        barcodeExpandDTO1.setSupplierName("SupplierName2222");
        barcodeExpandDTO1.setBrandName("setBrandName2222");
        barcodeExpandDTO1.setSpecModel("setSpecModel2222");
        barcodeExpandDTO1.setItemCode("setItemCode2222");
        expandDTOList.add(barcodeExpandDTO1);

        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(expandDTOList);

        Whitebox.invokeMethod(service, "setReplaceSnInfo", dtos);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

}
