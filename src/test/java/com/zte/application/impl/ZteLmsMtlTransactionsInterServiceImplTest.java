package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PmRepairRcvService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ZteLmsMtlTransactionsInterRepository;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.ZteLmsMtlTransactionsInterDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashMap;
import java.util.Map;

@PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class,CommonUtils.class})
public class ZteLmsMtlTransactionsInterServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ZteLmsMtlTransactionsInterServiceImpl service;
    @Mock
    private PmRepairRcvService pmRepairRcvService;
    @Mock
    private ZteLmsMtlTransactionsInterRepository zteLmsMtlTransactionsInterRepository;
    @Mock
    private FactoryConfig factoryConfig;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void extractedCheckAndSet() {
        service.extractedCheckAndSet(Lists.newArrayList(new ZteLmsMtlTransactionsInterDTO()),
                Lists.newArrayList(new ZteLmsMtlTransactionsInterDTO()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertZteLmsMtlTransactionsInterSelective() throws MesBusinessException {
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("111");
        ZteLmsMtlTransactionsInterDTO dto = new ZteLmsMtlTransactionsInterDTO();

        PowerMockito.when(pmRepairRcvService.newBillNo(Constant.STR_EMPTY,Constant.OUT_BOUND,4)).thenReturn("11111");
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.insertZteLmsMtlTransactionsInterSelective(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1,service.insertZteLmsMtlTransactionsInterSelective(dto));

    }
}