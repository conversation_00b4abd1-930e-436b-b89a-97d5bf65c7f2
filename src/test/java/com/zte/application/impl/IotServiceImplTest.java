package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.application.SnCollectInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.EmEqpScannerRecordDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.anyString;

@PrepareForTest({BasicsettingRemoteService.class, EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class, CommonUtils.class,SpringContextUtil.class})
public class IotServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    IotServiceImpl service;
    @Mock
    PsWipInfoService psWipInfoService;
    @Mock
    SnCollectInfoService snCollectInfoService;

    @Before
    public void init() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
    }
    @Test
    public void verificationResults() throws MesBusinessException {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, EqpmgmtsRemoteService.class);
        EmEqpScannerRecordDTO emEqpScannerRecordDTO = new EmEqpScannerRecordDTO();
        service.verificationResults(emEqpScannerRecordDTO,"",new ArrayList<>());
        Assert.assertNotNull(emEqpScannerRecordDTO);
        try{
            service.verificationResults(emEqpScannerRecordDTO,"spi",new ArrayList<>());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPI_NOT_PASS, e.getExMsgId());
        }
        try{
            service.verificationResults(emEqpScannerRecordDTO,"aoi2",new ArrayList<>());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.AOI_NOT_PASS, e.getExMsgId());
        }
        try{
            service.verificationResults(emEqpScannerRecordDTO,"m3",new ArrayList<>());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SCANNER_RECORD_NOT_PASS, e.getExMsgId());
        }
        PowerMockito.when(EqpmgmtsRemoteService.checkSpiResult(any(), any())).thenReturn(true);
        PowerMockito.when(EqpmgmtsRemoteService.checkPasterResult(any(), any(), any())).thenReturn(true);
        PowerMockito.when(EqpmgmtsRemoteService.checkAoiResult(any(), any(), any())).thenReturn(true);
        service.verificationResults(emEqpScannerRecordDTO,"spi,m3,m6,aoi2",new ArrayList<>());
    }

    @Test
    public void snCheck() throws MesBusinessException {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("error");

        EmEqpScannerRecordDTO scannerRecord = new EmEqpScannerRecordDTO();
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STATION_NOT_EXIT, e.getExMsgId());
        }

        scannerRecord.setStation("printer");
        // 条码为空
        service.snCheck(scannerRecord);

        // 临时码
        scannerRecord.setSn("7777666D00001");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(null);
        service.snCheck(scannerRecord);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        service.snCheck(scannerRecord);


        // 大板条码
        scannerRecord.setSn("P777766600001");
        /*PowerMockito.when(psWipInfoService.getWipInfoBySn(anyString())).thenReturn(null);
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_NOT_EXIST, e.getExMsgId());
        }*/

        // 大板条码
        /*scannerRecord.setSn("777766600001");
        PsWipInfo psWipInfo = new PsWipInfo();
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyString())).thenReturn(psWipInfo);*/
        service.snCheck(scannerRecord);

        scannerRecord.setStation("m3");
        scannerRecord.setSn("");
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_FORMAT_MSG, e.getExMsgId());
        }

        scannerRecord.setSn("P777766600001");
        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("");
        service.snCheck(scannerRecord);

        scannerRecord.setSn("7777666D00001");
        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("");
        service.snCheck(scannerRecord);

        scannerRecord.setSn("777766600001");
        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("");
        service.snCheck(scannerRecord);

        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("spi");
        PowerMockito.when(EqpmgmtsRemoteService.checkSpiResult(anyString(), anyList())).thenReturn(false);
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPI_NOT_PASS, e.getExMsgId());
        }

        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("aoi1");
        PowerMockito.when(EqpmgmtsRemoteService.checkAoiResult(anyString(), anyString(), anyList())).thenReturn(false);
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.AOI_NOT_PASS, e.getExMsgId());
        }

        PowerMockito.when(BasicsettingRemoteService.getCheckStation(anyString(), anyString())).thenReturn("m3,m6");
        PowerMockito.when(EqpmgmtsRemoteService.checkAoiResult(anyString(), anyString(), anyList())).thenReturn(false);
        try{
            service.snCheck(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SCANNER_RECORD_NOT_PASS, e.getExMsgId());
        }
    }
}
