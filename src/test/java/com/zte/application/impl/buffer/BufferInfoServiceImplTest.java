package com.zte.application.impl.buffer;

import com.zte.application.WarehouseEntryInfoService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.RedisKeyConstant;
import com.zte.domain.model.LinesideWarehouseInfo;
import com.zte.domain.model.MtlItemLocations;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.buffer.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@Slf4j
@PrepareForTest({MicroServiceRestUtil.class, BasicsettingRemoteService.class, ProductionDeliveryRemoteService.class})
public class BufferInfoServiceImplTest {

    @InjectMocks
    private BufferInfoServiceImpl bufferInfoService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Mock
    private IdGenerator idGenerator;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private HttpServletRequest request;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Before
    public void setUp() {
        // 启用部分 Mock
        bufferInfoService = PowerMockito.spy(bufferInfoService);
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
    }

    @Test
    public void testGetBufferInfo_WhenTaskTypeInvalid_ReturnNull() throws Exception {
        // 1. 模拟远程调用返回非缓冲任务类型数据
        List<PsTaskExtendedDTO> mockTasks = new ArrayList<>();
        when(centerfactoryRemoteService.bulkQueriesByTaskNos(any())).thenReturn(mockTasks);
        // 正确 Mock 实例方法
        PowerMockito.doReturn(false)
                .when(bufferInfoService)
                .isBufferTaskType(anyList());

        BufferInfoFormVO mockBuffer = PowerMockito.mock(BufferInfoFormVO.class);
        doCallRealMethod().when(bufferInfoService).getBufferInfo(mockBuffer);

        BufferInfoVO bufferInfo = bufferInfoService.getBufferInfo(mockBuffer);

        Assert.assertNull(bufferInfo);
    }

    @Test
    public void testGetBufferInfo_WhenTaskTypeInvalid_ReturnNull1() throws Exception {
        // 1. 模拟远程调用返回非缓冲任务类型数据
        List<PsTaskExtendedDTO> mockTasks = new ArrayList<>();
        when(centerfactoryRemoteService.bulkQueriesByTaskNos(any())).thenReturn(mockTasks);
        // 正确 Mock 实例方法
        PowerMockito.doReturn(true)
                .when(bufferInfoService)
                .isBufferTaskType(anyList());

        // 正确 Mock 实例方法
        PowerMockito.doReturn(false)
                .when(bufferInfoService)
                .validTaskStatus(any());

        BufferInfoFormVO mockBuffer = PowerMockito.mock(BufferInfoFormVO.class);
        doCallRealMethod().when(bufferInfoService).getBufferInfo(mockBuffer);

        BufferInfoVO bufferInfo = bufferInfoService.getBufferInfo(mockBuffer);

        Assert.assertNull(bufferInfo);
    }

    @Test
    public void testGetBufferInfo_WhenConditionsMet_ReturnNotNull() throws Exception {

        // 1. 模拟远程调用返回非缓冲任务类型数据
        List<PsTaskExtendedDTO> mockTasks = new ArrayList<>();
        PsTaskExtendedDTO mock = PowerMockito.mock(PsTaskExtendedDTO.class);
        mockTasks.add(mock);
        when(centerfactoryRemoteService.bulkQueriesByTaskNos(any())).thenReturn(mockTasks);
        // 正确 Mock 实例方法
        PowerMockito.doReturn(true)
                .when(bufferInfoService)
                .isBufferTaskType(anyList());

        // 正确 Mock 实例方法
        PowerMockito.doReturn(true)
                .when(bufferInfoService)
                .validTaskStatus(any());

        // 4. Mock核心方法返回非空对象
        BufferInfoVO expectedVO = new BufferInfoVO();
        expectedVO.setWarehouseEntryInfoList(new ArrayList<>());

        PowerMockito.doReturn(expectedVO)
                .when(bufferInfoService, "getPendingTransferInfo",
                        any(BufferInfoFormVO.class));

        BufferInfoFormVO mockBuffer = PowerMockito.mock(BufferInfoFormVO.class);
        doCallRealMethod().when(bufferInfoService).getBufferInfo(mockBuffer);

        BufferInfoVO bufferInfo = bufferInfoService.getBufferInfo(mockBuffer);
        Assert.assertNotNull(bufferInfo);

    }

    // 分支覆盖用例1：空列表场景
    @Test
    public void testIsBufferTaskType_WhenListEmpty_ReturnFalse() {
        // 构造空数据
        List<PsTaskExtendedDTO> emptyList = Collections.emptyList();

        // 执行验证
        doCallRealMethod().when(bufferInfoService).isBufferTaskType(emptyList);
        boolean result = bufferInfoService.isBufferTaskType(emptyList);
        Assert.assertFalse(result);
    }

    // 分支覆盖用例2：非空列表+非缓冲类型
    @Test
    public void testIsBufferTaskType_WhenTypeNotBuffer_ReturnFalse() {
        // 构造非缓冲类型数据
        List<PsTaskExtendedDTO> mockList = new ArrayList<>();
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskType("NORMAL_TASK");  // 非缓冲类型
        mockList.add(dto);

        // 执行验证
        doCallRealMethod().when(bufferInfoService).isBufferTaskType(mockList);
        boolean result = bufferInfoService.isBufferTaskType(mockList);
        Assert.assertFalse(result);
    }

    // 分支覆盖用例3：非空列表+缓冲类型
    @Test
    public void testIsBufferTaskType_WhenTypeIsBuffer_ReturnTrue() {
        // 构造缓冲类型数据
        List<PsTaskExtendedDTO> mockList = new ArrayList<>();
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskType(Constant.TASK_TYPE_BUFFER);  // 缓冲类型常量
        mockList.add(dto);

        // 执行验证
        doCallRealMethod().when(bufferInfoService).isBufferTaskType(mockList);
        boolean result = bufferInfoService.isBufferTaskType(mockList);
        Assert.assertTrue(result);
    }

    // 分支覆盖用例1：taskInfo为空
    @Test
    public void testValidTaskStatus_WhenTaskInfoNull_ReturnFalse() {
        // 模拟远程服务返回null
        when(centerfactoryRemoteService.getTaskInfo(anyString())).thenReturn(null);

        // 执行验证
        doCallRealMethod().when(bufferInfoService).validTaskStatus("TASK_001");
        boolean result = bufferInfoService.validTaskStatus("TASK_001");

        Assert.assertFalse(result);
    }

    // 分支覆盖用例2：taskInfo非空且状态不匹配
    @Test
    public void testValidTaskStatus_WhenStatusInvalid_ReturnFalse() {
        // 构造不匹配的DTO
        PsTaskDTO mockTask = new PsTaskDTO();
        mockTask.setConfirmationStatus("INVALID_STATUS");
        when(centerfactoryRemoteService.getTaskInfo(anyString())).thenReturn(mockTask);

        // 执行验证
        doCallRealMethod().when(bufferInfoService).validTaskStatus("TASK_002");
        boolean result = bufferInfoService.validTaskStatus("TASK_002");
        Assert.assertFalse(result);
    }

    // 分支覆盖用例3：taskInfo非空且状态匹配
    @Test
    public void testValidTaskStatus_WhenStatusValid_ReturnTrue() {
        // 构造匹配的DTO
        PsTaskDTO mockTask = new PsTaskDTO();
        mockTask.setConfirmationStatus(Constant.TASK_STATUS_P_TRANSFER);
        when(centerfactoryRemoteService.getTaskInfo(anyString())).thenReturn(mockTask);

        // 执行验证
        doCallRealMethod().when(bufferInfoService).validTaskStatus("TASK_003");
        boolean result = bufferInfoService.validTaskStatus("TASK_003");
        Assert.assertTrue(result);
    }

    // 分支1：resultDetailList为空
    @Test
    public void testGetPendingTransferInfo_WhenDetentionListEmpty_ReturnNull() throws Exception {
        // Mock系统配置数据
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_VALUE_WAREHOUSE);
        // 2. Mock远程服务响应
        List<SysLookupTypesDTO> mockTypes = Collections.singletonList(new SysLookupTypesDTO());

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        Map<String, String> mockWarehouseMap = createMockWarehouseMap();
        // Mock仓库编码解析
        PowerMockito.doReturn(mockWarehouseMap)
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        // Mock获取分页数据为空
        PowerMockito.doReturn(Collections.emptyList())
                .when(bufferInfoService, "getPageDetentionList",
                        any(BufferInfoFormVO.class), any(String[].class));

        BufferInfoFormVO bufferInfoFormVO = new BufferInfoFormVO();
        doCallRealMethod().when(bufferInfoService).getPendingTransferInfo(bufferInfoFormVO);
        BufferInfoVO result = bufferInfoService.getPendingTransferInfo(bufferInfoFormVO);
        Assert.assertNull(result);
    }

    // 分支3：正常返回数据
    @Test
    public void testGetPendingTransferInfo_WhenAllValid_ReturnVO() throws Exception {
        // Mock系统配置数据
        List<SysLookupTypesDTO> mockTypes = Arrays.asList(
        );
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        // Mock仓库编码解析
        PowerMockito.doReturn(createMockWarehouseMap())
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        // Mock分页数据
        List<WarehouseEntryInfoDetailDTO> mockList = new ArrayList<>();
        WarehouseEntryInfoDetailDTO mock = PowerMockito.mock(WarehouseEntryInfoDetailDTO.class);
        mockList.add(mock);
        PowerMockito.doReturn(mockList)
                .when(bufferInfoService, "getPageDetentionList",
                        any(BufferInfoFormVO.class), any(String[].class));

        BufferInfoFormVO bufferInfoFormVO = new BufferInfoFormVO();
        doCallRealMethod().when(bufferInfoService).getPendingTransferInfo(bufferInfoFormVO);
        BufferInfoVO result = bufferInfoService.getPendingTransferInfo(bufferInfoFormVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetPageDetentionList_WhenResultEmpty_ReturnEmptyList() {
        // 准备参数
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        formVO.setPage(1L);
        formVO.setRows(10L);
        formVO.setTaskNo("TASK_001");

        String[] bufferCodes = new String[]{"WH001"};
        String[] goodCodes = new String[]{"WH002"};

        // Mock仓库查询返回空
        when(warehouseEntryInfoRepository.getPagePendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(Collections.emptyList());

        doCallRealMethod().when(bufferInfoService).getPageDetentionList(
                formVO, bufferCodes);
        // 执行测试
        List<WarehouseEntryInfoDetailDTO> result = bufferInfoService.getPageDetentionList(
                formVO, bufferCodes);

        // 验证结果
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPageDetentionList_WhenDataExists_ReturnList() {

        // 准备参数
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        String[] bufferCodes = new String[0];
        String[] goodCodes = new String[0];

        // 准备测试数据
        List<WarehouseEntryInfoDetailDTO> mockData = Arrays.asList(
                new WarehouseEntryInfoDetailDTO(),
                new WarehouseEntryInfoDetailDTO()
        );

        // Mock仓库返回数据
        when(warehouseEntryInfoRepository.getPagePendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(mockData);

        // 执行测试
        doCallRealMethod().when(bufferInfoService).getPageDetentionList(
                formVO, bufferCodes);
        List<WarehouseEntryInfoDetailDTO> result = bufferInfoService.getPageDetentionList(
                formVO, bufferCodes);

        // 验证结果
        Assert.assertEquals(2, result.size());
    }
    @Test
    public void testAssembleTargetWarehouseInfo_WhenWarehouseNull_ReturnNull() throws Exception {
        // Mock静态方法返回null

        when(ProductionDeliveryRemoteService.queryWarehouseByCode(anyString())).thenReturn(null);

        // 执行测试
        doCallRealMethod().when(bufferInfoService).assembleTargetWarehouseInfo("WH001");
        WarehouseInfoVO result = bufferInfoService.assembleTargetWarehouseInfo("WH001");

        Assert.assertNull(result);
    }

    @Test
    public void testAssembleTargetWarehouseInfo_WhenDataValid_ReturnVO() throws Exception {
        // 1. Mock静态方法返回有效仓库数据
        LinesideWarehouseInfo mockWarehouse = new LinesideWarehouseInfo();
        mockWarehouse.setErpSubinventory("SUB001");
        mockWarehouse.setErpGoodsallocation("LOC001");
        mockWarehouse.setWarehouseType("GOOD");

        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        when(ProductionDeliveryRemoteService.queryWarehouseByCode(anyString()))
                .thenReturn(mockWarehouse);

        // 2. Mock实例方法返回货位数据
        MtlItemLocations mtlItemLocations = new MtlItemLocations();
        mtlItemLocations.setAttribute1("A-1");
        List<MtlItemLocations> mockLocations = Arrays.asList(
                mtlItemLocations
        );

        PowerMockito.doReturn(mockLocations)
                .when(bufferInfoService, "getGoodsMtlItemLocations", anyString(), anyString());

        // 3. 执行测试
        doCallRealMethod().when(bufferInfoService).assembleTargetWarehouseInfo("WH002");
        WarehouseInfoVO result = bufferInfoService.assembleTargetWarehouseInfo("WH002");

        // 4. 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetGoodsMtlItemLocations_WhenLocationListNull_ReturnEmptyList() throws Exception {

        when(BasicsettingRemoteService.mtlItemLocations(any(MtlItemLocationsDTO.class))).thenReturn(null);

        // 执行测试
        String erpSubInvestory = "SUB001";
        String erpGoodsllocation ="LOC1,LOC2";
        doCallRealMethod().when(bufferInfoService).getGoodsMtlItemLocations(erpSubInvestory, erpGoodsllocation);
        List<MtlItemLocations> result = bufferInfoService.getGoodsMtlItemLocations(erpSubInvestory, erpGoodsllocation);

        // 验证结果
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGoodsMtlItemLocations_WhenErpGoodsallocationEmpty_ReturnEmptyList() throws Exception {
        // Mock静态方法返回非空列表
        MtlItemLocations mtlItemLocations = new MtlItemLocations();
        mtlItemLocations.setSegment2("LOC3");

        List<MtlItemLocations> mockList = Arrays.asList(
                mtlItemLocations
        );
        when(BasicsettingRemoteService.mtlItemLocations(any(MtlItemLocationsDTO.class))).thenReturn(mockList);

        // 执行测试（传入空货位编码）
        String erpSubInvestory = "SUB001";
        doCallRealMethod().when(bufferInfoService).getGoodsMtlItemLocations(erpSubInvestory, null);
        List<MtlItemLocations> result = bufferInfoService.getGoodsMtlItemLocations("SUB001", null);

        // 验证结果
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetGoodsMtlItemLocations_WhenDataValid_ReturnFilteredList() throws Exception {
        // 构造测试数据
        MtlItemLocations validItem = new MtlItemLocations();
        validItem.setSegment2("LOC1");
        MtlItemLocations invalidItem = new MtlItemLocations();
        invalidItem.setSegment2("LOC2");
        List<MtlItemLocations> mockList = Arrays.asList(validItem, invalidItem, null);

        // Mock静态方法返回数据
        when(BasicsettingRemoteService.mtlItemLocations(any(MtlItemLocationsDTO.class))).thenReturn(mockList);

        // 执行测试（货位编码包含LOC1）
        String erpSubInvestory = "SUB001";
        String erpGoodsllocation ="LOC1";
        doCallRealMethod().when(bufferInfoService).getGoodsMtlItemLocations(erpSubInvestory, erpGoodsllocation);
        List<MtlItemLocations> result = bufferInfoService.getGoodsMtlItemLocations(erpSubInvestory, erpGoodsllocation);

        // 验证结果
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testGetWarehouseCodeMap_EmptyList_ReturnEmptyMap() {
        // 构造空列表
        List<SysLookupTypesDTO> emptyList = Collections.emptyList();

        // 执行测试
        doCallRealMethod().when(bufferInfoService).getWarehouseCodeMap(emptyList);
        Map<String, String> result = bufferInfoService.getWarehouseCodeMap(emptyList);

        // 验证结果
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetWarehouseCodeMap_GoodTypeOnly_ReturnGoodEntry() {
        // 构造含良品仓库的数据
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO type = new SysLookupTypesDTO();
        type.setLookupCode(BigDecimal.valueOf(7502002));
        type.setAttribute2("WH003");

        SysLookupTypesDTO goodType = new SysLookupTypesDTO();
        goodType.setLookupCode(BigDecimal.valueOf(7502001));
        goodType.setAttribute2("WH004");

        sysLookupTypesDTOS.add(type);
        sysLookupTypesDTOS.add(goodType);

        // 执行测试
        doCallRealMethod().when(bufferInfoService).getWarehouseCodeMap(sysLookupTypesDTOS);
        Map<String, String> result = bufferInfoService.getWarehouseCodeMap(sysLookupTypesDTOS);

        // 验证结果
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testValidPendingSn_WhenAllConditionsMet_ReturnTrue() throws Exception {
        // 1. Mock远程配置服务响应
        List<SysLookupTypesDTO> mockTypes = Arrays.asList(
                createLookupDTO(Constant.LOOKUP_CODE_BUFFER_WAREHOUSE, "WH001,WH002"),
                createLookupDTO(Constant.LOOKUP_CODE_GOOD_WAREHOUSE, "WH003")
        );

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        // 2. Mock仓库编码解析方法
        PowerMockito.doReturn(createMockWarehouseMap())
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        // 3. Mock仓库查询返回有效数据
        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setSn("SN123");
        List<WarehouseEntryInfoDetailDTO> mockList = new ArrayList<>();
        mockList.add(warehouseEntryInfoDetailDTO);
        when(warehouseEntryInfoRepository.getPendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(mockList);

        // 4. 执行测试
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        formVO.setTaskNo("TASK_001");
        List<String> sns = new ArrayList<>();
        sns.add("SN123");
        formVO.setSnList(sns);
        // 2. Mock仓库编码解析方法
        Map<String, Object> map = new HashMap<String, Object>();

        doCallRealMethod().when(bufferInfoService).validPendingSn(formVO);
        boolean result = bufferInfoService.validPendingSn(formVO);

        Assert.assertTrue(result);
    }

    @Test
    public void testValidPendingSn_WhenAllConditionsMet_ReturnFalse() throws Exception {
        // 1. Mock远程配置服务响应
        List<SysLookupTypesDTO> mockTypes = Arrays.asList(
                createLookupDTO(Constant.LOOKUP_CODE_BUFFER_WAREHOUSE, "WH001,WH002"),
                createLookupDTO(Constant.LOOKUP_CODE_GOOD_WAREHOUSE, "WH003")
        );

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        // 2. Mock仓库编码解析方法
        PowerMockito.doReturn(createMockWarehouseMap())
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        // 3. Mock仓库查询返回有效数据
        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setSn("SN1231");
        List<WarehouseEntryInfoDetailDTO> mockList = new ArrayList<>();
        mockList.add(warehouseEntryInfoDetailDTO);
        when(warehouseEntryInfoRepository.getPendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(mockList);

        // 4. 执行测试
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        formVO.setTaskNo("TASK_001");
        List<String> sns = new ArrayList<>();
        sns.add("SN123");
        formVO.setSnList(sns);
        // 2. Mock仓库编码解析方法
        Map<String, Object> map = new HashMap<String, Object>();

        doCallRealMethod().when(bufferInfoService).validPendingSn(formVO);
        boolean result = bufferInfoService.validPendingSn(formVO);

        Assert.assertFalse(result);
    }

    @Test
    public void testValidPendingSn_WhenNoDataFound_ReturnFalse() throws Exception {
        // 1. Mock有效配置数据
        when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(
                Collections.singletonList(createLookupDTO(Constant.LOOKUP_CODE_BUFFER_WAREHOUSE, "WH001"))
        );

        // 2. Mock仓库映射
        PowerMockito.doReturn(new HashMap<String, String>() {{
            put("buffer", "WH001");
            put("good", "WH002");
        }}).when(bufferInfoService, "getWarehouseCodeMap", anyList());

        // 3. Mock空查询结果
        when(warehouseEntryInfoRepository.getPendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(Collections.emptyList());

        // 4. 执行测试
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        doCallRealMethod().when(bufferInfoService).validPendingSn(formVO);
        boolean result = bufferInfoService.validPendingSn(formVO);

        Assert.assertFalse(result);
    }

    @Test
    public void testGetCountReturnTrue() throws Exception {
        // 1. Mock远程配置服务响应
        List<SysLookupTypesDTO> mockTypes = Arrays.asList(
                createLookupDTO(Constant.LOOKUP_CODE_BUFFER_WAREHOUSE, "WH001,WH002"),
                createLookupDTO(Constant.LOOKUP_CODE_GOOD_WAREHOUSE, "WH003")
        );

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        // 2. Mock仓库编码解析方法
        PowerMockito.doReturn(createMockWarehouseMap())
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        when(warehouseEntryInfoRepository.getCountPendingWarehouseEntryInfoDetail(anyMap()))
                .thenReturn(1L);

        // 4. 执行测试
        BufferInfoFormVO formVO = new BufferInfoFormVO();
        formVO.setTaskNo("TASK_001");
        List<String> sns = new ArrayList<>();
        sns.add("SN123");
        formVO.setSnList(sns);
        doCallRealMethod().when(bufferInfoService).getCount(formVO);
        Long count = bufferInfoService.getCount(formVO);

        Assert.assertTrue(count.equals(1L));
    }


    @Test
    public void testSubmitTransfer_WhenLockFailed_ThrowException() throws Exception {
        // Mock RedisLock返回锁失败
        RedisLock mockLock = mock(RedisLock.class);
        when(mockLock.lock()).thenReturn(false);
        BufferInfoServiceImpl serviceSpy = spy(bufferInfoService);
        // 或者精确匹配（需确保测试数据与业务代码一致）
        doReturn(mockLock).when(serviceSpy).getRedisLock(RedisKeyConstant.SAVE_WAREHOUSE_ENTRY_TRANSFER_LOCK + "TASK_001");

        HttpServletRequest mock = PowerMockito.mock(HttpServletRequest.class);
        BufferToBeTransferFormVO vo = new BufferToBeTransferFormVO();
        vo.setTaskNo("TASK_001");

        // 执行测试并验证异常
        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> serviceSpy.submitTransfer(mock, vo));

        // 验证错误码和锁释放
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        verify(mockLock).unlock(); // [2,3](@ref)
    }

    @Test
    public void testSubmitTransfer_WhenAllSuccess_ExecuteFlow() throws Exception {
        // Mock 所有依赖成功
        RedisLock mockLock = mock(RedisLock.class);
        when(mockLock.lock()).thenReturn(true);
        BufferInfoServiceImpl serviceSpy = spy(bufferInfoService);
        doReturn(mockLock).when(serviceSpy).getRedisLock(anyString());

        BufferToBeTransferFormVO bufferToBeTransferFormVO = new BufferToBeTransferFormVO();
        doNothing().when(serviceSpy).validateSingleGroup(bufferToBeTransferFormVO);
        doNothing().when(serviceSpy).validAllSn(bufferToBeTransferFormVO);

        // 执行正常流程
        HttpServletRequest mock = PowerMockito.mock(HttpServletRequest.class);
        doNothing().when(serviceSpy).insertTransferWarehouseEntryInfo(bufferToBeTransferFormVO, mock);

        doCallRealMethod().when(serviceSpy).submitTransfer(mock, bufferToBeTransferFormVO);
        serviceSpy.submitTransfer(mock, bufferToBeTransferFormVO);

        verify(serviceSpy, Mockito.times(1)).submitTransfer(mock, bufferToBeTransferFormVO);
    }

    @Test
    public void testGetRedisLock_WithValidKey_ReturnInitializedLock() throws Exception {
        // 1. 构造测试数据
        String validKey = "transfer_lock:TASK_001";

        // 2. 执行被测方法
        doCallRealMethod().when(bufferInfoService).getRedisLock(validKey);
        RedisLock resultLock = bufferInfoService.getRedisLock(validKey);

        // 3. 验证对象初始化参数
        Assert.assertNotNull(resultLock);
    }

    @Test
    public void testValidateSingleGroup_WhenValidationPassed_NoExceptionThrown() throws Exception {
        // 正确 Mock 实例方法
        PowerMockito.doReturn(true)
                .when(bufferInfoService)
                .validateSingleGroup(anyList());

        // 3. 构造测试数据
        BufferToBeTransferFormVO vo = new BufferToBeTransferFormVO();
        vo.setBufferToBeTransferVOList(Collections.singletonList(new BufferToBeTransferVO()));

        // 4. 执行验证（不应抛出异常）
        doCallRealMethod().when(bufferInfoService).validateSingleGroup(vo);
        assertDoesNotThrow(() -> bufferInfoService.validateSingleGroup(vo));
    }

    @Test
    public void testValidateSingleGroup_WhenValidationFailed_ThrowBusinessException() throws Exception {
        // 2. Mock 内部验证方法返回 false
        PowerMockito.doReturn(false)
                .when(bufferInfoService)
                .validateSingleGroup(anyList());

        // 3. 构造测试数据
        BufferToBeTransferFormVO vo = new BufferToBeTransferFormVO();
        vo.setBufferToBeTransferVOList(Collections.singletonList(new BufferToBeTransferVO()));

        // 4. 执行验证并捕获异常
        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> bufferInfoService.validateSingleGroup(vo));

        // 5. 验证错误码
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode()); // [2,5](@ref)
    }

    @Test
    public void testValidateSingleGroup_WhenListEmpty_ThrowException() {
        // 构造空列表测试数据
        BufferToBeTransferFormVO vo = new BufferToBeTransferFormVO();
        vo.setBufferToBeTransferVOList(Collections.emptyList());

        // 执行验证并捕获异常（需根据实际业务逻辑调整）
        assertThrows(IndexOutOfBoundsException.class,
                () -> bufferInfoService.validateSingleGroup(vo));
    }

    // 测试场景1：空列表抛出异常
    @Test
    public void testSingleElement() {
        List<BufferToBeTransferVO> list = Arrays.asList(
                createVO("A001", "Item1", BigDecimal.valueOf(123))
        );

        doCallRealMethod().when(bufferInfoService).validateSingleGroup(list);
        boolean result = bufferInfoService.validateSingleGroup(list);

        Assert.assertTrue(result);
    }

    @Test
    public void testAllKeysMatch() {
        List<BufferToBeTransferVO> list = Arrays.asList(
                createVO("A001", "Item1", BigDecimal.valueOf(123)),
                createVO("A001", "Item1", BigDecimal.valueOf(123)),
                createVO("A001", "Item1", BigDecimal.valueOf(123))
        );
        doCallRealMethod().when(bufferInfoService).validateSingleGroup(list);
        boolean result = bufferInfoService.validateSingleGroup(list);

        Assert.assertTrue(result);
    }

    @Test
    public void testDifferentKeys() {
        List<BufferToBeTransferVO> list = Arrays.asList(
                createVO("A001", "Item1",  BigDecimal.valueOf(123)),
                createVO("A001", "Item1",  BigDecimal.valueOf(123)),
                createVO("B002", "Item2",  BigDecimal.valueOf(123)) // ID不同触发组合键差异
        );

        doCallRealMethod().when(bufferInfoService).validateSingleGroup(list);
        boolean result = bufferInfoService.validateSingleGroup(list);

        Assert.assertFalse(result);
    }

    @Test
    public void testInsertTransferWarehouseEntryInfo() {
        // 正确 Mock 实例方法

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        PowerMockito.doReturn(warehouseEntryInfo)
                .when(bufferInfoService)
                .buildWarehouseEntryInfo(any(), any());

        HttpServletRequest mock = PowerMockito.mock(HttpServletRequest.class);
        BufferToBeTransferFormVO vo = new BufferToBeTransferFormVO();
        doNothing().when(bufferInfoService).doInsertTransferWarehouseEntryInfo(any(), any());
        doCallRealMethod().when(bufferInfoService).insertTransferWarehouseEntryInfo(vo, mock);
        bufferInfoService.insertTransferWarehouseEntryInfo(vo, mock);

        verify(bufferInfoService, Mockito.times(1)).insertTransferWarehouseEntryInfo(vo, mock);
    }

    @Test
    public void testDoInsertTransferWarehouseEntryInfo() {
        List<WarehouseEntryDetailDTO> warehouseEntryDetailDTOS = new ArrayList<>();

        PowerMockito.doReturn(warehouseEntryDetailDTOS)
                .when(bufferInfoService)
                .buildWarehouseEntryDetails(any(), any());

        when(warehouseEntryInfoRepository.insertTransferWarehouseEntryInfo(any())).thenReturn(1);
        when(warehouseEntryInfoRepository.insertBatchWithTransferDetail(any())).thenReturn(1);

        doCallRealMethod().when(bufferInfoService).doInsertTransferWarehouseEntryInfo(any(), any());
        bufferInfoService.doInsertTransferWarehouseEntryInfo(any(), any());

        verify(bufferInfoService, Mockito.times(1)).doInsertTransferWarehouseEntryInfo(any(), any());
    }

    @Test
    public void testBuildWarehouseEntryDetails() {
        BufferToBeTransferFormVO pendingTransferVO = new BufferToBeTransferFormVO();
        List<BufferToBeTransferVO> bufferToBeTransferVOList = new ArrayList<>();
        BufferToBeTransferVO bufferToBeTransferVO = new BufferToBeTransferVO();
        bufferToBeTransferVOList.add(bufferToBeTransferVO);
        bufferToBeTransferVO.setSourceLocationId(BigDecimal.valueOf(1111));
        pendingTransferVO.setBufferToBeTransferVOList(bufferToBeTransferVOList);

        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("123456");

        WarehouseEntryInfo warehouseEntryInfo = mock(WarehouseEntryInfo.class);

        doCallRealMethod().when(bufferInfoService).buildWarehouseEntryDetails(pendingTransferVO, warehouseEntryInfo);
        List<WarehouseEntryDetailDTO> warehouseEntryDetailDTOS = bufferInfoService.buildWarehouseEntryDetails(pendingTransferVO, warehouseEntryInfo);

        Assert.assertNotNull(warehouseEntryDetailDTOS);

    }

    @Test
    public void testBuildWarehouseEntryInfo() {
        BufferToBeTransferFormVO pendingTransferVO = new BufferToBeTransferFormVO();
        List<BufferToBeTransferVO> bufferToBeTransferVOList = new ArrayList<>();
        BufferToBeTransferVO bufferToBeTransferVO = new BufferToBeTransferVO();
        bufferToBeTransferVOList.add(bufferToBeTransferVO);
        bufferToBeTransferVO.setSourceLocationId(BigDecimal.valueOf(1111));
        pendingTransferVO.setBufferToBeTransferVOList(bufferToBeTransferVOList);

        HttpServletRequest mock = PowerMockito.mock(HttpServletRequest.class);
        PowerMockito.when(mock.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("52");
        PowerMockito.when(mock.getHeader(SysConst.HTTP_HEADER_X_EMP_NO)).thenReturn("52");
        when(warehouseEntryInfoService.getRKBillNo(any())).thenReturn("55");

        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");

        doNothing().when(bufferInfoService).buildWarehouseEntryInfoForTask(any(), any());

        doCallRealMethod().when(bufferInfoService).buildWarehouseEntryInfo(pendingTransferVO, mock);
        WarehouseEntryInfo warehouseEntryInfo = bufferInfoService.buildWarehouseEntryInfo(pendingTransferVO, mock);

        Assert.assertNotNull(warehouseEntryInfo);

    }

    @Test
    public void testValidAllSn_Success() {
        BufferToBeTransferFormVO bufferToBeTransferFormVO = new BufferToBeTransferFormVO();
        bufferToBeTransferFormVO.setTaskNo("TASK01");

        List<BufferToBeTransferVO> bufferToBeTransferVOList = new ArrayList<>();
        BufferToBeTransferVO bufferToBeTransferVO= new BufferToBeTransferVO();
        bufferToBeTransferVO.setSn("test1");
        bufferToBeTransferVOList.add(bufferToBeTransferVO);
        bufferToBeTransferFormVO.setBufferToBeTransferVOList(bufferToBeTransferVOList);

        // 正确 Mock 实例方法
        PowerMockito.doReturn(true)
                .when(bufferInfoService)
                .validPendingSn(any());

        doCallRealMethod().when(bufferInfoService).validAllSn(bufferToBeTransferFormVO);
        bufferInfoService.validAllSn(bufferToBeTransferFormVO);

        // 验证方法是否被调用一次
        verify(bufferInfoService, times(1)).validAllSn(bufferToBeTransferFormVO);

    }

    @Test
    public void testValidAllSn_False() throws Exception {
        BufferToBeTransferFormVO bufferToBeTransferFormVO = new BufferToBeTransferFormVO();
        bufferToBeTransferFormVO.setTaskNo("TASK01");

        List<BufferToBeTransferVO> bufferToBeTransferVOList = new ArrayList<>();
        BufferToBeTransferVO bufferToBeTransferVO= new BufferToBeTransferVO();
        bufferToBeTransferVO.setSn("test1");
        bufferToBeTransferVOList.add(bufferToBeTransferVO);
        bufferToBeTransferFormVO.setBufferToBeTransferVOList(bufferToBeTransferVOList);

        BufferInfoServiceImpl serviceSpy = spy(bufferInfoService);

        // 正确 Mock 实例方法
        PowerMockito.doReturn(false)
                .when(serviceSpy)
                .validPendingSn(any());

        String[] mockResult = new String[] { "value1", "value2", "value3" };
        PowerMockito.doReturn(mockResult)
                .when(serviceSpy)
                .getParams(any());

        // 执行测试并验证异常
        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> serviceSpy.validAllSn(bufferToBeTransferFormVO));

        // 验证错误码和锁释放
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());

    }

    @Test
    public void testParam() throws Exception {
        BufferInfoFormVO bufferInfoFormVO = new BufferInfoFormVO();
        bufferInfoFormVO.setFailVerificationSnList(new ArrayList<>(Collections.singleton("SN11")));
        
        doCallRealMethod().when(bufferInfoService).getParams(bufferInfoFormVO);
        bufferInfoService.getParams(bufferInfoFormVO);

        verify(bufferInfoService, times(1)).getParams(bufferInfoFormVO);
    }

    @Test
    public void testQueryTargetWarehouseInfos() throws Exception {
        // Mock系统配置数据
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_VALUE_WAREHOUSE);
        // 2. Mock远程服务响应
        List<SysLookupTypesDTO> mockTypes = Collections.singletonList(new SysLookupTypesDTO());

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(mockTypes);

        Map<String, String> mockWarehouseMap = createMockWarehouseMap();
        // Mock仓库编码解析
        PowerMockito.doReturn(mockWarehouseMap)
                .when(bufferInfoService, "getWarehouseCodeMap", anyList());

        List<WarehouseInfoVO> warehouseInfoVOList = new ArrayList<>();
        WarehouseInfoVO warehouseInfoVO = new WarehouseInfoVO();
        warehouseInfoVO.setStockType("test1");
        warehouseInfoVOList.add(warehouseInfoVO);

        PowerMockito.doReturn(warehouseInfoVOList)
                .when(bufferInfoService, "getWarehouseInfoVOList", any());


        doCallRealMethod().when(bufferInfoService).queryTargetWarehouseInfos();
        bufferInfoService.queryTargetWarehouseInfos();

        verify(bufferInfoService, times(1)).queryTargetWarehouseInfos();
    }

    @Test
    public void testGetWarehouseInfoVOList() throws Exception {
        String[] goodWarehouseCodes = {"WH001", "WH002", "WH003"};

        WarehouseInfoVO targetWarehouseInfo = new WarehouseInfoVO();
        PowerMockito.doReturn(targetWarehouseInfo)
                .when(bufferInfoService, "assembleTargetWarehouseInfo", any());

        doCallRealMethod().when(bufferInfoService).getWarehouseInfoVOList(goodWarehouseCodes);
        List<WarehouseInfoVO> warehouseInfoVOList = bufferInfoService.getWarehouseInfoVOList(goodWarehouseCodes);
        Assert.assertNotNull(warehouseInfoVOList);
    }

    @Test
    public void tesBuildWarehouseEntryInfoForTask() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        String taskNo="tase11";

        PsTaskDTO mockTask = new PsTaskDTO();
        mockTask.setConfirmationStatus("INVALID_STATUS");
        mockTask.setTaskQty(BigDecimal.valueOf(11));
        mockTask.setProdplanNo(taskNo);
        mockTask.setProdplanId("11");
        mockTask.setItemNo("11");
        mockTask.setItemName("11");
        mockTask.setIsLead("11");
        when(centerfactoryRemoteService.getTaskInfo(anyString())).thenReturn(mockTask);

        doCallRealMethod().when(bufferInfoService).buildWarehouseEntryInfoForTask(taskNo, warehouseEntryInfo);
        bufferInfoService.buildWarehouseEntryInfoForTask(taskNo, warehouseEntryInfo);

        verify(bufferInfoService, times(1)).buildWarehouseEntryInfoForTask(taskNo, warehouseEntryInfo);

    }

    private BufferToBeTransferVO createVO(String sourceStockName, String sourceSubStock, BigDecimal sourceLocationId) {
        BufferToBeTransferVO vo = new BufferToBeTransferVO();
        vo.setSourceStockName(sourceStockName);
        vo.setSourceSubStock(sourceSubStock);
        vo.setSourceLocationId(sourceLocationId);
        return vo;
    }

    private SysLookupTypesDTO createLookupDTO(String lookupCode, String attribute2) {
        SysLookupTypesDTO dto = new SysLookupTypesDTO();
        dto.setLookupCode(new BigDecimal(lookupCode));
        dto.setAttribute2(attribute2);
        return dto;
    }

    private Map<String, String> createMockWarehouseMap() {
        return new HashMap<String, String>() {{
            put("buffer", "WH001,WH002");
            put("good", "WH003");
        }};
    }


    /* Ended by AICoder, pid:g8bc0vc711sedb514b890b1e40dbaf8d0c65a9eb */

}