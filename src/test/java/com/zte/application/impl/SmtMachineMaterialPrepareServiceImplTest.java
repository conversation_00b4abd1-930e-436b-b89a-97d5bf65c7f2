package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.itp.msa.core.model.RetCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class})
public class SmtMachineMaterialPrepareServiceImplTest {

    @InjectMocks
    private SmtMachineMaterialPrepareServiceImpl smtMachineMaterialPrepareService;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
	@Mock
	private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private PkCodeInfoServiceImpl pkCodeInfoService;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;
    @Before
    public void init() {
//        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
    }
    @Test
    public void buildSameData() throws Exception {
        PsEntityPlanBasic current = new PsEntityPlanBasic();
        PsEntityPlanBasic next = new PsEntityPlanBasic();
        List<PkCodeInfo>  needUpdateQtyPkCodeList = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        Map<String, BigDecimal> mapReelIdToQty = new HashMap<>();
        Whitebox.invokeMethod(smtMachineMaterialPrepareService,"buildSameData",current,next,needUpdateQtyPkCodeList,smtMachineMaterialMouting,mapReelIdToQty);
        Assert.assertNotNull(smtMachineMaterialMouting);
        smtMachineMaterialMouting.setNextReelRowid("2");
        Whitebox.invokeMethod(smtMachineMaterialPrepareService,"buildSameData",current,next,needUpdateQtyPkCodeList,smtMachineMaterialMouting,mapReelIdToQty);
        Assert.assertNotNull(smtMachineMaterialMouting);
        mapReelIdToQty.put("2",BigDecimal.ONE);
        smtMachineMaterialMouting.setQty(BigDecimal.ONE);
        Whitebox.invokeMethod(smtMachineMaterialPrepareService,"buildSameData",current,next,needUpdateQtyPkCodeList,smtMachineMaterialMouting,mapReelIdToQty);
        Assert.assertNotNull(smtMachineMaterialMouting);
    }
    @Test
    public void pickkingReelidAndConfirm() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        try{
            smtMachineMaterialPrepareService.pickkingReelidAndConfirm(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.REELID_EMPTY, e.getMessage());
        }
        dto.setObjectId("test");
        try{
            PowerMockito.when(smtMachineMaterialPrepareRepository.getSmtMachineMaterialPrepareList(Mockito.anyObject())).thenReturn(new ArrayList<>());
            smtMachineMaterialPrepareService.pickkingReelidAndConfirm(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.REELID_NOTIN_PREPARE, e.getMessage());
        }
        List<SmtMachineMaterialPrepare> reList = new ArrayList<>();
        SmtMachineMaterialPrepare dto1 = new SmtMachineMaterialPrepare();
        dto1.setObjectId("test");
        dto1.setIsGiven("Y");
        reList.add(dto1);
        try{
            PowerMockito.when(smtMachineMaterialPrepareRepository.getSmtMachineMaterialPrepareList(Mockito.anyObject())).thenReturn(reList);
            smtMachineMaterialPrepareService.pickkingReelidAndConfirm(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.REELID_HAVE_BEEN_HAND_OVER, e.getMessage());
        }
        List<SmtMachineMaterialPrepare> reList1 = new ArrayList<>();
        SmtMachineMaterialPrepare dto2 = new SmtMachineMaterialPrepare();
        dto2.setObjectId("test");
        reList1.add(dto2);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getSmtMachineMaterialPrepareList(Mockito.anyObject())).thenReturn(reList1);
        PowerMockito.when(smtMachineMaterialPrepareRepository.updateGivenStatus(Mockito.anyObject())).thenReturn(1l);
        PowerMockito.doNothing().when(pkCodeHistoryRepository).insertPkCodeHistory(Mockito.any());
        smtMachineMaterialPrepareService.pickkingReelidAndConfirm(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getWetLevelByPkcode() {
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList =new ArrayList<>();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare =new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setMtlPrepareId("c8c26326-4d06-4fa2-9e3b-68684ee7a6e1");
        smtMachineMaterialPrepare.setWorkOrder("7777770-SMT-B5501");
        smtMachineMaterialPrepare.setItemCode("045020100180");
        smtMachineMaterialPrepare.setLineCode("SMT-HY004");
        smtMachineMaterialPrepare.setMachineNo("(X4S-2)");
        smtMachineMaterialPrepare.setLocationNo("4-1-1");
        smtMachineMaterialPrepare.setObjectId("7777777010115000000018");
        smtMachineMaterialPrepare.setQty(new BigDecimal(100));
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);

        List<PkCodeInfo> pkCodeInfos=new ArrayList<>();
        PkCodeInfo pkCodeInfo =new PkCodeInfo();
        pkCodeInfo.setPkCode("7777777010115000000018");
        pkCodeInfo.setWetLevel("一级");
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getPickCodeList(Mockito.any())).thenReturn(pkCodeInfos);
        smtMachineMaterialPrepareService.getWetLevelByPkcode(smtMachineMaterialPrepareList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getAbcTypeByItemNo() throws Exception {
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList =new ArrayList<>();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare =new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setMtlPrepareId("c8c26326-4d06-4fa2-9e3b-68684ee7a6e1");
        smtMachineMaterialPrepare.setWorkOrder("7777770-SMT-B5501");
        smtMachineMaterialPrepare.setItemCode("045020100180");
        smtMachineMaterialPrepare.setLineCode("SMT-HY004");
        smtMachineMaterialPrepare.setMachineNo("(X4S-2)");
        smtMachineMaterialPrepare.setLocationNo("4-1-1");
        smtMachineMaterialPrepare.setObjectId("7777777010115000000018");
        smtMachineMaterialPrepare.setQty(new BigDecimal(100));
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);

        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfoCollect=new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO ageingInfoFencePointToPointQueryItemInfoDTO=new AgeingInfoFencePointToPointQueryItemInfoDTO();
        ageingInfoFencePointToPointQueryItemInfoDTO.setItemNo("045020100180");
        ageingInfoFencePointToPointQueryItemInfoDTO.setAbcType("A");
        itemInfoCollect.add(ageingInfoFencePointToPointQueryItemInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyList())).thenReturn(itemInfoCollect);
        smtMachineMaterialPrepareService.getAbcTypeByItemNo(smtMachineMaterialPrepareList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

	@Test
	public void checkFeederTest() throws Exception {
		SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
		dto.setObjectId("2222");
		List<SmtMachineMaterialMouting> list = new LinkedList<>();
		PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.anyObject())).thenReturn(list);
		List<SmtMachineMaterialPrepare> list1 = new LinkedList<>();
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.anyObject()))
				.thenReturn(list1);
        Assert.assertEquals("0000",smtMachineMaterialPrepareService.checkFeeder(dto).getCode());
	}

	@Test
	public void checkFeederTestTwo() throws Exception {
		SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
		dto.setObjectId("2222");
		List<SmtMachineMaterialMouting> list = new LinkedList<>();
		PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.anyObject())).thenReturn(list);
		List<SmtMachineMaterialPrepare> list1 = new LinkedList<>();
		SmtMachineMaterialPrepare smtPrepare = new SmtMachineMaterialPrepare();
		smtPrepare.setItemCode("2222");
		list1.add(smtPrepare);
		PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.anyObject()))
				.thenReturn(list1);
        Assert.assertEquals("0000",smtMachineMaterialPrepareService.checkFeeder(dto).getCode());
    }

	@Test
	public void getPrepareOrderByWorkOrderStr(){
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareOrderByWorkOrderStr(Mockito.any()))
                .thenReturn("8899856-A");
        String str = smtMachineMaterialPrepareRepository.getPrepareOrderByWorkOrderStr(dto);
        Assert.assertNotNull(smtMachineMaterialPrepareService.getPrepareOrderByWorkOrderStr(dto));
    }

    @Test
    public void getRelOnePage(){
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        SmtMachineMaterialPrepare info = new SmtMachineMaterialPrepare();
        List<SmtMachineMaterialPrepare> list = new ArrayList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOnePage(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(smtMachineMaterialPrepareService.getRelOnePage(dto));

        info.setCreateUser("123");
        list.add(info);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOnePage(Mockito.any())).thenReturn(list);
        smtMachineMaterialPrepareService.getRelOnePage(dto);

        info.setIsGiven("Y");
        list.add(info);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOnePage(Mockito.any())).thenReturn(list);
        smtMachineMaterialPrepareService.getRelOnePage(dto);

        info.setIsGiven("N");
        list.add(info);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOnePage(Mockito.any())).thenReturn(list);
        smtMachineMaterialPrepareService.getRelOnePage(dto);
    }

    @Test
    public void getRelOneList(){
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        smtMachineMaterialPrepareService.getRelOneList(dto);
        dto.setSort("111");

        List<SmtMachineMaterialPrepare> list = new ArrayList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(smtMachineMaterialPrepareService.getRelOneList(dto));
    }
}