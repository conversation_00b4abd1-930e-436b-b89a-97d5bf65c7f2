package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.PsWipInfoService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;

@PrepareForTest({CenterfactoryRemoteService.class, PlanscheduleRemoteService.class, EqpmgmtsRemoteService.class, JacksonJsonConverUtil.class,
        ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class, MESHttpHelper.class, ServiceDataBuilderUtil.class,
        DatawbRemoteService.class, HttpRemoteUtil.class})
public class PrepareItemDataObtainServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PrepareItemDataObtainServiceImpl prepareItemDataObtainService;

    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private BSmtBomHeaderRepository bSmtBomHeaderRepository;
    @Mock
    MdsRemoteService mdsRemoteService;
    @Mock
    PsWipInfoService psWipInfoService;

    @Test
    public void test_getStatusFromInfo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("");
        psTask.setItemNo("");
        psTasks.add(psTask);
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookUpValue);
        Whitebox.invokeMethod(prepareItemDataObtainService, "getStatusFromInfo", psTasks);
        Assert.assertTrue(true);
    }

    @Test
    public void testSetPreStatus() throws Exception {
        PrepareItemDataObtainDTO itemDataObtainDTO = new PrepareItemDataObtainDTO();
        boolean allStatusLe95 = true;
        boolean allLocationEmpty = false;
        Whitebox.invokeMethod(prepareItemDataObtainService, "setPreStatus", itemDataObtainDTO,allStatusLe95,allLocationEmpty);
        allStatusLe95 = false;
        allLocationEmpty = true;
        Whitebox.invokeMethod(prepareItemDataObtainService, "setPreStatus", itemDataObtainDTO,allStatusLe95,allLocationEmpty);
        allLocationEmpty = false;
        Whitebox.invokeMethod(prepareItemDataObtainService, "setPreStatus", itemDataObtainDTO,allStatusLe95,allLocationEmpty);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetStatusFromInfo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<PsTask> psTasks = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(null);
        Whitebox.invokeMethod(prepareItemDataObtainService, "getStatusFromInfo", psTasks);
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("1");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookUpValue);
        PsTask psTask = new PsTask();
        psTask.setTaskNo("");  // 或 psTask.setTaskNo("");
        psTask.setItemNo("");  // 或 psTask.setItemNo("");
        psTasks.add(psTask);
        Whitebox.invokeMethod(prepareItemDataObtainService, "getStatusFromInfo", psTasks);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetItemNoStartWith040() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PrepareItemDataObtainDTO> checkLabelPCBsList = new ArrayList<>();
        PrepareItemDataObtainDTO dto = new PrepareItemDataObtainDTO();
        dto.setProdplanId("1");
        PrepareItemDataObtainDTO dto1 = new PrepareItemDataObtainDTO();
        dto1.setProdplanId("3");
        PrepareItemDataObtainDTO dto2 = new PrepareItemDataObtainDTO();
        dto2.setProdplanId("4");
        PrepareItemDataObtainDTO dto3 = new PrepareItemDataObtainDTO();
        dto3.setProdplanId("5");
        checkLabelPCBsList.add(dto);
        checkLabelPCBsList.add(dto1);
        checkLabelPCBsList.add(dto2);
        checkLabelPCBsList.add(dto3);
        Set<String> checkProdplanIds = new HashSet<>();
        checkProdplanIds.add("1");
        checkProdplanIds.add("3");
        checkProdplanIds.add("4");
//        checkProdplanIds.add("5");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(null);
        Whitebox.invokeMethod(prepareItemDataObtainService, "getItemNoStartWith040", checkLabelPCBsList, checkProdplanIds);
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("1");
        psTask.setItemNo(null);
        psTasks.add(psTask);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("3");
        psTask1.setItemNo("001");
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("4");
        psTask2.setItemNo("0401");
        PsTask psTask3 = new PsTask();
        psTask2.setProdplanId("5");
        psTask2.setItemNo("0401");
        psTasks.add(psTask1);
        psTasks.add(psTask2);
        psTasks.add(psTask3);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTasks);
        Whitebox.invokeMethod(prepareItemDataObtainService, "getItemNoStartWith040", checkLabelPCBsList, checkProdplanIds);
        Assert.assertTrue(true);
    }

    @Test
    public void testItemInfoExeObtain() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, DatawbRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.itemInfoExeObtain(list);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getInforExeByWorkOrderNos(any())).thenReturn(psTaskList);
        prepareItemDataObtainService.itemInfoExeObtain(list);
        List<String> mesValidList = new ArrayList<>();
        mesValidList.add("123");
        prepareItemDataObtainDTO.setProdplanId("123");
        prepareItemDataObtainService.itemInfoExeObtain(list);

        PowerMockito.when(DatawbRemoteService.getSubmitStatusBatch(any())).thenReturn(mesValidList);
        prepareItemDataObtainService.itemInfoExeObtain(list);
        psTask.setInforExe("1");
        prepareItemDataObtainService.itemInfoExeObtain(list);
        psTask.setInforExe("2");
        Assert.assertNotNull(prepareItemDataObtainService.itemInfoExeObtain(list));
    }

    @Test
    public void testWarehouseMaterialReceivingObtain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ProductionDeliveryRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        CFLine cfLine = new CFLine();
        List<CFLine> cfLineList = new ArrayList<>();
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        cfLine.setLineCode("SMT-1");
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        cfLine.setWarehouseCode("123");
        prepareItemDataObtainDTO.setLineCode("SMT-2");
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        prepareItemDataObtainDTO.setLineCode("SMT-1");
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        List<WmsReqHeadDTO> wmsReqHeadDTOS = new ArrayList<>();
        WmsReqHeadDTO wmsReqHeadDTO = new WmsReqHeadDTO();
        wmsReqHeadDTOS.add(wmsReqHeadDTO);
        wmsReqHeadDTO.setWorkOrderNo("no-123");
        wmsReqHeadDTO.setProductionBatch("prod-123");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        PowerMockito.when(ProductionDeliveryRemoteService.getReqHead(any())).thenReturn(wmsReqHeadDTOS);
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        prepareItemDataObtainDTO.setProdplanId("prod-123");
        wmsReqHeadDTO.setStatusCode("已关闭");
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        prepareItemDataObtainDTO.setProdplanId(null);
        prepareItemDataObtainDTO.setWorkOrderNo("no-123");
        prepareItemDataObtainService.warehouseMaterialReceivingObtain(list);
        prepareItemDataObtainDTO.setProdplanId("prod-123");
        Assert.assertNotNull(prepareItemDataObtainService.warehouseMaterialReceivingObtain(list));

    }

    @Test
    public void testFirstStandbyObtain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.firstStandbyObtain(list);
        CFLine cfLine = new CFLine();
        List<CFLine> cfLineList = new ArrayList<>();
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        cfLine.setLineCode("SMT-1");
        prepareItemDataObtainDTO.setLineCode("SMT-1");
        prepareItemDataObtainService.firstStandbyObtain(list);
        cfLine.setWarehouseCode("123");
        prepareItemDataObtainService.firstStandbyObtain(list);
        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        List<PsWorkOrderSmt> psWorkOrderSmtList = new ArrayList<>();
        psWorkOrderSmtList.add(psWorkOrderSmt);
        PowerMockito.when(PlanscheduleRemoteService.selectPsWorkOrderSmtList(any())).thenReturn(psWorkOrderSmtList);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        prepareItemDataObtainService.firstStandbyObtain(list);
        psWorkOrderSmt.setStockStatus("首备中");
        prepareItemDataObtainService.firstStandbyObtain(list);
        psWorkOrderSmt.setStockStatus("首备完成");
        prepareItemDataObtainService.firstStandbyObtain(list);
        psWorkOrderSmt.setStockStatus("123");
        Assert.assertNotNull(prepareItemDataObtainService.firstStandbyObtain(list));
    }

    @Test
    public void testComprehensiveMaterialPreparationObtain() {
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list);
        prepareItemDataObtainDTO.setWorkOrderNo("1234");
        prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list);
        List<SmtMachineMTLHistoryH> smtMachineMTLHistoryHList = new ArrayList<>();
        SmtMachineMTLHistoryH smtMachineMTLHistoryH = new SmtMachineMTLHistoryH();
        smtMachineMTLHistoryHList.add(smtMachineMTLHistoryH);
        PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(any())).thenReturn(smtMachineMTLHistoryHList);
        smtMachineMTLHistoryH.setWorkOrder("123");
        prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list);
        prepareItemDataObtainDTO.setWorkOrderNo("123");
        smtMachineMTLHistoryH.setPickStatus("1");
        prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list);
        smtMachineMTLHistoryH.setPickStatus("2");
        prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list);
        smtMachineMTLHistoryH.setPickStatus("xx");
        Assert.assertNotNull(prepareItemDataObtainService.comprehensiveMaterialPreparationObtain(list));
    }

    @Test
    public void testTemperatureObtain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CenterfactoryRemoteService.class, EqpmgmtsRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.temperatureObtain(list);
        List<WorkOrderCraftAttributeDTO> workOrderCraftAttributeDTOS = new ArrayList<>();
        WorkOrderCraftAttributeDTO workOrderCraftAttributeDTO = new WorkOrderCraftAttributeDTO();
        workOrderCraftAttributeDTOS.add(workOrderCraftAttributeDTO);
        PowerMockito.when(centerfactoryRemoteService.getCraftAttributeForWorkOrder(any())).thenReturn(workOrderCraftAttributeDTOS);

        List<SysLookupValuesDTO> sysList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(null);
        try {
            prepareItemDataObtainService.temperatureObtain(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupMeaning("1");
        sysList.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysList);
        try {
            prepareItemDataObtainService.temperatureObtain(list);
        } catch (MesBusinessException e) {
            Assert.assertNotNull(e.getMessage());
        }

        dto.setLookupMeaning(Constant.OVENFLOW_TYPE);
        sysList.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysList);
        try {
            prepareItemDataObtainService.temperatureObtain(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_UP_CODE_IS_NULL, e.getMessage());
        }

        dto.setLookupCode(new BigDecimal(123));
        sysList.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysList);
        try {
            prepareItemDataObtainService.temperatureObtain(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EQPSUBTYPE_INFO_IS_NULL, e.getMessage());
        }

        List<EmEqpInfo> eqpSubtypeDTOList = new ArrayList<>();
        PowerMockito.when(EqpmgmtsRemoteService.getEqpSubtypeList(any(), any())).thenReturn(null);
        try {
            prepareItemDataObtainService.temperatureObtain(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EQPSUBTYPE_INFO_IS_NULL, e.getMessage());
        }
        EmEqpInfo info = new EmEqpInfo();
        info.setLineCode("SMT-HY001");
        info.setEqpSubtype("123");
        eqpSubtypeDTOList.add(info);
        PowerMockito.when(EqpmgmtsRemoteService.getEqpSubtypeList(any(), any())).thenReturn(eqpSubtypeDTOList);

        prepareItemDataObtainService.temperatureObtain(list);
        List<EmSmtOvenflowTemperatureDTO> temperatureList = new ArrayList<>();
        EmSmtOvenflowTemperatureDTO emSmtOvenflowTemperatureDTO = new EmSmtOvenflowTemperatureDTO();
        temperatureList.add(emSmtOvenflowTemperatureDTO);
        emSmtOvenflowTemperatureDTO.setTemperatureName("123");
        PowerMockito.when(EqpmgmtsRemoteService.getTemperatureByNameType(any())).thenReturn(temperatureList);
        prepareItemDataObtainService.temperatureObtain(list);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        workOrderCraftAttributeDTO.setFurnaceTempName("temp");
        workOrderCraftAttributeDTO.setBomNo("123");
        prepareItemDataObtainDTO.setItemNo("123");
        prepareItemDataObtainService.temperatureObtain(list);
        emSmtOvenflowTemperatureDTO.setTemperatureName("temp");
        Assert.assertNotNull(prepareItemDataObtainService.temperatureObtain(list));
    }

    @Test
    public void testFixtureObtain() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.fixtureObtain(list);
        List<FixtureReceiveRelationshipDTO> fixtureRelationList = new ArrayList<>();
        FixtureReceiveRelationshipDTO fixtureReceiveRelationshipDTO = new FixtureReceiveRelationshipDTO();
        fixtureRelationList.add(fixtureReceiveRelationshipDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.getRelationInfoByProductList(any())).thenReturn(fixtureRelationList);
        prepareItemDataObtainService.fixtureObtain(list);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        fixtureReceiveRelationshipDTO.setNeedFixture("N");
        fixtureReceiveRelationshipDTO.setProductCode("123");
        prepareItemDataObtainDTO.setItemNo("123");
        prepareItemDataObtainService.fixtureObtain(list);
        List<FixtureUseBillHeadEntityDTO> fixtureUseBillList = new ArrayList<>();
        FixtureUseBillHeadEntityDTO fixtureUseBillHeadEntityDTO = new FixtureUseBillHeadEntityDTO();
        fixtureUseBillList.add(fixtureUseBillHeadEntityDTO);
        PowerMockito.when(ProductionDeliveryRemoteService.getFixtureUseBill(any())).thenReturn(fixtureUseBillList);
        prepareItemDataObtainService.fixtureObtain(list);
        fixtureUseBillHeadEntityDTO.setProductCode("1234");
        fixtureUseBillHeadEntityDTO.setStatus(2);
        FixtureReceiveRelationshipDTO fixtureReceiveRelationshipDTO1 = new FixtureReceiveRelationshipDTO();
        fixtureRelationList.add(fixtureReceiveRelationshipDTO1);
        fixtureReceiveRelationshipDTO1.setNeedFixture("Y");
        fixtureReceiveRelationshipDTO1.setProductCode("1234");
        prepareItemDataObtainDTO1.setItemNo("1234");
        prepareItemDataObtainService.fixtureObtain(list);
        fixtureReceiveRelationshipDTO1.setSingleLineCfgQty(BigDecimal.valueOf(2));
        fixtureUseBillHeadEntityDTO.setGiveOutQty(BigDecimal.valueOf(1));
        prepareItemDataObtainService.fixtureObtain(list);
        fixtureReceiveRelationshipDTO1.setFixtureModel("A");
        fixtureUseBillHeadEntityDTO.setFixtureModel("B");
        prepareItemDataObtainService.fixtureObtain(list);
        fixtureUseBillHeadEntityDTO.setFixtureModel("A");
        prepareItemDataObtainService.fixtureObtain(list);
        fixtureUseBillHeadEntityDTO.setGiveOutQty(BigDecimal.valueOf(2));
        Assert.assertNotNull(prepareItemDataObtainService.fixtureObtain(list));
    }

    @Test
    public void testStencilObtain() throws Exception {
        PowerMockito.mockStatic(EqpmgmtsRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.stencilObtain(list);
        List<EmSmtStencilReceiveDTO> stencilReceiveList = new ArrayList<>();
        EmSmtStencilReceiveDTO emSmtStencilReceiveDTO = new EmSmtStencilReceiveDTO();
        stencilReceiveList.add(emSmtStencilReceiveDTO);
        PowerMockito.when(EqpmgmtsRemoteService.getEmSmtStencilReceive(any())).thenReturn(stencilReceiveList);
        prepareItemDataObtainService.stencilObtain(list);
        emSmtStencilReceiveDTO.setOrderNum("123");
        prepareItemDataObtainDTO.setWorkOrderNo("123");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        prepareItemDataObtainService.stencilObtain(list);
        emSmtStencilReceiveDTO.setState("待发放");
        prepareItemDataObtainService.stencilObtain(list);
        emSmtStencilReceiveDTO.setState("已发放");
        prepareItemDataObtainService.stencilObtain(list);
        emSmtStencilReceiveDTO.setState("123");
        Assert.assertNotNull(prepareItemDataObtainService.stencilObtain(list));
    }

    @Test
    public void testSolderObtain() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        prepareItemDataObtainService.solderObtain(list);
        List<SolderUseBillHead> solderUseBillHeads = new ArrayList<>();
        SolderUseBillHead solderUseBillHead = new SolderUseBillHead();
        PowerMockito.when(ProductionDeliveryRemoteService.getSolderUseBill(any())).thenReturn(solderUseBillHeads);
        prepareItemDataObtainService.solderObtain(list);
        solderUseBillHeads.add(solderUseBillHead);
        solderUseBillHead.setProdplanId("123");
        prepareItemDataObtainDTO.setProdplanId("123");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        list.add(prepareItemDataObtainDTO1);
        prepareItemDataObtainService.solderObtain(list);
        solderUseBillHead.setStatus(0);
        prepareItemDataObtainService.solderObtain(list);
        solderUseBillHead.setStatus(1);
        prepareItemDataObtainService.solderObtain(list);
        solderUseBillHead.setStatus(2);
        prepareItemDataObtainService.solderObtain(list);
        solderUseBillHead.setStatus(4);
        Assert.assertNotNull(prepareItemDataObtainService.solderObtain(list));
    }

    @Test
    public void eqpMaintainObtain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<PrepareItemDataObtainDTO> list = Lists.newArrayList(
                new PrepareItemDataObtainDTO() {{
                    setLineCode("1");
                }},
                new PrepareItemDataObtainDTO() {{
                    setLineCode("2");
                }},
                new PrepareItemDataObtainDTO() {{
                    setLineCode("3");
                }}
        );
        Assert.assertNotNull(prepareItemDataObtainService.eqpMaintainObtain(Lists.newArrayList()));
        PowerMockito.when(BasicsettingRemoteService.getLinesByCodeList(any())).thenReturn(null);
        Assert.assertNotNull(prepareItemDataObtainService.eqpMaintainObtain(list));
        PowerMockito.when(BasicsettingRemoteService.getLinesByCodeList(any())).thenReturn(Lists.newArrayList(
                new CFLine() {{
                    setLineCode("1");
                    setLineName("1");
                }},
                new CFLine() {{
                    setLineCode("2");
                    setLineName("2");
                }}
        ));
        PowerMockito.when(mdsRemoteService.getMdsEqpMaintain(any())).thenReturn(new HashMap());
        Assert.assertNotNull(prepareItemDataObtainService.eqpMaintainObtain(list));
    }

    @Test
    public void writeFilmObtain() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class);
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        List<PrepareItemDataObtainDTO> list = new ArrayList<>();
        list.add(prepareItemDataObtainDTO);
        list.add(prepareItemDataObtainDTO1);
        List<BsPremanuBomInfoDTO> bsPremanuBomInfos = new ArrayList<>();
        BsPremanuBomInfoDTO bsPremanuBomInfoDTO = new BsPremanuBomInfoDTO();
        bsPremanuBomInfoDTO.setBomCode("1234");
        bsPremanuBomInfos.add(bsPremanuBomInfoDTO);
        prepareItemDataObtainService.writeFilmObtain(list);
        PowerMockito.when(centerfactoryRemoteService.selectPreBomInfoByBomList(any())).thenReturn(bsPremanuBomInfos);
        prepareItemDataObtainDTO.setItemNo("1234");
        prepareItemDataObtainDTO.setProdplanId("1234");
        prepareItemDataObtainService.writeFilmObtain(list);
        List<ProdPlanProgramInfoDTO> prodPlanProgramInfoDTOS = new ArrayList<>();
        ProdPlanProgramInfoDTO prodPlanProgramInfoDTO = new ProdPlanProgramInfoDTO();
        prodPlanProgramInfoDTO.setProdPlanId("1234");
        prodPlanProgramInfoDTOS.add(prodPlanProgramInfoDTO);
        PowerMockito.when(PlanscheduleRemoteService.getProgramStatusByProdplanIds(any())).thenReturn(prodPlanProgramInfoDTOS);
        prepareItemDataObtainService.writeFilmObtain(list);
        prodPlanProgramInfoDTO.setProgramStatus("0");
        prepareItemDataObtainService.writeFilmObtain(list);
        prodPlanProgramInfoDTO.setProgramStatus("1");
        prepareItemDataObtainService.writeFilmObtain(list);
        prodPlanProgramInfoDTO.setProgramStatus("2");
        Assert.assertNotNull(prepareItemDataObtainService.writeFilmObtain(list));
        List<String> prodplanIds = new ArrayList<>();
        prodplanIds.add("1234");
        prepareItemDataObtainService.getInfoStatus(list, prodplanIds);
        Assert.assertNotNull(prepareItemDataObtainService.writeFilmObtain(list));
    }

    @Test
    public void testLabelPCBsObtain() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class,
                HttpRemoteUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        List<PrepareItemDataObtainDTO> checkLabelPCBsList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO.setProdplanId("1234");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO1 = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO1.setProdplanId("2222");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO2 = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO2.setProdplanId("3333");
        PrepareItemDataObtainDTO prepareItemDataObtainDTO3 = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO3.setProdplanId("4444");
        checkLabelPCBsList.add(prepareItemDataObtainDTO);
        checkLabelPCBsList.add(prepareItemDataObtainDTO1);
        checkLabelPCBsList.add(prepareItemDataObtainDTO2);
        checkLabelPCBsList.add(prepareItemDataObtainDTO3);
        List<PsTaskExtraDTO> psTaskExtras = new ArrayList<>();
        PsTaskExtraDTO psTaskExtraDTO = new PsTaskExtraDTO();
        psTaskExtraDTO.setProdplanId("1234");
        psTaskExtras.add(psTaskExtraDTO);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskExtras(any())).thenReturn(psTaskExtras);
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("2222");
        psWipInfo.setSn("1");
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setAttribute1("5555");
        psWipInfo1.setSn("1");
        psWipInfos.add(psWipInfo1);
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoService.selectWipInfoByBatch(any())).thenReturn(psWipInfos);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(null);
        try {
            prepareItemDataObtainService.labelPCBsObtain(checkLabelPCBsList);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("3333");
        psTask.setTaskNo("1");
        psTask.setItemNo("04040");
        psTask.setCenterStatus(null);
        psTask.setCenterLocation(null);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("9");
        psTask1.setTaskNo("1");
        psTask1.setItemNo("04040");
        psTask1.setCenterStatus("96");
        psTask1.setCenterLocation("1");
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("3333");
        psTask2.setTaskNo("1");
        psTask2.setItemNo(null);
        psTask2.setCenterStatus("96");
        psTask2.setCenterLocation("2");
        psTasks.add(psTask);
        psTasks.add(psTask1);
        psTasks.add(psTask2);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTasks);
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("11111");
        sysLookUpValue.setAttribute1("11111");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(null);
        prepareItemDataObtainService.labelPCBsObtain(checkLabelPCBsList);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookUpValue);
        String msg = JSON.toJSONString(new ServiceData<List<PsTask>>() {{
            setBo(psTasks);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(msg);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), any()))
                .thenReturn(psTasks);
        prepareItemDataObtainService.labelPCBsObtain(checkLabelPCBsList);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetInfoStatus() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class,
                HttpRemoteUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        List<PrepareItemDataObtainDTO> itemDataObtainDTOList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        itemDataObtainDTOList.add(prepareItemDataObtainDTO);
        List<String> noStatusPlanId = new ArrayList<>();
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        noStatusPlanId.add("1234");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(null);
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("1");
        psTask.setItemNo("1");
        psTask.setProdplanId("1234");
        psTasks.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTasks);
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("11111");
        sysLookUpValue.setAttribute1("11111");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookUpValue);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(null);
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetInfoStatus2() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class,
                HttpRemoteUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        List<PrepareItemDataObtainDTO> itemDataObtainDTOList = new ArrayList<>();
        PrepareItemDataObtainDTO prepareItemDataObtainDTO = new PrepareItemDataObtainDTO();
        prepareItemDataObtainDTO.setProdplanId("1234");
        prepareItemDataObtainDTO.setTaskNo("1");
        prepareItemDataObtainDTO.setItemNo("1");
        itemDataObtainDTOList.add(prepareItemDataObtainDTO);
        List<String> noStatusPlanId = new ArrayList<>();
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        noStatusPlanId.add("1234");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(null);
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("1");
        psTask1.setItemNo("1");
        psTask1.setProdplanId("1234");
        psTask1.setCenterStatus("96");
        psTask1.setCenterLocation("96");
        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("1");
        psTask2.setItemNo("1");
        psTask2.setProdplanId("1234");
        psTask2.setCenterStatus(null);
        psTask2.setCenterLocation(null);
        PsTask psTask3 = new PsTask();
        psTask3.setTaskNo("1");
        psTask3.setItemNo("1");
        psTask3.setProdplanId("1234");
        psTask3.setCenterStatus("96");
        psTask3.setCenterLocation("96");
        psTasks.add(psTask1);
        psTasks.add(psTask2);
        psTasks.add(psTask3);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTasks);
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("11111");
        sysLookUpValue.setAttribute1("11111");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookUpValue);
        String msg = JSON.toJSONString(new ServiceData<List<PsTask>>() {{
            setBo(psTasks);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(msg);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), any()))
                .thenReturn(psTasks);
        prepareItemDataObtainService.getInfoStatus(itemDataObtainDTOList, noStatusPlanId);
        Assert.assertTrue(true);
    }

    @Test
    public void checkSMTBomStatus() {
        List<PrepareItemDataObtainDTO> checkSMTBomStatusList = new ArrayList<>();
        List<BSmtBomHeader> bomStatusList = new ArrayList<>();

        PrepareItemDataObtainDTO dto = new PrepareItemDataObtainDTO();
        dto.setProdplanId("123456");
        dto.setItemNo("Item123456");
        dto.setLineCode("LineCode123456");
        dto.setCraftSection("SMT-A");
        dto.setWorkOrderNo("123456-A");
        PrepareItemDataObtainDTO dto1 = new PrepareItemDataObtainDTO();
        dto1.setProdplanId("1");
        dto1.setItemNo("Item1");
        dto1.setLineCode("LineCode1");
        dto1.setCraftSection("SMT-B");
        dto1.setWorkOrderNo("123456-B");
        checkSMTBomStatusList.add(dto);
        checkSMTBomStatusList.add(dto1);

        BSmtBomHeader info = new BSmtBomHeader();
        info.setAttr1("123456");
        info.setProductCode("Item123456");
        info.setLineCode("LineCode123456");
        info.setCraftSection("SMT-A");
        BSmtBomHeader info1 = new BSmtBomHeader();
        info1.setAttr1("1");
        info1.setProductCode("Item1");
        info1.setLineCode("LineCode1");
        info1.setCraftSection("SMT-A");
        bomStatusList.add(info);
        bomStatusList.add(info1);

        PowerMockito.when(bSmtBomHeaderRepository.getSMTBomStatus(any())).thenReturn(null);
        prepareItemDataObtainService.checkSMTBomStatus(checkSMTBomStatusList);
        PowerMockito.when(bSmtBomHeaderRepository.getSMTBomStatus(any())).thenReturn(bomStatusList);
        Assert.assertNotNull(prepareItemDataObtainService.checkSMTBomStatus(checkSMTBomStatusList));
    }
}
