/*Started by AICoder, pid:rfeb6xc86b2227f141df0aeec1d355161201c591*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.domain.model.TaskReconfigurationRecord;
import com.zte.domain.model.TaskReconfigurationRecordRepository;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.PushStdModelDataQueryDTO;
import com.zte.interfaces.dto.PushStdModelSnDataInitDTO;
import com.zte.interfaces.dto.WarehouseEntryDetailExtDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(RequestHeadValidationUtil.class)
public class PushStdModelSnDataServiceImpl_19_Test {

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private TaskReconfigurationRecordRepository taskReconfigurationRecordRepository;

    @Test
    public void testInitPushStdModelSnData_NullPageRows() {
        // Given
        PushStdModelSnDataInitDTO pushStdModelSnDataInit = new PushStdModelSnDataInitDTO();
        pushStdModelSnDataInit.setTaskNos(Collections.singletonList("task1"));
        pushStdModelSnDataInit.setCustomerNos(Collections.singletonList("customer1"));

        when(centerfactoryRemoteService.getPushStdModelData(any(PushStdModelDataQueryDTO.class)))
                .thenReturn(null);

        // When
        boolean result = pushStdModelSnDataService.initPushStdModelSnData(pushStdModelSnDataInit);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, atLeastOnce()).getPushStdModelData(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testInitPushStdModelSnData_Success() {
        // Given
        PushStdModelSnDataInitDTO pushStdModelSnDataInit = new PushStdModelSnDataInitDTO();
        pushStdModelSnDataInit.setTaskNos(Collections.singletonList("task1"));
        pushStdModelSnDataInit.setCustomerNos(Collections.singletonList("customer1"));

        PageRows<PushStdModelDataDTO> pageRows = new PageRows<>();
        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        PushStdModelDataDTO pushStdModelDataDTO2 = new PushStdModelDataDTO();
        pushStdModelDataDTO2.setEntityClass(Constant.FG_DISAS_2);
        pushStdModelDataDTO2.setTaskStatus(Constant.FINISH_WORK);
        pageRows.setRows(Lists.newArrayList(pushStdModelDataDTO, pushStdModelDataDTO2));

        when(centerfactoryRemoteService.getPushStdModelData(any(PushStdModelDataQueryDTO.class)))
                .thenReturn(pageRows);
        when(warehouseEntryDetailRepository.selectExtByQuery(any())).thenReturn(Lists.newArrayList(new WarehouseEntryDetailExtDTO()));
        when(taskReconfigurationRecordRepository.selectByQuery(any())).thenReturn(Lists.newArrayList(new TaskReconfigurationRecord()));

        // When
        boolean result = pushStdModelSnDataService.initPushStdModelSnData(pushStdModelSnDataInit);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, atLeastOnce()).getPushStdModelData(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testInitPushStdModelSnData_Success1() {
        // Given
        PushStdModelSnDataInitDTO pushStdModelSnDataInit = new PushStdModelSnDataInitDTO();
        pushStdModelSnDataInit.setTaskNos(Collections.singletonList("task1"));
        pushStdModelSnDataInit.setCustomerNos(Collections.singletonList("customer1"));

        PageRows<PushStdModelDataDTO> pageRows = new PageRows<>();
        PushStdModelDataDTO pushStdModelDataDTO2 = new PushStdModelDataDTO();
        pushStdModelDataDTO2.setEntityClass(Constant.FG_DISAS_2);
        pageRows.setRows(Lists.newArrayList(pushStdModelDataDTO2));

        when(centerfactoryRemoteService.getPushStdModelData(any(PushStdModelDataQueryDTO.class)))
                .thenReturn(pageRows);

        // When
        boolean result = pushStdModelSnDataService.initPushStdModelSnData(pushStdModelSnDataInit);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, atLeastOnce()).getPushStdModelData(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testInitPushStdModelSnData_EmptyPageRows() {
        // Given
        PushStdModelSnDataInitDTO pushStdModelSnDataInit = new PushStdModelSnDataInitDTO();
        pushStdModelSnDataInit.setTaskNos(Collections.singletonList("task1"));
        pushStdModelSnDataInit.setCustomerNos(Collections.singletonList("customer1"));

        PageRows<PushStdModelDataDTO> pageRows = new PageRows<>();
        pageRows.setRows(Collections.emptyList());

        when(centerfactoryRemoteService.getPushStdModelData(any(PushStdModelDataQueryDTO.class)))
                .thenReturn(pageRows);

        // When
        boolean result = pushStdModelSnDataService.initPushStdModelSnData(pushStdModelSnDataInit);

        // Then
        assertTrue(result);
        verify(centerfactoryRemoteService, atLeastOnce()).getPushStdModelData(any(PushStdModelDataQueryDTO.class));
    }

    @Before
    public void setUp() {
        // Any setup if needed
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("12");
    }

}
/*Ended by AICoder, pid:rfeb6xc86b2227f141df0aeec1d355161201c591*/