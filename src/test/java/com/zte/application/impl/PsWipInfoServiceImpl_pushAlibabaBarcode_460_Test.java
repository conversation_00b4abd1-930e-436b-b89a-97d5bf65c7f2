/*Started by AICoder, pid:5fc44o77fee50d7142680b35f4f9ab320f23a25e*/
package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.ReflectUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class,PlanscheduleRemoteService.class,CrafttechRemoteService.class})
public class PsWipInfoServiceImpl_pushAlibabaBarcode_460_Test {

    @InjectMocks
    private PsWipInfoServiceImpl psWipInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private IdGenerator idGenerator;

    @Mock
    private BasicsettingRemoteService basicsettingRemoteService; // Added mock for BasicsettingRemoteService

    @Before
    public void setUp() throws IllegalAccessException {
        // Setup code if needed
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class,CrafttechRemoteService.class);
        PowerMockito.field(PsWipInfoServiceImpl.class, "redundancy")
                .set(psWipInfoService, 1);
        PowerMockito.field(PsWipInfoServiceImpl.class, "pageSize")
                .set(psWipInfoService, 1);
    }

    @Test
    public void testPushAlibabaBarcode_sysLookupTypesDTOIsNull() throws Exception {
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);

        try {
            psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
            fail("Expected MesBusinessException");
        } catch (MesBusinessException e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
    }

    @Test
    public void testPushAlibabaBarcode_sysLookupTypesDTOIsEmpty() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);

        try {
            psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
            fail("Expected MesBusinessException");
        } catch (MesBusinessException e) {
            assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
    }

    @Test
    public void testPushAlibabaBarcode_noWipInfo() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2023-01-01 00:00:00");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        when(psWipInfoRepository.getWipInfoByALi(any(Page.class))).thenReturn(Collections.emptyList());

        psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");

        verify(centerfactoryRemoteService, never()).pushStdModelSnData(anyList());
    }

    @Test
    public void testPushAlibabaBarcode_withWipInfo() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2023-01-01 00:00:00");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setAttribute2("taskNo");
        wipInfo.setWorkOrderNo("workOrderNo");
        wipInfo.setRouteId("routeId");
        wipInfo.setCurrProcessCode("currProcessCode");
        wipInfo.setSn("sn");
        wipInfo.setItemNo("itemNo");

        List<PsWipInfo> wipInfoList = Collections.singletonList(wipInfo);
        when(psWipInfoRepository.getWipInfoByALi(any(Page.class))).thenReturn(wipInfoList, Collections.emptyList());

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("taskNo");
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = Collections.singletonList(psTaskExtendedDTO);
        when(centerfactoryRemoteService.getSpecificTaskExtended(anyList())).thenReturn(psTaskExtendedDTOList);

        PsWorkOrderBasic workOrderBasic = new PsWorkOrderBasic();
        workOrderBasic.setWorkOrderNo("workOrderNo");
        workOrderBasic.setRouteId("routeId");
        List<PsWorkOrderBasic> workOrderList = Collections.singletonList(workOrderBasic);
        when(PlanscheduleRemoteService.getListByWorkOrderNos(anyMap())).thenReturn(workOrderList);

        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setRouteId("routeId");
        ctRouteDetailDTO.setNextProcess("testProcessCode");
        List<CtRouteDetailDTO> ctRouteDetailDTOList = Collections.singletonList(ctRouteDetailDTO);
        when(CrafttechRemoteService.getCtRouteDetailByRouteIds(anyList())).thenReturn(ctRouteDetailDTOList);

        when(idGenerator.snowFlakeIdStr()).thenReturn("id");

        psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");

        assertNotNull(ctRouteDetailDTOList);
    }

    @Test
    public void testPushAlibabaBarcode_taskNoIsEmpty() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2023-01-01 00:00:00");
        when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);

        psWipInfoService.pushAlibabaBarcode("", "factoryId", "empNo");

        assertNotNull(sysLookupTypesDTO);
    }
    /*Ended by AICoder, pid:5fc44o77fee50d7142680b35f4f9ab320f23a25e*/

    @Test
    public void pushAlibabaBarcode() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2023-01-01 00:00:00");
        when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6745), eq(Constant.LOOKUP_TYPE_6745001))).thenReturn(sysLookupTypesDTO);

        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupMeaning("2");
        when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6745), eq(Constant.LOOKUP_TYPE_6745002))).thenReturn(null);

        try {
            psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6745), eq(Constant.LOOKUP_TYPE_6745002))).thenReturn(new SysLookupTypesDTO());
        try {
            psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setAttribute2("taskNo");
        wipInfo.setWorkOrderNo("workOrderNo");
        wipInfo.setRouteId("routeId");
        wipInfo.setCurrProcessCode("currProcessCode");
        wipInfo.setSn("sn");
        wipInfo.setItemNo("itemNo");

        List<PsWipInfo> wipInfoList = Collections.singletonList(wipInfo);
        when(psWipInfoRepository.getWipInfoByALi(any(Page.class))).thenReturn(wipInfoList, Collections.emptyList());

        when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6745), eq(Constant.LOOKUP_TYPE_6745002))).thenReturn(sysLookupTypesDTO2);
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("taskNo2");

        when(centerfactoryRemoteService.getSpecificTaskExtended(anyList())).thenReturn(null);
        psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
        Assert.assertNotNull(sysLookupTypesDTO2);

        List<PsTaskExtendedDTO> psTaskExtendedDTOList = Collections.singletonList(psTaskExtendedDTO);
        when(centerfactoryRemoteService.getSpecificTaskExtended(anyList())).thenReturn(psTaskExtendedDTOList);
        psWipInfoService.pushAlibabaBarcode("taskNo", "factoryId", "empNo");
        Assert.assertNotNull(sysLookupTypesDTO2);
    }
    @Test
    public void pushSnData() throws Exception {
        List<PushStdModelSnDataDTO> list = new ArrayList<>();
        list.add(new PushStdModelSnDataDTO());
        Whitebox.invokeMethod(psWipInfoService,"pushSnData","",list,new Date());
        Assert.assertNotNull(list);
    }

    @Test
    public void assembleNewData() throws Exception {
        List<PushStdModelSnDataDTO> list = new ArrayList<>();
        list.add(new PushStdModelSnDataDTO());

        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setAttribute2("taskNo");
        wipInfo.setWorkOrderNo("workOrderNo");
        wipInfo.setRouteId("routeId");
        wipInfo.setCurrProcessCode("1");
        wipInfo.setSn("sn");
        wipInfo.setItemNo("itemNo");

        List<PsWipInfo> wipInfoList = Collections.singletonList(wipInfo);

        Whitebox.invokeMethod(psWipInfoService,"assembleNewData","55","empNo",wipInfoList,"new Date()",list);
        Assert.assertNotNull(list);

        List<CtRouteDetailDTO> routeDetailDTOList = new ArrayList<>();
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("0");setProcessSeq(new BigDecimal(0));}});
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("1");setProcessSeq(new BigDecimal(1));}});
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("2");setProcessSeq(new BigDecimal(2));}});
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("3");setProcessSeq(new BigDecimal(3));}});
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("4");}});
        routeDetailDTOList.add(new CtRouteDetailDTO(){{setNextProcess("6");}});
        wipInfo.setCtRouteDetailDTOList(routeDetailDTOList);

        Whitebox.invokeMethod(psWipInfoService,"assembleNewData","55","empNo",wipInfoList,"0",list);
        Assert.assertNotNull(list);

        Whitebox.invokeMethod(psWipInfoService,"assembleNewData","55","empNo",wipInfoList,"2",list);
        Assert.assertNotNull(list);

        for (int i = 0; i < 1001; i++) {
            list.add(new PushStdModelSnDataDTO());
        }
        Whitebox.invokeMethod(psWipInfoService,"assembleNewData","55","empNo",wipInfoList,"5",list);
        Assert.assertNotNull(list);

    }
}
/*Ended by AICoder, pid:5fc44o77fee50d7142680b35f4f9ab320f23a25e*/