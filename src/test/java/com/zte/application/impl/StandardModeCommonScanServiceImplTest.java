package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.scan.CommonSnScanService;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AssemblyRelaScanDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.FlowControlConditionDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CommonUtils.class, CrafttechRemoteService.class, DatawbRemoteService.class})
public class StandardModeCommonScanServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    StandardModeCommonScanServiceImpl service;

    @Mock
    private PsWipInfoServiceImpl psWipInfoService;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    /* Started by AICoder, pid:b98d7a1aea3e4c92929b8d6360a1fe0f */
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private ValueOperations<String, Object> operations;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private CommonSnScanService commonSnScanService;

    // AI生成代码,方法已废弃,不好用
    //    @Before
    //    public void setUp() {
    //        MockitoAnnotations.initMocks(this);
    //    }
    /* Ended by AICoder, pid:b98d7a1aea3e4c92929b8d6360a1fe0f */
    @Test
    public void getRouteIdByFlow() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        service.getRouteIdByFlow(null);
        service.getRouteIdByFlow(new FlowControlInfoDTO());
        service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setRouteId("1");
        }});
        service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                setWorkOrderNo("1");
            }});
        }});
        service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                setRouteId("1");
            }});
        }});
        service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setWipInfo(new PsWipInfo() {{
                setRouteId("1");
            }});
        }});
        service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                setRouteId("1");
            }});
            setWipInfo(new PsWipInfo() {{
                setWorkOrderNo("1");
                setRouteId("1");
            }});
        }});
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(new PsEntityPlanBasic() {{
            setRouteId("1");
        }});
        Assert.assertEquals("1", service.getRouteIdByFlow(new FlowControlInfoDTO() {{
            setWipInfo(new PsWipInfo() {{
                setWorkOrderNo("1");
                setRouteId("1");
            }});
        }}));
    }

    @Test
    public void testSmCommonScan() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        PmScanConditionDTO dto = new PmScanConditionDTO();
        try {
            service.smCommonScan(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SN_IS_NULL);
        }
        dto.setSn("1234");
        dto.setMixedTasksFlag(1);
        Map<String, Object> lookupMap = new HashMap<>();
        lookupMap.put("lookupType", Constant.LOOKUP_VALUE_7580006);
        List<SysLookupTypesDTO> lookupList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_N);
        lookupList.add(sysLookupTypesDTO);
        Map<String, Object> map = new HashMap<>(1);
        map.put("sn", "1234");
        List<PsWipInfo> listWip = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        listWip.add(psWipInfo);
        List<PsEntityPlanBasicDTO> psEntityPlanInfoList = new ArrayList<>();
        psEntityPlanInfoList.add(new PsEntityPlanBasicDTO());
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap)).thenReturn(lookupList);
        PowerMockito.when(psWipInfoService.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanInfoList);
        PowerMockito.when(CommonUtils.getLmbMessage(any(), any(), any())).thenReturn("msg");
        try {
            service.smCommonScan(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.MIXED_TASKS_SCAN_PARAM_NULL);
        }
        dto.setCurrProcessCode("P1234");
        dto.setWorkStation("S1234");
        service.smCommonScan(dto);
        PowerMockito.when(psWipInfoService.getList(map)).thenReturn(listWip);
        service.smCommonScan(dto);
        List<PsWorkOrderDTO> psWorkOrderDTOs = new ArrayList<>();
        List<PsWorkOrderDTO> psWorkOrderDTOsTwo = new ArrayList<>();
        psWorkOrderDTOs.add(new PsWorkOrderDTO());
        psWorkOrderDTOsTwo.add(new PsWorkOrderDTO());
        psWorkOrderDTOsTwo.add(new PsWorkOrderDTO());
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(any(), any())).thenReturn(psWorkOrderDTOsTwo);
        service.smCommonScan(dto);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(any(), any())).thenReturn(psWorkOrderDTOs);
        service.smCommonScan(dto);
        psWipInfo.setWorkOrderNo("1234SMT-A-V01");
        psWipInfo.setLineCode("SMT-01");
        PowerMockito.when(psWipInfoService.getPsEntityPlanInfo(any())).thenReturn(null);
        try {
            service.smCommonScan(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.WORKORDER_INFO_IS_NULL);
        }
        PowerMockito.when(psWipInfoService.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanInfoList);
        service.smCommonScanTran(dto);
        String s = service.smCommonScan(dto);

        service.smCommonScanTran(dto);
    }

    @Test
    public void testCheckBindFinish() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CommonUtils.class, CrafttechRemoteService.class);
        PmScanConditionDTO dto = new PmScanConditionDTO();
        dto.setCurrProcessCode("P1234");
        dto.setSn("1234");
        dto.setWorkStation("S1234");
        dto.setMixedTasksFlag(1);
        Map<String, Object> lookupMap = new HashMap<>();
        lookupMap.put("lookupType", Constant.LOOKUP_VALUE_7580006);
        List<SysLookupTypesDTO> lookupList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_N);
        lookupList.add(sysLookupTypesDTO);
        List<PsWipInfo> listWip = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        listWip.add(psWipInfo);
        List<PsEntityPlanBasicDTO> psEntityPlanInfoList = new ArrayList<>();
        psEntityPlanInfoList.add(new PsEntityPlanBasicDTO());
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap)).thenReturn(lookupList);
        PowerMockito.when(psWipInfoService.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanInfoList);
        service.checkBindFinish(dto);
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        service.checkBindFinish(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap)).thenReturn(null);
        service.checkBindFinish(dto);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(psWipInfo);
        service.checkBindFinish(dto);
        psWipInfo.setItemNo("1234ABCD");
        service.checkBindFinish(dto);
        List<CtRouteDetailDTO> ctRouteDetailDTOS = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTOS.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.getProcessList(any(), any())).thenReturn(ctRouteDetailDTOS);
        service.checkBindFinish(dto);
        ctRouteDetailDTO.setLastProcess("Y");
        service.checkBindFinish(dto);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("productCode", listWip.get(Constant.INT_0).getItemNo());
        paramMap.put("formSn", listWip.get(Constant.INT_0).getSn());
        paramMap.put("processCode", "P1234");
        List<ProdBindingSetting> bindList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting = new ProdBindingSetting();
        bindList.add(prodBindingSetting);
        PowerMockito.when(prodBindingSettingRepository.getList(paramMap)).thenReturn(bindList);
        service.checkBindFinish(dto);
        paramMap.put("workStation", "S1234");
        service.checkBindFinish(dto);
        Map<String, Object> lookupMap1 = new HashMap<>();
        lookupMap1.put("lookupType", Constant.LOOKUP_VALUE_7580007);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(null);
        service.checkBindFinish(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(lookupList);
        psWipInfo.setAttribute2("Y");
        service.checkBindFinish(dto);
        psWipInfo.setAttribute2("Y1234");
        service.checkBindFinish(dto);
        List<WipExtendIdentification> finishedList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        finishedList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.getBindingInfoByProcessCodeAndWorkStation(any())).thenReturn(finishedList);
        service.checkBindFinish(dto);
        prodBindingSetting.setUsageCount(BigDecimal.valueOf(1));
        prodBindingSetting.setItemCode("1234");
        service.checkBindFinish(dto);
        wipExtendIdentification.setItemNo("1234");
        service.checkBindFinish(dto);
        wipExtendIdentification.setFormQty(BigDecimal.valueOf(1));
        Assert.assertNotNull(service.checkBindFinish(dto));
        Map<String, Object> lookupMap2 = new HashMap<>();
        lookupMap2.put("lookupType", Constant.LOOKUP_VALUE_7580026);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap2)).thenReturn(lookupList);
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(anyString())).thenReturn(new BSProcess());
        PowerMockito.when(CommonUtils.getLmbMessage(any(), any(), any())).thenReturn("msg");
        service.checkBindFinish(dto);
    }

    @Test
    public void testGetScanDTO() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
        try {
            service.getScanDTO(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getMessage(),MessageId.MIXED_TASKS_SCAN_PARAM_NULL);
        }
        dto.setProcessName("子工序");
        try {
            service.getScanDTO(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getMessage(),MessageId.MIXED_TASKS_SCAN_PARAM_NULL);
        }
        dto.setProcessName(null);
        dto.setWorkStationName("工站");
        try {
            service.getScanDTO(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getMessage(),MessageId.MIXED_TASKS_SCAN_PARAM_NULL);
        }
        dto.setProcessName("子工序");
        try {
            service.getScanDTO(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getMessage(),MessageId.PROCESS_NOT_EXIST);
        }
        List<BSProcess> processList = new ArrayList<>();
        processList.add(new BSProcess());
        PowerMockito.when(CrafttechRemoteService.getProcess(any())).thenReturn(processList);
        service.getScanDTO(dto);
    }

    @Test
    public void testCheckTestControl() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        service.checkTestControl(new FlowControlInfoDTO(){{
            setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST); setCurrProcessCode("0");
            setWipInfo(new PsWipInfo(){{setCurrProcessCode("1"); setStatus("Y");}});}}, new ArrayList<>());
        service.checkTestControl(
                new FlowControlInfoDTO(){{
                    setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setWipInfo(new PsWipInfo(){{setCurrProcessCode("1");}});
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        service.checkTestControl(
                new FlowControlInfoDTO(){{
                    setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setWipInfo(new PsWipInfo(){{setCurrProcessCode("2");}});
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        String msg = service.checkTestControl(
                new FlowControlInfoDTO(){{
                    setWipInfo(new PsWipInfo());
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        Assert.assertEquals("", msg);
    }

    @Test
    public void processTransferCheck() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,CommonUtils.class);
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        FlowControlConditionDTO entity = new FlowControlConditionDTO();
        PsWipInfo currWipInfo = new PsWipInfo();
        currWipInfo.setWorkOrderNo("test123");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(null);
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWorkOrderBasic.setRouteId("test123");
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void processTransferCheck1() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,CommonUtils.class);
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        FlowControlConditionDTO entity = new FlowControlConditionDTO();
        PsWipInfo currWipInfo = new PsWipInfo();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(null);
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWorkOrderBasic.setRouteId("test123");
        try{
            service.processTransferCheck(flow,entity,currWipInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void doZjCheckProcess() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setWorkOrderNo("test123");
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        entity.setWipInfo(psWipInfo);
        PowerMockito.when(psWipInfoService.getPsEntityPlanInfo(Mockito.anyString())).thenReturn(new ArrayList<>());
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(null);
        try{
            service.doZjCheckProcess(entity);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        try {
            service.doZjCheckProcess(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        psWorkOrderBasic.setRouteId("test123");
        try {
            service.doZjCheckProcess(entity);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void testCheckAuxMaterialBindFinish() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CommonUtils.class, CrafttechRemoteService.class, DatawbRemoteService.class);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        Map<String, Object> lookupMap = new HashMap<>();
        lookupMap.put("lookupType", Constant.LOOKUP_VALUE_7580026);
        Map<String, Object> lookupMap1 = new HashMap<>();
        lookupMap1.put("lookupType", Constant.LOOKUP_VALUE_7580027);
        List<SysLookupTypesDTO> lookupList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_N);
        lookupList.add(sysLookupTypesDTO);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap)).thenReturn(lookupList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        psWipInfo.setAttribute2("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap)).thenReturn(null);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(lookupList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(null);
        Assert.assertEquals(null, service.checkAuxMaterialBindFinish(entity, psWipInfo));
        PmScanConditionDTO info = new PmScanConditionDTO();
        PsWipInfo dto = new PsWipInfo();
        info.setMixedTasksFlag(Constant.INT_1);
        service.checkAuxMaterialBindFinish(info, dto);
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(new BSProcess() {{
            setAuxBindFlag("N");
        }});
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(new BSProcess() {{
            setAuxBindFlag("Y");
        }});
        service.checkAuxMaterialBindFinish(entity, psWipInfo);

        List<PsWorkOrderDTO> psWorkOrderDTOs = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTOs.add(psWorkOrderDTO);
        psWorkOrderDTO.setTaskNo("123");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(any())).thenReturn(psWorkOrderDTO);
        List<SysLookupTypesDTO> lookupList1 = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("123");
        lookupList1.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(lookupList1);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupMap1)).thenReturn(null);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);

        psWorkOrderDTO.setRouteId("1234");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        List<CtRouteInfoDTO> listRoute = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        listRoute.add(ctRouteInfoDTO);
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        listDetail.add(ctRouteDetailDTO);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        ctRouteInfoDTO.setListDetail(listDetail);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(any(), any(), any(), any(), any())).thenReturn(listRoute);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn(null);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);

        ctRouteDetailDTO.setIsUnnecessary("Y");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        ctRouteDetailDTO.setIsUnnecessary("N");
        ctRouteDetailDTO.setNextProcess("SMT");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        entity.setWorkStation("SMT");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(any(), any())).thenReturn(psWorkOrderDTOs);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(PlanscheduleRemoteService.getTaskListByTaskNos(any())).thenReturn(null);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTaskList.add(psTask);
        psTask.setTaskQty(new BigDecimal(2));
        psWorkOrderDTO.setInputQty(new BigDecimal(1));
        PowerMockito.when(PlanscheduleRemoteService.getTaskListByTaskNos(any())).thenReturn(psTaskList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(any())).thenReturn(null);
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting = new ProdBindingSetting();
        prodBindingSetting.setUsageCount(NumConstant.BIG_TWO);
        ProdBindingSetting prodBindingSetting1 = new ProdBindingSetting();
        prodBindingSetting1.setUsageCount(NumConstant.BIG_TWO);
        prodBindingSetting1.setProductCode("15646814AAA");
        prodBindingList.add(prodBindingSetting);
        prodBindingList.add(prodBindingSetting1);
        PowerMockito.when(prodBindingSettingRepository.getList(any())).thenReturn(prodBindingList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        List<ItemListEntityDTO> erpList = new ArrayList<>();
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        erpList.add(itemListEntityDTO);
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(any())).thenReturn(erpList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        itemListEntityDTO.setRequiredQuantity("4");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn(new BigDecimal(2));
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn(new BigDecimal(1));
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn(new BigDecimal(0));
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setFormQty(new BigDecimal(2));
        wipExtendIdentification.setItemNo("123A");
        wipExtendList.add(wipExtendIdentification);
        prodBindingSetting.setItemCode("123A");
        itemListEntityDTO.setItemNo("123A");
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(wipExtendList);
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        psTask.setTaskQty(new BigDecimal(4));
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        wipExtendIdentification.setFormQty(new BigDecimal(4));
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
        itemListEntityDTO.setRequiredQuantity("0");
        service.checkAuxMaterialBindFinish(entity, psWipInfo);
    }
}