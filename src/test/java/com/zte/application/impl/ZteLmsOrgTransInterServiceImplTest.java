package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.ZteLmsOrgTransInter;
import com.zte.domain.model.ZteLmsOrgTransInterRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.ZteLmsOrgTransInterDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.doThrow;

@PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class, CommonUtils.class})
public class ZteLmsOrgTransInterServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ZteLmsOrgTransInterServiceImpl service;

    @Mock
    private ZteLmsOrgTransInterRepository zteLmsMtlTransactionsInterRepository;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }


    @Test
    public void write() throws MesBusinessException {
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("111");

        ZteLmsOrgTransInterDTO dto = new ZteLmsOrgTransInterDTO();
        ZteLmsOrgTransInter zteLmsOrgTransInter = null;
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.selectByPrimaryKey(anyString()))
                .thenReturn(zteLmsOrgTransInter);
        int write = service.write(dto);
        Assert.assertTrue(write == 0);

        zteLmsOrgTransInter = new ZteLmsOrgTransInter();
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.selectByPrimaryKey(anyString()))
                .thenReturn(zteLmsOrgTransInter);
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.insertSelective(anyObject()))
                .thenReturn(1);
        int write1 = service.write(dto);
        Assert.assertTrue(write1 == 1);
    }


    @Test
    public void transferDo() throws Exception {

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("5");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_ERP,
                MpConstant.LOOKUP_ERP_RETRY_THRESHOLD))
                .thenReturn(sysLookupTypesDTO);

        List<ZteLmsOrgTransInter> tranList = new ArrayList<>();
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.getTransferList(5))
                .thenReturn(tranList);
        // 测试1
        service.transferDo();

        // 测试2
        ZteLmsOrgTransInter zteLmsOrgTransInter = new ZteLmsOrgTransInter();
        tranList.add(zteLmsOrgTransInter);
        zteLmsOrgTransInter.setTimes(new BigDecimal(1));
        doReturn(1).when(datawbRemoteService).insertZteLmsOrgTransInterBatch(anyObject());
        doReturn(1).when(zteLmsMtlTransactionsInterRepository).updateByPrimaryKey(anyObject());
        service.transferDo();

        // 测试3
        zteLmsOrgTransInter.setTimes(new BigDecimal(4));
        zteLmsOrgTransInter.setDealId("aaaaa");
        doThrow(new Exception()).when(datawbRemoteService).insertZteLmsOrgTransInterBatch(anyObject());
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("10296137");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_ERP,
                MpConstant.LOOKUP_ERP_TRANS_FAIL_RECIPIENT))
                .thenReturn(sysLookupTypesDTO1);
        doReturn(false).when(emailUtils).sendMail(anyString(), anyString(), anyString(), anyString(), anyString());
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.TRANS_ORG_ERP_FAIL_MSG);
        try {
            service.transferDo();
        } catch (Exception returnMessage) {
            Assert.assertEquals( MessageId.TRANS_ORG_ERP_FAIL_MSG, returnMessage.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
