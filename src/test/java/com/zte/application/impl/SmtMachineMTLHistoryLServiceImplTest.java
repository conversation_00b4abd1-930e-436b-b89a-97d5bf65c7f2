package com.zte.application.impl;

import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class SmtMachineMTLHistoryLServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    SmtMachineMTLHistoryLServiceImpl service;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
	@Mock
	private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Test
    public void extractedUpdateOrDel() throws Exception {
        service.extractedUpdateOrDel(new SmtMachineMTLHistoryL(), new BigDecimal(1));
        service.extractedUpdateOrDel(new SmtMachineMTLHistoryL(), new BigDecimal(0));
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
    }

	@Test
	public void getAnyTypeSmtMachineHisDetail() throws Exception {
		SmtMachineMTLHistoryL dto = new SmtMachineMTLHistoryL();
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.PAGING_QUERY_PARAMETERS_IS_EMPTY, e.getMessage());
		}
		dto.setPage(1);
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.PAGING_QUERY_PARAMETERS_IS_EMPTY, e.getMessage());
		}
		dto.setRows(501);
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.ROWS_MORE_THAN_FIVE_HUNDRED, e.getMessage());
		}
		dto.setRows(5);
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.TIME_CAN_NOT_BE_NULL, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("212312sdasd1");
		dto.setLastUpdatedDateStart("2022-08-07 00:00:00");
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.TIME_CAN_NOT_BE_NULL, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("2022-07-06 23:59:59");
		dto.setLastUpdatedDateStart("dasdas");
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.TIME_CAN_NOT_BE_NULL, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("2022-07-06 23:59:59");
		dto.setLastUpdatedDateStart("2022-08-07 00:00:00");
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.START_TIME_IS_LATER, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("2022-07-08 23:59:59");
		dto.setLastUpdatedDateStart("2022-06-07 00:00:00");
		try{
			service.getAnyTypeSmtMachineHisDetail(dto);
		}catch(Exception e){
			Assert.assertEquals(MessageId.SPAN_WHITIN_30DAYS, e.getMessage());
		}
		dto.setLastUpdatedDateEnd("2022-07-06 23:59:59");
		dto.setLastUpdatedDateStart("2022-06-07 00:00:00");
		List<SmtMachineMTLHistoryL> list = new ArrayList<>();
		PowerMockito.when(smtMachineMTLHistoryLRepository.getAnyTypeSmtMachineHisDetail(Mockito.any())).thenReturn(list);
		service.getAnyTypeSmtMachineHisDetail(dto);
	}
}
