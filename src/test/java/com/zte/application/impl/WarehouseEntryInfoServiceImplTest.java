/*Started by AICoder, pid:d6ea512c51e6486989c81c368011a894*/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BarSubmitDTO;
import com.zte.interfaces.dto.CtRouteDetailParamDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.PsWipInfoQueryDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SemisBoxDTO;
import com.zte.interfaces.dto.SemisBoxInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WarehouseEntryDetailDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoQueryDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.BILL_TYPE_LOOKUP_CODE_DBFX;
import static com.zte.common.utils.Constant.BILL_TYPE_LOOKUP_CODE_SCRAP;
import static com.zte.common.utils.Constant.STR_1;
import static com.zte.common.utils.Constant.STR_200;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class, CrafttechRemoteService.class, SpringUtil.class, ProductionmgmtRemoteService.class, PlanscheduleRemoteService.class,
        MicroServiceRestUtil.class, RedisHelper.class, ConstantInterface.class, HttpRemoteUtil.class, CommonUtils.class, BasicsettingRemoteService.class,
        MessageId.class, CollectionUtils.class,ProductionDeliveryRemoteService.class,HttpRemoteService.class})
public class WarehouseEntryInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    WarehouseEntryInfoServiceImpl service;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private ScrapBillDetailService scrapBillDetailService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private ErpRemoteService erpRemoteService;
//    @Mock
//    private ProductionDeliveryRemoteService productionDeliveryRemoteService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    DataSourceTransactionManager transactionManager;

    @Mock
    TransactionDefinition transactionDefinition;

    @Mock
    WipScanHistoryRepository wipScanHistoryRepository;

    @Mock
    PsWipInfoRepository wipInfoRepository;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private PmMachineWeightService pmMachineWeightService;
    @Mock
    private TaskReconfigurationRecordService taskReconfigurationRecordService;
    @Value("${high.process.code:P2065}")
    private String highProcessCode;
    @Value("${home.ledger.flag:Y}")
    private String homeLedgerFlag;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, DatawbRemoteService.class, PlanscheduleRemoteService.class);
    }


    @Test
    public void getWmesWarehouseEntryInfo()throws Exception {
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setLeadFlag("test");
        psTask.setTaskQty(new BigDecimal("1"));
        psTaskList.add(psTask);
        List<PsWorkOrderBasic> psWorkOrderBasicList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("7119859-SMT-A5201");
        psWorkOrderBasic.setLineCode("SMT-1");
        psWorkOrderBasic.setRouteId("2");
        psWorkOrderBasicList.add(psWorkOrderBasic);
        PowerMockito.mockStatic(MicroServiceRestUtil.class,CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<PsWorkOrderBasic>>(){{setBo(psWorkOrderBasicList);}}));
        try{
            service.getWmesWarehouseEntryInfo( new HashMap<>(),psTaskList);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<PsWorkOrderBasic>>(){{setBo(new ArrayList<>());}}));
        try{
            service.getWmesWarehouseEntryInfo( new HashMap<>(),psTaskList);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void verifyWholeMachineOrSingleBoard() {
        try {
            service.verifyWholeMachineOrSingleBoard(new WarehouseEntryInfoQueryDTO() {{
                setBillTypeCode(new BigDecimal("1"));
            }});
            service.setCommitQty(new WarehouseEntryInfo(), Lists.newArrayList(new PsWipInfoDTO()));
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkLmsPackingInfo() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("20220302Test");
        warehouseEntryInfo.setStockType("半成品K2库");
        list.add(warehouseEntryInfo);

        List<BarSubmitDTO> barSubmitDTOList = new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setSerialId(new Long(20220303));
        barSubmitDTOList.add(barSubmitDTO);
        List<Long> longs = new ArrayList<Long>();
        longs.add(barSubmitDTO.getSerialId());

        List<SemisBoxInfoDTO> semisBoxInfoDTOS = new ArrayList<>();
        SemisBoxInfoDTO semisBoxInfoDTO = new SemisBoxInfoDTO();
        semisBoxInfoDTO.setApplyId(new Long(20220302));
        semisBoxInfoDTOS.add(semisBoxInfoDTO);

        PowerMockito.when(datawbRemoteService.queryBarSubmitInfo(anyList())).thenReturn(barSubmitDTOList);
        PowerMockito.when(datawbRemoteService.querySemisBoxInfo(anyList())).thenReturn(semisBoxInfoDTOS);
        try{
            service.checkLmsPackingInfo(list);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkSpmStatus() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("20220302Test");
        warehouseEntryInfo.setStockType("半成品K2库");
        list.add(warehouseEntryInfo);

        List<BarSubmitDTO> barSubmitDTOList = new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setSerialId(new Long(20220303));
        barSubmitDTO.setStatus(new Long(0));
        barSubmitDTOList.add(barSubmitDTO);
        List<Long> longs = new ArrayList<Long>();
        longs.add(barSubmitDTO.getSerialId());

        PowerMockito.when(datawbRemoteService.queryBarSubmitInfo(anyList())).thenReturn(barSubmitDTOList);
        try{
            service.checkSpmStatus(list);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void isBomSubCard() {
        Map<String, Object> map = new HashMap<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("2202334455");
        Assert.assertThrows(BusiException.class, () -> service.isBomSubCard(map, warehouseEntryInfo, true));
    }

    @Test
    public void getStringFromJson() {
        ServiceData serviceData = new ServiceData();
        List<BBomHeaderDTO> list = new ArrayList<>();
        BBomHeaderDTO bBomHeaderDTO = new BBomHeaderDTO();
        bBomHeaderDTO.setZjSubcardFlag("Y");
        list.add(bBomHeaderDTO);
        serviceData.setBo(list);
        String json = JacksonJsonConverUtil.beanToJson(serviceData);
        JsonNode jsonNode = null;
        try {
            jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(json);
        } catch (JsonProcessingException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            service.getStringFromJson(jsonNode);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void goK2AndSaveWarehouse() throws Exception{
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setEmNo("23213");
        dto.setAppNo("23424");
        dto.setFacId("55");
        Map<String, Object> result = new HashMap<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setProdplanId("213123");
        warehouseEntryInfo.setStatus("4");
        Assert.assertThrows(NullPointerException.class, () -> service.goK2AndSaveWarehouse(dto, result, warehouseEntryInfo));
    }

    @Test
    public void isSubcardK2() throws Exception{
        Map<String, Object> result = new HashMap<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("22023545ABP");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(null);
        Assert.assertThrows(BusiException.class, () -> service.isSubcardK2(result, warehouseEntryInfo, true));
    }

    @Test
    public void doK2OrNotK2() throws Exception {
        Map<String, Object> result = new HashMap<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("22023545ABP");
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        List<SysLookupTypesDTO> lookupTypes = new ArrayList<>();
        SysLookupTypesDTO lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypes.add(lookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap()))
                .thenReturn(lookupTypes);

        List<CFFactory> factoryList = new ArrayList<>();
        CFFactory factoryDto = new CFFactory();
        factoryDto.setOrgId(52L);
        factoryList.add(factoryDto);
        PowerMockito.when(BasicsettingRemoteService.getFactory(any()))
                .thenReturn(factoryList);

        List<CFFactory> subFactoryIdList = new ArrayList<>();
        CFFactory cfFactory = new CFFactory();
        cfFactory.setErpCode("123");
        subFactoryIdList.add(cfFactory);
        PowerMockito.when(BasicsettingRemoteService.getFactoryByFactoryCode(any()))
                .thenReturn(subFactoryIdList);
        boolean isK2 = false;
        Assert.assertThrows(NullPointerException.class, () -> service.doK2OrNotK2(isK2, dto, result, warehouseEntryInfo));

        boolean isK21 = true;
        Assert.assertThrows(NumberFormatException.class, () -> service.doK2OrNotK2(isK21, dto, result, warehouseEntryInfo));
    }

    @Test
    public void saveWarehouseEntryInfoForBatchScrap() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(SpringUtil.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        String factoryId = "55";
        String entityId = "2";
        String empNo = "00286523";
        dto.setBillTypeCode(new BigDecimal(BILL_TYPE_LOOKUP_CODE_SCRAP));
        dto.setProdplanId("213123");
        dto.setCurrProcess("P0007");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777815645613");
        psWipInfos.add(psWipInfo);
        dto.setPsWipInfoList(psWipInfos);
        Map<String, Object> map = new HashMap<>();
        map.put("status", Constant.FLAG_Y);
        map.put("msg", "success");
        map.put("warehouseEntryInfo", null);
        String prodplanId = "test";
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setLeadFlag("test");
        psTask.setTaskQty(new BigDecimal("1"));
        psTaskList.add(psTask);
        PowerMockito.when(CrafttechRemoteService.getWorkCodeStation(any())).thenReturn("已报废");
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(psTaskList);
                }}));
        dto.setBillTypeCode(new BigDecimal(BILL_TYPE_LOOKUP_CODE_SCRAP));
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(SpringUtil.getBean(WarehouseEntryInfoServiceImpl.class)).thenReturn(service);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        dto.setStockType("半成品K2库");
        List<SysLookupTypesDTO> list = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.SCRAP_BILL_WRITE_BACK_LOOKUP_CODE));
        sysLookupTypesDTO.setLookupMeaning("Y");
        list.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(list);
                }}));
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("2213123");
                }})
        );
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        List<List<WarehouseEntryDetailDTO>> list1 = new ArrayList<>();
        List<WarehouseEntryDetailDTO> list2 = new ArrayList<>();
        WarehouseEntryDetailDTO dto2 = new WarehouseEntryDetailDTO();
        list2.add(dto2);
        list1.add(list2);
        PowerMockito.when(warehouseEntryInfoRepository.insertWarehouseEntryInfo(any())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.insertWarehouseEntryDetailBatchNotExist(any())).thenReturn(1);

        try {
            service.saveWarehouseEntryInfoForBatchScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), com.zte.common.model.MessageId.PRODPLANID_IS_NOT_EXITS);
        }
        List<PsTask> psTaskList1 = new ArrayList<>();
        PsTask task = new PsTask();
        task.setProdplanId("qqqq");
        task.setExternalType("qwe");
        task.setOrgId(new BigDecimal(123));
        psTaskList1.add(task);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(Mockito.anyList())).thenReturn(psTaskList1);
        service.saveWarehouseEntryInfoForBatchScrap(dto, factoryId, entityId, empNo);
        dto.setLocatorId(null);
        dto.setStockType("123");
        try {
            service.saveWarehouseEntryInfoForBatchScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), com.zte.common.model.MessageId.LOCATOR_ID_ERROR);
        }
        dto.setLocatorId(BigDecimal.valueOf(123));
        try {
            service.saveWarehouseEntryInfoForBatchScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), com.zte.common.model.MessageId.LOCATOR_ID_ERROR);
        }
    }


    @Test
    public void batchUpdateWarehouseInfoStatus() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<WarehouseEntryInfo> warehouseEntryInfos = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("test123");
        warehouseEntryInfo.setProdplanId("test123");
        warehouseEntryInfos.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.batchUpdateWarehouseEntryInfoStatus(anyList())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.batchUpdateWarehouseEntryDetailStatus(anyList())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.selectCountDetailInfo(any())).thenReturn(1);
        PowerMockito.doNothing().when(PlanscheduleRemoteService.class ,"batchHandleCompleteQty", any());
        try{
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            service.batchUpdateWarehouseInfoStatus(new ArrayList<>(), "1", "2","52");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        warehouseEntryInfo.setBillType("9");
        try {
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } finally {
            Assert.assertEquals(0,  service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52"));
        }
    }

    @Test
    public void batchUpdateWarehouseInfoStatusTwo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<WarehouseEntryInfo> warehouseEntryInfos = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("test123");
        warehouseEntryInfo.setProdplanId("test123");
        warehouseEntryInfos.add(warehouseEntryInfo);
        WarehouseEntryInfo headInfo = new WarehouseEntryInfo();
        headInfo.setStatus("0");
        headInfo.setBillType("9");
        PowerMockito.when(warehouseEntryInfoRepository.batchUpdateWarehouseEntryInfoStatus(anyList())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.batchUpdateWarehouseEntryDetailStatus(anyList())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.selectCountDetailInfo(any())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.selectBillTypeByBillNo(any())).thenReturn(headInfo);
        PowerMockito.doNothing().when(PlanscheduleRemoteService.class ,"batchHandleCompleteQty", any());
        try {
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } finally {
            Assert.assertEquals(1,  service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52"));
        }
        headInfo.setBillType("8");
        try {
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } finally {
            Assert.assertEquals(1,  service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52"));
        }
    }

    @Test
    public void batchUpdateWarehouseInfoStatusThree() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<WarehouseEntryInfo> warehouseEntryInfos = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("test123");
        warehouseEntryInfo.setProdplanId("test123");
        warehouseEntryInfos.add(warehouseEntryInfo);
        WarehouseEntryInfo headInfo = new WarehouseEntryInfo();
        headInfo.setStatus("1");
        headInfo.setBillType("9");
        PowerMockito.when(warehouseEntryInfoRepository.selectBillTypeByBillNo(any())).thenReturn(headInfo);
        try {
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } finally {
            Assert.assertEquals(0,  service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52"));
        }
    }

    @Test
    public void batchUpdateWarehouseInfoStatusFour() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<WarehouseEntryInfo> warehouseEntryInfos = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("test123");
        warehouseEntryInfo.setProdplanId("test123");
        warehouseEntryInfos.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.selectBillTypeByBillNo(any())).thenReturn(null);
        try {
            service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52");
        } finally {
            Assert.assertEquals(0,  service.batchUpdateWarehouseInfoStatus(warehouseEntryInfos, "1", "2","52"));
        }
    }

    @Test
    public void updateBoardOnlineAndBarSubmit() throws Exception {

        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWip = new PsWipInfo();
        psWip.setAttribute1("8888777");
        psWip.setSn("888877700123");
        psWipInfos.add(psWip);
        dto.setPsWipInfoList(psWipInfos);
        WarehouseEntryInfoDTO warehouseEntryDTO = new WarehouseEntryInfoDTO();
        warehouseEntryDTO.setProdplanId("8888777");
        warehouseEntryDTO.setBillNo("test123");
        List<BarSubmitDTO> barSubmitDTOList = new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setBillNo("test123");
        barSubmitDTOList.add(barSubmitDTO);
        PowerMockito.when(datawbRemoteService.getBarSubmitBatch(anyMap())).thenReturn(barSubmitDTOList);
        PowerMockito.when(datawbRemoteService.updateBarSubmitWithType(any())).thenReturn(1);
        PowerMockito.when(datawbRemoteService.updateBoardOnlineBatch(anyList())).thenReturn(1);
        try{
            service.updateBoardOnlineAndBarSubmit(dto, warehouseEntryDTO);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getErpCode() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOrgId(new BigDecimal("4437"));
        CFFactory factory = new CFFactory();
        factory.setOrgId(new Long("4437"));
        factory.setErpCode("CS_db");
        List<SysLookupTypesDTO> subLookupTypes = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("4437");
        sysLookupTypesDTO.setAttribute1("52");
        subLookupTypes.add(sysLookupTypesDTO);

        List<CFFactory> subFactoryIdList = new ArrayList<>();
        subFactoryIdList.add(factory);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(subLookupTypes);
        PowerMockito.when(BasicsettingRemoteService.getFactoryByFactoryCode(anyString())).thenReturn(subFactoryIdList);
        try{
            service.getErpCode(warehouseEntryInfo);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getBoardOnlineInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(SpringUtil.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setProdplanId("8888777");
        dto.setTaskNo("test123");
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setProdplanId("8888777");
        warehouseEntryInfoDTO.setPage(new Long("1"));
        warehouseEntryInfoDTO.setRows(new Long("10"));

        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", "8888777");
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        warehouseEntryInfoQueryDTO.setProdplanId("8888777");
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setLeadFlag("test");
        psTask.setTaskQty(new BigDecimal("1"));
        psTask.setTaskNo("test123");
        psTask.setProdplanId("8888777hai");
        psTaskList.add(psTask);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(psTaskList);
                }}));
        Page<BoardOnline> page = new Page<BoardOnline>();
        List<BoardOnline> boardOnlines = new ArrayList<>();
        BoardOnline boardOnline = new BoardOnline();
        boardOnline.setProdplanId(new BigDecimal("8888777"));
        boardOnline.setBoardSn(new BigDecimal("22"));
        BoardOnline boardOnline1 = new BoardOnline();
        boardOnline1.setProdplanId(new BigDecimal("8888777"));
        boardOnline1.setBoardSn(new BigDecimal("33"));
        boardOnline.setPage(new Long(1));
        boardOnline.setRows(new Long(10));
        boardOnlines.add(boardOnline);
        boardOnlines.add(boardOnline1);
        page.setRows(boardOnlines);

        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("888877700104");
        psWipInfoDTOList.add(psWipInfoDTO);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(anyMap())).thenReturn(page);
        PowerMockito.when(psWipInfoService.getWipInfoList(anyList())).thenReturn(psWipInfoDTOList);
        try{
            service.getBoardOnlineInfo(warehouseEntryInfoDTO);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void queryBoardOnlineInfo() throws Exception {
        Page<BoardOnline> page = new Page<BoardOnline>();
        List<BoardOnline> boardOnlines = new ArrayList<>();
        BoardOnline boardOnline = new BoardOnline();
        boardOnline.setProdplanId(new BigDecimal("8888777"));
        boardOnline.setBoardSn(new BigDecimal("22"));
        boardOnlines.add(boardOnline);
        page.setRows(boardOnlines);

        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("888877700104");
        psWipInfoDTOList.add(psWipInfoDTO);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(anyMap())).thenReturn(page);
        PowerMockito.when(psWipInfoService.getWipInfoList(anyList())).thenReturn(psWipInfoDTOList);
        try{
            service.queryBoardOnlineInfo(boardOnline);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getProcessForQuery() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setTaskNo("121270351048APB");
        list.add(dto);

        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderBasic = new PsWorkOrderDTO();
        psWorkOrderBasic.setRouteId("4efc10ae-4457-4dc9-9906-a9761e133735");
        psWorkOrderBasic.setWorkOrderNo("121270351048APB");
        psWorkOrderDTOList.add(psWorkOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any()))
                .thenReturn(psWorkOrderDTOList);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetail() {{
                    setCurrProcess("test");
                }}));
        try{
            service.getProcessForQuery(dto);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void dealScrapBillSn() {
        List<PsWipInfo> listPsWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        listPsWipInfos.add(psWipInfo);
        List<PsScanHistory> insertWipScanHistorys = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setLastUpdatedBy("00286523");
        PowerMockito.when(psWipInfoService.updateWipInfoByHistory(anyList()))
                .thenReturn(1);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(scrapBillDetailService).updateProcessCodeBatchBySn(any());
        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryDetailsByBillNo(anyMap()))
                .thenReturn(1);
        PowerMockito.when(psScanHistoryRepository.insertPsScanHistoryBatch(anyList()))
                .thenReturn(1);
        try{
            service.dealScrapBillSn(warehouseEntryInfo, listPsWipInfos, insertWipScanHistorys);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void pullOuterSnDetail() {
        try{
            service.pullOuterSnDetail("1", 1);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(warehouseEntryInfoRepository.getUnconfirmedOuterBill(anyString(), anyInt())).thenReturn(Lists.newArrayList("1"));
        try {
            PowerMockito.when(datawbRemoteService.queryBarSubmitInfo(anyList())).thenReturn(Lists.newArrayList(new BarSubmitDTO() {{
                setBillNo("1");
                setSerialId(1L);
                setStatus(1L);
            }}));
            PowerMockito.when(datawbRemoteService.querySemisBox(anyList())).thenReturn(Lists.newArrayList(new SemisBoxDTO() {{
                setProdPlanId(1);
                setSerialId(1L);
                setSn("1");
            }}));
        } catch (Exception e) {
        }
        try{
            service.pullOuterSnDetail("1", 1000);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void dealWip() {
        try{
            service.dealWip(Lists.newArrayList(new WarehouseEntryDetailDTO() {{
                setSn("1");
            }}));
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void permissionControlForStock() throws Exception {
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("rejectRight");
        sysLookupTypesDTO.setLookupMeaning("00286569");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        try{
            service.permissionControlForStock("00286569");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void permissionControlForStockTwo() throws Exception {
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("rcvRight");
        sysLookupTypesDTO.setLookupMeaning("00286569");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        try{
            service.permissionControlForStock("00286569");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void permissionControlForStockThree() throws Exception {
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("scanReceiveRight");
        sysLookupTypesDTO.setLookupMeaning("00286569");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        try{
            service.permissionControlForStock("00286569");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void permissionControlForStockFour() throws Exception {
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        try{
            service.permissionControlForStock("00286569");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void syncFormSpm() throws Exception {
        try{
            service.syncFormSpm(null);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            service.syncFormSpm(Lists.newArrayList("1"));
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(datawbRemoteService.dataWbGetBarSubmitByPlanIds(anyList()))
                .thenReturn(Lists.newArrayList(new BarSubmitDTO() {{
                                                   setProdplanId(1L);
                                                   setBillNo("1");
                                               }},
                        new BarSubmitDTO() {{
                            setProdplanId(1L);
                            setBillNo("2");
                        }}));
        try{
            service.syncFormSpm(Lists.newArrayList("1"));
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(warehouseEntryInfoRepository.getByBillNo(anyList()))
                .thenReturn(Lists.newArrayList("1"));
        try{
            service.syncFormSpm(Lists.newArrayList("1"));
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void batchUpdateStatus() throws Exception {
        String empNo = "00286569";
        List<String> billNos = new ArrayList<>();
        billNos.add("test");
        List<List<String>> splitList = new ArrayList<>();
        splitList.add(billNos);
        PowerMockito.when(warehouseEntryInfoRepository.getByBillNo(any()))
                .thenReturn(billNos);
        try{
            service.batchUpdateStatus(empNo, billNos);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }


        List<String> billNoss = new ArrayList<>();
        try{
            service.batchUpdateStatus(empNo, billNoss);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            service.getPrintDetailProdByBillNo("123");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getLpnByBillNo() {
        PowerMockito.when(warehouseEntryInfoRepository.getLpnByBillNo(any()))
                .thenReturn(Arrays.asList("222", "333"));
        List<String> res = service.getLpnByBillNo("111");
        Assert.assertEquals(2, res.size());
    }


    @Test
    public void getCountByProdplanId() throws Exception {
        PowerMockito.when(warehouseEntryInfoRepository.getCountByProdplanId(Mockito.anyString())).thenReturn(1);
        Assert.assertNotNull(service.getCountByProdplanId("test"));
    }

    @Test
    public void batchUpdateStatusTwo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        String empNo = "00286569";
        List<String> billNos = new ArrayList<>();
        billNos.add("test");
        List<List<String>> splitList = new ArrayList<>();
        splitList.add(billNos);
        PowerMockito.when(warehouseEntryInfoRepository.getByBillNo(anyList()))
                .thenReturn(Lists.newArrayList());
        try {
            service.batchUpdateStatus(empNo, billNos);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void updateWipInfo() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_DBFX);
        warehouseEntryInfo.setBillNo("test123");
        warehouseEntryInfo.setPsWipInfoList(new ArrayList() {{
            add(new PsWipInfo() {{
                setSn("1111");
            }});
        }});
        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryDetailsByBillNo(anyMap()))
                .thenReturn(1);
        try{
            service.updateWipInfo(warehouseEntryInfo);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_CONFIRMATION);
        try{
            service.updateWipInfo(warehouseEntryInfo);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_SON);
        List<PsWorkOrderDTO> psWorkOrderDTOList = new ArrayList<>();
        PsWorkOrderDTO p1 = new PsWorkOrderDTO();
        p1.setRouteId("123");
        psWorkOrderDTOList.add(p1);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.any())).thenReturn(psWorkOrderDTOList);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(Mockito.any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetail() {{
                    setCurrProcess("test");
                }}));
        service.updateWipInfo(warehouseEntryInfo);

        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(Mockito.any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetail() {{
                    setCurrProcess("");
                }}));
        service.updateWipInfo(warehouseEntryInfo);
    }

    @Test
    public void checkIsHighTempProcess() throws Exception {
        CtRouteDetailParamDTO paramObj = new CtRouteDetailParamDTO();
        Whitebox.invokeMethod(service, "checkIsHighTempProcess", paramObj, "P0256");
        ReflectionTestUtils.setField(service, "highProcessCode", "P0256");
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(Mockito.any()))
                .thenReturn(null);
        Whitebox.invokeMethod(service, "checkIsHighTempProcess", paramObj, "P0256");
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(Mockito.any()))
                .thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "checkIsHighTempProcess", paramObj, "P0256");
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(Mockito.any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetail() {{
                    setCurrProcess("test");
                }}));
        Assert.assertNotNull(Whitebox.invokeMethod(service, "checkIsHighTempProcess", paramObj, "P0256"));
    }

    @Test
    public void setProcessName() {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        try{
            service.setProcessName(new WarehouseEntryInfoQueryDTO());
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(
                Lists.newArrayList(new BSProcess(){{setProcessName("1");setProcessCode("1");}}));
        try{
            service.setProcessName(new WarehouseEntryInfoQueryDTO(){{setPsWipInfoList(Lists.newArrayList(new PsWipInfo(){{setCurrProcessCode("1");}}));}});
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any())).thenReturn(null);
        try{
            service.setProcessName(new WarehouseEntryInfoQueryDTO(){{setPsWipInfoList(Lists.newArrayList(new PsWipInfo(){{setCurrProcessCode("1");}}));}});
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void verifyWarehouseSn() throws Exception {
        PowerMockito.mockStatic(MessageId.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);
        List<WarehouseEntryInfoDTO> warehouseEntryInfoDTOS =new ArrayList<>();
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(Mockito.any())).thenReturn(warehouseEntryInfoDTOS);
        try{
            service.verifyWarehouseSn("test123","123456789101");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        WarehouseEntryInfoDTO warehouseEntryInfoDTO =new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setBillNo("test123");
        warehouseEntryInfoDTOS.add(warehouseEntryInfoDTO);
        try{
            service.verifyWarehouseSn("test123","123456789101");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        Page<BoardOnline> boardOnlineList = new Page<>();
        boardOnlineList.setRows(new ArrayList<>());
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(Mockito.anyMap())).thenReturn(boardOnlineList);
        try{
            service.verifyWarehouseSn("test123","123456789101");
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkWipExtendForZJ() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        WarehouseEntryInfoQueryDTO dto =new WarehouseEntryInfoQueryDTO();
        dto.setBillType("2");
        try{
            service.checkWipExtendForZJ(dto);
        } catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setBillType("3");
        dto.setTaskNo("test123");
        dto.setItemNo("");
        List<PsWipInfo> wipList = new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setSn("test123");
        wipList.add(psWipInfo);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setItemNo("test123");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(null);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<SysLookupValuesDTO> sysWhiteList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("test123");
        sysWhiteList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysWhiteList);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysLookupValuesDTO.setLookupMeaning("");
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.getList(Mockito.any())).thenReturn(prodBindingList);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ProdBindingSetting prodBindingSetting=new ProdBindingSetting();
        prodBindingSetting.setUsageCount(new BigDecimal("1"));
        prodBindingSetting.setItemCode("test123");
        prodBindingSetting.setProcessCode("test123");
        prodBindingList.add(prodBindingSetting);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<WipExtendIdentification> wipExtendList =new ArrayList<>();

        dto.setPsWipInfoList(wipList);
        WipExtendIdentification wipExtendIdentification =new WipExtendIdentification();
        wipExtendIdentification.setFormSn("test123");
        wipExtendIdentification.setItemNo("test123");
        wipExtendIdentification.setFormQty(new BigDecimal("1"));
        wipExtendIdentification.setReplaceItemNo("test123");
        wipExtendIdentification.setProcessCode("test123");
        wipExtendList.add(wipExtendIdentification);
        List<BSProcess> process =new ArrayList<>();
        BSProcess bsProcess=new BSProcess();
        bsProcess.setProcessCode("test123");
        bsProcess.setProcessName("test124");
        process.add(bsProcess);
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any())).thenReturn(wipExtendList);
        PowerMockito.when( CrafttechRemoteService.getProcessByProList(Mockito.any())).thenReturn(process);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ProdBindingSetting prodBindingSetting1=new ProdBindingSetting();
        prodBindingSetting1.setUsageCount(new BigDecimal("2"));
        prodBindingSetting1.setItemCode("test123");
        prodBindingSetting1.setProcessCode("test234");
        prodBindingList.add(prodBindingSetting1);
        try{
            service.checkWipExtendForZJ(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void filterErrorBindingDetail() {
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting1=new ProdBindingSetting();
        prodBindingSetting1.setUsageCount(new BigDecimal("2"));
        prodBindingSetting1.setItemCode("test123");
        prodBindingSetting1.setProcessCode("test234");
        prodBindingList.add(prodBindingSetting1);
        try{
            service.filterErrorBindingDetail(prodBindingList,new HashMap<>(),"",new ArrayList<>());
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getWipInfoInfo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setOriginalTask("");
        psTaskList.add(psTask);
        Map<String, Object> wipInfoResult = new HashMap<>();
        wipInfoResult.put("total",new Long("10"));
        wipInfoResult.put("rows",new ArrayList<>());
        PowerMockito.when(psWipInfoService.getPsWipInfoList(Mockito.any())).thenReturn(wipInfoResult);
        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.getWipInfoInfo(psTaskList, new PsWipInfoQueryDTO(), new WarehouseEntryInfoQueryDTO(), "", new ArrayList<>());
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test123");
        psWipInfoList.add(psWipInfo);
        wipInfoResult.put("rows",psWipInfoList);
        List<String> list = new ArrayList<>();
        list.add("test234");
        PowerMockito.when(ProductionDeliveryRemoteService.getContentInfoList(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(list);
        try{
            service.getWipInfoInfo(psTaskList, new PsWipInfoQueryDTO(), new WarehouseEntryInfoQueryDTO(), "", new ArrayList<>());
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkRetentionForDBRK() throws Exception {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setBillType("3");
        try{
            service.checkRetentionForDBRK(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        dto.setBillType("2");
        dto.setPsWipInfoList(new ArrayList<>());
        List<String> list = new ArrayList<>();
        list.add("test234");
        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(list);
        try{
            service.checkRetentionForDBRK(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.checkRetentionForDBRK(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void isPreserveRetention() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(),any())).thenReturn(new ArrayList<>());
        try{
            service.isPreserveRetention("test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setRouteId("test123");
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(),any())).thenReturn(workOrderList);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
        try{
            service.isPreserveRetention("test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        List<CtRouteDetail> lastList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        ctRouteDetail.setNextProcess("N");
        ctRouteDetail.setCurrProcess("1");
        lastList.add(ctRouteDetail);
        CtRouteDetail ctRouteDetail1 = new CtRouteDetail();
        ctRouteDetail1.setRemainTime(new BigDecimal("1"));
        ctRouteDetail1.setNextProcess("1");
        ctRouteDetail1.setCurrProcess("2");
        lastList.add(ctRouteDetail1);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(lastList);
        try{
            service.isPreserveRetention("test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail1.setNextProcess("2");
        try{
            service.isPreserveRetention("test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setNextProcess("2");
        try{
            service.isPreserveRetention("test123");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkSnIsRetention() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(),any())).thenReturn(new ArrayList<>());
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setProdplanId("test123");
        try{
            service.checkSnIsRetention(warehouseEntryInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setRouteId("test123");
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(),any())).thenReturn(workOrderList);
        List<CtRouteDetail> lastList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        ctRouteDetail.setNextProcess("N");
        ctRouteDetail.setCurrProcess("1");
        lastList.add(ctRouteDetail);
        CtRouteDetail ctRouteDetail1 = new CtRouteDetail();
        ctRouteDetail1.setRemainTime(new BigDecimal("1"));
        ctRouteDetail1.setNextProcess("1");
        ctRouteDetail1.setCurrProcess("2");
        lastList.add(ctRouteDetail1);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(lastList);
        warehouseEntryInfo.setPsWipInfoList(new ArrayList<>());
        try{
            service.checkSnIsRetention(warehouseEntryInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.checkSnIsRetention(warehouseEntryInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<WarehouseEntryInfoDTO> detailList = new ArrayList<>();
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setSn("test123");
        detailList.add(warehouseEntryInfoDTO);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(Mockito.any())).thenReturn(detailList);
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.checkSnIsRetention(warehouseEntryInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        List<PsWipInfoDTO> psWipInfoList = new ArrayList<>();
        PsWipInfoDTO psWipInfo = new PsWipInfoDTO();
        psWipInfo.setSn("test123");
        psWipInfoList.add(psWipInfo);
        warehouseEntryInfo.setSnList(psWipInfoList);
        List<String> list = new ArrayList<>();
        list.add("test234");
        PowerMockito.when(psWipInfoService.filterRetentionList(Mockito.any())).thenReturn(list);
        try{
            service.checkSnIsRetention(warehouseEntryInfo);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getStepWarehouseEntryInfo() throws Exception {
        List<PsTask> psTaskList = new ArrayList() {{
            add(new PsTask() {{
                setLeadFlag("HFS");
                setTaskQty(new BigDecimal("12"));
                setTaskId("111");
                setAttribute8("123");
            }});
        }};
        PowerMockito.mockStatic(MicroServiceRestUtil.class,CommonUtils.class);
        List<CtRouteDetail> ctRouteDetails = new ArrayList() {{
            add(new CtRouteDetail() {{
                setRouteId("123321");
                setProcessSeq(0);
                setNextProcess("P0123");
                setLastProcess("Y");
                setCraftSection("入库");
            }});
        }};
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<CtRouteDetail>>(){{setBo(ctRouteDetails);}}));
        try {
            Whitebox.invokeMethod(service, "getStepWarehouseEntryInfo", new HashMap() ,psTaskList);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void getBeforeHighProcessCode() throws Exception {
        ReflectionTestUtils.setField(service, "highProcessCode", "P0123");
        List<CtRouteDetail> ctRouteDetails = new ArrayList<>();
        CtRouteDetail detail = new CtRouteDetail();
        detail.setNextProcess("123");
        ctRouteDetails.add(detail);
        List<String> processCodeList = new ArrayList() {{
            add("P012");
        }};
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        processCodeList.add("P0123");
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        processCodeList.clear();
        processCodeList.add("P0123");
        processCodeList.add("入库");
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        detail.setNextProcess("P0123");
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        detail.setDoOrNotFlag("Y");
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        detail.setSkipRule(Constant.HIGH_TEMP_RULE);
        processCodeList.clear();
        processCodeList.add("000");
        processCodeList.add("P0123");
        processCodeList.add("入库");
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList));
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList));
        Whitebox.invokeMethod(service, "getBeforeHighProcessCode", ctRouteDetails ,"",processCodeList);
        Assert.assertTrue(processCodeList.size() > 0);
    }

    @Test
    public void saveStockInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setSnList(new ArrayList<>());
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(
                JSON.toJSONString(new ServiceData() {{
                    setBo("page");
                    setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                }}));
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), any()))
                .thenReturn(null);
        ReflectionTestUtils.setField(service, "homeLedgerFlag", "N");
        Whitebox.invokeMethod(service, "saveStockInfo", warehouseEntryInfo);
        ReflectionTestUtils.setField(service, "homeLedgerFlag", "Y");
        try {
            Whitebox.invokeMethod(service, "saveStockInfo", warehouseEntryInfo);
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.FAILED_TO_SAVE_STOCK_INFO,e.getMessage());
        }
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        try {
            Whitebox.invokeMethod(service, "saveStockInfo", warehouseEntryInfo);
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.FAILED_TO_SAVE_STOCK_INFO,e.getMessage());
        }
        JsonNode jsonNode1 = JacksonJsonConverUtil.getMapperInstance().readTree(
                JSON.toJSONString(new ServiceData() {{
                    setBo("page");
                    setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                }}));
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode1);
        Whitebox.invokeMethod(service, "saveStockInfo", warehouseEntryInfo);
    }

    @Test
    public void receiptByBillType() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillType("2");
        warehouseEntryInfo.setStockType("家端单板库");
        try {
            Whitebox.invokeMethod(service, "receiptByBillType", warehouseEntryInfo,null,"",new StringBuffer());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        ReflectionTestUtils.setField(service, "homeLedgerFlag", "Y");
        try {
            Whitebox.invokeMethod(service, "receiptByBillType", warehouseEntryInfo,null,"",new StringBuffer());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void invokeErpInterface() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setSnList(new ArrayList<>());
        Whitebox.invokeMethod(service, "invokeErpInterface", warehouseEntryInfo,new StringBuffer());
        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_HOME_BOARD);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.when(warehouseEntryDetailRepository.getLpnByBillNo(Mockito.any())).thenReturn("1123");
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(),Mockito.any())).thenReturn(false);
        Whitebox.invokeMethod(service, "invokeErpInterface", warehouseEntryInfo,new StringBuffer());
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(),Mockito.any())).thenReturn(true);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "invokeErpInterface", warehouseEntryInfo,new StringBuffer()));
    }

    @Test
    public void extractMethod() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        Whitebox.invokeMethod(service, "extractMethod", warehouseEntryInfo, new StringBuffer());
        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_HOME_BOARD);
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenReturn(false);
        Whitebox.invokeMethod(service, "extractMethod", warehouseEntryInfo, new StringBuffer());
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenReturn(true);
        Whitebox.invokeMethod(service, "extractMethod", warehouseEntryInfo, new StringBuffer());
        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_K2);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "extractMethod", warehouseEntryInfo, new StringBuffer()));
    }

    @Test
    public void testCheckAuxMaterialBindingForZJ() throws Exception {
        WarehouseEntryInfoQueryDTO warehouseEntryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        warehouseEntryInfoQueryDTO.setBillType("3");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        try {
            service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), com.zte.common.model.MessageId.SUBMIT_QTY_IS_NULL);
        }
        warehouseEntryInfoQueryDTO.setBillType("2");
        warehouseEntryInfoQueryDTO.setCommitedQty(new BigDecimal(10));
        try {
            service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), com.zte.common.model.MessageId.TRACKING_OR_TASK_NO_NOT_EXIST);
        }
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTaskList.add(psTask);
        psTask.setOriginalTask("123A");
        PowerMockito.when(PlanscheduleRemoteService.getTaskListByTaskNos(any())).thenReturn(psTaskList);
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        psTask.setOriginalTask(null);
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        List<ProdBindingSetting> prodBindingList = new ArrayList<>();
        ProdBindingSetting prodBindingSetting = new ProdBindingSetting();
        prodBindingSetting.setUsageCount(NumConstant.BIG_ONE);
        prodBindingList.add(prodBindingSetting);
        ProdBindingSetting prodBindingSetting1 = new ProdBindingSetting();
        prodBindingSetting1.setProductCode("1232131aaa");
        prodBindingSetting1.setUsageCount(NumConstant.BIG_ONE);
        prodBindingList.add(prodBindingSetting1);
        psTask.setTaskNo("123A");
        PowerMockito.when(prodBindingSettingRepository.getList(any())).thenReturn(prodBindingList);
        List<WipExtendIdentification> wipExtendList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendList.add(wipExtendIdentification);
        List<WarehouseEntryInfoDTO> commitedQtyList = new ArrayList<>();
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        commitedQtyList.add(warehouseEntryInfoDTO);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(wipExtendList);
        PowerMockito.when(warehouseEntryInfoRepository.getCommitQtyByTaskNo(any(), any())).thenReturn(commitedQtyList);
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        warehouseEntryInfoDTO.setCommitedQty(new BigDecimal(0));
        List<ItemListEntityDTO> erpList = new ArrayList<>();
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setRequiredQuantity("0");
        prodBindingSetting.setUsageCount(NumConstant.BIG_ZERO);
        erpList.add(itemListEntityDTO);
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(any())).thenReturn(erpList);
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        prodBindingSetting.setUsageCount(NumConstant.BIG_ONE);
        itemListEntityDTO.setItemNo("12345");
        prodBindingSetting.setItemCode("12345");
        prodBindingSetting.setProductCode("123A");
        psTask.setTaskQty(new BigDecimal(0));
        psTask.setItemNo("123A");
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);

        prodBindingSetting.setUsageCount(null);
        assertThrows(com.zte.common.model.MessageId.BIND_SETTING_USAGE_COUNT_IS_NULL, MesBusinessException.class,()->service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO));

        prodBindingSetting.setUsageCount(NumConstant.BIG_ONE);
        itemListEntityDTO.setItemNo("1234");
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        itemListEntityDTO.setItemNo("12345");
        itemListEntityDTO.setRequiredQuantity("10");
        psTask.setTaskQty(new BigDecimal(10));
        try {
            service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.STOCK_AUX_MATERIAL_UNBINDING,e.getMessage());
        }
        wipExtendIdentification.setItemNo("12345");
        wipExtendIdentification.setFormQty(new BigDecimal(10));
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
        sysLookupTypesDTO.setLookupMeaning("N");
        service.checkAuxMaterialBindingForZJ(warehouseEntryInfoQueryDTO);
    }

    @Test
    public void updateOrDeleteTest() throws Exception {

        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        int insertDetailResult = 1;
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        Map<String, Object> result = new HashMap<>();
        dto.setBillTypeCode(new BigDecimal(BILL_TYPE_LOOKUP_CODE_DBFX));
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        Whitebox.invokeMethod(service, "updateOrDelete", dto, insertDetailResult, warehouseEntryInfo, result);
        Assert.assertTrue(1==1);
        dto.setBillTypeCode(new BigDecimal(BILL_TYPE_LOOKUP_CODE_SCRAP));
        Whitebox.invokeMethod(service, "updateOrDelete", dto, insertDetailResult, warehouseEntryInfo, result);
        Assert.assertTrue(1==1);
    }
    @Test(timeout = 8000)
    public void testBatchInsertPsScanHistory_EmptyList() {
        List<PsScanHistory> insertWipScanHistorys = new ArrayList<>();
        service.batchInsertPsScanHistory(insertWipScanHistorys);
        Assert.assertTrue(1==1);
        PsScanHistory dto=new PsScanHistory();
        dto.setSmtScanId("234343");
        insertWipScanHistorys.add(dto);
        service.batchInsertPsScanHistory(insertWipScanHistorys);
        Assert.assertTrue(1==1);
    }

    @Test(timeout = 8000)
    public void testGetLastProcess_WithEmptyList() throws Exception {
        List<CtRouteDetail> listRouteDetail = new ArrayList<>();
        PowerMockito.mockStatic(MicroServiceRestUtil.class,CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<CtRouteDetail>>(){{setBo(listRouteDetail);}}));
        CtRouteDetail result = service.getLastProcess("routeId");
        assertNull(result);
        CtRouteDetail dto=new CtRouteDetail();
        dto.setProcessSeq(1);
        listRouteDetail.add(dto);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<CtRouteDetail>>(){{setBo(listRouteDetail);}}));
        CtRouteDetail result1 = service.getLastProcess("routeId");
        assertNull(result1);

        CtRouteDetail dto1=new CtRouteDetail();
        dto1.setLastProcess("Y");
        listRouteDetail.add(dto1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(JSON.toJSONString(new ServiceData<List<CtRouteDetail>>(){{setBo(listRouteDetail);}}));
        CtRouteDetail result2 = service.getLastProcess("routeId");
        Assert.assertTrue(1==1);
    }

    @Test(timeout = 8000)
    public void deleteWarehouseEntryAdditionalInfo_withValidBillNo_shouldCallRepositoryMethod() {
        String billNo = "validBillNo";
        service.deleteWarehouseEntryAdditionalInfo(billNo);
        verify(warehouseEntryInfoRepository, times(1)).deleteWarehouseEntryAdditionalInfo(billNo);
    }

    @Test(timeout = 8000)
    public void deleteWarehouseEntryAdditionalInfo_withNullBillNo_shouldNotCallRepositoryMethod() {
        String billNo = null;
        service.deleteWarehouseEntryAdditionalInfo(billNo);
        verify(warehouseEntryInfoRepository, never()).deleteWarehouseEntryAdditionalInfo(anyString());
    }

    @Test(timeout = 8000)
    public void testGetBoardPackageWarehouseDetail_EmptyBillNo() {
        List<WarehouseEntryDetailDTO> result =
                service.getBoardPackageWarehouseDetail("");
        assertEquals(0, result.size());
    }

    @Test(timeout = 8000)
    public void testGetBoardPackageWarehouseInfoJob_EmptyList() {
        WarehouseEntryInfo dto=new WarehouseEntryInfo();
        dto.setCarryAccount(STR_1);
        dto.setBoardPackErpSucceNode(STR_200);
        List<WarehouseEntryInfo> result = service.getBoardPackageWarehouseInfoJob(dto);
        assertEquals(new ArrayList<>(), result);
    }

    @Test(timeout = 8000)
    public void testUpdateWarehouseEntryInfoByIdSucceNode_Positive() {
        WarehouseEntryInfo record = new WarehouseEntryInfo();
        record.setWarehouseEntryId("RK12323");
        record.setBoardPackErpSucceNode("1");
        when(warehouseEntryInfoRepository.updateWarehouseEntryInfoByIdSucceNode(record))
                .thenReturn(1);
        int result = service.updateWarehouseEntryInfoByIdSucceNode(record);
        assertEquals(1, result);
    }

    @Test(timeout = 8000)
    public void deleteWarehouseEntryByBillNo_withValidBillNo_shouldCallRepositoryMethod() {
        String billNo = "validBillNo";
        service.deleteWarehouseEntryByBillNo(billNo);
        verify(warehouseEntryInfoRepository, times(1)).deleteWarehouseEntryByBillNo(billNo);
    }

    /* Started by AICoder, pid:0d03c182a2xaa4514d1a0b37008319704ab62f6e */
    @Test
    public void checkWeight() throws Exception {
        String taskNo = "";
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        ReflectionTestUtils.setField(service, "needCheckWeight", true);
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        taskNo = "111";
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("111");
        wipInfoList.add(psWipInfo);

        try {
            Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(com.zte.common.model.MessageId.FAILED_TO_GET_TASK_INFO_BY_TASKNO, e.getExMsgId());
        }

        PsTask psTask = new PsTask();
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(Mockito.anyString())).thenReturn(psTask);
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        psTask.setCustomerName("test111");
        List<SysLookupValuesDTO> lookupValuesList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(Constant.LOOKUP_TYPE_7500)).thenReturn(lookupValuesList);
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("test");
        lookupValuesList.add(sysLookupValuesDTO);
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);

        psTask.setCustomerName("test");
        Whitebox.invokeMethod(service, "checkWeight", taskNo, wipInfoList);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:0d03c182a2xaa4514d1a0b37008319704ab62f6e */

    @Test
    public void testGetPage() {
       Assert.assertEquals(new ArrayList<>(), service.getPage(new HashMap<>(), 1L, 1L));
       when(warehouseEntryInfoRepository.getPage(anyMap())).thenReturn(null);
       service.getPage(new HashMap<>(), 1L, 1L);
        ArrayList<WarehouseEntryInfo> warehouseEntryInfos = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setStockCode("10349620");
        warehouseEntryInfo.setStockName("10349620");
        warehouseEntryInfos.add(warehouseEntryInfo);
        when(warehouseEntryInfoRepository.getPage(anyMap())).thenReturn(warehouseEntryInfos);
        service.getPage(new HashMap<>(), 1L, 1L);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        ArrayList<LinesideWarehouseInfo> linesideWarehouseInfos = new ArrayList<>();
        LinesideWarehouseInfo linesideWarehouseInfo = new LinesideWarehouseInfo();
        linesideWarehouseInfo.setWarehouseCode("10349620");
        linesideWarehouseInfo.setWarehouseName("10349620");
        linesideWarehouseInfos.add(linesideWarehouseInfo);
        PowerMockito.when(ProductionDeliveryRemoteService.queryWarehouseListByCode(anyString())).thenReturn(linesideWarehouseInfos);
        service.getPage(new HashMap<>(), 1L, 1L);
    }


    @Test
    public void getReconfigurationList () throws Exception {
        PsWipInfoQueryDTO psWipInfoQueryDTO = new PsWipInfoQueryDTO();
        WarehouseEntryInfoQueryDTO entryInfoQueryDTO = new WarehouseEntryInfoQueryDTO();
        PowerMockito.when(centerfactoryRemoteService.getReconfigurationFlag(Mockito.any())).thenReturn(false);
        Whitebox.invokeMethod(service, "getReconfigurationList", psWipInfoQueryDTO, entryInfoQueryDTO);
        Assert.assertFalse(psWipInfoQueryDTO.isChangeConfigurationFlag());

        PowerMockito.when(centerfactoryRemoteService.getReconfigurationFlag(Mockito.any())).thenReturn(true);
        List<ItemListEntityDTO> itemListEntityDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getErpItemListByTaskNo(Mockito.any())).thenReturn(itemListEntityDTOList);
        Whitebox.invokeMethod(service, "getReconfigurationList", psWipInfoQueryDTO, entryInfoQueryDTO);
        Assert.assertTrue(psWipInfoQueryDTO.isChangeConfigurationFlag());

        ItemListEntityDTO entityDTO2 = new ItemListEntityDTO();
        entityDTO2.setItemNo("");
        entityDTO2.setRequiredQuantity("1");
        itemListEntityDTOList.add(entityDTO2);
        Whitebox.invokeMethod(service, "getReconfigurationList", psWipInfoQueryDTO, entryInfoQueryDTO);
        Assert.assertTrue(psWipInfoQueryDTO.isChangeConfigurationFlag());

        ItemListEntityDTO entityDTO = new ItemListEntityDTO();
        entityDTO.setItemNo("123");
        entityDTO.setRequiredQuantity("1");
        itemListEntityDTOList.add(entityDTO);
        ItemListEntityDTO entityDTO1 = new ItemListEntityDTO();
        entityDTO1.setItemNo("321");
        entityDTO1.setRequiredQuantity("-1");
        itemListEntityDTOList.add(entityDTO1);
        ItemListEntityDTO entityDTO3 = new ItemListEntityDTO();
        entityDTO3.setItemNo("132");
        entityDTO3.setRequiredQuantity("");
        itemListEntityDTOList.add(entityDTO3);
        ItemListEntityDTO entityDTO4 = new ItemListEntityDTO();
        entityDTO4.setItemNo("112");
        entityDTO4.setRequiredQuantity("1");
        itemListEntityDTOList.add(entityDTO4);
        Page<TaskReconfigurationRecord> recordPage = new Page<>();
        PowerMockito.when(taskReconfigurationRecordService.selectSumQuantityPage(Mockito.any())).thenReturn(recordPage);
        Whitebox.invokeMethod(service, "getReconfigurationList", psWipInfoQueryDTO, entryInfoQueryDTO);
        Assert.assertTrue(psWipInfoQueryDTO.getChangeConfigurationList().isEmpty());

        List<TaskReconfigurationRecord> recordList = new ArrayList<>();
        TaskReconfigurationRecord record = new TaskReconfigurationRecord();
        record.setItemCode("123");
        record.setQuantity(1);
        recordList.add(record);
        TaskReconfigurationRecord record1 = new TaskReconfigurationRecord();
        record1.setItemCode("321");
        record1.setQuantity(1);
        recordList.add(record1);
        TaskReconfigurationRecord record2 = new TaskReconfigurationRecord();
        record2.setItemCode("312");
        record2.setQuantity(1);
        recordList.add(record2);
        TaskReconfigurationRecord record3 = new TaskReconfigurationRecord();
        record3.setItemCode("132");
        record3.setQuantity(1);
        recordList.add(record3);
        TaskReconfigurationRecord record4 = new TaskReconfigurationRecord();
        record4.setItemCode("112");
        record4.setQuantity(2);
        recordList.add(record4);
        recordPage.setRows(recordList);
        Whitebox.invokeMethod(service, "getReconfigurationList", psWipInfoQueryDTO, entryInfoQueryDTO);
        assertEquals(2, psWipInfoQueryDTO.getChangeConfigurationList().size());

    }
}
/*Ended by AICoder, pid:d6ea512c51e6486989c81c368011a894*/