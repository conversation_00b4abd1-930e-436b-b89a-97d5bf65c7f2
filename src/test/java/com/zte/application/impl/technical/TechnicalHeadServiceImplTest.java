package com.zte.application.impl.technical;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.BarcodeLockHeadService;
import com.zte.application.BarcodeUnlockService;
import com.zte.application.CommonTechnicalService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.technical.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.technical.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2022-09-29 14:03
 */
@PrepareForTest({BasicsettingRemoteService.class, ImesExcelUtil.class, RedisHelper.class,
        EasyExcelFactory.class, BeanUtils.class, CommonUtils.class, CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, JacksonJsonConverUtil.class, JSON.class, CenterfactoryRemoteService.class,
        RequestHeadValidationUtil.class})
public class TechnicalHeadServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private TechnicalHeadServiceImpl technicalHeadServiceImpl;
    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Mock
    private TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private HttpServletResponse response;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private BarcodeLockHeadService barcodeLockHeadService;

    @Mock
    private BarcodeLockHeadRepository barcodeLockHeadRepository;
    @Mock
    private BarcodeUnlockService barcodeUnlockService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private CommonTechnicalService commonTechnicalService;
    @Mock
    private TechnicalChangeDetailHisRepository technicalChangeDetailHisRepository;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;


    @Before
    public void init() {
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }
    @Test
    public void sendMailForUnissued2() {
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        technicalHeadServiceImpl.sendMailForUnissued(null,"","",barcodeLockDetailEntityDTOList);
        barcodeLockDetailEntityDTOList.add(new BarcodeLockDetailEntityDTO());
        technicalHeadServiceImpl.sendMailForUnissued(new PdmTechnicalDealForKafkaDTO(),"","",barcodeLockDetailEntityDTOList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void queryTechnicalInfoByProdPlanId() throws Exception {
        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalInfoByProdPlanId(""));
    }
    @Test
    public void setCraftSection() throws Exception {
        Whitebox.invokeMethod(technicalHeadServiceImpl,"setCraftSection",new TechnicalChangeDetailDTO(),new ArrayList<>(),new PsTask());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        Whitebox.invokeMethod(technicalHeadServiceImpl,"setCraftSection",new TechnicalChangeDetailDTO(),new ArrayList<>(),new PsTask(){{setTaskStatus(Constant.FINISH_WORK);}});
    }
    @Test
    public void barcodeLockBatchInsert() throws Exception {
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "barcodeLockBatchInsert", barcodeLockDetailEntityDTOList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "barcodeLockBatchInsert", new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setErrorMsg() throws Exception {
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setErrorMsg", new PdmTechnicalChangeInfoEntityDTO(), new ArrayList<>(), "");
        PdmTechnicalChangeInfoEntityDTO technicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        technicalChangeInfoEntityDTO.setErrorMsg("msg");
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setErrorMsg", technicalChangeInfoEntityDTO, new ArrayList<>(), "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getContent() throws Exception {
        // Whitebox.invokeMethod(technicalHeadServiceImpl, "getContent", "", "", "", "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void undistributedMail() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> needDealList = new ArrayList<>();
        needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setNeedDealList(needDealList);
        for (int i = 0; i < 5; i++) {
            needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "undistributedMail", pdmTechnicalDealForKafkaDTO, needDealList, "", "");
        Whitebox.invokeMethod(technicalHeadServiceImpl, "undistributedMail", pdmTechnicalDealForKafkaDTO, new ArrayList<>(), "", "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void sentMailWhenHasBeenIssued() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> needDealList = new ArrayList<>();
        needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setNeedDealList(needDealList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sentMailWhenHasBeenIssued", new PdmTechnicalDealForKafkaDTO());
        PowerMockito.when(commonTechnicalService.getContent(any())).thenReturn("msg");
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sentMailWhenHasBeenIssued", pdmTechnicalDealForKafkaDTO);
        for (int i = 0; i < 5; i++) {
            PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
            pdmTechnicalChangeInfoEntityDTO.setPsTask(new PsTask());
            needDealList.add(pdmTechnicalChangeInfoEntityDTO);
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sentMailWhenHasBeenIssued", pdmTechnicalDealForKafkaDTO);

        try {
            Whitebox.invokeMethod(technicalHeadServiceImpl, "sentMailWhenHasBeenIssued", null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setPsTaskAndRouteInfo() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> needDealList = new ArrayList<>();
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setErrorMsg("msg");
        needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        needDealList.add(pdmTechnicalChangeInfoEntityDTO);

        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO1 = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO1.setPsTask(new PsTask());
        pdmTechnicalChangeInfoEntityDTO1.setErrorMsg("msg");
        needDealList.add(pdmTechnicalChangeInfoEntityDTO1);

        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setNeedDealList(needDealList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setPsTaskAndRouteInfo", needDealList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setPsTaskAndRouteInfo", new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateCenterFactoryTechicalChangeInfoStatus() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> needDealList = new ArrayList<>();
        needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO1 = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO1.setSendStatus(Constant.PdmTechnicalChangeStatus.DELETED);
        pdmTechnicalDealForKafkaDTO1.setNeedDealList(needDealList);

        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO2 = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO2.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        pdmTechnicalDealForKafkaDTO2.setNeedDealList(needDealList);

        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.VOIDED);
        pdmTechnicalDealForKafkaDTO.setNeedDealList(needDealList);
        Whitebox.invokeMethod(technicalHeadServiceImpl,"updateCenterFactoryTechicalChangeInfoStatus",pdmTechnicalDealForKafkaDTO);
        Whitebox.invokeMethod(technicalHeadServiceImpl,"updateCenterFactoryTechicalChangeInfoStatus",pdmTechnicalDealForKafkaDTO1);
        Whitebox.invokeMethod(technicalHeadServiceImpl,"updateCenterFactoryTechicalChangeInfoStatus",pdmTechnicalDealForKafkaDTO2);
        Whitebox.invokeMethod(technicalHeadServiceImpl,"updateCenterFactoryTechicalChangeInfoStatus",new PdmTechnicalDealForKafkaDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void sendMailForUnissued() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> needDealList = new ArrayList<>();
        needDealList.add(new PdmTechnicalChangeInfoEntityDTO());
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setNeedDealList(needDealList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sendMailForUnissued", pdmTechnicalDealForKafkaDTO, needDealList, needDealList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getCcList() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "getCcList");
        List<String> ccList = new ArrayList<>();
        for (int i = 0; i < 201; i++) {
            ccList.add("11");
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setCcList", ccList, new BarcodeLockHeadEntityDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void insertLockDetail() throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("123");
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setCraftSection("DIP");
        ctRouteDetailDTO.setNextProcess("8");
        listDetail.add(ctRouteDetailDTO);
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setCtRouteDetailDTOList(listDetail);
            pdmTechnicalChangeInfoEntityDTO.setProdplanId("7778889");
        }
        List<String> needInsertProdPlanIdList = new ArrayList<>();
        needInsertProdPlanIdList.add("7778889");
        Whitebox.invokeMethod(technicalHeadServiceImpl, "insertLockDetail", pdmTechnicalChangeInfoEntityDTOList, needInsertProdPlanIdList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertLockHeadAndDetail() throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("123");
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setCraftSection("DIP");
        ctRouteDetailDTO.setNextProcess("8");
        listDetail.add(ctRouteDetailDTO);
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setCtRouteDetailDTOList(listDetail);
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "insertLockHeadAndDetail", "name", pdmTechnicalChangeInfoEntityDTOList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "insertLockHeadAndDetail", "name", new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void technicalChangeInfoDeal4() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        ctRouteDetailDTO2.setNextProcess("2");
        ctRouteDetailDTO2.setProcessSeq(new BigDecimal(2));
        ctRouteDetailDTO2.setCraftSection("SMT-B");
        listDetail.add(ctRouteDetailDTO2);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(null);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        technicalChangeInfoDeal3();
        technicalChangeInfoDeal2();
        technicalChangeInfoDeal();
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);


    }

    public void technicalChangeInfoDeal3() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = new ArrayList<>();
        TechnicalChangeDetailDTO changeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTOList.add(changeDetailDTO);
        PowerMockito.when(technicalChangeDetailRepository.getUnCompleteByBillNo(anyString())).thenReturn(technicalChangeDetailDTOList);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    public void technicalChangeInfoDeal() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.UNISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setUpdatedBy("10270445");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.UNISSUED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    public void technicalChangeInfoDeal2() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setHeadId(UUID.randomUUID().toString());
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.DELETED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.UNISSUED);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(null);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryTechnicalHeadByPage() throws Exception {
        Page<TechnicalChangeHeadDTO> pageParams = new Page<>();
        TechnicalChangeHeadDTO dto = new TechnicalChangeHeadDTO();
        dto.setSn("889952400001,889952400002");
        pageParams.setParams(dto);

        PowerMockito.when(psWipInfoRepository.selectAttribute1BySn(Mockito.anyString()))
                .thenReturn("8899512");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap)
        ;

        List<TechnicalChangeHeadDTO> list = new LinkedList<>();
        TechnicalChangeHeadDTO b1 = new TechnicalChangeHeadDTO();
        b1.setCreatedBy("00000");
        b1.setLastUpdatedBy("0000");
        b1.setTechnicalStatus("0");
        list.add(b1);
        TechnicalChangeHeadDTO b2 = new TechnicalChangeHeadDTO();
        b2.setCreatedBy("00000");
        b2.setLastUpdatedBy("0000");
        b2.setTechnicalStatus("1");
        list.add(b2);
        TechnicalChangeHeadDTO b3 = new TechnicalChangeHeadDTO();
        b3.setCreatedBy("00000");
        b3.setLastUpdatedBy("0000");
        b3.setTechnicalStatus("2");
        list.add(b3);
        TechnicalChangeHeadDTO b4 = new TechnicalChangeHeadDTO();
        b4.setCreatedBy("00000");
        b4.setLastUpdatedBy("0000");
        b4.setTechnicalStatus("3");
        list.add(b4);
        TechnicalChangeHeadDTO b5 = new TechnicalChangeHeadDTO();
        b5.setCreatedBy("00000");
        b5.setLastUpdatedBy("0000");
        b5.setTechnicalStatus("4");
        list.add(b5);

        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalHeadByPage(any())).thenReturn(list);

        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);
        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalHeadByPage(pageParams));


    }

    @Test
    public void queryTechnicalDetailByPage() throws Exception {
        Page<TechnicalChangeDetailDTO> pageParams = new Page<>();
        TechnicalChangeHeadDTO dto = new TechnicalChangeHeadDTO();
        dto.setHeadId("123");
        pageParams.setParams(dto);

        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setTechnicalStatus("1");
        d1.setUnlockType("1");
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("0");
        d2.setTechnicalStatus("0");
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("0");
        d3.setTechnicalStatus("2");
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("0");
        d4.setTechnicalStatus("3");
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("0");
        d5.setTechnicalStatus("4");
        resultList.add(d5);
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalDetailByPage(any()))
                .thenReturn(resultList);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
        b1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("0025", b1);
        HrmPersonInfoDTO b2 = new HrmPersonInfoDTO();
        b2.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("0026", b2);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap);

        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);

        technicalHeadServiceImpl.queryTechnicalDetailByPage(pageParams);
        dto.setSn("889952400001,889952400002");
        pageParams.setParams(dto);
        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalDetailByPage(pageParams));
    }

    @Test
    public void exportTechnicalDetails() throws Exception {
        TechnicalChangeHeadDTO queryParam = new TechnicalChangeHeadDTO();

        List<SysLookupValuesDTO> valueByTypeCodes = new LinkedList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setAttribute1(Constant.TECHNICAL);
        sysLookupValuesDTO.setLookupMeaning("200");
        valueByTypeCodes.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.lookupType.VALUE_1004041))
                .thenReturn(valueByTypeCodes)
        ;

        PowerMockito.when(EasyExcelFactory.writerSheet(0, Constant.TECHNICAL_EXPORT_SHEET_NAME)).thenReturn(excelWriterSheetBuilder);
        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

        PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), TechnicalChangeDetailDTO.class)).thenReturn(write);
        PowerMockito.when(write.excludeColumnFiledNames(Mockito.anyList())).thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
        PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);


        Page<TechnicalChangeHeadDTO> pageParams = new Page<>();
        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setSn("889952400001");
        pageParams.setParams(technicalChangeHeadDTO);

        PowerMockito.when(psWipInfoRepository.selectAttribute1BySn(Mockito.anyString()))
                .thenReturn("8899512");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap)
        ;
        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setUnlockType("0");
        d1.setTechnicalStatusHead("0");
        d1.setTechnicalStatus("0");
        d1.setUnlockStatus(0);
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("1");
        d2.setTechnicalStatusHead("1");
        d2.setTechnicalStatus("1");
        d2.setUnlockStatus(1);
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("2");
        d3.setTechnicalStatusHead("2");
        d3.setTechnicalStatus("2");
        d3.setUnlockStatus(2);
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("3");
        d4.setTechnicalStatusHead("3");
        d4.setTechnicalStatus("3");
        d4.setUnlockStatus(3);
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("4");
        d5.setTechnicalStatusHead("4");
        d5.setTechnicalStatus("4");
        d5.setUnlockStatus(4);
        resultList.add(d5);
        PowerMockito.when(technicalChangeHeadRepository.exportDetailByPage(Mockito.any())).thenReturn(resultList);
        technicalHeadServiceImpl.exportTechnicalDetails(queryParam, response);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void batchDeleteByHeadId() {
        List<String> list = new LinkedList<>();
        list.add("123");
        technicalHeadServiceImpl.batchDeleteByHeadId(list, "system");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void querySnDetailByPage() throws Exception {

        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setUnlockType("0");
        d1.setTechnicalStatusHead("0");
        d1.setTechnicalStatus("0");
        d1.setUnlockStatus(0);
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("1");
        d2.setTechnicalStatusHead("1");
        d2.setTechnicalStatus("1");
        d2.setUnlockStatus(1);
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("2");
        d3.setTechnicalStatusHead("2");
        d3.setTechnicalStatus("2");
        d3.setUnlockStatus(2);
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("3");
        d4.setTechnicalStatusHead("3");
        d4.setTechnicalStatus("3");
        d4.setUnlockStatus(3);
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("4");
        d5.setTechnicalStatusHead("4");
        d5.setTechnicalStatus("4");
        d5.setUnlockStatus(4);
        resultList.add(d5);

        // 1. 查条码明细
        PowerMockito.when(technicalChangeHeadRepository.querySnDetailByPage(Mockito.any())).thenReturn(resultList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap);
        Page<TechnicalChangeDetailDTO> pageParams = new Page<>();
        TechnicalChangeHeadDTO dto = new TechnicalChangeHeadDTO();
        dto.setSn("889952400001,889952400002");
        pageParams.setParams(dto);

        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);

        technicalHeadServiceImpl.querySnDetailByPage(pageParams);

        PowerMockito.when(technicalChangeHeadRepository.querySnDetailSnByPage(Mockito.any())).thenReturn(resultList);

        TechnicalChangeHeadDTO head = new TechnicalChangeHeadDTO();
        head.setSnList(Arrays.asList("889973000001"));
        pageParams.setParams(head);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(JSON.class);

        Assert.assertNotNull(technicalHeadServiceImpl.querySnDetailSnByPage(pageParams));
    }


    @Test
    public void testQueryTechnicalDetailList() {
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(null);
        try {
            technicalHeadServiceImpl.queryTechnicalDetailList("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHICAL_CHANGE_NOT_EXITS, e.getMessage());
        }

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(NumConstant.STRING_THREE);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);
        try {
            technicalHeadServiceImpl.queryTechnicalDetailList("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHICAL_CHANGE_UNLOCKED, e.getMessage());
        }
        technicalChangeHeadDTO.setTechnicalStatus(NumConstant.STRING_TWO);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        PowerMockito.when(technicalChangeDetailRepository.getListByBillNo(anyString())).thenReturn(detailList);
        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalDetailList(""));
    }

    @Test
    public void testQueryRetentionPage() throws Exception {
        List<TechnicalChangeSnDTO> detailList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTO.setCurrProcess("test");
        detailList.add(technicalChangeSnDTO);
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(detailList);

        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));
        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        param.setProdplanId("test");
        Assert.assertNotNull(technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
            setParams(param);
        }}));
    }

    @Test
    public void testQueryRetentionPage2() throws Exception {
        List<TechnicalChangeSnDTO> detailList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTO.setCurrProcess("test");
        detailList.add(technicalChangeSnDTO);
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(detailList);

        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));

        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        param.setSn("test");
        param.setProdplanId("test");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("test");
        wipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(wipInfoList);
        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(new ArrayList<TechnicalChangeExecInfo>() {
        });
        try {
            Assert.assertNotNull(technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
                setParams(param);
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_EXEC_INFO_EXISTED, e.getMessage());
        }


    }

    @Test
    public void testQueryRetentionPage3() throws Exception {
        List<TechnicalChangeSnDTO> detailList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTO.setCurrProcess("test");
        detailList.add(technicalChangeSnDTO);
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(detailList);

        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));

        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        param.setSn("test");
        param.setProdplanId("test");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("test");
        wipInfoList.add(psWipInfo);
        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(new ArrayList<TechnicalChangeExecInfo>() {
        });
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(null);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(new ArrayList<BarcodeExpandDTO>() {{
            add(new BarcodeExpandDTO());
        }});
        technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
            setParams(param);
        }});

        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(new ArrayList<TechnicalChangeExecInfo>() {
            {
                add(new TechnicalChangeExecInfo());
            }
        });
        try {
            technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
                setParams(param);
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_EXEC_INFO_EXISTED, e.getMessage());
        }

        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(new ArrayList<TechnicalChangeExecInfo>() {
        });
        try {

            param.setSn("2test2");
            param.setProdplanId("1test");
            Assert.assertNotNull(technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
                setParams(param);
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_BELONG_PRODPLANID, e.getMessage());
        }
    }

    @Test
    public void testScanSubmit() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        List<TechnicalChangeExecInfo> paramList = new ArrayList<>();
        TechnicalChangeExecInfo execInfo = new TechnicalChangeExecInfo();
        execInfo.setId("1");
        execInfo.setChgReqNo("rrrt");
        execInfo.setSn("sss");
        paramList.add(execInfo);
        PowerMockito.when(technicalChangeExecInfoRepository.insertBatch(any())).thenReturn(1l);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno()).thenReturn(Pair.of("52", "55"));

        PowerMockito.when(redisTemplate.execute(Mockito.anyObject(), Mockito.anyList(), Mockito.anyString())).thenReturn(0l);
        try {
            technicalHeadServiceImpl.scanSubmit(paramList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CHG_SUBMIT_ERROR, e.getMessage());
        }
        PowerMockito.when(redisTemplate.execute(Mockito.anyObject(), Mockito.anyList(), Mockito.anyString())).thenReturn(-1l);
        PowerMockito.when(technicalChangeExecInfoRepository.selectSnBatch(Mockito.anyList()))
                .thenReturn(paramList);
        try {
            technicalHeadServiceImpl.scanSubmit(paramList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryTechnicalDetail() throws Exception {
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setCraftSection("SMT-A");
        technicalChangeDetailDTO.setProdplanId("8899720");
        technicalChangeDetailDTO.setTaskNo("kk");


        List<PsTask> childTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setProdplanId("8899721");
        childTasks.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPartsPlanNo(Mockito.any()))
                .thenReturn(childTasks);

        List<PsWorkOrderDTO> workOrderDTOS = new LinkedList<>();
        PsWorkOrderDTO b1 = new PsWorkOrderDTO();
        b1.setCraftSection("SMT-A");
        b1.setChildFlag(true);
        b1.setSourceTask("8899721");
        workOrderDTOS.add(b1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(Mockito.anyString()))
                .thenReturn(workOrderDTOS)
        ;

        List<TechnicalChangeDetailDTO> detailDTOList = new LinkedList<>();
        TechnicalChangeDetailDTO c1 = new TechnicalChangeDetailDTO();
        c1.setChgReqNo("req9987");
        detailDTOList.add(c1);
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalDetail(Mockito.any()))
                .thenReturn(detailDTOList)
        ;

        List<BarcodeLockDetail> lockDetailList = new LinkedList<>();
        BarcodeLockDetail d1 = new BarcodeLockDetail();
        d1.setBillNo("SD456");
        lockDetailList.add(d1);
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(lockDetailList)
        ;

        Assert.assertEquals(null,technicalHeadServiceImpl.queryTechnicalDetail(technicalChangeDetailDTO));
    }

    @Test
    public void syncTechnicalInfo() throws Exception {
        String factoryId = "52";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.LookUpKey.LOOK_7000014));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.eq(Constant.LookUpKey.LOOK_7000014))).thenReturn(sysLookupTypesDTO);
        try {
            technicalHeadServiceImpl.syncTechnicalInfo(factoryId);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        sysLookupTypesDTO.setLookupMeaning("2023-02-10 20:30:22");
        List<TechnicalChangeDetailDTO> syncDataList = new ArrayList<>();
        PowerMockito.when(technicalChangeDetailRepository.getSyncDataByLastUpdatedDate(Mockito.any(), Mockito.anyInt(), Mockito.anyString())).thenReturn(syncDataList);
        technicalHeadServiceImpl.syncTechnicalInfo(factoryId);

        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setLastUpdatedDate(new Date());
        syncDataList.add(technicalChangeDetailDTO);

        technicalHeadServiceImpl.syncTechnicalInfo(factoryId);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void retentionSn() throws Exception {
        TechnicalChangeRetentionListQueryDTO query = new TechnicalChangeRetentionListQueryDTO();
        query.setFactoryId("52");
        try {
            technicalHeadServiceImpl.retentionSn(query);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_RULE_ERROR, e.getMessage());
        }
        query.setSn("889973000001");
        try {
            technicalHeadServiceImpl.retentionSn(query);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }
        List<BarcodeExpandDTO> expandDTOList = new LinkedList<>();
        BarcodeExpandDTO b1 = new BarcodeExpandDTO();
        expandDTOList.add(b1);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any()))
                .thenReturn(expandDTOList)
        ;
        try {
            technicalHeadServiceImpl.retentionSn(query);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_IS_NOT_EXITS, e.getMessage());
        }
        List<PsTask> psTasks = new LinkedList<>();
        PsTask p1 = new PsTask();
        p1.setFactoryId(new BigDecimal("53"));
        psTasks.add(p1);
        PowerMockito.when(CenterfactoryRemoteService.getPsTaskListFromCenterFactory(Mockito.anyString()))
                .thenReturn(psTasks)
        ;
        technicalHeadServiceImpl.retentionSn(query);
        p1.setFactoryId(new BigDecimal("52"));
        PowerMockito.when(CenterfactoryRemoteService.getPsTaskListFromCenterFactory(Mockito.anyString()))
                .thenReturn(psTasks);
        technicalHeadServiceImpl.retentionSn(query);

        List<TechnicalChangeDetailDTO> technicalList = new LinkedList<>();
        TechnicalChangeDetailDTO c1 = new TechnicalChangeDetailDTO();
        c1.setChgReqNo("889973000001");
        technicalList.add(c1);
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalDetail(Mockito.any()))
                .thenReturn(technicalList);
        PowerMockito.when(technicalChangeExecInfoRepository.select(Mockito.any()))
                .thenReturn(null);

        Assert.assertNotNull(technicalHeadServiceImpl.retentionSn(query));
    }

    @Test
    public void insertLockForRouteChange() {
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(new OnlineRouteChangeHistoryDTO());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        List<String> processCodes = new ArrayList<>();
        processCodes.add("test123");
        OnlineRouteChangeHistoryDTO onlineChangeHistoryDTO = new OnlineRouteChangeHistoryDTO();
        onlineChangeHistoryDTO.setProdplanId("test123");
        onlineChangeHistoryDTO.setCreateBy("00286523");
        onlineChangeHistoryDTO.setAddProcessList(processCodes);
        onlineChangeHistoryDTO.setAddSectionList(processCodes);
        List<BarcodeLockDetail> barcodeLockDetails = new ArrayList<>();
        PowerMockito.when(barcodeLockDetailRepository.getLockInfoSnByPlanAndStatus(Mockito.any())).thenReturn(barcodeLockDetails);
        PowerMockito.when(barcodeLockDetailRepository.batchInsertForRouteChange(Mockito.any())).thenReturn(1);
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(onlineChangeHistoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        BarcodeLockDetail barcodeLockDetail = new BarcodeLockDetail();
        barcodeLockDetail.setBillNo("test123");
        barcodeLockDetails.add(barcodeLockDetail);
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(onlineChangeHistoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList = new ArrayList<>();
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalInfoByProdPlanId(Mockito.any())).thenReturn(technicalChangeHeadDTOList);
        PowerMockito.when(technicalChangeHeadRepository.batchUpdateForRouteChange(Mockito.any())).thenReturn(1);
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(onlineChangeHistoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setCraftSection("test123");
        technicalChangeHeadDTO.setCreatedBy("00286523");
        List<TechnicalChangeDetailDTO> details = new ArrayList<>();
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        details.add(technicalChangeDetailDTO);
        technicalChangeHeadDTO.setDetails(details);
        technicalChangeHeadDTOList.add(technicalChangeHeadDTO);
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(onlineChangeHistoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        technicalChangeDetailDTO.setCraftSection("test");
        try {
            technicalHeadServiceImpl.insertLockForRouteChange(onlineChangeHistoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    /* Started by AICoder, pid:zec38qbccan98441461e0ba73038276a72a6a34b */
    @Test
    public void testSetMBom()throws Exception {
        // 创建并初始化PsEntityPlanBasic对象
        TechnicalChangeDetailDTO dto1 = new TechnicalChangeDetailDTO();
        dto1.setProductCode("11");
        dto1.setProdplanId("prodplanId_2");

        TechnicalChangeDetailDTO dto2 = new TechnicalChangeDetailDTO();
        dto2.setProductCode("11");
        dto2.setProdplanId("prodplanId_3");

        List<TechnicalChangeDetailDTO> listEntity = new ArrayList<>();
        listEntity.add(dto1);
        listEntity.add(dto2);

        // 模拟远程服务调用返回null的情况
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(null);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setMBom", listEntity);
        assertNotNull(listEntity);

        // 创建并初始化BProdBomHeaderDTO对象
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo1");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_1");
        }});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo2");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_2");
        }});

        // 模拟远程服务调用返回有效数据的情况
        when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setMBom", listEntity);
        assertNotNull(listEntity);
    }

    /* Ended by AICoder, pid:zec38qbccan98441461e0ba73038276a72a6a34b */
}
