package com.zte.application.impl.technical;

import com.zte.application.BSmtBomHeaderService;
import com.zte.application.impl.StItemBarcodeServiceImpl;
import com.zte.application.impl.TaskMaterialIssueSeqServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.TaskMaterialIssueSeqRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.domain.model.technical.TechnicalChangeSkipInfo;
import com.zte.domain.model.technical.TechnicalChangeSkipInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.TechnicalChangeSkipInfoDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeHeadDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanscheduleRemoteService.class})
public class TechnicalChangeSkipInfoServiceImplTest {
    @InjectMocks
    TechnicalChangeSkipInfoServiceImpl service;
    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeSkipInfoRepository technicalChangeSkipInfoRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void pageList() throws Exception {
        TechnicalChangeSkipInfoDTO record = new TechnicalChangeSkipInfoDTO();
        record.setChgReqNo("test123");
        record.setProdplanId("test123");
        record.setStartTime("2022-11-08 00:00:00");
        record.setPage(1);
        record.setRows(10);
        List<TechnicalChangeSkipInfo> resultList = new ArrayList<>();
        TechnicalChangeSkipInfo result = new TechnicalChangeSkipInfo();
        result.setChgReqNo("test123");
        result.setProdplanId("test123");
        result.setCreateBy("00286523");
        result.setLastUpdatedBy("00286523");
        result.setQcConfirmBy("00286523");
        result.setStatus(0);
        TechnicalChangeSkipInfo result1 = new TechnicalChangeSkipInfo();
        result1.setChgReqNo("test123");
        result1.setProdplanId("test123");
        result1.setCreateBy("00286523");
        result1.setLastUpdatedBy("00286523");
        result1.setQcConfirmBy("00286523");
        result1.setStatus(1);
        resultList.add(result1);
        TechnicalChangeSkipInfo result2 = new TechnicalChangeSkipInfo();
        result2.setChgReqNo("test123");
        result2.setProdplanId("test123");
        result2.setCreateBy("00286523");
        result2.setLastUpdatedBy("00286523");
        result2.setQcConfirmBy("00286523");
        result2.setStatus(2);
        resultList.add(result2);
        PowerMockito.when(technicalChangeSkipInfoRepository.pageList(Mockito.any())).thenReturn(resultList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO =new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("HL");
        hrmPersonInfo.put("00286523",hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfo);
        Assert.assertNotNull(service.pageList(record));
    }

    @Test
    public void getList() throws Exception {
        TechnicalChangeSkipInfoDTO record = new TechnicalChangeSkipInfoDTO();
        record.setChgReqNo("test123");
        record.setProdplanId("test123");
        record.setStartTime("2022-11-08 00:00:00");
        record.setPage(1);
        record.setRows(10);
        List<TechnicalChangeSkipInfo> resultList = new ArrayList<>();
        TechnicalChangeSkipInfo result = new TechnicalChangeSkipInfo();
        result.setChgReqNo("test123");
        result.setProdplanId("test123");
        result.setCreateBy("00286523");
        result.setLastUpdatedBy("00286523");
        result.setQcConfirmBy("00286523");
        result.setStatus(0);
        TechnicalChangeSkipInfo result1 = new TechnicalChangeSkipInfo();
        result1.setChgReqNo("test123");
        result1.setProdplanId("test123");
        result1.setCreateBy("00286523");
        result1.setLastUpdatedBy("00286523");
        result1.setQcConfirmBy("00286523");
        result1.setStatus(1);
        resultList.add(result1);
        TechnicalChangeSkipInfo result2 = new TechnicalChangeSkipInfo();
        result2.setChgReqNo("test123");
        result2.setProdplanId("test123");
        result2.setCreateBy("00286523");
        result2.setLastUpdatedBy("00286523");
        result2.setQcConfirmBy("00286523");
        result2.setStatus(2);
        resultList.add(result2);
        PowerMockito.when(technicalChangeSkipInfoRepository.getList(Mockito.any())).thenReturn(resultList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO =new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("HL");
        hrmPersonInfo.put("00286523",hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfo);
        Assert.assertNotNull(service.getList(record));
    }

    @Test
    public void insert() throws Exception{
        TechnicalChangeSkipInfoDTO record = new TechnicalChangeSkipInfoDTO();
        record.setChgReqNo("test123");
        record.setProdplanId("test123");
        record.setSkipReason("测试");
        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setChgReqNo("test123");
        PsTask psTask=new PsTask();
        psTask.setProdplanId("test123");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoOrPlanId(Mockito.anyString())).thenReturn(psTask);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(Mockito.anyString())).thenReturn(technicalChangeHeadDTO);
        PowerMockito.when(technicalChangeSkipInfoRepository.getList(Mockito.any())).thenReturn(null);
        PowerMockito.when(technicalChangeSkipInfoRepository.insert(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.insert(record,"00286523"));
    }

    @Test
    public void batchUpdate() throws Exception{
        TechnicalChangeSkipInfoDTO record = new TechnicalChangeSkipInfoDTO();
        record.setChgReqNo("test123");
        record.setProdplanId("test123");
        record.setSkipReason("测试");
        record.setId("0000");
        List<TechnicalChangeSkipInfoDTO> paramList = new ArrayList<>();
        paramList.add(record);
        List<TechnicalChangeSkipInfo> resultList = new ArrayList<>();
        TechnicalChangeSkipInfo result = new TechnicalChangeSkipInfo();
        result.setChgReqNo("test123");
        result.setProdplanId("test123");
        result.setCreateBy("00286523");
        result.setLastUpdatedBy("00286523");
        result.setQcConfirmBy("00286523");
        result.setStatus(0);
        result.setId("0000");
        resultList.add(result);
        PowerMockito.when(technicalChangeSkipInfoRepository.getList(Mockito.any())).thenReturn(resultList);
        PowerMockito.when(technicalChangeSkipInfoRepository.batchUpdate(Mockito.any())).thenReturn(1);
        service.batchUpdate(paramList,"00286523");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}