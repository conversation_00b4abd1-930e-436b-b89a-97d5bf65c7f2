package com.zte.application.impl.technical;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.application.CommonTechnicalService;
import com.zte.application.DocFilePropertiesService;
import com.zte.application.IMESLogService;
import com.zte.application.impl.BarcodeLockDetailServiceImpl;
import com.zte.application.technical.TechnicalHeadService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.technical.TechnicalChangeDetailHisRepository;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeExecInfo;
import com.zte.domain.model.technical.TechnicalChangeExecInfoRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.FilePreViewRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.DocFilePropertiesEntityDTO;
import com.zte.interfaces.dto.PdmTechnicalChangeInfoEntityDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SynchronizeSpmDateDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TechChgTaskDTO;
import com.zte.interfaces.dto.TechnicalChangeDetailEditDTO;
import com.zte.interfaces.dto.TechnicalChangeInfoEditDTO;
import com.zte.interfaces.dto.WipCraftQtyDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeHeadDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.cxf.transport.commons_text.StringEscapeUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Matchers.*;

@PrepareForTest({CenterfactoryRemoteService.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,MicroServiceRestUtil.class,
        CrafttechRemoteService.class, RedisHelper.class,DatawbRemoteService.class,MESHttpHelper.class, HttpClientUtil.class, StringEscapeUtils.class})
public class TechnicalChangeServiceImplTest extends PowerBaseTestCase {
    
    @InjectMocks
    TechnicalChangeServiceImpl service;

    @Mock
    CloudDiskHelper cloudDiskHelper;

    @Mock
    FilePreViewRemoteService filePreViewService;

    @Mock
    DocFilePropertiesService docService;

    @Mock
    TechnicalChangeHeadRepository headRepository;

    @Mock
    TechnicalChangeDetailRepository detailRepository;

    @Mock
    BarcodeLockDetailServiceImpl barcodeLockDetailService;

    @Mock
    PsWipInfoRepository wipInfoRepository;

    @Mock
    EmailUtils emailUtils;

    @Mock
    TechnicalHeadService technicalHeadService;

    @Mock
    TechnicalChangeDetailHisRepository detailHisRepository;

    @Mock
    CommonTechnicalService commonTechnicalService;
    @Mock
    private IMESLogService imesLogService;

    @Mock
    private TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;

    @Test
    public void getPartTitle()throws Exception{
        TechnicalChangeInfoEditDTO dto = new TechnicalChangeInfoEditDTO();

        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setChgReqNo("chg1");
        technicalChangeDetailDTO.setProdplanId("213123");
        technicalChangeDetailDTO.setTechnicalStatus("3");
        detailList.add(technicalChangeDetailDTO);
        PowerMockito.when(detailRepository.getListByBillNo(anyString())).thenReturn(detailList);

        Whitebox.invokeMethod(service,"getPartTitle",dto);
        for (int i = 0; i < 8; i++) {
            TechnicalChangeDetailDTO detailDTO = new TechnicalChangeDetailDTO();
            detailDTO.setChgReqNo("chg1");
            detailDTO.setProdplanId("213123");
            detailDTO.setTechnicalStatus("3");
            detailList.add(detailDTO);
        }
        PowerMockito.when(detailRepository.getListByBillNo(anyString())).thenReturn(detailList);
        Whitebox.invokeMethod(service,"getPartTitle",dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void handleCraftSection()throws Exception{
        SynchronizeSpmDateDTO entity=new SynchronizeSpmDateDTO();
        Map<String, Integer> craftSeqMap=new HashMap<>();
        Map<String, List<String>> spmCraftToImesCraftMap=new HashMap<>();
        Whitebox.invokeMethod(service,"handleCraftSection",
                entity,craftSeqMap,spmCraftToImesCraftMap);
        List<String> spmList=new ArrayList<>();
        spmList.add("123");
        spmCraftToImesCraftMap.put("123",spmList);
        try {
            Whitebox.invokeMethod(service,"handleCraftSection",
                    entity,craftSeqMap,spmCraftToImesCraftMap);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void uploadFile() {
        try {
            Assert.assertNotNull(service.uploadFile("", "", "", null));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FAIL_TO_UPLOAD_FILE, e.getMessage());
        }
    }

    @Test
    public void checkFileTypeTest() {
        MultipartFile file = null;
        service.checkFileType(file);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        file = new MultipartFile() {
            @Override
            public String getName() {
                return null;
            }

            @Override
            public String getOriginalFilename() {
                return "dsdasdasd.png";
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return null;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
        service.checkFileType(file);
        file = new MultipartFile() {
            @Override
            public String getName() {
                return null;
            }

            @Override
            public String getOriginalFilename() {
                return "dsdasdasd.jsp";
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return null;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
        try {
            service.checkFileType(file);
        } catch (MesBusinessException e) {
            MessageId.FILE_TYPE_ILLEGAL.equals(e.getExMsgId());
        }
    }

    @Test
    public void viewFile() {
        try {
            Assert.assertNull(service.viewFile("", "", ""));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void viewFileEm() throws Exception{
        PowerMockito.mockStatic(StringEscapeUtils.class);
        service.viewFileEmpNo("", "", "");
        Assert.assertNotNull(service.filePreviewDME("", ""));
    }

    @Test
    public void downloadFile() {
        try {
            Assert.assertNull(service.downloadFile("", "", ""));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void submit() {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class);
        try {
            PowerMockito.when(CenterfactoryRemoteService.getPDMTechChgInfoByNo(any())).thenReturn(
                    Lists.newArrayList(new PdmTechnicalChangeInfoEntityDTO(){{setProdplanId("1");setSendStatus("1");}},
                            new PdmTechnicalChangeInfoEntityDTO(){{setProdplanId("2");setSendStatus("1");}})
            );
            PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(
                    Lists.newArrayList(new PsTask(){{setProdplanId("2");setTaskStatus("已完工");}},new PsTask(){{setProdplanId("1");setTaskStatus("");}}));
            PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(Lists.newArrayList(new BarcodeLockDetailEntityDTO()));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            service.preSave(new TechnicalChangeInfoEditDTO(){{
                setChgReqNo("1");
                setCreatedBy("1");
                setChangeType("1");
                setPreFileId("1");
                setTechnicalStatus("2");
                setEmailSend("1");
                setEmailCopy("1");
                setDetailList(Lists.newArrayList(new TechnicalChangeDetailEditDTO(){{setDetailId("1");setProdplanId("1");setControlCraft(Lists.newArrayList("1"));}}));
            }});
            service.saveAsDraft(new TechnicalChangeInfoEditDTO(){{
                setChgReqNo("1");
                setCreatedBy("1");
                setChangeType("1");
                setPreFileId("1");
                setTechnicalStatus("2");
                setEmailSend("1");
                setEmailCopy("1");
                setDetailList(Lists.newArrayList(new TechnicalChangeDetailEditDTO(){{setDetailId("1");setProdplanId("1");setControlCraft(Lists.newArrayList("1"));}}));
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        PowerMockito.when(headRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(new TechnicalChangeHeadDTO(){{setTechnicalStatus("2");}});
        try {
            service.submit(new TechnicalChangeInfoEditDTO(){{
                setChgReqNo("1");
                setCreatedBy("1");
                setChangeType("0");
                setTechnicalStatus("2");
                setEmailSend("1");
                setEmailCopy("1");
                setPreFileId("3");
                setDetailList(Lists.newArrayList(new TechnicalChangeDetailEditDTO(){{setDetailId("1");setProdplanId("1");setControlCraft(Lists.newArrayList("1"));}}));
                setFileList(Lists.newArrayList(new DocFilePropertiesEntityDTO(){{setDocType("0");setDocId("1");}}));
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void getTechChgInfoByNo() throws Exception {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class);
        try {
            PowerMockito.when(headRepository.getTechnicalChangeHeadDTOByChgReqNo("1")).thenReturn(new TechnicalChangeHeadDTO(){{
                setEmailSend("1");setEmailCopy("2");
            }});
            PowerMockito.when(detailRepository.getUnCompleteByBillNo("1"))
                    .thenReturn(Lists.newArrayList(
                            new TechnicalChangeDetailDTO(){{setProdplanId("1");setCompletionStatus("Y");}},
                            new TechnicalChangeDetailDTO(){{setProdplanId("2");setCraftSection("1,2");setCompletionStatus("N");}},
                            new TechnicalChangeDetailDTO(){{setProdplanId("4");setCompletionStatus("N");}}));
            PowerMockito.when(CenterfactoryRemoteService.getPDMTechChgInfoByNo(any())).thenReturn(
                    Lists.newArrayList(new PdmTechnicalChangeInfoEntityDTO(){{setProdplanId("1");setSendStatus("1");}})
            );
            PowerMockito.when(PlanscheduleRemoteService.getTopTaskRecForTechChg(any(), any())).thenReturn(Lists.newArrayList(
                    new TechChgTaskDTO(){{setProdplanId("1");setOriginPlanId("1");setTaskQty(BigDecimal.TEN);setTaskStatus("已完工");}},
                    new TechChgTaskDTO(){{setProdplanId("3");setOriginPlanId("1");setTaskQty(BigDecimal.TEN);}},
                    new TechChgTaskDTO(){{setProdplanId("2");setOriginPlanId("2");setTaskQty(BigDecimal.ONE);setTaskStatus("2");}}
            ));
            PowerMockito.when(wipInfoRepository.getWipCraftQty(any(), any())).thenReturn(Lists.newArrayList(
                    new WipCraftQtyDTO(){{setProdPlanId("1");setQty(5);}},
                    new WipCraftQtyDTO(){{setProdPlanId("2");setQty(1);}}
            ));
            PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(new HashMap(){{
                put("1", Lists.newArrayList(new CtRouteDetailDTO(){{setCraftSection("DIP");}},
                        new CtRouteDetailDTO(){{setCraftSection("SMT-A");}},
                        new CtRouteDetailDTO(){{setCraftSection("SMT-B");}}));
                put("2", Lists.newArrayList(new CtRouteDetailDTO(){{setCraftSection("SMT-A");}}));
            }});
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(
                    Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");}})
            );
            PowerMockito.when(docService.getList(any())).thenReturn(Lists.newArrayList(
                    new DocFilePropertiesEntityDTO(){{setDocType("0");setDocSort(1);setDocId("1");}},
                    new DocFilePropertiesEntityDTO(){{setDocType("1");setDocSort(1);setDocId("2");}},
                    new DocFilePropertiesEntityDTO(){{setDocType("2");setDocSort(2);setDocId("3");}}
            ));
            service.getTechChgInfoByNo("1");
            service.getTechChgInfoByNo("2");
            service.getTechChgInfoByNo("3");
            service.getCraftByProdPlanId(new TechnicalChangeDetailEditDTO(){{setProdplanId("1");
                setMainCraft(Arrays.asList("SMT-A","SMT-B","DIP"));}});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PDM_TECH_CHG_INFO, e.getMessage());
        }
        try {
            PowerMockito.when(headRepository.getTechnicalChangeHeadDTOByChgReqNo(any())).thenReturn(new TechnicalChangeHeadDTO());
            PowerMockito.when(detailRepository.getListByBillNo("1"))
                    .thenReturn(Lists.newArrayList(new TechnicalChangeDetailDTO(){{setProdplanId("1");}},
                            new TechnicalChangeDetailDTO(){{setProdplanId("2");setCraftSection("1,2");}}));
            service.getTechChgInfoByNo("1");
            service.getTechChgInfoByNo("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PDM_TECH_CHG_INFO, e.getMessage());
        }
        try {
            PowerMockito.when(headRepository.getTechnicalChangeHeadDTOByChgReqNo("1")).thenReturn(new TechnicalChangeHeadDTO());
            PowerMockito.when(detailRepository.getListByBillNo("1"))
                    .thenReturn(Lists.newArrayList(new TechnicalChangeDetailDTO(),
                            new TechnicalChangeDetailDTO(){{setCraftSection("1,2");}}));
            service.getTechChgInfoByNo("1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PDM_TECH_CHG_INFO, e.getMessage());
        }
        PowerMockito.when(PlanscheduleRemoteService.getTopTaskRecForTechChg(any(), any())).thenReturn(Lists.newArrayList(
                new TechChgTaskDTO(){{setProdplanId("1");setOriginPlanId("1");setTaskQty(BigDecimal.TEN);setTaskStatus("已完工");}}
        ));
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(CenterfactoryRemoteService.getPDMTechChgInfoByNo(any(),anyString())).thenReturn(
                Lists.newArrayList(new PdmTechnicalChangeInfoEntityDTO(){{setProdplanId("1");setSendStatus("1");}})
        );
        Assert.assertNotNull(service.getTechChgInfoByNo("1"));

    }

    @Test
    public void getDownloadUrl() throws Exception {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,
                CrafttechRemoteService.class);
        PowerMockito.when(headRepository.queryTechnicalHeadByPage(any())).thenReturn(Lists.newArrayList(new TechnicalChangeHeadDTO() {{setTechnicalStatus("1");}}));
        PowerMockito.when(docService.getList(any())).thenReturn(Lists.newArrayList(new DocFilePropertiesEntityDTO()));
        service.getTechChgFileDownloadUrl("1", "1", "1", "2", "1,2");
        try {
            service.getTechChgFileDownloadUrl("", "1", "1", "2", "1,2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FACTORY_ID_IS_NULL, e.getMessage());
        }
        try {
            service.getTechChgFileDownloadUrl("1", "", "1", "2", "1,2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_ID_NULL, e.getMessage());
        }
        try {
            service.getTechChgFileDownloadUrl("1", "1", "", "2", "1,2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        String factoryId = "52";
        String prodPlanId = "1231232";
        String craftSection = "test";
        String issuanceCraftSection = "test";
        String status = "报废,已解锁";
        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupMeaning("1");
        dto.setDescriptionChin("已解锁");
        batchSysValueByCode.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(anyList())).thenReturn(batchSysValueByCode);
        Assert.assertNotNull(service.getTechChgFileDownloadUrl(factoryId, prodPlanId, craftSection, issuanceCraftSection, status));
    }

    @Test
    public void synchronizeSpmDateTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setChgRegNo("test1");
        synchronizeSpmDateDTO1.setProdplanId(123L);
        synchronizeSpmDateDTO1.setSmtTc(1);
        synchronizeSpmDateDTO1.setTestTc(1);
        synchronizeSpmDateDTO1.setPowerTc(1);
        synchronizeSpmDateDTO1.setZhTc(1);
        synchDTOList.add(synchronizeSpmDateDTO1);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test2");
        synchronizeSpmDateDTO2.setProdplanId(1234L);
        synchronizeSpmDateDTO2.setSmtTc(1);
        synchronizeSpmDateDTO2.setTestTc(1);
        synchronizeSpmDateDTO2.setPowerTc(1);
        synchronizeSpmDateDTO2.setZhTc(1);
        synchronizeSpmDateDTO2.setLastUpdateBy("陈俊熙123456");
        synchDTOList.add(synchronizeSpmDateDTO2);
        List<TechnicalChangeDetailDTO> techChangeDetList = new ArrayList<>();
        TechnicalChangeDetailDTO dto1 = new TechnicalChangeDetailDTO();
        dto1.setChgReqNo("test1");
        dto1.setProdplanId("123");
        techChangeDetList.add(dto1);
        List<TechnicalChangeHeadDTO> techChangeHedList = new ArrayList<>();
        TechnicalChangeHeadDTO TechnicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        TechnicalChangeHeadDTO.setChgReqNo("test1");
        techChangeHedList.add(TechnicalChangeHeadDTO);
        PowerMockito.when(headRepository.getListByChgNoSet(anySet())).thenReturn(techChangeHedList);
        PowerMockito.when(detailRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techChangeDetList);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("123");
        psTask1.setItemNo("itemNo1");
        psTask1.setTaskStatus(null);
        psTaskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("1234");
        psTask2.setItemNo("itemNo2");
        psTask2.setTaskStatus("已排产");
        psTaskList.add(psTask2);
        PowerMockito.when(CenterfactoryRemoteService.selectPsTaskByProdIdSet(anySet())).thenReturn(psTaskList);

        List<CtBasicRouteDTO> routeInfoList = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("itemNo1");
        ctBasicRouteDTO1.setCraftRoutePath("SMT-B+SMT-A+ICT+Test+DIP+清洗+电源模块+定制电源");
        routeInfoList.add(ctBasicRouteDTO1);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByItemNos(anyList())).thenReturn(routeInfoList);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic workOrderBasic = new PsWorkOrderBasic();
        workOrderBasic.setSourceTask("1234");
        workOrderBasic.setRouteId("route1");
        workOrderList.add(workOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdDistinctByProdIdList(anyList())).thenReturn(workOrderList);

        List<CtBasicRouteDTO> routeInfoList1 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO2 = new CtBasicRouteDTO();
        ctBasicRouteDTO2.setCraftRoutePath("定制电源+电源模块+ICT+Test+SMT-B+清洗+DIP");
        ctBasicRouteDTO2.setRouteId("route1");
        routeInfoList1.add(ctBasicRouteDTO2);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(anyList())).thenReturn(routeInfoList1);
        service.synchronizeSpmDate(synchDTOList);

        ctBasicRouteDTO2.setCraftRoutePath("定制电源1+电源模块1+ICT1+Test1+SMT-B1+清洗1+DIP1");
        service.synchronizeSpmDate(synchDTOList);
        ctBasicRouteDTO2.setCraftRoutePath("");
        service.synchronizeSpmDate(synchDTOList);
        ctBasicRouteDTO2.setRouteId("route1111");
        Assert.assertNotNull(service.synchronizeSpmDate(synchDTOList));
    }

    @Test
    public void synchronizeSnSpmDataTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        service.synchronizeSnSpmData(synchDTOList);
        SynchronizeSpmDateDTO dataDto1 = new SynchronizeSpmDateDTO();
        dataDto1.setProdplanId(123L);
        dataDto1.setChgRegNo("test1");
        dataDto1.setItemBarcode("111");
        dataDto1.setLastUpdateBy("陈俊熙10307315");
        dataDto1.setLastUpdateDate(new Date());
        dataDto1.setScanDate(new Date());
        dataDto1.setBarChangeMemo("memo1");
        synchDTOList.add(dataDto1);

        SynchronizeSpmDateDTO dataDto2 = new SynchronizeSpmDateDTO();
        dataDto2.setProdplanId(1234L);
        dataDto2.setChgRegNo("test2");
        dataDto2.setItemBarcode("222");
        dataDto2.setLastUpdateBy("陈俊熙10307315");
        dataDto2.setLastUpdateDate(new Date());
        dataDto2.setScanDate(new Date());
        dataDto2.setBarChangeMemo("memo2");
        synchDTOList.add(dataDto2);

        List<TechnicalChangeExecInfo> techExecInfoList = new ArrayList<>();
        TechnicalChangeExecInfo techExecInfo1 = new TechnicalChangeExecInfo();
        techExecInfo1.setProdplanId("1234");
        techExecInfo1.setChgReqNo("test2");
        techExecInfo1.setSn("222");
        techExecInfoList.add(techExecInfo1);
        PowerMockito.when(technicalChangeExecInfoRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techExecInfoList);
        service.synchronizeSnSpmData(synchDTOList);

        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList<>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("222");
        psWipInfoDTO.setCurrProcessCode("2");
        psWipInfoDTOList.add(psWipInfoDTO);
        PsWipInfoDTO psWipInfoDTO1 = new PsWipInfoDTO();
        psWipInfoDTO1.setSn("111");
        psWipInfoDTO1.setCurrProcessCode("1");
        psWipInfoDTOList.add(psWipInfoDTO1);
        PowerMockito.when(wipInfoRepository.getListByBatchSnList(anyList())).thenReturn(psWipInfoDTOList);
        Assert.assertNotNull(service.synchronizeSnSpmData(synchDTOList));
    }

    @Test
    public void deleteSynchronizeSnSpmDataTest() {
        service.deleteSynchronizeSnSpmData();
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void findBeforeInBoundTest() throws Exception {
        Map<String, Integer> craftSeqMap = new HashMap<>();
        craftSeqMap.put("test", 1);
        Whitebox.invokeMethod(service, "findBeforeInBound", craftSeqMap);
        craftSeqMap.put("入库", 2);
        Whitebox.invokeMethod(service, "findBeforeInBound", craftSeqMap);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getProdIdToCraftSeqMapTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("test1");
        psTask1.setItemNo("itemNo1");
        psTaskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("test2");
        psTask2.setItemNo("itemNo2");
        psTaskList.add(psTask2);
        PsTask psTask3 = new PsTask();
        psTask3.setProdplanId("test3");
        psTask3.setItemNo("itemNo3");
        psTask3.setPartsPlanno("test");
        psTaskList.add(psTask3);
        PowerMockito.when(CenterfactoryRemoteService.selectPsTaskByProdIdSet(anySet())).thenReturn(psTaskList);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setItemNo("itemNo1");
        psWorkOrderBasic.setSourceTask("test1");
        psWorkOrderBasic.setRouteId("routID1");
        workOrderList.add(psWorkOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdDistinctByProdIdList(anyList())).thenReturn(workOrderList);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<CtBasicRouteDTO> routeInfoList = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO = new CtBasicRouteDTO();
        ctBasicRouteDTO.setRouteId("routID1");
        ctBasicRouteDTO.setCraftRoutePath("SMT-A+SMT-B");
        routeInfoList.add(ctBasicRouteDTO);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(anyList())).thenReturn(routeInfoList);

        List<CtBasicRouteDTO> routeInfoList1 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("itemNo2");
        ctBasicRouteDTO1.setCraftRoutePath("SMT-A+SMT-B+DIP");
        routeInfoList1.add(ctBasicRouteDTO1);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByItemNos(anyList())).thenReturn(routeInfoList1);
        List<CtBasicRouteDTO> routeInfoList2 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO2 = new CtBasicRouteDTO();
        ctBasicRouteDTO2.setItemNo("itemNo3");
        ctBasicRouteDTO2.setCraftRoutePath("SMT-A+SMT-B+DIP+ICT");
        routeInfoList2.add(ctBasicRouteDTO2);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByItemNosChild(anyList())).thenReturn(routeInfoList2);
        Whitebox.invokeMethod(service, "getProdIdToCraftSeqMap", synchDTOList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkTaskNotSpecialHandler() throws Exception {
        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", null);
        PsTask psTask = new PsTask();
        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", psTask);

        psTask.setTaskNo("111");
        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", psTask);

        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", psTask);

        psTask.setTaskNo("111-w");
        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", psTask);

        psTask.setTaskStatus("1a");
        Whitebox.invokeMethod(service, "checkTaskNotSpecialHandler", psTask);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void scheduleTaskUpdateTechDetailStatusTest () throws Exception {
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(false);
        try {
            service.scheduleTaskUpdateTechDetailStatus(synchronizeSpmDateDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.FAILED_TO_GET_REDIS_LOCK));
        }
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(new SysLookupTypesDTO(){{
            setLookupMeaning("1996-11-11 11:11:11");
        }});
        synchronizeSpmDateDTO.setLastSynchronizeTime("1996-11-11 11:11:11");
        synchronizeSpmDateDTO.setEndSynchronizeTime("1996-11-11 11:11:11");
        PowerMockito.when(technicalHeadService.pageSelectNotFinishTechSPMDate(any(),anyInt(),anyInt())).thenReturn(new ArrayList<>());
        service.scheduleTaskUpdateTechDetailStatus(synchronizeSpmDateDTO);
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setLastUpdatedDate(new Date());
        technicalChangeDetailDTO.setChgReqNo("test1");
        technicalChangeDetailDTO.setProdplanId("111");
        TechnicalChangeDetailDTO technicalChangeDetailDTO1 = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO1.setLastUpdatedDate(new Date());
        technicalChangeDetailDTO1.setChgReqNo("test2");
        technicalChangeDetailDTO1.setProdplanId("1111");
        List<TechnicalChangeDetailDTO> technicalChangeExecInfoList = new ArrayList<TechnicalChangeDetailDTO>(){{add(technicalChangeDetailDTO); add(technicalChangeDetailDTO1);}};
        PowerMockito.when(technicalHeadService.pageSelectNotFinishTechSPMDate(any(),anyInt(),anyInt())).thenReturn(technicalChangeExecInfoList);
        service.scheduleTaskUpdateTechDetailStatus(synchronizeSpmDateDTO);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setChgRegNo("test1");
        synchronizeSpmDateDTO1.setProdplanId(111L);
        resultList.add(synchronizeSpmDateDTO1);
        PowerMockito.when(DatawbRemoteService.selectUnFinishTechSPMData(anyList())).thenReturn(resultList);
        service.scheduleTaskUpdateTechDetailStatus(synchronizeSpmDateDTO);
        synchronizeSpmDateDTO.setEndSynchronizeTime("");
        service.scheduleTaskUpdateTechDetailStatus(synchronizeSpmDateDTO);
    }

    @Test
    public void sendEmailMsgTest() throws  Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesList);
        Whitebox.invokeMethod(service, "sendEmailMsg", "","");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("10307315");
        sysLookupTypesList.add(sysLookupTypesDTO);
        Whitebox.invokeMethod(service, "sendEmailMsg", "","");
        sysLookupTypesDTO.setAttribute1("email");
        Whitebox.invokeMethod(service, "sendEmailMsg", "","");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void synchronizeLastDayFinishTechTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO  = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("chg1");
        synchronizeSpmDateDTO.setProdplanId(123L);
        synchDTOList.add(synchronizeSpmDateDTO);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO1  = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setChgRegNo("chg2");
        synchronizeSpmDateDTO1.setProdplanId(123L);
        synchDTOList.add(synchronizeSpmDateDTO1);

        List<TechnicalChangeDetailDTO> techChangeDetList = new ArrayList<>();
        TechnicalChangeDetailDTO dto1 = new TechnicalChangeDetailDTO();
        dto1.setChgReqNo("chg1");
        dto1.setProdplanId("123");
        techChangeDetList.add(dto1);
        PowerMockito.when(detailRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techChangeDetList);


        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setChgReqNo("chg1");
        technicalChangeDetailDTO.setProdplanId("213123");
        technicalChangeDetailDTO.setTechnicalStatus("3");
        detailList.add(technicalChangeDetailDTO);
        PowerMockito.when(detailRepository.getListByBillNo(anyString())).thenReturn(detailList);
        service.synchronizeLastDayFinishTech(synchDTOList);


        TechnicalChangeDetailDTO technicalChangeDetailDTO1 = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO1.setChgReqNo("chg1");
        technicalChangeDetailDTO1.setProdplanId("213123");
        technicalChangeDetailDTO1.setTechnicalStatus("2");
        detailList.add(technicalChangeDetailDTO1);
        Assert.assertNotNull(service.synchronizeLastDayFinishTech(synchDTOList));
    }

    @Test
    public void synchronizeFinishTechSPMDataTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setChgRegNo("test1");
        synchronizeSpmDateDTO1.setProdplanId(123L);
        synchronizeSpmDateDTO1.setSmtTc(1);
        synchronizeSpmDateDTO1.setTestTc(1);
        synchronizeSpmDateDTO1.setPowerTc(1);
        synchronizeSpmDateDTO1.setZhTc(1);
        synchDTOList.add(synchronizeSpmDateDTO1);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test2");
        synchronizeSpmDateDTO2.setProdplanId(1234L);
        synchronizeSpmDateDTO2.setSmtTc(1);
        synchronizeSpmDateDTO2.setTestTc(1);
        synchronizeSpmDateDTO2.setPowerTc(1);
        synchronizeSpmDateDTO2.setZhTc(1);
        synchronizeSpmDateDTO2.setLastUpdateBy("陈俊熙123456");
        synchDTOList.add(synchronizeSpmDateDTO2);
        List<TechnicalChangeDetailDTO> techChangeDetList = new ArrayList<>();
        TechnicalChangeDetailDTO dto1 = new TechnicalChangeDetailDTO();
        dto1.setChgReqNo("test1");
        dto1.setProdplanId("123");
        techChangeDetList.add(dto1);
        List<TechnicalChangeHeadDTO> techChangeHedList = new ArrayList<>();
        TechnicalChangeHeadDTO TechnicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        TechnicalChangeHeadDTO.setChgReqNo("test1");
        techChangeHedList.add(TechnicalChangeHeadDTO);
        PowerMockito.when(headRepository.getListByChgNoSet(anySet())).thenReturn(techChangeHedList);
        PowerMockito.when(detailRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techChangeDetList);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("123");
        psTask1.setItemNo("itemNo1");
        psTask1.setTaskStatus(null);
        psTaskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("1234");
        psTask2.setItemNo("itemNo2");
        psTask2.setTaskStatus("已排产");
        psTaskList.add(psTask2);
        PowerMockito.when(CenterfactoryRemoteService.selectPsTaskByProdIdSet(anySet())).thenReturn(psTaskList);

        List<CtBasicRouteDTO> routeInfoList = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("itemNo1");
        ctBasicRouteDTO1.setCraftRoutePath("SMT-B+SMT-A+ICT+Test+DIP+清洗+电源模块+定制电源");
        routeInfoList.add(ctBasicRouteDTO1);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByItemNos(anyList())).thenReturn(routeInfoList);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic workOrderBasic = new PsWorkOrderBasic();
        workOrderBasic.setSourceTask("1234");
        workOrderBasic.setRouteId("route1");
        workOrderList.add(workOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdDistinctByProdIdList(anyList())).thenReturn(workOrderList);

        List<CtBasicRouteDTO> routeInfoList1 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO2 = new CtBasicRouteDTO();
        ctBasicRouteDTO2.setCraftRoutePath("定制电源+电源模块+ICT+Test+SMT-B+清洗+DIP");
        ctBasicRouteDTO2.setRouteId("route1");
        routeInfoList1.add(ctBasicRouteDTO2);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(anyList())).thenReturn(routeInfoList1);
        service.synchronizeFinishTechSPMData(synchDTOList);

        ctBasicRouteDTO2.setCraftRoutePath("定制电源1+电源模块1+ICT1+Test1+SMT-B1+清洗1+DIP1");
        service.synchronizeFinishTechSPMData(synchDTOList);
        ctBasicRouteDTO2.setCraftRoutePath("");
        service.synchronizeFinishTechSPMData(synchDTOList);
        ctBasicRouteDTO2.setRouteId("route1111");
        Assert.assertNotNull(service.synchronizeFinishTechSPMData(synchDTOList));
    }


    @Test
    public void specialTechDataSynchronizationTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setChgRegNo("test1");
        synchronizeSpmDateDTO1.setProdplanId(123L);
        synchronizeSpmDateDTO1.setStatus("0");
        synchronizeSpmDateDTO1.setLastUpdateDate(new Date());
        synchronizeSpmDateDTO1.setLastUpdateBy("陈俊熙123456");
        synchDTOList.add(synchronizeSpmDateDTO1);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test2");
        synchronizeSpmDateDTO2.setProdplanId(1234L);
        synchronizeSpmDateDTO2.setStatus("1");
        synchronizeSpmDateDTO2.setLastUpdateBy("陈俊熙123456");
        synchDTOList.add(synchronizeSpmDateDTO2);
        List<TechnicalChangeDetailDTO> techChangeDetList = new ArrayList<>();
        TechnicalChangeDetailDTO dto1 = new TechnicalChangeDetailDTO();
        dto1.setChgReqNo("test1");
        dto1.setProdplanId("123");
        techChangeDetList.add(dto1);
        List<TechnicalChangeHeadDTO> techChangeHedList = new ArrayList<>();
        TechnicalChangeHeadDTO TechnicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        TechnicalChangeHeadDTO.setChgReqNo("test1");
        techChangeHedList.add(TechnicalChangeHeadDTO);
        PowerMockito.when(headRepository.getListByChgNoSet(anySet())).thenReturn(techChangeHedList);
        PowerMockito.when(detailRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techChangeDetList);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("123");
        psTask1.setItemNo("itemNo1");
        psTask1.setTaskStatus(null);
        psTaskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("1234");
        psTask2.setItemNo("itemNo2");
        psTask2.setTaskStatus("已排产");
        psTaskList.add(psTask2);
        PowerMockito.when(CenterfactoryRemoteService.selectPsTaskByProdIdSet(anySet())).thenReturn(psTaskList);

        List<CtBasicRouteDTO> routeInfoList = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("itemNo1");
        ctBasicRouteDTO1.setCraftRoutePath("SMT-B+SMT-A+ICT+Test+DIP+清洗+电源模块+定制电源");
        routeInfoList.add(ctBasicRouteDTO1);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByItemNos(anyList())).thenReturn(routeInfoList);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic workOrderBasic = new PsWorkOrderBasic();
        workOrderBasic.setSourceTask("1234");
        workOrderBasic.setRouteId("route1");
        workOrderList.add(workOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdDistinctByProdIdList(anyList())).thenReturn(workOrderList);

        List<CtBasicRouteDTO> routeInfoList1 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO2 = new CtBasicRouteDTO();
        ctBasicRouteDTO2.setCraftRoutePath("定制电源+电源模块+ICT+Test+SMT-B+清洗+DIP");
        ctBasicRouteDTO2.setRouteId("route1");
        routeInfoList1.add(ctBasicRouteDTO2);
        PowerMockito.when(CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(anyList())).thenReturn(routeInfoList1);
        service.specialTechDataSynchronization(synchDTOList);

        ctBasicRouteDTO2.setCraftRoutePath("定制电源1+电源模块1+ICT1+Test1+SMT-B1+清洗1+DIP1");
        service.specialTechDataSynchronization(synchDTOList);
        ctBasicRouteDTO2.setCraftRoutePath("");
        service.specialTechDataSynchronization(synchDTOList);
        ctBasicRouteDTO2.setRouteId("route1111");
        Assert.assertNotNull(service.specialTechDataSynchronization(synchDTOList));
    }

    @Test
    public void getTechDetListTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        Whitebox.invokeMethod(service, "getTechDetList", synchDTOList, 500);
        Set<String> chgReqNoSet= new HashSet<>();
        Whitebox.invokeMethod(service, "getTechHeadList", chgReqNoSet, 500);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void specialSnTechDataSynchronizationTest() throws Exception {
        List<SynchronizeSpmDateDTO> synchDTOList = new ArrayList<>();
        service.specialSnTechDataSynchronization(synchDTOList);
        SynchronizeSpmDateDTO dataDto1 = new SynchronizeSpmDateDTO();
        dataDto1.setProdplanId(123L);
        dataDto1.setChgRegNo("test1");
        dataDto1.setItemBarcode("111");
        dataDto1.setLastUpdateBy("陈俊熙10307315");
        dataDto1.setLastUpdateDate(new Date());
        dataDto1.setScanDate(new Date());
        dataDto1.setBarChangeMemo("memo1");
        synchDTOList.add(dataDto1);

        SynchronizeSpmDateDTO dataDto2 = new SynchronizeSpmDateDTO();
        dataDto2.setProdplanId(1234L);
        dataDto2.setChgRegNo("test2");
        dataDto2.setItemBarcode("222");
        dataDto2.setLastUpdateBy("陈俊熙10307315");
        dataDto2.setLastUpdateDate(new Date());
        dataDto2.setScanDate(new Date());
        dataDto2.setBarChangeMemo("memo2");
        synchDTOList.add(dataDto2);

        List<TechnicalChangeExecInfo> techExecInfoList = new ArrayList<>();
        TechnicalChangeExecInfo techExecInfo1 = new TechnicalChangeExecInfo();
        techExecInfo1.setProdplanId("1234");
        techExecInfo1.setChgReqNo("test2");
        techExecInfo1.setSn("222");
        techExecInfoList.add(techExecInfo1);
        PowerMockito.when(technicalChangeExecInfoRepository.getListBySynchronizeSpmDTO(anyList())).thenReturn(techExecInfoList);
        service.specialSnTechDataSynchronization(synchDTOList);

        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList<>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("222");
        psWipInfoDTO.setCurrProcessCode("2");
        psWipInfoDTOList.add(psWipInfoDTO);
        PsWipInfoDTO psWipInfoDTO1 = new PsWipInfoDTO();
        psWipInfoDTO1.setSn("111");
        psWipInfoDTO1.setCurrProcessCode("1");
        psWipInfoDTOList.add(psWipInfoDTO1);
        PowerMockito.when(wipInfoRepository.getListByBatchSnList(anyList())).thenReturn(psWipInfoDTOList);
        Assert.assertNotNull(service.specialSnTechDataSynchronization(synchDTOList));
    }

    @Test
    public void synchronizeSPMDataToCorrectFactoryTest() throws Exception {
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        dto.setFactoryId("53");
        List<String> prodIds = new ArrayList<>();
        PowerMockito.when(detailRepository.pageSelectSPMData(any())).thenReturn(prodIds);
        service.synchronizeSPMDataToCorrectFactory(dto);
        prodIds.add("testProd1");
        prodIds.add("testProd2");
        Set<String> resultSet = new HashSet<>();
        resultSet.add("testProd1");
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(resultSet);
                }}));

        List<SysLookupValuesDTO> sysLookupValuesDTOS = new ArrayList<>();
        SysLookupValuesDTO sys1 = new SysLookupValuesDTO();
        sys1.setAttribute2("cs");
        sys1.setLookupMeaning("52");
        SysLookupValuesDTO sys2 = new SysLookupValuesDTO();
        sys2.setAttribute2("sz");
        sys2.setLookupMeaning("53");
        sysLookupValuesDTOS.add(sys1);
        sysLookupValuesDTOS.add(sys2);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(anyList())).thenReturn(sysLookupValuesDTOS);


        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        ServiceData<Set<String>> rt = new ServiceData<>();
        Set<String> prodSet = new HashSet<>();
        prodSet.add("testProd2");
        rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        rt.setBo(prodSet);
        String msg = JSONObject.toJSONString(rt);

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(msg);


        service.synchronizeSPMDataToCorrectFactory(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}