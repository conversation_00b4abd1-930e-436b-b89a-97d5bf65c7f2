package com.zte.application.impl.technical;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.technical.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.technical.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.LOCKING;
import static com.zte.common.utils.Constant.UN_LOCK;
import static org.mockito.Matchers.*;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2022-09-29 14:03
 */

@PrepareForTest({CrafttechRemoteService.class})
public class TechnicalHeadServiceImplTwoTest extends PowerBaseTestCase {
    @InjectMocks
    private TechnicalHeadServiceImpl technicalHeadServiceImpl;
    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Mock
    private TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private HttpServletResponse response;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private BarcodeLockHeadService barcodeLockHeadService;

    @Mock
    private BarcodeLockHeadRepository barcodeLockHeadRepository;
    @Mock
    private BarcodeUnlockService barcodeUnlockService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private CommonTechnicalService commonTechnicalService;
    @Mock
    private TechnicalChangeDetailHisRepository technicalChangeDetailHisRepository;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    IMESLogService iMESLogService;
    @Test
    public void dealUnLockBarcodeList() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        barcodeLockDetailEntityDTOList.add(new BarcodeLockDetailEntityDTO());

        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = new ArrayList<>();
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no1");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no2");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no4");setBarcodeLockDetailEntityDTOList(barcodeLockDetailEntityDTOList);}});
        technicalAndLockInfoDTO.setBarcodeLockHeadEntityDTOList(barcodeLockHeadEntityDTOList);
        technicalHeadServiceImpl.dealUnLockBarcodeList("2",null,"2","2");
        technicalHeadServiceImpl.dealUnLockBarcodeList("2",barcodeLockHeadEntityDTOList,"2","2");

        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = new ArrayList<>();
        technicalChangeDetailDTOList.add(new TechnicalChangeDetailDTO());
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOS = new ArrayList<>();
        technicalChangeHeadDTOS.add(new TechnicalChangeHeadDTO(){{setTechnicalStatus("2");setChgReqNo("no5");}});
        technicalChangeHeadDTOS.add(new TechnicalChangeHeadDTO(){{setTechnicalStatus("3");setChgReqNo("no4");setDetails(technicalChangeDetailDTOList);}});
        PowerMockito.when(technicalChangeHeadRepository.getListByChgNoSet(any())).thenReturn(technicalChangeHeadDTOS);
        technicalHeadServiceImpl.dealUnLockBarcodeList("2",barcodeLockHeadEntityDTOList,"2","2");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void dealLockOrders() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = new ArrayList<>();
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no1");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no2");}});
        technicalAndLockInfoDTO.setBarcodeLockHeadEntityDTOList(barcodeLockHeadEntityDTOList);
        technicalHeadServiceImpl.dealLockOrders(technicalAndLockInfoDTO);
        technicalAndLockInfoDTO.setToFactoryId("2");
        technicalAndLockInfoDTO.setBarcodeLockHeadEntityDTOList(barcodeLockHeadEntityDTOList);
        technicalHeadServiceImpl.dealLockOrders(technicalAndLockInfoDTO);

        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOS = new ArrayList<>();
        barcodeLockHeadEntityDTOS.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no1");}});
        PowerMockito.when( barcodeLockHeadService.getHeadInfoByBillNoList(any())).thenReturn(barcodeLockHeadEntityDTOS);
        technicalHeadServiceImpl.dealLockOrders(technicalAndLockInfoDTO);

        barcodeLockHeadEntityDTOS.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no1");setStatus(UN_LOCK);}});
        PowerMockito.when( barcodeLockHeadService.getHeadInfoByBillNoList(any())).thenReturn(barcodeLockHeadEntityDTOS);
        technicalHeadServiceImpl.dealLockOrders(technicalAndLockInfoDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void moveTechnicalAndLockInfoByProdplanId() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(null);
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList = new ArrayList<>();
        technicalAndLockInfoDTO.setTechnicalChangeHeadDTOList(technicalChangeHeadDTOList);
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);
        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO());
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        barcodeLockDetailEntityDTOList.add(new BarcodeLockDetailEntityDTO());
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = new ArrayList<>();
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO());
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no1");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no2");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no3");}});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO(){{setBillNo("no5");setStatus(LOCKING);}});
        PowerMockito.when(barcodeLockHeadService.getHeadInfoByBillNoList(any())).thenReturn(barcodeLockHeadEntityDTOList);
        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = new ArrayList<>();
        technicalChangeDetailDTOList.add(new TechnicalChangeDetailDTO());

        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO(){{setChgReqNo("no1");setDetails(technicalChangeDetailDTOList);}});
        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO(){{setChgReqNo("no2");}});
        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO(){{setChgReqNo("no3");}});

        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);

        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO(){{setChgReqNo("no4");setDetails(technicalChangeDetailDTOList);}});
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);
        List<PsTaskDTO> psTaskList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("7778889");
        psTaskList.add(psTaskDTO);
        PowerMockito.when(centerfactoryRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskList);
        technicalChangeHeadDTOList.add(new TechnicalChangeHeadDTO(){{setChgReqNo("no5");setDetails(technicalChangeDetailDTOList);}});

        List<CtRouteInfoDTO> ctRouteInfoDTOList = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        ctRouteInfoDTO.setItemNo("7778889");
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        listDetail.add(ctRouteDetailDTO);
        ctRouteInfoDTO.setListDetail(listDetail);
        ctRouteInfoDTOList.add(ctRouteInfoDTO);
        PowerMockito.when(CrafttechRemoteService.getRouteInfo(anyString(), anyString())).thenReturn(ctRouteInfoDTOList);

        try {
            technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CT_ROUTE_DETAIL_OF_PRODPLANID_NOT_EXIST, e.getMessage());
        }
        technicalAndLockInfoDTO.setProdplanId("7778889");
        technicalAndLockInfoDTO.setToFactoryId("7778889");
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);
        List<TechnicalChangeHeadDTO> technicalChangeHeadDTOS = new ArrayList<>();
        technicalChangeHeadDTOS.add(new TechnicalChangeHeadDTO(){{setTechnicalStatus("2");setChgReqNo("no5");setHeadId("22");}});
        technicalChangeHeadDTOS.add(new TechnicalChangeHeadDTO(){{setTechnicalStatus("3");setChgReqNo("no4");setHeadId("22");setDetails(technicalChangeDetailDTOList);}});
        PowerMockito.when(technicalChangeHeadRepository.getListByChgNoSet(any())).thenReturn(technicalChangeHeadDTOS);
        technicalHeadServiceImpl.moveTechnicalAndLockInfoByProdplanId(technicalAndLockInfoDTO);

    }
}
