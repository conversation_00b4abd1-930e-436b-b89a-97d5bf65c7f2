package com.zte.application.impl.technical;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeExecInfo;
import com.zte.domain.model.technical.TechnicalChangeExecInfoRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeExecInfoEntityDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class, RedisHelper.class})
public class TechnicalChangeExecInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private TechnicalChangeExecInfoServiceImpl technicalChangeExecInfoService;

    @Mock
    private TechnicalChangeExecInfoRepository technicalChangeExecInforRpository;

    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Test
    public void testPageList() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        TechnicalChangeExecInfoEntityDTO record = new TechnicalChangeExecInfoEntityDTO();
        try {
            technicalChangeExecInfoService.pageList(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MUST_ENTER_A_QUERY_CRITERIA, e.getMessage());
        }
        record.setUnlockCraftSection("SMT-A");
        try {
            technicalChangeExecInfoService.pageList(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UNLOCK_PROCESS_MUST_BE_QUERIED_WITH_TIME, e.getMessage());
        }
        record.setUnlockStartTime("2022-06-27 00:00:00");
        record.setUnlockEndTime("2021-06-30 00:00:00");
        try {
            technicalChangeExecInfoService.pageList(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPAN_WHITIN_30DAYS, e.getMessage());
        }
        record.setUnlockEndTime("2022-06-30 00:00:00");
        record.setUnlockStatus(0);
        record.setProdplanIdList(Arrays.asList("8899730", "8899730", "8899730", "8899730", "8899730", "8899730"));
        try {
            technicalChangeExecInfoService.pageList(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_OVER_LENGTH, e.getMessage());
        }
        record.setProdplanIdList(null);
        PowerMockito.when(technicalChangeExecInforRpository.pageList(Mockito.any()))
                .thenReturn(null);
        technicalChangeExecInfoService.pageList(record);
        record = new TechnicalChangeExecInfoEntityDTO();
        record.setSnList(Arrays.asList("123"));
        try {
            technicalChangeExecInfoService.pageList(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_FEED_BACK, e.getMessage());
        }
        List<TechnicalChangeExecInfoEntityDTO> infoList = new ArrayList<>();
        TechnicalChangeExecInfoEntityDTO a1 = new TechnicalChangeExecInfoEntityDTO();
        a1.setUnlockType("1");
        a1.setUnlockStatus(0);
        infoList.add(a1);
        PowerMockito.when(technicalChangeExecInforRpository.pageList(Mockito.any()))
                .thenReturn(infoList);

        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO b1 = new SysLookupValuesDTO();
        b1.setLookupType(new BigDecimal(1004096));
        b1.setLookupMeaning("1");
        b1.setDescriptionChin("1");
        batchSysValueByCode.add(b1);
        SysLookupValuesDTO b2 = new SysLookupValuesDTO();
        b2.setLookupType(new BigDecimal(1004102));
        b2.setLookupMeaning("0");
        b2.setDescriptionChin("0");
        batchSysValueByCode.add(b2);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode)
        ;
        technicalChangeExecInfoService.pageList(record);

    }

    @Test
    public void testUpdate() throws Exception {
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("1");
        dto.setUnlockStatus(1);
        List<TechnicalChangeExecInfoEntityDTO> needUpdateList = new ArrayList<>();
        TechnicalChangeExecInfoEntityDTO dto1 = new TechnicalChangeExecInfoEntityDTO();
        dto1.setId("1");
        dto1.setProdplanId("***********");
        needUpdateList.add(dto1);
        dto.setNeedUpdateList(needUpdateList);
        TechnicalChangeExecInfoEntityDTO.StateEnum buttonOption = TechnicalChangeExecInfoEntityDTO.StateEnum.CONFIRMED;
        PowerMockito.when(technicalChangeExecInforRpository.batchUpdate(Mockito.any())).thenReturn(1);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("***********");
        psTask.setItemNo("itemNo");
        psTask.setTaskQty(BigDecimal.ONE);
        psTaskList.add(psTask);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(RedisHelper.delete(anyString(), anyString())).thenReturn(true);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyObject())).thenReturn(psTaskList);
        PowerMockito.when(technicalChangeExecInforRpository.selectCountofConfirmedByProdplanId(Mockito.any())).thenReturn(1L);
        PowerMockito.when(technicalChangeDetailRepository.updateByChgReqNoAndProdplanId(any())).thenReturn(1);

        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        TechnicalChangeDetailDTO dto2 = new TechnicalChangeDetailDTO();
        dto2.setTechnicalStatus(Constant.TechnicalChangeStatus.UNLOCKED);
        detailList.add(dto2);
        PowerMockito.when(technicalChangeDetailRepository.getListByBillNo(anyString())).thenReturn(detailList);

        PowerMockito.when(technicalChangeHeadRepository.updateStatusByChgReqNo(any(), any(), any())).thenReturn(1);

        technicalChangeExecInfoService.update(dto, buttonOption, "00286569");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test(expected = MesBusinessException.class)
    public void testUpdate2() throws Exception {
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("1");
        dto.setUnlockStatus(1);
        List<TechnicalChangeExecInfoEntityDTO> needUpdateList = new ArrayList<>();
        TechnicalChangeExecInfoEntityDTO dto1 = new TechnicalChangeExecInfoEntityDTO();
        dto1.setId("1");
        dto1.setProdplanId("***********");
        needUpdateList.add(dto1);
        dto.setNeedUpdateList(needUpdateList);
        TechnicalChangeExecInfoEntityDTO.StateEnum buttonOption = TechnicalChangeExecInfoEntityDTO.StateEnum.CONFIRMED;
        PowerMockito.when(technicalChangeExecInforRpository.batchUpdate(Mockito.any())).thenReturn(1);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("***********");
        psTask.setItemNo("itemNo");
        psTask.setTaskQty(BigDecimal.ONE);
        psTaskList.add(psTask);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        PowerMockito.when(RedisHelper.delete(anyString(), anyString())).thenReturn(true);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyObject())).thenReturn(psTaskList);
        PowerMockito.when(technicalChangeExecInforRpository.selectCountofConfirmedByProdplanId(Mockito.any())).thenReturn(1L);
        PowerMockito.when(technicalChangeDetailRepository.updateByChgReqNoAndProdplanId(any())).thenReturn(1);

        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        TechnicalChangeDetailDTO dto2 = new TechnicalChangeDetailDTO();
        dto2.setTechnicalStatus(Constant.TechnicalChangeStatus.UNLOCKED);
        detailList.add(dto2);
        PowerMockito.when(technicalChangeDetailRepository.getListByBillNo(anyString())).thenReturn(detailList);

        PowerMockito.when(technicalChangeHeadRepository.updateStatusByChgReqNo(any(), any(), any())).thenReturn(1);

        technicalChangeExecInfoService.update(dto, buttonOption, "00286569");
    }

    @Test
    public void testUpdateTwo() throws Exception {
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("1");
        dto.setUnlockStatus(2);
        dto.setRejectionReason("测试");
        List<TechnicalChangeExecInfoEntityDTO> needUpdateList = new ArrayList<>();
        TechnicalChangeExecInfoEntityDTO dto1 = new TechnicalChangeExecInfoEntityDTO();
        dto1.setId("1");
        needUpdateList.add(dto1);
        dto.setNeedUpdateList(needUpdateList);
        TechnicalChangeExecInfoEntityDTO.StateEnum buttonOption = TechnicalChangeExecInfoEntityDTO.StateEnum.REJECT;
        PowerMockito.when(technicalChangeExecInforRpository.batchUpdate(Mockito.any())).thenReturn(1);
        technicalChangeExecInfoService.update(dto, buttonOption, "00286569");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void syncTechnicalChangeExecInfo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        String factoryId = "52";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.LookUpKey.LOOK_7000015));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.eq(Constant.LookUpKey.LOOK_7000015))).thenReturn(sysLookupTypesDTO);
        try {
            technicalChangeExecInfoService.syncTechnicalChangeExecInfo(factoryId);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupTypesDTO.setLookupMeaning("2023-02-10 20:30:22");
        List<TechnicalChangeExecInfoEntityDTO> syncDataList = new ArrayList<>();
        PowerMockito.when(technicalChangeExecInforRpository.getSyncDataByLastUpdatedDate(Mockito.any(), Mockito.anyInt(), Mockito.anyString())).thenReturn(syncDataList);
        technicalChangeExecInfoService.syncTechnicalChangeExecInfo(factoryId);

        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setSn("22222");
        dto.setLastUpdatedDate(new Date());
        syncDataList.add(dto);
        technicalChangeExecInfoService.syncTechnicalChangeExecInfo(factoryId);

    }

    /*Started by AICoder, pid:l800dk667064842140f10b7d40e9729bec23ebc7*/

    @Test
    public void testGetBarcodeTechnologyUpgradeCompletedInfo_EmptySnList() {
        List<String> snList = new ArrayList<>();
        List<TechnicalChangeExecInfo> result =
                technicalChangeExecInfoService.getBarcodeTechnologyUpgradeCompletedInfo(snList);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetBarcodeTechnologyUpgradeCompletedInfo_NonEmptySnList_NoMatchingRecords() {
        List<String> snList = Arrays.asList("SN1", "SN2");
        when(technicalChangeExecInforRpository.selectBySn(anyList())).thenReturn(new ArrayList<>());

        List<TechnicalChangeExecInfo> result =
                technicalChangeExecInfoService.getBarcodeTechnologyUpgradeCompletedInfo(snList);

        assertEquals(0, result.size());
    }

    @Test
    public void testGetBarcodeTechnologyUpgradeCompletedInfo_NonEmptySnList_WithMatchingRecords() {
        List<String> snList = Arrays.asList("SN1", "SN2");
        TechnicalChangeExecInfoEntityDTO entityDTO1 = mock(TechnicalChangeExecInfoEntityDTO.class);
        TechnicalChangeExecInfoEntityDTO entityDTO2 = mock(TechnicalChangeExecInfoEntityDTO.class);
        List<TechnicalChangeExecInfoEntityDTO> expectedEntities =
                Arrays.asList(entityDTO1, entityDTO2);
        when(technicalChangeExecInforRpository.selectBySn(anyList()))
                .thenReturn(Arrays.asList(mock(TechnicalChangeExecInfo.class), mock(TechnicalChangeExecInfo.class)));

        List<TechnicalChangeExecInfo> result =
                technicalChangeExecInfoService.getBarcodeTechnologyUpgradeCompletedInfo(snList);

        assertEquals(expectedEntities.size(), result.size());
    }
    /*Ended by AICoder, pid:l800dk667064842140f10b7d40e9729bec23ebc7*/
}