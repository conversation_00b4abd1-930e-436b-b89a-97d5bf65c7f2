package com.zte.application.impl;

import com.zte.application.PsWipInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.domain.model.DailyWorkAmountStatRepository;
import com.zte.domain.model.DocFilePropertiesRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.DocFilePropertiesEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class DocFilePropertiesServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private DocFilePropertiesServiceImpl service;

    @Mock
    private DocFilePropertiesRepository docFilePropertiesRepository;

    @Test
    public void pageList() throws Exception {
        List<DocFilePropertiesEntityDTO> result = new ArrayList<>();
        DocFilePropertiesEntityDTO record = new DocFilePropertiesEntityDTO();
        result.add(record);
        record.setPage(1);
        record.setRows(10);
        record.setHeadId("test123");
        PowerMockito.when(docFilePropertiesRepository.pageList(Mockito.any())).thenReturn(result);
        Assert.assertNotNull(service.pageList(record));
    }

    @Test
    public void getList() throws Exception {
        List<DocFilePropertiesEntityDTO> result = new ArrayList<>();
        DocFilePropertiesEntityDTO record = new DocFilePropertiesEntityDTO();
        result.add(record);
        record.setHeadId("test123");
        PowerMockito.when(docFilePropertiesRepository.getList(Mockito.any())).thenReturn(result);
        Assert.assertNotNull(service.getList(record));
    }

    @Test
    public void batchInsert() throws Exception {
        List<DocFilePropertiesEntityDTO> result = new ArrayList<>();
        DocFilePropertiesEntityDTO record = new DocFilePropertiesEntityDTO();
        result.add(record);
        record.setHeadId("test123");
        PowerMockito.when(docFilePropertiesRepository.batchInsert(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1,service.batchInsert(result));
    }
}