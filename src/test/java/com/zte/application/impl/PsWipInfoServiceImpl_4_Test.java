/*Started by AICoder, pid:ea8aeg40b0ke64914df309a533ad5f07c0c0283d*/
package com.zte.application.impl;

import com.zte.application.WarehouseEntryInfoService;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.QualityCodeTaskDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;


@PrepareForTest({CrafttechRemoteService.class,PlanscheduleRemoteService.class,BasicsettingRemoteService.class})
public class PsWipInfoServiceImpl_4_Test extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl psWipInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchDealBySn_emptyWipInfoList() {
        List<String> excludeTaskNoList = Arrays.asList("task1", "task2");
        List<PsWipInfo> wipInfoList = Collections.emptyList();
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();

        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);
        Assert.assertEquals(0,wipInfoList.size());
    }

    @Test(expected = MesBusinessException.class)
    public void testGetQualityCode_WithEmpty() {
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        psWipInfoService.getQualityCode(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetQualityCode_WithEmptyCustomerNo() {
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        dto.setCustomerName("TestName");
        psWipInfoService.getQualityCode(dto);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetQualityCode_WithEmptyCustomerName() {
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        dto.setCustomerNo("TestNo");
        psWipInfoService.getQualityCode(dto);
    }

    @Test
    public void testGetQualityCode_WithValidDto() {
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        dto.setCustomerNo("TestNo");
        dto.setCustomerName("TestName");
        when(psWipInfoRepository.getSnInfoList(anyString(), any(Date.class), anyString(), anyInt()))
                .thenReturn(new ArrayList<>());
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(Collections.emptyList());
        psWipInfoService.getQualityCode(dto);

        dto.setPreDays(0);
        psWipInfoService.getQualityCode(dto);

        dto.setPreDays(1);
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        psWipInfoService.getQualityCode(dto);
        verify(psWipInfoRepository, times(0)).getSnInfoList(anyString(), any(Date.class), anyString(), anyInt());
    }
    @Test
    public void testBatchDealBySn_fullFlow() {
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        dto.setCustomerNo("customer1");
        List<String> excludeTaskNoList = new ArrayList<>();
        List<PsWipInfo> wipInfoList =  new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute2("");
        wipInfoList.add(psWipInfo);
        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);

        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setAttribute2("gfgd");
        wipInfoList.add(psWipInfo1);
        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);


        excludeTaskNoList = Arrays.asList("task1", "gfgd");
        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);

        PsWipInfo psWipInfo11 = new PsWipInfo();
        psWipInfo11.setAttribute2("gfgdgfgf");
        wipInfoList.add(psWipInfo11);
        when(centerfactoryRemoteService.getTaskNoFromWmes(anyList(), anyString())).thenReturn(new ArrayList<>());
        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);

        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        when(centerfactoryRemoteService.getTaskNoFromWmes(anyList(), anyString())).thenReturn(Arrays.asList("gfgdgfgf", "gfdf"));
        psWipInfoService.batchDealBySn(excludeTaskNoList, wipInfoList, dto);
        Assert.assertEquals(3,wipInfoList.size());
    }
    @Test
    public void testGetSnExistQualityCodeWithNonEmptyListAndNoBlankSn() {
        List<PsWipInfo> wipInfoWmesList =  new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("gg");
        wipInfoWmesList.add(psWipInfo);
        when(centerfactoryRemoteService.getSnExistQualityCode(anyList())).thenReturn(Arrays.asList("SN1", "SN2"));
        List<String> result = psWipInfoService.getSnExistQualityCode(wipInfoWmesList);
        verify(centerfactoryRemoteService, times(1)).getSnExistQualityCode(anyList());
    }
    /* Started by AICoder, pid:7458266344f699514a840aa730f85c69c314d57b */
    @Test
    public void testFilterWipInfosBySn_NonEmptySnList() {
        List<PsWipInfo> wipInfoList =  new ArrayList<>();
        psWipInfoService.filterWipInfosBySn(wipInfoList, new ArrayList<>());

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("YTUYT");
        psWipInfo.setAttribute2("BGFD");
        wipInfoList.add(psWipInfo);
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setSn("SN12");
        wipInfoList.add(psWipInfo2);
        List<String> snList = Arrays.asList("YTUYT,BGFD");
        psWipInfoService.filterWipInfosBySn(wipInfoList, snList);
        assertEquals(1, wipInfoList.size());
    }
    /* Ended by AICoder, pid:7458266344f699514a840aa730f85c69c314d57b */
    @Test
    public void testCheckSnCurProcess_NeedDealWipListNotEmpty() {
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);

        List<PsWipInfo> wipInfoList =  new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("gg");
        psWipInfo.setLastProcess("Y");
        psWipInfo.setCurrProcessCode("#");
        wipInfoList.add(psWipInfo);
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setSn("gg");
        psWipInfo1.setCurrProcessCode("#FFF");
        wipInfoList.add(psWipInfo1);
        PsWipInfo psWipInfo11 = new PsWipInfo();
        psWipInfo11.setSn("gg");
        psWipInfo11.setCurrProcessCode("#FFF");
        psWipInfo11.setWorkOrderNo("GDFG");
        wipInfoList.add(psWipInfo11);

        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        dto.setCustomerNo("customerNo");
        dto.setCustomerName("customerName");

        psWipInfoService.checkSnCurProcess(wipInfoList, dto);
        verify(centerfactoryRemoteService).saveQualityCode(eq("customerName"), anyList());
    }


    @Test
    public void testCheckSnCurProcess_EmptyWipInfoList() {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        QualityCodeTaskDTO dto = new QualityCodeTaskDTO();
        psWipInfoService.checkSnCurProcess(wipInfoList, dto);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("gg");
        wipInfoList.add(psWipInfo);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(new ArrayList<>());
        psWipInfoService.checkSnCurProcess(wipInfoList, dto);
        verifyNoInteractions(centerfactoryRemoteService);
    }
    @Test
    public void testGetCtRouteDetail_EmptyConfirmWipList() {
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setRouteId("gsdg");
        workOrderList.add(psWorkOrderBasic);
        List<PsWipInfo> confirmWipList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("gg");
        psWipInfo.setWorkOrderNo("Y");
        psWipInfo.setCurrProcessCode("#");
        confirmWipList.add(psWipInfo);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        when(PlanscheduleRemoteService.getListByWorkOrderNos(anyMap())).thenReturn(null);
        psWipInfoService.getCtRouteDetail(workOrderList, confirmWipList);

        when(PlanscheduleRemoteService.getListByWorkOrderNos(anyMap())).thenReturn(workOrderList);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        when(CrafttechRemoteService.getCtRouteDetailByRouteIds(anyList())).thenReturn(null);
        List<CtRouteDetailDTO> result = psWipInfoService.getCtRouteDetail(workOrderList, confirmWipList);
        Assert.assertNull(result);

    }

    @Test
    public void testSplitCtRouteDetailCheck_EmptyConfirmWipList() {
        List<String> processList = new ArrayList<>();
        List<PsWipInfo> confirmWipList = new ArrayList<>();
        List<PsWipInfo> needDealWipList = new ArrayList<>();
        psWipInfoService.splitCtRouteDetailCheck(processList, confirmWipList, needDealWipList);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setRouteId("gsdg");
        psWorkOrderBasic.setWorkOrderNo("Y");
        workOrderList.add(psWorkOrderBasic);
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setRouteId("gsdg");
        routeDetailList.add(ctRouteDetailDTO);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("gg");
        psWipInfo.setWorkOrderNo("Y");
        psWipInfo.setCurrProcessCode("#");
        confirmWipList.add(psWipInfo);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        when(PlanscheduleRemoteService.getListByWorkOrderNos(anyMap())).thenReturn(workOrderList);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        when(CrafttechRemoteService.getCtRouteDetailByRouteIds(anyList())).thenReturn(routeDetailList);
        psWipInfoService.splitCtRouteDetailCheck(processList, confirmWipList, needDealWipList);
        Assert.assertEquals(0,needDealWipList.size());
    }

    @Test
    public void checkSnCurProcessOrder() {
        List<PsWipInfo> needDealWipList = new ArrayList<>();
        List<String> processList = new ArrayList<>();
        PsWipInfo  wip = new PsWipInfo();
        wip.setCurrProcessCode("bf1");
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        psWipInfoService.checkSnCurProcessOrder(needDealWipList, processList,wip,routeDetailList);

        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setRouteId("gsdg");
        ctRouteDetailDTO.setCurrProcess("VF");
        routeDetailList.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setRouteId("gsdg");
        ctRouteDetailDTO1.setCurrProcess("bf");
        routeDetailList.add(ctRouteDetailDTO1);
        psWipInfoService.checkSnCurProcessOrder(needDealWipList, processList,wip,routeDetailList);

        processList.add("VF");
        processList.add("GDFD");
        psWipInfoService.checkSnCurProcessOrder(needDealWipList, processList,wip,routeDetailList);
        Assert.assertEquals(0,needDealWipList.size());
    }
    @Test
    public void checkSnCurProcessOrderExists() {
        List<PsWipInfo> needDealWipList = new ArrayList<>();
        List<String> processList = new ArrayList<>();
        PsWipInfo  wip = new PsWipInfo();
        wip.setCurrProcessCode("bf");
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setRouteId("gsdg");
        ctRouteDetailDTO.setCurrProcess("VF");
        routeDetailList.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setRouteId("gsdg");
        ctRouteDetailDTO1.setCurrProcess("bf");
        ctRouteDetailDTO1.setProcessSeq(BigDecimal.TEN);
        routeDetailList.add(ctRouteDetailDTO1);
        processList.add("VF");
        processList.add("GDFD");
        psWipInfoService.checkSnCurProcessOrder(needDealWipList, processList,wip,routeDetailList);
        Assert.assertEquals(1,needDealWipList.size());
    }
    @Test
    public void checkSnCurProcessOrderNotExists() {
        List<PsWipInfo> needDealWipList = new ArrayList<>();
        List<String> processList = new ArrayList<>();
        PsWipInfo  wip = new PsWipInfo();
        wip.setCurrProcessCode("bf");
        List<CtRouteDetailDTO> routeDetailList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setRouteId("gsdg");
        ctRouteDetailDTO.setCurrProcess("VF");
        ctRouteDetailDTO.setProcessSeq(BigDecimal.TEN);
        routeDetailList.add(ctRouteDetailDTO);
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setRouteId("gsdg");
        ctRouteDetailDTO1.setCurrProcess("bf");
        ctRouteDetailDTO1.setProcessSeq(BigDecimal.ONE);
        routeDetailList.add(ctRouteDetailDTO1);
        processList.add("VF");
        processList.add("GDFD");
        psWipInfoService.checkSnCurProcessOrder(needDealWipList, processList,wip,routeDetailList);
        Assert.assertEquals(0,needDealWipList.size());
    }

    /* Started by AICoder, pid:xc992r80c1qcc2f14b570b1820f45f255928069e */
    @Test
    public void saveQualityCode() throws Exception{
        PowerMockito.doNothing().when(centerfactoryRemoteService).saveQualityCode(Mockito.anyString(),Mockito.anyList());
        // 获取私有方法
        Method addMethod = PsWipInfoServiceImpl.class.getDeclaredMethod("saveQualityCode", QualityCodeTaskDTO.class, List.class);
        // 设置方法为可访问
        addMethod.setAccessible(true);
        // 调用私有方法并获取结果
        addMethod.invoke(psWipInfoService,new QualityCodeTaskDTO(), new ArrayList<>());
        verify(centerfactoryRemoteService, times(0)).saveQualityCode(anyString(), anyList());
    }
    /* Ended by AICoder, pid:xc992r80c1qcc2f14b570b1820f45f255928069e */
}
/*Ended by AICoder, pid:ea8aeg40b0ke64914df309a533ad5f07c0c0283d*/