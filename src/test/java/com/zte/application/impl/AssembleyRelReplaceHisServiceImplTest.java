package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.AssembleyRelReplaceHisEntityDTO;
import com.zte.interfaces.dto.StandardModeStockInfoDTO;
import com.zte.interfaces.dto.WhiteListInfoDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class})
public class AssembleyRelReplaceHisServiceImplTest {

    @InjectMocks
    AssembleyRelReplaceHisServiceImpl service;

    @Mock
    private AssembleyRelReplaceHisRepository assembleyRelReplaceHisrepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WhiteListInfoRepository whiteListInfoRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
    }

    @Test
    public void pageList() throws Exception {
        List<AssembleyRelReplaceHisEntityDTO> assembleyRelReplaceHisEntityDTOS = new ArrayList<>();
        PowerMockito.when(assembleyRelReplaceHisrepository.pageList(any())).thenReturn(assembleyRelReplaceHisEntityDTOS);
        AssembleyRelReplaceHisEntityDTO record = new AssembleyRelReplaceHisEntityDTO();
        Assert.assertNotNull(service.pageList(record));
    }

    @Test
    public void getCheckReplaceSnInfo() throws Exception {
        AssembleyRelReplaceHisEntityDTO record = new AssembleyRelReplaceHisEntityDTO();
        try {
            service.getCheckReplaceSnInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_IS_NULL, e.getExMsgId());
        }

        record.setOldSn("oldSn");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("mainSn");
        psWipInfo.setItemNo("129692851021abc");
        psWipInfo.setAttribute2("taskNo");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(psWipInfos);

        List<PsTask> psTasks = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(any())).thenReturn(psTasks);
        try {
            service.getCheckReplaceSnInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.TASK_NOT_HAVE_DETAILS, e.getExMsgId());
        }
        PsTask psTask = new PsTask();
        psTask.setProductType("11");
        psTasks.add(psTask);
        try {
            service.getCheckReplaceSnInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_REWORK_CANNOT_REPLACE, e.getExMsgId());
        }

        psTask.setProductType(MpConstant.TASK_TYPE_RETURN);
        try {
            service.getCheckReplaceSnInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ITEM_NO_NULL, e.getExMsgId());
        }

        record.setTaskNo("129692851021zhq");
        psWipInfos.clear();
        List<WhiteListInfoDTO> whiteListInfoDTOS = new ArrayList<>();
        PowerMockito.when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(any())).thenReturn(whiteListInfoDTOS);
        try {
            service.getCheckReplaceSnInfo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_REWORK_CANNOT_REPLACE, e.getExMsgId());
        }
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTOS.add(whiteListInfoDTO);
        whiteListInfoDTO.setTaskNo("TaskNo");
        whiteListInfoDTO.setItemNo("129692851021zhq");
        service.getCheckReplaceSnInfo(record);

        // 替换条码测试
        String newSn = "111";
        record.setNewSn(newSn);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setItemNo("129692851021zzz");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(newSn)).thenReturn(wipInfoBySn);

        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(psWipInfos);

        Assert.assertThrows(NullPointerException.class, () ->service.getCheckReplaceSnInfo(record));

    }


    @Test
    public void checkNewSn() throws Exception {
        AssembleyRelReplaceHisEntityDTO record = new AssembleyRelReplaceHisEntityDTO();
        record.setOldSn("oldSn");
        record.setItemNo("129692851021222");

        // 替换条码测试
        String newSn = "111";
        record.setNewSn(newSn);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setItemNo("129692851021zzz");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(newSn)).thenReturn(wipInfoBySn);

        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(null);

        PageRows<StandardModeStockInfoDTO> pageRows = new PageRows<>();
        List<StandardModeStockInfoDTO> standardModeStockInfoDTOS = new ArrayList<>();
        StandardModeStockInfoDTO stockInfoDTO = new StandardModeStockInfoDTO();
        standardModeStockInfoDTOS.add(stockInfoDTO);
        pageRows.setRows(standardModeStockInfoDTOS);
        PowerMockito.when(ProductionDeliveryRemoteService.getStandardModeStockInfoPage(any())).thenReturn(pageRows);
        try {
            service.checkNewSn(record, true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_FAILED, e.getExMsgId());
        }


        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(newSn)).thenReturn(null);
        List<WhiteListInfoDTO> whiteListInfoDTOS = new ArrayList<>();
        PowerMockito.when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(any())).thenReturn(whiteListInfoDTOS);
        try {
            service.checkNewSn(record, true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_FIND_SN_CANNOT_REPLACE, e.getExMsgId());
        }

        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTO.setItemNo("129692851021zzz");
        whiteListInfoDTOS.add(whiteListInfoDTO);
        service.checkNewSn(record, true);

    }

    @Test
    public void submit() throws Exception {
        AssembleyRelReplaceHisEntityDTO record = new AssembleyRelReplaceHisEntityDTO();
        record.setOldSn("oldSn");
        try {
            service.submit(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_IS_NULL, e.getExMsgId());
        }
    }

    @Test
    public void submitDo() throws Exception {
        AssembleyRelReplaceHisEntityDTO record = new AssembleyRelReplaceHisEntityDTO();
        record.setOldSn("oldSn");

        record.setNewSn("newSn");
        record.setItemNo("ItemNo");

        PowerMockito.when(assembleyRelReplaceHisrepository.batchInsert(any())).thenReturn(1);
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("mainSn");
        psWipInfo.setItemNo("129692851021abc");
        psWipInfo.setAttribute2("taskNo");
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(psWipInfos);
        PowerMockito.when(psWipInfoRepository.updatePsWipInfoByIdSelective(any())).thenReturn(1);

        PsWipInfo wipInfoBySn = new PsWipInfo();
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(wipInfoBySn);

        PowerMockito.when(ProductionDeliveryRemoteService.replaceSnDeleteStock(any())).thenReturn(1);
        service.submitDo(record);

        psWipInfos.clear();
        PowerMockito.when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(any())).thenReturn(null);
        try {
            service.submitDo(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIP_INFO_OF_PARENT_SN_NOT_EXIST, e.getExMsgId());
        }

        List<WhiteListInfoDTO> whiteListInfoDTOS = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTOS.add(whiteListInfoDTO);
        PowerMockito.when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(any())).thenReturn(whiteListInfoDTOS);
        PowerMockito.when(whiteListInfoRepository.updateSnByid(any())).thenReturn(1);
        PowerMockito.when(whiteListInfoRepository.deleteWhiteListInfoDetailBySN(any())).thenReturn(1);
        service.submitDo(record);

        psWipInfos.add(psWipInfo);
        service.submitDo(record);
    }


}
