package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.vo.EquipmentConsumeReelIdVO;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, SpringUtil.class, PlanscheduleRemoteService.class,
		ProductionDeliveryRemoteService.class, MESHttpHelper.class, RedisHelper.class,
		RequestHeadValidationUtil.class, AopContext.class})
public class SmtMachineMaterialMoutingServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    SmtMachineMaterialMoutingServiceImpl service;

    @Mock
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

	@Mock
	private PkCodeInfoService pkCodeInfoService;

    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private IMESLogService imesLogService;

	@Mock
	private PsWipInfoService psWipInfoService;
	@Mock
	private WipScanHistoryService wipScanHistoryService;
	@Mock
	private BSmtBomDetailService bSmtBomDetailService;
	@Mock
	private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;
	@Mock
	CloudDiskHelper cloudDiskHelper;

	@Mock
	private BSmtBomDetailRepository smtBomDetailRepository;

	@Mock
	private SmtMachineMTLHistoryLRepository historyLRepository;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

	@Before
	public void init() {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.mockStatic(RequestHeadValidationUtil.class);
		PowerMockito.mockStatic(AopContext.class);
		PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
		PowerMockito.mockStatic(MESHttpHelper.class, RedisHelper.class);
	}
    @Test
    public void getReelIdAndQtyMap() {
        java.util.List<EquipmentConsumeReelIdDTO> consumeReelIdList = new ArrayList<>();
        consumeReelIdList.add(new EquipmentConsumeReelIdDTO());
        List<EquipmentConsumeReelIdVO > reelIdUseInfos = new ArrayList<>();
		Assert.assertNotNull(service.getReelIdAndQtyMap(consumeReelIdList,reelIdUseInfos));
    }
    @Test
    public void insertSMTPrepare() {
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByWorkOrderNO(any())).thenReturn(Lists.newArrayList(
                new SmtMachineMaterialMouting(){{setNextReelRowid("1");}},
                new SmtMachineMaterialMouting()
        ));
        PowerMockito.when(smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(any())).thenReturn(2);
		Assert.assertNotNull(service.insertSMTPrepare(new SmtMachineMaterialMouting(){{setForward("1");}}));
    }

	@Test
	public void dipBoardScanning() throws Exception {
		DipBoardScanningDTO param = new DipBoardScanningDTO();
		param.setWorkOrderNo("123");
		param.setFactoryId("2");
		param.setScanByWorkStation(false);
		List<PsWorkOrderDTO> workOrderList = new LinkedList<>();
		PsWorkOrderDTO a1 = new PsWorkOrderDTO();
		a1.setWorkOrderNo("123");
		a1.setWorkOrderStatus("已开工");
		a1.setCfgHeaderId("123");
		a1.setItemNo("129206751425AKB2");
		workOrderList.add(a1);

		PowerMockito.when(PlanscheduleRemoteService.getPsWorkOrderBasic(Mockito.anyMap()))
				.thenReturn(workOrderList);
		PsWipInfo psWipInfo = new PsWipInfo();
		psWipInfo.setCraftSection("DIP");
		psWipInfo.setAttribute1("123");
		PowerMockito.when(psWipInfoService.getPsWipInfoBySn(Mockito.any())).thenReturn(psWipInfo);

		PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.anyMap()))
				.thenReturn(1l);
		List<PsWorkOrderSmt> workOrderSmtList = new LinkedList<>();
		PsWorkOrderSmt c1 = new PsWorkOrderSmt();
		c1.setCfgHeaderId("123");
		workOrderSmtList.add(c1);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(Mockito.any()))
				.thenReturn(workOrderSmtList);

		List<BSmtBomDetail> bSmtBomDetails = new LinkedList<>();
		BSmtBomDetail d1 = new BSmtBomDetail();
		bSmtBomDetails.add(d1);
		PowerMockito.when(bSmtBomDetailService.getList(Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(bSmtBomDetails);
		List<BBomDetail> allChildByProductCode = new LinkedList<>();
		BBomDetail bBomDetail1 = new BBomDetail();
		bBomDetail1.setProductCode("129206751425AKB");
		bBomDetail1.setItemCode("129206751425AKB1");
		allChildByProductCode.add(bBomDetail1);

		BBomDetail a2 = new BBomDetail();
		a2.setProductCode("129206751425AKB1");
		a2.setItemCode("129206751425AKB2");
		allChildByProductCode.add(a2);

		BBomDetail a3 = new BBomDetail();
		a3.setProductCode("129206751425AKB1");
		a3.setItemCode("129206751425AKB3");
		allChildByProductCode.add(a3);
		PowerMockito.when(BasicsettingRemoteService.getBomListofDipNew(any())).thenReturn(allChildByProductCode);
		service.dipBoardScanning(param);
		param.setScanByWorkStation(false);
		service.dipBoardScanning(param);
		List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
		WipScanHistory wipScanHistory = new WipScanHistory();
		wipScanHistory.setSn("77788890002");
		wipScanHistory.setCraftSection("ICT");
		wipScanHistoryList.add(wipScanHistory);
		wipScanHistory.setSn("77788890003");
		wipScanHistory.setCraftSection("ICT");
		wipScanHistoryList.add(wipScanHistory);
		PowerMockito.when(wipScanHistoryService.getOverBoardList(Mockito.any())).thenReturn(wipScanHistoryList);
		service.dipBoardScanning(param);
		List<WipScanHistory> wipScanHistoryList1 = new ArrayList<>();
		PowerMockito.when(wipScanHistoryService.getOverBoardList(Mockito.any())).thenReturn(wipScanHistoryList1);
		Assert.assertNotNull(service.dipBoardScanning(param));
	}

	@Test
	public void getCommonItemLocationAndCode(){
		Map<String, List<BSmtBomDetail>> woSmtDetail = new HashMap<>();
		OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
		dto.setCurWorkOrder("2022507-SMT-A5501");
		dto.setTarWorkOrder("2022507-SMT-B5502");

		List<BSmtBomDetail> cur = new ArrayList<BSmtBomDetail>();
		List<BSmtBomDetail> tar = new ArrayList<BSmtBomDetail>();
		woSmtDetail.put(dto.getCurWorkOrder(),cur);
		woSmtDetail.put(dto.getTarWorkOrder(),tar);

		service.getCommonItemLocationAndCode(woSmtDetail,dto);
        BSmtBomDetail bs1 = new BSmtBomDetail();
        bs1.setLocationNo("2-35-1");
        bs1.setItemCode("046030200050");
		cur.add(bs1);

		BSmtBomDetail bs2 = new BSmtBomDetail();
		bs2.setLocationNo("4-1-1");
		bs2.setItemCode("045020100180");
		tar.add(bs2);
		service.getCommonItemLocationAndCode(woSmtDetail,dto);

		tar.add(bs1);
		Assert.assertNotNull(service.getCommonItemLocationAndCode(woSmtDetail,dto));
	}

	@Test
	public void compareBomAndPrepare () throws Exception {
		OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
		dto.setCurWorkOrder("2022507-SMT-A5501");
		dto.setTarWorkOrder("2022507-SMT-B5502");

		Map<String, List<BSmtBomDetail>> woSmtDetail = new HashMap<>();
		List<String> sameLocationItems = new ArrayList<>();

		PsWorkOrderSmtDTO tarSmt = new PsWorkOrderSmtDTO();
		tarSmt.setWorkOrderNo("2022507-SMT-A5501");

		// 上料
		List<BSmtBomDetail> cur = new ArrayList<BSmtBomDetail>();
		List<BSmtBomDetail> tar = new ArrayList<BSmtBomDetail>();
		woSmtDetail.put(dto.getCurWorkOrder(),cur);
		woSmtDetail.put(dto.getTarWorkOrder(),tar);

		BSmtBomDetail bs1 = new BSmtBomDetail();
		bs1.setLocationNo("2-35-1");
		bs1.setItemCode("046030200050");
		cur.add(bs1);

		BSmtBomDetail bs2 = new BSmtBomDetail();
		bs2.setLocationNo("4-1-1");
		bs2.setItemCode("045020100180");
		tar.add(bs2);

		sameLocationItems.add("4-1-1045020100180");
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		// 提前备料
		List<SmtMachineMaterialPrepare> prepares = new ArrayList<>();
		SmtMachineMaterialPrepare s1 = new SmtMachineMaterialPrepare();
		s1.setLocationNo(bs2.getLocationNo());
		s1.setItemCode(bs2.getItemCode());
		s1.setCreateDate(new Date());
		prepares.add(s1);
		SmtMachineMaterialPrepare s2 = new SmtMachineMaterialPrepare();
		s2.setLocationNo(bs1.getLocationNo());
		s2.setItemCode(bs1.getItemCode());
		s2.setCreateDate(new Date());
		prepares.add(s2);
		PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(Mockito.any())).thenReturn(null);
		tar.add(bs1);

		SmtMachineMaterialPrepare s3 = new SmtMachineMaterialPrepare();
		s3.setLocationNo("2-39-2");
		s3.setItemCode("045020100092");
		prepares.add(s3);
		dto.setCurWorkOrder(null);

		PowerMockito.when(PlanscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(any(),any(),any()))
				.thenReturn(null);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		PsWorkOrderSmt curWorkOrderSmt = new PsWorkOrderSmt();
		curWorkOrderSmt.setCfgHeaderId("");
		PowerMockito.when(PlanscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(any(),any(),any()))
				.thenReturn(curWorkOrderSmt);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		curWorkOrderSmt.setCfgHeaderId("1111");
		PowerMockito.when(PlanscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(any(),any(),any()))
				.thenReturn(curWorkOrderSmt);

		PowerMockito.when(smtBomDetailRepository.selectBSmtBomDetailListById(any()))
				.thenReturn(null);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		List<BSmtBomDetail> bomDetails = new ArrayList<>();
		BSmtBomDetail detail = new BSmtBomDetail();
		bomDetails.add(detail);
		PowerMockito.when(smtBomDetailRepository.selectBSmtBomDetailListById(any()))
				.thenReturn(bomDetails);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		dto.setCurWorkOrder("2022507-SMT-A5501");
		PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(Mockito.any())).thenReturn(prepares);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);
		// 有不是共用料的
		BSmtBomDetail bs3 = new BSmtBomDetail();
		bs3.setLocationNo("1-39-1");
		bs3.setItemCode("046050200038");
		tar.add(bs3);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		// 都是共用料,另一面指令提前备料数据为空
		cur.add(bs3);
		PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeederByCurWorkOrder(Mockito.any())).thenReturn(null);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		// 另一面指令提前备料数据不为空，未备料数据不全在另一面指令的提前备料数据中
		PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeederByCurWorkOrder(Mockito.any())).thenReturn(prepares);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);

		// 未备料数据全在另一面指令的提前备料数据中
		SmtMachineMaterialPrepare s4 = new SmtMachineMaterialPrepare();
		s4.setLocationNo("1-39-1");
		s4.setItemCode("046050200038");
		s4.setCreateDate(new Date());
		List<SmtMachineMaterialPrepare> prepares1 = new ArrayList<>(prepares);
		prepares1.add(s4);
		SmtMachineMaterialPrepare s5 = new SmtMachineMaterialPrepare();
		s5.setLocationNo("1-39-1");
		s5.setItemCode("046050200038");
		s5.setCreateDate(new Date("2021/07/14"));
		prepares1.add(s5);
		SmtMachineMaterialPrepare s6 = new SmtMachineMaterialPrepare();
		s6.setLocationNo("1-39-1");
		s6.setItemCode("046050200038");
		s6.setCreateDate(new Date("2025/03/14"));
		prepares1.add(s6);
		PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeederByCurWorkOrder(Mockito.any())).thenReturn(prepares1);
		compareBomAndPrepareInvoke(dto, woSmtDetail, tarSmt, sameLocationItems);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	public void compareBomAndPrepareInvoke(OneKeySwitchMoutingDTO dto, Map<String, List<BSmtBomDetail>> woSmtDetail,
										   PsWorkOrderSmtDTO tarSmt, List<String> sameLocationItems){
		try{
			Whitebox.invokeMethod(service, "compareBomAndPrepare", dto, woSmtDetail, tarSmt, sameLocationItems);
		}catch(Exception e){
			Assert.assertEquals(MessageId.WORK_ORDER_MODULE_NO_PREPARE,e.getMessage());
		}
	}

    @Test
    public void updateMoutingByIdForFeederChange() throws Exception {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        record.setMachineMaterialMoutingId("TEST");
        service.updateMoutingByIdForFeederChange(record);
        Assert.assertNotNull(record);
    }

	@Test
	public void checkCraftSectionTest() throws Exception {
		OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
		dto.setCurWorkOrder("2022507-SMT-A5501");
		dto.setTarWorkOrder("2022507-SMT-B5502");
		dto.setLineCode("SMT-0001");
		dto.setModuleNo("(X4S-1)3");
		PowerMockito.when(PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(Mockito.any())).thenReturn(null);
		Whitebox.invokeMethod(service, "checkCraftSection", dto);

		List<PsEntityPlanBasic> psEntityPlanBasicList = new ArrayList<>();
		PsEntityPlanBasic psEntityPlanBasic1 = new PsEntityPlanBasic();
		psEntityPlanBasic1.setWorkOrderNo("2022507-SMT-B5502");
		psEntityPlanBasic1.setRemark("0");
		psEntityPlanBasicList.add(psEntityPlanBasic1);
		Whitebox.invokeMethod(service, "checkCraftSection", dto);

		PsEntityPlanBasic psEntityPlanBasic2 = new PsEntityPlanBasic();
		psEntityPlanBasic2.setWorkOrderNo("2022507-SMT-A5501");
		psEntityPlanBasic2.setRemark("1");

		psEntityPlanBasicList.add(psEntityPlanBasic2);
		PowerMockito.when(PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(Mockito.any())).thenReturn(psEntityPlanBasicList);
		Whitebox.invokeMethod(service, "checkCraftSection", dto);

		psEntityPlanBasic1.setWorkOrderNo("2022507-SMT-B5503");
		psEntityPlanBasic1.setLineCode("SMT-TZ002");
		PowerMockito.when(PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(Mockito.any())).thenReturn(psEntityPlanBasicList);
		try {
			Whitebox.invokeMethod(service, "checkCraftSection", dto);
		} catch (MesBusinessException e) {
			Assert.assertTrue(e.getExMsgId().equals(MessageId.WORK_ORDER_NO_CFG_ID));
		}
		psEntityPlanBasic1.setCfgHeaderId("ea9d7675-5647-4741-bfcb-43c94e79a371");
		PowerMockito.when(PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(Mockito.any())).thenReturn(psEntityPlanBasicList);
		dto.setTarWorkOrder("2022507-SMT-A5501");
		PowerMockito.when(smtBomDetailRepository.countBomDetailByModuleNo(Mockito.any())).thenReturn(0);
		Whitebox.invokeMethod(service, "checkCraftSection", dto);

		PowerMockito.when(smtBomDetailRepository.countBomDetailByModuleNo(Mockito.any())).thenReturn(1);
		PowerMockito.when(historyLRepository.countHistoryByWorkOrder(Mockito.any())).thenReturn(0);
		try{
			Whitebox.invokeMethod(service, "checkCraftSection", dto);
		}catch (MesBusinessException e){
			Assert.assertEquals(MessageId.SWITCH_FIRST_WORK_ORDER,e.getExMsgId());
		}

		PowerMockito.when(historyLRepository.countHistoryByWorkOrder(Mockito.any())).thenReturn(1);
		Whitebox.invokeMethod(service, "checkCraftSection", dto);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

    @Test
    public void selectByLineAndWorkOrderNo() throws Exception {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineAndWorkOrderNo(Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getMountingByLineAndWorkOrder(record));
    }

    @Test
    public void selectByLineAndWorkOrderNoTwo() throws Exception {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        record.setWorkOrder("7119859-SMT-B5202");
        record.setLineCode("SMT-CS1");
        record.setObjectId("ZTE2022020900002");
        list.add(record);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineAndWorkOrderNo(Mockito.any(),Mockito.any())).thenReturn(list);
        PowerMockito.when(pkCodeInfoRepository.getListByPkCodes(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getMountingByLineAndWorkOrder(record));
    }

    @Test
    public void selectByLineAndWorkOrderNoThree() throws Exception {
        SmtMachineMaterialMouting record = new SmtMachineMaterialMouting();
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        record.setWorkOrder("7119859-SMT-B5202");
        record.setLineCode("SMT-CS1");
        record.setObjectId("ZTE2022020900002");
        list.add(record);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineAndWorkOrderNo(Mockito.any(),Mockito.any())).thenReturn(list);
        List<PkCodeInfo> tempList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("ZTE2022020900002");
        pkCodeInfo.setItemQty(new BigDecimal("10"));
        tempList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getListByPkCodes(Mockito.any())).thenReturn(tempList);
        Assert.assertNotNull(service.getMountingByLineAndWorkOrder(record));
    }

	@Test
	public void checkItemTypeAndSupplerCode() throws Exception {
		SmtMachineMaterialMouting oldMounting = new SmtMachineMaterialMouting();
		PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
		String oldPkCode = "1";
		String newPkCode = "2";

		oldMounting.setItemCode("123");
		List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo = new ArrayList<>();
		List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo1 = new ArrayList<>();
		PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyList())).thenReturn(null);
		try{
			Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);
		}catch (MesBusinessException e){
			Assert.assertEquals(MessageId.BINDLIST_IS_NULL,e.getExMsgId());
		}
		//getAbcType == null
		AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
		itemInfoNew.setItemNo("123");
		itemInfo1.add(itemInfoNew);
		PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyList())).thenReturn(itemInfo1);
		Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);

		itemInfoNew.setAbcType("A");
		itemInfo1.add(itemInfoNew);
		PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyList())).thenReturn(itemInfo1);


		List<PkCodeInfo> listByPkCodes = new ArrayList<>();
		PowerMockito.when(pkCodeInfoService.getListByPkCodes(Mockito.any())).thenReturn(listByPkCodes);

		try{
			Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);
		}catch (MesBusinessException e){
			Assert.assertEquals(MessageId.PK_CODE_NOT_EXISTS,e.getExMsgId());
		}

		PkCodeInfo dto = new PkCodeInfo();
		dto.setSupplerCode("123");
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setSupplerCode("121");
		listByPkCodes.add(dto);
		listByPkCodes.add(dto1);
		PowerMockito.when(pkCodeInfoService.getListByPkCodes(Mockito.any())).thenReturn(listByPkCodes);
		Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);

		PkCodeInfo dto2 = new PkCodeInfo();
		dto2.setSupplerCode("121");
		listByPkCodes.add(dto2);
		PowerMockito.when(pkCodeInfoService.getListByPkCodes(Mockito.any())).thenReturn(listByPkCodes);
		Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);

	}

	@Test
	public void checkItemTypeAndSupplerCode2() throws Exception {
		SmtMachineMaterialMouting oldMounting = new SmtMachineMaterialMouting();
		PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
		String oldPkCode = "1";
		String newPkCode = "2";

		List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo1 = new ArrayList<>();

		Assert.assertEquals("1",oldPkCode);
		AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
		itemInfoNew.setAbcType("123");
		itemInfo1.add(itemInfoNew);
		PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyList())).thenReturn(itemInfo1);
		Whitebox.invokeMethod(service, "checkItemTypeAndSupplerCode", oldPkCode, newPkCode, oldMounting, entity);


	}
}