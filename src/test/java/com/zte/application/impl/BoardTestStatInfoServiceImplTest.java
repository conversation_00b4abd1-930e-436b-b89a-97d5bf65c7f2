package com.zte.application.impl;

import com.zte.application.BoardTestStatInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.JsonConvertUtil;
import junit.framework.TestCase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, RedisHelper.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class})
public class BoardTestStatInfoServiceImplTest extends TestCase {
    @InjectMocks
    BoardTestStatInfoServiceImpl service;
    @Mock
    BoardTestStatInfoRepository repository;
    @Mock
    PsWipInfoRepository wipInfoRepository;
    @Mock
    WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
    }

    @Test
    public void isTestCraftSectionListTest() throws Exception{
        // 模拟输入数据
        List<PsTaskBasicDTO> testCraftSectionList = new ArrayList<>();
        List<String> repairList = new ArrayList<>();

        // 执行方法
        Whitebox.invokeMethod(service,"isTestCraftSectionList",repairList, testCraftSectionList);
        testCraftSectionList.add(new PsTaskBasicDTO());
        Whitebox.invokeMethod(service,"isTestCraftSectionList",repairList, testCraftSectionList);

        Assert.assertEquals(repairList.size(),1);
    }

    @Test
    public void statBoardTestPassRate() throws Exception {
        List<PsTaskBasicDTO> psTaskDTOList = new ArrayList<>();
        PsTaskBasicDTO dto = new PsTaskBasicDTO();
        PsTaskBasicDTO dto1 = new PsTaskBasicDTO();
        dto.setProdplanId("7777888");
        dto.setCraftSection("Test");
        dto.setRouteId("1111222333");
        dto.setWorkOrderNo("7777888-Test-B5012");
        dto.setTaskQty(new BigDecimal(10));
        dto.setItemNo("100000545444ABB");
        dto1.setProdplanId("7777888");
        dto1.setCraftSection("Test1");
        dto1.setRouteId("1111222333");
        dto1.setWorkOrderNo("7777888-Test1-B5013");
        dto1.setTaskQty(new BigDecimal(10));
        dto1.setItemNo("100000545444ABB");
        psTaskDTOList.add(dto);
        psTaskDTOList.add(dto1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskAndWorkOrder(Mockito.anyObject(), any())).thenReturn(psTaskDTOList);

        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO dto2 = new SysLookupTypesDTO();
        SysLookupTypesDTO dto3 = new SysLookupTypesDTO();
        dto2.setLookupMeaning("Test");
        dto3.setLookupMeaning("Test1");
        types.add(dto2);
        types.add(dto3);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);

        List<BBomHeaderDTO> headerDTOList = new ArrayList<>();
        BBomHeaderDTO bBomHeaderDTO = new BBomHeaderDTO();
        bBomHeaderDTO.setVerNo("123");
        bBomHeaderDTO.setProductCode("100000545444ABB");
        headerDTOList.add(bBomHeaderDTO);
        PowerMockito.when(BasicsettingRemoteService.getBBomHeaderByProductCodeList(any())).thenReturn(headerDTOList);

        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO detailDTO = new CtRouteDetailDTO();
        detailDTO.setRouteId("1111222333");
        detailDTO.setCraftSection("Test");
        detailDTO.setProcessSeq(new BigDecimal(1));
        CtRouteDetailDTO detailDTO1 = new CtRouteDetailDTO();
        detailDTO1.setRouteId("1111222333");
        detailDTO1.setCraftSection("Test1");
        detailDTO1.setProcessSeq(new BigDecimal(2));
        CtRouteDetailDTO detailDTO2 = new CtRouteDetailDTO();
        detailDTO2.setRouteId("1111222333");
        detailDTO2.setCraftSection("入库");
        detailDTO2.setProcessSeq(new BigDecimal(3));
        ctRouteDetailDTOList.add(detailDTO);
        ctRouteDetailDTOList.add(detailDTO1);
        ctRouteDetailDTOList.add(detailDTO2);
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(any(), Mockito.anyString())).thenReturn(ctRouteDetailDTOList);

        List<BoardTestStatInfo> nextWorkOrderSnList = new ArrayList<>();
        BoardTestStatInfo testStatInfo = new BoardTestStatInfo();
        testStatInfo.setWorkOrderNo("7777888-Test1-B5013");
        testStatInfo.setTestQty(1);
        nextWorkOrderSnList.add(testStatInfo);
        PowerMockito.when(wipScanHistoryRepository.getNextTestQty(any(), Mockito.anyList(), Mockito.anyList())).thenReturn(nextWorkOrderSnList);
		List<BoardTestStatInfo> maps = new ArrayList<>();
		BoardTestStatInfo testStatInfo11 = new BoardTestStatInfo();
		testStatInfo11.setWorkOrderNo("7777888");
		testStatInfo11.setProdplanId("7777888");
		testStatInfo11.setTestQty(1);
		maps.add(testStatInfo11);
		PowerMockito.when(wipInfoRepository.getInStoreSnMap(any(),Mockito.anyList())).thenReturn(maps);
        List<BoardTestStatInfo> pmRepairRcvDetailList = new ArrayList<>();
        Map<String,Object> map1 = new HashMap<>();
        map1.put("prodplanId", "7777888");
        map1.put("craftSection", "Test");
        map1.put("defectQty", 1);
        Map<String,Object> map2 = new HashMap<>();
        map2.put("prodplanId", "7777888");
        map2.put("craftSection", "Test1");
        map2.put("defectQty", 1);
        BoardTestStatInfo testStatInfo1 = new BoardTestStatInfo();
        testStatInfo1.setProdplanId("7777888");
        testStatInfo1.setTestCraftSection("Test");
        testStatInfo1.setTestDefectQty(1);
        pmRepairRcvDetailList.add(testStatInfo1);
        BoardTestStatInfo testStatInfo2 = new BoardTestStatInfo();
        testStatInfo2.setProdplanId("7777888");
        testStatInfo2.setTestCraftSection("Test1");
        testStatInfo2.setTestDefectQty(1);
        pmRepairRcvDetailList.add(testStatInfo2);

        PowerMockito.when(pmRepairRcvDetailRepository.getRepairSnList(any())).thenReturn(pmRepairRcvDetailList);

        Map<String,String> lineMap = new HashMap<>();
        lineMap.put("SMT-A", "SMT-A");
        PowerMockito.when(BasicsettingRemoteService.getSmtAndDipLine()).thenReturn(lineMap);
        PowerMockito.when(repository.batchInsertOrUpdate(any())).thenReturn(1);
        try {
            service.statBoardTestPassRate("10313234", dto);
        } catch (Exception e) {
        }
        List<List<BoardTestStatInfo>> boardTestStatInfoListS = new ArrayList<>();
        List<BoardTestStatInfo> boardTestStatInfoList = new ArrayList<>();
        BoardTestStatInfo statInfo = new BoardTestStatInfo();
        statInfo.setTestCraftSection("123");
        boardTestStatInfoList.add(statInfo);
        boardTestStatInfoListS.add(boardTestStatInfoList);
        Assert.assertNotNull(service.batchInsertOrUpdate(boardTestStatInfoListS));
    }

    @Test
    public void queryBoardTestPassRate() throws Exception {
        BoardTestStatInfoDTO dto = new BoardTestStatInfoDTO();
        dto.setIsRealTime("Y");
        dto.setProdplanId("123123");
        List<PsTaskBasicDTO> psTaskDTOList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskAndWorkOrder(Mockito.anyObject(), any())).thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskAndWorkOrder(Mockito.anyObject(), any())).thenReturn(psTaskDTOList);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        try{
            service.queryBoardTestPassRate(dto, "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto.setIsRealTime("N");
        dto.setStartDate("2022-10-01 00:00:00");
        dto.setEndDate("2022-11-01 00:00:00");
        dto.setPage(1L);
        dto.setRows(10L);
        Assert.assertNotNull(service.queryBoardTestPassRate(dto, "123"));
        PowerMockito.when(repository.getPage(any())).thenReturn(Collections.singletonList(new BoardTestStatInfo()));
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(new ArrayList<>());
        service.queryBoardTestPassRate(dto, "123");
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(Collections.singletonList(new BProdBomHeaderDTO()));
        service.queryBoardTestPassRate(dto, "123");
    }

    @Test
    public void computeNotInStorePassRate() throws Exception {
        List<BBomHeaderDTO> headerDTOList = new ArrayList<>();
        BoardTestStatInfo boardTestStatInfo = new BoardTestStatInfo();
        Map<String, List<PsTaskBasicDTO>> prodplanMap = new HashMap<>();
        List<BoardTestStatInfo> boardTestStatInfoList = new ArrayList<>();
        Map<String, String> lineMap = new HashMap<>();
        Map<String, String> notInStoreMap = new HashMap<>();
        boardTestStatInfo.setNotInStoreMap(notInStoreMap);
        notInStoreMap.put("111,1", "111a");
        notInStoreMap.put("222,1", "222a");
        notInStoreMap.put("333,1", "333a");

        List<PsTaskBasicDTO> psTaskBasicDTOList = new ArrayList<>();
        PsTaskBasicDTO psTaskBasicDTO = new PsTaskBasicDTO();
        psTaskBasicDTOList.add(psTaskBasicDTO);
        psTaskBasicDTO.setItemNo("ItemNo111");
        psTaskBasicDTO.setTaskQty(BigDecimal.TEN);
        psTaskBasicDTO.setCraftSection(Constant.CRAFTSECTION_SMT_A);

        prodplanMap.put("111", psTaskBasicDTOList);
        PsTaskBasicDTO psTaskBasicDTO1 = new PsTaskBasicDTO();
        psTaskBasicDTOList.add(psTaskBasicDTO1);
        psTaskBasicDTO1.setItemNo("ItemNo111");
        psTaskBasicDTO1.setTaskQty(BigDecimal.TEN);
        psTaskBasicDTO1.setCraftSection(Constant.CRAFTSECTION_SMT_A);
        prodplanMap.put("222", psTaskBasicDTOList);

        Map<String, Integer> testQtyMap = new HashMap<>();
        testQtyMap.put("111a", 3);
        boardTestStatInfo.setNotInStoreTestQtyMap(testQtyMap);

        Map<String, Integer> testRepairMap = new HashMap<>();
        boardTestStatInfo.setTestRepairMap(testRepairMap);

        testRepairMap.put("1111",1);
        Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        try {
            Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                    boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        } catch (Exception e) {
        }

        psTaskBasicDTO.setCraftSection(Constant.CRAFTSECTION_SMT_B);
        Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        try {
            Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                    boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        } catch (Exception e) {
        }

        psTaskBasicDTO.setCraftSection(Constant.DIP_CRAFT_STR);
        Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        try {
            Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                    boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        } catch (Exception e) {
        }

        psTaskBasicDTO.setCraftSection("1111");
        Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        try {
            Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                    boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        } catch (Exception e) {
        }

        psTaskBasicDTO.setTaskQty(null);
        Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        try {
            Whitebox.invokeMethod(service, "computeNotInStorePassRate", headerDTOList,
                    boardTestStatInfo, prodplanMap, boardTestStatInfoList, lineMap);
        } catch (Exception e) {
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateFirstWarehouseDate() throws Exception {
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.updateFirstWarehouseDate("7000001", "51", "10313234");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_WAREHOUSE_DATE_IN, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);

        PowerMockito.when(repository.getCount(any())).thenReturn(1L);
        PowerMockito.when(repository.getProdplanPage(any())).thenReturn(new ArrayList<>());
        try {
            service.updateFirstWarehouseDate("7000001", "51", "10313234");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_WAREHOUSE_DATE_IN, e.getMessage());
        }
        List<String> prodList = new ArrayList<>();
        prodList.add("7000001");
        PowerMockito.when(repository.getProdplanPage(any())).thenReturn(prodList);

        PowerMockito.when(PlanscheduleRemoteService.getFirstWarehouseDate(any())).thenReturn(new ArrayList<>());
        try {
            service.updateFirstWarehouseDate("7000001", "51", "10313234");
        } catch (Exception e) {}
        List<PsTaskBasicDTO> psTaskDTOList = new ArrayList<>();
        PsTaskBasicDTO dto = new PsTaskBasicDTO();
        dto.setProdplanId("7000001");
        psTaskDTOList.add(dto);
        PowerMockito.when(PlanscheduleRemoteService.getFirstWarehouseDate(any())).thenReturn(psTaskDTOList);
        Assert.assertNotNull(service.updateFirstWarehouseDate("7000001", "51", "10313234"));
    }

    @Test
    public void queryRepairSnQty () throws Exception {
        List<String> repairList = new ArrayList<>();
        Whitebox.invokeMethod(service, "queryRepairSnQty", repairList);
        repairList.add("123");
        Whitebox.invokeMethod(service, "queryRepairSnQty", repairList);
        PowerMockito.when(pmRepairRcvDetailRepository.getRepairSnList(any())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "queryRepairSnQty", repairList);
        List<BoardTestStatInfo> list = new ArrayList<>();

        BoardTestStatInfo testStatInfo = new BoardTestStatInfo();
        testStatInfo.setProdplanId("123");
        list.add(testStatInfo);
        testStatInfo.setTestQty(1);
        PowerMockito.when(pmRepairRcvDetailRepository.getRepairSnList(any())).thenReturn(list);
        Whitebox.invokeMethod(service, "queryRepairSnQty", repairList);
        testStatInfo.setTestDefectQty(10);
        testStatInfo.setTestCraftSection("10");
        testStatInfo.setProdplanId("321");
        PowerMockito.when(pmRepairRcvDetailRepository.getRepairSnList(any())).thenReturn(list);
        Whitebox.invokeMethod(service, "queryRepairSnQty", repairList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryInStoreTestQty () throws Exception {
        List<String> inStoreList = new ArrayList<>();
        List<String> inStoreRepairList = new ArrayList<>();
        Whitebox.invokeMethod(service, "queryInStoreTestQty", inStoreList, inStoreRepairList);
        inStoreList.add("123");
        inStoreRepairList.add("123");
        Whitebox.invokeMethod(service, "queryInStoreTestQty", inStoreList, inStoreRepairList);
        PowerMockito.when(wipInfoRepository.getInStoreSnMap(any(),Mockito.anyList())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "queryInStoreTestQty", inStoreList, inStoreRepairList);
        List<BoardTestStatInfo> maps = new ArrayList<>();
        BoardTestStatInfo testStatInfo = new BoardTestStatInfo();
        testStatInfo.setWorkOrderNo("123");
        maps.add(testStatInfo);
        PowerMockito.when(wipInfoRepository.getInStoreSnMap(any(),Mockito.anyList())).thenReturn(maps);
        Whitebox.invokeMethod(service, "queryInStoreTestQty", inStoreList, inStoreRepairList);
        testStatInfo.setTestQty(10);
        PowerMockito.when(wipInfoRepository.getInStoreSnMap(any(),Mockito.anyList())).thenReturn(maps);
        Whitebox.invokeMethod(service, "queryInStoreTestQty", inStoreList, inStoreRepairList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryNextWorkOrderTestQty () throws Exception {
        List<String> notInStoreList = new ArrayList<>();
        Whitebox.invokeMethod(service, "queryNextWorkOrderTestQty", notInStoreList, new ArrayList<>(), new ArrayList<>());
        notInStoreList.add("123");
        Whitebox.invokeMethod(service, "queryNextWorkOrderTestQty", notInStoreList, new ArrayList<>(), new ArrayList<>());
        PowerMockito.when(wipScanHistoryRepository.getNextTestQty(any(), Mockito.anyList(), Mockito.anyList())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "queryNextWorkOrderTestQty", notInStoreList, new ArrayList<>(), new ArrayList<>());
        List<BoardTestStatInfo> maps = new ArrayList<>();
        BoardTestStatInfo testStatInfo = new BoardTestStatInfo();
        testStatInfo.setWorkOrderNo("123");
        maps.add(testStatInfo);
        PowerMockito.when(wipScanHistoryRepository.getNextTestQty(any(), Mockito.anyList(), Mockito.anyList())).thenReturn(maps);
        Whitebox.invokeMethod(service, "queryNextWorkOrderTestQty", notInStoreList, new ArrayList<>(), new ArrayList<>());
        testStatInfo.setTestQty(10);
        PowerMockito.when(wipScanHistoryRepository.getNextTestQty(any(), Mockito.anyList(), Mockito.anyList())).thenReturn(maps);
        Whitebox.invokeMethod(service, "queryNextWorkOrderTestQty", notInStoreList, new ArrayList<>(), new ArrayList<>());String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

}