package com.zte.application.impl;

import com.zte.domain.model.ParentsnAndSn;
import com.zte.domain.model.ParentsnAndSnRepository;
import com.zte.interfaces.dto.ParentsnAndSnDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ParentsnAndSnServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ParentsnAndSnServiceImpl service;

    @Mock
    private ParentsnAndSnRepository parentsnAndSnRepository;

    @Before
    public void init() {
        service.afterPropertiesSet();
    }

    @Test
    public void generateSnTrans() throws Exception {
        List<ParentsnAndSnDTO> parentsnAndSnDTOS = new ArrayList<>();
        ParentsnAndSnDTO parentsnAndSn = new ParentsnAndSnDTO();
        parentsnAndSn.setSn("11");
        parentsnAndSnDTOS.add(parentsnAndSn);
        PowerMockito.when(parentsnAndSnRepository.batchInsert(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(parentsnAndSnRepository.deleteParentsnAndSnBysn(Mockito.anyList())).thenReturn(1);
        Assert.assertEquals(1,service.batchInsert(parentsnAndSnDTOS));
    }

    @Test
    public void getSnByBarcode() {
        List<String> result = new ArrayList<>();
        result.add("11111");
        PowerMockito.when(parentsnAndSnRepository.getSnByParentSn(Mockito.any())).thenReturn(result);
        List<String> snList = service.getSnByBarcode("111");
        Assert.assertTrue(snList.size() == result.size());

        PowerMockito.when(parentsnAndSnRepository.getSnByParentSn(Mockito.any())).thenReturn(null);
        PowerMockito.when(parentsnAndSnRepository.getSameParentsnList(Mockito.any())).thenReturn(null);
        snList = service.getSnByBarcode("111");
        Assert.assertNull(snList);
    }


    @Test
    public void getBySns() {
        List<String> sns = new ArrayList<>();
        sns.add("11111");
        Map<String, String> snList = service.getBySns(sns);
        Assert.assertEquals(snList.size(),0);
    }
    @Test
    public void getAllSubSn() {
        List<String> sns = new ArrayList<>();
        sns.add("11111");
        Assert.assertEquals(parentsnAndSnRepository.getAllSubSn(sns).size(),0);
    }

    @Test
    public void getSplicingBoardSn() {
        PowerMockito.when(parentsnAndSnRepository.getSplicingBoardSn(Mockito.any())).thenReturn(null);
        service.getSplicingBoardSn("123");
        Assert.assertNull(service.getSplicingBoardSn("123"));

        ParentsnAndSn parentsnAndSn = new ParentsnAndSn();
        parentsnAndSn.setParentSn("123");
        parentsnAndSn.setSn("1,2,3,4");
        PowerMockito.when(parentsnAndSnRepository.getSplicingBoardSn(Mockito.any())).thenReturn(parentsnAndSn);
        Assert.assertNotNull(service.getSplicingBoardSn("123"));
    }

    @Test
    public void getSnByParentSnList(){
        List<String> snList = new ArrayList<>();
        Map<String, String> map1 = service.getSnByParentSnList(snList);
        Assert.assertEquals(0,map1.size());
        List<ParentsnAndSn> list = new ArrayList<>();
        list.add(new ParentsnAndSn(){{setSn("111");setParentSn("123");}});
        list.add(new ParentsnAndSn(){{setSn("222");setParentSn("123");}});
        PowerMockito.when(parentsnAndSnRepository.getSnByParentSnList(Mockito.any())).thenReturn(list);

        snList.add("111");
        Map<String, String> map = service.getSnByParentSnList(snList);
        Assert.assertEquals("111,222",map.get("123"));
    }
}