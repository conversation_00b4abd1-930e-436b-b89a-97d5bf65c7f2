package com.zte.application.impl;

import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.TaskBarcodeQuery;
import com.zte.domain.model.TaskBarcodeQueryRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.TaskBarcodeQueryDTO;
import com.zte.interfaces.dto.mds.MdsNameBoardDTO;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
@PrepareForTest({PlanscheduleRemoteService.class})
public class TaskBarcodeQueryServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private TaskBarcodeQueryServiceImpl service;

    @Mock
    private TaskBarcodeQueryRepository taskBarcodeQueryRepository;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Test
    public void getListByPage() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        TaskBarcodeQueryDTO dto = new TaskBarcodeQueryDTO();
        List<TaskBarcodeQuery> list = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setWorkOrderNo("test123");
        list.add(taskBarcodeQuery);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("test123");
        psWorkOrderBasic.setRouteId("test123");
        workOrderList.add(psWorkOrderBasic);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPage(Mockito.any())).thenReturn(list);
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.any())).thenReturn(workOrderList);
        try{
            service.getListByPage(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        List<MdsNameBoardDTO> nameBoards = new ArrayList<>();
        MdsNameBoardDTO mdsNameBoardDTO = new MdsNameBoardDTO();
        mdsNameBoardDTO.setNameboard("Nameboard");
        mdsNameBoardDTO.setPartcode("Partcode");
        nameBoards.add(mdsNameBoardDTO);
        PowerMockito.when(mdsRemoteService.getNameBoard(Mockito.any())).thenReturn(nameBoards);
        try{
            service.getListByPage(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void queryExportData() throws Exception {
        TaskBarcodeQueryDTO taskBarcodeQueryDTO = new TaskBarcodeQueryDTO();
        taskBarcodeQueryDTO.setAttribute2("ZZ190816CPE005-CS浙江电信");
        taskBarcodeQueryDTO.setSn("742953302297");

        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setAttribute2("ZZ190816CPE005-CS浙江电信");
        taskBarcodeQuery.setSn("742953302297");
        taskBarcodeQuery.setWorkOrderNo("test123");
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<TaskBarcodeQuery> list = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery1 = new TaskBarcodeQuery();
        taskBarcodeQuery1.setAttribute2("ZZ190816CPE005-CS浙江电信");
        taskBarcodeQuery1.setSn("742953302298");
        taskBarcodeQuery1.setWorkOrderNo("test123");
        taskBarcodeQuery1.setIsPrint(1);
        list.add(taskBarcodeQuery);
        list.add(taskBarcodeQuery1);
        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("test123");
        psWorkOrderBasic.setRouteId("test123");
        workOrderList.add(psWorkOrderBasic);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPage(Mockito.any())).thenReturn(list);
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(Mockito.any())).thenReturn(workOrderList);

        List<MdsNameBoardDTO> nameBoards = new ArrayList<>();
        MdsNameBoardDTO mdsNameBoardDTO = new MdsNameBoardDTO();
        mdsNameBoardDTO.setNameboard("Nameboard");
        mdsNameBoardDTO.setPartcode("Partcode");
        nameBoards.add(mdsNameBoardDTO);
        PowerMockito.when(mdsRemoteService.getNameBoard(Mockito.any())).thenReturn(nameBoards);
        Assert.assertNotNull(service.queryExportData(taskBarcodeQueryDTO,1,10));
    }

    @Test
    public void countExportTotal() throws Exception {
        TaskBarcodeQueryDTO taskBarcodeQueryDTO = new TaskBarcodeQueryDTO();
        taskBarcodeQueryDTO.setAttribute2("ZZ190816CPE005-CS浙江电信");
        taskBarcodeQueryDTO.setSn("742953302297");

        Assert.assertNotNull(service.countExportTotal(taskBarcodeQueryDTO));
    }
}