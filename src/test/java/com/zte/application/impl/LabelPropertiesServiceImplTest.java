package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.HttpClientUtils;
import com.zte.domain.model.LabelPropertiesMapper;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.LabelPropertiesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.mds.MdsNameBoardDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({BasicsettingRemoteService.class, HttpRemoteUtil.class,
        HttpClientUtils.class, ConstantInterface.class, CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, CommonUtils.class, SpringContextUtil.class
})
public class LabelPropertiesServiceImplTest extends PowerBaseTestCase {


    @InjectMocks
    private LabelPropertiesServiceImpl service;

    @Mock
    private LabelPropertiesMapper labelPropertiesMapper;
    @Mock
    private MdsRemoteService mdsRemoteService;

    @Test
    public void testBatchInsertOrUpdate() {
        Assert.assertThrows(Exception.class, () -> service.batchInsertOrUpdate("10311279", null));
    }

    @Test
    public void testBatchInsertOrUpdate1() {
        List<LabelPropertiesDTO> list = new ArrayList<>();
        for (int i = 0; i < 201; i++) {
            LabelPropertiesDTO labelPropertiesDTO = new LabelPropertiesDTO();
            list.add(labelPropertiesDTO);
        }
        Assert.assertThrows(Exception.class, () -> service.batchInsertOrUpdate("10311279", list));
    }

    @Test
    public void testBatchInsertOrUpdate2() {
        List<LabelPropertiesDTO> list = new ArrayList<>();
        LabelPropertiesDTO labelPropertiesDTO1 = new LabelPropertiesDTO();
        labelPropertiesDTO1.setLabelProperty("l1");
        labelPropertiesDTO1.setSn("s1");
        list.add(labelPropertiesDTO1);
        LabelPropertiesDTO labelPropertiesDTO2 = new LabelPropertiesDTO();
        labelPropertiesDTO2.setLabelProperty("l2");
        labelPropertiesDTO2.setSn("s2");
        list.add(labelPropertiesDTO2);
        service.batchInsertOrUpdate("10311279", list);
        Assert.assertNotNull(list);
    }

    @Test
    public void testBatchInsertOrUpdate3() {
        List<LabelPropertiesDTO> list = new ArrayList<>();
        LabelPropertiesDTO labelPropertiesDTO1 = new LabelPropertiesDTO();
        labelPropertiesDTO1.setLabelProperty("l1");
        labelPropertiesDTO1.setSn("s1");
        list.add(labelPropertiesDTO1);
        LabelPropertiesDTO labelPropertiesDTO2 = new LabelPropertiesDTO();
        labelPropertiesDTO2.setLabelProperty("l2");
        labelPropertiesDTO2.setSn("s2");
        list.add(labelPropertiesDTO2);
        PowerMockito.when(labelPropertiesMapper.selectBatchBySn(any())).thenReturn(Lists.newArrayList("s1", "s2"));
        service.batchInsertOrUpdate("10311279", list);
        Assert.assertNotNull(list);
    }

    @Test
    public void testBatchInsertOrUpdate4() {
        List<LabelPropertiesDTO> list = new ArrayList<>();
        List<LabelPropertiesDTO> existList = new ArrayList<>();
        LabelPropertiesDTO labelPropertiesDTO1 = new LabelPropertiesDTO();
        labelPropertiesDTO1.setLabelProperty("l1");
        labelPropertiesDTO1.setSn("s1");
        list.add(labelPropertiesDTO1);
        LabelPropertiesDTO labelPropertiesDTO2 = new LabelPropertiesDTO();
        labelPropertiesDTO2.setLabelProperty("l2");
        labelPropertiesDTO2.setSn("s2");
        list.add(labelPropertiesDTO2);
        existList.add(labelPropertiesDTO2);
        LabelPropertiesDTO labelPropertiesDTO3 = new LabelPropertiesDTO();
        labelPropertiesDTO3.setLabelProperty("l3");
        labelPropertiesDTO3.setSn("s3");
        existList.add(labelPropertiesDTO3);
        PowerMockito.when(labelPropertiesMapper.selectBatchBySn(any())).thenReturn(Lists.newArrayList("s3", "s2"));
        service.batchInsertOrUpdate("10311279", list);
        Assert.assertNotNull(list);
    }

    @Test
    public void getOuterBoxTemplate() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        Assert.assertThrows(MesBusinessException.class, () -> service.getOuterBoxTemplate(new ArrayList<>()));

        List<MdsNameBoardDTO> existList = new ArrayList<>();
        MdsNameBoardDTO labelPropertiesDTO2 = new MdsNameBoardDTO();
        labelPropertiesDTO2.setNameboard("l2");
        labelPropertiesDTO2.setPartcode("s2");
        existList.add(labelPropertiesDTO2);
        MdsNameBoardDTO labelPropertiesDTO3 = new MdsNameBoardDTO();
        labelPropertiesDTO3.setNameboard("l3");
        labelPropertiesDTO3.setPartcode("s3");
        existList.add(labelPropertiesDTO3);
        PowerMockito.when(mdsRemoteService.getNameBoard(any())).thenReturn(existList);

        List<SysLookupValuesDTO> directoryList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupMeaning("l2");
        s1.setAttribute1("l2");
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupMeaning("l3");
        s2.setAttribute1("l3");
        directoryList.add(s1);
        directoryList.add(s2);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getDirectoryList(Mockito.any())).thenReturn(directoryList);

        List<String> sns = new ArrayList<>();
        sns.add("s1");

        Assert.assertThrows(MesBusinessException.class, () -> service.getOuterBoxTemplate(sns));
        sns.clear();
        sns.add("s2");
        sns.add("s3");
        Assert.assertThrows(MesBusinessException.class, () -> service.getOuterBoxTemplate(sns));
        sns.clear();
        sns.add("s2");
        service.getOuterBoxTemplate(sns);
    }
}