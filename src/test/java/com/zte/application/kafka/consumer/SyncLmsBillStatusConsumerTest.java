package com.zte.application.kafka.consumer;

import com.zte.application.PsWipInfoService;
import com.zte.application.SysErrorLogService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.SyncBillStatusForKafKaDTO;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonConvertUtil.class, MESHttpHelper.class,MsgData.class, SyncBillStatusForKafKaDTO.class, DatabaseContextHolder.class})
public class SyncLmsBillStatusConsumerTest {
    @InjectMocks
    private SyncLmsBillStatusConsumer syncLmsBillStatusConsumer;
    @Mock
    private SysErrorLogService sysErrorLogService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Before
    public void init() {

        PowerMockito.mockStatic(JsonConvertUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(MsgData.class);
        PowerMockito.mockStatic(SyncBillStatusForKafKaDTO.class);
        PowerMockito.mockStatic(DatabaseContextHolder.class);

    }
    @Test
    public void consume() throws Exception {
        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("123");
        PowerMockito.when(warehouseEntryInfoService.batchUpdateWarehouseInfoStatus(Mockito.anyList(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(1);
        try{
            syncLmsBillStatusConsumer.consume(new ArrayList<>(),"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("");
        PowerMockito.when(warehouseEntryInfoService.batchUpdateWarehouseInfoStatus(Mockito.anyList(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(1);
        try{
            syncLmsBillStatusConsumer.consume(new ArrayList<>(),"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void testConsume() throws Exception {
        List<WarehouseEntryInfo> psWipInfoList=new ArrayList<>();
        WarehouseEntryInfo psWipInfo =new WarehouseEntryInfo();
        psWipInfo.setBillNo("00286523");
        psWipInfo.setFactoryId(new BigDecimal("52"));
        psWipInfoList.add(psWipInfo);
        MsgData<SyncBillStatusForKafKaDTO> msgData= new MsgData<>();
        SyncBillStatusForKafKaDTO syncBillStatusForKafKaDTO=new SyncBillStatusForKafKaDTO();
        syncBillStatusForKafKaDTO.setEmpNo("0028653");
        syncBillStatusForKafKaDTO.setWarehouseEntryInfoList(psWipInfoList);
        msgData.setData(syncBillStatusForKafKaDTO);
        String consumerRecord =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(msgData);
        PowerMockito.when(warehouseEntryInfoService.batchUpdateWarehouseInfoStatus(Mockito.anyList(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(1);
        Assert.assertEquals("00286523", psWipInfo.getBillNo());
        syncLmsBillStatusConsumer.consume(consumerRecord);
    }
}