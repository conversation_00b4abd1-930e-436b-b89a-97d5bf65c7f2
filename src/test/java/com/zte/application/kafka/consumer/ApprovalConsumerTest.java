package com.zte.application.kafka.consumer;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.IMESLogService;
import com.zte.application.RepairApprovalService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.KafkaConstant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.StorageCenterRemoteService;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @Author: Russell.pan
 * @Date: 2021/4/6 11:28
 * @Description:
 */
@PrepareForTest({BasicsettingRemoteService.class, StorageCenterRemoteService.class})
public class ApprovalConsumerTest extends PowerBaseTestCase {


    @InjectMocks
    private ApprovalConsumer approvalConsumer;

    @Mock
    private ConsumerRecord consumerRecord;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private IMESLogService imesLogService;

    @Mock
    private RepairApprovalService repairApprovalService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(StorageCenterRemoteService.class);
        ReflectUtil.setFieldValue(approvalConsumer,"appCode","appCode");
        ReflectUtil.setFieldValue(approvalConsumer,"repairApprovalFlowCode","imes-zte-mes-manufactureshare-repair-exception");
        ReflectUtil.setFieldValue(approvalConsumer,"empNo","00000000");
    }

    @Test
    public void kafkaMsgReceive()throws Exception
    {
        approvalConsumer.kafkaMsgReceive(null);
        approvalConsumer.kafkaMsgReceive(consumerRecord);

        when(consumerRecord.key()).thenReturn("10349620");
        approvalConsumer.kafkaMsgReceive(consumerRecord);

        when(consumerRecord.key()).thenReturn("appCode-10349620");
        approvalConsumer.kafkaMsgReceive(consumerRecord);

        ReflectUtil.setFieldValue(approvalConsumer,"factoryId","55");
        approvalConsumer.kafkaMsgReceive(consumerRecord);

        ReflectUtil.setFieldValue(approvalConsumer,"factoryId",",");
        approvalConsumer.kafkaMsgReceive(consumerRecord);

        when(factoryConfig.getFactoryDbById(any())).thenReturn("");
        approvalConsumer.kafkaMsgReceive("55","22","{}");
        when(factoryConfig.getFactoryDbById(any())).thenReturn("HY");
        approvalConsumer.kafkaMsgReceive("55","22","{}");
        approvalConsumer.kafkaMsgReceive("55","22","{\"flowCode\":\"imes-zte-mes-manufactureshare-repair-exception\"}");

        JSONObject jsonObject = JSONObject.parseObject("{\"nodeName\":\"工艺工程师\",\"approver\":\"10270446\",\"approvalDate\":1682064091000,\"nodeCode\":\"Activity_11knwg2\",\"opinionSource\":\"ICENTER_APPROVAL\",\"businessId\":\"GZ20230421003\",\"nodeStatus\":\"COMPLETED\",\"flowName\":\"工装夹具申请流程\",\"flowInstanceId\":\"flowM-FQKyIAibyEGSUT9IVhx\",\"opinion\":\"111\",\"result\":\"Y\",\"flowCode\":\"imes-zte-mes-manufactureshare-repair-exception\",\"tenantId\":\"10001\",\"extendedCode\":\"craftEngineer\",\"id\":\"njOolbQcX8bHpUYp7UFfBom\",\"extOpinion\":\"{}\",\"taskId\":\"taskwBg2twYVEK0D1R28W_4Cq\",\"createDate\":1682063345000}");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");
        jsonObject.put(KafkaConstant.RESULT,"N");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");

        jsonObject.put(KafkaConstant.FLOWCODE,"10349620");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");

        jsonObject.put(KafkaConstant.RESULT,"");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");

        jsonObject.put(KafkaConstant.OPINION_SOURCE,"ISS_APPROVAL");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");
        jsonObject.put(KafkaConstant.OPINION_SOURCE,"ISS_CENTER");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");

        jsonObject.put(KafkaConstant.OPINION_SOURCE,"");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskCompleted",jsonObject,"iMES");
        approvalConsumer.approvalCenterKafkaMessageCallback("iMES-taskReassign",jsonObject,"iMES");
    }
}