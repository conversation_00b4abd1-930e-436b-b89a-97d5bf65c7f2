package com.zte.application.kafka.consumer;

import com.zte.application.PsWipInfoService;
import com.zte.application.SysErrorLogService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfo;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.SyncSnImuForKafkaDTO;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonConvertUtil.class, MESHttpHelper.class,MsgData.class, SyncSnImuForKafkaDTO.class, DatabaseContextHolder.class})
public class SyncLmsSnImuConsumerTest {
    @InjectMocks
    private SyncLmsSnImuConsumer syncLmsSnImuConsumer;
    @Mock
    private SysErrorLogService sysErrorLogService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private PsWipInfoService psWipInfoService;
    @Before
    public void init() {

        PowerMockito.mockStatic(JsonConvertUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(MsgData.class);
        PowerMockito.mockStatic(SyncSnImuForKafkaDTO.class);
        PowerMockito.mockStatic(DatabaseContextHolder.class);

    }
    @Test
    public void consume() throws Exception {
        List<PsWipInfo> psWipInfoList=new ArrayList<>();
        PsWipInfo psWipInfo =new PsWipInfo();
        psWipInfo.setSn("00286523");
        psWipInfo.setFactoryId(new BigDecimal("52"));
        psWipInfo.setSourceSysName("612");
        psWipInfo.setWorkStation("612");
        psWipInfoList.add(psWipInfo);
        MsgData<SyncSnImuForKafkaDTO> msgData= new MsgData<>();
        SyncSnImuForKafkaDTO syncSnImuForKafkaDTO=new SyncSnImuForKafkaDTO();
        syncSnImuForKafkaDTO.setEmpNo("0028653");
        syncSnImuForKafkaDTO.setPsWipInfoList(psWipInfoList);
        msgData.setData(syncSnImuForKafkaDTO);
        String consumerRecord =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(msgData);
        PowerMockito.when(psWipInfoService.updatePsWipInfoBySnBatch(Mockito.anyList())).thenReturn(1);

        syncLmsSnImuConsumer.consume(consumerRecord);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testConsume() throws Exception {
        try{
            PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}");
            syncLmsSnImuConsumer.consume(new ArrayList<>(),"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("");
        PowerMockito.when(psWipInfoService.updatePsWipInfoBySnBatch(Mockito.anyList()));
        try{
            syncLmsSnImuConsumer.consume(new ArrayList<>(),"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("123");
        PowerMockito.when(psWipInfoService.updatePsWipInfoBySnBatch(Mockito.anyList()));
        try{
            syncLmsSnImuConsumer.consume(new ArrayList<>(),"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<PsWipInfo> list = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        list.add(psWipInfo);
        try{
            syncLmsSnImuConsumer.consume(list,"52");
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void recordErrorLog() throws Exception {
        List<PsWipInfo> psWipInfoList=new ArrayList<>();
        PsWipInfo psWipInfo =new PsWipInfo();
        psWipInfo.setSn("00286523");
        psWipInfo.setFactoryId(new BigDecimal("52"));
        psWipInfo.setSourceSysName("612");
        psWipInfo.setWorkStation("612");
        psWipInfoList.add(psWipInfo);
        Exception e = new Exception();
        PowerMockito.when(sysErrorLogService.exceptionHandler(Mockito.anyString())).thenReturn("yes");
        syncLmsSnImuConsumer.recordErrorLog(psWipInfoList,"53",e);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}