package com.zte.application.helper;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/4/26 下午11:24
 */
/* Started by AICoder, pid:sb7f8x4916e6d0b145f50aebb1275e3ab2745ec7 */

import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.consts.CommonConst;
import com.zte.domain.DO.PmMachineWeightDO;
import com.zte.domain.DTO.PmMachineWeightSearchDTO;
import com.zte.domain.model.PmMachineWeightRepository;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WarehouseEntryDetail;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class MachineWeightValidateHelperTest {

    @InjectMocks
    private MachineWeightValidateHelper machineWeightValidateHelper;

    @Mock
    private PmMachineWeightRepository machineWeightRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private PmMachineWeightSearchDTO pmMachineWeightSearchDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(CommonUtils.class);

        // 配置getLmbMessage的默认返回值
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("msg");
        when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("msg");

    }

    @Test
    public void testValidateMachineWeight_snIsEmpty() {
        /** 测试SN为空的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("");
        pmMachineWeightDO.setWeight("123.45");

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals("msg,msg,", result);
    }

    @Test
    public void testValidateMachineWeight_weightIsEmpty() {
        /** 测试重量为空的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");
        pmMachineWeightDO.setWeight("");

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals("msg,msg,", result);
    }

    @Test
    public void testValidateMachineWeight_invalidWeight() {
        /** 测试无效的重量值 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");
        pmMachineWeightDO.setWeight("abc");

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals("msg,msg,", result);
    }

    @Test
    public void testValidateMachineWeight_snNotRegistered() {
        /** 测试SN未注册的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");
        pmMachineWeightDO.setWeight("123.45");

        when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.emptyList());

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals(CommonUtils.getLmbMessage(MessageId.SN_IS_NOT_REGISTER, "SN123") + ",", result);
    }

    @Test
    public void testValidateMachineWeight_snHasWeight() {
        /** 测试SN已维护重量的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");
        pmMachineWeightDO.setWeight("123.45");

        when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.singletonList("SN123"));
        when(machineWeightRepository.selectByPrimaries(anyList())).thenReturn(Collections.singletonList(new PmMachineWeightDO()));

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals(CommonUtils.getLmbMessage(MessageId.SN_HAS_WEIGHT, "SN123") + ",", result);
    }

    @Test
    public void testValidateMachineWeight_allValid() {
        /** 测试所有条件都有效的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("SN123");
        pmMachineWeightDO.setWeight("123.45");

        when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.singletonList("SN123"));
        when(machineWeightRepository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());

        String result = machineWeightValidateHelper.validateMachineWeight(pmMachineWeightDO);
        assertEquals("", result);
    }

    @Test
    public void testValidateSNIsStorage_snInStorage() {
        /** 测试条码已入库的情况 */
        String sn = "SN123";
        WarehouseEntryDetail detail = new WarehouseEntryDetail();
        detail.setStatus("IN_STOCK");
        when(warehouseEntryDetailRepository.listByCondition(any(WarehouseEntryInfoDTO.class))).thenReturn(Collections.singletonList(detail));

        boolean result = machineWeightValidateHelper.validateSNIsStorage(sn);
        assertEquals(true, result);
    }

    @Test
    public void testValidateSNIsStorage_snNotInStorage() {
        /** 测试条码未入库的情况 */
        String sn = "SN123";
        WarehouseEntryDetail detail = new WarehouseEntryDetail();
        detail.setStatus(CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
        when(warehouseEntryDetailRepository.listByCondition(any(WarehouseEntryInfoDTO.class))).thenReturn(Collections.singletonList(detail));

        boolean result = machineWeightValidateHelper.validateSNIsStorage(sn);
        assertEquals(false, result);
    }

    /* Started by AICoder, pid:o0d5c5d783v13ae149ee09093033f47d8db90c5e */
    /**
     * 测试有效正数权重，无小数部分。
     */
    @Test
    public void testIsValidWeight_ValidPositiveInteger() {
        assertTrue(machineWeightValidateHelper.isValidWeight("10"));
    }

    /**
     * 测试有效正数权重，有一位小数。
     */
    @Test
    public void testIsValidWeight_ValidPositiveDecimal_OneDigit() {
        assertTrue(machineWeightValidateHelper.isValidWeight("10.5"));
    }

    /**
     * 测试有效正数权重，有两位小数。
     */
    @Test
    public void testIsValidWeight_ValidPositiveDecimal_TwoDigits() {
        assertTrue(machineWeightValidateHelper.isValidWeight("10.55"));
    }

    /**
     * 测试无效权重，包含非数字字符。
     */
    @Test
    public void testIsValidWeight_InvalidNonNumericCharacters() {
        assertFalse(machineWeightValidateHelper.isValidWeight("10.5a"));
    }

    /**
     * 测试无效权重，小数位超过两位。
     */
    @Test
    public void testIsValidWeight_InvalidMoreThanTwoDecimalPlaces() {
        assertFalse(machineWeightValidateHelper.isValidWeight("10.555"));
    }

    /**
     * 测试无效权重，负数。
     */
    @Test
    public void testIsValidWeight_InvalidNegativeNumber() {
        assertFalse(machineWeightValidateHelper.isValidWeight("-10"));
    }

    /**
     * 测试无效权重，空字符串。
     */
    @Test
    public void testIsValidWeight_InvalidEmptyString() {
        assertFalse(machineWeightValidateHelper.isValidWeight(""));
    }

    /**
     * 测试空字符权重，前导和尾随空格。
     */
    @Test
    public void testIsValidWeight_InvalidLeadingTrailingSpaces() {
        assertTrue(machineWeightValidateHelper.isValidWeight(" 10 "));
    }

    /**
     * 测试无效权重，零值。
     */
    @Test
    public void testIsValidWeight_InvalidZeroValue() {
        assertFalse(machineWeightValidateHelper.isValidWeight("0"));
    }

    /**
     * 测试无效权重，格式错误的数字。
     */
    @Test
    public void testIsValidWeight_InvalidMalformedNumber() {
        assertFalse(machineWeightValidateHelper.isValidWeight("10..5"));
    }

    /* Ended by AICoder, pid:o0d5c5d783v13ae149ee09093033f47d8db90c5e */

    /* Started by AICoder, pid:2390f7a0dav02c2146190b1750b10c656532acee */
    @Test
    public void testValidateTimeRelation_SnOrTaskNoNotNull() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        dto.setSn("SN123");
        assertEquals(null, machineWeightValidateHelper.validateTimeRelation(dto));
    }

    @Test
    public void testValidateTimeRelation_SnOrTaskNoNotNull2() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        dto.setTaskNo("SN123");
        assertEquals(null, machineWeightValidateHelper.validateTimeRelation(dto));
    }

    @Test
    public void testValidateTimeRelation_AllNullAndAllBlank() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        when(CommonUtils.getLmbMessage(any())).thenReturn("Error message");
        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_CreateByWithoutDates() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        dto.setCreateBy("Creator");
        when(CommonUtils.getLmbMessage(any())).thenReturn("Error message");
        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_CreateDateNotNull() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        Date createStartDate = new Date();
        Date createEndDate = new Date(createStartDate.getTime() + 1000000000 * 10L); // 未来日期
        dto.setCreateBy("Creator");
        dto.setCreateStartDate(createStartDate);
        dto.setCreateEndDate(createEndDate);
        when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("Error message");
        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_UpdatedByWithoutDates() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        dto.setLastUpdatedBy("Updater");
        when(CommonUtils.getLmbMessage(any())).thenReturn("Error message");
        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_CreateStartDatePlusLargerThanEndDate() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        Date createStartDate = new Date();
        Date createEndDate = new Date(createStartDate.getTime() + 1000000000L); // 未来日期
        dto.setCreateStartDate(createStartDate);
        dto.setCreateEndDate(createEndDate);

        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertTrue(StringUtils.isBlank(result));
    }

    @Test
    public void testValidateTimeRelation_CreateStartDatePlusLessThanEndDate() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        Date createStartDate = new Date();
        Date createEndDate = new Date(createStartDate.getTime() + 1000000000 * 10L); // 未来日期
        dto.setCreateStartDate(createStartDate);
        dto.setCreateEndDate(createEndDate);
        when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("Error message");

        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_UpdatedStartDatePlusLessThanEndDate() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        Date updatedStartDate = new Date();
        Date updatedEndDate = new Date(updatedStartDate.getTime() + 1000000000 * 10L); // 未来日期
        dto.setLastUpdatedStartDate(updatedStartDate);
        dto.setLastUpdatedEndDate(updatedEndDate);

        when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("Error message");

        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertEquals("Error message,", result);
    }

    @Test
    public void testValidateTimeRelation_UpdatedStartDatePlusLargerThanEndDate() {
        PmMachineWeightSearchDTO dto = new PmMachineWeightSearchDTO();
        Date updatedStartDate = new Date();
        Date updatedEndDate = new Date(updatedStartDate.getTime() + 1000000000L); // 未来日期
        dto.setLastUpdatedStartDate(updatedStartDate);
        dto.setLastUpdatedEndDate(updatedEndDate);

        String result = machineWeightValidateHelper.validateTimeRelation(dto);
        assertTrue(StringUtils.isBlank(result));
    }
    /* Ended by AICoder, pid:2390f7a0dav02c2146190b1750b10c656532acee */

    /* Started by AICoder, pid:r9c7681554ef9d9149560a6571ceba21f6b052ba */
    @Test
    public void testValidateDeviceMachineWeight_snEmpty() {
        /** 测试SN为空的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("");
        pmMachineWeightDO.setWeight("123.45");

        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.SN_CAN_NOT_EMPTY)).thenReturn("SN cannot be empty");
        PowerMockito.when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.singletonList("a"));

        String result = machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);

        assertEquals("SN cannot be empty,", result);
    }

    @Test
    public void testValidateDeviceMachineWeight_weightEmpty() {
        /** 测试重量为空的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("validSN");
        pmMachineWeightDO.setWeight("");

        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.WEIGHT_NOT_EMPTY)).thenReturn("Weight cannot be empty");
        PowerMockito.when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.singletonList("a"));

        String result = machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);

        assertEquals("Weight cannot be empty,", result);
    }

    @Test
    public void testValidateDeviceMachineWeight_invalidWeight() {
        /** 测试无效的重量值 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("validSN");
        pmMachineWeightDO.setWeight("invalidWeight");

        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.WEIGHT_NOT_VALID_POSITIVE_NUMBER)).thenReturn("Weight must be a valid positive number");
        PowerMockito.when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.singletonList("a"));

        String result = machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);

        assertEquals("Weight must be a valid positive number,", result);
    }

    @Test(expected = BusiException.class)
    public void testValidateDeviceMachineWeight_snAlreadyStored() {
        /** 测试SN已入库的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("storedSN");
        pmMachineWeightDO.setWeight("123.45");

        WarehouseEntryDetail warehouseEntryDetail = new WarehouseEntryDetail();
        warehouseEntryDetail.setStatus(CommonConst.WAREHOUSE_DATAIL_STATUS_CONFIRM);
        when(warehouseEntryDetailRepository.listByCondition(any())).thenReturn(Collections.singletonList(warehouseEntryDetail));

        try {
            machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);
        } catch (BusiException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(CommonUtils.getLmbMessage(MessageId.SN_STORAGE_CAN_NOT_MODIFY_WEIGHT), e.getExMsg());
            throw e;
        }
    }

    @Test
    public void testValidateDeviceMachineWeight_snNotRegistered() {
        /** 测试SN未注册的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("unregisteredSN");
        pmMachineWeightDO.setWeight("123.45");

        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.SN_IS_NOT_REGISTER, "unregisteredSN")).thenReturn("SN unregisteredSN is not registered");

        WarehouseEntryDetail warehouseEntryDetail = new WarehouseEntryDetail();
        warehouseEntryDetail.setStatus(CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
        when(warehouseEntryDetailRepository.listByCondition(any())).thenReturn(Collections.singletonList(warehouseEntryDetail));
        when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Collections.emptyList());

        String result = machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);

        assertEquals("SN unregisteredSN is not registered,", result);
    }

    @Test
    public void testValidateDeviceMachineWeight_allValid() {
        /** 测试所有验证都通过的情况 */
        PmMachineWeightDO pmMachineWeightDO = new PmMachineWeightDO();
        pmMachineWeightDO.setSn("validSN");
        pmMachineWeightDO.setWeight("123.45");

        WarehouseEntryDetail warehouseEntryDetail = new WarehouseEntryDetail();
        warehouseEntryDetail.setStatus(CommonConst.WAREHOUSE_DATAIL_SATATUS_REJECT);
        when(warehouseEntryDetailRepository.listByCondition(any())).thenReturn(Collections.singletonList(warehouseEntryDetail));
        when(psWipInfoRepository.checkSnExist(anyList())).thenReturn(Arrays.asList("validSN"));
        when(machineWeightRepository.selectByPrimaries(anyList())).thenReturn(Collections.emptyList());

        String result = machineWeightValidateHelper.validateDeviceMachineWeight(pmMachineWeightDO);

        assertEquals("", result);
        assertEquals("123.45", pmMachineWeightDO.getWeight());
    }
    /* Ended by AICoder, pid:r9c7681554ef9d9149560a6571ceba21f6b052ba */
}

/* Ended by AICoder, pid:sb7f8x4916e6d0b145f50aebb1275e3ab2745ec7 */
