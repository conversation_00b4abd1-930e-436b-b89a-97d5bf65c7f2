package com.zte.autoTest.unitTest;

import com.zte.application.impl.AgeingInfoHeaderServiceImpl;
import com.zte.application.impl.AssemblyResultServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.AssemblyResultHisRepository;
import com.zte.domain.model.AssemblyResultRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.AgeingDto;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
public class AgeingInfoHeaderServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private AgeingInfoHeaderServiceImpl ageingInfoHeaderService;


    @Test
    public void setFactoryId()throws Exception {
        ageingInfoHeaderService.setFactoryId(new AgeingDto(),new PsScanHistory());
        ageingInfoHeaderService.setFactoryId(new AgeingDto(){{setFactoryId("55");}},new PsScanHistory());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
