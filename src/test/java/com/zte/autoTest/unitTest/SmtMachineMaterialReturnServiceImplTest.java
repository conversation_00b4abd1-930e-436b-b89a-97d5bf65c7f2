package com.zte.autoTest.unitTest;

import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.SmtMachineMtlOnlineStandbyService;
import com.zte.application.impl.SmtMachineMaterialReturnServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeHistoryRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.SmtMachineMaterialReturn;
import com.zte.domain.model.SmtMachineMaterialReturnRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.infrastructure.remote.StorageCenterRemoteService;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnQtyDTO;
import com.zte.interfaces.dto.SmtMachineMaterialReturnTestValueDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WmsQueryReturnDTO;
import com.zte.interfaces.dto.storage.StorageDetailsDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.EasyExcelUtils;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.util.Pair;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/12/10 16:54
 */
@PrepareForTest({ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class, HttpClientUtils.class,
        ServiceDataUtil.class, PlanscheduleRemoteService.class, HttpClientUtil.class, CommonUtils.class,
        ServiceDataBuilderUtil.class, MicroServiceRestUtil.class,
        RequestHeadValidationUtil.class, EasyExcelUtils.class})
public class SmtMachineMaterialReturnServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtMachineMaterialReturnServiceImpl service;

    @Mock
    private SmtMachineMaterialReturnRepository smtMachineMaterialReturnRepository;
    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;

    @Mock
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Mock
    private SmtMachineMtlOnlineStandbyService smtMachineMtlOnlineStandbyService;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Mock
    private PkCodeInfoService pkCodeInfoService;

    @Mock
    private PkCodeHistoryService pkCodeHistoryService;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    private StorageCenterRemoteService storageCenterRemoteService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private ValueOperations<String, Object> redisOpsValue;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private InputStream inputStream;
    @Mock
    ExcelReaderBuilder read;
    @Mock
    ExcelReaderSheetBuilder sheet;
    @Mock
    private AnalysisEventListener analysisEventListener;
    @Mock
    private String autoCompensationReturn;

    @Before
    public void init() {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class,
                ServiceDataUtil.class, HttpClientUtils.class, PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(EasyExcelUtils.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);

    }

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void verifyReelidReturnStatus() throws Exception {
        SmtMachineMaterialReturnDTO smtMachineMaterialReturnDTO = new SmtMachineMaterialReturnDTO();
        smtMachineMaterialReturnDTO.setStatus(BigDecimal.ZERO);
        SysLookupTypesDTO sysLookupValuesDTO = new SysLookupTypesDTO();
        sysLookupValuesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupValuesDTO);
        try {
            Whitebox.invokeMethod(service, "verifyReelidReturnStatus", "reelid", smtMachineMaterialReturnDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void returnMaterialsAndReceiveGoods() throws Exception {

        SmtMachineMaterialReturnDTO smtReturn = new SmtMachineMaterialReturnDTO();
        smtReturn.setStatus(new BigDecimal("1"));
        smtReturn.setItemCode("040012345230");
        smtReturn.setWorkOrder("7778889");
        smtReturn.setQty(1);
        smtReturn.setCreateUser("123");
        PowerMockito.when(smtMachineMaterialReturnRepository.getSmtMachineMaterialReturnByReelId(Mockito.any()))
                .thenReturn(smtReturn);
        List<StorageDetailsDTO> flowListAll = new ArrayList<>();
        StorageDetailsDTO flowDto = new StorageDetailsDTO();
        flowDto.setSrcBillNo("1");
        flowDto.setBillTypeCode("XBCK_SB");
        flowDto.setQtyChange(new BigDecimal("200"));
        flowListAll.add(flowDto);
        PowerMockito.when(storageCenterRemoteService.queryStockFlowCustomized(Mockito.any()))
                .thenReturn(flowListAll);
        PsWorkOrderBasic reelidWorkOrder = new PsWorkOrderBasic();
        reelidWorkOrder.setWorkOrderStatus("已完工");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.any()))
                .thenReturn(reelidWorkOrder);

        PowerMockito.when(smtMachineMaterialReturnRepository.getSmtMachineMaterialReturn(any())).thenReturn(Lists.newArrayList(new SmtMachineMaterialReturnDTO() {{
            setObjectId("1");
            setWorkOrder("1");
        }}));
        PowerMockito.when(pkCodeInfoService.getPkCodeInfoByCode(anyString())).thenReturn(new PkCodeInfo() {{
            setPkCode("1");
        }});
        PowerMockito.when(BasicsettingRemoteService.checkCIV(any(), any())).thenReturn(Lists.newArrayList("1"));
        PowerMockito.when(ServiceDataUtil.getBusinessError(any())).thenReturn(null);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(BasicsettingRemoteService.checkCIV(Mockito.any(), Mockito.any())).thenReturn(null);
        List<PkCodeInfo> pkCodeInfos = new LinkedList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemType("A");
        pkCodeInfo.setWetLevel("一  级");
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.mockStatic(HttpClientUtil.class);
        List<BSmtBomDetailDTO> reList = new LinkedList<>();
        BSmtBomDetailDTO a1 = new BSmtBomDetailDTO();
        a1.setItemCode("040012345230");
        a1.setUsageCount(new BigDecimal(10));
        reList.add(a1);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(reList));
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(Mockito.any())).thenReturn(pkCodeInfos);
        Assert.assertNotNull(service.returnMaterialsAndReceiveGoods(new SmtMachineMaterialReturnDTO() {{
            setObjectId("1");
            setWorkOrder("1");
        }}));
    }

    @Test
    public void insertReturnByWorkOrderAndReelId() throws Exception {
        List<SmtMachineMaterialReturnDTO> smtMachineMaterialList = new ArrayList<>();
        SmtMachineMaterialReturnDTO returnDTO = new SmtMachineMaterialReturnDTO();
        returnDTO.setMachineMaterialReturnId(UUID.randomUUID().toString());
        returnDTO.setObjectId("ZTE000010001");
        returnDTO.setLineCode("SMT-120");
        smtMachineMaterialList.add(returnDTO);
        PowerMockito.when(smtMachineMaterialReturnRepository.getSmtMachineMaterialReturn(any())).thenReturn(null);
        SmtMachineMaterialReturn materialReturn = new SmtMachineMaterialReturn();
        materialReturn.setObjectId("ZTE000010001");
        materialReturn.setWorkOrder("7119859-SMT-A5201");
        Assert.assertNotNull(service.insertReturnByWorkOrderAndReelId(materialReturn));
    }

    @Test
    public void queryMaterialReturnInfoByReelId() throws Exception {
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno()).thenReturn(Pair.of("52", "344"));
        try {
            service.queryMaterialReturnInfoByReelId("123");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.REELID_NOT_EXIST.equals(e.getMessage()));
        }
        SmtMachineMaterialReturnDTO dto = new SmtMachineMaterialReturnDTO();
        dto.setWorkOrder("7300077-SMT-B5201");
        dto.setLastUpdateDate(sdf.parse("2022-03-06 15:01:42"));
        dto.setLineCode("SMT-CS1");
        dto.setObjectId("ZTEtest20220310");

        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setSourceTask("7300077");
        psWorkOrderBasic.setWorkOrderStatus(Constant.FINISH_WORK);
        psWorkOrderBasic.setActualEndDate(new Date());
        PowerMockito.when(PlanscheduleRemoteService.getTaskByWorkOrderNo(Mockito.anyString()))
                .thenReturn(psWorkOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString()))
                .thenReturn(psWorkOrderBasic);

        CFLine lineInfo = new CFLine();
        lineInfo.setLineName("SMT-长沙1");
        PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyString()))
                .thenReturn(lineInfo);

        List<StorageDetailsDTO> flowListAll = new LinkedList<>();
        StorageDetailsDTO a1 = new StorageDetailsDTO();
        a1.setReelid("ZTEtest20220310");
        a1.setSrcBillNo("7300077-SMT-B5201");
        a1.setBillNo("7300077-SMT-B5201");
        a1.setBillTypeCode(Constant.BILL_TYPE_XBCK_SB);
        a1.setCreatedDate(new Date());
        a1.setQtyChange(new BigDecimal("200"));
        flowListAll.add(a1);
        StorageDetailsDTO a2 = new StorageDetailsDTO();
        a2.setReelid("ZTEtest20220310");
        a2.setSrcBillNo("7300077-SMT-B5201");
        a2.setBillNo("7300077-SMT-B5201");
        a2.setBillTypeCode(Constant.BILL_TYPE_XBCK_SB);
        a2.setCreatedDate(new Date());
        a2.setQtyChange(new BigDecimal("201"));
        a2.setFromSummaryId("201");
        flowListAll.add(a2);
        StorageDetailsDTO a3 = new StorageDetailsDTO();
        a3.setReelid("ZTEtest20220310");
        a3.setSrcBillNo("7300077-SMT-B5201");
        a3.setBillNo("7300077-SMT-B5201");
        a3.setBillTypeCode(Constant.BILL_TYPE_XBCK_SB);
        a3.setCreatedDate(new Date());
        a3.setFromSummaryId("201");
        flowListAll.add(a3);
        PowerMockito.when(storageCenterRemoteService.queryStockFlowCustomized(Mockito.any()))
                .thenReturn(flowListAll);
        PowerMockito.when(pkCodeInfoService.getPkCodeInfoByCode(anyString())).thenReturn(new PkCodeInfo() {{
            setProductTask("7300077");
            setItemQty(new BigDecimal("1"));
        }});
        try {
            service.queryMaterialReturnInfoByReelId("ZTEtest20220310");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.REELID_NOT_EXIST.equals(e.getMessage()));
        }

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        pkCodeInfoList.add(new PkCodeInfo());
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(anyString())).thenReturn(pkCodeInfoList);
        try {
            service.queryMaterialReturnInfoByReelId("ZTEtest20220310");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.REEL_ID_NOT_IN_TABLE_MATERIAL_RETURN.equals(e.getMessage()));
        }

        PowerMockito.field(SmtMachineMaterialReturnServiceImpl.class, "autoCompensationReturn")
                .set(service, "Y");
        PowerMockito.when(smtMachineMaterialReturnRepository.getSmtMachineMaterialReturnByReelId(any())).thenReturn(dto);
        try {
            service.queryMaterialReturnInfoByReelId("ZTEtest20220310");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.THE_LATEST_RETURNED_MATERIAL_QUANTITY_IS_ABNORMAL.equals(e.getMessage()));
        }

        dto.setQty(4);
        try {
            service.queryMaterialReturnInfoByReelId("ZTEtest20220310");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.REEL_ID_NOT_IN_TABLE_MATERIAL_RETURN.equals(e.getMessage()));
        }

        dto.setWorkOrder("GTTT");
        try {
            service.queryMaterialReturnInfoByReelId("ZTEtest20220310");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.REEL_ID_NOT_IN_TABLE_MATERIAL_RETURN.equals(e.getMessage()));
        }

    }

    @Test
    public void updateStatusByReelId() throws Exception {
        SmtMachineMaterialReturnDTO dto = new SmtMachineMaterialReturnDTO();
        dto.setObjectId("ZTE181015001877");
        dto.setWorkOrder("7300077-SMT-B5201");
        dto.setStatus(new BigDecimal("1"));
        PowerMockito.when(smtMachineMaterialReturnRepository.updateStatusByReelId(any())).thenReturn(1);
        Assert.assertEquals(1, service.updateStatusByReelId(dto));
    }

    @Test
    public void updateReturnMaterialQty() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        List<SmtMachineMaterialReturnQtyDTO> list = new ArrayList<>();
        SmtMachineMaterialReturnQtyDTO tempDto = new SmtMachineMaterialReturnQtyDTO();
        tempDto.setQty(new BigDecimal("3.2"));
        tempDto.setReelId("9898");
        list.add(tempDto);
        try {
            service.updateReturnMaterialQty(list, "52");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        tempDto.setQty(new BigDecimal("3"));
        PkCodeInfo pkCodeInfo = null;
        PowerMockito.when(pkCodeInfoRepository.getInfoByReelId(any())).thenReturn(pkCodeInfo);
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        service.updateReturnMaterialQty(list, "52");
        pkCodeInfo = new PkCodeInfo();
        SmtMachineMaterialPrepare info = null;
        PowerMockito.when(pkCodeInfoRepository.getInfoByReelId(any())).thenReturn(pkCodeInfo);
        List<StorageDetailsDTO> storageDetailsDTOList = null;
        StorageDetailsDTO dto2 = new StorageDetailsDTO();
        dto2.setFromSummaryId("dad");
        PowerMockito.when(storageCenterRemoteService.queryStockFlowCustomized(dto2)).thenReturn(storageDetailsDTOList);
        service.updateReturnMaterialQty(list, "52");
        storageDetailsDTOList = new ArrayList<>();
        StorageDetailsDTO dto1 = new StorageDetailsDTO();
        dto1.setCreatedDate(new Date());
        dto1.setFromSummaryId("321");
        dto1.setWarehouseId("123321");
        dto1.setProductionBatch("7777777");
        storageDetailsDTOList.add(dto1);
        PowerMockito.when(storageCenterRemoteService.queryStockFlowCustomized(Mockito.any())).thenReturn(storageDetailsDTOList);
        service.updateReturnMaterialQty(list, "52");
        dto1.setSrcBillNo("666");
        service.updateReturnMaterialQty(list, "52");
        PowerMockito.when(smtMachineMaterialPrepareRepository.getInfoByObjectId(any())).thenReturn(info);
        service.updateReturnMaterialQty(list, "52");
        info = new SmtMachineMaterialPrepare();
        info.setWorkOrder("123");
        PsWorkOrderBasic workOrderInfo = null;
        PowerMockito.when(smtMachineMaterialPrepareRepository.getInfoByObjectId(any())).thenReturn(info);
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString()))
                .thenReturn(workOrderInfo);
        service.updateReturnMaterialQty(list, "52");
        PsWorkOrderBasic workOrderInfo1 = new PsWorkOrderBasic();
        workOrderInfo1.setWorkOrderStatus("已废止");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString()))
                .thenReturn(workOrderInfo1);
        service.updateReturnMaterialQty(list, "52");
        workOrderInfo1.setWorkOrderStatus("已完工");
        SmtMachineMaterialReturnDTO tempDto1 = null;
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(anyString())).thenReturn(tempDto1);
        service.updateReturnMaterialQty(list, "52");
        SmtMachineMaterialReturnDTO temp = new SmtMachineMaterialReturnDTO();
        temp.setStatus(new BigDecimal("1"));
        temp.setItemCode("sda");
        temp.setWorkOrder("7777771-1");
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(any())).thenReturn(temp);
        service.updateReturnMaterialQty(list, "52");

        PowerMockito.when(PlanscheduleRemoteService.getSubTaskByTaskNo(Mockito.anyList()))
                .thenReturn(null);
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.any())).thenReturn(null);
        workOrderInfo1.setWorkOrderStatus("零星板挂起");
        temp.setStatus(new BigDecimal("2"));
        service.updateReturnMaterialQty(list, "52");

        List<PsTask> subTaskByTaskNoList = new LinkedList<>();
        PsTask p1 = new PsTask();
        p1.setProdplanId("7777777");
        subTaskByTaskNoList.add(p1);
        PowerMockito.when(PlanscheduleRemoteService.getSubTaskByTaskNo(Mockito.any())).thenReturn(subTaskByTaskNoList);
        workOrderInfo1.setWorkOrderStatus("零星板挂起");
        temp.setStatus(new BigDecimal("2"));
        service.updateReturnMaterialQty(list, "52");

        PowerMockito.when(PlanscheduleRemoteService.getSubTaskByTaskNo(Mockito.any())).thenReturn(subTaskByTaskNoList);
        workOrderInfo1.setWorkOrderStatus("零星板挂起");
        temp.setStatus(new BigDecimal("2"));
        service.updateReturnMaterialQty(list, "52");

        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString()))
                .thenReturn(workOrderInfo1);
        temp.setStatus(new BigDecimal("3"));
        List<PkCodeInfo> pkCodeInfoList = null;
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(Mockito.anyString()))
                .thenReturn(pkCodeInfoList);
        List<PkCodeInfo> pkCodeInfos = new ArrayList<>();
        service.updateReturnMaterialQty(list, "52");
        List<PkCodeInfo> pkCodeInfoList1 = new ArrayList<>();
        PkCodeInfo info1 = new PkCodeInfo();
        info.setItemType("");
        pkCodeInfoList1.add(info1);
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(any()))
                .thenReturn(pkCodeInfoList1);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(sysLookupTypesDTO);
        service.updateReturnMaterialQty(list, "52");
        info.setItemType("A");
        service.updateReturnMaterialQty(list, "52");
        pkCodeInfo.setItemType("321");
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(any())).thenReturn(pkCodeInfos);
        List<WmsQueryReturnDTO> warehouseNameList = new ArrayList<>();
        WmsQueryReturnDTO returnDTO = new WmsQueryReturnDTO();
        returnDTO.setWarehouseId("123321");
        PowerMockito.when(ProductionDeliveryRemoteService.getWarehouseName()).thenReturn(warehouseNameList);
        tempDto.setQty(new BigDecimal("0"));
        try {
            service.updateReturnMaterialQty(list, "52");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        tempDto.setQty(new BigDecimal("2"));
        try {
            service.updateReturnMaterialQty(list, "52");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        temp.setStatus(null);
        try {
            service.updateReturnMaterialQty(list, "52");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

        PowerMockito.when(PlanscheduleRemoteService.getSubTaskByTaskNo(Mockito.any())).thenReturn(null);
        workOrderInfo1.setWorkOrderStatus("零星板挂起");
        temp.setStatus(new BigDecimal("2"));
        service.updateReturnMaterialQty(list, "52");
    }

    @Test
    public void insertSmtMachineMaterialReturnByReelId() {
        List<String> reelIdList = new LinkedList<>();
        ProductionDeliveryRemoteService.insertSmtMachineMaterialReturnByReelId(reelIdList);

        reelIdList.add("123");
        ProductionDeliveryRemoteService.insertSmtMachineMaterialReturnByReelId(reelIdList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void updateReturnList() throws Exception {
        //
        Pair<String, String> factoryIdAndEmpNo = Pair.of("12", "44");
        LinkedList<SmtMachineMaterialReturnDTO> excelReadList = new LinkedList<>();
        Whitebox.invokeMethod(service, "updateReturnList", factoryIdAndEmpNo, excelReadList);

        SmtMachineMaterialReturnDTO a1 = new SmtMachineMaterialReturnDTO();
        excelReadList.add(a1);
        Whitebox.invokeMethod(service, "updateReturnList", factoryIdAndEmpNo, excelReadList);

        a1.setObjectId("345");
        Whitebox.invokeMethod(service, "updateReturnList", factoryIdAndEmpNo, excelReadList);

        a1.setQty(3);
        a1.setCreateDate(new Date());
        SmtMachineMaterialReturnDTO a2 = new SmtMachineMaterialReturnDTO();
        a2.setObjectId("123");
        a2.setQty(4);
        excelReadList.add(a2);
        Whitebox.invokeMethod(service, "updateReturnList", factoryIdAndEmpNo, excelReadList);

        List<SmtMachineMaterialReturnDTO> result = new LinkedList<>();
        result.add(a1);
        PowerMockito.when(smtMachineMaterialReturnRepository.selectReturnReelIdBatch(Mockito.any()))
                .thenReturn(result);
        Whitebox.invokeMethod(service, "updateReturnList", factoryIdAndEmpNo, excelReadList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateTestAndStandardValues() {
        SmtMachineMaterialReturnTestValueDTO dto = new SmtMachineMaterialReturnTestValueDTO();
        SmtMachineMaterialReturnDTO tempDto = null;
        dto.setReelId("ZTE202311090001");
        PkCodeInfo pkCodeInfo = null;
        PowerMockito.when(pkCodeInfoRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(pkCodeInfo);
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(tempDto);
        try {
            service.updateTestAndStandardValues(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        pkCodeInfo = new PkCodeInfo();

        PowerMockito.when(pkCodeInfoRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(pkCodeInfo);
        try {
            service.updateTestAndStandardValues(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        tempDto = new SmtMachineMaterialReturnDTO();
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(tempDto);
        try {
            service.updateTestAndStandardValues(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        tempDto.setMachineMaterialReturnId("50fdf92d-8c18-4059-88df-0a0ed69629e6");
        tempDto.setStatus(new BigDecimal("2"));
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(tempDto);
        try {
            service.updateTestAndStandardValues(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

        tempDto.setStatus(new BigDecimal("1"));
        PowerMockito.when(smtMachineMaterialReturnRepository.getInfoByReelId(Mockito.any()))
                .thenReturn(tempDto);
        PowerMockito.when(smtMachineMaterialReturnRepository.updateTestAndStandardValues(any())).thenReturn(1);
        try {
            Assert.assertNull(service.updateTestAndStandardValues(dto));
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
}
