package com.zte.autoTest.unitTest;

import com.zte.application.MaintenanceMaterialLineService;
import com.zte.application.impl.MaintenanceMaterialHeadServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.MaintenanceMaterialHeadRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.BaItemBrandstyle;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.CreateSupplementBillParamDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.Lotxlocxid;
import com.zte.interfaces.dto.MaintenanceMaterialHeadDTO;
import com.zte.interfaces.dto.MaintenanceMaterialInfoDTO;
import com.zte.interfaces.dto.MaintenanceMaterialLineDTO;
import com.zte.interfaces.dto.PageRowsVO;
import com.zte.interfaces.dto.PdRequirementHeaderDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SupplementBillDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * $
 *
 * <AUTHOR>
 * @ProjectName develop
 * @PackageName com.zte.autoTest.unitTest
 * @Date 2021-03-05 15:06
 * @user 10275508
 */
@PrepareForTest({BasicsettingRemoteService.class, ImesExcelUtil.class,  DatawbRemoteService.class, PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class, SpringContextUtil.class})
public class MaintenanceMaterialHeadServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private MaintenanceMaterialHeadServiceImpl maintenanceMaterialHeadServiceImpl;
    @Mock
    private MaintenanceMaterialHeadRepository maintenanceMaterialHeadRepository;
    @Mock
    private MaintenanceMaterialLineService maintenanceMaterialLineService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemote;
    @Mock
    private PmRepairRcvServiceImpl pmRepairRcvService;
    @Mock
    private IscpRemoteService iscpRemoteService;
    @Mock
    private LocaleMessageSourceBean lmb;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(DatawbRemoteService.class, PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
    }

    @Test
    public void testInsertOneHead() {
        Assert.assertNotNull(maintenanceMaterialHeadServiceImpl.insertOneHead(new MaintenanceMaterialHeadDTO()));
    }

    @Test
    public void testInsertHeadAndDetails() throws Exception {
        MaintenanceMaterialHeadDTO headDTO = new MaintenanceMaterialHeadDTO();
        List<MaintenanceMaterialLineDTO> lines = new LinkedList<>();
        MaintenanceMaterialLineDTO a1 = new MaintenanceMaterialLineDTO();
        a1.setRcvProdplanId("123");
        a1.setMainRcvProdplanId("123");
        lines.add(a1);
        headDTO.setDetails(lines);
        headDTO.setProdplanId("123");

        PowerMockito.when(centerfactoryRemote.createCfBizCode(Mockito.any())).thenReturn(new LinkedList<String>() {{
            add("123");
        }});
        Assert.assertThrows(NullPointerException.class, () -> maintenanceMaterialHeadServiceImpl.submitHeadAndDetails(headDTO));

        headDTO.setHeadId("123");
        Assert.assertThrows(NullPointerException.class, () -> maintenanceMaterialHeadServiceImpl.saveHeadAndDetails(headDTO));
    }

    @Test
    public void testSubmitHeadAndDetails() throws Exception {
        MaintenanceMaterialHeadDTO headDTO = new MaintenanceMaterialHeadDTO();
        List<MaintenanceMaterialLineDTO> lines = new LinkedList<>();
        MaintenanceMaterialLineDTO a1 = new MaintenanceMaterialLineDTO();
        a1.setRcvProdplanId("123");
        a1.setMainRcvProdplanId("123");
        lines.add(a1);
        headDTO.setDetails(lines);
        headDTO.setHeadId("123");
        headDTO.setProdplanId("123");

        PowerMockito.when(centerfactoryRemote.createCfBizCode(Mockito.any())).thenReturn(new LinkedList<String>(){{
            add("123");}});
        Assert.assertThrows(NullPointerException.class, () -> maintenanceMaterialHeadServiceImpl.submitHeadAndDetails(headDTO));
    }

    @Test
    public void testDeleteOneByHeadId() {
        Assert.assertEquals(1,maintenanceMaterialHeadServiceImpl.deleteOneByHeadId(new MaintenanceMaterialHeadDTO()));
    }

    @Test
    public void queryPageByConditionWithDetails() {
        Assert.assertNotNull(maintenanceMaterialHeadServiceImpl.queryPageByConditionWithDetails(new PageRowsVO<>()));
    }

    @Test
    public void testQueryPageByCondition() throws Exception {
        PageRowsVO pageRowsVO = new PageRowsVO();
        MaintenanceMaterialHeadDTO dto = new MaintenanceMaterialHeadDTO();
        pageRowsVO.setBo(dto);

        List<SysLookupValuesDTO> directoryGroup = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setAttribute2("123");
        a1.setLookupMeaning("123");
        a1.setDescriptionChin("123");
        directoryGroup.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyMap())).thenReturn(directoryGroup);

        MaintenanceMaterialHeadDTO b1 = new MaintenanceMaterialHeadDTO();
        b1.setBillStatus("已提交");
        List<MaintenanceMaterialLineDTO> detailList = new ArrayList<>();
        MaintenanceMaterialLineDTO detail = new MaintenanceMaterialLineDTO();
        detailList.add(detail);
        b1.setDetails(detailList);
        MaintenanceMaterialHeadDTO b2 = new MaintenanceMaterialHeadDTO();
        b1.setBillStatus("拟制中");
        MaintenanceMaterialHeadDTO b3 = new MaintenanceMaterialHeadDTO();
        b1.setBillStatus("待确认");
        List<MaintenanceMaterialHeadDTO> rows = new LinkedList<>();
        rows.add(b1);
        rows.add(b2);
        rows.add(b3);
        PowerMockito.when(maintenanceMaterialHeadRepository.queryPageByCondition(Mockito.any())).thenReturn(rows);
        try {
            Assert.assertNotNull(maintenanceMaterialHeadServiceImpl.queryPageByCondition(pageRowsVO));
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void testUpdateHeadByHeadId() {
        Assert.assertNotNull(maintenanceMaterialHeadServiceImpl.updateHeadByHeadId(new MaintenanceMaterialHeadDTO()));
    }

    @Test
    public void itemReleaseOpreation() throws Exception {
        MaintenanceMaterialHeadDTO dto = new MaintenanceMaterialHeadDTO();
        dto.setMaterialRequisitionBill("123");
        dto.setBillStatus("3");
        dto.setChangeType("123");
        dto.setOldMaterialStatus("123");

        List<MaintenanceMaterialHeadDTO> releaseInfoList = new ArrayList<>();
        MaintenanceMaterialHeadDTO paramDto = new MaintenanceMaterialHeadDTO();
        paramDto.setBillStatus("2");
        List<MaintenanceMaterialLineDTO> detail = new ArrayList<>();
        MaintenanceMaterialLineDTO detailDto = new MaintenanceMaterialLineDTO();
        detailDto.setLineId("123");
        detail.add(detailDto);
        paramDto.setDetails(detail);
        releaseInfoList.add(paramDto);
        PowerMockito.when(maintenanceMaterialHeadRepository.queryReleaseHeadAndDetailInfo(Mockito.any())).thenReturn(releaseInfoList);

        maintenanceMaterialHeadServiceImpl.itemReleaseOpreation(dto);

        dto.setBillStatus("4");
        dto.setInforBillNo("123");
        paramDto.setBillStatus("3");
        maintenanceMaterialHeadServiceImpl.itemReleaseOpreation(dto);

        dto.setBillStatus("5");
        paramDto.setBillStatus("4");
        maintenanceMaterialHeadServiceImpl.itemReleaseOpreation(dto);

        dto.setBillStatus("6");
        dto.setScrapReason("123");
        paramDto.setBillStatus("5");

        Assert.assertEquals(0,maintenanceMaterialHeadServiceImpl.itemReleaseOpreation(dto));


    }

    @Test
    public void exportExcel() throws Exception {
        maintenanceMaterialHeadServiceImpl.exportExcel(null, null, new MaintenanceMaterialHeadDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void confirmReceipt() {
        MaintenanceMaterialHeadDTO dto = new MaintenanceMaterialHeadDTO();
        List<MaintenanceMaterialLineDTO> lines = new LinkedList<>();
        MaintenanceMaterialLineDTO a1 = new MaintenanceMaterialLineDTO();
        lines.add(a1);
        PowerMockito.when(maintenanceMaterialLineService.queryByHeadId(Mockito.any())).thenReturn(lines);
        maintenanceMaterialHeadServiceImpl.confirmReceipt(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    /*Started by AICoder, pid:mbb779db93n5553148270b8a81f3c80f1680826a*/
    @Test
    public void getTBCBillWithoutCiv() throws Exception {
        List<MaintenanceMaterialInfoDTO> list = new ArrayList<>();
        PowerMockito.when(maintenanceMaterialHeadRepository.getTBCBillLast2Year()).thenReturn(list);
        List<MaintenanceMaterialInfoDTO> resultList = maintenanceMaterialHeadServiceImpl.getTBCBillWithoutCiv();
        Assert.assertTrue(resultList.isEmpty());

        MaintenanceMaterialInfoDTO dto = new MaintenanceMaterialInfoDTO();
        dto.setItemNo("046050300101");
        dto.setCreateUser("00000000");
        dto.setMaterialRequisitionBill("1001");
        list.add(dto);
        MaintenanceMaterialInfoDTO materialInfoDTO1 = new MaintenanceMaterialInfoDTO();
        materialInfoDTO1.setCreateUser("00000001");
        materialInfoDTO1.setItemNo("046050300101");
        materialInfoDTO1.setMaterialRequisitionBill("1001");
        list.add(materialInfoDTO1);
        MaintenanceMaterialInfoDTO materialInfoDTO2 = new MaintenanceMaterialInfoDTO();
        materialInfoDTO2.setCreateUser("00000001");
        materialInfoDTO2.setMaterialRequisitionBill("1002");
        materialInfoDTO2.setItemNo("046050300102");
        list.add(materialInfoDTO2);
        List<CivControlInfoDTO> civControlList = new ArrayList<>();
        PowerMockito.when(iscpRemoteService.getCivControlInfo((Mockito.anyList()))).thenReturn(civControlList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        PowerMockito.when(centerfactoryRemote.getHrmPersonInfo((Mockito.anyList()))).thenReturn(hrmPersonInfoDTOMap);
        try {
            maintenanceMaterialHeadServiceImpl.getTBCBillWithoutCiv();
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("046050300101");
        civControlList.add(civControlInfoDTO);
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("测试");
        hrmPersonInfoDTO.setId("00000001");
        hrmPersonInfoDTOMap.put("00000001", hrmPersonInfoDTO);
        try {
            maintenanceMaterialHeadServiceImpl.getTBCBillWithoutCiv();
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }
    /*Ended by AICoder, pid:mbb779db93n5553148270b8a81f3c80f1680826a*/

    @Test
    public void createSupplementBill() throws Exception {
        CreateSupplementBillParamDTO dto = new CreateSupplementBillParamDTO();
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CREATE_SUPPLEMENT_BILL_PARAM_EMPTY, e.getExMsgId());
        }

        dto.setUseFor("装测二科");
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CREATE_SUPPLEMENT_BILL_PARAM_EMPTY, e.getExMsgId());
        }

        dto.setDelivery("河源14栋2F");
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CREATE_SUPPLEMENT_BILL_PARAM_EMPTY, e.getExMsgId());
        }

        List<SupplementBillDTO> paramBillInfoList = new ArrayList<>();
        SupplementBillDTO supplementBillDTO = new SupplementBillDTO();
        paramBillInfoList.add(supplementBillDTO);
        dto.setBillInfoList(paramBillInfoList);
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CREATE_SUPPLEMENT_BILL_PARAM_ERROR, e.getExMsgId());
        }
        supplementBillDTO.setMaterialRequisitionBill("IMESWXLL202405210001");
        SupplementBillDTO supplementBillDTO1 = new SupplementBillDTO();
        supplementBillDTO1.setMaterialRequisitionBill("IMESWXLL202405210002");
        supplementBillDTO1.setWisId("WMWHSE2");
        paramBillInfoList.add(supplementBillDTO1);

        List<SysLookupTypesDTO> lookupValue1508 = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(lookupValue1508);
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.DELIVERY_NOT_EXIST, e.getExMsgId());
        }

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        lookupValue1508.add(sysLookupTypesDTO);
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.DELIVERY_NOT_EXIST, e.getExMsgId());
        }

        sysLookupTypesDTO.setAttribute1("54");
        dto.setFactoryId("55");
        List<MaintenanceMaterialInfoDTO> billInfoList = new ArrayList<>();
        MaintenanceMaterialInfoDTO materialInfoDTO = new MaintenanceMaterialInfoDTO();
        materialInfoDTO.setMaterialRequisitionBill("IMESWXLL202405210001");
        materialInfoDTO.setItemNo("046050300101");
        materialInfoDTO.setNeedQty(10);
        billInfoList.add(materialInfoDTO);
        MaintenanceMaterialInfoDTO materialInfoDTO1 = new MaintenanceMaterialInfoDTO();
        materialInfoDTO1.setMaterialRequisitionBill("IMESWXLL202405210002");
        materialInfoDTO1.setItemNo("046050300102");
        materialInfoDTO1.setNeedQty(5);
        billInfoList.add(materialInfoDTO1);

        PowerMockito.when(maintenanceMaterialHeadRepository.getNeedQty(Mockito.anyList())).thenReturn(billInfoList);
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypesDTO.setLookupMeaning("WMWHSE1");
        lookupTypesDTO.setAttribute1("55");
        lookupTypesDTOList.add(lookupTypesDTO);
        lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypesDTO.setLookupMeaning("WMWHSE2");
        lookupTypesDTO.setAttribute1("55");
        lookupTypesDTOList.add(lookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.eq(Constant.LOOK_UP_TYPE_6609))).thenReturn(lookupTypesDTOList);

        List<Lotxlocxid> okLotxlocxidList = new ArrayList<>();
        List<Lotxlocxid> holdLotxlocxidList = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.getStockQtyWithWisId(Mockito.any(), Mockito.any(), Mockito.eq(Constant.STATUS_OK))).thenReturn(okLotxlocxidList);
        PowerMockito.when(DatawbRemoteService.getStockQtyWithWisId(Mockito.any(), Mockito.any(), Mockito.eq(Constant.STATUS_HOLD))).thenReturn(holdLotxlocxidList);

        Lotxlocxid lotxlocxid = new Lotxlocxid();
        lotxlocxid.setQty(20);
        lotxlocxid.setSku("046050300101");
        lotxlocxid.setWisId("WMWHSE1");
        holdLotxlocxidList.add(lotxlocxid);
        Lotxlocxid lotxlocxid1 = new Lotxlocxid();
        lotxlocxid1.setQty(20);
        lotxlocxid1.setSku("046050300102");
        lotxlocxid1.setWisId("WMWHSE3");
        holdLotxlocxidList.add(lotxlocxid1);
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_ENOUGH_STOCK, e.getExMsgId());
        }
        lotxlocxid1.setWisId("WMWHSE2");
        List<SysLookupTypesDTO> leadFlagLookUpList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.eq(MpConstant.LOOKUP_TYPE_ISLEAD))).thenReturn(leadFlagLookUpList);
        try {
            maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.GET_LOOKUP_VALUE_ERROR, e.getExMsgId());
        }
        SysLookupTypesDTO leadFlagLookUp = new SysLookupTypesDTO();
        leadFlagLookUp.setLookupMeaning("1");
        leadFlagLookUp.setAttribute1("10");
        leadFlagLookUpList.add(leadFlagLookUp);
        leadFlagLookUp = new SysLookupTypesDTO();
        leadFlagLookUp.setLookupMeaning("2");
        leadFlagLookUp.setAttribute1("20");
        leadFlagLookUpList.add(leadFlagLookUp);

        List<MaintenanceMaterialInfoDTO> maintenanceBillInfo = new ArrayList<>();
        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
        PowerMockito.when(maintenanceMaterialHeadRepository.getBillInfoByBillNo(Mockito.eq("IMESWXLL202405210002"))).thenReturn(maintenanceBillInfo);
        List<SupplementBillDTO> result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isNotBlank(result.get(0).getErrMsg()));

        MaintenanceMaterialInfoDTO maintenanceMaterialHeadDTO = new MaintenanceMaterialInfoDTO();
        maintenanceMaterialHeadDTO.setMaterialRequisitionBill("IMESWXLL202405210002");
        maintenanceMaterialHeadDTO.setProdplanId("7777666");
        maintenanceMaterialHeadDTO.setItemNo("101222101111");
        maintenanceMaterialHeadDTO.setNeedQty(10);
        maintenanceMaterialHeadDTO.setBrandName("");
        maintenanceBillInfo.add(maintenanceMaterialHeadDTO);

        MaintenanceMaterialInfoDTO maintenanceMaterialHeadDTO1 = new MaintenanceMaterialInfoDTO();
        maintenanceMaterialHeadDTO1.setMaterialRequisitionBill("IMESWXLL202405210002");
        maintenanceMaterialHeadDTO1.setProdplanId("7777666");
        maintenanceMaterialHeadDTO1.setItemNo("101222101111");
        maintenanceMaterialHeadDTO1.setNeedQty(10);
        maintenanceMaterialHeadDTO1.setBrandName("LJ");
        maintenanceBillInfo.add(maintenanceMaterialHeadDTO1);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isNotBlank(result.get(0).getErrMsg()));

        maintenanceBillInfo.remove(maintenanceMaterialHeadDTO1);
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.anyString())).thenReturn(workOrderList);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isNotBlank(result.get(0).getErrMsg()));

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("7777666-SMT-A5502");
        psWorkOrderDTO.setLineCode("SMT-1");
        psWorkOrderDTO.setLeadFlag("1");
        workOrderList.add(psWorkOrderDTO);

        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(Mockito.anyString())).thenReturn(null);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isNotBlank(result.get(0).getErrMsg()));
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("101222101111");
        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(Mockito.anyString())).thenReturn(bsItemInfo);
        PowerMockito.when(ProductionDeliveryRemoteService.isKeyDevices(Mockito.anyString())).thenReturn("N");
        PdRequirementHeaderDTO submitSupplementBillResult = new PdRequirementHeaderDTO();
        submitSupplementBillResult.setReqNo("BLD001");
        PowerMockito.when(ProductionDeliveryRemoteService.submitSupplementBill(Mockito.any())).thenReturn(submitSupplementBillResult);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isBlank(result.get(0).getReqNo()));

        maintenanceMaterialHeadDTO.setBrandName("LJ");
        List<BaItemBrandstyle> brandInfoList = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.queryItemBrandInfo(Mockito.anyString(), Mockito.anyString())).thenReturn(brandInfoList);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isBlank(result.get(0).getReqNo()));
        BaItemBrandstyle brandstyle = new BaItemBrandstyle();
        brandInfoList.add(brandstyle);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isBlank(result.get(0).getReqNo()));

        brandstyle.setItemUuid(123L);
        result = maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        Assert.assertTrue(StringUtils.isBlank(result.get(0).getReqNo()));
        maintenanceMaterialHeadDTO1.setChangeType("以旧换新");
        maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
        maintenanceMaterialHeadDTO1.setChangeType("新领");
        maintenanceMaterialHeadServiceImpl.createSupplementBill(dto);
    }


    @Test
    public void checkStockOfBillNoList() throws Exception {
        List<String> billNoList = new ArrayList<>();
        String factoryId = "";
        try {
            maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.BILL_NO_IS_EMPTY, e.getExMsgId());
        }

        billNoList.add("IMESWXLL202103100003");
        billNoList.add("IMESWXLL202103110004");
        List<MaintenanceMaterialInfoDTO> billInfoList = new ArrayList<>();
        PowerMockito.when(maintenanceMaterialHeadRepository.getNeedQty(Mockito.eq(billNoList))).thenReturn(billInfoList);
        try {
            maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.BILL_NOT_EXISTS, e.getExMsgId());
        }

        MaintenanceMaterialInfoDTO dto = new MaintenanceMaterialInfoDTO();
        dto.setMaterialRequisitionBill("IMESWXLL202103100003");
        dto.setItemNo("046050300101");
        dto.setNeedQty(10);
        billInfoList.add(dto);
        dto = new MaintenanceMaterialInfoDTO();
        dto.setMaterialRequisitionBill("IMESWXLL202103110004");
        dto.setItemNo("046050300103");
        dto.setNeedQty(10);
        billInfoList.add(dto);
        try {
            maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FACTORY_ID_IS_NULL, e.getExMsgId());
        }

        factoryId = "52";
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupTypesDTOList);
        try {
            maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        SysLookupTypesDTO lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypesDTO.setLookupMeaning("WMWHSE1");
        lookupTypesDTO.setAttribute1("55");
        lookupTypesDTOList.add(lookupTypesDTO);
        SysLookupTypesDTO lookupTypesDTO1 = new SysLookupTypesDTO();
        lookupTypesDTO1.setLookupMeaning("WMWHSE2");
        lookupTypesDTO1.setAttribute1("55");
        lookupTypesDTOList.add(lookupTypesDTO1);
        try {
            maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FACTORY_ID_NOT_CONIFG_LOOKUP, e.getExMsgId());
        }

        factoryId = "55";
        List<Lotxlocxid> okLotxlocxidList = new ArrayList<>();
        List<Lotxlocxid> holdLotxlocxidList = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.getStockQtyWithWisId(Mockito.any(), Mockito.any(), Mockito.eq(Constant.STATUS_OK))).thenReturn(okLotxlocxidList);
        PowerMockito.when(DatawbRemoteService.getStockQtyWithWisId(Mockito.any(), Mockito.any(), Mockito.eq(Constant.STATUS_HOLD))).thenReturn(holdLotxlocxidList);
        List<MaintenanceMaterialInfoDTO> result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertEquals(0, result.size());

        Lotxlocxid lotxlocxid = new Lotxlocxid();
        lotxlocxid.setQty(20);
        lotxlocxid.setSku("046050300101");
        lotxlocxid.setWisId("WMWHSE1");
        Lotxlocxid lotxlocxid1 = new Lotxlocxid();
        lotxlocxid1.setQty(20);
        lotxlocxid1.setSku("046050300103");
        lotxlocxid1.setWisId("WMWHSE2");
        Lotxlocxid lotxlocxid2 = new Lotxlocxid();
        lotxlocxid2.setQty(20);
        lotxlocxid2.setSku("046050300101");
        lotxlocxid2.setWisId("WMWHSE1");
        Lotxlocxid lotxlocxid3 = new Lotxlocxid();
        lotxlocxid3.setQty(20);
        lotxlocxid3.setSku("046050300103");
        lotxlocxid3.setWisId("WMWHSE2");
        okLotxlocxidList.add(lotxlocxid2);
        okLotxlocxidList.add(lotxlocxid3);
        holdLotxlocxidList.add(lotxlocxid);
        holdLotxlocxidList.add(lotxlocxid1);

        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertTrue(result.isEmpty());

        lookupTypesDTO.setAttribute2("Y");
        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertTrue(result.isEmpty());

        lotxlocxid2.setQty(0);
        lotxlocxid3.setQty(0);
        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertEquals("WMWHSE1", result.get(0).getWisId());

        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertEquals("WMWHSE1", result.get(0).getWisId());

        lotxlocxid.setQty(2);
        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertEquals("WMWHSE2", result.get(0).getWisId());

        lotxlocxid.setQty(null);
        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertEquals("WMWHSE2", result.get(0).getWisId());

        lookupTypesDTO.setAttribute2("");
        SysLookupTypesDTO lookupTypesDTO2 = new SysLookupTypesDTO();
        lookupTypesDTO2.setLookupMeaning("WMWHSE3");
        lookupTypesDTO2.setAttribute1("55");
        lookupTypesDTO2.setAttribute2("Y");
        lookupTypesDTOList.add(lookupTypesDTO2);

        lotxlocxid.setQty(20);
        okLotxlocxidList.add(lotxlocxid);
        okLotxlocxidList.add(lotxlocxid1);
        result = maintenanceMaterialHeadServiceImpl.checkStockOfBillNoList(billNoList, factoryId);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void setExplain() throws Exception {
        List<MaintenanceMaterialInfoDTO> maintenanceBillInfo = new ArrayList<>();
        MaintenanceMaterialInfoDTO dto = new MaintenanceMaterialInfoDTO();
        maintenanceBillInfo.add(dto);
        Assert.assertEquals("", Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "setExplain", maintenanceBillInfo, "123", "10337580"));
        dto.setBrandName("123");
        try {
            Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "setExplain", maintenanceBillInfo, "123", "10337580");
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        hrmPersonInfoDTOMap.put("10337580", new HrmPersonInfoDTO());
        PowerMockito.when(centerfactoryRemote.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfoDTOMap);
        dto.setMainSn("12333");
        dto.setSubSn("12345");
        try {
            Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "setExplain", maintenanceBillInfo, "123", "10337580");
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void updateHeadWithSupplementBill() throws Exception {
        // 触发
        MaintenanceMaterialInfoDTO dto = new MaintenanceMaterialInfoDTO();
        Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "updateHeadWithSupplementBill", dto, new PdRequirementHeaderDTO());
        dto.setChangeType("以旧换新");
        Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "updateHeadWithSupplementBill", dto, new PdRequirementHeaderDTO());
        Assert.assertEquals("以旧换新", dto.getChangeType());
        dto.setChangeType("新领");
        Whitebox.invokeMethod(maintenanceMaterialHeadServiceImpl, "updateHeadWithSupplementBill", dto, new PdRequirementHeaderDTO());
    }

    @Test
    public void builderOperationButton() throws Exception {
        List<MaintenanceMaterialHeadDTO> rows = new ArrayList<>();
        MaintenanceMaterialHeadDTO dto = new MaintenanceMaterialHeadDTO();
        rows.add(dto);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        sysLookupValuesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(sysLookupValuesDTOList);
        maintenanceMaterialHeadServiceImpl.builderOperationButton(rows, "1024");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}