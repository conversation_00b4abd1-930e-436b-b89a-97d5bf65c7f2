package com.zte.autoTest.unitTest;

import com.zte.application.WarehousehmEntryInfoService;
import com.zte.application.impl.WarehousehmEntryInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.WarehousehmEntryInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.WarehouseEntryInfoDetailDTO;
import com.zte.interfaces.dto.WarehousehmEntryInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2024/1/31 17:49
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class WarehousehmEntryInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WarehousehmEntryInfoServiceImpl warehousehmEntryInfoService;

    @Mock
    private WarehousehmEntryInfoRepository warehousehmEntryInfoRepository;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Before
    public void init() {
        // PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void getWeDetailInfoBySn() throws Exception {
        List<WarehousehmEntryInfoDTO> list = new ArrayList() {{
            add(new WarehousehmEntryInfoDTO() {{
                setSn("701170611111");
            }});
        }};
        PowerMockito.when(warehousehmEntryInfoRepository.selectDetailInfoBySn(Mockito.anyMap())).thenReturn(null);
        PowerMockito.when(warehouseEntryInfoRepository.selectDetailInfoBySn(Mockito.anyMap())).thenReturn(new ArrayList<>());
        try {
            warehousehmEntryInfoService.getWeDetailInfoBySn(list);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.when(warehousehmEntryInfoRepository.selectDetailInfoBySn(Mockito.anyMap())).thenReturn(new ArrayList() {{
            add(new WarehouseEntryInfoDetailDTO());
        }});
        try {
            warehousehmEntryInfoService.getWeDetailInfoBySn(list);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
}
