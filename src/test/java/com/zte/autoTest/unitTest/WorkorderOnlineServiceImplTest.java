package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.SmtMachineMTLHistoryHServiceImpl;
import com.zte.application.impl.WorkorderOnlineServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.*;

/**
 * @Author:
 * @Date: 2020/12/17 11:04
 */
@PrepareForTest({MicroServiceRestUtil.class,CrafttechRemoteService.class,BasicsettingRemoteService.class,PlanscheduleRemoteService.class, ObtainRemoteServiceDataUtil.class, CommonUtils.class})
public class WorkorderOnlineServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WorkorderOnlineServiceImpl service;

    @Mock
    private OfflineTaskService offlineTaskService;

    @Mock
    private WorkorderOnlineRepository workorderOnlineRepository;

    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Mock
    private SmtMachineMaterialReturnService smtMachineMaterialReturnService;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Mock
    private BSmtBomDetailService bSmtBomDetailService;

    @Mock
    private SmtFeedErrorSkipService smtFeedErrorSkipService;

    @Mock
    private SmtMachineMTLHistoryHServiceImpl smtMachineMTLHistoryHServiceImpl;

    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Test
    public void filterBSmtBomDetails() throws Exception {
        List<BSmtBomDetail> noTransferDetail = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setModuleCode("010");
        noTransferDetail.add(bSmtBomDetail);
        Whitebox.invokeMethod(service,"filterBSmtBomDetails",new PsWorkOrderDTO() {{
            setCraftSection("DIP");
            setWorkOrderNo("workOrder");
            setLineCode("loneCode");
            setChangeLineByModule(true);
        }},noTransferDetail);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void verifyWorkOrderCanStartWork() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPartsPlanNo(anyString())).thenReturn(Lists.newArrayList(new PsTask()));
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(anyString())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setCraftSection("1");
            setChildFlag(true);
        }}));
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(new CFLine(){{
            setSupChgLineOnModule("2");
        }});
        service.verifyWorkOrderCanStartWork(new PsWorkOrderDTO() {{
            setCraftSection("SMT-A");
            setWorkOrderNo("workOrder");
            setLineCode("loneCode");
        }});
        service.verifyWorkOrderCanStartWork(new PsWorkOrderDTO() {{
            setCraftSection("DIP");
            setWorkOrderNo("workOrder");
            setLineCode("loneCode");
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void verifyWorkOrderCanStartWorkTwo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPartsPlanNo(anyString())).thenReturn(Lists.newArrayList(new PsTask()));
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(anyString())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setCraftSection("1");
            setChildFlag(true);
        }}));
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(new CFLine(){{
            setSupChgLineOnModule("Y");
        }});
        try {
            service.verifyWorkOrderCanStartWork(new PsWorkOrderDTO() {{
                setWorkOrderNo("workOrder");
                setLineCode("loneCode");
            }});

        }catch (Exception e){
            Assert.assertEquals(MessageId.CRAFTSECTION_IS_NULL, e.getMessage());
        }
        try {
            service.verifyWorkOrderCanStartWork(new PsWorkOrderDTO() {{
                setCraftSection("DIP");
                setWorkOrderNo("workOrder");
            }});

        }catch (Exception e){
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }
        try {
            service.verifyWorkOrderCanStartWork(new PsWorkOrderDTO() {{
                setCraftSection("SMT-A");
                setLineCode("loneCode");
            }});

        }catch (Exception e){
            Assert.assertEquals(MessageId.WORKORDER_IS_FULL, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void verifyTheLineBodyIsOpenedAccordingToTheModule() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(new CFLine(){{
            setSupChgLineOnModule("Y");
        }});
        try {
            Whitebox.invokeMethod(service,"verifyTheLineBodyIsOpenedAccordingToTheModule","2");
        }catch (Exception e){
            Assert.assertEquals(MessageId.LINE_NOT_FOUND, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void verifyTheLineBodyIsOpenedAccordingToTheModuleTwo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service,"verifyTheLineBodyIsOpenedAccordingToTheModule","2");
        }catch (Exception e){
            Assert.assertEquals(MessageId.LINE_NOT_FOUND, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void startChildWorkOrder() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPartsPlanNo(anyString())).thenReturn(Lists.newArrayList(new PsTask()));
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(anyString())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setCraftSection("1");
            setChildFlag(true);
        }}));
        Assert.assertNotNull(service.startChildWorkOrder(new PsWorkOrderDTO() {{
            setCraftSection("1");
        }}));
    }

    @Test
    public void beginwork4workorder() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class, ObtainRemoteServiceDataUtil.class);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLineCodeInfo(anyString())).thenReturn(new CFLine());
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(Lists.newArrayList(new SysLookupTypesDTO() {{
            setLookupCode(new BigDecimal("10510005"));
            setMeaning("on");
            setLookupMeaning("on");
        }}));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.anyString())).thenReturn(new SysLookupTypesDTO() {{
            setLookupCode(new BigDecimal("286520001"));
            setMeaning("on");
            setLookupMeaning("N");
        }});
        try {
            Assert.assertNotNull(service.beginwork4workorder(new PsWorkOrderDTO() {{
                setCraftSection("1");
                setLineCode("1");
                setWorkOrderStatus("已提交");
            }}));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkWorkOrderStatus() throws Exception {
        List<SysLookupValuesDTO> typeCodes = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupMeaning("123");
        typeCodes.add(a1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.lookupType.VALUE_1004076))
                .thenReturn(typeCodes);

        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        workOrder.setWorkOrderStatus("123");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(workOrder);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(any()))
                .thenReturn(null);
        try {
            service.checkWorkOrderStatus("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_NO_STATUS_ERROR, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void judgeIsPartsPlanNo() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(any()))
                .thenReturn(Lists.newArrayList());
        service.judgeIsPartsPlanno("1");
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfoNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetail() {{
                    setNextProcess(MpConstant.PROCESS_CODE_PARTS_CARDS);
                }}));
        Assert.assertTrue(service.judgeIsPartsPlanno("1"));
    }

    @Test
    public void insertWorkorderOnline() {
        Assert.assertNotNull(service.insertWorkorderOnline(new WorkorderOnline()));
    }

    @Test
    public void insertWorkorderOnlineSelective() {
        service.insertWorkorderOnlineSelective(new WorkorderOnline());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void deleteWorkorderOnlineById() {
        Assert.assertNotNull(service.deleteWorkorderOnlineById(new WorkorderOnline()));
    }

    @Test
    public void updateWorkorderOnlineByIdSelective() {
        service.updateWorkorderOnlineByIdSelective(new WorkorderOnline());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateWorkorderOnlineById() {
        Assert.assertNotNull(service.updateWorkorderOnlineById(new WorkorderOnline()));
    }

    @Test
    public void selectWorkorderOnline() throws Exception {
        WorkorderOnline record = new WorkorderOnline();
        List<WorkorderOnline> workorderOnlineList = new LinkedList<>();
        WorkorderOnline a1 = new WorkorderOnline();
        workorderOnlineList.add(a1);
        PowerMockito.when(
                workorderOnlineRepository.selectWorkorderOnlineSelective(Mockito.any()))
                .thenReturn(workorderOnlineList);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\":[{\"workOrderId\":\"3\",\"lineCode\":\"33\"}]\n" +
                        "  \n" +
                        "}");
        Assert.assertThrows(NullPointerException.class, () ->Assert.assertNotNull(service.selectWorkorderOnline(record)));
    }

    @Test
    public void getWorkOrderByLineCode() {
        Assert.assertNotNull(service.getWorkOrderByLineCode("1"));
    }

    @Test
    public void testSetMBom() throws Exception {
        // 创建并初始化PsEntityPlanBasic对象
        PsWorkOrderDTO dto1 = new PsWorkOrderDTO();
        dto1.setItemNo("11");
        dto1.setProdplanId("prodplanId_2");

        PsWorkOrderDTO dto2 = new PsWorkOrderDTO();
        dto2.setItemNo("11");
        dto2.setProdplanId("prodplanId_3");

        List<PsWorkOrderDTO> listEntity = new ArrayList<>();
        Whitebox.invokeMethod(service, "setMBom", listEntity);
        assertNotNull(listEntity);

        listEntity.add(dto1);
        listEntity.add(dto2);

        // 模拟远程服务调用返回null的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(null);
        Whitebox.invokeMethod(service, "setMBom", listEntity);
        assertNotNull(listEntity);

        // 创建并初始化BProdBomHeaderDTO对象
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo1");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_1");
        }});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo2");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_2");
        }});

        // 模拟远程服务调用返回有效数据的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOList);
        Whitebox.invokeMethod(service, "setMBom", listEntity);
        assertNotNull(listEntity);
    }

    @Test
    public void isFeedInputTest() throws Exception {
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        CFLine lineInfo = new CFLine();
        psWorkOrderDTO.setCraftSection("SMT-A");
        psWorkOrderDTO.setWorkOrderId("123");
        psWorkOrderDTO.setWorkOrderNo("123");
        psWorkOrderDTO.setLineCode("123");

        PsWorkOrderSmt b1 = new PsWorkOrderSmt();
        b1.setTransferStrategy("1");
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTInfo(anyMap())).thenReturn(b1);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.MOUNT_INFO_IS_NULL);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "isFeedInput", psWorkOrderDTO,lineInfo));

        b1.setCfgStatus("已导入");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTInfo(anyMap())).thenReturn(b1);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "isFeedInput", psWorkOrderDTO,lineInfo));
    }
}
