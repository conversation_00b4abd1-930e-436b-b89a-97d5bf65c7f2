package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.ScrapBillDetailServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.CfWorkshop;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.ResultData;
import com.zte.domain.model.ScrapBillDetailRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.ScrapBillDetailEntityDTO;
import com.zte.interfaces.dto.ScrapBillHeaderEntityDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.io.FilenameUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2021/6/19
 */
@PrepareForTest({PlanscheduleRemoteService.class,ExcelCommonUtils.class ,ImesExcelUtil.class,CommonUtils.class,ConstantInterface.class,
        RedisHelper.class, MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class, FilenameUtils.class})
public class ScrapBillDetailServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ScrapBillDetailServiceImpl scrapBillDetailService;

    @Mock
    private ScrapBillDetailRepository scrapBillDetailrepository;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private MultipartFile file;
    @Test
    public void getItemNoList() throws Exception {
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfoList.add(psWipInfo);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        Whitebox.invokeMethod(scrapBillDetailService, "getItemNoList", psWipInfoList, scrapBillDetailEntityDTOList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void downloadTemplate() throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class);
        scrapBillDetailService.downloadTemplate(null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getCanNotScrapList() throws Exception {
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOList = new ArrayList<>();

        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setSn("sn1");
        pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_COMPLETE);
        pmRepairRcvDetailDTO.setRepairProuctMstype(Constant.REPAIR_PROUCT_MS_TYPE_SCRAP);
        pmRepairRcvDetailDTOList.add(pmRepairRcvDetailDTO);

        PmRepairRcvDetailDTO pmRepairRcvDetailDTO2 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO2.setSn("sn1");
        pmRepairRcvDetailDTO2.setStatus(Constant.REPAIR_STATUS_RESTORE);
        pmRepairRcvDetailDTO2.setRepairProuctMstype(Constant.REPAIR_PROUCT_MS_TYPE_SCRAP);
        pmRepairRcvDetailDTOList.add(pmRepairRcvDetailDTO2);

        PmRepairRcvDetailDTO pmRepairRcvDetailDTO3 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO3.setSn("sn2");
        pmRepairRcvDetailDTO3.setStatus(Constant.REPAIR_STATUS_COMPLETE);
        pmRepairRcvDetailDTO3.setRepairProuctMstype(Constant.REPAIR_PROUCT_MS_TYPE_SCRAP);
        pmRepairRcvDetailDTOList.add(pmRepairRcvDetailDTO3);
        Whitebox.invokeMethod(scrapBillDetailService,"getCanNotScrapList",pmRepairRcvDetailDTOList);
        Assert.assertEquals("sn1", pmRepairRcvDetailDTO.getSn());
    }


    @Test
    public void getCountMap() throws Exception {
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO.setSn("************");
        scrapBillDetailEntityDTO.setBillNo("billNo");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        PowerMockito.when(scrapBillDetailrepository.getList(any())).thenReturn(scrapBillDetailEntityDTOList);
        Whitebox.invokeMethod(scrapBillDetailService,"getCountMap",scrapBillDetailEntityDTOList);
        Assert.assertEquals("************", scrapBillDetailEntityDTO.getSn());
    }

    @Test
    public void excelAnalysis() throws Exception {
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = JSON.parseObject("{\"billNo\":\"111\",\"sn\":\"\",\"scrapType\":\"0\",\"billStatus\":\"\",\"createBy\":\"\",\"createDateStart\":\"2021-12-16 00:00:00\",\"createDateEnd\":\"2022-06-16 23:59:59\",\"rows\":10,\"page\":1}",
                ScrapBillDetailEntityDTO.class);
        scrapBillDetailEntityDTO.setBillNo("124");
        scrapBillDetailEntityDTO.setSn("7778889001");
        scrapBillDetailEntityDTO.setProdplanId("7778889");
        scrapBillDetailEntityDTO.setWorkshopName("11");
        scrapBillDetailEntityDTO.setCreateBy("11");
        scrapBillDetailEntityDTO.setApplyer("1027");
        scrapBillDetailEntityDTO.setCurrProcessCode("8");
        scrapBillDetailEntityDTO.setProcessCodeBefore("8");
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO1 = JSON.parseObject("{\"billNo\":\"111\",\"sn\":\"\",\"scrapType\":\"0\",\"billStatus\":\"\",\"createBy\":\"\",\"createDateStart\":\"2021-12-16 00:00:00\",\"createDateEnd\":\"2022-06-16 23:59:59\",\"rows\":10,\"page\":1}",
                ScrapBillDetailEntityDTO.class);
        scrapBillDetailEntityDTO1.setBillNo("124");
        scrapBillDetailEntityDTO1.setSn("7778888001");
        scrapBillDetailEntityDTO1.setProdplanId("7778888");
        scrapBillDetailEntityDTO1.setWorkshopName("11");
        scrapBillDetailEntityDTO1.setCreateBy("11");
        scrapBillDetailEntityDTO1.setApplyer("1027");
        scrapBillDetailEntityDTO1.setCurrProcessCode("8");
        scrapBillDetailEntityDTO1.setProcessCodeBefore("8");

        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO1);
        ResultData resultData =new ResultData();
        resultData.setCode(RetCode.SUCCESS_CODE);
        resultData.setData(scrapBillDetailEntityDTOList);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,ExcelCommonUtils.class);
        PowerMockito.when(ExcelCommonUtils.resolveExcel(any(),any(),any())).thenReturn(resultData);
        PowerMockito.when(ExcelCommonUtils.saveFile(any(),any(),any())).thenReturn("1231231");
        PowerMockito.mockStatic(RedisHelper.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("11241");
        lookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOList);

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("P0003");
        psWipInfo.setSn("************");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);

        PowerMockito.mockStatic(ConstantInterface.class,HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-centerfactory/user/ucs/getByAccountId");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<CfWorkshop> cfWorkshopList= new ArrayList<>();
        CfWorkshop cfWorkshop = new CfWorkshop();
        cfWorkshop.setWorkshopName("11");
        cfWorkshopList.add(cfWorkshop);
        PowerMockito.when(BasicsettingRemoteService.getCfWorkshopListByWorkshopNameList(any())).thenReturn(cfWorkshopList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("ZZ190816CPE005-CS浙江电信");
        psTask.setSourceSys("WMES");
        psTask.setProdplanId("7778889");
        psTask.setTaskQty(new BigDecimal("10"));
        psTask.setOrgId(new BigDecimal("4434"));
        psTask.setTaskId(UUID.randomUUID().toString());
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskList);
        try{
            scrapBillDetailService.excelAnalysis("124","N",file);
        }catch (Exception e){
            Assert.assertEquals("7778889001", scrapBillDetailEntityDTO.getSn());
        }
        psTask.setItemNo("12312");
        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("ZZ190816CPE005-CS浙江电信");
        psTask1.setSourceSys("WMES");
        psTask1.setProdplanId("7778888");
        psTask1.setTaskQty(new BigDecimal("10"));
        psTask1.setOrgId(new BigDecimal("395"));
        psTask1.setTaskId(UUID.randomUUID().toString());
        psTask1.setItemNo("12312");
        psTaskList.add(psTask1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskList);
        PowerMockito.when(file.getOriginalFilename()).thenReturn("123222.xlsx");
        try{
            scrapBillDetailService.excelAnalysis("124","N",file);
        }catch (Exception e){
            Assert.assertEquals("7778889001", scrapBillDetailEntityDTO.getSn());
        }
    }
    @Test
    public void uploadFile() throws Exception {
        Assert.assertNotNull(scrapBillDetailService.uploadFile(file,"124"));
        PowerMockito.mockStatic(FilenameUtils.class);
        PowerMockito.when(file.getOriginalFilename()).thenReturn("123222.xlsx");
        Assert.assertNotNull(scrapBillDetailService.uploadFile(file,"124"));
    }

    @Test
    public void getList() throws Exception {
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = JSON.parseObject("{\"billNo\":\"111\",\"sn\":\"\",\"scrapType\":\"0\",\"billStatus\":\"\",\"createBy\":\"\",\"createDateStart\":\"2021-12-16 00:00:00\",\"createDateEnd\":\"2022-06-16 23:59:59\",\"rows\":10,\"page\":1}",
                ScrapBillDetailEntityDTO.class);
        scrapBillDetailEntityDTO.setBillNo("124");
        scrapBillDetailEntityDTO.setCreateBy("11");
        scrapBillDetailEntityDTO.setApplyer("1027");
        scrapBillDetailEntityDTO.setCurrProcessCode("8");
        scrapBillDetailEntityDTO.setProcessCodeBefore("8");
        Assert.assertNotNull(scrapBillDetailService.getList(scrapBillDetailEntityDTO));
    }
    @Test
    @PrepareForTest({ConstantInterface.class, RedisHelper.class, MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class})
    public void pageList() throws Exception {
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = JSON.parseObject("{\"billNo\":\"111\",\"sn\":\"\",\"scrapType\":\"0\",\"billStatus\":\"\",\"createBy\":\"\",\"createDateStart\":\"2021-12-16 00:00:00\",\"createDateEnd\":\"2022-06-16 23:59:59\",\"rows\":10,\"page\":1}",
                ScrapBillDetailEntityDTO.class);
        scrapBillDetailEntityDTO.setEmpNo("124");
        scrapBillDetailEntityDTO.setCreateBy("11");
        scrapBillDetailEntityDTO.setApplyer("1027");
        scrapBillDetailEntityDTO.setCurrProcessCode("8");
        scrapBillDetailEntityDTO.setProcessCodeBefore("8");
        List<ScrapBillHeaderEntityDTO> scrapBillHeaderlist = new ArrayList<>();
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("11241");
        lookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOList);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        PowerMockito.when(scrapBillDetailrepository.pageList(any())).thenReturn(scrapBillDetailEntityDTOList);
        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("8");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(barcodeLockDetailService.getBsProcessList(any())).thenReturn(bsProcessList);
        Assert.assertEquals("11241", sysLookupTypesDTO.getLookupMeaning());
        scrapBillDetailService.pageList(scrapBillDetailEntityDTO);
    }

    @Test
    public void pageQueryForSubmitWarehouseTest() {
        ScrapBillDetailEntityDTO dto = new ScrapBillDetailEntityDTO();
        dto.setPage(1);
        dto.setRows(20);
        int total = 1;
        PowerMockito.when(scrapBillDetailrepository.getCountForSubmitWarehous(any())).thenReturn(total);
        List<ScrapBillDetailEntityDTO> list = new ArrayList<>();
        ScrapBillDetailEntityDTO dto1 = new ScrapBillDetailEntityDTO();
        dto1.setSn("sn1");
        PowerMockito.when(scrapBillDetailrepository.getPageForSubmitWarehous(any(), anyInt(), anyInt())).thenReturn(list);
        List<PsWipInfo> wipInfos = new ArrayList<>();
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(anyList())).thenReturn(wipInfos);
        scrapBillDetailService.pageQueryForSubmitWarehouse(dto);
        list.add(dto1);
        Assert.assertNotNull(scrapBillDetailService.pageQueryForSubmitWarehouse(dto));
    }

    @Test
    public void updateProcessCodeBatchBySnTest() throws Exception {
        List<ScrapBillDetailEntityDTO> scrapBilldetList = new ArrayList<>();
        scrapBillDetailService.updateProcessCodeBatchBySn(scrapBilldetList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setTaskQty() {
        scrapBillDetailService.setTaskQty(Lists.newArrayList(), Lists.newArrayList());
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(Lists.newArrayList(
                new PsTask(){{setProdplanId("1");setTaskQty(new BigDecimal(6));}}
        ));
        scrapBillDetailService.setTaskQty(Lists.newArrayList(
                new ScrapBillDetailEntityDTO(){{setProdplanId("1");setTotalApprovedScrap(BigDecimal.ONE);}},
                new ScrapBillDetailEntityDTO(){{setProdplanId("1");}}),
                Lists.newArrayList("1"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void verifySubSn() {
        try {
            PowerMockito.mockStatic(PlanscheduleRemoteService.class, CommonUtils.class,BasicsettingRemoteService.class);
            PowerMockito.when(PlanscheduleRemoteService.getSubTaskByTaskNo(any())).thenReturn(Lists.newArrayList(
                    new PsTask(){{setProdplanId("2");setPartsPlanno("t1");}},
                    new PsTask(){{setProdplanId("3");setPartsPlanno("t1");}}
            ));
            PowerMockito.when(CommonUtils.getLmbMessage(MessageId.SUB_SN_IS_NOT_NULL)).thenReturn(MessageId.SUB_SN_IS_NOT_NULL);
            PowerMockito.when(CommonUtils.getLmbMessage(MessageId.NOT_CONTROL_BY_BATECH)).thenReturn(MessageId.NOT_CONTROL_BY_BATECH);
            scrapBillDetailService.verifySubSn(Lists.newArrayList(new ScrapBillDetailEntityDTO(){{
                        setProdplanId("1");
                        setSubSn("21,32，41");
                    }},new ScrapBillDetailEntityDTO(){{
                        setProdplanId("1");
                    }},new ScrapBillDetailEntityDTO(){{
                        setProdplanId("1");
                        setSubSn("21,22，31");
                    }},new ScrapBillDetailEntityDTO(){{
                        setProdplanId("1");
                        setSubSn("21,22，31，21,22，3121,22，31，21,22，" +
                                "21,22，31，21,22，3121,22，31，21,22，3121,22，31，21,22，31" +
                                "21,22，31，21,22，3121,22，31，21,22，31" +
                                "21,22，31，21,22，3121,22，31，21,22，3121,22，31，21,22，31" +
                                "3121,22，31，21,22，3121,22，31，21,22，31");
                    }},new ScrapBillDetailEntityDTO(){{
                        setProdplanId("2");
                        setSubSn("21,22，31");
                    }}),
                    Lists.newArrayList(new PsTask(){{setProdplanId("1"); setTaskNo("t1");}}));
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(MessageId.SUB_SN_IS_NOT_NULL, e.getMessage());
        }
    }

    @Test
    public void testSubSnBindingCheck() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CommonUtils.class);
        String taskNo = "21134";
        String finalCheckTaskNos = "31134";
        List<String> subLists = new ArrayList<>();
        subLists.add("123");
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setSubSn("3333,4444");
        Whitebox.invokeMethod(scrapBillDetailService,"subSnBindingCheck", scrapBillDetailEntityDTO, taskNo, finalCheckTaskNos, subLists);
        Assert.assertEquals("31134",finalCheckTaskNos);
    }

    @Test
    public void testSetMBom() throws Exception {
        // 创建并初始化PsEntityPlanBasic对象
        ScrapBillDetailEntityDTO dto1 = new ScrapBillDetailEntityDTO();
        dto1.setItemNo("11");
        dto1.setProdplanId("prodplanId_2");

        ScrapBillDetailEntityDTO dto2 = new ScrapBillDetailEntityDTO();
        dto2.setItemNo("11");
        dto2.setProdplanId("prodplanId_3");

        List<ScrapBillDetailEntityDTO> listEntity = new ArrayList<>();
        Whitebox.invokeMethod(scrapBillDetailService, "setMBom", listEntity);
        assertNotNull(listEntity);

        listEntity.add(dto1);
        listEntity.add(dto2);

        // 模拟远程服务调用返回null的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(null);
        Whitebox.invokeMethod(scrapBillDetailService, "setMBom", listEntity);
        assertNotNull(listEntity);

        // 创建并初始化BProdBomHeaderDTO对象
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo1");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_1");
        }});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo2");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_2");
        }});

        // 模拟远程服务调用返回有效数据的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOList);
        Whitebox.invokeMethod(scrapBillDetailService, "setMBom", listEntity);
        assertNotNull(listEntity);
    }
}