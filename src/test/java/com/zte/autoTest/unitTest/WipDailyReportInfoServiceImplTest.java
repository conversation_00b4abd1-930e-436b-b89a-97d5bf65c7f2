package com.zte.autoTest.unitTest;

import com.zte.application.BProdBomService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WarehousehmEntryInfoService;
import com.zte.application.impl.WipDailyReportInfoServiceImpl;
import com.zte.application.impl.strategy.WRDetailListHandle;
import com.zte.client.CrafttechsysFeginClient;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: 10307315
 * @Date: 2022/1/17 下午2:58
 */
@PrepareForTest({MicroServiceRestUtil.class,CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, ImesExcelUtil.class,BeanUtils.class,BasicsettingRemoteService.class})
public class WipDailyReportInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipDailyReportInfoServiceImpl wipDailyReportInfoService;
    @Mock
    private WipDailyReportInfoRepository wipDailyReportInfoRepository;
    @Mock
    WarehousehmEntryInfoService warehousehmEntryInfoService;
    @Mock
    private PsWipInfoService psWipInfoServiceImpl;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private CrafttechsysFeginClient crafttechsysFeginClient;
    @Mock
    private WRDetailListHandle handle;
    @Mock
    AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    private BProdBomService bProdBomService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
    }

    @Test
    public void insertWipDailyReportInfo() {
        wipDailyReportInfoService.insertWipDailyReportInfo(null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateWipDailyReportInfoById() {
        wipDailyReportInfoService.updateWipDailyReportInfoById(null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void selectGroupByWd() throws Exception {
        WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();
        record.setFactoryId(new BigDecimal("52"));
        wipDailyReportInfoService.selectGroupByWd(record);

        List<PsTaskInfoDTO> list = new LinkedList<>();
        PsTaskInfoDTO b1 = new PsTaskInfoDTO();
        b1.setProdplanId("123");
        b1.setTaskNo("123");
        b1.setTaskQty(new BigDecimal("3"));
        list.add(b1);
        PowerMockito.when(PlanscheduleRemoteService.getPstaskNew(Mockito.anyMap()))
                .thenReturn(list);

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setDescriptionChin("123");
        lookupValueList.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setDescriptionChin(Constant.DAILY_REPORT_SIZE);
        c2.setLookupMeaning("2");
        lookupValueList.add(c2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(Mockito.any()))
                .thenReturn(lookupValueList);
        List<WipDailyReportInfoDTO> splitmainListTemp = new LinkedList<>();
        WipDailyReportInfoDTO d1 = new WipDailyReportInfoDTO();
        d1.setAttribute1("123");
        d1.setTaskQty(2);
        splitmainListTemp.add(d1);
        PowerMockito.when(wipDailyReportInfoRepository.selectGroupByWd(Mockito.any()))
                .thenReturn(splitmainListTemp);

        List<WarehousehmEntryInfo> splitlistMap4FinishIn = new LinkedList<>();
        WarehousehmEntryInfo e1 = new WarehousehmEntryInfo();
        e1.setProdplanId("123");
        e1.setInQty("2");
        splitlistMap4FinishIn.add(e1);
        PowerMockito.when(warehousehmEntryInfoService.getWarehousehmEntryInfoSum(Mockito.any()))
                .thenReturn(splitlistMap4FinishIn);

        List<PsWipInfoDTO> splistListMap4TotalIn = new LinkedList<>();
        PsWipInfoDTO f1 = new PsWipInfoDTO();
        f1.setAttribute1("123");
        f1.setInCountTotal("2");
        splistListMap4TotalIn.add(f1);
        PowerMockito.when(psWipInfoServiceImpl.getList4report(Mockito.any()))
                .thenReturn(splistListMap4TotalIn);

        List<ContainerContentInfoDTO> splitDeliverList = new LinkedList<>();
        ContainerContentInfoDTO g1 = new ContainerContentInfoDTO();
        g1.setProdPlanId("123");
        g1.setSmt("2");
        g1.setDip("3");
        g1.setAss("4");
        splitDeliverList.add(g1);
        PowerMockito.when(wipDailyReportInfoRepository.getDeliverList(Mockito.any()))
                .thenReturn(splitDeliverList);

        List<BSProcess> proList = new LinkedList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessName("123");
        bsProcess.setProcessCode("2");
        proList.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.bsProcess4(Mockito.any()))
                .thenReturn(proList);

        Assert.assertNotNull(wipDailyReportInfoService.selectGroupByWd(record));

    }

    @Test
    public void getListByCondition() throws Exception{
        WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();
        wipDailyReportInfoService.getListByCondition(record);

        record.setStartTime("2023-02-12");
        record.setEndTime("2023-02-01");

        List<WipDailyReportInfo> wipDailyReportInfoList = new LinkedList<>();
        WipDailyReportInfo a1 = new WipDailyReportInfo();
        a1.setItemCode("i2222");
        wipDailyReportInfoList.add(a1);
        PowerMockito.when(wipDailyReportInfoRepository.getListByCondition(Mockito.any()))
                .thenReturn(wipDailyReportInfoList);

        List<WipDailyReportInfo> tempList = new LinkedList<>();
        WipDailyReportInfo b1 = new WipDailyReportInfo();
        b1.setProdplanId("123");
        PowerMockito.when(wipDailyReportInfoRepository.getCountWipPerProdplanId(Mockito.any()))
                .thenReturn(tempList);

        Assert.assertNotNull(wipDailyReportInfoService.getListByCondition(record));
        a1.setProdplanId("123");
        WipDailyReportInfo a2 = new WipDailyReportInfo();
        a2.setItemCode("i2222");
        a1.setProdplanId("1231");
        wipDailyReportInfoList.add(a2);
        Map<String, String> bomMap = new HashMap<>();
        bomMap.put("1231","222");
        Assert.assertNotNull(wipDailyReportInfoService.getListByCondition(record));
        PowerMockito.when(bProdBomService.getBProdBomListBatch(Mockito.anyList())).thenReturn(bomMap);
        Assert.assertNotNull(wipDailyReportInfoService.getListByCondition(record));

    }

    @Test
    public void exportModel2() {
        wipDailyReportInfoService.exportModel(null,null,null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void prepareExportData() throws Exception{
        WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();
        Assert.assertNotNull(wipDailyReportInfoService.prepareExportData(record));
    }
    @Test
    public void getBuildListByCond() throws Exception{
        WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();

        List<WipDailyReportInfo> wipDailyReportInfoList = new LinkedList<>();
        WipDailyReportInfo a1 = new WipDailyReportInfo();
        wipDailyReportInfoList.add(a1);
        PowerMockito.when(wipDailyReportInfoRepository.getBuildListByCond(Mockito.any()))
                .thenReturn(wipDailyReportInfoList);

        Assert.assertNotNull(wipDailyReportInfoService.getBuildListByCond(record));
    }
	@Test
	public void setLeftQty() throws Exception {
		WipDailyReportInfoDTO record = new WipDailyReportInfoDTO();
		List<String> inProdplanIds = new ArrayList<>();
		inProdplanIds.add(0,"456789");
		inProdplanIds.add(1,"123456");
		record.setInProdplanIds(inProdplanIds);
		List<List<String>> list = new ArrayList<>();
		list.add(inProdplanIds);
		List<WipDailyReportInfo> countList = new ArrayList<>();
		WipDailyReportInfo dto = new WipDailyReportInfo();
		dto.setProdplanId("123456");
		dto.setCountWipPerProdplanId(1L);
		countList.add(dto);
		PowerMockito.when(wipDailyReportInfoRepository.getCountWipPerProdplanId(Mockito.any())).thenReturn(countList);

		List<WipDailyReportInfoDTO> dailyReportInfos = new ArrayList<>();
		WipDailyReportInfoDTO wipDailyReportInfo = new WipDailyReportInfoDTO();
		wipDailyReportInfo.setTaskQty(1L);
		wipDailyReportInfo.setProdplanId("123456");
		dailyReportInfos.add(wipDailyReportInfo);
		wipDailyReportInfoService.setLeftQty(record,list,dailyReportInfos);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void prepareExportData2() throws Exception {
		WipDailyReportInfoDTO wipDailyReportInfoDTO = new WipDailyReportInfoDTO();
		List<WipDailyReportInfo> wipDailyReportInfoList = new ArrayList<>();
		wipDailyReportInfoDTO.setProdplanId("test");
		PowerMockito.when(wipDailyReportInfoRepository.getListByCondition(Mockito.any())).thenReturn(wipDailyReportInfoList);
        Assert.assertNotNull(wipDailyReportInfoService.prepareExportData(wipDailyReportInfoDTO));
	}

	@Test
	public void exportModel() throws Exception {
		List<String> wipDailyReportInfoList = new ArrayList<>();
		wipDailyReportInfoService.exportModel("",new String[]{},new ArrayList(),new String[]{});
		wipDailyReportInfoList.add("1");
		wipDailyReportInfoService.exportModel("",new String[]{},wipDailyReportInfoList,new String[]{});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
	}
}
