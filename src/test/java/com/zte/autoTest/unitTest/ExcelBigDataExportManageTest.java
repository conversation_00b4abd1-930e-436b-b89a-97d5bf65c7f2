package com.zte.autoTest.unitTest;

import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelBigDataExportManage;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.Matchers.any;

@PrepareForTest({RedisCacheUtils.class})
public class ExcelBigDataExportManageTest extends PowerBaseTestCase {

    @InjectMocks
    ExcelBigDataExportManage manage;

    @Test
    public void serverFileManager() {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.when(RedisCacheUtils.get(any(), any())).thenReturn(null);
        manage.serverFileManager("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}