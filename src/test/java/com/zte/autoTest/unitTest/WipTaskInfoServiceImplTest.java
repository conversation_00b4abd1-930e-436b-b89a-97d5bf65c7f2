package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.WipTaskInfoServiceImpl;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipTaskInfoRepository;
import com.zte.interfaces.dto.WipFirstDateGetDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 *
 * @Author:
 * @Date: 2020/9/21 10:53
 */
public class WipTaskInfoServiceImplTest  extends PowerBaseTestCase {

	@InjectMocks
	private WipTaskInfoServiceImpl service;

	@Mock
	private PsWipInfoRepository wipInfoRepository;

	@Test
	public void getWipFirstDate() {
		Assert.assertNotNull(service.getWipFirstDate(new WipFirstDateGetDTO()));
	}

	@Test
	public void getWipCraftQty() {
		Assert.assertNotNull(service.getWipCraftQty(Lists.newArrayList("q"), Lists.newArrayList("1")));
	}

}
