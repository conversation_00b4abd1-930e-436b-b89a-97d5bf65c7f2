package com.zte.autoTest.unitTest;

import com.zte.application.impl.AssemblyResultServiceImpl;
import com.zte.application.impl.AuxMaterialTracingServiceImpl;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.AssemblyResultHisRepository;
import com.zte.domain.model.AssemblyResultRepository;
import com.zte.domain.model.AuxMaterialTracingRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.interfaces.dto.AuxMaterialTracingEntityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({ExcelCommonUtils.class,RedisHelper.class})
public class AuxMaterialTracingServiceImplTest extends PowerBaseTestCase {

    @Mock
    private AuxMaterialTracingRepository auxMaterialTracingrepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @InjectMocks
    private AuxMaterialTracingServiceImpl auxMaterialTracingService;
    @Mock
    private HttpServletResponse response;

    @Test
    public void batchQuery() throws Exception {
        AuxMaterialTracingEntityDTO auxMaterialTracingEntityDTO = new AuxMaterialTracingEntityDTO();
        Page<AuxMaterialTracingEntityDTO> pageInfo = new Page<>();
        pageInfo.setTotalPage(5);

        Whitebox.invokeMethod(auxMaterialTracingService, "batchQuery", auxMaterialTracingEntityDTO, new BigExcelProcesser(), 3);
        List<AuxMaterialTracingEntityDTO> temporaryScheduledTaskEntityDTOList = new ArrayList<>();
        temporaryScheduledTaskEntityDTOList.add(auxMaterialTracingEntityDTO);
        PowerMockito.when(auxMaterialTracingrepository.exportExcel(any())).thenReturn(temporaryScheduledTaskEntityDTOList);
        Whitebox.invokeMethod(auxMaterialTracingService, "batchQuery", auxMaterialTracingEntityDTO, new BigExcelProcesser(),3);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void exportExcel() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        AuxMaterialTracingEntityDTO auxMaterialTracingEntityDTO = new AuxMaterialTracingEntityDTO();
        try {
            auxMaterialTracingService.exportExcel(response, auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CREATION_TIME_CANNOT_BE_EMPTY.equals(e.getMessage()));
        }

        auxMaterialTracingEntityDTO.setStartDateStr("2021-01-01 01:01:01");
        auxMaterialTracingEntityDTO.setEndDateStr("2023-01-01 01:01:01");
        try {
            PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
            auxMaterialTracingService.exportExcel(response, auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.THE_TIME_FRAME_CANNOT_BE_GREATER_THAN_ONE_YEAR.equals(e.getMessage()));
        }
        auxMaterialTracingEntityDTO.setStartDateStr("2023-01-01 01:01:01");
        try {
            PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
            auxMaterialTracingService.exportExcel(response, auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CURRENTLY_EXPORTING.equals(e.getMessage()));
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        try {
            auxMaterialTracingService.exportExcel(response, auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertFalse(MessageId.CURRENTLY_EXPORTING.equals(e.getMessage()));
        }

        List<AuxMaterialTracingEntityDTO> auxMaterialTracingEntityDTOList = new ArrayList<>();
        auxMaterialTracingEntityDTOList.add(auxMaterialTracingEntityDTO);
        PowerMockito.when(auxMaterialTracingrepository.pageList(any())).thenReturn(auxMaterialTracingEntityDTOList);
        try {
            auxMaterialTracingService.exportExcel(response, auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertFalse(MessageId.CURRENTLY_EXPORTING.equals(e.getMessage()));
        }
    }
    @Test
    public void batchInsert()throws Exception {
        auxMaterialTracingService.batchInsert(new ArrayList<>());
        List<AuxMaterialTracingEntityDTO> auxMaterialTracingEntityDTOList=new ArrayList<>();
        AuxMaterialTracingEntityDTO auxMaterialTracingEntityDTO=new AuxMaterialTracingEntityDTO();
        auxMaterialTracingEntityDTOList.add(auxMaterialTracingEntityDTO);
        Assert.assertEquals(0,auxMaterialTracingService.batchInsert(auxMaterialTracingEntityDTOList));
    }
    @Test
    public void pageList()throws Exception {
        List<AuxMaterialTracingEntityDTO> auxMaterialTracingEntityDTOList=new ArrayList<>();
        AuxMaterialTracingEntityDTO auxMaterialTracingEntityDTO=new AuxMaterialTracingEntityDTO();
        try {
            auxMaterialTracingService.pageList(auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CREATION_TIME_CANNOT_BE_EMPTY.equals(e.getMessage()));
        }
        auxMaterialTracingEntityDTO.setStartDateStr("2021-01-01 01:01:01");
        auxMaterialTracingEntityDTO.setEndDateStr("2023-01-01 01:01:01");
        try {
            auxMaterialTracingService.pageList(auxMaterialTracingEntityDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.THE_TIME_FRAME_CANNOT_BE_GREATER_THAN_ONE_YEAR.equals(e.getMessage()));
        }
        auxMaterialTracingEntityDTO.setStartDateStr("2023-01-01 01:01:01");
        auxMaterialTracingService.pageList(auxMaterialTracingEntityDTO);
        auxMaterialTracingEntityDTO.setCreateBy("1027");
        auxMaterialTracingEntityDTO.setLastUpdatedBy("1027");
        auxMaterialTracingEntityDTOList.add(auxMaterialTracingEntityDTO);
        PowerMockito.when(auxMaterialTracingrepository.pageList(anyObject())).thenReturn(auxMaterialTracingEntityDTOList);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        auxMaterialTracingService.pageList(auxMaterialTracingEntityDTO);
    }
}
