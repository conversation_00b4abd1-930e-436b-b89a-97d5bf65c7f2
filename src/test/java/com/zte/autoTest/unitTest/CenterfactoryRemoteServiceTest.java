package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.ProdUnbindingSetting;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WipDailyStatisticReport;
import com.zte.domain.model.WipScanHistory;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BindQueryDTO;
import com.zte.interfaces.dto.BindQuerySectionDTO;
import com.zte.interfaces.dto.BmEmailReceiverDTO;
import com.zte.interfaces.dto.BsBomHierarchicalDetailDTO;
import com.zte.interfaces.dto.CfBizSequenceDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.NetworkLicenseBindingDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PrintRecordDTO;
import com.zte.interfaces.dto.PrintTemplateInfoDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.ScrapBillHeaderEntityDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.VReelidInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/10/14 17:17
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteService.class, JacksonJsonConverUtil.class, CommonUtils.class,MicroServiceDiscoveryInvoker.class,
        HttpClientUtil.class, ConstantInterface.class, HttpRemoteUtil.class, JSON.class, ServiceDataBuilderUtil.class, MicroServiceRestUtil.class})
public class CenterfactoryRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private CenterfactoryRemoteService service;

    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
    }

    @Test
    public void batchUpdateQtyTaskByPkCodes(){
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        service.batchUpdateQtyTaskByPkCodes(null);
        Assert.assertTrue(Objects.nonNull(service));
    }

    @Test
    public void getCenterPkCodeListOrigin() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PkCodeInfo(){{setAttribute1("12");}}));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        Assert.assertNotNull(service.getCenterPkCodeListOrigin("test"));
    }

    @Test
    public void getNeedBindList() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new ProdBindingSettingDTO()));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.getNeedBindList("test", "test", "test"));
    }

    @Test
    public void postNeedBindSectionList() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new ProdBindingSettingDTO()));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.postNeedBindSectionList(new BindQuerySectionDTO()));
    }

    @Test
    public void batchUpdateLastUpdateDate() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.batchUpdateLastUpdateDate(Lists.newArrayList()));
    }
    @Test
    public void queryWarehouseEntryIdIsExist() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList("123"));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        Assert.assertNotNull(service.queryWarehouseEntryIdIsExist(Lists.newArrayList("123")));
    }
    @Test
    public void getPushedTestProcessSnScanData() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new CustomerDataLogDTO()));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        Assert.assertNotNull(service.getPushedTestProcessSnScanData(Lists.newArrayList(new WipScanHistory())));
    }
    @Test
    public void getPushedSnScanData() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("123");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.getPushedSnScanData(Lists.newArrayList("123")));
    }
    @Test
    public void selectPsTaskByProdIdSet() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsTask()));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        Assert.assertNotNull(service.selectPsTaskByProdIdSet(Sets.newHashSet()));
    }

    @Test
    public void getFactoryIdByProdplanId() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn("");
        Assert.assertNotNull(service.getFactoryIdByProdplanId(Lists.newArrayList("123")));
    }

    @Test
    public void batchInsertIntoWarehouseEntryIdIsExist() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn("");
        try {
            service.batchInsertIntoWarehouseEntryIdIsExist(Lists.newArrayList());
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void centerFactoryUpdatePkCodeInfo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn("");
        try {
            service.centerFactoryUpdatePkCodeInfo(new PkCodeInfo());
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void getPDMTechChgInfoByNo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        try {
            service.getPDMTechChgInfoByNo("");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void batchReelIdDataProcess() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        VReelidInfoDTO dto = new VReelidInfoDTO();
        service.batchReelIdDataProcess(dto);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertNull(service.batchReelIdDataProcess(dto));
    }

    @Test
    public void getPkCodeInfo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        service.getPkCodeInfo("123");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertNull(service.getPkCodeInfo("123"));
    }

    @Test
    public void getProductNameCn() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        Assert.assertNotNull(service.getProductNameCn("123"));
    }

    @Test
    public void getProductNameCnTwo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        Assert.assertNull(service.getProductNameCn("123"));
    }

    @Test
    public void bindingNetworkLicense() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        NetworkLicenseBindingDTO bindingDTO = new NetworkLicenseBindingDTO();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        service.bindingNetworkLicense(bindingDTO);
        Assert.assertNotNull(bindingDTO);
    }

    @Test
    public void bindingNetworkLicenseTwo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        NetworkLicenseBindingDTO bindingDTO = new NetworkLicenseBindingDTO();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("123");
        service.bindingNetworkLicense(bindingDTO);
        Assert.assertNotNull(bindingDTO);
    }

    @Test
    public void centerFactoryInsertPkCodeInfo() {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        service.centerFactoryInsertPkCodeInfo(new PkCodeInfoDTO());

        PowerMockito.when(ServiceDataBuilderUtil.checkResponseSimpleBo(Mockito.any()))
                .thenReturn("1");
        Assert.assertNotNull(service.centerFactoryInsertPkCodeInfo(new PkCodeInfoDTO()));
    }

    private void mockHttp() {
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
    }

    @Test
    public void queryRecordBySnList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class, HttpRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        Page page = new Page();
        List<PrintRecordDTO> printRecordDTOList = new ArrayList<>();
        printRecordDTOList.add(new PrintRecordDTO());
        page.setRows(printRecordDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(page);
                        }}));
        Assert.assertNotNull(service.queryRecordBySnList(new ArrayList<>()));
    }

    @Test
    public void batchInsertWipDailyStatisticReportInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class, HttpRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[\"0000\"]," +
                "\"other\":null}";
        PowerMockito.when(HttpRemoteService.remoteExePostForRequestJson(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        service.batchInsertWipDailyStatisticReportInfo(new ArrayList<>(), "2");
        List<WipDailyStatisticReport> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReportEntityDTO = new WipDailyStatisticReport();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(Lists.newArrayList(""));
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(serviceData)
        );
        service.batchInsertWipDailyStatisticReportInfo(wipDailyStatisticReportEntityDTOList, "2");
        serviceData.setCode(new RetCode() {{
            setCode(RetCode.BUSINESSERROR_CODE);
        }});
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(serviceData)
        );
        service.batchInsertWipDailyStatisticReportInfo(wipDailyStatisticReportEntityDTOList, "2");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void createBillNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[\"0000\"]," +
                "\"other\":null}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);
        service.createBillNo("IEMSBF", "", 4, null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void writeOmLog() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.writeOmLog(Maps.newHashMap()));
    }

    @Test
    public void getList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        service.getPeriodValue(Maps.newHashMap());
        service.queryPkCodeInfoList(new PkCodeInfoDTO());
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/bmEmailReceiver/selectBmEmailInfo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        CenterfactoryRemoteService.getEmailReceiverList(new BmEmailReceiverDTO());
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNull(CenterfactoryRemoteService.getSubLevelPremanuInfooDip(Maps.newHashMap()));
    }

    //查询中心工厂绑定数据
    @Test
    public void postNeedBindList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "    {\n" +
                        "      \"sort\": null,\n" +
                        "      \"order\": null,\n" +
                        "      \"page\": 1,\n" +
                        "      \"rows\": 10,\n" +
                        "      \"settingId\": null,\n" +
                        "      \"productCode\": \"129571751003AOB\",\n" +
                        "      \"productName\": null,\n" +
                        "      \"itemCode\": \"129571751004AFB\",\n" +
                        "      \"itemName\": \"ZXRAN R9214E S7200 M94PAB7572A\",\n" +
                        "      \"itemType\": \"1\",\n" +
                        "      \"usageCount\": 4,\n" +
                        "      \"processCode\": null,\n" +
                        "      \"craftSection\": null,\n" +
                        "      \"sourceSystem\": null,\n" +
                        "      \"createBy\": null,\n" +
                        "      \"createDate\": null,\n" +
                        "      \"lastUpdatedBy\": null,\n" +
                        "      \"lastUpdatedDate\": null,\n" +
                        "      \"enabledFlag\": null,\n" +
                        "      \"remark\": null,\n" +
                        "      \"orgId\": null,\n" +
                        "      \"factoryId\": null,\n" +
                        "      \"entityId\": null,\n" +
                        "      \"attribute1\": null,\n" +
                        "      \"attribute2\": null,\n" +
                        "      \"attribute3\": null,\n" +
                        "      \"attribute4\": null,\n" +
                        "      \"attribute5\": null,\n" +
                        "      \"chiDesc\": null,\n" +
                        "      \"processName\": null,\n" +
                        "      \"bindedCount\": null,\n" +
                        "      \"mainSn\": null,\n" +
                        "      \"boundNo\": 0,\n" +
                        "      \"prodPlanId\": null\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"sort\": null,\n" +
                        "      \"order\": null,\n" +
                        "      \"page\": 1,\n" +
                        "      \"rows\": 10,\n" +
                        "      \"settingId\": null,\n" +
                        "      \"productCode\": \"129571751003AOB\",\n" +
                        "      \"productName\": null,\n" +
                        "      \"itemCode\": \"042080800007\",\n" +
                        "      \"itemName\": \"新型3.96间距电缆压接插头（簧片）\",\n" +
                        "      \"itemType\": \"0\",\n" +
                        "      \"usageCount\": 2,\n" +
                        "      \"processCode\": null,\n" +
                        "      \"craftSection\": null,\n" +
                        "      \"sourceSystem\": null,\n" +
                        "      \"createBy\": null,\n" +
                        "      \"createDate\": null,\n" +
                        "      \"lastUpdatedBy\": null,\n" +
                        "      \"lastUpdatedDate\": null,\n" +
                        "      \"enabledFlag\": null,\n" +
                        "      \"remark\": null,\n" +
                        "      \"orgId\": null,\n" +
                        "      \"factoryId\": null,\n" +
                        "      \"entityId\": null,\n" +
                        "      \"attribute1\": null,\n" +
                        "      \"attribute2\": null,\n" +
                        "      \"attribute3\": null,\n" +
                        "      \"attribute4\": null,\n" +
                        "      \"attribute5\": null,\n" +
                        "      \"chiDesc\": null,\n" +
                        "      \"processName\": null,\n" +
                        "      \"bindedCount\": null,\n" +
                        "      \"mainSn\": null,\n" +
                        "      \"boundNo\": 0,\n" +
                        "      \"prodPlanId\": null\n" +
                        "    }\n" +
                        "  ],\n" +
                        "  \"other\": {\n" +
                        "    \"msg\": \"操作成功\",\n" +
                        "    \"opResult\": \"Success\",\n" +
                        "    \"event_id\": \"com.zte.interfaces.ProdBindingSettingController@getProdBindingSettingList\",\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"costTime\": \"233401ms\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"clickTime\": \"Wed Apr 07 17:44:25 CST 2021\",\n" +
                        "    \"tag\": \"查询单板绑定扫描需要绑定代码清单\",\n" +
                        "    \"serviceName\": \"zte-mes-manufactureshare-productionmgmtsysxiaoming\",\n" +
                        "    \"userId\": \"10275508\"\n" +
                        "  }\n" +
                        "}");
        BindQueryDTO dto = new BindQueryDTO();
        dto.setMainProductCode("123");
        Assert.assertNotNull(service.postNeedBindList(dto));
    }

    @Test
    public void postUnBindingList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        ProdUnbindingSetting dto = new ProdUnbindingSetting();
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "\t\"code\": {\n" +
                        "\t\t\"code\": \"0000\",\n" +
                        "\t\t\"msgId\": \"RetCode.Success\",\n" +
                        "\t\t\"msg\": \"操作成功\"\n" +
                        "\t},\n" +
                        "\t\"bo\": null\n" +
                        "}");

        Assert.assertNotNull(service.postUnBindingList(dto));
    }

    @Test
    public void selectBatchBomByBomCode() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("1");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(Lists.newArrayList(new BsBomHierarchicalDetailDTO()));
                        }}));
        Assert.assertNotNull(service.selectBatchBomByBomCode(Lists.newArrayList("1")));
    }

    @Test
    public void getPbNumByProduction() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("1");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                }}));
        Assert.assertNotNull(service.getPbNumByProduction(""));
    }

    @Test
    public void createReelIds() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("1");
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setCode(new RetCode("1", "1"));
                    }}));
            service.createReelIds("", "", "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GENERATE_REELID_IN_THE_FACTORY_NEW, e.getMessage());
        }
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList());
                    }}));
            Assert.assertNotNull(service.createReelIds("", "", ""));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GENERATE_REELID_IN_THE_FACTORY_NEW, e.getMessage());
        }
    }


    public void batchUpdateQty() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        List<PkCodeInfoDTO> list = new ArrayList<>();
        PkCodeInfoDTO pkCodeInfo = new PkCodeInfoDTO();
        pkCodeInfo.setFactoryId(new BigDecimal("55"));
        pkCodeInfo.setLpn("oldLpn");
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                }})
        );
        service.batchUpdateQty(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void batchInsertPkCodeInfoAndHistory() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setFactoryId(new BigDecimal("55"));
        pkCodeInfo.setLpn("oldLpn");
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                }})
        );
        service.batchInsertPkCodeInfoAndHistory(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void createCfBizCode() throws Exception {
        CfBizSequenceDTO cfBizSequenceDTO = new CfBizSequenceDTO();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "   \t\"123\"\n" +
                        "  ]\n" +
                        "}");


        Assert.assertNotNull(service.createCfBizCode(cfBizSequenceDTO));
    }

    @Test
    public void getNumByProductCodeAndItemCode() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        List<BBomDetailDTO> dtoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setFactoryId(new BigDecimal("55"));
        pkCodeInfo.setLpn("oldLpn");
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(Lists.newArrayList(new BBomDetailDTO()));
                        }}));
        Assert.assertNotNull(service.getNumByProductCodeAndItemCode(dtoList));
    }

    @Test
    public void getPsTaskListFromCenterFactoryTest() throws Exception {
        String prodPlanId = "23213123";
        PageRows<PsTask> pageRows = new PageRows<>();
        PsTask psTask = new PsTask();
        List<PsTask> listPsTask = new ArrayList<PsTask>();
        listPsTask.add(psTask);
        pageRows.setRows(listPsTask);
        String res = JSON.toJSONString(new ServiceData() {{
            setBo(pageRows);
        }});
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpGet(any(), any(), anyMap())).thenReturn(res);
        Assert.assertNotNull(service.getPsTaskListFromCenterFactory(prodPlanId));
    }

    @Test
    public void queryProdBindingBatch() throws Exception {
        BindQuerySectionDTO dto = new BindQuerySectionDTO();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"操作成功\"\n" +
                        "    },\n" +
                        "    \"bo\": []\n" +
                        "}");
        Assert.assertNotNull(service.queryProdBindingBatch(dto));

        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkResponseSimpleBo(Mockito.any()))
                .thenReturn("");
        Assert.assertTrue(CollectionUtils.isEmpty(service.queryProdBindingBatch(dto)));

    }

    @Test
    public void updateApproverInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        ApprovalProcessInfoEntityDTO dto = new ApprovalProcessInfoEntityDTO();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/approvalProcessInfo/updateApproverInfo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        service.updateApproverInfo(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getApprovalProInfoList() throws Exception {
        ApprovalProcessInfoEntityDTO dto = new ApprovalProcessInfoEntityDTO();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory");
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), anyString()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setCode(new RetCode("1", "1"));
                        setBo(Lists.newArrayList(new ApprovalProcessInfoEntityDTO()));
                    }}));
            service.getApprovalProInfoList(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList(new ApprovalProcessInfoEntityDTO()));
                    }}));
            Assert.assertNotNull(service.getApprovalProInfoList(dto));
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void scrapFlowWithChangeApproval() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        ScrapBillHeaderEntityDTO dto = new ScrapBillHeaderEntityDTO();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/approvalProcessInfo/scrapFlowWithChangeApproval");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        service.scrapFlowWithChangeApproval(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void queryUnBingForItemNo() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        Assert.assertNotNull(service.queryUnBingForItemNo("129229251001AQB"));

    }

    @Test
    public void queryBomHeadByProductCode() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(bo);

        Assert.assertNotNull(service.queryBomHeadByProductCode("129229251001AQB"));

    }

    @Test
    public void getSysLookupValuesCenter() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        SysLookupValues values = new SysLookupValues();
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(values);
                }})
        );
        Assert.assertNotNull(service.getSysLookupValuesCenter("12312"));
    }

    @Test
    public void isHomeTerminal() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("ooo");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/approvalProcessInfo/updateApproverInfo");
        Assert.assertFalse(service.isHomeTerminal("test"));
    }

    @Test
    public void testGetCraftAttributeForWorkOrder() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        Assert.assertNull(service.getCraftAttributeForWorkOrder(new ArrayList<>()));
    }

    @Test
    public void selectPreBomInfoByBomList() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        Assert.assertNull(service.selectPreBomInfoByBomList(new ArrayList<>()));
    }

    @Test
    public void getPsTaskByItemNoList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://xx/zte-mes-manufactureshare-centerfactory/1");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsTaskDTO()));
                }})
        );

        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("111");
        List<PsTaskDTO> psTasks = service.getPsTaskByItemNoList(itemNoList);
        Assert.assertEquals(1, psTasks.size());

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }})
        );

        itemNoList.add("111");
        List<PsTaskDTO> psTasks1 = service.getPsTaskByItemNoList(itemNoList);
        Assert.assertEquals(0, psTasks1.size());
    }


    @Test
    public void getPsTaskByProdplanIdList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://xx/zte-mes-manufactureshare-centerfactory/1");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsTaskDTO()));
                }})
        );

        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("111");
        List<PsTaskDTO> psTasks = service.getPsTaskByProdplanIdList(itemNoList);
        Assert.assertEquals(1, psTasks.size());

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }})
        );

        itemNoList.add("111");
        List<PsTaskDTO> psTasks1 = service.getPsTaskByProdplanIdList(itemNoList);
        Assert.assertEquals(0, psTasks1.size());
    }


    @Test
    public void getListByItemNoList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://xx/zte-mes-manufactureshare-centerfactory/1");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new CustomerItemsDTO()));
                }})
        );

        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("111");
        List<CustomerItemsDTO> psTasks = service.getListByItemNoList(itemNoList, "222");
        Assert.assertEquals(1, psTasks.size());

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }})
        );

        itemNoList.add("111");
        List<CustomerItemsDTO> psTasks1 = service.getListByItemNoList(itemNoList, "222");
        Assert.assertEquals(0, psTasks1.size());
    }


    @Test
    public void getPushErrorData() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
        mockHttp();
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://xx/zte-mes-manufactureshare-centerfactory/1");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new CustomerDataLogDTO()));
                }})
        );
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        List<CustomerDataLogDTO> customerDataLogDTOList = service.getPushErrorData(customerDataLogDTO);
        Assert.assertEquals(1, customerDataLogDTOList.size());

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }})
        );
        List<CustomerDataLogDTO> customerDataLogDTOList1 = service.getPushErrorData(customerDataLogDTO);
        Assert.assertEquals(0, customerDataLogDTOList1.size());
    }

    @Test
    public void queryTemplateInfoByTypeAndScene() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class, HttpRemoteService.class,
                HttpRemoteUtil.class,MicroServiceDiscoveryInvoker.class, CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        ServiceData<PrintTemplateInfoDTO> serviceData = new ServiceData<>();
        PrintTemplateInfoDTO printTemplateInfoDTO = new PrintTemplateInfoDTO();

        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class, CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PrintTemplateInfoDTO() {{
                        setAttribute1("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());

        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[\"0000\"]," +
                "\"other\":null}";
        PowerMockito.when(HttpRemoteService.remoteExePostForRequestJson(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("serviceDataStr");

        try {
            service.queryTemplateInfoByTypeAndScene(printTemplateInfoDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_QUERY_THE_PRINT_TEMPLATE_INFORMATION, e.getMessage());
        }

    }
    /* Started by AICoder, pid:ldf81f24a5e5008145f90bc360a5f22751a26875 */

    @Test
    public void convertAndIntegrateMBomTest() {
        List<String> productCodes = new ArrayList<>();
        String prodplanId ="test";
        PowerMockito.mockStatic(MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        productCodes.add("test");
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(productCodes);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("msg");
                    }});
                }}));
        List<String> strings = service.convertAndIntegrateMBom(productCodes, prodplanId);
        Assert.assertTrue("test".equals(strings.get(0)));
    }
    /* Ended by AICoder, pid:ldf81f24a5e5008145f90bc360a5f22751a26875 */
    /* Started by AICoder, pid:mf090089bdr013b145c908d4507fe452ee766598 */

    @Test
    public void queryMBomDetailChangeByProdplanId() {
        List<String> productCodes = new ArrayList<>();
        String prodplanId ="test";
        PowerMockito.mockStatic(MESHttpHelper.class, HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        productCodes.add("test");
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(productCodes);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("msg");
                    }});
                }}));
        List<BProdBomChangeDetailDTO> prodBomChangeDetailDTOList = service.queryMBomDetailChangeByProdplanId(new ArrayList<>());
        Assert.assertNull(prodBomChangeDetailDTOList);
    }
    /* Ended by AICoder, pid:mf090089bdr013b145c908d4507fe452ee766598 */

    @Test
    public void getBomDetailIncludeMBomConditionTest() {
        String sourceTask = "";
        String itemNo = "";

        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        String result = "";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        List<BBomDetailDTO> r = service.getBomDetailIncludeMBomCondition(sourceTask, itemNo);
        Assert.assertTrue(CollectionUtils.isEmpty(r));

    }


    @Test
    public void testGetBProdBomChangeDetail() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class,ServiceDataBuilderUtil.class);
        BProdBomChangeDetailDTO params = new BProdBomChangeDetailDTO();
        PowerMockito.when(ConstantInterface.getUrlStatic(Mockito.any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.any(), Mockito.any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        service.getBProdBomChangeDetail(params);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(Lists.newArrayList("012110100002")));
        List<BProdBomChangeDetailDTO> bProdBomChangeDetail = service.getBProdBomChangeDetail(params);
        Assert.assertNull(bProdBomChangeDetail);
    }

    /* Started by AICoder, pid:f8c8ff8bc0r1adb142ab0845c04c3c6a4c609896 */
    @Test
    public void queryProductCodeByProdPlanIdList() {
        mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo("mockedResult")).thenReturn("[]");

        // When
        List<BProdBomHeaderDTO> result = service.queryProductCodeByProdPlanIdList(null);

        // Then
        Assert.assertEquals(result.size(), 0);

        // When
        result = service.queryProductCodeByProdPlanIdList(Arrays.asList("prodPlanId"));

        // Then
        Assert.assertEquals(result.size(), 0);

        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        bProdBomHeaderDTO.setProductCode("123456789");
        bProdBomHeaderDTOS.add(bProdBomHeaderDTO);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSON.toJSONString(bProdBomHeaderDTOS));
        // When
        service.queryProductCodeByProdPlanIdList(Arrays.asList("prodPlanId"));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSON.toJSONString(new ArrayList<>()));
        // When
        result = service.queryProductCodeByProdPlanIdList(Arrays.asList("prodPlanId"));

        // Then
        Assert.assertEquals(result.size(), 0);
    }
    /* Ended by AICoder, pid:f8c8ff8bc0r1adb142ab0845c04c3c6a4c609896 */
    /* Started by AICoder, pid:488a8y6a3cqf2f4149200912303b5c6f14a54b6d */
    @Test
    public void getPushedSnScanDataOfL6() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.anyMap(), Mockito.any(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("123");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(service.getPushedSnScanDataOfL6(Lists.newArrayList("123")));
    }

    @Test
    public void queryListByCustomerAndItemNoListTest() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, ConstantInterface.class, HttpRemoteUtil.class);
        String customerName = "";
        ArrayList<String> itemNoList = new ArrayList<>();
        itemNoList.add("itemNo");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new CustomerItemsDTO(){{setId("12");}}));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(null);
        List<CustomerItemsDTO> customerItemsDTOList = service.queryListByCustomerAndItemNoList(customerName, itemNoList);
        Assert.assertTrue(customerItemsDTOList.size() == 0);
        ArrayList<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        customerItemsDTOS.add(new CustomerItemsDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(JSONObject.toJSONString(customerItemsDTOS));
        List<CustomerItemsDTO> customerItemsDTOList1 = service.queryListByCustomerAndItemNoList(customerName, itemNoList);
        Assert.assertTrue(customerItemsDTOList1.size() == 1);
    }

    /* Started by AICoder, pid:p01b9z61986ac1914be009f4b0c97b310a61e277 */
    @Test
    public void updateConfirmationStatus() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        service.updateConfirmationStatus("3","taskNo");
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:p01b9z61986ac1914be009f4b0c97b310a61e277 */

    @Test
    public void getPcbItemCodeTest() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, HttpRemoteUtil.class);
        List<String> itemNoList = new ArrayList<>();
        List<BBomDetailDTO> pcbItemCode = service.getPcbItemCode(itemNoList);
        Assert.assertTrue(pcbItemCode.size() == 0);
        itemNoList.add("test");
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new BBomDetailDTO(){{setItemCode("12");}}));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(null);
        List<BBomDetailDTO> pcbItemCode2 = service.getPcbItemCode(itemNoList);
        Assert.assertTrue(pcbItemCode2.size() == 0);
        ArrayList<BBomDetailDTO> list = new ArrayList<>();
        list.add(new BBomDetailDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(JSONObject.toJSONString(list));
        List<BBomDetailDTO> pcbItemCode1 = service.getPcbItemCode(itemNoList);
        Assert.assertTrue(pcbItemCode1.size() == 1);

    }

    @Test
    public void getProdplanIdByMBom() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class, ServiceDataBuilderUtil.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new BProdBomHeaderDTO(){{setProductCode("12");}}));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                        setMsg("test");
                    }});
                }}));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        Assert.assertNull(service.getProdplanIdByMBom(""));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(null);
        Assert.assertNull(service.getProdplanIdByMBom("123123"));
        List<BProdBomHeaderDTO> dtoList = new ArrayList<>();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSONObject.toJSONString(dtoList));
        Assert.assertNull(service.getProdplanIdByMBom("123123"));
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dtoList.add(dto);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSONObject.toJSONString(dtoList));
        Assert.assertNotNull(service.getProdplanIdByMBom("123123"));
    }
    /* Ended by AICoder, pid:488a8y6a3cqf2f4149200912303b5c6f14a54b6d */

    /* Started by AICoder, pid:waae9m1361lfdb51466b0a6280eaa654e9d11ad6 */
    @Test
    public void getNotPushDoneProdplanId() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList("7777666"));
                }})
        );
        List<String> result = service.getNotPushDoneProdplanId(Arrays.asList("alibaba"), 10, 1);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void mergeSnToPushDataDetail() {
        List<PushBoardDataDetailDTO> mergelist = new ArrayList<>();
        int count = service.mergeSnToPushDataDetail(mergelist);
        Assert.assertEquals(0, count);

        mergelist.add(new PushBoardDataDetailDTO());
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                }})
        );
        count = service.mergeSnToPushDataDetail(mergelist);
        Assert.assertEquals(1, count);
    }
    /* Ended by AICoder, pid:waae9m1361lfdb51466b0a6280eaa654e9d11ad6 */

    @Test
    public void testGetSysLookUpValue() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        List<SysLookupTypesDTO> result = service.getSysLookUpValue(new HashMap<String, Object>() {
            private static final long serialVersionUID = -4719862032415905586L;

            {
                put("map", "map");
            }
        });
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertNull(result);
    }

    @Test
    public void test_getCustomerItemsInfo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        CustomerItemsDTO dto = new CustomerItemsDTO();
        List<CustomerItemsDTO> result = service.getCustomerItemsInfo(dto);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertTrue(true);
    }

    /* Started by AICoder, pid:e8e2ad3246xae9014444096250fab466765277be */
    @Test
    public void testQueryCustomerItemsInfo_Success() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        String url = "http://example.com/api";
        PowerMockito.when(constantInterface.getUrl(InterfaceEnum.queryListByCustomerList)).thenReturn(url);

        String responseMsg = "[{\"id\": 1, \"name\": \"Test Item\"}]";
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(HttpRemoteUtil.SENDTYPEPOST)))
                .thenReturn(responseMsg);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(responseMsg);

        List<CustomerItemsDTO> expectedList = Collections.singletonList(new CustomerItemsDTO());
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(com.fasterxml.jackson.core.type.TypeReference.class)))
                .thenReturn(expectedList);

        List<CustomerItemsDTO> result = service.queryCustomerItemsInfo(customerItemsDTO);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testQueryCustomerItemsInfo_BlankResponse() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        String url = "http://example.com/api";
        PowerMockito.when(constantInterface.getUrl(InterfaceEnum.queryListByCustomerList)).thenReturn(url);

        String responseMsg = "";
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(HttpRemoteUtil.SENDTYPEPOST)))
                .thenReturn(responseMsg);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg)).thenReturn("");

        List<CustomerItemsDTO> result = service.queryCustomerItemsInfo(customerItemsDTO);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getReconfigurationFlag() {
        PowerMockito.mockStatic(ConstantInterface.class);
        Assert.assertFalse(service.getReconfigurationFlag(""));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(true);
                }})
        );
        Assert.assertTrue(service.getReconfigurationFlag("123"));
    }
    /* Ended by AICoder, pid:e8e2ad3246xae9014444096250fab466765277be */
}
