package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.FisService;
import com.zte.application.PmRepairInfoService;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BarcodeLockDetail;
import com.zte.domain.model.BarcodeLockDetailRepository;
import com.zte.domain.model.BarcodeLockTemp;
import com.zte.domain.model.BarcodeLockTempRepository;
import com.zte.domain.model.PmRepairInfo;
import com.zte.domain.model.PmRepairRcv;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.vo.PmRepairDqasRcvVO;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.interfaces.dto.PmRepairRcvDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.PmRepairVirtualSnDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.powermock.api.mockito.PowerMockito.when;
@PrepareForTest({CommonUtils.class})
public class PmRepairRcvServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairRcvServiceImpl service;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PmRepairInfoService pmRepairInfoService;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    @Mock
    private BarcodeLockTempRepository barcodeLockTempRepository;

    @Mock
    private FisService fisService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void extractedGetVoList() {
        service.extractedGetVoList(new ServiceData(),
                Lists.newArrayList(new PmRepairRcvVo() {{
                                       setStatus("10560001");
                                   }},
                        new PmRepairRcvVo() {{
                            setStatus("10560006");
                        }}),
                "1",
                Lists.newArrayList("2"),
                Lists.newArrayList("2"));
        Assert.assertNotNull(service.extractedGetVoList(new ServiceData(),
                Lists.newArrayList(),
                "1",
                Lists.newArrayList("2"),
                Lists.newArrayList("2")));
    }

    @Test
    public void searchResultBySn() {
        when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(anyObject())).thenAnswer(
                (InvocationOnMock invocationOnMock) -> {
                    PmRepairRcvDTO arg = (PmRepairRcvDTO) invocationOnMock.getArguments()[0];
                    if (null == arg) {
                        return null;
                    } else if (arg.getSn().equals("'777402200001'")) {
                        List<PmRepairRcvVo> list = new ArrayList<>(1);
                        PmRepairRcvVo a1 = new PmRepairRcvVo();
                        list.add(a1);
                        a1.setStatus("10560001");
                        a1.setDeliveryBy("送修人1");
                        a1.setCreateDate(DateUtil.convertStringToDate("2021-08-19 14:45:01", DateUtil.DATE_FORMATE_FULL));
                        a1.setErrorDescription("故障描述1");
                        return list;
                    } else {
                        return null;
                    }
                });

        ServiceData<List<PmRepairDqasRcvVO>> listServiceData = service.searchResultBySn("777402200001", "55");
        Assert.assertTrue(listServiceData.getBo().get(0).getDeliveryBy().equals("送修人1"));
        ServiceData<List<PmRepairDqasRcvVO>> listServiceData1 = service.searchResultBySn("a1111", "55");
        Assert.assertTrue(listServiceData1.getCode().getCode().equals(RetCode.BUSINESSERROR_CODE));
        ServiceData<List<PmRepairDqasRcvVO>> listServiceData2 = service.searchResultBySn("777402200001", "a5");
        Assert.assertTrue(listServiceData2.getCode().getCode().equals(RetCode.BUSINESSERROR_CODE));
        ServiceData<List<PmRepairDqasRcvVO>> listServiceData3 = service.searchResultBySn("", "55");
        Assert.assertTrue(listServiceData3.getCode().getCode().equals(RetCode.BUSINESSERROR_CODE));
    }

    @Test
    public void selectPmRepairRcvDTOBySnNew1() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFactoryId(new BigDecimal(52));
        String empNo = "11111111";
        record.setPageType(new BigDecimal(2));

        //测试1
        try {
            PmRepairRcvDTO pmRepairRcvDTO1 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        List<PsWipInfo> wipInfoList1 = new ArrayList<>();
        wipInfoList1.add(new PsWipInfo());
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(wipInfoList1);
        try {
            PmRepairRcvDTO pmRepairRcvDTO13 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(null);

        //测试2
        record.setSn("7306388000261");
        try {
            PmRepairRcvDTO pmRepairRcvDTO2 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试4
        record.setFromStation("单板测试段");
        List<PmRepairRcv> pmRepairRcvList = new ArrayList<>();
        pmRepairRcvList.add(new PmRepairRcv());
        PowerMockito.when(pmRepairRcvRepository.getPmRepairRcvDetail(Mockito.any())).thenReturn(pmRepairRcvList);
        try {
            PmRepairRcvDTO pmRepairRcvDTO4 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试5
        List<PmRepairRcv> pmRepairRcvList1 = new ArrayList<>();
        PowerMockito.when(pmRepairRcvRepository.getPmRepairRcvDetail(Mockito.any())).thenReturn(pmRepairRcvList1);
        try {
            PmRepairRcvDTO pmRepairRcvDTO5 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试8
        record.setSn("730638800026");
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord(Mockito.any(), Mockito.any())).thenReturn(2);
        try {
            PmRepairRcvDTO pmRepairRcvDTO8 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试9
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord(Mockito.any(), Mockito.any())).thenReturn(0);
        try {
            PmRepairRcvDTO pmRepairRcvDTO9 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试10
        record.setPageType(new BigDecimal(1));
        PowerMockito.when(pmRepairRcvRepository.getRelOneCount(Mockito.any())).thenReturn(1L);
        try {
            PmRepairRcvDTO pmRepairRcvDTO10 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试11
        PowerMockito.when(pmRepairRcvRepository.getRelOneCount(Mockito.any())).thenReturn(0L);
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PowerMockito.when(psWipInfoService.getWipInfoJoinTestBySn(Mockito.any())).thenReturn(wipInfoList);
        try {
            PmRepairRcvDTO pmRepairRcvDTO11 = service.selectPmRepairRcvDTOBySnNew(record, empNo);
        } catch (Exception e) {
        }

        //测试12
        wipInfoList.add(new PsWipInfo());
        PowerMockito.when(psWipInfoService.getWipInfoJoinTestBySn(Mockito.any())).thenReturn(wipInfoList);
        try {
            Assert.assertNotNull(service.selectPmRepairRcvDTOBySnNew(record, empNo));
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }


    }

    @Test
    public void getRepairMinSn() throws Exception {
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setFactoryId(new BigDecimal(52));
        dto.setProdplanId("7306389");
        PowerMockito.when(pmRepairRcvDetailRepository.getRepairMinSn("7306389")).thenReturn("730638900034");

        //测试1
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "1")).thenReturn(0);
        try {
            String repairMinSn = service.getRepairMinSn(dto);
            Assert.assertTrue("730638900034".equals(repairMinSn));
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.PRODPLAN_EXIST_SN_RECORD, e.getMessage());
        }

        //测试2
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "1")).thenReturn(1);
        try {
            service.getRepairMinSn(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_EXIST_SN_RECORD, e.getMessage());
        }
    }

    @Test
    public void getRepairRcvListBySn() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        dto.setPageType("2");
        dto.setFactoryId(new BigDecimal(52));
        dto.setSn("730638900033");
        PowerMockito.when(pmRepairRcvRepository.getRelOneCount(Mockito.any())).thenReturn(0L);
        PsWipInfo returnPsWipInfo = new PsWipInfo();
        returnPsWipInfo.setAttribute1("1234567");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.any())).thenReturn(returnPsWipInfo);

        List<BarcodeLockDetail> lockList =new ArrayList<>();
        BarcodeLockDetail barcodeLockDetail=new BarcodeLockDetail();
        barcodeLockDetail.setAttribute1("awqqrw");
        barcodeLockDetail.setBillNo("awqqrw");
        List<BarcodeLockTemp> barcodeLockTemps = new ArrayList<>();
        BarcodeLockTemp barcodeLockTemp =new BarcodeLockTemp();
        barcodeLockTemp.setBillNo("1234567");
        barcodeLockTemp.setSn("1234567");

        BarcodeLockTemp barcodeLockTemp1 =new BarcodeLockTemp();
        barcodeLockTemp1.setBillNo("awqqrw");
        barcodeLockTemp1.setSn("730638900033");
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.any(),Mockito.any())).thenReturn(lockList);
        PowerMockito.when(barcodeLockTempRepository.selectUnLockInfo(Mockito.any(),Mockito.any())).thenReturn(barcodeLockTemps);
        List<PmRepairInfo> pmRepairInfos = new ArrayList<>();

        PowerMockito.when(pmRepairInfoService.getRelOnePageNoChange(Mockito.any())).thenReturn(pmRepairInfos);
        //测试1
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试2
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfos.add(pmRepairInfo);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试3
        pmRepairInfo.setSnType("2");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试4
        pmRepairInfo.setSnType("1");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试5
        pmRepairInfo.setRepairStatus(Constant.REPAIR_STATUS_RESTORE);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试6
        pmRepairInfo.setRepairStatus(Constant.REPAIR_STATUS_COMPLETE);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试7
        pmRepairInfo.setLineCode("SMT8");
        dto.setLineCode("SMT7");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试8
        dto.setLineCode("SMT8");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试9
        pmRepairInfo.setFromStation("单板测试段");
        dto.setFromStation("SMT段");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试10
        pmRepairInfo.setFromStation("单板测试段");
        dto.setFromStation("单板测试段");
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试11
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        PowerMockito.when(pmRepairRcvDetailService.getRelOneList(Mockito.any())).thenReturn(pmRepairRcvDetails);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        //测试12
        PmRepairRcvDetail pmRepairRcvDetail = new PmRepairRcvDetail();
        pmRepairRcvDetails.add(pmRepairRcvDetail);
        PowerMockito.when(pmRepairRcvDetailService.getRelOneList(Mockito.any())).thenReturn(pmRepairRcvDetails);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
        }

        lockList.add(barcodeLockDetail);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        barcodeLockTemps.add(barcodeLockTemp);
        barcodeLockTemps.add(barcodeLockTemp1);
        try {
            PmRepairInfo out = service.getRepairRcvListBySn(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getRepairRcvListNoSn() throws Exception {
        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        dto.setPageType("2");
        dto.setFactoryId(new BigDecimal(52));

        //测试1
        dto.setProdplanId("");
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试2
        dto.setProdplanId("7306388000");
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试2.1
        dto.setProdplanId("730638a");
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试3
        dto.setProdplanId("7306388");
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试4
        dto.setQty(200);
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试4.1
        dto.setQty(-2);
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试5
        dto.setQty(null);
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试6
        dto.setQty(null);
        try {
            List<PmRepairInfo> out = service.getRepairRcvListNoSn(dto);
        } catch (Exception e) {
        }

        //测试7
        List<PmRepairInfo> pmRepairInfos = new ArrayList<>();
        PowerMockito.when(pmRepairInfoService.getRelOnePageNoChange(Mockito.any())).thenReturn(pmRepairInfos);
        PmRepairInfo p1 = new PmRepairInfo();
        pmRepairInfos.add(p1);
        p1.setSnType("1");
        PmRepairInfo p2 = new PmRepairInfo();
        pmRepairInfos.add(p2);
        p2.setSnType("2");
        try {
            Assert.assertNotNull(service.getRepairRcvListNoSn(dto));
        } catch (Exception e) {
        }
    }

    @Test
    public void updatePmRepairRcvDetailBatchTerminal() throws Exception {
        List<PmRepairRcvDetailDTO> records = new ArrayList<>();

        //测试
        PowerMockito.when(pmRepairInfoService.getRelOnePageNoChange(Mockito.any())).thenReturn(null);
        PowerMockito.when(pmRepairRcvRepository.updatePmRepairRcvDetailBatch(Mockito.any())).thenReturn(1);
        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试1
        PmRepairRcvDetailDTO r1 = new PmRepairRcvDetailDTO();
        records.add(r1);
        r1.setSn("730638900033");
        r1.setDelTestData(false);
        PowerMockito.when(pmRepairInfoService.getRelOnePageNoChange(Mockito.any())).thenReturn(null);
        PowerMockito.when(pmRepairRcvRepository.updatePmRepairRcvDetailBatch(Mockito.any())).thenReturn(1);
        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试2
        List<PmRepairInfo> pmRepairRcvs = new ArrayList<>();
        PowerMockito.when(pmRepairInfoService.getRelOnePageNoChange(Mockito.any())).thenReturn(pmRepairRcvs);
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairRcvs.add(pmRepairInfo);
        pmRepairInfo.setSn("730638900033");
        pmRepairInfo.setRepairStatus(Constant.REPAIR_STATUS_WAIT);
        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试3
        pmRepairInfo.setRepairStatus(Constant.REPAIR_STATUS_COMPLETE);
        pmRepairInfo.setFromStation("SMT段");
        PowerMockito.when(pmRepairRcvRepository.updatePmRepairRcvDetailBatch(Mockito.any())).thenReturn(1);
        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试4
        pmRepairInfo.setFromStation(Constant.FROM_STATION_ASSEMBLE);
        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试5
        r1.setDelTestData(true);
        PowerMockito.when(fisService.deleteTestData(Mockito.any())).thenReturn(true);

        try {
            service.updatePmRepairRcvDetailBatchTerminal(records);
        } catch (Exception e) {
        }

        //测试6
        r1.setDelTestData(true);
        try {
            Assert.assertNotNull(service.updatePmRepairRcvDetailBatchTerminal(records));
        } catch (Exception e) {
        }

    }

    @Test
    public void returnValid() {
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        service.returnValid(dto);
        try {
            service.batchReject(dto);
        } catch (Exception e) {
        }
        try {
            Assert.assertNotNull(service.batchReceived(dto));
        } catch (Exception e) {
        }
    }


    @Test
    public void getNoSnRepairSns() throws Exception {
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setFactoryId(new BigDecimal(52));
        dto.setProdplanId("7306389");
        dto.setQty(1);
        dto.setStartSn("1111111111111");

        PowerMockito.when(pmRepairRcvDetailRepository.getNoSnRepairSns(dto)).thenReturn(Arrays.asList("730638900034"));
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "1")).thenReturn(0);
        Assert.assertEquals("1111111111111", dto.getStartSn());
        service.getNoSnRepairSns(dto);
    }

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBomOfPmRepairInfoList", null);
        Assert.assertTrue(1==1);
        List<PmRepairInfo> list = new ArrayList<>();
        PmRepairInfo entity = new PmRepairInfo();
        entity.setProdplanId("1234567");
        list.add(entity);
        PmRepairInfo entity1 = new PmRepairInfo();
        entity1.setProdplanId("12345671");
        entity1.setItemCode("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBomOfPmRepairInfoList", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertTrue(list.get(1).getMbom().equals("itemNo"));
    }
}
