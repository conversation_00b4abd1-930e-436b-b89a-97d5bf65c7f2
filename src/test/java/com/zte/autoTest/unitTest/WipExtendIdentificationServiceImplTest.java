package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.AssemblyOptRecordService;
import com.zte.application.impl.WipEntityScanInfoServiceImpl;
import com.zte.application.impl.WipExtendIdentificationServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @Date 2022/9/30 14:03
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class, DatawbRemoteService.class, RedisHelper.class})
public class WipExtendIdentificationServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipExtendIdentificationServiceImpl service;

    @Mock
    private WipExtendIdentificationRepository repository;
    @Mock
    private PsWipInfoRepository wipInfoRepository;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private WriteBackSpmFailInfoRepository writeBackSpmFailInfoRepository;
    @Mock
    private RedisLock redisLock;
    @Mock
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;
    @Mock
    private AssemblyOptRecordService assemblyOptRecordService;

    @Test
    public void sendWipExtToSpm() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class, DatawbRemoteService.class);
        SysLookupTypesDTO lookupValuesDTO = new SysLookupTypesDTO();
        lookupValuesDTO.setLookupMeaning("2022-09-01 00:00:00");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(lookupValuesDTO);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setSn("123123123123");
        wipExtendIdentification.setFormSn("321321321321");
        wipExtendIdentification.setItemNo("123123123");
        wipExtendIdentification.setCreateDate(new Date());
        WipExtendIdentification wipExtendIdentification1 = new WipExtendIdentification();
        wipExtendIdentification1.setSn("234234234234");
        wipExtendIdentification1.setFormSn("432432432432");
        wipExtendIdentification1.setItemNo("234234234");
        wipExtendIdentification1.setCreateDate(new Date());
        WipExtendIdentification wipExtendIdentification2 = new WipExtendIdentification();
        wipExtendIdentification2.setSn("345345345345");
        wipExtendIdentification2.setFormSn("543543543543");
        wipExtendIdentification2.setItemNo("234234234");
        wipExtendIdentification2.setCreateDate(new Date());
        wipExtendIdentificationList.add(wipExtendIdentification);
        wipExtendIdentificationList.add(wipExtendIdentification1);
        wipExtendIdentificationList.add(wipExtendIdentification2);
        PowerMockito.when(repository.getWipExtendIdentificationByCreateDate(Mockito.anyObject())).thenReturn(wipExtendIdentificationList);
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123123123");
        bsItemInfo.setItemType("0");
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("234234234");
        bsItemInfo1.setItemType("1");
        itemInfoList.add(bsItemInfo);
        itemInfoList.add(bsItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.getItemType((Mockito.anyList()))).thenReturn(itemInfoList);

        List<PsWipInfoDTO> wipList = new ArrayList<>();
        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setAttribute1("1231231");
        wipList.add(dto);
        PowerMockito.when(wipInfoRepository.getListByBatchSnList((Mockito.anyList()))).thenReturn(wipList);

        PowerMockito.when(datawbRemoteService.pushMaterialSpm(Mockito.anyList())).thenReturn(1);

        PsTask psTask = new PsTask();
        Map<String, String> isTopMap = new HashMap<>();
        isTopMap.put("234234234234", "432432432432");
        Map<String, String> notTopMap = new HashMap<>();
        notTopMap.put("345345345345", "543543543543");
        psTask.setIsTopProdplanMap(isTopMap);
        psTask.setNotTopProdplanMap(notTopMap);
        PowerMockito.when(PlanscheduleRemoteService.getTopProdplan(Mockito.anyMap())).thenReturn(psTask);
        Assert.assertNotNull(service.sendWipExtToSpm("10313234", "52", "180"));

        PowerMockito.when(datawbRemoteService.pushSemiSpm((Mockito.anyList()))).thenReturn(1);
        PowerMockito.when(writeBackSpmFailInfoRepository.batchInsert(Mockito.anyList())).thenReturn(1);

    }

    @Test
    public void sendSemiFailSpm() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<WriteBackSpmFailInfo> writeBackSpmFailInfoList = new ArrayList<>();
        WriteBackSpmFailInfo info = new WriteBackSpmFailInfo();
        info.setSn("123123123123");
        info.setFormSn("321321321321");
        info.setTopProdplanId("6546546");
        writeBackSpmFailInfoList.add(info);
        PowerMockito.when(writeBackSpmFailInfoRepository.getWriteBackSpmFailInfoByCreateDate(Mockito.anyObject())).thenReturn(writeBackSpmFailInfoList);
        List<WipExtendIdentification> wipExtendIdentifications = new ArrayList<>();
        WipExtendIdentification wip = new WipExtendIdentification();
        wip.setSn("321321321321");
        wip.setFormSn("654654654654");
        wipExtendIdentifications.add(wip);
        PowerMockito.when(repository.getTopFormSn(Mockito.anySet())).thenReturn(wipExtendIdentifications);
        Assert.assertNotNull(service.sendSemiFailSpm("180", "52"));
    }

    @Test
    public void getTopProdplan() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            PsTask psTask = new PsTask();
                            psTask.setTaskNo("123");
                            setBo(psTask);
                        }})));
        Map<String, String> topSnMap = new HashMap<>();
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        PlanscheduleRemoteService.getTopProdplan(topSnMap);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void getRouteIdByWip() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        CommonUtils.getRouteIdByWip(new PsWipInfo());
        CommonUtils.getRouteIdByWip(new PsWipInfo() {{
            setWorkOrderNo("1");
        }});
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(new PsEntityPlanBasic() {{
            setRouteId("1");
        }});
        CommonUtils.getRouteIdByWip(new PsWipInfo() {{
            setWorkOrderNo("1");
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void pushWipExtSemiToFactory() throws Exception {
        List<WipExtendIdentification> list = new ArrayList<>();
        WipExtendIdentification info = new WipExtendIdentification();
        info.setSn("123123");
        list.add(info);
        PowerMockito.when(repository.batchInsertWipExt(any())).thenReturn(1);
        Assert.assertNotNull(service.pushWipExtSemiToFactory(list));
    }

    @Test
    public void verifySnIsLockMain() throws Exception {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("111");
        psWipInfo.setCurrProcessCode("222");
        PowerMockito.doNothing().when(flowControlCommonService).snLockControl(Mockito.any());
        Whitebox.invokeMethod(service, "verifySnIsLockMain", psWipInfo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void queryBindRelDataBatch() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        WipExtendIdentificationDTO conditions = new WipExtendIdentificationDTO();
        List<WipExtendIdentification> list = new LinkedList<>();
        service.queryBindRelDataBatch(conditions);
        WipExtendIdentification a1 = new WipExtendIdentification();
        a1.setProcessCode("1");
        a1.setFormType("2");
        list.add(a1);
        PowerMockito.when(repository.queryBindRelDataBatch(Mockito.any()))
                .thenReturn(list);

        List<SysLookupTypesDTO> sysLookUpValue = new LinkedList<>();
        SysLookupTypesDTO b1 = new SysLookupTypesDTO();
        b1.setLookupMeaning("2");
        sysLookUpValue.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString()))
                .thenReturn(sysLookUpValue)
        ;

        List<BSProcessDTO> bsProcessDTOS = new LinkedList<>();
        BSProcessDTO c1 = new BSProcessDTO();
        c1.setProcessCode("1");
        bsProcessDTOS.add(c1);
        PowerMockito.when(wipEntityScanInfoService.getProcessInfo(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(bsProcessDTOS);

        Assert.assertNotNull(service.queryBindRelDataBatch(conditions));
    }
}
