package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.DailyReportService;
import com.zte.application.impl.DailyReportServiceImpl;
import com.zte.application.impl.DipDailyReportServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.Matchers.*;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/9/23 16:46
 */
@PrepareForTest({PlanscheduleRemoteService.class, CrafttechRemoteService.class, BasicsettingRemoteService.class})
public class DipDailyReportServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private DipDailyReportServiceImpl service;

	@Mock
	private DipDailyReportRepository reportRepository;

	@Mock
	private DailyReportService reportService;

	@Test
	public void statisticJob() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class, BasicsettingRemoteService.class);
		PowerMockito.when(reportService.getPsTask(anyInt(), anyInt(), any())).thenReturn(new PageRows<PsTask>(){{
			setRows(Lists.newArrayList(new PsTask() {{
				setProdplanId("1");
			}}));
			setTotal(1L);
		}});
		PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(Lists.newArrayList(
				new SysLookupValuesDTO(){{
					setLookupMeaning("6");
					setAttribute1("INSERTION");
				}},
				new SysLookupValuesDTO(){{
					setLookupMeaning("+");
					setAttribute1("INSERTION");
				}},
				new SysLookupValuesDTO(){{
					setLookupMeaning("*");
					setAttribute1("MILLING_PLATE");
				}}
		));

		service.statisticJob(1, "");
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}
}
