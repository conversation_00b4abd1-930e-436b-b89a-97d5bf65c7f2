package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.SnAttrInfoService;
import com.zte.application.impl.BarcodeCenterServiceImpl;
import com.zte.application.impl.PdmServiceImpl;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/6/19
 */

@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class})
public class BarcodeCenterServiceImplTest extends PowerBaseTestCase {

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WipPrintConfigRepository wipPrintConfigRepository;
    @Mock
    private PsWipInfoServiceImpl psWipInfoService;
    @Mock
    private PdmServiceImpl pdmServiceImpl;

    @InjectMocks
    private BarcodeCenterServiceImpl barcodeCenterServiceImpl;

    @InjectMocks
    private BarcodeCenterRemoteService barcodeCenterRemoteServiceInject;

    @Mock
    private TaskBarcodeQueryRepository taskBarcodeQueryRepository;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private PdmRemoteService pdmRemoteService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private SnAttrInfoService snAttrInfoService;

    @Test
    public void expandQuery() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        sysLookupTypesDTOUrl.setLookupCode(new BigDecimal(1004052011));
        SysLookupTypesDTO sysLookupTypesDTOUrl2 = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl2.setLookupMeaning("http://");
        sysLookupTypesDTOUrl2.setLookupCode(new BigDecimal(1004052012));
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl2);
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barList = new ArrayList<>();
        barList.add("11");
        barcodeExpandQueryDTO.setBarcodeList(barList);
        Assert.assertNotNull(barcodeCenterRemoteServiceInject.expandQuery(barcodeExpandQueryDTO));
    }

    @Test
    public void getTemplateNameList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        Assert.assertNotNull(barcodeCenterServiceImpl.getTemplateNameList("templateContent"));
    }

    @Test
    public void getTemplateInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(new BarcodeTemplateDTO());
                }})
        );
        BarcodeTemplateDTO barcodeTemplateDTO = new BarcodeTemplateDTO();
        barcodeTemplateDTO.setTemplateName("templateName");
        Assert.assertNull(barcodeCenterServiceImpl.getTemplateInfo(barcodeTemplateDTO));
    }

    @Test
    public void getDownloadUrl() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);

        List<TaskBarcodeQuery> taskBarcodeQueryList = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQueryList.add(taskBarcodeQuery);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPageNew(any()))
                .thenReturn(taskBarcodeQueryList);
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        lookupValueList.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);

        BarcodeCenterDTO barcodeCenterDTO1 = new BarcodeCenterDTO();
        barcodeCenterDTO1.setDownloadUrl("222");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(barcodeCenterDTO1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));


        BarcodeCenterDTO barcodeCenterDTO = new BarcodeCenterDTO();
        barcodeCenterDTO.setSn("sn");
        barcodeCenterDTO.setPrinter("pinter");
        barcodeCenterDTO.setTemplateName("name");
        Assert.assertNotNull(barcodeCenterServiceImpl.getDownloadUrl());

    }

    @Test
    public void supplementaryPrint() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);

        List<TaskBarcodeQuery> taskBarcodeQueryList = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setSn("sn");
        taskBarcodeQueryList.add(taskBarcodeQuery);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPageNew(any()))
                .thenReturn(taskBarcodeQueryList);

        BarcodeCenterDTO barcodeCenterDTO = new BarcodeCenterDTO();
        barcodeCenterDTO.setSn("sn");
        try {
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL_VALID, e.getMessage());
        }
        barcodeCenterDTO.setSnList(Arrays.asList("777766600001"));
        try {
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PARAM_MISSING));
        }
        barcodeCenterDTO.setTemplateName("TemplateName");
        try {
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PARAM_MISSING));
        }
        barcodeCenterDTO.setPrinter("Printer");


        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        barcodeCenterDTO.setTaskNo("taskNo");
        barcodeCenterDTO.setSn("sn");
        barcodeCenterDTO.setPrinter("pinter");
        barcodeCenterDTO.setTemplateName("name");
        List<PrintRecordDTO> printRecordDTOList = new ArrayList<>();
        printRecordDTOList.add(new PrintRecordDTO() {{
            setSn("sn");
        }});
        PowerMockito.when(centerfactoryRemoteService.queryRecordBySnList(anyList())).thenReturn(printRecordDTOList);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPageNew(any()))
                .thenReturn(taskBarcodeQueryList);
        PdmEnCodeDTO pdmEnCodeDTO = new PdmEnCodeDTO();
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(any()))
                .thenReturn(pdmEnCodeDTO);
        try {
            List<String> snList = new ArrayList();
            snList.add("sn");
            barcodeCenterDTO.setSnList(snList);
            barcodeCenterDTO.setOuterBox(true);
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_IS_NULL, e.getMessage());
        }
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkContinuous() throws Exception {

        List<TaskBarcodeQuery> taskBarcodeQueryList = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setSn("1001");
        taskBarcodeQueryList.add(taskBarcodeQuery);
        TaskBarcodeQuery taskBarcodeQuery1 = new TaskBarcodeQuery();
        taskBarcodeQuery1.setSn("1002");
        taskBarcodeQueryList.add(taskBarcodeQuery1);
        Assert.assertNotNull(barcodeCenterServiceImpl.checkContinuous(1, taskBarcodeQueryList));

    }

    @Test
    public void supplementaryPrint2() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));

        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo1");
        psTask.setIsLead("无铅");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setDescriptionChinV("无铅");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);

        PdmPageDTO<PdmEnCodeDTO> pdmPageDTO = new PdmPageDTO<>();
        List<PdmEnCodeDTO> pdmEnCodeDTOList = new ArrayList<>();
        PdmEnCodeDTO pdmEnCodeDTO = new PdmEnCodeDTO();
        pdmEnCodeDTOList.add(pdmEnCodeDTO);
        pdmPageDTO.setData(pdmEnCodeDTOList);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(pdmPageDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(anyString()))
                .thenReturn(new BsItemInfo());

        List<TaskBarcodeQuery> taskBarcodeQueryList = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setIsPrint(1);
        taskBarcodeQuery.setAttribute2("taskNo");
        taskBarcodeQuery.setSn("2");
        taskBarcodeQueryList.add(taskBarcodeQuery);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPageNew(any()))
                .thenReturn(taskBarcodeQueryList);

        BarcodeCenterDTO barcodeCenterDTO = new BarcodeCenterDTO();
        barcodeCenterDTO.setTaskNo("taskNo");
        barcodeCenterDTO.setSn("sn");
        barcodeCenterDTO.setPrinter("pinter");
        barcodeCenterDTO.setTemplateName("name");
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(any()))
                .thenReturn(pdmEnCodeDTO);
        List<String> snList = new ArrayList<>();
        snList.add("2");
        barcodeCenterDTO.setSnList(snList);
        try {
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.THE_BARCODE_HAS_NOT_BEEN_PRINTED));
        }
        List<PrintRecordDTO> printRecordDTOList = new ArrayList<>();
        printRecordDTOList.add(new PrintRecordDTO() {{
            setSn("sn");
        }});
        PowerMockito.when(centerfactoryRemoteService.queryRecordBySnList(anyList())).thenReturn(printRecordDTOList);
        try {
            barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WIP_INFO_DATA_NOT_FOUND));
        }
        printRecordDTOList = new ArrayList<>();
        printRecordDTOList.add(new PrintRecordDTO() {{
            setSn("2");
            setPrintData(JSON.toJSONString(new BarcodeCenterPrintSubStrDTO()));
        }});
        PowerMockito.when(centerfactoryRemoteService.queryRecordBySnList(anyList())).thenReturn(printRecordDTOList);
        barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

        barcodeCenterDTO.setOuterBox(true);
        barcodeCenterServiceImpl.supplementaryPrint(barcodeCenterDTO);

    }

    @Test
    public void getPrintInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));

        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo1");
        psTask.setIsLead("无铅");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setDescriptionChinV("无铅");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);

        PdmPageDTO<PdmEnCodeDTO> pdmPageDTO = new PdmPageDTO<>();
        List<PdmEnCodeDTO> pdmEnCodeDTOList = new ArrayList<>();
        PdmEnCodeDTO pdmEnCodeDTO = new PdmEnCodeDTO();
        pdmEnCodeDTOList.add(pdmEnCodeDTO);
        pdmPageDTO.setData(pdmEnCodeDTOList);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(pdmPageDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(anyString()))
                .thenReturn(new BsItemInfo());

        BarcodeCenterDTO barcodeCenterDTO = new BarcodeCenterDTO();
        barcodeCenterDTO.setTaskNo("taskNo");
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(any()))
                .thenReturn(pdmEnCodeDTO);
        Assert.assertNotNull(barcodeCenterServiceImpl.getPrintInfo(barcodeCenterDTO));
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(any()))
                .thenReturn(null);
        try {
            Assert.assertNotNull(barcodeCenterServiceImpl.getPrintInfo(barcodeCenterDTO));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE, e.getMessage());
        }
    }

    /* Started by AICoder, pid:g6443736127891814d3d0bdd218f728d356446df */
    @Test
    public void barCodePrint() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));

        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo1");
        psTask.setIsLead("无铅");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setDescriptionChinV("无铅");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        lookupValueList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);

        PdmPageDTO<PdmEnCodeDTO> pdmPageDTO = new PdmPageDTO<>();
        List<PdmEnCodeDTO> pdmEnCodeDTOList = new ArrayList<>();
        PdmEnCodeDTO pdmEnCodeDTO = new PdmEnCodeDTO();
        pdmEnCodeDTOList.add(pdmEnCodeDTO);
        pdmPageDTO.setData(pdmEnCodeDTOList);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(pdmPageDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(anyString()))
                .thenReturn(new BsItemInfo());

        List<TaskBarcodeQuery> taskBarcodeQueryList = new ArrayList<>();
        TaskBarcodeQuery taskBarcodeQuery = new TaskBarcodeQuery();
        taskBarcodeQuery.setSn("1001");
        taskBarcodeQuery.setIsPrint(1);
        taskBarcodeQuery.setAttribute2("taskNo");
        taskBarcodeQueryList.add(taskBarcodeQuery);
        TaskBarcodeQuery taskBarcodeQuery1 = new TaskBarcodeQuery();
        taskBarcodeQuery1.setSn("1002");
        taskBarcodeQuery1.setIsPrint(1);
        taskBarcodeQuery1.setAttribute2("taskNo");
        taskBarcodeQueryList.add(taskBarcodeQuery1);
        PowerMockito.when(taskBarcodeQueryRepository.getListByPageNew(any()))
                .thenReturn(taskBarcodeQueryList);

        BarcodeCenterDTO barcodeCenterDTO = new BarcodeCenterDTO();
        barcodeCenterDTO.setTaskNo("taskNo");
        barcodeCenterDTO.setPrintNum(2);
        barcodeCenterDTO.setSn("sn");
        barcodeCenterDTO.setPrinter("pinter");
        barcodeCenterDTO.setTemplateName("name");
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(any()))
                .thenReturn(pdmEnCodeDTO);
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        barcodeCenterDTO.setPrintNum(3);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.THE_TASK_INSUFFICIENT_NUMBER_OF_PRINTABLE_BARCODES);
        }
        barcodeCenterDTO.setPrintNum(1);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.THE_BARCODE_TO_BE_PRINTED_IS_NOT_CONTINUOUS);
        }
        barcodeCenterDTO.setPrintNum(2);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

        List<WipPrintConfigDTO> wipPrintConfigs = new ArrayList<>();
        wipPrintConfigs.add(new WipPrintConfigDTO());
        wipPrintConfigs.get(0).setSn("1002");
        PowerMockito.when(wipPrintConfigRepository.selectEntityListPage(any())).thenReturn(wipPrintConfigs);
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        barcodeCenterDTO.setTaskNo("");
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_NO_IS_NULL);
        }
        barcodeCenterDTO.setTaskNo("taskNo");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_NOT_HAVE_DETAILS);
        }
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_TYPE_ISLEAD)).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        sysLookupTypesDTO.setDescriptionChinV("有铅");
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST);
        }
        sysLookupTypesDTO.setDescriptionChinV("无铅");
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(Mockito.any())).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE);
        }
        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(anyString())).thenReturn(null);
        PdmEnCodeDTO pdmEnCodeDTO1 = new PdmEnCodeDTO();
        pdmEnCodeDTO1.setEnCode("123");
        PowerMockito.when(pdmRemoteService.getLastPdmEnCode(Mockito.any())).thenReturn(pdmEnCodeDTO1);
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemEnName("enname");
        PowerMockito.when(BasicsettingRemoteService.getItemInfoByItemNo(anyString())).thenReturn(bsItemInfo);
        barcodeCenterDTO.setPrintType("1");
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        psTask.setItemNo("ite");
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        barcodeCenterDTO.setNameplateLabelFlag(true);
        barcodeCenterDTO.setTaskNo("");
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_NO_IS_NULL);
        }
        barcodeCenterDTO.setTaskNo("taskNo");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_NOT_HAVE_DETAILS);
        }
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_TYPE_ISLEAD)).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupValueList);
        sysLookupTypesDTO.setDescriptionChinV("有铅");
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.TASK_ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST);
        }
        sysLookupTypesDTO.setDescriptionChinV("无铅");

        barcodeCenterDTO.setDocDownloadNo("6604851");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        sysLookupTypesDTOUrl.setLookupMeaning("");
        try {
            barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.FAILED_TO_GET_BARCODE_CENTER_URL);
        }
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        barcodeCenterDTO.setPdmupdatetime(new GregorianCalendar(2021, Calendar.JANUARY, 1).getTime());
        barcodeCenterDTO.setCreateBy("10307329");
        UploadResultDTO uploadResultDTO = new UploadResultDTO();
        uploadResultDTO.setFileSize(1765);
        uploadResultDTO.setFileKey("d5ee5088-a03a-4f3d-805d-4c9dd1032d64");
        PowerMockito.when(pdmServiceImpl.uploadFileByUrl(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(uploadResultDTO);
        barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO);
        barcodeCenterDTO.setNameplateLabelFlag(false);

        Mockito.doThrow(new Exception("Something went wrong")).when(barcodeCenterRemoteService).serverTemplatePrint(any());

        Assert.assertThrows(Exception.class, () -> barcodeCenterServiceImpl.barCodePrint(barcodeCenterDTO));
    }
    /* Ended by AICoder, pid:g6443736127891814d3d0bdd218f728d356446df */

    @Test
    public void wholeBarcodePrintTest() throws Exception {
        WholeBarcodePrintDTO dto = new WholeBarcodePrintDTO();
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(RetCode.VALIDATIONERROR_MSGID));
        }
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.BARCODE_PARAMS_ALL_NULL));
        }
        dto.setEnCode("2");
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PRINT_MACHINE_IS_NULL));
        }
        dto.setPrinter("testPrint");
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PRINT_TEMPLATE_NAME_IS_NULL));
        }
        dto.setTemplateName("temp");
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PRINT_IP_IS_NULL));
        }
        dto.setServerIp("*******");
        List<SnAttrInfoEntityDTO> snAttrInfoEntityList = new ArrayList<>();
        PowerMockito.when(snAttrInfoService.getList(anyObject())).thenReturn(snAttrInfoEntityList);
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.SN_ATTR_INFO_IS_NULL));
        }
        SnAttrInfoEntityDTO snAttrInfoEntityDTO = new SnAttrInfoEntityDTO();
        snAttrInfoEntityDTO.setWholeBarcode("");
        snAttrInfoEntityList.add(snAttrInfoEntityDTO);
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WHOLE_BARCODE_OF_SN_ATTR_INFO_IS_NULL));
        }
        snAttrInfoEntityDTO.setWholeBarcode("ZJ123");
        PowerMockito.when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(null);
        try {
            barcodeCenterServiceImpl.wholeBarcodePrint(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.WHOLE_BARCODE_WIP_INFO_NULL));
        }
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setItemNo("test");
        psWipInfo.setSn("ZJ711133300001");
        psWipInfo.setParentSn("711133300001");
        PowerMockito.when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(psWipInfo);

        List<BsItemInfo> bsItemInfos = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setStyle("style");
        bsItemInfos.add(bsItemInfo);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyString())).thenReturn(bsItemInfos);
        dto.setPrintByIbarcodeFlag(true);
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        Assert.assertNotNull(barcodeCenterServiceImpl.wholeBarcodePrint(dto));
    }

    @Test
    public void setIsLeadWithNum() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BarcodeRegisterDTO> list = new ArrayList<>();
        BarcodeRegisterDTO dto = new BarcodeRegisterDTO();
        dto.setIsLead("有铅");
        list.add(dto);

        List<SysLookupValuesDTO> listSys = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("0");
        sysLookupValuesDTO.setAttribute1("30");
        sysLookupValuesDTO.setLookupType(new BigDecimal(1036));
        sysLookupValuesDTO.setDescriptionChin("有铅");
        listSys.add(sysLookupValuesDTO);

        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys);
        barcodeCenterServiceImpl.setIsLeadWithNum(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    /*Started by AICoder, pid:sc01dqd00cz1667142fb0815a0784b7277f6bfb9*/
    @Test
    public void testExpandQueryOneByOne_NullQuery() throws Exception {
        List<BarcodeExpandDTO> result = barcodeCenterRemoteServiceInject.expandQueryOneByOne(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExpandQueryOneByOne_EmptyBarcodeList() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(new ArrayList<>());
        List<BarcodeExpandDTO> result = barcodeCenterRemoteServiceInject.expandQueryOneByOne(queryDTO);
        assertTrue(result.isEmpty());
    }
    /*Ended by AICoder, pid:sc01dqd00cz1667142fb0815a0784b7277f6bfb9*/
    /* Started by AICoder, pid:995c142de1t45a714a020a7cc077c2388148f8c0 */
    @Test
    public void testExpandQueryOneByOne_NotEmptyBarcodeList() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodeList = new ArrayList<>();
        barcodeList.add("2");
        queryDTO.setBarcodeList(barcodeList);

        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        sysLookupTypesDTOUrl.setLookupCode(new BigDecimal(1004052011));
        SysLookupTypesDTO sysLookupTypesDTOUrl2 = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl2.setLookupMeaning("http://");
        sysLookupTypesDTOUrl2.setLookupCode(new BigDecimal(1004052012));
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl2);
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        barcodeExpandDTOList.add(new BarcodeExpandDTO());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData(){{setBo(barcodeExpandDTOList);}})
        );

        List<BarcodeExpandDTO> result = barcodeCenterRemoteServiceInject.expandQueryOneByOne(queryDTO);
        assertTrue(!result.isEmpty());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData(){{setBo(new ArrayList<>());}})
        );

        result = barcodeCenterRemoteServiceInject.expandQueryOneByOne(queryDTO);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:995c142de1t45a714a020a7cc077c2388148f8c0 */
}