package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.ExceptionSkipInfoService;
import com.zte.application.HighTempSamplingService;
import com.zte.application.IMESLogService;
import com.zte.application.PsWipInfoService;
import com.zte.application.StandardModeCommonScanService;
import com.zte.application.impl.WipExtendIdentificationServiceImpl;
import com.zte.application.impl.common.FlowControlCommonServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.BarcodeLockDetail;
import com.zte.domain.model.BarcodeLockDetailRepository;
import com.zte.domain.model.BarcodeLockTemp;
import com.zte.domain.model.BarcodeLockTempRepository;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.PilotTestControlReslut;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.domain.model.WipTestRecodeRepository;
import com.zte.domain.model.WipTestRecord;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.CallOnlineControlDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.ExceptionSkipInfoDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.scan.BarcodeBindingDTO;
import com.zte.interfaces.dto.scan.BarcodeTestControlDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2022-02-16 10:14
 */
@PrepareForTest({BasicsettingRemoteService.class, HttpRemoteUtil.class,
        HttpClientUtils.class, ConstantInterface.class, CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, CommonUtils.class, SpringContextUtil.class, Normalizer.class,JsonConvertUtil.class
})
public class FlowControlCommonServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private FlowControlCommonServiceImpl flowControlCommonServiceImpl;
    @Mock
    private StandardModeCommonScanService standardModeCommonScanService;
    @Mock
    private WipExtendIdentificationServiceImpl wipExtendIdentificationServiceImpl;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private BarcodeLockTempRepository barcodeLockTempRepository;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private ExceptionSkipInfoService exceptionSkipInfoService;
    @Mock
    private WipTestRecodeRepository wipTestRecodeRepository;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private HrmUserInfoService hrmUserInfoService;
    @Mock
    private HighTempSamplingService highTempSamplingService;
    @Value("${high.temp.switch:Y}")
    private String highTempSwitch;


    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(Normalizer.class);
        PowerMockito.mockStatic(JsonConvertUtil.class);
    }

    @Test
    public void verifySnIsTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class, SpringContextUtil.class, ConstantInterface.class, HttpClientUtils.class, HttpRemoteUtil.class, CrafttechRemoteService.class, BasicsettingRemoteService.class);
        BarcodeTestControlDTO barcodeTestControlDTO = new BarcodeTestControlDTO();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setCraftSection("SMT-A");
        psEntityPlanBasicDTO.setWorkOrderNo("7119859-SMT-A5202");
        psEntityPlanBasicDTO.setSourceTask("7119859");
        barcodeTestControlDTO.setWorkOrder(psEntityPlanBasicDTO);
        List<BSProcess> needTestControlProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        needTestControlProcessList.add(bsProcess);
        List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setLastProcess("Y");
        ctRouteDetailDTO.setProcessCode("1");
        ctRouteDetailDTO.setNextProcess("410");
        ctRouteDetailList.add(ctRouteDetailDTO);

        CtRouteDetail b1 = new CtRouteDetail();
        List<CtRouteDetail> listDetail = new LinkedList<>();
        CtRouteDetail b2 = new CtRouteDetail();
        b2.setNextProcess("2");
        b2.setProcessCode("1");
        b2.setLastProcess("Y");
        listDetail.add(b1);
        listDetail.add(b2);
        barcodeTestControlDTO.setCtRouteDetailInfo(listDetail);
        barcodeTestControlDTO.setListDetail(ctRouteDetailList);
        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("888862100104");
        psWipInfoDTOList.add(psWipInfoDTO);
        barcodeTestControlDTO.setWipList(psWipInfoDTOList);
        List<String> snList = new ArrayList<>();
        snList.add("888862100104");
        barcodeTestControlDTO.setSnList(snList);
        List<ExceptionSkipInfoDTO> exceptionSkipInfoDTOList = new ArrayList<>();
        ExceptionSkipInfoDTO exceptionSkipInfoDTO = new ExceptionSkipInfoDTO();
        exceptionSkipInfoDTO.setSnOrBatchOrItem("888862100104");
        exceptionSkipInfoDTO.setProcessCode("2");
        exceptionSkipInfoDTOList.add(exceptionSkipInfoDTO);
        PowerMockito.when(exceptionSkipInfoService.getList(Mockito.any()))
                .thenReturn(exceptionSkipInfoDTOList);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(2000000005));
        sysLookupTypesDTO.setMeaning("供应商信息查询服务");
        sysLookupTypesDTO.setDatatype("业务数据");
        sysLookupTypesDTO.setDescriptionChin("供应商信息查询服务");
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOList.add(sysLookupTypesDTO);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOList);

        List<WipTestRecord> wipTestRecordList = new ArrayList<>();
        WipTestRecord wipTestRecord = new WipTestRecord();
        wipTestRecord.setSn("sn");
        wipTestRecord.setTestDescription("1");
        wipTestRecord.setTestResult("1");
        wipTestRecordList.add(wipTestRecord);
        PowerMockito.when(wipTestRecodeRepository.getLastRecordBySnAndProcessCodeAndWorkStation(any(), any())).thenReturn(wipTestRecordList);
        PowerMockito.when(wipTestRecodeRepository.getLastRecordBySnAndTestType(any(), any())).thenReturn(wipTestRecordList);
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "verifySnIsTest", barcodeTestControlDTO, needTestControlProcessList, "1");
        Assert.assertEquals("sn", wipTestRecord.getSn());
    }

    @Test
    public void snTestControl() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class, SpringContextUtil.class, ConstantInterface.class, HttpClientUtils.class, HttpRemoteUtil.class, CrafttechRemoteService.class, BasicsettingRemoteService.class);
        List<PsEntityPlanBasicDTO> psEntityList = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setRouteId("123");
        a1.setIsProcessControl("N");
        psEntityList.add(a1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString()))
                .thenReturn(psEntityList);
        List<CtRouteInfoDTO> ctRouteInfoDTOList = new LinkedList<>();
        CtRouteInfoDTO b1 = new CtRouteInfoDTO();
        ctRouteInfoDTOList.add(b1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO b2 = new CtRouteDetailDTO();
        b2.setNextProcess("2");
        b2.setProcessCode("1");
        b2.setLastProcess("Y");
        listDetail.add(b2);
        b1.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.postScanProcess(Mockito.anyObject()))
                .thenReturn(ctRouteInfoDTOList)
        ;
        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("888862100104");
        psWipInfoDTOList.add(psWipInfoDTO);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(anyList())).thenReturn(psWipInfoDTOList);
        PowerMockito.when(psWipInfoService.getWipInfoList(anyList())).thenReturn(psWipInfoDTOList);
        List<CtRouteDetail> ctRouteDetailList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setProcessSeq(0);
        ctRouteDetail.setRouteId("123456");
        ctRouteDetail.setNextProcess("SMT-B");
        ctRouteDetail.setCraftSection("SMT-B");

        ctRouteDetailList.add(ctRouteDetail);
        CtRouteDetail ctRouteDetail2 = new CtRouteDetail();
        ctRouteDetail2.setProcessSeq(0);
        ctRouteDetail2.setRouteId("123456");
        ctRouteDetail2.setNextProcess("SMT-B");
        ctRouteDetail2.setCraftSection("SMT-B");

        ctRouteDetailList.add(ctRouteDetail2);

        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(any())).thenReturn(ctRouteDetailList);

        List<WipTestRecord> wipTestRecordList = new ArrayList<>();
        WipTestRecord wipTestRecord = new WipTestRecord();
        wipTestRecord.setSn("sn");
        wipTestRecord.setTestDescription("1");
        wipTestRecord.setTestResult("1");
        wipTestRecordList.add(wipTestRecord);

        WipTestRecord wipTestRecord1 = new WipTestRecord();
        wipTestRecord1.setSn("sn1");
        wipTestRecord1.setTestDescription("");
        wipTestRecord1.setTestResult("1");
        wipTestRecordList.add(wipTestRecord1);

        WipTestRecord wipTestRecord2 = new WipTestRecord();
        wipTestRecord2.setSn("sn2");
        wipTestRecord2.setTestDescription("");
        wipTestRecord2.setTestResult("2");
        wipTestRecordList.add(wipTestRecord2);
        PowerMockito.when(wipTestRecodeRepository.getLastRecordBySnAndProcessCodeAndWorkStation(any(), any())).thenReturn(wipTestRecordList);
        PowerMockito.when(wipTestRecodeRepository.getLastRecordBySnAndTestType(any(), any())).thenReturn(wipTestRecordList);

        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(exceptionSkipInfoService.getNeedTestControlBsProcessList(any())).thenReturn(bsProcessList);

        List<BarcodeBindingDTO> childList = new ArrayList<>();
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        barcodeBindingDTO.setCurrProcessCode("2");
        barcodeBindingDTO.setWorkStation("410");
        childList.add(barcodeBindingDTO);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString(), anyString())).thenReturn("");

        BarcodeTestControlDTO barcodeTestControlDTO = new BarcodeTestControlDTO();
        barcodeTestControlDTO.setChildList(childList);
        List<String> snList = new ArrayList<>();
        snList.add("sn");
        snList.add("sn1");
        snList.add("sn2");
        barcodeTestControlDTO.setSnList(snList);
        barcodeTestControlDTO.setCurrProcessCode("currProcessCode");
        barcodeTestControlDTO.setWorkStation("workStation");
        barcodeTestControlDTO.setWorkOrderNo("workOrderNo");
        flowControlCommonServiceImpl.snTestControl(barcodeTestControlDTO);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(2000000005));
        sysLookupTypesDTO.setMeaning("供应商信息查询服务");
        sysLookupTypesDTO.setDatatype("业务数据");
        sysLookupTypesDTO.setDescriptionChin("供应商信息查询服务");
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOList.add(sysLookupTypesDTO);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOList);
        BarcodeTestControlDTO barcodeTestControlDTO2 = new BarcodeTestControlDTO();
        barcodeTestControlDTO2.setSnList(snList);
        barcodeTestControlDTO2.setCurrProcessCode("1");
        barcodeTestControlDTO2.setWorkStation("2");
        barcodeTestControlDTO2.setWorkOrderNo("workOrderNo");
        try {
            flowControlCommonServiceImpl.snTestControl(barcodeTestControlDTO2);
        } catch (Exception e) {
            Assert.assertEquals("workOrderNo", barcodeTestControlDTO2.getWorkOrderNo());
        }

    }

    @Test
    public void technicalInnovationControl() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setaSourceImu(new BigDecimal(2));
        flow.setSn("2");

        FlowControlInfoDTO flow1 = new FlowControlInfoDTO();
        flow1.setaSourceImu(new BigDecimal(2));
        flow1.setSn("2");

        FlowControlInfoDTO flow2 = new FlowControlInfoDTO();
        flow2.setSourceImu(new BigDecimal(2));
        flow2.setSn("2");
        List<FlowControlInfoDTO> list = new LinkedList<>();
        list.add(flow1);
        list.add(flow2);
        flow.setProcessInfoList(list);

        PsEntityPlanBasicDTO b1 = new PsEntityPlanBasicDTO();
        b1.setSourceTask("123");
        flow.setEntityPlanBasic(b1);

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_SPM_CONTROL_THREE));
        a1.setLookupMeaning(Constant.FLAG_Y);
        SysLookupValuesDTO a2 = new SysLookupValuesDTO();
        a2.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_ATEST_CONTROL_THREE));
        a2.setLookupMeaning(Constant.FLAG_Y);
        SysLookupValuesDTO a3 = new SysLookupValuesDTO();
        a3.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_TECH_CONTROL_THREE));
        a3.setLookupMeaning(Constant.FLAG_Y);
        SysLookupValuesDTO a4 = new SysLookupValuesDTO();
        a4.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_ABIND_CONTROL_THREE));
        a4.setLookupMeaning(Constant.FLAG_Y);
        SysLookupValuesDTO a5 = new SysLookupValuesDTO();
        a5.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_BATCH_CONTROL_THREE));
        a5.setLookupMeaning(Constant.FLAG_Y);
        lookupValueList.add(a1);
        lookupValueList.add(a2);
        lookupValueList.add(a3);
        lookupValueList.add(a4);
        lookupValueList.add(a5);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_SPM_CONTROL))
                .thenReturn(lookupValueList);

        ServiceData ret = new ServiceData();
        CallOnlineControlDTO c1 = new CallOnlineControlDTO();
        c1.setCode("S");
        ret.setBo(c1);

        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(),
                Mockito.anyString())).thenReturn(JSON.toJSONString(ret));
        Assert.assertEquals("2", flow2.getSn());
        flowControlCommonServiceImpl.technicalInnovationControl(flow);
    }

    @Test
    public void getItemNo() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setItemNo("15562222225656BAB");
        flow.setEntityPlanBasic(psEntityPlanBasicDTO);
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getItemNo", flow);
        Assert.assertEquals("15562222225656BAB", psEntityPlanBasicDTO.getItemNo());
    }

    @Test
    public void checkByPoiltTest() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        List<FlowControlInfoDTO> processInfoList = new LinkedList<>();
        FlowControlInfoDTO a1 = new FlowControlInfoDTO();
        processInfoList.add(a1);
        flow.setProcessInfoList(processInfoList);
        flowControlCommonServiceImpl.checkByPoiltTest(flow);

        flow.setProcessInfoList(new LinkedList<>());
        flow.setIsZLLastProcess("N");
        flowControlCommonServiceImpl.checkByPoiltTest(flow);

        flow.setIsZLLastProcess("Y");
        PowerMockito.when(standardModeCommonScanService.getProcess(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        try {
            flowControlCommonServiceImpl.checkByPoiltTest(flow);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CAN_NOT_FOUND_WORKSTATION, e.getMessage());
        }
        flow.setProcessInfoList(new LinkedList<>());
        flow.setIsZLLastProcess("Y");
        flow.setLineCode("122");
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setItemNo("123456789123ACB");
        flow.setWipInfo(psWipInfo);

        List<BSProcess> processList = new LinkedList<>();
        BSProcess b1 = new BSProcess();
        b1.setControlAddress("::::");
        b1.setProcessControl("1");
        processList.add(b1);
        PowerMockito.when(standardModeCommonScanService.getProcess(Mockito.any(), Mockito.any()))
                .thenReturn(processList)
        ;

        PilotTestControlReslut checkResult = new PilotTestControlReslut();
        checkResult.setbResult(true);
        PowerMockito.when(HttpClientUtils.get(Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(checkResult));
        CFLine line = new CFLine();
        line.setLineName("1234");
        PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyString()))
                .thenReturn(line)
        ;

        PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.any(), Mockito.any())).thenReturn(checkResult);
        flowControlCommonServiceImpl.checkByPoiltTest(flow);
        b1.setControlAddress("::$::");
        flowControlCommonServiceImpl.checkByPoiltTest(flow);
    }

    @Test
    public void boardBindScanCheck() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setIsLastProcess("Y");
        flow.setWorkOrderNo("123");
        PsEntityPlanBasicDTO entityPlanBasicDTO = new PsEntityPlanBasicDTO();
        entityPlanBasicDTO.setItemNo("123");
        entityPlanBasicDTO.setSourceTask("23");
        flow.setEntityPlanBasic(entityPlanBasicDTO);
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setItemNo("123");
        flow.setWipInfo(wipInfo);

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(MpConstant.LOOKUP_VALUE_BIND_SCAN_CHECK));
        sysLookupValuesDTO.setLookupMeaning(Constant.FLAG_Y);
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_COMMON_SCAN))
                .thenReturn(lookupValueList);

        List<ProdBindingSetting> needBindingItems = new LinkedList<>();
        ProdBindingSetting b1 = new ProdBindingSetting();
        needBindingItems.add(b1);
        PowerMockito.when(prodBindingSettingRepository.getList(Mockito.anyMap()))
                .thenReturn(needBindingItems)
        ;

        PowerMockito.when(wipExtendIdentificationServiceImpl.isFinish(Mockito.any(), Mockito.any(),
                Mockito.any()))
                .thenReturn(true);
        flowControlCommonServiceImpl.boardBindScanCheck(flow);

        PowerMockito.when(prodBindingSettingRepository.getList(Mockito.anyMap()))
                .thenReturn(new LinkedList())
        ;

        List<BSProcess> bsProcesses = new LinkedList<>();
        BSProcess c1 = new BSProcess();
        c1.setProcessSeq(1);
        bsProcesses.add(c1);
        bsProcesses.add(c1);
        PowerMockito.when(CrafttechRemoteService.getProcessInfoByItemOrTask(Mockito.anyString()))
                .thenReturn(bsProcesses)
        ;
        Assert.assertEquals("123",  wipInfo.getItemNo());
        flowControlCommonServiceImpl.boardBindScanCheck(flow);
    }

    @Test
    public void checkFirstCraftWoStatus() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO b1 = new PsEntityPlanBasicDTO();
        b1.setSourceTask("123");
        b1.setWorkOrderStatus("已开工");
        b1.setCraftSection("SMT-B");
        flow.setEntityPlanBasic(b1);

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupMeaning("已开工");
        lookupValueList.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_SN_COMMON_SCAN_WORKSTATUS))
                .thenReturn(lookupValueList);

        List<CtRouteInfoDTO> routeInfo = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        routeInfo.add(c1);
        List<CtRouteDetailDTO> detailDTOS = new LinkedList<>();
        CtRouteDetailDTO c2 = new CtRouteDetailDTO();
        c2.setCraftSection("SMT-B");
        detailDTOS.add(c2);
        c1.setListDetail(detailDTOS);

        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(Mockito.anyObject()))
                .thenReturn(routeInfo);
        Assert.assertEquals("SMT-B",  c2.getCraftSection());
        flowControlCommonServiceImpl.checkFirstCraftWoStatus(flow);
    }

    @Test
    public void checkZLFirstProcess() {
        String processGroup = "(*)12$34$5";
        String processCode = "34";
        Assert.assertTrue(flowControlCommonServiceImpl.checkZLFirstProcess(processGroup, processCode));
    }

    @Test
    public void checkZLLastProcess() {
        String processGroup = "12$34$(*)5";
        String processCode = "34";
        Assert.assertTrue(flowControlCommonServiceImpl.checkZLLastProcess(processGroup, processCode));
    }

    @Test
    public void checkInputQty() throws MesBusinessException {
        FlowControlInfoDTO infoDTO = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setWorkOrderQty(new BigDecimal("234"));
        a1.setInputQty(new BigDecimal(0));
        a1.setOutputQty(new BigDecimal(0));
        infoDTO.setEntityPlanBasic(a1);
        infoDTO.setSn("877521000001");
        infoDTO.setIsFirstProcess("Y");
        infoDTO.setIsZLLastProcess("Y");
        Assert.assertEquals("877521000001", infoDTO.getSn());
        flowControlCommonServiceImpl.checkInputQty(infoDTO);
    }

    @Test
    public void checkZLCheckProcess() {
        String processGroup = "3$(*)56$4";
        String wipCurrProcess = "3";
        String inputProcess = "4";

        flowControlCommonServiceImpl.checkZLCheckProcess(processGroup, wipCurrProcess, inputProcess);
        flowControlCommonServiceImpl.checkZLCheckProcess("", wipCurrProcess, inputProcess);
        Assert.assertFalse(flowControlCommonServiceImpl.checkZLCheckProcess("3", wipCurrProcess, inputProcess));
    }


    @Test
    public void snLockControl() throws Exception {
        BarcodeLockDetailEntityDTO barcodeLock = new BarcodeLockDetailEntityDTO();
        List<String> snList = new LinkedList<>();
        snList.add("123123400001");
        barcodeLock.setSnList(snList);
        barcodeLock.setProcessCodeList(snList);

        List<WipExtendIdentification> allChildSn = new LinkedList<>();
        WipExtendIdentification a1 = new WipExtendIdentification();
        a1.setFormSn("123123400001");
        a1.setSn("23456789");
        allChildSn.add(a1);
        PowerMockito.when(wipExtendIdentificationRepository.getAllChildSn(Mockito.anyList()))
                .thenReturn(allChildSn);

        List<BarcodeLockDetail> lockDetailList = new LinkedList<>();
        BarcodeLockDetail b1 = new BarcodeLockDetail();
        b1.setBatchSn("1235678");
        b1.setLastUpdatedBy("123");
        b1.setBillNo("123");
        b1.setType(Constant.LOCK_TYPE_BATCH);
        lockDetailList.add(b1);
        BarcodeLockDetail b2 = new BarcodeLockDetail();
        b2.setBatchSn("123123400001");
        b2.setLockProcessCode("123");
        b2.setLastUpdatedBy("234");
        b2.setBillNo("234");
        lockDetailList.add(b2);
        BarcodeLockDetail b3 = new BarcodeLockDetail();
        b3.setBatchSn("23456789");
        b3.setLockProcessCode("123");
        b3.setBillNo("345");
        lockDetailList.add(b3);
        BarcodeLockDetail b4 = new BarcodeLockDetail();
        b4.setBatchSn("234567800001");
        b4.setLockProcessCode("123");
        b4.setBillNo("45666");
        lockDetailList.add(b4);
        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.anyList(), Mockito.any()))
                .thenReturn(lockDetailList);

        PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(lockDetailList);

        List<BsPubHrvOrgId> bsPubHrvOrgInfo = new LinkedList<>();
        BsPubHrvOrgId c1 = new BsPubHrvOrgId();
        c1.setUserId("234");
        c1.setUserName("234");
        bsPubHrvOrgInfo.add(c1);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyString()))
                .thenReturn(bsPubHrvOrgInfo);

        ArrayList<BSProcessDTO> processDTOS = new ArrayList<>();
        BSProcessDTO d1 = new BSProcessDTO();
        d1.setProcessCode("1234");
        processDTOS.add(d1);

        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(Constant.JSON_BO).toString()).thenReturn(JSON.toJSONString(processDTOS));

        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);

        List<String> currList = new LinkedList<>();
        currList.add("133");
        PowerMockito.when(psWipInfoRepository.currProcessListByBatchSn(Mockito.anyList()))
                .thenReturn(currList);

        List<BarcodeLockTemp> list = new LinkedList<>();
        BarcodeLockTemp e1 = new BarcodeLockTemp();
        e1.setBillNo("234");
        e1.setSn("789");
        list.add(e1);
        PowerMockito.when(barcodeLockTempRepository.selectUnLockInfo(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(list)
        ;
        try {
            flowControlCommonServiceImpl.snLockControl(barcodeLock);
        } catch (Exception e) {
            Assert.assertEquals("234", e1.getBillNo());
        }
    }

    @Test
    public void snBindingControl() throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        List<String> snList = new LinkedList<>();
        snList.add("123");
        snList.add("234");
        barcodeBindingDTO.setSnList(snList);
        barcodeBindingDTO.setWorkOrderNo("123");
        barcodeBindingDTO.setCurrProcessCode("1");
        barcodeBindingDTO.setWorkStation("2");

        List<PsEntityPlanBasicDTO> psEntityList = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setRouteId("123");
        a1.setIsProcessControl("N");
        psEntityList.add(a1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString()))
                .thenReturn(psEntityList);

        List<CtRouteInfoDTO> ctRouteInfoDTOList = new LinkedList<>();
        CtRouteInfoDTO b1 = new CtRouteInfoDTO();
        ctRouteInfoDTOList.add(b1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO b2 = new CtRouteDetailDTO();
        b2.setNextProcess("2");
        b2.setProcessCode("1");
        b2.setLastProcess("Y");
        listDetail.add(b2);
        b1.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.postScanProcess(Mockito.anyObject()))
                .thenReturn(ctRouteInfoDTOList)
        ;

        List<PsWipInfoDTO> listByBatchSnList = new LinkedList<>();
        PsWipInfoDTO c1 = new PsWipInfoDTO();
        c1.setItemNo("123");
        c1.setSn("123");
        c1.setAssembleFlag("N");
        listByBatchSnList.add(c1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(Mockito.anyList()))
                .thenReturn(listByBatchSnList)
        ;

        List<ProdBindingSettingDTO> prodBindingSettingDTOS = new LinkedList<>();
        ProdBindingSettingDTO d1 = new ProdBindingSettingDTO();
        d1.setProductCode("123");
        d1.setItemCode("123");
        d1.setProcessCode("1");
        d1.setUsageCount(new BigDecimal(1));
        prodBindingSettingDTOS.add(d1);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(Mockito.anyObject()))
                .thenReturn(prodBindingSettingDTOS);

        List<WipExtendIdentification> wipExtendIdentifications = new LinkedList<>();
        WipExtendIdentification e1 = new WipExtendIdentification();
        e1.setFormSn("123");
        e1.setItemNo("123");
        e1.setFormQty(new BigDecimal(1));
        wipExtendIdentifications.add(e1);
        PowerMockito.when(wipExtendIdentificationRepository.queryBindingQtyBatch(Mockito.anyList()))
                .thenReturn(wipExtendIdentifications);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(sysLookUpValue);

        try {
            flowControlCommonServiceImpl.snBindingControl(barcodeBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals("123", e1.getFormSn());
        }
    }

    @Test
    public void technicalControl() throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        barcodeBindingDTO.setCurrProcessCode("1");
        List<String> sns = new LinkedList<>();
        sns.add("889951200001");
        barcodeBindingDTO.setSnList(sns);
        barcodeBindingDTO.setWorkOrderNo("8899512-SMT-B5201");
        barcodeBindingDTO.setWorkStation("534");

        List<BarcodeBindingDTO> list = new LinkedList<>();
        BarcodeBindingDTO a1 = new BarcodeBindingDTO();
        a1.setCurrProcessCode("2");
        a1.setWorkStation("345");
        list.add(a1);
        barcodeBindingDTO.setChildList(list);

        List<PsEntityPlanBasicDTO> psEntityList = new LinkedList<>();
        PsEntityPlanBasicDTO b1 = new PsEntityPlanBasicDTO();
        b1.setWorkOrderNo("8899512-SMT-B5201");
        b1.setCraftSection("SMT-B");
        b1.setRouteId("u8u88-999");
        b1.setItemNo("123456789JIU");
        b1.setProdplanId("8899512");
        b1.setProcessGroup("1$2(*)3");
        b1.setIsProcessControl(Constant.FLAG_N);
        psEntityList.add(b1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString()))
                .thenReturn(psEntityList)
        ;

        List<CtRouteInfoDTO> ctRouteInfoDTOList = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        List<CtRouteDetailDTO> details = new LinkedList<>();
        CtRouteDetailDTO c2 = new CtRouteDetailDTO();
        c2.setProcessCode("2");
        c2.setNextProcess("345");
        c2.setLastProcess(Constant.FLAG_Y);
        details.add(c2);
        c1.setListDetail(details);
        ctRouteInfoDTOList.add(c1);
        PowerMockito.when(CrafttechRemoteService.postScanProcess(Mockito.anyObject()))
                .thenReturn(ctRouteInfoDTOList)
        ;

        List<TechnicalChangeDetailDTO> technicalList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setSn("889951200001");
        d1.setChgReqNo("123");
        d1.setUnlockStatus(1);
        d1.setUnlockType("3");
        d1.setUnlockCraftSection("SMT-B");
        d1.setProdplanId("8899512");
        technicalList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setSn("889951200001");
        d2.setChgReqNo("1234");
        d2.setUnlockStatus(1);
        d2.setUnlockType("0");
        d2.setUnlockCraftSection("SMT-B");
        d2.setProdplanId("8899512");
        technicalList.add(d2);
        PowerMockito.when(technicalChangeHeadRepository.querySnBatchDetail(Mockito.any()))
                .thenReturn(technicalList)
        ;

        List<WipExtendIdentification> childList = new LinkedList<>();
        WipExtendIdentification e1 = new WipExtendIdentification();
        e1.setSn("88995220001");
        childList.add(e1);
        PowerMockito.when(wipExtendIdentificationRepository.getAllChildSn(Mockito.anyList()))
                .thenReturn(childList)
        ;

        List<PsWipInfo> listByBatchSn = new LinkedList<>();
        PsWipInfo f1 = new PsWipInfo();
        f1.setSn("88995220001");
        f1.setAttribute1("8899522");
        listByBatchSn.add(f1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.any()))
                .thenReturn(listByBatchSn);

        List<ProdBindingSettingDTO> bingList = new LinkedList<>();
        ProdBindingSettingDTO g1 = new ProdBindingSettingDTO();
        g1.setProductCode("123456789JIU");
        g1.setItemCode("123456789JIU1");
        bingList.add(g1);
        PowerMockito.when(centerfactoryRemoteService.queryUnBingForItemNo(Mockito.anyString()))
                .thenReturn(bingList)
        ;

        List<PsTask> treeList = new LinkedList<>();
        PsTask h1 = new PsTask();
        h1.setItemNo("123456789JIU");
        treeList.add(h1);
        List<PsTask> child = new LinkedList<>();
        PsTask h2 = new PsTask();
        h2.setProdplanId("8899522");
        h2.setItemNo("123456789JIU1");
        child.add(h2);
        List<PsTask> childTwo = new LinkedList<>();
        PsTask h3 = new PsTask();
        h3.setProdplanId("8899562");
        h3.setItemNo("123456789JIU12");
        childTwo.add(h3);
        h2.setSubChildList(childTwo);
        h1.setSubChildList(child);
        PowerMockito.when(planscheduleRemoteService.getSubTaskTreeByTaskNo(Mockito.anyList(), Mockito.any()))
                .thenReturn(treeList);

        List<CtRouteDetail> ctRouteDetailInfo = new LinkedList<>();
        CtRouteDetail i1 = new CtRouteDetail();
        i1.setProcessCode("1");
        i1.setProcessSeq(0);
        i1.setCraftSection("SMT-A");
        ctRouteDetailInfo.add(i1);
        CtRouteDetail i2 = new CtRouteDetail();
        i2.setProcessCode("2");
        i2.setProcessSeq(1);
        i2.setCraftSection("SMT-B");
        ctRouteDetailInfo.add(i2);
        CtRouteDetail i3 = new CtRouteDetail();
        i3.setProcessCode("N");
        i3.setProcessSeq(3);
        i3.setCraftSection("入库");
        ctRouteDetailInfo.add(i3);

        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyObject()))
                .thenReturn(ctRouteDetailInfo)
        ;

        List<BSProcess> processAll = new LinkedList<>();
        BSProcess j1 = new BSProcess();
        j1.setProcessCode("B");
        processAll.add(j1);
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.any()))
                .thenReturn(processAll)
        ;

        List<TechnicalChangeDetailDTO> dtoList = new LinkedList<>();
        TechnicalChangeDetailDTO k1 = new TechnicalChangeDetailDTO();
        k1.setChgReqNo("123");
        k1.setProdplanId("8899524");
        dtoList.add(k1);
        PowerMockito.when(technicalChangeDetailRepository.selectNoSkipDataBatch(Mockito.anyList()))
                .thenReturn(dtoList)
        ;

        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);

        try {
            flowControlCommonServiceImpl.technicalControl(barcodeBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals("customize.msg", e.getMessage());
        }
        PowerMockito.when(technicalChangeDetailRepository.selectNoSkipDataBatch(Mockito.anyList()))
                .thenReturn(null)
        ;
        flowControlCommonServiceImpl.technicalControl(barcodeBindingDTO);
    }

    @Test
    public void technicalControlBySn() throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        try {
            flowControlCommonServiceImpl.technicalControlBySn(barcodeBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAM_IS_NULL, e.getMessage());
        }
        barcodeBindingDTO.setSn("889975800001");
        barcodeBindingDTO.setProdplanId("8899758");

        List<TechnicalChangeDetailDTO> technicalList = new LinkedList<>();
        TechnicalChangeDetailDTO a1 = new TechnicalChangeDetailDTO();
        a1.setChgReqNo("520133");
        a1.setSn("889975800001");
        technicalList.add(a1);
        TechnicalChangeDetailDTO a2 = new TechnicalChangeDetailDTO();
        a2.setChgReqNo("5201334");
        a2.setSn("889975800001");
        a2.setUnlockStatus(1);
        a2.setUnlockType(Constant.STR_0);
        technicalList.add(a2);
        PowerMockito.when(technicalChangeHeadRepository.querySnBatchDetail(Mockito.any()))
                .thenReturn(technicalList);


        PowerMockito.mockStatic(CommonUtils.class);
        try {
            flowControlCommonServiceImpl.technicalControlBySn(barcodeBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_LOCK_MSG, e.getMessage());
        }

        a1.setUnlockType(Constant.STR_0);
        a1.setUnlockStatus(1);
        try {
            flowControlCommonServiceImpl.technicalControlBySn(barcodeBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_LOCK_MSG, e.getMessage());
        }
    }

    @Test
    public void checkSnTechnical() throws Exception{
        BarcodeBindingDTO barcodeBindingDTO=new BarcodeBindingDTO();
        barcodeBindingDTO.setProdplanId("123");
        List<TechnicalChangeDetailDTO> technicalList=null;
        PowerMockito.when(technicalChangeHeadRepository.querySnBatchDetail(Mockito.any()))
                .thenReturn(technicalList);
        Whitebox.invokeMethod(flowControlCommonServiceImpl,
                "checkSnTechnical",barcodeBindingDTO);
        Assert.assertEquals("123", barcodeBindingDTO.getProdplanId());

    }

    @Test
    public void checkProcessIsSkip() throws Exception {
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setCurrProcessCode("N");
        entity.setSn("7110001200001");
        ReflectionTestUtils.setField(flowControlCommonServiceImpl, "highTempSwitch", "N");
        flowControlCommonServiceImpl.checkProcessIsSkip(entity,new ArrayList<>());
        ReflectionTestUtils.setField(flowControlCommonServiceImpl, "highTempSwitch", "Y");
        try {
            flowControlCommonServiceImpl.checkProcessIsSkip(entity,new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_SN_NEXT_PROCESS,e.getMessage());
        }
        List<CtRouteDetailDTO> listDetail = new ArrayList() {{
            add(new CtRouteDetailDTO() {{
                setNextProcess("123");
                setProcessSeq(new BigDecimal("0"));
            }});
            add(new CtRouteDetailDTO() {{
                setNextProcess("N");
                setProcessSeq(new BigDecimal("1"));
            }});
        }};
        try {
            flowControlCommonServiceImpl.checkProcessIsSkip(entity,listDetail);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TO_NEXT_PROCESS,e.getMessage());
        }
        entity.setaProcessCode("N");
        listDetail.clear();
        listDetail.add(new CtRouteDetailDTO() {{
            setNextProcess("N");
            setProcessSeq(new BigDecimal("0"));
        }});
        flowControlCommonServiceImpl.checkProcessIsSkip(entity,listDetail);
    }

    @Test
    public void checkSkipRule() throws Exception {
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setSn("7110001200001");
        entity.setaProcessCode("N");
        List<CtRouteDetailDTO> listDetail = new ArrayList() {{
            add(new CtRouteDetailDTO() {{
                setNextProcess("123");
                setProcessSeq(new BigDecimal("0"));
                setDoOrNotFlag("Y");
            }});
            add(new CtRouteDetailDTO() {{
                setNextProcess("N");
                setProcessSeq(new BigDecimal("1"));
                setDoOrNotFlag("Y");
                setSkipRule(Constant.HIGH_TEMP_RULE);
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "checkSkipRule",entity,listDetail,2);
        Assert.assertEquals("7110001200001", entity.getSn());
    }

    /* Started by AICoder, pid:i309575dfcc223e141db0bb682a196392948e7f1 */
    @Test
    public void getProdplanIdMap() throws Exception {
        ReflectUtil.setFieldValue(flowControlCommonServiceImpl,"bindControlSwitch","N");

        List<ProdBindingSettingDTO> prodBindingSettingDTOList = new ArrayList() {{
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851281");
                setProcessCode("2");
                setUsageCount(new BigDecimal("1"));
                setProductCode("2");
            }});
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851282");
                setProcessCode("2");
                setProductCode("2");
                setUsageCount(new BigDecimal("1"));
            }});
        }};

        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
            }});
            add(new PsWipInfoDTO() {{
                setItemNo("122096851281ALB");
            }});
        }};

        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,psWipInfoDTOList);
        Assert.assertEquals(3, psWipInfoDTOList.size());
        ReflectUtil.setFieldValue(flowControlCommonServiceImpl,"bindControlSwitch","Y");
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,psWipInfoDTOList);
        Assert.assertEquals(3, psWipInfoDTOList.size());
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,new ArrayList<>());
        Assert.assertEquals(3, psWipInfoDTOList.size());

        prodBindingSettingDTOList. add(new ProdBindingSettingDTO() {{
            setItemCode("122096851281");
            setProcessCode("2");
            setUsageCount(new BigDecimal("1"));
            setProductCode("122096851281ALB");
        }});
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,psWipInfoDTOList);
        Assert.assertEquals(3, psWipInfoDTOList.size());

        psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setAttribute1("7778889");
            }});
        }};

        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,psWipInfoDTOList);
        Assert.assertEquals(1, psWipInfoDTOList.size());
        Map<String, List<ProdBindingSettingDTO>> prodplanIdMap = new HashMap<>();
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOList = new ArrayList() {{
            add(new BProdBomChangeDetailDTO() {{
                setItemCode("122096851282");
                setOriginalItemCode("122096851281");
                setProdplanId("2");
                setUsageCount(new BigDecimal("1"));
            }});
            add(new BProdBomChangeDetailDTO() {{
                setItemCode("122096851282");
                setOriginalItemCode("122096851282");
                setProdplanId("7778889");
                setUsageCount(new BigDecimal("1"));
            }});
        }};
        prodplanIdMap.put("7778889",prodBindingSettingDTOList);
        psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
            }});
            add(new PsWipInfoDTO() {{
                setItemNo("122096851281ALB");
            }});
        }};
        PowerMockito.when(centerfactoryRemoteService.queryMBomDetailChangeByProdplanId(any())).thenReturn(bProdBomChangeDetailDTOList);
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "getProdplanIdMap",prodBindingSettingDTOList,psWipInfoDTOList);
        Assert.assertEquals(3, psWipInfoDTOList.size());
    }

    @Test
    public void filterNoComplete() throws Exception {
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        Map<String, List<ProdBindingSettingDTO>> prodplanIdMap = new HashMap<>();

        List<ProdBindingSettingDTO> prodBindingSettingDTOList = new ArrayList() {{
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851281");
                setProcessCode("2");
                setUsageCount(new BigDecimal("1"));
            }});
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851282");
                setProcessCode("2");
                setUsageCount(new BigDecimal("1"));
            }});
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851282");
                setProcessCode("5");
                setUsageCount(new BigDecimal("1"));
            }});
        }};
        prodplanIdMap.put("122096851281ALB",prodBindingSettingDTOList);

        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900002");
                setItemNo("122096851281ALB");
            }});
        }};
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setFormQty(BigDecimal.valueOf(2));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setFormQty(BigDecimal.valueOf(2));
            }});
        }};
        barcodeBindingDTO.setLastProcessList(new ArrayList(){{add("2");add("4");}});

        List<String> returnList = Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoComplete",barcodeBindingDTO,prodplanIdMap,psWipInfoDTOList,wipExtendIdentificationList);
        Assert.assertEquals(0, returnList.size());

        psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900002");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoComplete",barcodeBindingDTO,prodplanIdMap,psWipInfoDTOList,wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());


        wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(2));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(2));
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoComplete",barcodeBindingDTO,prodplanIdMap,psWipInfoDTOList,wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

        wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(0));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(0));
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoComplete",barcodeBindingDTO,prodplanIdMap,psWipInfoDTOList,wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

        barcodeBindingDTO.setLastProcessList(new ArrayList(){{add("2");add("5");}});
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoComplete",barcodeBindingDTO,prodplanIdMap,psWipInfoDTOList,wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

    }

    @Test
    public void filterNoCompleteNew() throws Exception {
        ReflectUtil.setFieldValue(flowControlCommonServiceImpl, "bindControlSwitch", "N");
        BarcodeBindingDTO barcodeBindingDTO = new BarcodeBindingDTO();
        Map<String, List<ProdBindingSettingDTO>> prodplanIdMap = new HashMap<>();

        List<ProdBindingSettingDTO> prodBindingSettingDTOList = new ArrayList() {{
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851281");
                setProcessCode("2");
                setUsageCount(new BigDecimal("1"));
            }});
            add(new ProdBindingSettingDTO() {{
                setItemCode("122096851282");
                setProcessCode("2");
                setUsageCount(new BigDecimal("1"));
            }});
        }};
        prodplanIdMap.put("7778889", prodBindingSettingDTOList);

        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900002");
                setItemNo("122096851281ALB");
            }});
        }};
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setFormQty(BigDecimal.valueOf(2));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setFormQty(BigDecimal.valueOf(2));
            }});
        }};
        barcodeBindingDTO.setLastProcessList(new ArrayList() {{
            add("2");
        }});

        List<String> returnList = Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(0, returnList.size());

        ReflectUtil.setFieldValue(flowControlCommonServiceImpl, "bindControlSwitch", "Y");
        returnList = Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(0, returnList.size());

        psWipInfoDTOList = new ArrayList() {{
            add(new PsWipInfoDTO() {{
                setSn("777888900001");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
            add(new PsWipInfoDTO() {{
                setSn("777888900002");
                setItemNo("122096851281ALB");
                setAttribute1("7778889");
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());


        wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(2));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(2));
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

        wipExtendIdentificationList = new ArrayList() {{
            add(new WipExtendIdentification() {{
                setFormSn("777888900001");
                setItemNo("122096851281");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(0));
            }});
            add(new WipExtendIdentification() {{
                setFormSn("777888900002");
                setItemNo("122096851282");
                setProcessCode("2");
                setFormQty(BigDecimal.valueOf(0));
            }});
        }};
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

        barcodeBindingDTO.setLastProcessList(new ArrayList() {{
            add("2");
            add("5");
        }});
        Whitebox.invokeMethod(flowControlCommonServiceImpl, "filterNoCompleteNew", barcodeBindingDTO, prodplanIdMap, psWipInfoDTOList, wipExtendIdentificationList);
        Assert.assertEquals(2, psWipInfoDTOList.size());

    }
    /* Ended by AICoder, pid:i309575dfcc223e141db0bb682a196392948e7f1 */


    @Test
    public void replaceItemCode() throws Exception {
        Map<String, List<BProdBomChangeDetailDTO>> bMap = new HashMap<>();
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOList = new ArrayList() {{
            add(new BProdBomChangeDetailDTO() {{
                setItemCode("122096851282");
                setOriginalItemCode("122096851281");
                setProdplanId("2");
                setUsageCount(new BigDecimal("1"));
            }});
            add(new BProdBomChangeDetailDTO() {{
                setItemCode("122096851282");
                setOriginalItemCode("122096851282");
                setProdplanId("7778889");
                setUsageCount(new BigDecimal("1"));
            }});
        }};
        bMap.put("7778889", bProdBomChangeDetailDTOList);
        List<ProdBindingSettingDTO> prodBindingSettingDTOS = new LinkedList<>();
        ProdBindingSettingDTO d1 = new ProdBindingSettingDTO();
        d1.setProductCode("123");
        d1.setItemCode("122096851281");
        d1.setProcessCode("1");
        d1.setUsageCount(new BigDecimal(1));
        prodBindingSettingDTOS.add(d1);


        Whitebox.invokeMethod(flowControlCommonServiceImpl,"replaceItemCode", "7778889", bMap, prodBindingSettingDTOS);
        Assert.assertEquals(1, prodBindingSettingDTOS.size());

    }
}
