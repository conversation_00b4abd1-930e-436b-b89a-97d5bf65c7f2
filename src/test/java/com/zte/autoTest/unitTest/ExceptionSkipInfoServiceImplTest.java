package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.AssemblyResultServiceImpl;
import com.zte.application.impl.ExceptionSkipInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class,CommonUtils.class, MESHttpHelper.class, CrafttechRemoteService.class, BasicsettingRemoteService.class, HttpRemoteService.class,PlanscheduleRemoteService.class})
public class ExceptionSkipInfoServiceImplTest extends PowerBaseTestCase {

    @Mock
    private ExceptionSkipInfoRepository exceptionSkipInforepository;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @InjectMocks
    private ExceptionSkipInfoServiceImpl exceptionSkipInfoService;


    @Test
    public void verifySnCanSkip()throws Exception {
        List<ExceptionSkipInfoDTO> exceptionSkipInfolist = new ArrayList<>();
        ExceptionSkipInfoDTO exceptionSkipInfoDTO2 = new ExceptionSkipInfoDTO();
        exceptionSkipInfoDTO2.setProcessCode("1");
        exceptionSkipInfolist.add(exceptionSkipInfoDTO2);
        PowerMockito.when(exceptionSkipInforepository.getList(any())).thenReturn(exceptionSkipInfolist);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(new PsWipInfo());
        Assert.assertNotNull(exceptionSkipInfoService.verifySnCanSkip("sn","1"));
    }

    @Test
    public void verifySnOrItemOrProdplanId()throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CrafttechRemoteService.class,PlanscheduleRemoteService.class);
        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        PowerMockito.when(barcodeLockDetailService.getCtRouteDetailDTOS(any(),any())).thenReturn(ctRouteDetailDTOList);
        List<BSProcess> bsProcessList = JSON.parseArray("[{\"controlAddress\":\"\",\"craftSection\":\"DIP\",\"enabledFlag\":\"Y\",\"factoryId\":55,\"lastUpdatedBy\":\"10270446\",\"lastUpdatedDate\":1657040037000,\"processCode\":\"6\",\"processControl\":\"0\",\"processControlGroup\":\"Uni_Scan\",\"processControlGroupName\":\"通用扫描（不含不良扫描）\",\"processId\":\"iMESTEST00000003\",\"processName\":\"短插\",\"processType\":\"手工测试\",\"remark\":\"河源测试，勿删\",\"scanByStation\":1,\"testControl\":\"1\",\"xType\":\"子工序\"}]",BSProcess.class);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(anyObject())).thenReturn(bsProcessList);

        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        a1.setSn("888862100104");
        psWipInfoDTOList.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(anyList())).thenReturn(psWipInfoDTOList);
        PowerMockito.when(psWipInfoService.getWipInfoList(anyList())).thenReturn(psWipInfoDTOList);

        List<PsTask> psTaskList = JSON.parseArray("[{\"createBy\":\"system\",\"createDate\":1655780875000,\"enabledFlag\":\"Y\",\"entityId\":2,\"externalType\":\"DHOME\",\"factoryId\":55,\"firstWarehouseDate\":1655878851000,\"internalType\":\"ONT\",\"itemName\":\"ZXV10 B860AV2D1PR STBAB\",\"itemNo\":\"129580160029AAB\",\"lastDeliveryDate\":1656316349000,\"lastUpdatedBy\":\"00236517\",\"lastUpdatedDate\":1656253251000,\"prodplanId\":\"8888621\",\"prodplanNo\":\"130000206830ABB\",\"releaseDate\":1655711549000,\"sourceSys\":\"STEP\",\"taskId\":\"AAAcsbAAFAAAAS621\",\"taskNo\":\"129580160029AAB\",\"taskQty\":20000,\"taskStatus\":\"已完工\"}]",PsTask.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(psTaskList);

        ExceptionSkipInfoDTO exceptionSkipInfoDTO = JSON.parseObject("{\"maintenanceType\":\"2\",\"snOrBatchOrItem\":\"129580160029AAB\"}",ExceptionSkipInfoDTO.class);
        PowerMockito.when(BasicsettingRemoteService.getBBomHeaderByProductCodes(any())).thenReturn(new ArrayList<BBomHeaderDTO>(){{add(new BBomHeaderDTO());}});
        exceptionSkipInfoDTO.setNeedProcess(true);
        exceptionSkipInfoService.verifySnOrItemOrProdplanId(exceptionSkipInfoDTO);

        ExceptionSkipInfoDTO exceptionSkipInfoDTO2 = new ExceptionSkipInfoDTO();
        exceptionSkipInfoDTO2.setMaintenanceType("0");
        exceptionSkipInfoDTO2.setSnOrBatchOrItem("888862100104");
        exceptionSkipInfoDTO2.setNeedProcess(true);
        exceptionSkipInfoService.verifySnOrItemOrProdplanId(exceptionSkipInfoDTO2);

        ExceptionSkipInfoDTO exceptionSkipInfoDTO3 = new ExceptionSkipInfoDTO();
        exceptionSkipInfoDTO3.setMaintenanceType("1");
        exceptionSkipInfoDTO3.setSnOrBatchOrItem("8888621");
        exceptionSkipInfoDTO3.setNeedProcess(true);
        Assert.assertNotNull(exceptionSkipInfoService.verifySnOrItemOrProdplanId(exceptionSkipInfoDTO3));
    }
    @Test
    public void save()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class,BasicsettingRemoteService.class,CrafttechRemoteService.class,PlanscheduleRemoteService.class);
        ExceptionSkipInfoDTO exceptionSkipInfoDTO = JSON.parseObject("{\"maintenanceType\":\"2\",\"snOrBatchOrItem\":\"129580160029AAB\",\"processCode\":\"6\",\"exceptionType\":\"需要测试\",\"reason\":\"2132\"}",ExceptionSkipInfoDTO.class);
        List<ExceptionSkipInfoDTO> exceptionSkipInfolist = new ArrayList<>();
        ExceptionSkipInfoDTO exceptionSkipInfoDTO2 = new ExceptionSkipInfoDTO();
        BeanUtils.copyProperties(exceptionSkipInfoDTO,exceptionSkipInfoDTO2);
        exceptionSkipInfoDTO2.setMaintenanceType("0");
        exceptionSkipInfolist.add(exceptionSkipInfoDTO);
        PowerMockito.when(exceptionSkipInforepository.getList(any())).thenReturn(exceptionSkipInfolist);
        PowerMockito.when(BasicsettingRemoteService.getBBomHeaderByProductCodes(any())).thenReturn(new ArrayList<BBomHeaderDTO>(){{add(new BBomHeaderDTO());}});
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(),anyString(), anyString())).thenReturn("");
        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        psWipInfoDTOList.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(any())).thenReturn(psWipInfoDTOList);
        try {
            exceptionSkipInfoService.save(exceptionSkipInfoDTO);
        }catch (Exception e){String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);}
    }
    @Test
    public void saveTwo()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class,BasicsettingRemoteService.class,CrafttechRemoteService.class,PlanscheduleRemoteService.class);
        ExceptionSkipInfoDTO exceptionSkipInfoDTO = JSON.parseObject("{\"maintenanceType\":\"2\",\"snOrBatchOrItem\":\"129580160029AAB\",\"processCode\":\"6\",\"exceptionType\":\"需要测试\",\"reason\":\"2132\"}",ExceptionSkipInfoDTO.class);
        exceptionSkipInfoDTO.setMaintenanceType("1");
        List<ExceptionSkipInfoDTO> exceptionSkipInfolist = new ArrayList<>();
        PowerMockito.when(exceptionSkipInforepository.getList(any())).thenReturn(null);
        PowerMockito.when(BasicsettingRemoteService.getBBomHeaderByProductCodes(any())).thenReturn(new ArrayList<BBomHeaderDTO>(){{add(new BBomHeaderDTO());}});
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(),anyString(), anyString())).thenReturn("");
        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        psWipInfoDTOList.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(any())).thenReturn(psWipInfoDTOList);
        List<PsTask> psTaskList = JSON.parseArray("[{\"createBy\":\"system\",\"createDate\":1655780875000,\"enabledFlag\":\"Y\",\"entityId\":2,\"externalType\":\"DHOME\",\"factoryId\":55,\"firstWarehouseDate\":1655878851000,\"internalType\":\"ONT\",\"itemName\":\"ZXV10 B860AV2D1PR STBAB\",\"itemNo\":\"129580160029AAB\",\"lastDeliveryDate\":1656316349000,\"lastUpdatedBy\":\"00236517\",\"lastUpdatedDate\":1656253251000,\"prodplanId\":\"8888621\",\"prodplanNo\":\"130000206830ABB\",\"releaseDate\":1655711549000,\"sourceSys\":\"STEP\",\"taskId\":\"AAAcsbAAFAAAAS621\",\"taskNo\":\"129580160029AAB\",\"taskQty\":20000,\"taskStatus\":\"已完工\"}]",PsTask.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(psTaskList);
        exceptionSkipInfoService.save(exceptionSkipInfoDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void pageList()throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,PlanscheduleRemoteService.class);
        ExceptionSkipInfoDTO exceptionSkipInfoDTO = JSON.parseObject("{\"prodplanId\":\"11\",\"sn\":\"888862100104\",\"itemNo\":\"\",\"createDateStart\":\"\",\"createDateEnd\":\"\",\"rows\":10,\"page\":1}",ExceptionSkipInfoDTO.class);
        exceptionSkipInfoDTO.setCreateDateStart("2022-07-11 01:01:01");
        exceptionSkipInfoDTO.setCreateDateEnd("2022-08-11 01:01:01");
        List<ExceptionSkipInfoDTO> exceptionSkipInfolist = JSON.parseArray("[{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"65748370-201a-45f892-9af3-bd9443ab5bc3\",\"snOrBatchOrItem\":\"888862100104\",\"snOrBatchOrItemList\":null,\"sn\":\"888862100104\",\"snList\":null,\"prodplanId\":\"8888621\",\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"\",\"processCodeName\":null,\"exceptionType\":\"需要测试\",\"maintenanceType\":\"0\",\"maintenanceTypeName\":\"按条码\",\"reason\":\"222\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 15:24:31\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:24:31\",\"enabledFlag\":\"Y\",\"enabledFlagName\":\"有效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"f7ac4d70-3f97-4d4555e-b513-9067d2259dec\",\"snOrBatchOrItem\":\"129580160029AAB\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":null,\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"2\",\"maintenanceTypeName\":\"按料单\",\"reason\":\"44\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 15:18:58\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:20:09\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"f7ac4d70-3f97-4d4e-b513-9067d2259dec\",\"snOrBatchOrItem\":\"129580160029AAB\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":null,\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"2\",\"maintenanceTypeName\":\"按料单\",\"reason\":\"44\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 15:18:58\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:20:09\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"869607a4-9baf-4809-8f99-71cc21a17d89\",\"snOrBatchOrItem\":\"129580160029AAB\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":null,\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"2\",\"maintenanceTypeName\":\"按料单\",\"reason\":\"收发\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 14:38:11\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:18:51\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"869607a4-9baf-482209-8f99-71cc21a17d89\",\"snOrBatchOrItem\":\"129580160029AAB\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":null,\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"2\",\"maintenanceTypeName\":\"按料单\",\"reason\":\"收发\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 14:38:11\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:18:51\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"2547b86d-2ee8-42844c-bb2f-43ee97574b3f\",\"snOrBatchOrItem\":\"8888621\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":\"8888621\",\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"1\",\"maintenanceTypeName\":\"按批次\",\"reason\":\"阿萨德的说法\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 14:02:52\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:20:07\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"2547b86d-2ee8-428c-bb2f-43ee97574b3f\",\"snOrBatchOrItem\":\"8888621\",\"snOrBatchOrItemList\":null,\"sn\":null,\"snList\":null,\"prodplanId\":\"8888621\",\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"6\",\"processCodeName\":\"短插\",\"exceptionType\":\"需要测试\",\"maintenanceType\":\"1\",\"maintenanceTypeName\":\"按批次\",\"reason\":\"阿萨德的说法\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 14:02:52\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:20:07\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null},{\"sort\":null,\"order\":null,\"page\":1,\"rows\":10,\"id\":\"d87c6b9b-86b8-4288b5-a4c3-f50c28c6cdfb\",\"snOrBatchOrItem\":\"888862100104\",\"snOrBatchOrItemList\":null,\"sn\":\"888862100104\",\"snList\":null,\"prodplanId\":\"8888621\",\"prodplanIdList\":null,\"itemNo\":\"129580160029AAB\",\"processCode\":\"\",\"processCodeName\":null,\"exceptionType\":\"需要测试\",\"maintenanceType\":\"0\",\"maintenanceTypeName\":\"按条码\",\"reason\":\"11\",\"remark\":null,\"createBy\":\"10270446\",\"createByName\":\"李泽奎10270446\",\"createDate\":\"2022-07-06 13:33:39\",\"lastUpdatedBy\":\"10270446\",\"lastUpdatedByName\":\"李泽奎10270446\",\"lastUpdatedDate\":\"2022-07-06 15:24:26\",\"enabledFlag\":\"N\",\"enabledFlagName\":\"失效\",\"createDateStart\":null,\"createDateEnd\":null}]",ExceptionSkipInfoDTO.class);
        PowerMockito.when(exceptionSkipInforepository.pageList(any())).thenReturn(exceptionSkipInfolist);
        List<BSProcess> bsProcessList = JSON.parseArray("[{\"controlAddress\":\"\",\"craftSection\":\"DIP\",\"enabledFlag\":\"Y\",\"factoryId\":55,\"lastUpdatedBy\":\"10270446\",\"lastUpdatedDate\":1657040037000,\"processCode\":\"6\",\"processControl\":\"0\",\"processControlGroup\":\"Uni_Scan\",\"processControlGroupName\":\"通用扫描（不含不良扫描）\",\"processId\":\"iMESTEST00000003\",\"processName\":\"短插\",\"processType\":\"手工测试\",\"remark\":\"河源测试，勿删\",\"scanByStation\":1,\"testControl\":\"1\",\"xType\":\"子工序\"}]",BSProcess.class);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(anyObject())).thenReturn(bsProcessList);
        List<PsTask> psTaskList = JSON.parseArray("[{\"createBy\":\"system\",\"createDate\":1655780875000,\"enabledFlag\":\"Y\",\"entityId\":2,\"externalType\":\"DHOME\",\"factoryId\":55,\"firstWarehouseDate\":1655878851000,\"internalType\":\"ONT\",\"itemName\":\"ZXV10 B860AV2D1PR STBAB\",\"itemNo\":\"129580160029AAB\",\"lastDeliveryDate\":1656316349000,\"lastUpdatedBy\":\"00236517\",\"lastUpdatedDate\":1656253251000,\"prodplanId\":\"8888621\",\"prodplanNo\":\"130000206830ABB\",\"releaseDate\":1655711549000,\"sourceSys\":\"STEP\",\"taskId\":\"AAAcsbAAFAAAAS621\",\"taskNo\":\"129580160029AAB\",\"taskQty\":20000,\"taskStatus\":\"已完工\"}]",PsTask.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("10270446", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        Assert.assertNotNull(exceptionSkipInfoService.pageList(exceptionSkipInfoDTO));
    }
}
