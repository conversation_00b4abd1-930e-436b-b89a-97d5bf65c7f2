package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.*;
import com.zte.application.impl.ErpLogServiceImpl;
import com.zte.application.impl.WarehouseInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.RedisUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.StandardAutomaticSubmitWarehouseDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.apache.cxf.endpoint.Client;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021-05-21 15:39
 */
@PrepareForTest({WarehouseInfoServiceImpl.class, CommonUtils.class,BasicsettingRemoteService.class,
        MicroServiceRestUtil.class, HttpRemoteService.class, HttpClientUtil.class, RedisHelper.class, ErpRemoteService.class, PlanscheduleRemoteService.class})
public class ErpLogServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ErpLogServiceImpl erpLogService;
    @Mock
    private ErpLogRepository erpLogRepository;


    @Test
    public void insertErpLogBatch() throws Exception {
        Assert.assertEquals(NumConstant.NUM_ZERO,erpLogService.insertErpLogBatch(null));

        List<ErpLog> list = new ArrayList<>();
        list.add(new ErpLog());
        Assert.assertEquals(NumConstant.NUM_ZERO,erpLogService.insertErpLogBatch(list));
    }


}
