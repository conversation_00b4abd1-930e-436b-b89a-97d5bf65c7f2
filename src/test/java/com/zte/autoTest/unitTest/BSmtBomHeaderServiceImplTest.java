package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.impl.BSmtBomDetailServiceImpl;
import com.zte.application.impl.BSmtBomHeaderServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.BSmtBomHeader;
import com.zte.domain.model.BSmtBomHeaderRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.ItemCheckInfoHead;
import com.zte.domain.model.ItemCheckInfoHeadRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.BSmtBomHeaderDTO;
import com.zte.interfaces.dto.BSmtBomIDTO;
import com.zte.interfaces.dto.BSmtBomInfoDetailDTO;
import com.zte.interfaces.dto.BsBomHierarchicalDetailDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class,
        MicroServiceRestUtil.class, CrafttechRemoteService.class, BasicsettingRemoteService.class, BSmtBomHeaderService.class})
public class BSmtBomHeaderServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    BSmtBomHeaderServiceImpl service;

    @Mock
    BSmtBomHeaderRepository bSmtBomHeaderRepository;

    @Mock
    BSmtBomHeaderService bSmtBomHeaderService;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    ItemCheckInfoHeadRepository itemCheckInfoHeadRepository;

    @Mock
    PkCodeHistoryService pkCodeHistoryService;

    @Mock
    SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;

    @Mock
    SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Mock
    BSmtBomDetailService bSmtBomDetailService;

    @Test
    public void processingMaterialLoadingTableInformation() throws Exception{
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO());
        try {
            service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setCfgHeaderId("2");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        try {
            service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setCfgHeaderId("2");setLineCode("lineCode");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");}});

        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareListByWorkOrder(any())).thenReturn(0);
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");setCopyLoadingInformation(true);}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareListByWorkOrder(any())).thenReturn(1);
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(any())).thenReturn(null);
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");setCopyLoadingInformation(true);}});
        List<BSmtBomDetail> dList = new ArrayList<>();
        dList.add(new BSmtBomDetail(){{setMaterialTray("reelid");}});
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(any())).thenReturn(dList);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SmtLocationInfoDTO> smtLocationInfoDTOList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any())).thenReturn(smtLocationInfoDTOList);
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");setCopyLoadingInformation(true);}});

        smtLocationInfoDTOList.clear();
        smtLocationInfoDTOList.add(new SmtLocationInfoDTO());
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any())).thenReturn(smtLocationInfoDTOList);
        dList.clear();
        dList.add(new BSmtBomDetail(){{setMaterialTray("");}});
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(any())).thenReturn(dList);
        service.processingMaterialLoadingTableInformation(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");setCopyLoadingInformation(true);}});

        List<BSmtBomHeaderDTO> dtos = new ArrayList<>();
        dtos.add(new BSmtBomHeaderDTO(){{setLineCode("lineCode");setCfgHeaderId("headId");setCopyLoadingInformation(true);}});
        service.processingMaterialLoadingTableInformationBatch(dtos);

    }

    @Test
    public void isNotScanFlag()throws Exception{
        List<BSmtBomDetail> dList=new ArrayList<>();
        dList.add(new BSmtBomDetail());
        dList.add(new BSmtBomDetail(){{setMachineNo("1");setModuleNo("1");setLocationNo("1");}});
        dList.add(new BSmtBomDetail(){{setMachineNo("1");setModuleNo("2");setLocationNo("1");}});
        dList.add(new BSmtBomDetail(){{setMaterialTray("2");}});
        dList.add(new BSmtBomDetail(){{setMaterialTray("3");}});
        List<String> trayList = new ArrayList<>();
        trayList.add("1");
        trayList.add("1_1_1");
        service.isNotScanFlag(dList,trayList);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationByLineCodeAndLocationType(any(),any())).thenReturn(null);
        service.getListOfPalletStations(new BSmtBomHeaderDTO());
        List<SmtLocationInfoDTO> smtLocationInfoDTOS = new ArrayList<>();
        smtLocationInfoDTOS.add(new SmtLocationInfoDTO());
        trayList.add("1_1_1");
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationByLineCodeAndLocationType(any(),any())).thenReturn(smtLocationInfoDTOS);
        Assert.assertNotNull(service.getListOfPalletStations(new BSmtBomHeaderDTO()));
    }

    @Test
    public void insertMoveToPreparePkHis()throws Exception{
        List<SmtMachineMaterialMouting> matrialMoutingList=new ArrayList<>();
        SmtMachineMaterialMouting materialMouting=new SmtMachineMaterialMouting();
        materialMouting.setObjectId("123");
        matrialMoutingList.add(materialMouting);
        Assert.assertEquals("123", materialMouting.getObjectId());
        service.insertMoveToPreparePkHis(matrialMoutingList);
    }

    @Test
    public void getListFormString()throws Exception {
        Whitebox.invokeMethod(service,"getListFormString","'1'");
        Whitebox.invokeMethod(service,"getListFormString","");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void handlerStyle() {
        service.handlerStyle(Lists.newArrayList(new BsItemInfo(){{
                    setItemNo("1");
                    setAbcType("1");
                    setStyle("1");
                }}),
                new ArrayList(){{
                    add(Lists.newArrayList(new BSmtBomDetail(){{setItemCode("1");}}));
                }});
        new BSmtBomDetailServiceImpl().addStyleDetail(
                Lists.newArrayList(new BSmtBomDetail(){{setItemCode("1");}}),
                Lists.newArrayList(new BsItemInfo(){{
            setItemNo("1");
            setAbcType("1");
            setStyle("1");
        }}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void exportBomDataBoth() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class,
                MicroServiceRestUtil.class, CrafttechRemoteService.class, BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(new com.zte.domain.model.CFLine(){{
            setWarehouseCode("1");
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{
            setLookupMeaning("Y");
        }}));
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(any()))
                .thenReturn(Lists.newArrayList(
                        new CtRouteDetail(){{setCraftSection("SMT-B");}},
                        new CtRouteDetail(){{setCraftSection("SMT-A");}}));
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new BsItemInfo());}}));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(any(), any(), any()))
                .thenReturn(Lists.newArrayList(
                        new PsWorkOrderDTO(){{setCraftSection("SMT-A");}},
                        new PsWorkOrderDTO(){{setCraftSection("SMT-B");}}
                        ));
        PowerMockito.when(ProductionDeliveryRemoteService.getChildPsTask(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasicDTO(){{setItemNo("A");}}));
        PowerMockito.when(centerfactoryRemoteService.selectBatchBomByBomCode(any()))
                .thenReturn(Lists.newArrayList(new BsBomHierarchicalDetailDTO(){{
                    setItemNo("1");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(1));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("2");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(1));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("3");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(5));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("5");
                    setTypeCode("A");setDeliveryProcess("SMT配送");
                    setItemQty(new BigDecimal(5));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("3");
                    setTypeCode("A");setDeliveryProcess("SMT配送");
                    setItemQty(new BigDecimal(10));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("1");
                    setTypeCode("A");setDeliveryProcess("SMT-B");
                    setItemQty(new BigDecimal(1));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("2");
                    setTypeCode("A");setDeliveryProcess("SMT-B");
                    setItemQty(new BigDecimal(3));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("3");
                    setTypeCode("A");setDeliveryProcess("SMT-B");
                    setItemQty(new BigDecimal(5));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("4");
                    setTypeCode("A");setDeliveryProcess("SMT-B");
                    setItemQty(new BigDecimal(10));
                }}));
        PowerMockito.when(centerfactoryRemoteService.getPbNumByProduction(any()))
                .thenReturn(2);
        try {
            service.exportBomData(new BSmtBomIDTO() {{setPcbQty("1");}},
                Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                    setItemCode("1");
                }},new BSmtBomInfoDetailDTO(){{
                    setItemCode("2");
                }}),
                Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                    setItemCode("1");
                }},new BSmtBomInfoDetailDTO(){{
                    setItemCode("3");
                }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.B_SMT_BOM_INFO_EMPTY, e.getMessage());
        }
        PowerMockito.when(bSmtBomDetailRepository.getSmtOtherSideDetail(anyString(), anyString()))
                .thenReturn(Lists.newArrayList(new BSmtBomDetail(){{setItemCode("1");setCfgHeaderId("1");}}));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(any(), any(), any()))
                .thenReturn(Lists.newArrayList(
                        new PsWorkOrderDTO(){{setCraftSection("SMT-A");}}
                ));
        try {
            service.exportBomData(new BSmtBomIDTO() {{setPcbQty("1");}},
                    Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                        setItemCode("1");
                    }},new BSmtBomInfoDetailDTO(){{
                        setItemCode("2");
                    }}),null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.B_SMT_BOM_INFO_EMPTY, e.getMessage());
        }
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(any(), any(), any()))
                .thenReturn(Lists.newArrayList(
                        new PsWorkOrderDTO(){{setCraftSection("SMT-B");}}
                ));
        try{
            service.exportBomData(new BSmtBomIDTO() {{setPcbQty("1");}},
                    Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                        setItemCode("1");
                    }},new BSmtBomInfoDetailDTO(){{
                        setItemCode("2");
                    }}),null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.B_SMT_BOM_INFO_EMPTY, e.getMessage());
        }
    }
    @Test
    public void exportBomDataSingle() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ProductionDeliveryRemoteService.class,
                MicroServiceRestUtil.class, CrafttechRemoteService.class, BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(new com.zte.domain.model.CFLine(){{
            setWarehouseCode("1");
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{
            setLookupMeaning("Y");
        }}));
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(any()))
                .thenReturn(Lists.newArrayList(
                        new CtRouteDetail(){{setCraftSection("SMT-A");}}));
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new BsItemInfo());}}));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(any(), any(), any()))
                .thenReturn(Lists.newArrayList(
                        new PsWorkOrderDTO(){{setCraftSection("SMT-A");}}
                ));
        PowerMockito.when(ProductionDeliveryRemoteService.getChildPsTask(any()))
                .thenReturn(Lists.newArrayList(new PsWorkOrderBasicDTO(){{setItemNo("A");}}));
        PowerMockito.when(bSmtBomDetailRepository.getStartVirtualNo(any()))
                .thenReturn(Lists.newArrayList("1-1","1-5", "1"));
        PowerMockito.when(centerfactoryRemoteService.selectBatchBomByBomCode(any()))
                .thenReturn(Lists.newArrayList(new BsBomHierarchicalDetailDTO(){{
                    setItemNo("1");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(1));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("2");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(1));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("3");
                    setTypeCode("A");setDeliveryProcess("SMT-A");
                    setItemQty(new BigDecimal(5));
                }},new BsBomHierarchicalDetailDTO(){{
                    setItemNo("3");
                    setTypeCode("A");setDeliveryProcess("SMT配送");
                    setItemQty(new BigDecimal(5));
                }}));
        PowerMockito.when(centerfactoryRemoteService.getPbNumByProduction(any()))
                .thenReturn(2);
        service.exportBomData(new BSmtBomIDTO() {{setPcbQty("1");}},
                Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                    setItemCode("1");
                }},new BSmtBomInfoDetailDTO(){{
                    setItemCode("2");
                }}), null);
        PowerMockito.when(bSmtBomDetailRepository.getSmtOtherSideDetail(anyString(), anyString()))
                .thenReturn(Lists.newArrayList(new BSmtBomDetail(){{setItemCode("1");setCfgHeaderId("1");}}));
        service.exportBomData(new BSmtBomIDTO() {{setPcbQty("1");}},
                Lists.newArrayList(new BSmtBomInfoDetailDTO(){{
                    setItemCode("1");
                }},new BSmtBomInfoDetailDTO(){{
                    setItemCode("2");
                }}), null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void deleteBSmtBomHeaderByCfgHeaderIdTest() throws Exception{
        Assert.assertEquals(0,service.deleteBSmtBomHeaderByCfgHeaderId("test"));
    }

    @Test
    public void removeMaterialTableInfoTest() throws Exception{
        service.removeMaterialTableInfo("test");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void importBomTest() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        List<BSmtBomIDTO> bomIDTOList = new ArrayList<>();
        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTO = new ArrayList<>();
        BSmtBomInfoDetailDTO dto = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.add(dto);
        bSmtBomIDTO.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailDTO);
        bomIDTOList.add(bSmtBomIDTO);
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test");
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(cfLine);
        List<PsEntityPlanBasic> list = new ArrayList<>();
        PsEntityPlanBasic entityPlanBasic = new PsEntityPlanBasic();
        entityPlanBasic.setWorkOrderStatus("已完工");
        list.add(entityPlanBasic);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(list);}}));
        try {
            service.importBom(bomIDTOList);
        } catch (Exception e) {
            Assert.assertEquals("已完工", entityPlanBasic.getWorkOrderStatus());
        }
    }

    @Test
    public void importBomTest1() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        List<BSmtBomIDTO> bomIDTOList = new ArrayList<>();
        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailDTO = new ArrayList<>();
        BSmtBomInfoDetailDTO dto = new BSmtBomInfoDetailDTO();
        bSmtBomInfoDetailDTO.add(dto);
        bSmtBomIDTO.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailDTO);
        bomIDTOList.add(bSmtBomIDTO);
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("test");
        PowerMockito.when(BasicsettingRemoteService.getLine(anyString())).thenReturn(cfLine);
        List<PsEntityPlanBasic> list = new ArrayList<>();
        PsEntityPlanBasic entityPlanBasic = new PsEntityPlanBasic();
        entityPlanBasic.setWorkOrderStatus("已提交");
        list.add(entityPlanBasic);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(list);}}));
        try {
            service.importBom(bomIDTOList);
        } catch (Exception e) {
            Assert.assertEquals("已提交", entityPlanBasic.getWorkOrderStatus());
        }
    }

    @Test
    public void handleHistoricalDataTest() throws Exception {
        String beginTime1 = "";
        String endTime1 = "";
        String headerId1 = "";
        try {
            service.handleHistoricalData(beginTime1, endTime1, headerId1);
        } catch (Exception e) {
            Assert.assertEquals("",beginTime1);
        }
        beginTime1 = "2022-03-24 11:00:00";
        try {
            service.handleHistoricalData(beginTime1, endTime1, headerId1);
        } catch (Exception e) {
            Assert.assertEquals("",beginTime1);
        }
        beginTime1 = "";
        endTime1 = "2022-03-24 11:00:00";
        try {
            service.handleHistoricalData(beginTime1, endTime1, headerId1);
        } catch (Exception e) {
            Assert.assertEquals("",beginTime1);
        }
        String beginTime = "2022-03-24 11:00:00";
        String endTime = "2022-05-24 11:00:00";
        String headerId = "sdsd";
        service.handleHistoricalData(beginTime, endTime, headerId);
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader header = new BSmtBomHeader();
        header.setLineCode("test");
        header.setCfgHeaderId("test");
        bSmtBomHeaderList.add(header);
        PowerMockito.when(bSmtBomHeaderRepository.getList(any())).thenReturn(bSmtBomHeaderList);
        List<BSmtBomDetail> details = new ArrayList<>();
        BSmtBomDetail detail = new BSmtBomDetail();
        detail.setLocationNo("test");
        detail.setModuleNo("test");
        detail.setMachineNo("test");
        details.add(detail);
        PowerMockito.when(bSmtBomDetailRepository.getBomDetailPageByIds(anyList(), anyInt(), anyInt())).thenReturn(details);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SmtLocationInfoDTO> locationInfoDTOs = new ArrayList<>();
        SmtLocationInfoDTO dto = new SmtLocationInfoDTO();
        dto.setLocationNo("test");
        dto.setModuleNo("test");
        dto.setMachineNo("test");
        dto.setLocationSn("050203010405");
        locationInfoDTOs.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(),any())).thenReturn(locationInfoDTOs);
        PowerMockito.mockStatic(BSmtBomHeaderService.class);
        service.handleHistoricalData(beginTime, endTime, headerId);
    }

    @Test
    public void handleHistoricalDataMonthlyTest() throws Exception {
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setLineCode("test");
        bSmtBomHeader.setCfgHeaderId("test");
        bSmtBomHeaderList.add(bSmtBomHeader);
        bSmtBomHeaderList.add(bSmtBomHeader);
        List<BSmtBomDetail> details = new ArrayList<>();
        BSmtBomDetail detail = new BSmtBomDetail();
        detail.setLocationNo("test");
        detail.setModuleNo("test");
        detail.setMachineNo("test");
        details.add(detail);
        PowerMockito.when(bSmtBomDetailRepository.getBomDetailPageByIds(anyList(), anyInt(), anyInt())).thenReturn(details);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SmtLocationInfoDTO> locationInfoDTOs = new ArrayList<>();
        SmtLocationInfoDTO dto = new SmtLocationInfoDTO();
        dto.setLocationNo("test");
        dto.setModuleNo("test");
        dto.setMachineNo("test");
        dto.setLocationSn("050203010405");
        locationInfoDTOs.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(),any())).thenReturn(locationInfoDTOs);
        Assert.assertEquals("test",dto.getLocationNo());
        service.handleHistoricalDataMonthly(bSmtBomHeaderList);
    }
    @Test
    public void getAttr1ByTime() {
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setLineCode("test");
        bSmtBomHeaderList.add(bSmtBomHeader);
        PowerMockito.when(bSmtBomHeaderRepository.getAttr1ByTime(Mockito.anyString())).thenReturn(bSmtBomHeaderList);
        Assert.assertNotNull(service.getAttr1ByTime("2022-06-14 00:00:00"));
    }
    @Test
    public void isRecurCheck() throws Exception {
        ItemCheckInfoHead itemCheckInfoHead = new ItemCheckInfoHead();
        itemCheckInfoHead.setCfgHeaderIdA("123");
        PowerMockito.when(bSmtBomHeaderRepository.getCountByAttr1(Mockito.any())).thenReturn(1);
        PowerMockito.when(itemCheckInfoHeadRepository.getEntityByProdPlanId(Mockito.any())).thenReturn(itemCheckInfoHead);
        Assert.assertTrue(service.isRecurCheck("123"));
    }
    /* Started by AICoder, pid:6799b3bcf3x829f147440a99e077552d0f22908f */
    @Test
    public void handlerMBomTest() throws Exception {
        List<String> productCodes = new ArrayList<>();
        String prodplanId ="test";
        Object handlerMBom = Whitebox.invokeMethod(service, "handlerMBom", productCodes, prodplanId);
        List<String> result = (List<String>)handlerMBom;
        Assert.assertTrue(result.size() == 0);
        prodplanId ="";
        productCodes.add("test");
        Object handlerMBom1 = Whitebox.invokeMethod(service, "handlerMBom", productCodes, prodplanId);
        List<String> result1 = (List<String>)handlerMBom1;
        Assert.assertTrue(result1.size() == 1);
        List<String> resultList = new ArrayList<>();
        resultList.add("test");
        resultList.add("test");
        PowerMockito.when(centerfactoryRemoteService.convertAndIntegrateMBom(Mockito.anyList(), Mockito.anyString()))
                .thenReturn(resultList);
        prodplanId ="test";
        Object handlerMBom2 = Whitebox.invokeMethod(service, "handlerMBom", productCodes, prodplanId);
        List<String> result2 = (List<String>)handlerMBom2;
        Assert.assertTrue(result2.size() == 2);
    }
    /* Ended by AICoder, pid:6799b3bcf3x829f147440a99e077552d0f22908f */

    @Test
    public void getQtyByProdPlanIdAndItemCode() {
        Assert.assertEquals(new ArrayList<>(), service.getQtyByProdPlanIdAndItemCode(new BSmtBomHeaderDTO()));
    }
}