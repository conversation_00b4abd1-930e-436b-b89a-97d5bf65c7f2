package com.zte.autoTest.unitTest;

import com.zte.application.PsWipInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.WipScanHistoryService;
import com.zte.application.impl.CustomerDataUploadServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodePriceApiDTO;
import com.zte.interfaces.dto.CompleteMachineHeadDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerDataUploadDTO;
import com.zte.interfaces.dto.CustomerDataUploadParamDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.MaterialsDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.interfaces.dto.PsTaskDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;

/**
 * <AUTHOR>
 * @date 2023/5/10
 */
@PrepareForTest({ConstantInterface.class, DatawbRemoteService.class,SpringContextUtil.class,
        BasicsettingRemoteService.class, RequestHeadValidationUtil.class,PlanscheduleRemoteService.class})
public class CustomerDataUploadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private CustomerDataUploadServiceImpl service;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Mock
    private PsWipInfoService wipInfoService;
    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;
    @Mock
    private WipScanHistoryService wipScanHistoryService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    private ICenterRemoteService iCenterRemoteService;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.field(CustomerDataUploadServiceImpl.class, "alibabaManufacturerName")
                .set(service, "ZTE101");
        Map<String, String> stationNameMap = new HashMap<>();
        stationNameMap.put("ICT", "ICT");
        stationNameMap.put("Test", "FT");
        PowerMockito.field(CustomerDataUploadServiceImpl.class, "stationNameMap")
                .set(service, stationNameMap);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno()).thenReturn(Pair.of("54", "102"));
        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
    }

    /* Started by AICoder, pid:m6c09938c0i7c2014c59096ea10d6f325f159196 */
    @Test
    public void customerTestAlibabaFeedback() throws Exception{
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setRows(10);
        List<SysLookupValuesDTO> sysLookupValues = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            SysLookupValuesDTO a1 = new SysLookupValuesDTO();
            a1.setAttribute1("正常维修完成");
            a1.setLookupMeaning("ok2");
            sysLookupValues.add(a1);
            SysLookupValuesDTO a2 = new SysLookupValuesDTO();
            a2.setAttribute1("良品");
            a2.setLookupMeaning("ok2");
            sysLookupValues.add(a2);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValues(Mockito.any()))
                .thenReturn(sysLookupValues);
        service.customerTestAlibabaFeedback(dto);

        List<PushBoardDataProcessDTO> list = new LinkedList<>();
        List<WipScanHistory> historyList = new LinkedList<>();
        List<String> boundList = new LinkedList<>();
        List<PmRepairDetailDTO> repairList = new LinkedList<>();
        for (int i = 0; i < 8; i++) {
            PushBoardDataProcessDTO a1 = new PushBoardDataProcessDTO();
            a1.setSn(i + "");
            if (i / 2 == 0) {
                a1.setBusinessType("ICT");
            } else {
                a1.setBusinessType("Test");
            }
            a1.setProdplanId("1");
            boundList.add(a1.getSn());
            list.add(a1);
            WipScanHistory b1 = new WipScanHistory();
            b1.setSn(i+"");
            b1.setCreateDate(new Date());
            b1.setCraftSection("Test");
            historyList.add(b1);
            if(i==3){
                PmRepairDetailDTO c1 = new PmRepairDetailDTO();
                c1.setSn(i+"");
                c1.setCraftSection(a1.getBusinessType());
                repairList.add(c1);
            }

        }
        PowerMockito.when(centerfactoryRemoteService.queryPushDataList(any())).thenReturn(list);
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }

        List<PsWorkOrderBasic> workBasicByTask = new LinkedList<>();
        PsWorkOrderBasic a1 = new PsWorkOrderBasic();
        a1.setCraftSection("Test");
        a1.setItemNo("123457689123ACB");
        a1.setSourceTask("1");
        workBasicByTask.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkBasicByTask(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(workBasicByTask);
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND, e.getMessage());
        }

        a1.setCraftSection("GT");
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND, e.getMessage());
        }

        a1.setCraftSection("Test");
        PowerMockito.when(psWipInfoRepository.selectInboundSnListBatch(Mockito.any()))
                .thenReturn(Arrays.asList("333"));
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND, e.getMessage());
        }

        PowerMockito.when(psWipInfoRepository.selectInboundSnListBatch(Mockito.any()))
                .thenReturn(boundList);
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_INFO_LOST, e.getMessage());
        }

        PowerMockito.when(wipScanHistoryRepository.getWipScanHistoryListBySnAndCraft(Mockito.anyList(),Mockito.any()))
                .thenReturn(historyList);

        List<CustomerItemsDTO> itemsDTOList = new LinkedList<>();
        CustomerItemsDTO c1 = new CustomerItemsDTO();
        itemsDTOList.add(c1);
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(Mockito.any()))
                .thenReturn(itemsDTOList);
        List<Map> maps = new LinkedList<>();
        Map<String, String> map = new HashMap<>();
        map.put("fpn","444");
        maps.add(map);
        PowerMockito.when(mdsRemoteService.queryCrossInfo(Mockito.anyList())).thenReturn(maps);
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_INFO_LOST, e.getMessage());
        }


        PowerMockito.when(pmRepairRcvRepository.querySnRepairMaxBatch(Mockito.anyList(), Mockito.any()))
                .thenReturn(repairList);
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_INFO_LOST, e.getMessage());
        }

        repairList.forEach(item -> item.setResult("良品3"));
        try {
            service.customerTestAlibabaFeedback(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_INFO_LOST, e.getMessage());
        }

        List<PushBoardDataProcessDTO> updateList = new LinkedList<>();
        for (int i = 0; i <4; i++) {
            PushBoardDataProcessDTO a3 = new PushBoardDataProcessDTO();
            a3.setErrorMsg("44");
            updateList.add(a3);
        }
        Whitebox.invokeMethod(service, "sendCenterMsg", updateList);
    }
    /* Ended by AICoder, pid:m6c09938c0i7c2014c59096ea10d6f325f159196 */

    @Test
    public void setLocation() throws Exception {
        Map<String, Integer> sortMap = new HashMap<>();
        service.setLocation(sortMap,new MaterialsDTO(){});
        service.setLocation(sortMap,new MaterialsDTO(){{setLocation("2");setMaterialsType("23");}});
        sortMap.put("23",1);
        service.setLocation(sortMap,new MaterialsDTO(){{setLocation("");setMaterialsType("23");}});
        service.setLocation(sortMap,new MaterialsDTO(){{setLocation("");setMaterialsType("223");}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void getWipMap() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setCurrProcessCode("P0003");
        psWipInfo2.setSn("77788890002");
        psWipInfoList.add(psWipInfo2);
        List<String> snList = new ArrayList<>();
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setSn("77788890002");}});
        PowerMockito.when(wipExtendIdentificationService.getAllChildSn(any())).thenReturn(null);
        service.getWipMap(null,snList);

        psWipInfo2.setItemNo("77788890002");
        PowerMockito.when(wipExtendIdentificationService.getAllChildSn(any())).thenReturn(wipExtendIdentificationList);
        service.getWipMap(psWipInfoList,snList);
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setSn("77788890002");setItemNo("77788890002");}});
        PowerMockito.when(wipExtendIdentificationService.getAllChildSn(any())).thenReturn(wipExtendIdentificationList);
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123123123");
        bsItemInfo.setItemType("0");
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("234234234");
        bsItemInfo1.setItemType("1");
        itemInfoList.add(bsItemInfo);
        itemInfoList.add(bsItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.getItemType((Mockito.anyList()))).thenReturn(null);

        try {
            service.getWipMap(psWipInfoList,snList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_INFO_FIND_ERROR, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getItemType((Mockito.anyList()))).thenReturn(itemInfoList);
        service.getWipMap(psWipInfoList,snList);
    }
    @Test
    public void getMaterialsDTOList() throws Exception {

        List<BarcodePriceApiDTO> barcodePriceApiDTOS = new ArrayList<>();
        BarcodePriceApiDTO barcodePriceApiDTO = new BarcodePriceApiDTO();
        barcodePriceApiDTO.setQty("2");
        barcodePriceApiDTOS.add(barcodePriceApiDTO);

        BarcodePriceApiDTO barcodePriceApiDTO1 = new BarcodePriceApiDTO();
        barcodePriceApiDTO1.setQty("2");
        barcodePriceApiDTO1.setPositionExt("1");
        barcodePriceApiDTO1.setPositionExt1("1");
        barcodePriceApiDTO1.setPositionExt2("1");
        barcodePriceApiDTO1.setPositionExt3("1");
        barcodePriceApiDTO1.setPositionExt6("1");
        barcodePriceApiDTO1.setPositionExt5("1");
        barcodePriceApiDTO1.setPositionExt4("1");
        barcodePriceApiDTOS.add(barcodePriceApiDTO1);

        BarcodePriceApiDTO barcodePriceApiDTO2 = new BarcodePriceApiDTO();
        barcodePriceApiDTO2.setQty(null);
        barcodePriceApiDTO2.setPositionExt("1");
        barcodePriceApiDTOS.add(barcodePriceApiDTO2);

        BarcodePriceApiDTO barcodePriceApiDTO3 = new BarcodePriceApiDTO();
        barcodePriceApiDTO3.setQty("-1");
        barcodePriceApiDTO3.setPositionExt("1");
        barcodePriceApiDTOS.add(barcodePriceApiDTO3);


        Map<String, BarcodeExpandDTO> barcodeExpandDTOMap = new HashMap<>();
        barcodeExpandDTOMap.put("1",new BarcodeExpandDTO());
        service.getMaterialsDTOList(barcodePriceApiDTOS,barcodeExpandDTOMap);

        barcodePriceApiDTOS = new ArrayList<>();
        BarcodePriceApiDTO barcodePriceApiDTO9 = new BarcodePriceApiDTO();
        barcodePriceApiDTOS.add(barcodePriceApiDTO9);
        Assert.assertNotNull(service.getMaterialsDTOList(barcodePriceApiDTOS,barcodeExpandDTOMap));

    }
    @Test
    public void assembleAndReturnInformationBasedOnBarcode() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setSn("77788890002");
        customerDataLogDTOList.add(customerDataLogDTO);
        customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);

        List<String> snList = new ArrayList<>();
        snList.add("777888900001");
        customerDataUploadParamDTO.setBoardSnList(snList);
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setCurrProcessCode("P0003");
        psWipInfo2.setSn("77788890002");
        psWipInfoList.add(psWipInfo2);
        PowerMockito.when(wipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);

        psWipInfo2 = new PsWipInfo();
        psWipInfo2.setAttribute1("7778889");
        psWipInfo2.setSn("77788890002");
        psWipInfoList.add(psWipInfo2);
        PowerMockito.when(wipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);

        PsTaskDTO dto = new PsTaskDTO();
        dto.setProdplanId("2132131");
        dto.setFactoryId(new BigDecimal("52"));
        List<PsTaskDTO> psTaskDTOs = new LinkedList<>();
        psTaskDTOs.add(dto);
        PowerMockito.when(centerfactoryRemoteService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskDTOs);
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);

        List<CustomerItemsDTO> customerItemsDTOList =new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(),any())).thenReturn(customerItemsDTOList);
        Assert.assertEquals("2132131", dto.getProdplanId());
        service.assembleAndReturnInformationBasedOnBarcode(customerDataUploadParamDTO);
    }

    @Test
    public void customerDataFeedback() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
        customerDataUploadParamDTO.setSchedulingType("0");
        service.customerDataFeedback(customerDataUploadParamDTO);
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setSn("77788890002");
        customerDataLogDTOList.add(customerDataLogDTO);
        customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
        service.customerDataFeedback(customerDataUploadParamDTO);
        customerDataUploadParamDTO.setSchedulingType("2");
        List<CustomerDataUploadDTO> customerDataUploadDTOList = new ArrayList<>();
        CustomerDataUploadDTO customerDataUploadDTO = new CustomerDataUploadDTO();
        customerDataUploadDTOList.add(customerDataUploadDTO);
        customerDataUploadParamDTO.setSchedulingType("2");
        customerDataUploadParamDTO.setCustomerDataUploadDTOList(customerDataUploadDTOList);

        service.customerDataFeedback(customerDataUploadParamDTO);
        customerDataUploadDTOList = new ArrayList<>();
        customerDataUploadDTO = new CustomerDataUploadDTO();
        customerDataUploadDTO.setBoardSn("77788890002");
        customerDataUploadDTOList.add(customerDataUploadDTO);
        customerDataUploadParamDTO.setSchedulingType("2");
        customerDataUploadParamDTO.setCustomerDataUploadDTOList(customerDataUploadDTOList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setCurrProcessCode("P0003");
        psWipInfo2.setSn("77788890002");
        psWipInfoList.add(psWipInfo2);
        PowerMockito.when(wipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);

        service.customerDataFeedback(customerDataUploadParamDTO);
        customerDataUploadParamDTO.setSchedulingType("1");
        customerDataUploadParamDTO.setCustomerDataLogDTOList(null);
        try {
            service.customerDataFeedback(customerDataUploadParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupTypesDTO> sysLookUpValueList = new LinkedList<>();
        SysLookupTypesDTO b1 = new SysLookupTypesDTO();
        b1.setLookupCode(new BigDecimal("1100"));
        sysLookUpValueList.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(b1);
        List<CustomerItemsDTO> customerItemsDTOList =new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(),any())).thenReturn(customerItemsDTOList);
        service.customerDataFeedback(customerDataUploadParamDTO);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(),any())).thenReturn(customerItemsDTOList);
        service.customerDataFeedback(customerDataUploadParamDTO);
        customerItemsDTOList =new ArrayList<>();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getListByItemNoList(any(),any())).thenReturn(customerItemsDTOList);
        service.customerDataFeedback(customerDataUploadParamDTO);
        PsTaskDTO dto = new PsTaskDTO();
        dto.setProdplanId("2132131");
        dto.setFactoryId(new BigDecimal("52"));
        List<PsTaskDTO> psTaskDTOs = new LinkedList<>();
        psTaskDTOs.add(dto);
        service.customerDataFeedback(customerDataUploadParamDTO);
        b1.setLookupMeaning("2022-04-04 01:01:00");
        sysLookUpValueList.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(b1);
        PowerMockito.when(centerfactoryRemoteService.getPsTaskByItemNoList(any())).thenReturn(psTaskDTOs);
        service.customerDataFeedback(customerDataUploadParamDTO);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setRouteId("routeId");
        psWipInfo.setSn("77788890001");
        psWipInfo.setItemNo("itemNo");
        psWipInfo.setAttribute1("7778889");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        psWipInfos.add(psWipInfo);
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setRouteId("routeId");
        psWipInfo1.setSn("77788890002");
        psWipInfo1.setItemNo("itemNo");
        psWipInfo1.setAttribute1("7778889");
        psWipInfo1.setCraftSection("入库");
        psWipInfos.add(psWipInfo1);
        PowerMockito.when(wipInfoService.getWipInfoByProdPlanIdAndLastUpdateDate(anyList(),any(),any(),anyInt())).thenReturn(new ArrayList<>());
        service.customerDataFeedback(customerDataUploadParamDTO);
        Map<String, PsTaskDTO> psTaskMap= new HashMap<>();
        psTaskMap.put("7778889",dto);
        List<String> tempProdPlanIdList = new ArrayList<>();
        tempProdPlanIdList.add("7778889");
        Map<String, CustomerItemsDTO> customerItemsDTOMap = new HashMap<>();
        customerItemsDTOMap.put("itemNo",customerItemsDTO);

        Whitebox.invokeMethod(service,"assemblyReturnData",customerDataUploadParamDTO,psTaskMap,tempProdPlanIdList,psWipInfos,customerItemsDTOMap);
        List<WipScanHistory> statDataList = new ArrayList<WipScanHistory>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setAttribute1("7777666");
        wipScanHistory.setCreateDate(new Date());
        wipScanHistory.setSn("77788890002");
        statDataList.add(wipScanHistory);
        PowerMockito.when(wipScanHistoryService.getTheEarliestInboundScanningTime(any())).thenReturn(statDataList);
        customerDataUploadParamDTO.setSchedulingType("0");
        customerDataUploadParamDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
        List<CompleteMachineHeadDTO> completeMachineHeadDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getMainBoardInfo(any())).thenReturn(completeMachineHeadDTOList);
        Whitebox.invokeMethod(service,"assemblyReturnData",customerDataUploadParamDTO,psTaskMap,tempProdPlanIdList,psWipInfos,customerItemsDTOMap);
        CompleteMachineHeadDTO completeMachineHeadDTO = new CompleteMachineHeadDTO();
        completeMachineHeadDTO.setSn("77788890002");
        completeMachineHeadDTO.setOrderId("77788890002");
        completeMachineHeadDTO.setManufacturerTime(new Date());
        completeMachineHeadDTO.setOrderTime(new Date());
        completeMachineHeadDTOList.add(completeMachineHeadDTO);
        PowerMockito.when(datawbRemoteService.getMainBoardInfo(any())).thenReturn(completeMachineHeadDTOList);

        service.assemblyReturnData(customerDataUploadParamDTO,psTaskMap,tempProdPlanIdList,psWipInfos,customerItemsDTOMap);

        Whitebox.invokeMethod(service, "beforeLastExecuteDate", null);
    }


    @Test
    public void setManufacturerTimeAndOrderTime() throws Exception {
        CustomerDataUploadParamDTO customerDataUploadParamDTO = new CustomerDataUploadParamDTO();
        customerDataUploadParamDTO.setSchedulingType("2");
        CompleteMachineHeadDTO completeMachineHeadDTO = new CompleteMachineHeadDTO();
        completeMachineHeadDTO.setSn("77788890002");
        completeMachineHeadDTO.setOrderId("77788890002");
        completeMachineHeadDTO.setManufacturerTime(new Date());
        completeMachineHeadDTO.setOrderTime(new Date());
        Map<String, CustomerDataUploadDTO> customerItemsDTOMap = new HashMap<>();

        CustomerDataUploadDTO customerDataUploadDTO = new CustomerDataUploadDTO();
        customerDataUploadParamDTO.setSchedulingType("2");

        Map<String, CompleteMachineHeadDTO> completeMachineHeadDTOMap = new HashMap<>();
        completeMachineHeadDTOMap.put("77788890002",completeMachineHeadDTO);
        service.setManufacturerTimeAndOrderTime(customerDataUploadParamDTO,customerItemsDTOMap,
                new PsWipInfo(){{setSn("77788890002");}},customerDataUploadDTO,completeMachineHeadDTOMap);

        customerItemsDTOMap.put("77788890002",customerDataUploadDTO);
        Assert.assertEquals("77788890002", completeMachineHeadDTO.getSn());
        service.setManufacturerTimeAndOrderTime(customerDataUploadParamDTO,customerItemsDTOMap,
                new PsWipInfo(){{setSn("77788890002");}},customerDataUploadDTO,completeMachineHeadDTOMap);
    }

}
