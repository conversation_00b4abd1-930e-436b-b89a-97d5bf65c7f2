package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.BProdBomService;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.WorkorderOnlineService;
import com.zte.application.impl.PsScanHistoryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.domain.model.WorkorderOnline;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2022-03-05 20:35
 */
@PrepareForTest({MicroServiceRestUtil.class, ObtainRemoteServiceDataUtil.class, CommonUtils.class
, CrafttechRemoteService.class,PlanscheduleRemoteService.class})
public class PsScanHistoryServiceImplTest extends PowerBaseTestCase {
    @Mock
    HttpServletResponse response;
    @InjectMocks
    private PsScanHistoryServiceImpl psScanHistoryServiceImpl;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WorkorderOnlineService workorderOnlineService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private WipScanHisExtraRepository wipScanHisExtraRepository;
    @Mock
    private BProdBomService bProdBomService;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void getPageScanHistoryList() throws Exception{

        List<PsScanHistory> list = new LinkedList<>();
        PsScanHistory a1 = new PsScanHistory();
        list.add(a1);
        PowerMockito.when(psScanHistoryRepository.getPageScanHistoryList(Mockito.any()))
                .thenReturn(list);
        PsScanHistoryDTO dto = new PsScanHistoryDTO();
        List<PsScanHistory> pageScanHistoryList = psScanHistoryServiceImpl.getPageScanHistoryList(dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(pageScanHistoryList));

        PowerMockito.when(CrafttechRemoteService.getProcessAll()).thenReturn(null);
        dto.setWorkOrderNo("777-");
        dto.setLastUpdatedDate(new Date());
        pageScanHistoryList = psScanHistoryServiceImpl.getPageScanHistoryList(dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(pageScanHistoryList));

        dto.setLastUpdatedDate(null);
        pageScanHistoryList = psScanHistoryServiceImpl.getPageScanHistoryList(dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(pageScanHistoryList));

        List<PsTask> taskList = new LinkedList<>();
        PsTask b1 = new PsTask();
        taskList.add(b1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(Mockito.any()))
                .thenReturn(taskList);
        pageScanHistoryList = psScanHistoryServiceImpl.getPageScanHistoryList(dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(pageScanHistoryList));

        b1.setCreateDate(new Date());
        pageScanHistoryList = psScanHistoryServiceImpl.getPageScanHistoryList(dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(pageScanHistoryList));

    }

    @Test
    public void setSMTScanParamDTO()throws Exception {
        List<EmEqpPdcountDTO> spiList = new ArrayList<>();
        spiList.add(new EmEqpPdcountDTO());
        psScanHistoryServiceImpl.setSMTScanParamDTO(1,2,spiList,new ArrayList<>(), new SMTScanParamDTO());

        psScanHistoryServiceImpl.getSmtScanParamDTO(new SMTScanDTO(),new PsWorkOrderDTO(),"",new Date(),new ArrayList<>());

        Assert.assertNotNull(psScanHistoryServiceImpl.getSmtScanParamDTO(new SMTScanDTO(),new Date(),new ArrayList<>()));
    }
    @Test
    public void getScanHistoryListByCondition()throws Exception {
        SMTScanDTO dto = new SMTScanDTO();
        dto.setSn("7778889000001");
        dto.setPassNum(5);
        dto.setFactoryId(new BigDecimal(55));
        dto.setIsPass(1);
        dto.setCurDate(new Date());
        dto.setPcbQty(1);
        dto.setWorkOrderNo("77788999-DIP5598");
        PsWorkOrderDTO psWorkOrder = new PsWorkOrderDTO();
        psWorkOrder.setProcessGroup("2");
        psWorkOrder.setCfgHeaderId("2");
        psWorkOrder.setItemNo("2");
        psWorkOrder.setSourceTask("2");
        psWorkOrder.setWorkshopCode("2");
        psWorkOrder.setProcessCode("2");

        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        List<PsScanHistory> pmjList  = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(spiList);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));
        try {
            psScanHistoryServiceImpl.getScanHistoryListByCondition(dto,"2","2", pmjList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MOUNTING_DETAIL,e.getExCode());
        }
        PsScanHistory psScanHistory = new PsScanHistory();
        pmjList.add(psScanHistory);
        try {
            psScanHistoryServiceImpl.getScanHistoryListByCondition(dto,"2","2", pmjList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MOUNTING_DETAIL,e.getExCode());
        }
    }
    @Test
    public void spiCallScanNum()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        SMTScanDTO dto = new SMTScanDTO();
        dto.setModuleNo("2");
        dto.setIsEndBoard("1");
        dto.setSn("7778889000001");
        dto.setIsPass(1);
        dto.setPcbQty(1);
        dto.setWorkOrderNo("77788999-DIP5598");
        dto.setLineCode("2");
        List<CFLine> list = new LinkedList<>();
        list.add(new CFLine(){{setTracingCalPoint("SPI");}});
        List<CFLine> listLine = new ArrayList<>();
        CFLine cFLine = new CFLine();
        cFLine.setLineCode("SMT-4");
        cFLine.setTraceSource("PMJ");
        cFLine.setCalInSmt(Constant.FLAG_Y);
        cFLine.setCalInModule(Constant.FLAG_Y);
        cFLine.setTracingCalPoint("SMT");
        listLine.add(cFLine);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLine(any())).thenReturn(listLine);
        try{psScanHistoryServiceImpl.spiCallScanNum(list,dto);}catch (Exception e){}
        try{ psScanHistoryServiceImpl.spiCallScanNum(new ArrayList<>(),dto);}catch (Exception e){}
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }
    @Test
    public void dealWithScanHistoryList()throws Exception {
        SMTScanDTO dto = new SMTScanDTO();
        dto.setSn("7778889000001");
        dto.setPassNum(5);
        dto.setFactoryId(new BigDecimal(55));
        dto.setIsPass(1);
        dto.setCurDate(new Date());
        dto.setPcbQty(1);
        dto.setWorkOrderNo("77788999-DIP5598");
        PsWorkOrderDTO psWorkOrder = new PsWorkOrderDTO();
        psWorkOrder.setProcessGroup("2");
        psWorkOrder.setCfgHeaderId("2");
        psWorkOrder.setItemNo("2");
        psWorkOrder.setSourceTask("2");
        psWorkOrder.setWorkshopCode("2");
        psWorkOrder.setProcessCode("2");

        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        List<PsScanHistory> pmjList  = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(spiList);
        String workStation = "22";
        String factoryId = "55";

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));
        try {
            psScanHistoryServiceImpl.dealWithScanHistoryList(dto,psWorkOrder,1, pmjList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MOUNTING_DETAIL,e.getExCode());
        }
        PsScanHistory psScanHistory = new PsScanHistory();
        pmjList.add(psScanHistory);
        try {
            psScanHistoryServiceImpl.dealWithScanHistoryList(dto,psWorkOrder,1, pmjList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MOUNTING_DETAIL,e.getExCode());
        }
    }
    @Test
    public void excuteSpiInfo()throws Exception {
        SMTScanDTO dto = new SMTScanDTO();
        dto.setSn("7778889000001");
        dto.setIsPass(1);
        dto.setPcbQty(1);
        dto.setWorkOrderNo("77788999-DIP5598");
        PsWorkOrderDTO psWorkOrder = new PsWorkOrderDTO();
        psWorkOrder.setProcessGroup("2");
        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        List<PsScanHistory> pmjList  = new ArrayList<>();
        psScanHistoryServiceImpl.excuteSpiInfo(dto,psWorkOrder,spiList,pmjList);
        PsScanHistory psScanHistory = new PsScanHistory();
        pmjList.add(psScanHistory);
        psScanHistoryServiceImpl.excuteSpiInfo(dto,psWorkOrder,spiList,pmjList);

        EmEqpSpiBoardDTO emEqpSpiBoardDTO = new EmEqpSpiBoardDTO();
        emEqpSpiBoardDTO.setArrayCount(1);
        spiList.add(emEqpSpiBoardDTO);
        Assert.assertFalse(psScanHistoryServiceImpl.excuteSpiInfo(dto,psWorkOrder,spiList,new ArrayList<>()));
    }
    @Test
    public void getStringObjectMap()throws Exception {
        psScanHistoryServiceImpl.getStringObjectMap("2");
        Assert.assertNotNull(psScanHistoryServiceImpl.getParamMap("2","23"));
    }

    @Test
    public void getListRangelastUpDate()throws Exception {
        Page<CFLine> page = new Page<>();
        List<CFLine> list = new LinkedList<>();
        list.add(new CFLine());
        page.setRows(list);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(page);
        String workStation = "22";
        String factoryId = "55";

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));

        ProductInfoQueryIntegrateRequest productInfoQueryIntegrateRequest = new ProductInfoQueryIntegrateRequest();
        productInfoQueryIntegrateRequest.setStartTime("2022-07-03 11:10:00");
        productInfoQueryIntegrateRequest.setEndTime("2022-07-03 11:10:00");
        Assert.assertNull(psScanHistoryServiceImpl.getListRangelastUpDate(productInfoQueryIntegrateRequest));
    }
    @Test
    public void insertPsScanHistoryByScan() {
        FlowControlInfoDTO record = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO p = new PsEntityPlanBasicDTO();
        record.setEntityPlanBasic(p);
        List<FlowControlInfoDTO> list = new LinkedList<>();
        FlowControlInfoDTO record1 = new FlowControlInfoDTO();
        list.add(record1);
        record.setProcessInfoList(list);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1,psScanHistoryServiceImpl.insertPsScanHistoryByScan(record));
    }

    @Test
    public void insertPsScanHistoryByScanBatch() {
        List<FlowControlInfoDTO> listFlow = new ArrayList<>();
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO entityPlanBasicDTO=new PsEntityPlanBasicDTO();
        entityPlanBasicDTO.setWorkOrderNo("47415");
        dto.setEntityPlanBasic(entityPlanBasicDTO);
        dto.setOperation("s");
        listFlow.add(dto);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(Mockito.any())).thenReturn(1);
        int num = 1;
        Assert.assertEquals(0,psScanHistoryServiceImpl.insertPsScanHistoryByScanBatch(listFlow,num));
    }

    @Test
    public void getWorkStationInfo() throws Exception {
        List<CtWorkStationsDTO> list = new LinkedList<>();
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(list);
        String workStation = "22";
        String factoryId = "55";

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));
        Assert.assertNotNull(psScanHistoryServiceImpl.getWorkStationInfo(workStation, factoryId));
    }

    @Test
    public void generateEqpInteractiveStartInfo() throws Exception {
        RetCode ret = new RetCode();
        ret.setCode(Constant.SUCCESS_CODE);
        PowerMockito.when(ObtainRemoteServiceDataUtil.insertEmEqpInteractive(Mockito.anyObject()))
                .thenReturn(ret);
        SMTScanDTO smtScanDTO = new SMTScanDTO();
        smtScanDTO.setCurDate(null);
        smtScanDTO.setWorkOrderNo("");
        psScanHistoryServiceImpl.generateEqpInteractiveStartInfo(smtScanDTO, null
                , null, null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void collectFinishContByLineCodes() {
        PsScanHistoryDTO record = new PsScanHistoryDTO();

        Assert.assertNotNull(psScanHistoryServiceImpl.collectFinishContByLineCodes(record));
    }

    @Test
    public void queryProdDetail() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ObtainRemoteServiceDataUtil.class, CommonUtils.class);
        ProductInfoQueryIntegrateRequest busiData = new ProductInfoQueryIntegrateRequest();
        busiData.setPage(1);
        busiData.setCurrentSize(10);
        try {
            psScanHistoryServiceImpl.queryProdDetail(busiData);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.PRODUCT_QUERY_PARAMS_CAN_NOT_BE_NULL.equals(e.getMessage()));
        }
        busiData.setLineCode("SMT1-4");
        busiData.setWorkStation("S222");
        busiData.setTaskNo("7777777");
        try {
            psScanHistoryServiceImpl.queryProdDetail(busiData);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.TIME_CAN_NOT_BE_NULL.equals(e.getMessage()));
        }
        busiData.setStartTime("2021-01-25 12:00:00");
        busiData.setEndTime("2021-02-25 12:00:00");
        try {
            psScanHistoryServiceImpl.queryProdDetail(busiData);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SPAN_WHITIN_7DAYS.equals(e.getMessage()));
        }
        busiData.setEndTime("2021-01-30 12:00:00");
        List<PsScanHistory> hisList = new LinkedList<>();
        PsScanHistory a1 = new PsScanHistory();
        a1.setLastUpdatedDate(new Date());
        a1.setSn("123");
        hisList.add(a1);
        ServiceData serviceData = new ServiceData();
        List<CFLine> listLine = new ArrayList<>();
        CFLine line = new CFLine();
        line.setLineCode("SMT1-4");
        line.setLineName("SMT1-4");
        listLine.add(line);
        Map<String, Object> map = new HashMap<>();
        map.put("rows", listLine);
        serviceData.setBo(map);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(psScanHistoryRepository.getListRangelastUpDate(Mockito.any()))
                .thenReturn(new ArrayList<>());
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any()))
                .thenReturn("123");

        psScanHistoryServiceImpl.queryProdDetail(busiData);
        PowerMockito.when(psScanHistoryRepository.getListRangelastUpDate(Mockito.any()))
                .thenReturn(hisList);
        PowerMockito.when(psScanHistoryRepository.getFirstOrLastProdInfo(Mockito.any()))
                .thenReturn(a1);
        PowerMockito.when(psScanHistoryRepository.getFirstOrLastProdInfo(Mockito.any()))
                .thenReturn(a1);
        psScanHistoryServiceImpl.queryProdDetail(busiData);
        a1.setAttribute1("123");
        PowerMockito.when(psScanHistoryRepository.getListRangelastUpDate(Mockito.any()))
                .thenReturn(hisList);
        psScanHistoryServiceImpl.queryProdDetail(busiData);
        Map<String, String> bomMap = new HashMap<>();
        bomMap.put("1231","222");
        PowerMockito.when(bProdBomService.getBProdBomListBatch(Mockito.anyList())).thenReturn(bomMap);
        psScanHistoryServiceImpl.queryProdDetail(busiData);
        bomMap.put("123","222");
        PowerMockito.when(bProdBomService.getBProdBomListBatch(Mockito.anyList())).thenReturn(bomMap);
        ServiceData<Map<String, Object>> mapServiceData = psScanHistoryServiceImpl.queryProdDetail(busiData);
        Assert.assertNotNull(mapServiceData);
    }

    @Test
    public void setProdProductCode() throws Exception {
        List<ProductInfoQueryIntegrateResponse> proInfoList = new ArrayList<>();
        ProductInfoQueryIntegrateResponse response = new ProductInfoQueryIntegrateResponse();
        response.setTaskNo("111");
        proInfoList.add(response);
        ProductInfoQueryIntegrateResponse response1 = new ProductInfoQueryIntegrateResponse();
        response1.setTaskNo("1231");
        proInfoList.add(response1);
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "setProdProductCode", new ArrayList<>(), proInfoList);
        Assert.assertTrue(StringUtils.isEmpty(response.getProdProductCode()));
        Map<String, String> bomMap = new HashMap<>();
        bomMap.put("1231","222");
        List<String> prodList = new ArrayList(){{add("321");}};
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "setProdProductCode", prodList, proInfoList);
        Assert.assertTrue(StringUtils.isEmpty(response.getProdProductCode()));
        PowerMockito.when(bProdBomService.getBProdBomListBatch(Mockito.anyList())).thenReturn(bomMap);
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "setProdProductCode", prodList, proInfoList);
        Assert.assertEquals("222",response1.getProdProductCode());

    }

    @Test
    public void exportDetail() throws Exception{
        ProductInfoQueryIntegrateRequest busiData = new ProductInfoQueryIntegrateRequest();
        busiData.setPage(1);
        busiData.setCurrentSize(10);
        busiData.setStartTime("2021-01-25 12:00:00");
        busiData.setEndTime("2021-02-25 12:00:00");

        List<PsScanHistory> hisList = new LinkedList<>();
        PsScanHistory a1 = new PsScanHistory();
        a1.setLastUpdatedDate(new Date());
        hisList.add(a1);
        PowerMockito.when(psScanHistoryRepository.getList(Mockito.anyObject()))
                .thenReturn(hisList);

        List<String> itemNoList = new LinkedList<>();
        PowerMockito.when(psScanHistoryRepository.getItemNoList(Mockito.anyMap()))
                .thenReturn(itemNoList);

        ServiceData serviceData = new ServiceData();
        serviceData.setBo(new LinkedList<>());
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(serviceData));
        Assert.assertNotNull(psScanHistoryServiceImpl.exportDetail(busiData, response));
    }

    @Test
    public void dealWithOtherConditionForHisInfo() {

        PsWorkOrderDTO psWorkOrder = new PsWorkOrderDTO();
        psWorkOrder.setProcessGroup("2");
        psWorkOrder.setWorkOrderNo("123456789");

        SMTScanDTO dto = new SMTScanDTO();
        dto.setPcbQty(1);
        dto.setIsPass(1);
        dto.setWorkOrderNo("27778581-DIp5524");
        SpiDataAnalysisDTO spiDataAnalysisDTO = new SpiDataAnalysisDTO();
        spiDataAnalysisDTO.setWorkOrderNo("123456789");
        spiDataAnalysisDTO.setLineCode(null);
        spiDataAnalysisDTO.setIsPass(3);
        spiDataAnalysisDTO.setEntityId(null);
        spiDataAnalysisDTO.setFactoryId(null);
        Assert.assertNotNull(psScanHistoryServiceImpl.dealWithOtherConditionForHisInfo(dto,psWorkOrder));
    }

    @Test
    public void finishContByLineCodes() {
        PsScanHistoryDTO record = new PsScanHistoryDTO();

        Assert.assertNotNull(psScanHistoryServiceImpl.finishContByLineCodes(record));
    }

    @Test
    public void updatePsScanHistoryByScanBatch() {
        List<FlowControlInfoDTO> listFlow = new LinkedList<>();
        FlowControlInfoDTO a1 = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO p1 = new PsEntityPlanBasicDTO();
        a1.setEntityPlanBasic(p1);
        listFlow.add(a1);
        Assert.assertEquals(0,psScanHistoryServiceImpl.updatePsScanHistoryByScanBatch(listFlow, 2));
    }

    @Test
    public void generateAvailableTimedData() throws Exception {
        SMTScanParamDTO scanParamDTO = new SMTScanParamDTO();
        scanParamDTO.setDto(new SMTScanDTO());
        scanParamDTO.setPcbQty(1);
        scanParamDTO.setMounting( new SmtMachineMaterialMouting());
        scanParamDTO.setBomQty( new BigDecimal(1));
        scanParamDTO.setProcessCode("1");
        scanParamDTO.setWorkStation("smt-1");
        scanParamDTO.setWorkshopCode("tt");
        scanParamDTO.setCurDate(new Date());

        Assert.assertNotNull(psScanHistoryServiceImpl.generateAvailableTimedData(new BigDecimal(2), scanParamDTO,
                new BigDecimal(1),  "smt-1", "22"));
    }

    @Test
    public void calAvailableTime() throws Exception {
        Assert.assertNotNull(psScanHistoryServiceImpl.calAvailableTime(new BigDecimal("2"), new BigDecimal("2"), 2, 2));
    }

    @Test
    public void excludeExistOfPmj() {
        List<PsScanHistory> pmjList = new ArrayList<>();
        PsScanHistory dto = new PsScanHistory();
        pmjList.add(dto);
        String workOrderNo = "7777793-SMT-B5301";
        String craftSection = "SMT-B";
        psScanHistoryServiceImpl.excludeExistOfPmj(null, null, null);
        psScanHistoryServiceImpl.excludeExistOfPmj(pmjList, null, null);
        psScanHistoryServiceImpl.excludeExistOfPmj(pmjList, workOrderNo, null);

        psScanHistoryServiceImpl.excludeExistOfPmj(pmjList, workOrderNo, craftSection);

        dto.setSn("777766600001");
        dto.setParentSn("P777766600001");
        List<String> snAndParentSnList = new ArrayList<>();
        snAndParentSnList.add("777766600001P777766600001");
        PowerMockito.when(psScanHistoryRepository.getPassSpiScanHistory(Mockito.any(),
                Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(snAndParentSnList);

        psScanHistoryServiceImpl.excludeExistOfPmj(pmjList, workOrderNo, craftSection);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void excludeExistOfSpi() {
        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        EmEqpSpiBoardDTO dto = new EmEqpSpiBoardDTO();
        spiList.add(dto);
        String workOrderNo = "7777793-SMT-B5301";
        String craftSection = "SMT-B";
        psScanHistoryServiceImpl.excludeExistOfSpi(null, null, null);
        psScanHistoryServiceImpl.excludeExistOfSpi(spiList, null, null);
        psScanHistoryServiceImpl.excludeExistOfSpi(spiList, workOrderNo, null);

        psScanHistoryServiceImpl.excludeExistOfSpi(spiList, workOrderNo, craftSection);

        dto.setBarcode("777766600001");
        dto.setBoardBarcode("P777766600001");
        List<String> snAndParentSnList = new ArrayList<>();
        snAndParentSnList.add("777766600001P777766600001");
        PowerMockito.when(psScanHistoryRepository.getPassSpiScanHistory(Mockito.any(),
                Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(snAndParentSnList);

        psScanHistoryServiceImpl.excludeExistOfSpi(spiList, workOrderNo, craftSection);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void availableTimeCal() throws Exception {
        SMTScanDTO dto = new SMTScanDTO();
        dto.setLineCode("1234");
        dto.setFactoryId(new BigDecimal(2));

        List<WorkorderOnline> onlineList = new LinkedList<>();
        WorkorderOnline a1 = new WorkorderOnline();
        a1.setWorkOrder("123");
        onlineList.add(a1);
        PowerMockito.when(workorderOnlineService.getWorkorderOnline(Mockito.anyObject())).thenReturn(onlineList);

        PsWorkOrderDTO b1 = new PsWorkOrderDTO();
        b1.setWorkOrderNo("123");
        b1.setProcessCode("2");
        b1.setProcessGroup("2");
        b1.setCfgHeaderId("1244");
        b1.setItemNo("23");
        PowerMockito.when(ObtainRemoteServiceDataUtil.getBasicWorkerOrderInfo(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(b1);

        List<BSmtBomDetail> bomDetailList = new LinkedList<>();
        BSmtBomDetail c1 = new BSmtBomDetail();
        bomDetailList.add(c1);
        PowerMockito.when(bSmtBomDetailService.getList(Mockito.anyObject(), Mockito.any(),Mockito.any()))
                .thenReturn(bomDetailList);

        List<SmtMachineMaterialMouting> mountingList = new LinkedList<>();
        SmtMachineMaterialMouting d1 = new SmtMachineMaterialMouting();
        d1.setQty(new BigDecimal(2));
        d1.setObjectId("123");
        d1.setMachineMaterialMoutingId("23");
        d1.setLocationNo("23");
        d1.setNextReelRowid("123");
        mountingList.add(d1);
        PowerMockito.when(smtMachineMaterialMoutingService.selectMoutingWithPkCodeInfo(Mockito.anyMap()))
                .thenReturn(mountingList);

        psScanHistoryServiceImpl.availableTimeCal(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkParentSnTest() throws Exception{
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        List<PsScanHistory> psScanHistoryList = new ArrayList<>();
        psScanHistoryList.add(new PsScanHistory());
        PowerMockito.when(psScanHistoryRepository.getMakeupRelationshipByParentSn(Mockito.anyString())).thenReturn(psScanHistoryList);
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "checkParentSn", dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    /* Started by AICoder, pid:899929c5c162c8814aa10afad02dff22c09255b0 */
    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "setMBom", null);
        Assert.assertTrue(1==1);
        List<PsScanHistoryDTO> list = new ArrayList<>();
        PsScanHistoryDTO entity = new PsScanHistoryDTO();
        entity.setAttribute1("1234567");
        list.add(entity);
        PsScanHistoryDTO entity1 = new PsScanHistoryDTO();
        entity1.setAttribute1("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(psScanHistoryServiceImpl, "setMBom", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertTrue(list.get(1).getMbom().equals("itemNo"));
    }
    /* Ended by AICoder, pid:899929c5c162c8814aa10afad02dff22c09255b0 */
}
