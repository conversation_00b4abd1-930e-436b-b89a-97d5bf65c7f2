package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.PmRepairRcvService;
import com.zte.application.impl.PmRepairScrapServiceImpl;
import com.zte.application.impl.strategy.WRDetailListLeftin;
import com.zte.interfaces.dto.PmRepairScrapDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.WipDailyReportInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
public class PmRepairScrapServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PmRepairScrapServiceImpl pmRepairScrapService;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    PmRepairRcvService pmRepairRcvService;

    @Test
    public void generatePmRepairRcvDetailDTO()throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        Assert.assertNotNull(pmRepairScrapService.generatePmRepairRcvDetailDTO(new PmRepairScrapDTO(),new String("2"),"2","sn"));
    }
    @Test
    public void enterScrapInformationInTheRepairForm()throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        Assert.assertNotNull(pmRepairScrapService.enterScrapInformationInTheRepairForm(new PmRepairScrapDTO(),"2"));
    }
    @Test
    public void getPmRepairRcvDetailDTOS()throws Exception {
        List<String> snList =new ArrayList<>();
        snList.add("2");
        Assert.assertNotNull(pmRepairScrapService.getPmRepairRcvDetailDTOS(new PmRepairScrapDTO(),snList));
    }
}
