package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.ScrapBillDetailService;
import com.zte.application.impl.DocFilePropertiesServiceImpl;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.ScrapBillHeaderServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/6/19
 */
@PrepareForTest({PlanscheduleRemoteService.class,CommonUtils.class,ConstantInterface.class, RedisHelper.class, MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class})
public class ScrapBillHeaderServiceImplTest extends PowerBaseTestCase {

    @Mock
    private ScrapBillHeaderRepository scrapBillHeaderRepository;

    @Mock
    private ScrapBillDetailRepository scrapBillDetailRepository;

    @Mock
    private PsWipInfoServiceImpl psWipInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private PsScanHistoryService scanHistoryService;

    @InjectMocks
    private ScrapBillHeaderServiceImpl scrapBillHeaderServiceImpl;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    /* Started by AICoder, pid:daed0n2b666b1b21491009add14d2d471609222a */
    @Test
    public void getScrapQtyBySourceTask() throws Exception {
        List<String> prodplanIds = new ArrayList<>();
        Assert.assertEquals(scrapBillHeaderServiceImpl.getScrapQtyBySourceTask(prodplanIds).size(),0);
        prodplanIds.add("2");
        List<ScrapBillDetailEntityDTO> batchResults = new ArrayList<>();
        PowerMockito.when(scrapBillDetailRepository.getScrapQtyForWorkOrder(anyList())).thenReturn(batchResults);
        Assert.assertEquals(scrapBillHeaderServiceImpl.getScrapQtyBySourceTask(prodplanIds).size(),0);
        batchResults.add(new ScrapBillDetailEntityDTO());
        PowerMockito.when(scrapBillDetailRepository.getScrapQtyForWorkOrder(anyList())).thenReturn(batchResults);
        Assert.assertEquals(scrapBillHeaderServiceImpl.getScrapQtyBySourceTask(prodplanIds).size(),1);

    }
    @Test
    public void setInputOutputIdentification() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfoList.add(psWipInfo);
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setSn("777888900002");
        psWipInfo1.setCurrProcessCode("P0003");
        psWipInfoList.add(psWipInfo1);
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setSn("777888900005");
        psWipInfo2.setCurrProcessCode("P0003");
        psWipInfoList.add(psWipInfo2);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTO.setProcessCodeBefore("P0003");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO1 = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO1.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO1.setSn("777888900005");
        scrapBillDetailEntityDTO1.setProcessCodeBefore("P0003");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO1);
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO2= new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO2.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO2.setSn("777888900008");
        scrapBillDetailEntityDTO2.setProcessCodeBefore("P0003");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO2);
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",new ArrayList<>(),scrapBillDetailEntityDTOList);
        Assert.assertNotNull(psWipInfoList);

        List<PsWorkOrderDTO> psWorkOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTOS.add(psWorkOrderDTO);
        psWorkOrderDTO.setWorkOrderNo("workOrderNo1");
        psWorkOrderDTO.setProdPlanId("ProdPlanId");
        psWorkOrderDTO.setSourceTask("SourceTask");
        psWorkOrderDTO.setProcessGroup("1");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(anyMap())).thenReturn(psWorkOrderDTOS);
        List<PmRepairRcvDetail> notReturnedList = new ArrayList<>();
        PmRepairRcvDetail detail = new PmRepairRcvDetail();
        detail.setSn("70360260001");
        detail.setIsAccept(new BigDecimal(0));
        detail.setStatus("1");
        notReturnedList.add(detail);
        PmRepairRcvDetail detail1 = new PmRepairRcvDetail();
        detail1.setSn("777888900005");
        detail1.setIsAccept(new BigDecimal(0));
        detail1.setStatus("1");
        notReturnedList.add(detail1);
        PowerMockito.when(pmRepairRcvDetailService.getTheLatestRepairRecord(anyList())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",psWipInfoList,scrapBillDetailEntityDTOList);
        Assert.assertNotNull(psWipInfoList);

        PowerMockito.when(pmRepairRcvDetailService.getTheLatestRepairRecord(anyList())).thenReturn(notReturnedList);
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",psWipInfoList,scrapBillDetailEntityDTOList);
        Assert.assertNotNull(psWipInfoList);

    }
    @Test
    public void setInputOutputIdentification2() throws Exception {
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTO.setProcessCodeBefore("P0003");
        Map<String,String> map = new HashMap<>();
        map.put("no","2");
        map.put("no","2$3");
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfo.setWorkOrderNo("no");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"0","",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);
        psWipInfo.setWorkOrderNo("no1");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"1","",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);
        psWipInfo.setWorkOrderNo("no");
        psWipInfo.setCurrProcessCode("2");
        psWipInfo.setLastProcess("Y");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"1","2",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);

        psWipInfo.setCurrProcessCode("3");
        psWipInfo.setLastProcess("N");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"1","3",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);
        psWipInfo.setCurrProcessCode("3");
        psWipInfo.setLastProcess("Y");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"1","3",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);
        psWipInfo.setCurrProcessCode("2");
        psWipInfo.setLastProcess("N");
        Whitebox.invokeMethod(scrapBillHeaderServiceImpl,"setInputOutputIdentification",scrapBillDetailEntityDTO,"1","2",map,psWipInfo);
        Assert.assertNotNull(scrapBillDetailEntityDTO);

    }
    /* Ended by AICoder, pid:daed0n2b666b1b21491009add14d2d471609222a */
    @Test
    public void updateBillStatusForApproval() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,RedisHelper.class, CommonUtils.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"1\"}",
                ScrapBillHeaderEntityDTO.class);
        scrapBillHeaderEntityDTO.setBillStatus(Constant.BoardScrapBillStatus.UNDER_APPROVAL);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(anyString())).thenReturn(scrapBillHeaderEntityDTO);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTO.setProcessCodeBefore("P0003");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        scrapBillHeaderEntityDTO.setScrapBillDetailEntityDTOList(scrapBillDetailEntityDTOList);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(anyString())).thenReturn(scrapBillHeaderEntityDTO);
        PowerMockito.when(scrapBillDetailRepository.getList(anyObject())).thenReturn(scrapBillDetailEntityDTOList);
        try {
            List<String> repairSnList = new ArrayList<>();
            List<List<String>> lists = new ArrayList<>();
            lists.add(repairSnList);
            repairSnList.add("777888900001");
            ApprovalCenterCallbackDealDTO approvalCenterCallbackDealDTO3 = new ApprovalCenterCallbackDealDTO();
            approvalCenterCallbackDealDTO3.setBillNo("iMESBF201255522");
            approvalCenterCallbackDealDTO3.setApprovalStatus(Constant.ApprovalStatus.APPROVAL_COMPLETED);
            scrapBillHeaderServiceImpl.updateBillStatusForApproval(approvalCenterCallbackDealDTO3);
        }catch (MesBusinessException e){
            Assert.assertEquals("777888900001", scrapBillDetailEntityDTO.getSn());
        }
        try {
            scrapBillHeaderEntityDTO.setBillStatus(Constant.BoardScrapBillStatus.PENDING_APPROVAL);
            ApprovalCenterCallbackDealDTO approvalCenterCallbackDealDTO = new ApprovalCenterCallbackDealDTO();
            approvalCenterCallbackDealDTO.setApprovalOpinion("y");
            approvalCenterCallbackDealDTO.setBillNo("iMESBF201255522");
            approvalCenterCallbackDealDTO.setApprover("1027");
            approvalCenterCallbackDealDTO.setApprovalStatus(Constant.ApprovalStatus.AGREE);
            scrapBillHeaderServiceImpl.updateBillStatusForApproval(approvalCenterCallbackDealDTO);
        }catch (MesBusinessException e){
            Assert.assertEquals("777888900001", scrapBillDetailEntityDTO.getSn());
        }
        try {

            ApprovalCenterCallbackDealDTO approvalCenterCallbackDealDTO2 = new ApprovalCenterCallbackDealDTO();
            approvalCenterCallbackDealDTO2.setBillNo("iMESBF201255522");
            approvalCenterCallbackDealDTO2.setApprovalStatus(Constant.ApprovalStatus.REFUSE);
            scrapBillHeaderServiceImpl.updateBillStatusForApproval(approvalCenterCallbackDealDTO2);
        }catch (MesBusinessException e){
            Assert.assertEquals("777888900001", scrapBillDetailEntityDTO.getSn());
        }

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);
        Assert.assertEquals(scrapBillHeaderServiceImpl.writeScanHistory("",scrapBillDetailEntityDTOList,new HashMap<>()),0);
        Assert.assertEquals(scrapBillHeaderServiceImpl.writeScanHistory("",new ArrayList<>(),new HashMap<>()),0);
    }
    @Test
    public void save() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        sysLookupTypesDTOUrl.setLookupCode(new BigDecimal(1004052011));
        SysLookupTypesDTO sysLookupTypesDTOUrl2 = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl2.setLookupMeaning("http://");
        sysLookupTypesDTOUrl2.setLookupCode(new BigDecimal(1004052012));
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl2);
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/user/ucs/getByAccountId");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"1\"}",
                ScrapBillHeaderEntityDTO.class);
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO2 = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"2\"}",
                ScrapBillHeaderEntityDTO.class);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setUnitPrice(BigDecimal.ZERO);
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        scrapBillHeaderEntityDTO2.setScrapBillDetailEntityDTOList(scrapBillDetailEntityDTOList);
        scrapBillHeaderEntityDTO.setScrapBillDetailEntityDTOList(scrapBillDetailEntityDTOList);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(anyString())).thenReturn(scrapBillHeaderEntityDTO);
        PowerMockito.when(scrapBillDetailRepository.getList(anyObject())).thenReturn(scrapBillDetailEntityDTOList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);

        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("11");
        lookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        PowerMockito.when(scrapBillDetailService.verifyExcelInfoList(any(),any(), anyList())).thenReturn(scrapBillHeaderEntityDTO);
        Assert.assertEquals("777888900001", scrapBillDetailEntityDTO.getSn());
        scrapBillHeaderServiceImpl.save(scrapBillHeaderEntityDTO);

    }


    @Mock
    private ScrapBillDetailService scrapBillDetailService;

    @Mock
    private DocFilePropertiesServiceImpl docFilePropertiesService;
    @Test
    public void pageList() throws Exception {
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"111\",\"sn\":\"\",\"scrapType\":\"0\",\"billStatus\":\"\",\"createBy\":\"\",\"createDateStart\":\"2021-12-16 00:00:00\",\"createDateEnd\":\"2022-06-16 23:59:59\",\"rows\":10,\"page\":1}",
                ScrapBillHeaderEntityDTO.class);
        List<String> list = new ArrayList<>();
        list.add("11");
        List<DocFilePropertiesEntityDTO> dtoList = new ArrayList<>();
        DocFilePropertiesEntityDTO docFilePropertiesEntityDTO = new DocFilePropertiesEntityDTO();
//        docFilePropertiesEntityDTO.setHeadId("111");
        docFilePropertiesEntityDTO.setDocId("testId");
        docFilePropertiesEntityDTO.setDocName("testName");
        dtoList.add(docFilePropertiesEntityDTO);
        scrapBillHeaderEntityDTO.setExportPropList(list);
        scrapBillHeaderEntityDTO.setExportNameList(list);
        List<ScrapBillHeaderEntityDTO> scrapBillHeaderlist = new ArrayList<>();
        scrapBillHeaderlist.add(scrapBillHeaderEntityDTO);
        PowerMockito.when(scrapBillHeaderRepository.pageList(any())).thenReturn(scrapBillHeaderlist);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(docFilePropertiesService.getList(Mockito.any())).thenReturn(dtoList);
        Assert.assertNotNull(scrapBillHeaderServiceImpl.pageList(scrapBillHeaderEntityDTO));
    }

    @Test
    public void exportExcel() throws Exception {
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"1\"}",
                ScrapBillHeaderEntityDTO.class);
        List<String> list = new ArrayList<>();
        list.add("11");
        scrapBillHeaderEntityDTO.setExportPropList(list);
        scrapBillHeaderEntityDTO.setExportNameList(list);

        try {
            scrapBillHeaderServiceImpl.exportExcel(null, scrapBillHeaderEntityDTO);
        } catch (Exception e) {
            String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);
        }
    }

    @Test
    public void downFileByKey() throws Exception {
        CloudDiskDTO cloudDiskDTO = new CloudDiskDTO();
        cloudDiskDTO.setEmpNo("1027");
        cloudDiskDTO.setFileName("1027");
        cloudDiskDTO.setFileKey("1027");
        Assert.assertNull(scrapBillHeaderServiceImpl.downFileByKey(cloudDiskDTO));
    }

    @Test
    public void createBillNo() throws Exception {
        Assert.assertNull(scrapBillHeaderServiceImpl.createBillNo());
    }

    @Test
    public void withdrawOperate() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        sysLookupTypesDTOUrl.setLookupCode(new BigDecimal(1004052011));
        SysLookupTypesDTO sysLookupTypesDTOUrl2 = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl2.setLookupMeaning("http://");
        sysLookupTypesDTOUrl2.setLookupCode(new BigDecimal(1004052012));
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl2);
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/user/ucs/getByAccountId");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"1\"}",
                ScrapBillHeaderEntityDTO.class);
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO2 = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"2\"}",
                ScrapBillHeaderEntityDTO.class);
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(anyString())).thenReturn(scrapBillHeaderEntityDTO);
        PowerMockito.when(scrapBillDetailRepository.getList(anyObject())).thenReturn(scrapBillDetailEntityDTOList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);

        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("11");
        lookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        Assert.assertEquals("11", sysLookupTypesDTO.getLookupMeaning());
        scrapBillHeaderServiceImpl.withdrawOperate(scrapBillHeaderEntityDTO);
        scrapBillHeaderServiceImpl.withdrawOperate(scrapBillHeaderEntityDTO2);

    }

    @Test
    public void submit() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class, PlanscheduleRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        sysLookupTypesDTOUrl.setLookupCode(new BigDecimal(1004052011));
        SysLookupTypesDTO sysLookupTypesDTOUrl2 = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl2.setLookupMeaning("http://");
        sysLookupTypesDTOUrl2.setLookupCode(new BigDecimal(1004052012));
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl2);
        sysLookupTypesDTOS.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/user/ucs/getByAccountId");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"1\",\"attachDocIdList\":[\"121\"],\"attachDocNameList\":[\"121\"]}",
                ScrapBillHeaderEntityDTO.class);
        scrapBillHeaderEntityDTO.setImportFileId("11");
        scrapBillHeaderEntityDTO.setImportFileName("xxx.xlsx");
        ScrapBillHeaderEntityDTO scrapBillHeaderEntityDTO2 = JSON.parseObject("{\"billNo\":\"IMESBF220615202206150001\",\"workshopDirector\":\"\",\"qualityEngineer\":\"********\",\"financer\":\"********\",\"qualityMinister\":\"********\",\"headOfPartsProductionDepartment\":\"\",\"deputyGeneralManagerOfProduction\":\"********\",\"qualityMinisterDomains\":[],\"qualityMinisterValue\":[],\"qualityMinisterOriginValue\":[],\"financerDomains\":[],\"financerValue\":[],\"financerOriginValue\":[],\"qualityEngineerDomains\":[{\"value\":\"qualityEngineerOther0\",\"key\":\"qualityEngineerOther0\"}],\"qualityEngineerValue\":[{\"label\":\"222\",\"value\":\"********\"}],\"qualityEngineerOriginValue\":[],\"headOfPartsProductionDepartmentDomains\":[],\"headOfPartsProductionDepartmentValue\":[],\"headOfPartsProductionDepartmentOriginValue\":[],\"deputyGeneralManagerOfProductionDomains\":[],\"deputyGeneralManagerOfProductionOriginValue\":[],\"deputyGeneralManagerOfProductionValue\":[],\"billStatus\":\"拟制中\",\"scrapType\":\"2\"}",
                ScrapBillHeaderEntityDTO.class);
        scrapBillHeaderEntityDTO.setAttachDocId("11");
        List<ScrapBillDetailEntityDTO> scrapBillDetailEntityDTOList = new ArrayList<>();
        ScrapBillDetailEntityDTO scrapBillDetailEntityDTO = new ScrapBillDetailEntityDTO();
        scrapBillDetailEntityDTO.setSn("777888900001");
        scrapBillDetailEntityDTOList.add(scrapBillDetailEntityDTO);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(anyString())).thenReturn(scrapBillHeaderEntityDTO);
        PowerMockito.when(scrapBillDetailRepository.getList(anyObject())).thenReturn(scrapBillDetailEntityDTOList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777888900001");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfoList);

        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("11");
        lookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(lookupTypesDTOList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("1");

        try {
            scrapBillHeaderServiceImpl.submit(scrapBillHeaderEntityDTO);
            scrapBillHeaderServiceImpl.submit(scrapBillHeaderEntityDTO2);
        }catch (Exception e){
            Assert.assertEquals("11", sysLookupTypesDTO.getLookupMeaning());
        }

        scrapBillHeaderEntityDTO.setTerminalFlag(true);
        try {
            scrapBillHeaderServiceImpl.submit(scrapBillHeaderEntityDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.APPROVER_CANNOT_BE_EMPTY_COMMON.equals(e.getExMsgId()));
        }
        List<ApprovalProcessInfoEntityDTO> approvers = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvers.add(approvalProcessInfoEntityDTO);
        scrapBillHeaderEntityDTO.setApprovers(approvers);
        scrapBillHeaderServiceImpl.submit(scrapBillHeaderEntityDTO);
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(null);
        scrapBillHeaderEntityDTO.setOfflineFlag("Y");
        try {
            scrapBillHeaderServiceImpl.submit(scrapBillHeaderEntityDTO);
        }catch (Exception e){
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void validateStatusForSubmitWarehouseTest() throws Exception {
        String billNo = "test";
        ScrapBillHeaderEntityDTO dto = new ScrapBillHeaderEntityDTO();
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(Mockito.any())).thenReturn(null);
        try {
            scrapBillHeaderServiceImpl.validateStatusForSubmitWarehouse(billNo);
        } catch (MesBusinessException e) {
            Assert.assertTrue( MessageId.SCRAP_BILL_NO_NOT_EXIST.equals(e.getExMsgId()));
        }
        dto.setBillStatus("test");
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(Mockito.any())).thenReturn(dto);
        try {
            scrapBillHeaderServiceImpl.validateStatusForSubmitWarehouse(billNo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SCRAP_BILL_NO_STATUS_IS_NOT_APPROVAL.equals(e.getExMsgId()));
        }
        dto.setBillStatus(Constant.SCRAP_BILL_STATUS_FINISH);
        PowerMockito.when(scrapBillHeaderRepository.getInfoByBillNo(Mockito.any())).thenReturn(dto);
        List<String> strings = new ArrayList<>();
        strings.add("test");
        PowerMockito.when(scrapBillDetailRepository.getMultiBatchWithBillNo(Mockito.any())).thenReturn(strings);
        Assert.assertTrue(scrapBillHeaderServiceImpl.validateStatusForSubmitWarehouse(billNo) != null);
    }


}