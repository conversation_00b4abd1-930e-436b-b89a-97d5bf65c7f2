package com.zte.autoTest.unitTest;

import com.zte.application.AvlService;
import com.zte.application.PmRepairRcvService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WipScanHistoryService;
import com.zte.application.impl.PmRepairRcvDetailServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.itp.msa.core.config.ICommonConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

@PrepareForTest({CommonUtils.class})
public class PmRepairRcvDetailServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairRcvDetailServiceImpl service;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvRepository;

    @Mock
    ICommonConfig commonConfig;

    @Mock
    private AvlService avlService;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private WipScanHistoryService wipScanHistoryService;

    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Mock
    private PmRepairRcvRepository prrRepository;

    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WipTaskInfoRepository wipTaskInfoRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository1;

    @Mock
    private PmRepairRcvRepository rcvRepository;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void getToRepairRecInfo() throws Exception {

        PmRepairRcvDetailDTO dto = new PmRepairRcvDetailDTO();
        dto.setSn("730638900034");
        dto.setWorkStation("S1053");
        dto.setPageType("2");

        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "2")).thenReturn(0);
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord(Mockito.any(), Mockito.any())).thenReturn(0);
        dto.setSnType("1");
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.REC_INFO_NULL);
        try {
            PmRepairRcvDetailDTO toRepairRecInfo1 = service.getToRepairRecInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REC_INFO_NULL, e.getMessage());
        }
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.PRODPLAN_EXIST_NO_SN_RECORD);
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "2")).thenReturn(1);
        dto.setSnType("1");
        try {
            PmRepairRcvDetailDTO toRepairRecInfo2 = service.getToRepairRecInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_EXIST_NO_SN_RECORD, e.getMessage());
        }

        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "1")).thenReturn(0);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.REC_INFO_ERROR);
        dto.setSnType("2");
        try {
            PmRepairRcvDetailDTO toRepairRecInfo3 = service.getToRepairRecInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REC_INFO_ERROR, e.getMessage());
        }

        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306389", "1")).thenReturn(1);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.PRODPLAN_EXIST_SN_RECORD);
        dto.setSnType("2");
        try {
            Assert.assertNotNull(service.getToRepairRecInfo(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_EXIST_SN_RECORD, e.getMessage());
        }
    }

    /* Started by AICoder, pid:of292oa22ek151f142f30b5d20ec9810d33771a5 */
    @Test
    public void getLatestNotReturnRcvDetailBySn() {
        PmRepairRcvDetail rt = service.getLatestNotReturnRcvDetailBySn("");
        Assert.assertNull("当序列号为空时，返回值应为null", rt);

        PmRepairRcvDetail repairRcvDetail = new PmRepairRcvDetail();
        repairRcvDetail.setSn("701912800030");
        PowerMockito.when(pmRepairRcvDetailRepository.getLatestRcvDetailBySn(Mockito.anyString(), Mockito.any())).thenReturn(repairRcvDetail);
        rt = service.getLatestNotReturnRcvDetailBySn("701912800030");
        Assert.assertNotNull(rt);
    }
    /* Ended by AICoder, pid:of292oa22ek151f142f30b5d20ec9810d33771a5 */

    @Test
    public void updateRcvNine() {
        service.updateRcvNine();
        Assert.assertNotNull(service.updateRepairNine());
    }

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBom", null);
        Assert.assertTrue(1==1);
        PmRepairRcvDetailDTO entity = new PmRepairRcvDetailDTO();
        entity.setRcvProdplanId("1234567");
        entity.setItemCode("itemCode");
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBom", entity);
        Assert.assertTrue(entity.getMbom().equals("test"));

        dto.setProdplanId("12345671");
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBom", entity);
        Assert.assertTrue(entity.getMbom().equals("itemCode"));
    }
}
