package com.zte.autoTest.unitTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.impl.SmtMachineMaterialMoutingServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.domain.vo.EquipmentConsumeReelIdVO;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.INT_0;
import static org.mockito.Matchers.*;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/12/10 16:54
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, SpringUtil.class, PlanscheduleRemoteService.class,
        ProductionDeliveryRemoteService.class, MESHttpHelper.class, RedisHelper.class,
        RequestHeadValidationUtil.class,AopContext.class, HttpRemoteUtil.class,DatawbRemoteService.class, CommonUtils.class})
public class SmtMachineMaterialMoutingServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtMachineMaterialMoutingServiceImpl service;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private PkCodeInfoService pkCodeInfoService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private WipScanHistoryService wipScanHistoryService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;

    @Mock
    private BSmtBomDetailRepository smtBomDetailRepository;

    @Mock
    private SmtMachineMTLHistoryHRepository historyHRepository;

    @Mock
    private SmtMachineMTLHistoryLRepository historyLRepository;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    private ImesPDACommonService imesPDACommonService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    SmtMachineMaterialMoutingService aopProxy;
    @Mock
    CloudDiskHelper cloudDiskHelper;
    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class, RedisHelper.class);
    }


    @Test
    public void checkBomQtyAndDeductQty() throws Exception {
        List<WipScanHistory> wipScanHistorylist = new ArrayList<>();
        Map<String, BBomDetail> bBomDetailDTOMap = new HashMap<>();
        Map<String, BSmtBomDetail> bSmtBomDetailMap = new HashMap<>();
        List<BSmtBomDetail> bSmtBomDetailAllList = new ArrayList<>();
        service.checkBomQtyAndDeductQty(wipScanHistorylist,bBomDetailDTOMap,bSmtBomDetailMap,bSmtBomDetailAllList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void getObjectServiceData() throws Exception {
        SmtMachineMaterialMouting dto = new SmtMachineMaterialMouting();
        dto.setItemCode("013040100119");
        dto.setFactoryId(new BigDecimal(55));
        List<SmtMachineMaterialMouting> tempNMountingList = new ArrayList<>();
        tempNMountingList.add(dto);
        Assert.assertNotNull(service.getObjectServiceData(new DipBoardScanningDTO(),new PsWorkOrderDTO(),new ArrayList<>(),new HashMap<>(),new ArrayList<>()));
    }
    @Test
    public void queryAbcType() throws Exception {
        SmtMachineMaterialMouting dto = new SmtMachineMaterialMouting();
        dto.setItemCode("013040100119");
        dto.setFactoryId(new BigDecimal(55));
        List<SmtMachineMaterialMouting> tempNMountingList = new ArrayList<>();
        tempNMountingList.add(dto);

        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfoCollect = new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO dto2 = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        dto2.setItemNo("013040100119");
        dto2.setFactoryId(new BigDecimal(55));
        dto2.setItemName("贴片绿色侧发光二极管");
        dto2.setAbcType("A");
        itemInfoCollect.add(dto2);

        AgeingInfoFencePointToPointQueryItemInfoDTO dto1 = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        dto1.setItemNo("046050200167");
        dto1.setFactoryId(new BigDecimal(55));
        dto1.setItemName("陶瓷贴片电容器");
        dto1.setAbcType("C");
        itemInfoCollect.add(dto1);

        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyList())).thenReturn(itemInfoCollect);
        service.queryAbcType(tempNMountingList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void deleteMoutingByUnbind() throws Exception {
        SmtMachineMaterialMoutingDTO dto = new SmtMachineMaterialMoutingDTO();
        dto.setLpn("123");
        List<String> reelidList = new ArrayList<>();
        String reelid = "123";
        reelidList.add(reelid);
        dto.setObjectIdList(reelidList);

        List<SmtMachineMaterialMouting> tempNextReelidMountingList = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setNextReelRowid("123");
        tempNextReelidMountingList.add(smtMachineMaterialMouting);
        PowerMockito.when(smtMachineMaterialMoutingRepository
                .getList(Mockito.any())).thenReturn(tempNextReelidMountingList);


        service.deleteMoutingByUnbind(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void getMoutingWithPkCodeInfoByWorkOrder() throws Exception {
        DipBoardScanningDTO dto = new DipBoardScanningDTO();
        dto.setScanByWorkStation(true);
        dto.setWorkOrderNo("7777999-DIP5503");
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getMoutingWithPkCodeInfoByWorkOrder(dto));
    }

    @Test
    public void deleteMountingDataOfDip() throws Exception {
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        list.add(smtMachineMaterialMouting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(any())).thenReturn(list);
        PowerMockito.when(smtMachineMTLHistoryHService.updateSmtHistoryByLineCodeAndWorkOrderAndMountType(any())).thenReturn(1);

        SmtMachineMaterialMoutingDTO dto = new SmtMachineMaterialMoutingDTO();
        dto.setWorkOrder("7777999-DIP5503");
        dto.setLastUpdatedBy("111");
        dto.setLineCode("DIP-HYM");
        service.deleteMountingDataOfDip(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateEquipmentDockingMsg() throws Exception {
        EquipmentConsumeReelIdDTO dto = new EquipmentConsumeReelIdDTO();
        dto.setReelId("ceshi001");
        dto.setQty(0L);
        EquipmentConsumeReelIdDTO dto1 = new EquipmentConsumeReelIdDTO();
        dto1.setReelId("ceshi002");
        dto1.setQty(15L);
        List<EquipmentConsumeReelIdDTO> consumeReelIdList = Lists.newArrayList(dto, dto1);
        EquipmentConsumeReelIdVO reelIdVO = new EquipmentConsumeReelIdVO();
        reelIdVO.setReelId("ceshi001");
        reelIdVO.setLineCode("SMT-HY005");
        reelIdVO.setQty(15L);
        reelIdVO.setRidList(consumeReelIdList);
        reelIdVO.setNextReelRowid("ceshi003");
        List<EquipmentConsumeReelIdVO> list = Lists.newArrayList(reelIdVO);

        EquipmentConsumeReelIdVO nextReelIdVO = new EquipmentConsumeReelIdVO();
        nextReelIdVO.setReelId("ceshi003");
        nextReelIdVO.setLineCode("SMT-HY005");
        nextReelIdVO.setQty(15L);
        nextReelIdVO.setRidList(consumeReelIdList);
        nextReelIdVO.setNextReelRowid("ceshi002");
        List<EquipmentConsumeReelIdVO> nextReelIdlist = Lists.newArrayList(nextReelIdVO);
        PowerMockito.when(smtMachineMaterialMoutingRepository.getReelIdUseInfo(consumeReelIdList)).thenReturn(list);

        PowerMockito.when(smtMachineMaterialMoutingRepository.getNextReelIdUseInfo(Mockito.any())).thenReturn(nextReelIdlist);

        PowerMockito.mockStatic(SpringUtil.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<CFLine> lineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("SMT-HY005");
        cfLine.setMaterielMarker(Constant.FLAG_Y);
        lineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLineCodeMaterielMarker(Mockito.any())).thenReturn(lineList);
        PowerMockito.when(SpringUtil.getBean(SmtMachineMaterialMoutingServiceImpl.class)).thenReturn(service);
        List<SmtMachineMaterialMouting> mountingList = new ArrayList<>();
        SmtMachineMaterialMouting mounting = new SmtMachineMaterialMouting();
        mounting.setObjectId("ceshi001");
        mounting.setNextReelRowid("ceshi003");
        mountingList.add(mounting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(mountingList);

        List<PkCodeInfo> pkCodeList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("ceshi003");
        pkCodeList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getList(Mockito.any())).thenReturn(pkCodeList);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(true);
        PowerMockito.when(redisOpsValue.increment(Mockito.anyObject())).thenReturn((long) 1);

        Assert.assertNotNull(service.updateEquipmentDockingMsg(reelIdVO));
    }

    @Test
    public void updatePkCodeInfoQty() throws Exception {
        List<EquipmentConsumeReelIdVO> equipmentConsumeReelId = new ArrayList<>();
        EquipmentConsumeReelIdVO reelIdVO = new EquipmentConsumeReelIdVO();
        reelIdVO.setReelId("ceshi002");
        reelIdVO.setQty(15L);
        equipmentConsumeReelId.add(reelIdVO);
        try {
            Whitebox.invokeMethod(service, "updatePkCodeInfoQty", "6606000001", equipmentConsumeReelId);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void closeRedisLock() {
        service.closeRedisLock(new RedisLock("1"), new RedisLock("1"));
        service.closeRedisLock(new RedisLock("1"), null);
        service.closeRedisLock(null, null);
        service.closeRedisLock(null, new RedisLock("1"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void dipBoardScanning() throws Exception {
        DipBoardScanningDTO param = new DipBoardScanningDTO();
        param.setWorkOrderNo("123");
        param.setFactoryId("2");
        List<PsWorkOrderDTO> workOrderList = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        a1.setItemNo("2");
        a1.setWorkOrderNo("123");
        a1.setWorkOrderStatus("已开工");
        a1.setCfgHeaderId("123");
        workOrderList.add(a1);

        PowerMockito.when(PlanscheduleRemoteService.getPsWorkOrderBasic(Mockito.anyMap()))
                .thenReturn(workOrderList);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCraftSection("DIP");
        psWipInfo.setAttribute1("123");
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(Mockito.any())).thenReturn(psWipInfo);

        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.anyMap()))
                .thenReturn(1l);
        List<PsWorkOrderSmt> workOrderSmtList = new LinkedList<>();
        PsWorkOrderSmt c1 = new PsWorkOrderSmt();
        c1.setCfgHeaderId("123");
        workOrderSmtList.add(c1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(Mockito.any()))
                .thenReturn(workOrderSmtList);

        List<BSmtBomDetail> bSmtBomDetails = new LinkedList<>();
        BSmtBomDetail d1 = new BSmtBomDetail();
        bSmtBomDetails.add(d1);
        PowerMockito.when(bSmtBomDetailService.getList(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(bSmtBomDetails);
        List<BBomDetail> allChildByProductCode = new LinkedList<>();
        BBomDetail bBomDetail1 = new BBomDetail();
        bBomDetail1.setProductCode("129206751425AKB");
        bBomDetail1.setItemCode("129206751425AKB1");
        allChildByProductCode.add(bBomDetail1);

        BBomDetail a2 = new BBomDetail();
        a2.setProductCode("129206751425AKB1");
        a2.setItemCode("129206751425AKB2");
        allChildByProductCode.add(a2);

        BBomDetail a3 = new BBomDetail();
        a3.setProductCode("129206751425AKB1");
        a3.setItemCode("129206751425AKB3");
        allChildByProductCode.add(a3);
        PowerMockito.when(BasicsettingRemoteService.getBomListofDipNew(any())).thenReturn(allChildByProductCode);
        service.dipBoardScanning(param);
        param.setScanByWorkStation(false);
        service.dipBoardScanning(param);
        List<WipScanHistory> wipScanHistoryList = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("77788890002");
        wipScanHistory.setCraftSection("ICT");
        wipScanHistoryList.add(wipScanHistory);
        wipScanHistory.setSn("77788890003");
        wipScanHistory.setCraftSection("ICT");
        wipScanHistoryList.add(wipScanHistory);
        PowerMockito.when(wipScanHistoryService.getOverBoardList(anyObject())).thenReturn(wipScanHistoryList);
        Assert.assertNotNull(service.dipBoardScanning(param));
    }

    @Test
    public void oneKeySwitchMouting() throws Exception {
        PowerMockito.when(PlanscheduleRemoteService.getWoSmtByWos(any(), any())).thenReturn(new HashMap() {{
            put("2022507-SMT-A5501", new PsWorkOrderSmtDTO() {{
                setCfgHeaderId("1");
                setSourceTask("2022507");
            }});
        }});
        PowerMockito.when(smtBomDetailRepository.selectBSmtBomDetailListById(any())).thenReturn(Lists.newArrayList(new BSmtBomDetail() {{
            setCfgHeaderId("1");
            setModuleNo("1");
            setItemCode("1");
            setLocationNo("1");
        }}));
        PowerMockito.when(smtMachineMaterialMoutingRepository.getBySmtBomDetail(any(), any())).thenReturn(Lists.newArrayList(new SmtMachineMaterialMouting() {{
            setCfgHeaderId("1");
            setModuleNo("1");
            setItemCode("1");
            setLocationNo("1");
            setMachineMaterialMoutingId("1");
        }}));
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(any())).thenReturn(Lists.newArrayList(new SmtMachineMaterialPrepare() {{
            setCfgHeaderId("1");
            setModuleNo("1");
            setItemCode("1");
            setLocationNo("1");
            setMtlPrepareId("1");
        }}));
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(PlanscheduleRemoteService.getPsEntityPlanBasicList(any())).thenReturn(null);
        try {
            service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO() {{
                setCurWorkOrder("2022507-SMT-A5501");
                setTarWorkOrder("2022507-SMT-B5502");
                setLineCode("SMT-TZ001");
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_NO_CFG_ID, e.getMessage());
        }
        try {
            service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO() {{
                setCurWorkOrder("2022507-SMT-A5501");
                setTarWorkOrder("2022507-SMT-A5501");
                setLineCode("SMT-TZ001");
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TAR_WORK_ORDER_IS_NULL, e.getMessage());
        }
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoWithFeeder(any())).thenReturn(Lists.newArrayList(new SmtMachineMaterialPrepare() {{
            setCfgHeaderId("1");
            setModuleNo("1");
            setItemCode("1");
            setLocationNo("1");
            setMtlPrepareId("1");
            setOldReelId("123456");
            setObjectId("12345678");
        }}));
        try {
            service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO() {{
                setCurWorkOrder("2022507-SMT-A5501");
                setTarWorkOrder("2022507-SMT-A5501");
                setLineCode("SMT-TZ001");
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TAR_WORK_ORDER_IS_NULL, e.getMessage());
        }
        try {
            service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO() {{
                setTarWorkOrder("2022507-SMT-A5501");
                setLineCode("SMT-TZ001");
            }});
        } catch (Exception e) {
        }
        try {
            service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TAR_WORK_ORDER_IS_NULL, e.getMessage());
        }
        // 找出当前线体，模组存在的有效机台在用，
        List<SmtMachineMaterialMouting> smtMachMatMoutList = new ArrayList<>();
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        smtMachMatMoutList.add(mouting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineModel(any(), any())).thenReturn(smtMachMatMoutList);
        try {
            Assert.assertEquals(1,service.oneKeySwitchMouting(new OneKeySwitchMoutingDTO() {{
                setTarWorkOrder("2022507-SMT-A5501");
                setLineCode("SMT-TZ001");
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_MODULE_HAS_MOUTING, e.getMessage());
        }
    }

    @Test
    public void checkSmtLocationLineInfo() {
        try {
            service.checkSmtLocationLineInfo("1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }
        try {
            PowerMockito.when(BasicsettingRemoteService.getSmtLocationLineInfo(any()))
                    .thenReturn(new OneKeySwitchMoutingDTO());
            service.checkSmtLocationLineInfo("1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_MODEL_CHG, e.getMessage());
        }
        try {
            PowerMockito.when(BasicsettingRemoteService.getSmtLocationLineInfo(any()))
                    .thenReturn(new OneKeySwitchMoutingDTO() {{
                        setSupChgLineOnModule("Y");
                    }});
            service.checkSmtLocationLineInfo("1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        try {
            PowerMockito.when(BasicsettingRemoteService.getSmtLocationLineInfo(any()))
                    .thenReturn(new OneKeySwitchMoutingDTO() {{
                        setSupChgLineOnModule("Y");
                        setIntelligentFeederFlag("Y");
                    }});
            service.checkSmtLocationLineInfo("1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        try {
            // 找出当前线体，模组存在的有效机台在用，
            List<SmtMachineMaterialMouting> smtMachMatMoutList = new ArrayList<>();
            SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
            smtMachMatMoutList.add(mouting);
            PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineModel(any(), any())).thenReturn(smtMachMatMoutList);
            Assert.assertNotNull(service.checkSmtLocationLineInfo("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
    }

    @Test
    public void rcvScanOldPkCode() throws Exception {
        List<SmtMachineMaterialMouting> mountingList = new ArrayList<>();
        SmtMachineMaterialMouting mounting = new SmtMachineMaterialMouting();
        mounting.setObjectId("ceshi001");
        mounting.setWorkOrder("7000614-SMT-A5301");
        mountingList.add(mounting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(mountingList);
        PowerMockito.when(smtSnMtlTracingTService.getLineInfoMap(Mockito.any())).thenReturn(null);

        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setSourceTask("7777666");
        psEntityPlanBasic.setItemNo("123456789ABC");
        psEntityPlanBasic.setWorkOrderStatus(Constant.IS_START);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.anyString()))
                .thenReturn(psEntityPlanBasic);

        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemQty(BigDecimal.TEN);
        PowerMockito.when(pkCodeInfoService.getPkCodeInfoByCode(Mockito.anyString()))
                .thenReturn(pkCodeInfo);

        ServiceData<SmtMachineMaterialMoutingDTO> serviceData = new ServiceData<>();
        PowerMockito.when(imesPDACommonService.pdaReceiveCheck(Mockito.anyString(), Mockito.any()))
                .thenReturn(serviceData);

        service.rcvScanOldPkCode("ceshi001", "52");
        List<PsWorkOrderSmt> workOrderSmtList = new LinkedList<>();
        PsWorkOrderSmt c1 = new PsWorkOrderSmt();
        c1.setCfgHeaderId("123");
        workOrderSmtList.add(c1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTByWorkOrder(Mockito.any()))
                .thenReturn(workOrderSmtList);
        List<SmtMachineMaterialMouting> list = service.rcvScanOldPkCode("ceshi001", "52");
        Assert.assertEquals(1, list.size());
    }

    @Test
    public void rcvScanNewPkCodeCheck() throws Exception {
        boolean wmsHasReelId = false;
        PowerMockito.when(ProductionDeliveryRemoteService.checkWmsHasReelId(Mockito.anyString()))
                .thenReturn(wmsHasReelId);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-factory-id", "2");
        headerMap.put("x-emp-no", "123");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");

        List<SmtMachineMaterialMouting> mountingList = new ArrayList<>();
        SmtMachineMaterialMouting mounting = new SmtMachineMaterialMouting();
        mounting.setObjectId("ceshi001");
        mounting.setWorkOrder("7000614-SMT-A5301");
        mountingList.add(mounting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(mountingList);
        PowerMockito.when(smtSnMtlTracingTService.getLineInfoMap(Mockito.any())).thenReturn(null);

        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setSourceTask("7777666");
        psEntityPlanBasic.setItemNo("123456789ABC");
        psEntityPlanBasic.setWorkOrderStatus(Constant.IS_START);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.anyString()))
                .thenReturn(psEntityPlanBasic);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any()))
                .thenReturn("Y");
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemQty(BigDecimal.TEN);
        PowerMockito.when(pkCodeInfoService.getPkCodeInfoByCode(Mockito.anyString()))
                .thenReturn(pkCodeInfo);

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn("");
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo1 = new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        itemInfoNew.setAbcType("123");
        itemInfo1.add(itemInfoNew);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(anyList())).thenReturn(itemInfo1);
        Assert.assertNull(service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002"));

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn((MpConstant.PDA_RS_MESSAGE_THREE + MpConstant.PDA_RS_MESSAGE_SEVEN));
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals((MpConstant.PDA_RS_MESSAGE_THREE + MpConstant.PDA_RS_MESSAGE_SEVEN), e.getMessage());
        }

        
        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_ONE);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_ONE, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_TWO);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_TWO, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_FOUR);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_EIGHT, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_FIVE);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_FIVE, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_SIX);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_SIX, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn(MpConstant.PDA_RS_MESSAGE_SEVEN);
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals(MpConstant.PDA_RS_MESSAGE_SEVEN, e.getMessage());
        }

        PowerMockito.when(imesPDACommonService.polarCheck(Mockito.any()))
                .thenReturn((MpConstant.PDA_RS_MESSAGE_FOUR + MpConstant.PDA_RS_MESSAGE_SEVEN));
        try {
            service.rcvScanNewPkCodeCheck("ceshi001", "ceshi002","ceshi002");
        } catch (Exception e) {
            Assert.assertEquals((MpConstant.PDA_RS_MESSAGE_NIGHT), e.getMessage());
        }
    }

    @Test
    public void handlerHaveNextReelTest() throws Exception {
        List<SmtMachineMaterialMouting> haveNextReelMoutingList = new ArrayList<>();
        OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        Whitebox.invokeMethod(service, "handlerHaveNextReel", haveNextReelMoutingList, dto);
        smtMachineMaterialMouting.setNextReelRowid("test");
        smtMachineMaterialMouting.setObjectId("test");
        haveNextReelMoutingList.add(smtMachineMaterialMouting);
        List<PkCodeInfo> subPkCodeInfo = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("pkcode1");
        pkCodeInfo.setItemQty(new BigDecimal("1"));
        subPkCodeInfo.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(Mockito.anyList()))
                .thenReturn(subPkCodeInfo);
        Whitebox.invokeMethod(service, "handlerHaveNextReel", haveNextReelMoutingList, dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void handlerNotHaveNextReelTest() throws Exception {
        List<SmtMachineMaterialMouting> sameMouting = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting1 = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting1.setMachineMaterialMoutingId("test1");
        SmtMachineMaterialMouting smtMachineMaterialMouting2 = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting2.setMachineMaterialMoutingId("test2");
        sameMouting.add(smtMachineMaterialMouting2);
        sameMouting.add(smtMachineMaterialMouting1);
        List<SmtMachineMaterialMouting> haveNextReelMoutingList = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting3 = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting3.setMachineMaterialMoutingId("test1");
        haveNextReelMoutingList.add(smtMachineMaterialMouting3);
        OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
        Whitebox.invokeMethod(service, "handlerNotHaveNextReel", sameMouting, haveNextReelMoutingList, dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkMountingWhenHaveCommonItemTest() throws Exception {
        Map<String, PsWorkOrderSmtDTO> woSmt = new HashMap<>();
        Map<String, List<BSmtBomDetail>> woSmtDetail = new HashMap<>();
        OneKeySwitchMoutingDTO dto = new OneKeySwitchMoutingDTO();
        dto.setCurWorkOrder("curWork");
        dto.setTarWorkOrder("tarWork");
        List<SmtMachineMaterialMouting> smtMachMatMoutList = new ArrayList<>();
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectByLineModel(any(), any())).thenReturn(smtMachMatMoutList);
        Whitebox.invokeMethod(service, "checkMountingWhenHaveCommonItem", woSmt, woSmtDetail, dto);

        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        smtMachMatMoutList.add(mouting);
        PsWorkOrderSmtDTO tarSmt = new PsWorkOrderSmtDTO();
        tarSmt.setWorkOrderNo("tarWork");
        tarSmt.setSourceTask("task1");
        PsWorkOrderSmtDTO curSmt = new PsWorkOrderSmtDTO();
        curSmt.setWorkOrderNo("curWork");
        curSmt.setSourceTask("task2");
        woSmt.put("tarWork", tarSmt);
        woSmt.put("curWork", curSmt);
        try {
            Whitebox.invokeMethod(service, "checkMountingWhenHaveCommonItem", woSmt, woSmtDetail, dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.LINE_MODULE_HAS_MOUTING));
        }
        curSmt.setSourceTask("task1");

        List<BSmtBomDetail> tarWorkBomDetail = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("item1");
        bSmtBomDetail.setLocationNo("location1");
        tarWorkBomDetail.add(bSmtBomDetail);
        List<BSmtBomDetail> curWorkBomDetail = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        bSmtBomDetail1.setItemCode("item1");
        bSmtBomDetail1.setLocationNo("location1");
        curWorkBomDetail.add(bSmtBomDetail1);
        woSmtDetail.put("tarWork", tarWorkBomDetail);
        woSmtDetail.put("curWork", curWorkBomDetail);
        try {
            Whitebox.invokeMethod(service, "checkMountingWhenHaveCommonItem", woSmt, woSmtDetail, dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.LINE_MODULE_HAS_MOUTING));
        }
    }

    @Test
    public void updateItemQtyBatchByEqp() {
        try {
            service.updatePkCodeInfoQty("", Lists.newArrayList(new EquipmentConsumeReelIdVO() {{
                setQty(1L);
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(new SysLookupTypesDTO());
            service.updatePkCodeInfoQty("", Lists.newArrayList(new EquipmentConsumeReelIdVO() {{
                setQty(1L);
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(new SysLookupTypesDTO() {{
                setLookupMeaning("Y");
            }});
            service.updatePkCodeInfoQty("", Lists.newArrayList(new EquipmentConsumeReelIdVO() {{
                setQty(1L);
            }}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkAllComItem() {
        try {
            service.checkAllComItem(new OneKeySwitchMoutingDTO() {{
                                        setTarWorkOrder("2");
                                    }},
                    Lists.newArrayList(new SmtMachineMaterialMouting()),
                    Lists.newArrayList(), Lists.newArrayList());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        try {
            service.checkAllComItem(new OneKeySwitchMoutingDTO() {{
                                        setTarWorkOrder("2");
                                    }},
                    Lists.newArrayList(new SmtMachineMaterialMouting() {{
                        setWorkOrder("2");
                    }}),
                    Lists.newArrayList("1", "2"), Lists.newArrayList("2", "1"));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.LINE_MODULE_HAS_MOUTING, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void dipBoardScanningForEm() throws Exception {
        DipBoardScanningDTO param = new DipBoardScanningDTO();
        param.setSn("889820200001");
        param.setProcessCode("2");
        param.setSourceSys("imes");
        param.setWorkStation("imes");
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno())
                .thenReturn(Pair.of("52", "system"));
        try {
            service.dipBoardScanningForEm(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SNS_IS_NOT_EXITS, e.getMessage());
        }
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("889820200001");
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(Mockito.any()))
                .thenReturn(psWipInfo);
        try {
            service.dipBoardScanningForEm(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_ID_NULL, e.getMessage());
        }
        psWipInfo.setAttribute1("8898202");
        PowerMockito.when(psWipInfoService.getPsWipInfoBySn(Mockito.any()))
                .thenReturn(psWipInfo);
        try {
            service.dipBoardScanningForEm(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }
        List<PsWorkOrderDTO> workOrderInfo = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        a1.setWorkOrderNo("8898202-SMT-B5202");
        a1.setProcessGroup("2");
        a1.setLineCode("SMT-CS003");
        workOrderInfo.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(Mockito.any()))
                .thenReturn(workOrderInfo);
        try {
            service.dipBoardScanningForEm(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_NO_NOT_START, e.getMessage());
        }
        a1.setWorkOrderStatus(Constant.IS_START);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(aopProxy);

        ServiceData ret = new ServiceData();
        RetCode retCode = new RetCode();
        ret.setCode(retCode);
        PowerMockito.when(aopProxy.verifyProcessCodeScan(Mockito.any()))
                .thenReturn(ret);
        try {
            service.dipBoardScanningForEm(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        retCode.setCode(RetCode.SUCCESS_CODE);

        PowerMockito.when(aopProxy.dipBoardScanning(Mockito.any()))
                .thenReturn(ret);
        service.dipBoardScanningForEm(param);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void addBgBrandNoTest() throws Exception{
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setObjectId("ZTE20220601000995");
        smtMachineMaterialMouting.setSourceBatchCode("220003338180");
        list.add(smtMachineMaterialMouting);

        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(new String());

        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(DatawbRemoteService.getStItemBarcode(any())).thenReturn(rtJson);
        try {
            service.addBgBrandNo(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NO_SUP_IN_FEEDER, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void addIssueSeqRelationTest() throws Exception{
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setObjectId("ZTE20220601000995");
        list.add(smtMachineMaterialMouting);

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(any())).thenReturn(pkCodeInfoList);
        service.addIssueSeqRelation(list);

        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("ZTE20220601000995");
        pkCodeInfo.setSysLotCode("2778983");
        pkCodeInfo.setSupplerCode("20035100");
        pkCodeInfoList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(any())).thenReturn(pkCodeInfoList);

        List<TaskMaterialIssueSeqEntityDTO> packSpecList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(anyList())).thenReturn(packSpecList);
        service.addIssueSeqRelation(list);

        TaskMaterialIssueSeqEntityDTO dto = new TaskMaterialIssueSeqEntityDTO();
        dto.setSysLotCode("2778983");
        dto.setSupplerCode("20035100");
        dto.setIsDir("是");
        dto.setBraidDirection("是");
        packSpecList.add(dto);
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(anyList())).thenReturn(packSpecList);
        service.addIssueSeqRelation(list);

        SmtMachineMaterialMouting smtMachineMaterialMouting2 = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting2.setObjectId("ZTE202206010009956");
        list.add(smtMachineMaterialMouting2);
        service.addIssueSeqRelation(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void pdaReceiveCheckItem() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            service.pdaReceiveCheckItem(null,null);
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL, e.getExMsgId());
        }

        try {
            service.pdaReceiveCheckItem("123",null);
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL, e.getExMsgId());
        }

        try {
            service.pdaReceiveCheckItem(null,"123");
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL, e.getExMsgId());
        }
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(anyList())).thenReturn(null);
        try {
            service.pdaReceiveCheckItem("123","123");
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.PK_CODE_NOT_EXISTS, e.getExMsgId());
        }
        List<PkCodeInfo> listByPkCodes = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("123");
        pkCodeInfo.setItemCode("itemCode");
        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        pkCodeInfo2.setPkCode("456");
        pkCodeInfo2.setItemCode("itemCode");
        listByPkCodes.add(pkCodeInfo);
        listByPkCodes.add(pkCodeInfo2);
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(anyList())).thenReturn(listByPkCodes);
        try {
            service.pdaReceiveCheckItem("23","45");
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.PK_CODE_NOT_EXISTS, e.getExMsgId());
        }

        try {
            service.pdaReceiveCheckItem("123","45");
        } catch (MesBusinessException e){
            Assert.assertEquals(MessageId.PK_CODE_NOT_EXISTS, e.getExMsgId());
        }
        Assert.assertNull(service.pdaReceiveCheckItem("123","456"));
        pkCodeInfo2.setItemCode("itemCode2");
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(anyList())).thenReturn(listByPkCodes);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-factory-id", "2");
        headerMap.put("x-emp-no", "123");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");

        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(null);
        Assert.assertNotNull(service.pdaReceiveCheckItem("123","456"));

        List<SmtMachineMaterialMouting> mountingList = new ArrayList<>();
        SmtMachineMaterialMouting mounting = new SmtMachineMaterialMouting();
        mounting.setObjectId("ceshi001");
        mounting.setWorkOrder("7000614-SMT-A5301");
        mountingList.add(mounting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(mountingList);
        PowerMockito.when(smtSnMtlTracingTService.getLineInfoMap(Mockito.any())).thenReturn(null);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.anyString()))
                .thenReturn(null);
        Assert.assertNotNull(service.pdaReceiveCheckItem("123","456"));

        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setSourceTask("7777666");
        psEntityPlanBasic.setItemNo("123456789ABC");
        psEntityPlanBasic.setWorkOrderStatus(Constant.IS_START);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(Mockito.anyString()))
                .thenReturn(psEntityPlanBasic);

        PowerMockito.doNothing().when(imesPDACommonService).insertMtlHistoryForPda(Mockito.any(), Mockito.any());

        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(null);
        service.pdaReceiveCheckItem("123","456");

        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(new SmtLocationInfo(){{
                    setLocationNo("1234");
                }});
        service.pdaReceiveCheckItem("123","456");
    }


    @Test
    public void addRemainingTimeAndAmountTest(){
        List<SmtMachineMaterialMouting> list = new ArrayList<>();
        service.addRemainingTimeAndAmount(list);
        Assert.assertEquals(list.size(),0);

        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setWorkOrder("111");
        mouting.setMachineNo("111");
        mouting.setLocationNo("111");
        mouting.setModuleNo("111");
        mouting.setItemCode("111");
        mouting.setQty(new BigDecimal(500));
        list.add(mouting);
        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.any()))
                .thenReturn(new ArrayList<>());
        service.addRemainingTimeAndAmount(list);
        Assert.assertEquals(list.size(),1);

        SmtMachineMaterialMouting mouting2 = new SmtMachineMaterialMouting();
        mouting2.setWorkOrder("222");
        mouting2.setMachineNo("222");
        mouting2.setLocationNo("222");
        mouting2.setModuleNo("222");
        mouting2.setItemCode("222");
        SmtMachineMaterialMouting mouting3 = new SmtMachineMaterialMouting();
        mouting3.setWorkOrder("444");
        mouting3.setMachineNo("444");
        mouting3.setLocationNo("444");
        mouting3.setModuleNo("444");
        mouting3.setItemCode("444");
        SmtMachineMaterialMouting mouting4 = new SmtMachineMaterialMouting();
        mouting4.setWorkOrder("555");
        mouting4.setMachineNo("555");
        mouting4.setLocationNo("555");
        mouting4.setModuleNo("555");
        mouting4.setItemCode("555");
        SmtMachineMaterialMouting mouting5 = new SmtMachineMaterialMouting();
        mouting5.setWorkOrder("666");
        mouting5.setMachineNo("666");
        mouting5.setLocationNo("666");
        mouting5.setModuleNo("666");
        mouting5.setItemCode("666");
        mouting5.setQty(new BigDecimal(50));
        SmtMachineMaterialMouting mouting6 = new SmtMachineMaterialMouting();
        mouting6.setWorkOrder("777");
        mouting6.setMachineNo("777");
        mouting6.setLocationNo("777");
        mouting6.setModuleNo("777");
        mouting6.setItemCode("777");
        mouting6.setQty(new BigDecimal(50));

        list.add(mouting2);
        list.add(mouting3);
        list.add(mouting4);
        list.add(mouting5);
        list.add(mouting6);


        List<PsWorkOrderBasicDTO> itemNoByWorkOrderNoList = new ArrayList<>();
        PsWorkOrderBasicDTO basicDTO = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO basicDTO2 = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO basicDTO3 = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO basicDTO4 = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO basicDTO5 = new PsWorkOrderBasicDTO();
        basicDTO.setWorkOrderNo("111");
        basicDTO2.setWorkOrderNo("222");
        basicDTO3.setWorkOrderNo("444");
        basicDTO4.setWorkOrderNo("555");
        basicDTO5.setWorkOrderNo("");
        basicDTO.setItemNo("111");
        basicDTO2.setItemNo("222");
        basicDTO3.setItemNo("444");
        basicDTO4.setItemNo("");
        itemNoByWorkOrderNoList.add(basicDTO);
        itemNoByWorkOrderNoList.add(basicDTO2);
        itemNoByWorkOrderNoList.add(basicDTO3);
        itemNoByWorkOrderNoList.add(basicDTO4);
        itemNoByWorkOrderNoList.add(basicDTO5);

        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.any()))
                .thenReturn(itemNoByWorkOrderNoList);
        PowerMockito.when(BasicsettingRemoteService.getBManufactureCapacityInfo(Mockito.any(),Mockito.any()))
                .thenReturn(null);
        service.addRemainingTimeAndAmount(list);
        Assert.assertEquals(list.size(),6);

        BManufactureCapacityDTO capacityDTO = new BManufactureCapacityDTO();
        capacityDTO.setItemCode("111");
        capacityDTO.setUph("0.8");
        BManufactureCapacityDTO capacityDTO1 = new BManufactureCapacityDTO();
        capacityDTO1.setItemCode("111");
        capacityDTO1.setUph("0.8");
        BManufactureCapacityDTO capacityDTO2 = new BManufactureCapacityDTO();
        capacityDTO2.setItemCode("222");
        capacityDTO2.setUph("");
        BManufactureCapacityDTO capacityDTO3 = new BManufactureCapacityDTO();
        capacityDTO3.setItemCode("22");
        capacityDTO3.setUph("");
        BManufactureCapacityDTO capacityDTO5 = new BManufactureCapacityDTO();
        capacityDTO5.setItemCode("444");
        capacityDTO5.setUph("1");
        BManufactureCapacityDTO capacityDTO6 = new BManufactureCapacityDTO();
        capacityDTO6.setItemCode("555");
        capacityDTO6.setUph("2");
        BManufactureCapacityDTO capacityDTO7 = new BManufactureCapacityDTO();
        capacityDTO7.setItemCode("666");
        capacityDTO7.setUph("");
        BManufactureCapacityDTO capacityDTO8 = new BManufactureCapacityDTO();
        capacityDTO8.setItemCode("");
        capacityDTO8.setUph("");

        List<BManufactureCapacityDTO> bManufactureCapacityList = new ArrayList<>();
        bManufactureCapacityList.add(capacityDTO);
        bManufactureCapacityList.add(capacityDTO1);
        bManufactureCapacityList.add(capacityDTO2);
        bManufactureCapacityList.add(capacityDTO3);
        bManufactureCapacityList.add(capacityDTO5);
        bManufactureCapacityList.add(capacityDTO6);
        bManufactureCapacityList.add(capacityDTO7);
        bManufactureCapacityList.add(capacityDTO8);

        PowerMockito.when(PlanscheduleRemoteService.getItemNoByWorkOrderNoList(Mockito.any()))
                .thenReturn(itemNoByWorkOrderNoList);
        PowerMockito.when(BasicsettingRemoteService.getBManufactureCapacityInfo(Mockito.any(),Mockito.any()))
                .thenReturn(bManufactureCapacityList);
        PowerMockito.when(smtBomDetailRepository.getQtyByMachineLocationModuleNo(Mockito.any()))
                .thenReturn(null);
        service.addRemainingTimeAndAmount(list);
        Assert.assertEquals(list.size(),6);

        PowerMockito.when(BasicsettingRemoteService.getBManufactureCapacityInfo(Mockito.any(),Mockito.any()))
                .thenReturn(bManufactureCapacityList);
        List<BSmtBomDetailDTO> dtoList = new ArrayList<>();
        BSmtBomDetailDTO detailDTO = new BSmtBomDetailDTO();
        detailDTO.setMachineNo("111");
        detailDTO.setLocationNo("111");
        detailDTO.setModuleNo("111");
        detailDTO.setQty(new BigDecimal("500"));
        BSmtBomDetailDTO detailDTO2 = new BSmtBomDetailDTO();
        detailDTO2.setMachineNo("222");
        detailDTO2.setLocationNo("222");
        detailDTO2.setModuleNo("222");
        detailDTO2.setQty(new BigDecimal("200"));
        BSmtBomDetailDTO detailDTO3 = new BSmtBomDetailDTO();
        detailDTO3.setMachineNo("11");
        detailDTO3.setLocationNo("11");
        detailDTO3.setModuleNo("11");
        detailDTO3.setQty(null);
        BSmtBomDetailDTO detailDTO4 = new BSmtBomDetailDTO();
        detailDTO4.setMachineNo("111");
        detailDTO4.setLocationNo("111");
        detailDTO4.setModuleNo("111");
        detailDTO4.setQty(new BigDecimal("500"));
        BSmtBomDetailDTO detailDTO5 = new BSmtBomDetailDTO();
        detailDTO5.setMachineNo("444");
        detailDTO5.setLocationNo("444");
        detailDTO5.setModuleNo("444");
        BSmtBomDetailDTO detailDTO6 = new BSmtBomDetailDTO();
        detailDTO6.setMachineNo("555");
        detailDTO6.setLocationNo("555");
        detailDTO6.setModuleNo("555");
        BSmtBomDetailDTO detailDTO7 = new BSmtBomDetailDTO();
        detailDTO7.setMachineNo("666");
        detailDTO7.setLocationNo("666");
        detailDTO7.setModuleNo("666");
        dtoList.add(detailDTO);
        dtoList.add(detailDTO2);
        dtoList.add(detailDTO3);
        dtoList.add(detailDTO4);
        dtoList.add(detailDTO5);
        dtoList.add(detailDTO6);
        dtoList.add(detailDTO7);

        PowerMockito.when(smtBomDetailRepository.getQtyByMachineLocationModuleNo(Mockito.any()))
                .thenReturn(dtoList);
        service.addRemainingTimeAndAmount(list);
        Assert.assertEquals(list.size(),6);

    }

}
