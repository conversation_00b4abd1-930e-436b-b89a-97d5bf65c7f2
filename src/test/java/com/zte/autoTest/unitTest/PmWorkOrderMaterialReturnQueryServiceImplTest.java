package com.zte.autoTest.unitTest;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.QueryAndSetPropertyLineNameService;
import com.zte.application.impl.PmWorkOrderMaterialReturnQueryServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.*;
import com.zte.domain.model.PmWorkOrderMaterialReturnQuery;
import com.zte.domain.model.PmWorkOrderMaterialReturnQueryRepository;
import com.zte.domain.model.PsTask;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.PmWorkOrderMaterialReturnQueryDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class, RedisHelper.class,RedisLock.class,
		EasyExcelFactory.class,ImesExcelUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,ObtainRemoteServiceDataUtil.class,PlanscheduleRemoteService.class})
public class PmWorkOrderMaterialReturnQueryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PmWorkOrderMaterialReturnQueryServiceImpl service;

    @Mock
    PmWorkOrderMaterialReturnQueryRepository pmWorkOrderMaterialReturnQueryRepository;
	@Mock
	QueryAndSetPropertyLineNameService queryAndSetPropertyLineNameService;
	@Mock
	HttpServletRequest request;
	@Mock
	HttpServletResponse response;
	@Mock
	private ExcelWriter excelWriter;
	@Mock
	private WriteSheet build;
	@Mock
	private ExcelWriterSheetBuilder excelWriterSheetBuilder;
	@Mock
	private ExcelWriterBuilder write;
	@Mock
	private ExcelWriterBuilder excelWriterBuilder;
	@Mock
	private CloudDiskHelper cloudDiskHelper;
	@Mock
	private CenterfactoryRemoteService centerfactoryRemoteService;
	@Mock
	EmailUtils emailUtils;

    @Test
    public void updateForCenter() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
        PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
        pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
        pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
        ServiceData serviceData = new ServiceData();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));

        Assert.assertNull(Whitebox.invokeMethod(service, "setItemNoAndProdplanNo", pmWorkOrderMaterialReturnQueries));
    }

    @Test
    public void getPage() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
        PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
        pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
        pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
        ServiceData serviceData = new ServiceData();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));

        PmWorkOrderMaterialReturnQueryDTO dto = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
        PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getPage(Mockito.anyObject()))
                .thenReturn(pmWorkOrderMaterialReturnQueries);
		Assert.assertNotNull(service.getPage(dto));
		dto.setSourceTask("1");
		Assert.assertNotNull(service.getPage(dto));
    }

	@Test
	public void exportMaterialReturnInfo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-12 00:00:00");
		record.setTimeStart("2023-04-14 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery.setStatus(new BigDecimal(0));
		pmWorkOrderMaterialReturnQueries.add(pmWorkOrderMaterialReturnQuery);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);

		Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries);

		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		record.setSourceTask("1");
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
		PmWorkOrderMaterialReturnQueryDTO record1 = new PmWorkOrderMaterialReturnQueryDTO();
		record1.setLineCode("TEST");
		record1.setFileNamePrefix("TEST+");
		try {
			service.exportMaterialReturnInfo(request,response,record1,"52","00286569");
		} catch (Exception e) {
			Assert.assertEquals(MessageId.TIME_CAN_NOT_BE_NULL, e.getMessage());
		}
		record1.setTimeEnd("2023-04-22 00:00:00");
		record1.setTimeStart("2023-04-12 00:00:00");
		try {
			service.exportMaterialReturnInfo(request,response,record1,"52","00286569");
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SPAN_WHITIN_7DAYS, e.getMessage());
		}
	}

	@Test
	public void exportMaterialReturnInfoTwo() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");

		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries2 = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery2 = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery2.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery2.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery2.setStatus(new BigDecimal(2));
		pmWorkOrderMaterialReturnQueries2.add(pmWorkOrderMaterialReturnQuery2);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries2);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);

		Assert.assertNull(Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries2));
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
	}

	@Test
	public void exportMaterialReturnInfoThree() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

		List<PmWorkOrderMaterialReturnQuery> pmWorkOrderMaterialReturnQueries2 = new ArrayList<>();
		PmWorkOrderMaterialReturnQuery pmWorkOrderMaterialReturnQuery2 = new PmWorkOrderMaterialReturnQuery();
		pmWorkOrderMaterialReturnQuery2.setWorkOrder("N");
		pmWorkOrderMaterialReturnQuery2.setLineCode("TEST");
		pmWorkOrderMaterialReturnQuery2.setStatus(new BigDecimal(1));
		pmWorkOrderMaterialReturnQueries2.add(pmWorkOrderMaterialReturnQuery2);
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoPage(Mockito.any()))
				.thenReturn(pmWorkOrderMaterialReturnQueries2);

		List<PsTask> psTaskList = new ArrayList<>();
		PsTask psTask = new PsTask();
		psTask.setWorkOrderNo("N");
		psTaskList.add(psTask);

		PowerMockito.when(PlanscheduleRemoteService.getPsTaskListByWorkOrderNos(Mockito.anyList())).thenReturn(psTaskList);
		Assert.assertNull(Whitebox.invokeMethod(queryAndSetPropertyLineNameService, "setPropertyLineNameForMaterialReturnQuery",new BigDecimal("52"),pmWorkOrderMaterialReturnQueries2));
		service.exportMaterialReturnInfo(request,response,record,"52","00286569");
	}

	@Test
	public void exportMaterialReturnInfoFour() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class,RedisHelper.class, RedisLock.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(50001);

		PowerMockito.when(RedisHelper.setnx(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(true);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
		try {
			service.exportMaterialReturnInfo(request, response, record, "52", "00286569");
		} catch (Exception e) {
			Assert.assertNotNull(e.getMessage());
		}
	}

	@Test
	public void exportMaterialReturnInfoFive() throws Exception {
		PowerMockito.mockStatic(EasyExcelFactory.class);
		PowerMockito.mockStatic(ImesExcelUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class,PlanscheduleRemoteService.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(0);
		PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), PmWorkOrderMaterialReturnQuery.class)).thenReturn(write);
		PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
		PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

		PowerMockito.when(EasyExcelFactory.writerSheet(0, record.getFileNamePrefix() + Constant.MATERIAL_RETURN_INFO)).thenReturn(excelWriterSheetBuilder);
		PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
		try {
			service.exportMaterialReturnInfo(request, response, record, "52", "00286569");
		} catch (Exception e) {
			Assert.assertNotNull(e.getMessage());
		}
	}

	@Test
	public void getInfoCountForExport() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getInfoCountForExport(Mockito.any()))
				.thenReturn(1);
		Assert.assertTrue(service.getInfoCountForExport(record) > 0);
		record.setSourceTask("1");
		Assert.assertTrue(service.getInfoCountForExport(record) > 0);
	}

	@Test
	public void getCount() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
			setWorkOrderNo("1");
		}}));
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		PowerMockito.when(pmWorkOrderMaterialReturnQueryRepository.getCount(Mockito.any()))
				.thenReturn(1L);
		Assert.assertTrue(service.getCount(record) > 0);
		record.setSourceTask("1");
		Assert.assertTrue(service.getCount(record) > 0);
	}

	@Test
	public void getCountTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		PmWorkOrderMaterialReturnQueryDTO record = new PmWorkOrderMaterialReturnQueryDTO();
		record.setLineCode("TEST");
		record.setFileNamePrefix("TEST+");
		record.setTimeEnd("2023-04-14 00:00:00");
		record.setTimeStart("2023-04-12 00:00:00");
		record.setSourceTask("1");
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList());
		Assert.assertNotNull(service.getCount(record));
	}
}
