package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.common.ConstantInterface;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.anyMap;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/10/14 15:54
 */

@PrepareForTest({JSON.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class,
        ServiceDataBuilderUtil.class, MESHttpHelper.class, HttpRemoteService.class, ConstantInterface.class, HttpRemoteUtil.class})
public class BasicsettingRemoteServiceTest extends PowerBaseTestCase {

    @Before
    public void init() {
        mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void checkCount() {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(BasicsettingRemoteService.checkCount(Maps.newHashMap()));
    }

    @Test
    public void getList() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        BasicsettingRemoteService.findListByAttribute1("");
        BasicsettingRemoteService.getBBomHeaderByProductCodes("");
        BasicsettingRemoteService.getBBomTechnicalChangeInfoList(Maps.newHashMap());
        BasicsettingRemoteService.getBManufactureCapacityInfo("", "", "", "");
        BasicsettingRemoteService.getBomListofDIP(Maps.newHashMap());
        BasicsettingRemoteService.getBomListofDipNew(Maps.newHashMap());
        BasicsettingRemoteService.getLineJsonByWorkshopCode(Maps.newHashMap());
        BasicsettingRemoteService.getLookupValueByTypeCodes("");
        BasicsettingRemoteService.getStyleInfo(Maps.newHashMap());
        BasicsettingRemoteService.getStyleInfo("");
        BasicsettingRemoteService.zsUniteInterface(JacksonJsonConverUtil.getMapperInstance().readTree(""));
        BasicsettingRemoteService.getListByLookupType(Maps.newHashMap());
        BasicsettingRemoteService.getLocationList("");
        BasicsettingRemoteService.getLookupValueByTypeCodes(Maps.newHashMap());
        Assert.assertNull(BasicsettingRemoteService.getSysLookupValuesList(new SysLookupValuesDTO()));
    }

    @Test
    public void checkExistVerNo() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        BasicsettingRemoteService.checkExistVerNo("111");
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        BasicsettingRemoteService.checkExistVerNo("111");
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(BasicsettingRemoteService.checkExistVerNo("111"));
    }

    @Test
    public void getRows() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("{}");
        mockStatic(ConstantInterface.class);
        when(ConstantInterface.getUrlStatic(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-datawbsys/mtlSystemItems/getItemIdBySegment1/222");
        mockStatic(HttpRemoteUtil.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new Page() {{
                        setRows(Lists.newArrayList());
                    }});
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Map map = new HashMap();
        List<BBomDetailDTO> detailDTOList = new ArrayList<>();
        BBomDetailDTO dto = new BBomDetailDTO();
        detailDTOList.add(dto);
        map.put("rows", detailDTOList);
        when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(map);
                }})
        );
        BasicsettingRemoteService.getBbomDetail("");
        BasicsettingRemoteService.getAllFactory();
        BasicsettingRemoteService.getFactory(Maps.newHashMap());
        BasicsettingRemoteService.getLine(Maps.newHashMap());
        BasicsettingRemoteService.getLineByWorkshopCode(Maps.newHashMap());
        BasicsettingRemoteService.getItemInfo("");
        BasicsettingRemoteService.getFactoryInfo("");
        Assert.assertNotNull(BasicsettingRemoteService.getLocationNo(""));
    }

    @Test
    public void getBsItemInfo() {
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        bsItemInfoList.add(new BsItemInfo());
        Assert.assertNotNull(BasicsettingRemoteService.getBsItemInfo("2", bsItemInfoList));
    }

    @Test
    public void getSmtLocationLineInfo() {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new HashMap());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(BasicsettingRemoteService.getSmtLocationLineInfo("1"));
    }

    @Test
    public void asmInfoQuery() throws MesBusinessException {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new SmtLocationInfo());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull(BasicsettingRemoteService.asmInfoQuery("1", "2", "3", "4"));
    }

    @Test
    public void getItemType() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class, HttpRemoteUtil.class);
        when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    List<BsItemInfo> bsItemInfoList = new ArrayList<>();
                    BsItemInfo bsItemInfo = new BsItemInfo();
                    bsItemInfo.setItemNo("123");
                    bsItemInfo.setItemType("1");
                    bsItemInfoList.add(bsItemInfo);
                    setBo(bsItemInfoList);
                }})
        );
        when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        List<String> itemList = new ArrayList<>();
        Assert.assertNotNull(BasicsettingRemoteService.getItemType(itemList));
    }

    @Test
    public void getBBomHeaderByProductCodeList() throws Exception {
        mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(
                anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            List<BBomHeaderDTO> bomHeaderDTOList = new ArrayList<>();
                            BBomHeaderDTO dto = new BBomHeaderDTO();
                            dto.setBomHeaderId("123");
                            bomHeaderDTOList.add(dto);
                            setBo(bomHeaderDTOList);
                        }}))
                );
        when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        List<String> bomNoList = new ArrayList<>();
        bomNoList.add("123");
        Assert.assertNotNull(BasicsettingRemoteService.getBBomHeaderByProductCodeList(bomNoList));
    }

    @Test
    public void getSmtAndDipLine() throws Exception {
        mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(
                anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            List<CFLine> cfLineList = new ArrayList<>();
                            CFLine cfLine = new CFLine();
                            cfLine.setLineCode("DIP1");
                            cfLine.setLineName("DIP1");
                            cfLineList.add(cfLine);
                            setBo(cfLineList);
                        }}))
                );
        when(ConstantInterface.getUrlStatic(any())).thenReturn("imes.zte.com.cn");
        Assert.assertNotNull(BasicsettingRemoteService.getSmtAndDipLine());
    }

    @Test
    public void getBatchSysValueByCode() throws Exception {
        List<String> lookUpCodeList = new LinkedList<>();
        mockStatic(JSON.class);
        mockStatic(MESHttpHelper.class);
        mockStatic(MicroServiceRestUtil.class);
        mockStatic(JacksonJsonConverUtil.class);
        mockStatic(ServiceDataBuilderUtil.class);

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("00");
        Assert.assertNotNull(BasicsettingRemoteService.getBatchSysValueByCode(lookUpCodeList));
    }


    @Test
    public void getWorkGroupBatch() throws Exception {
        List<String> lookUpCodeList = new LinkedList<>();
        mockStatic(JSON.class);
        mockStatic(MESHttpHelper.class);
        mockStatic(MicroServiceRestUtil.class);
        mockStatic(JacksonJsonConverUtil.class);
        mockStatic(ServiceDataBuilderUtil.class);

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("");
        BasicsettingRemoteService.getWorkGroupBatch(lookUpCodeList);

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("00");
        BasicsettingRemoteService.getWorkGroupBatch(lookUpCodeList);

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("[\n" +
                "    {\n" +
                "        \"groupCode\": \"YYY221208CF01-z111\",\n" +
                "        \"groupName\": \"129574451006ABB\",\n" +
                "        \"factoryId\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"groupCode\": \"YYY221208CF01-z222\",\n" +
                "        \"groupName\": \"129574451006ABB\",\n" +
                "        \"factoryId\": 4\n" +
                "    },\n" +
                "    {\n" +
                "        \"groupCode\": \"YYY221208CF01-z33\",\n" +
                "        \"groupName\": \"129574451006ABB\",\n" +
                "        \"factoryId\": 25\n" +
                "    }\n" +
                "]");
        Assert.assertNotNull(BasicsettingRemoteService.getWorkGroupBatch(lookUpCodeList));
    }

    @Test
    public void getSetting() throws Exception {
        List<SysLookupValuesDTO> settings = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        settings.add(sysLookupValuesDTO1);
        sysLookupValuesDTO1.setLookupCode(BigDecimal.TEN);
        sysLookupValuesDTO1.setLookupMeaning("imes_xa");
        SysLookupValuesDTO sysLookupValuesDTO2 = new SysLookupValuesDTO();
        settings.add(sysLookupValuesDTO2);
        sysLookupValuesDTO2.setLookupCode(BigDecimal.valueOf(11));
        sysLookupValuesDTO2.setLookupMeaning("imes_xa");
        String res1 = BasicsettingRemoteService.getSetting(settings, "11", "aaa");
        Assert.assertEquals("imes_xa", res1);
        String res2 = BasicsettingRemoteService.getSetting(settings, "12", "aaa");
        Assert.assertEquals("aaa", res2);
    }

}
