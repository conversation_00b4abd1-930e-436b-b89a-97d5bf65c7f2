package com.zte.autoTest.unitTest;

import com.zte.common.excel.ExcelCommonUtils;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
@PrepareForTest({FileUtils.class})
public class ExcelCommonUtilsTest extends PowerBaseTestCase {
    @Test
    public void saveFile() throws Exception {
        PowerMockito.mockStatic(FileUtils.class);
        Assert.assertNotNull(ExcelCommonUtils.saveFile(new ArrayList<>(),"xxs.xlsx","xx.xlsx"));
    }
}
