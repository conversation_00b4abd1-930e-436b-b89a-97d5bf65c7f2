package com.zte.autoTest.unitTest;

import com.zte.application.PsWipInfoService;
import com.zte.application.impl.SpotCheckServiceImpl;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.interfaces.assembler.PsWipInfoAssembler;
import com.zte.interfaces.assembler.SpotCheckDetailAssembler;
import com.zte.interfaces.assembler.SpotCheckHeadAssembler;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-14 11:13
 */
@PrepareForTest({ExcelCommonUtils.class, SpotCheckDetailAssembler.class, SpotCheckHeadAssembler.class,
        PsWipInfoAssembler.class})
public class SpotCheckServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private SpotCheckServiceImpl spotCheckServiceImpl;
    @Mock
    private HttpServletResponse response;
    @Mock
    private SpotCheckDetailRepository spotCheckDetailRepository;
    @Mock
    private SpotCheckHeadRepository spotCheckHeadRepository;
    @Mock
    private PsWipInfoService psWipInfoService;

    @Before
    public void init() {
        PowerMockito.mockStatic(ExcelCommonUtils.class);
        PowerMockito.mockStatic(PsWipInfoAssembler.class);
        PowerMockito.mockStatic(SpotCheckDetailAssembler.class);
        PowerMockito.mockStatic(SpotCheckHeadAssembler.class);
    }

    @Test
    public void exportExcel() throws Exception {
        SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try{
            spotCheckServiceImpl.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.CONDITION_NOT_FOUND, e.getMessage());
        }
        dto.setProdplanId("345");
        dto.setLineCode("23");

        List<SpotCheckDetail> spotCheckDetails = new LinkedList<>();
        SpotCheckDetail a1 = new SpotCheckDetail();
        spotCheckDetails.add(a1);
        PowerMockito.when(spotCheckDetailRepository.getSpotCheckDetail(Mockito.any()))
                .thenReturn(spotCheckDetails);

        spotCheckServiceImpl.exportExcel(response, dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void createSpotCheck() throws Exception {
        SpotCheckDTO dto = new SpotCheckDTO();
        SpotCheckHeadDTO head = new SpotCheckHeadDTO();
        head.setQty(3L);
        List<SpotCheckDetailDTO> details = new LinkedList<>();
        SpotCheckDetailDTO a1 = new SpotCheckDetailDTO();
        a1.setSn("123");
        details.add(a1);

        FlowControlConditionDTO flow = new FlowControlConditionDTO();
        dto.setFlow(flow);
        dto.setDetails(details);
        dto.setHead(head);

        List<SpotCheckDetail> spotCheckDetails = new LinkedList<>();
        PowerMockito.when(SpotCheckDetailAssembler.toSpotCheckDetailList(Mockito.anyList()))
                .thenReturn(spotCheckDetails);

        SpotCheckHead spotCheckHead = new SpotCheckHead();
        spotCheckHead.setQty(3l);
        PowerMockito.when(SpotCheckHeadAssembler.toEntity(Mockito.any()))
                .thenReturn(spotCheckHead)
        ;

        List<PsWipInfoDTO> wipInfoDTOList = new LinkedList<>();
        PsWipInfoDTO b1 = new PsWipInfoDTO();
        b1.setSn("123");
        wipInfoDTOList.add(b1);
        PowerMockito.when(PsWipInfoAssembler.toPsWipInfoDTOList(Mockito.anyList()))
                .thenReturn(wipInfoDTOList)
        ;

        Assert.assertNotNull(spotCheckServiceImpl.createSpotCheck(dto));
    }
}
