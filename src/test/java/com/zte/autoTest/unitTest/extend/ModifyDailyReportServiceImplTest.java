package com.zte.autoTest.unitTest.extend;

import com.zte.application.IMESLogService;
import com.zte.application.impl.extend.ModifyDailyReportServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.WipDailyStatisticReport;
import com.zte.domain.model.WipDailyStatisticReportRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarSubmitDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-21 15:05
 */
public class ModifyDailyReportServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ModifyDailyReportServiceImpl modifyDailyReportServiceImpl;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private WipDailyStatisticReportRepository wipDailyStatisticReportRepository;
    @Mock
    private IMESLogService imesLogService;

    @Before
    public void init() {

    }

    @Test
    public void compensationDailyReport() {
        List<WipDailyStatisticReport> reports = new LinkedList<>();
        WipDailyStatisticReport a1 = new WipDailyStatisticReport();
        a1.setProdplanId("8899726");
        a1.setTaskQty(2);
        a1.setTaskNo("8899726-2");
        reports.add(a1);
        WipDailyStatisticReport a2 = new WipDailyStatisticReport();
        a2.setProdplanId("8899727");
        a2.setTaskNo("8899726-1");
        a2.setTaskQty(1);
        reports.add(a2);

        PowerMockito.when(wipDailyStatisticReportRepository.queryWipDailyPage(Mockito.any()))
                .thenReturn(reports)
        ;
        List<BarSubmitDTO> submitList = new LinkedList<>();
        BarSubmitDTO b1 = new BarSubmitDTO();
        b1.setProdplanId(8899726L);
        b1.setStatus(1L);
        b1.setQty(2l);
        submitList.add(b1);
        BarSubmitDTO b2 = new BarSubmitDTO();
        b2.setProdplanId(8899727L);
        b2.setStatus(1L);
        b2.setVerifyDate(new Date(System.currentTimeMillis() - Constant.ONE_DAY_MILLIS));
        submitList.add(b2);

        PowerMockito.when(datawbRemoteService.dataWbGetBarSubmitByPlanIds(Mockito.anyList()))
                .thenReturn(submitList)
        ;
        List<PsTask> list = new LinkedList<>();
        PsTask c1 = new PsTask();
        c1.setProdplanId("8899726");
        list.add(c1);
        PsTask c2 = new PsTask();
        c2.setProdplanId("8899727");
        list.add(c2);
        PowerMockito.when(planscheduleRemoteService.getTaskListByProdPlanId(Mockito.anyList()))
                .thenReturn(list);

        List<PsTask> prodPlanGetDateList = new LinkedList<>();
        PsTask d1 = new PsTask();
        d1.setGetDate(new Date());
        d1.setProdPlanNo("8899726-1");
        prodPlanGetDateList.add(d1);
        PowerMockito.when(datawbRemoteService.getProdPlanGetDate(Mockito.anyList()))
                .thenReturn(prodPlanGetDateList)
        ;
        Assert.assertEquals("8899726-1", d1.getProdPlanNo());
        modifyDailyReportServiceImpl.compensationDailyReport();
    }
}
