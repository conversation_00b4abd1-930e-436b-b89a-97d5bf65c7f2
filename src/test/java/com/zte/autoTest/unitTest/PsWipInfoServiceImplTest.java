package com.zte.autoTest.unitTest;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PsBarcodeControlInfoService;
import com.zte.application.PsCommonScanService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WhiteListInfoService;
import com.zte.application.WipInfoDelLogService;
import com.zte.application.WipRepairSnService;
import com.zte.application.WipSplitScanTempService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.CraftConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsBarcodeControlInfo;
import com.zte.domain.model.PsOutputInfo;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipEntityInfo;
import com.zte.domain.model.WipEntityInfoRepository;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.domain.model.WipSplitScanTemp;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.BSProcessInfoDTO;
import com.zte.interfaces.dto.BsWorkTimeSectionDTO;
import com.zte.interfaces.dto.FlowControlConditionDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PcProcessTransferDTO;
import com.zte.interfaces.dto.PcProcessTransferSimpleDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PmSubmitConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsEntityPlanTestDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.PsWipInfoQueryDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SnUnBindDTO;
import com.zte.interfaces.dto.SnUnBindQueryDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskInstructionStatusDTO;
import com.zte.interfaces.dto.WarehouseEntryFlowInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@PrepareForTest({CommonUtils.class, BasicsettingRemoteService.class,
        MicroServiceRestUtil.class, HttpRemoteService.class, HttpClientUtil.class, PlanscheduleRemoteService.class,
        ObtainRemoteServiceDataUtil.class, ProductionDeliveryRemoteService.class, MicroServiceDiscoveryInvoker.class,
        ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class, SpringContextUtil.class,JacksonJsonConverUtil.class,
        ThreadUtil.class, BeanUtils.class})
public class PsWipInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PsWipInfoServiceImpl service;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private WipSplitScanTempService wipSplitScanTempService;
    @Mock
    private WhiteListInfoService whiteListInfoService;
    @Mock
    private WipInfoDelLogService wipInfoDelLogService;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private PsBarcodeControlInfoService psBarcodeControlInfoService;
    @Mock
    private WipRepairSnService wipRepairSnService;
    @Mock
    private PsScanHistoryService psScanHistoryService;
    @Mock
    private BsWorkTimeSectionService bsWorkTimeSectionService;
    @Mock
    private PsOutputInfoService psOutputInfoService;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private WipEntityInfoRepository wipEntityInfoRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;
    @Mock
    private PsCommonScanService psCommonScanService;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    Future future;

    @Before
    public void init(){
        PowerMockito.mockStatic(CrafttechRemoteService.class);
    }
    @Test
    public void getPcProcessTransferSimpleDTO() throws Exception {
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        PcProcessTransferSimpleDTO ptObj = new PcProcessTransferSimpleDTO();
        String checkDipFinish = ""; String[] toProcessCodeList = {};
        String toWorkOrderNo = "";
        PcProcessTransferSimpleDTO pcProcessTransferSimpleDTO = service.getPcProcessTransferSimpleDTO(dto,ptObj,checkDipFinish,toProcessCodeList,toWorkOrderNo);
        Assert.assertNull(pcProcessTransferSimpleDTO);
        List<String> notExistSnList = new ArrayList<>();
        for (int i = 0; i < 400; i++) {
            notExistSnList.add(i+"");
        }
        dto.setNotExistSnList(notExistSnList);
        pcProcessTransferSimpleDTO = service.getPcProcessTransferSimpleDTO(dto,ptObj,checkDipFinish,toProcessCodeList,toWorkOrderNo);
        Assert.assertNull(pcProcessTransferSimpleDTO);

        for (int i = 0; i < 400; i++) {
            notExistSnList.add(i+"");
        }
        dto.setNotExistSnList(notExistSnList);
        pcProcessTransferSimpleDTO = service.getPcProcessTransferSimpleDTO(dto,ptObj,checkDipFinish,toProcessCodeList,toWorkOrderNo);
        Assert.assertNull(pcProcessTransferSimpleDTO);

    }
    @Test
    public void writeBackWorkOrderToStep() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/databack/flow/add");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<PsEntityPlanBasicDTO> psEntityPlanBasicDTOS = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setWorkOrderNo("7777888");
        PsEntityPlanTestDTO psEntityPlanTestDTO = new PsEntityPlanTestDTO();
        psEntityPlanTestDTO.setProdplanId("77788889");
        psEntityPlanBasicDTO.setPsEntityPlanTest(psEntityPlanTestDTO);
        psEntityPlanBasicDTOS.add(psEntityPlanBasicDTO);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.anyString()))
        .thenReturn(psEntityPlanBasicDTOS);
        PmSubmitConditionDTO pmSubmitConditionDTO = new PmSubmitConditionDTO();
        pmSubmitConditionDTO.setWorker("111");
        pmSubmitConditionDTO.setWorkOrderNo("7777888");
        Assert.assertNotNull(service.writeBackWorkOrderToStep(pmSubmitConditionDTO));
    }

    @Test
    public void getSNAttributeInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[]\n" +
                "  \n" +
                "}");
        List<WipExtendIdentification> wipExtendIdentifications = new LinkedList<>();
        WipExtendIdentification a1 = new WipExtendIdentification();
        wipExtendIdentifications.add(a1);
        PowerMockito.when(wipExtendIdentificationRepository.getList(Mockito.any()))
                .thenReturn(wipExtendIdentifications);

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo b1 = new PsWipInfo();
        psWipInfoList.add(b1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(psWipInfoList);
        Assert.assertNotNull(service.getSNAttributeInfo("123"));
    }

    @Test
    public void splitSnScan() throws Exception {
        PmScanConditionDTO processInfo = new PmScanConditionDTO();
        processInfo.setSn("123");
        List<WipSplitScanTemp> returnentity = new LinkedList<>();
        WipSplitScanTemp a1 = new WipSplitScanTemp();
        returnentity.add(a1);
        PowerMockito.when(wipSplitScanTempService.selectWipSplitScanTempById(Mockito.any()))
                .thenReturn(returnentity);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        PowerMockito.mockStatic(CommonUtils.class);
        Assert.assertNotNull(service.splitSnScan(processInfo));
    }

    @Test
    public void pmSubmit() throws Exception {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        entity.setWorkOrderNo("123");
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  {\"sn\":\"123\"}]\n" +
                "  \n" +
                "}");
        Assert.assertNull(service.pmSubmit(entity));
    }


    @Test
    public void submitPcProcessTransferToZs() throws Exception {
        List<FlowControlConditionDTO> list = new LinkedList<>();
        FlowControlConditionDTO a1 = new FlowControlConditionDTO();
        list.add(a1);
        FlowControlInfoDTO controlResult = new FlowControlInfoDTO();
        controlResult.setStrStorgeFlag("123");
        controlResult.setFactoryId(new BigDecimal("2"));

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn("[\n" +
                        "  {\"strPartcode\":\"123\",\"strInfo\":\"9\",\"bResult\":true}]");
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(),
                Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  {\"descriptionEng\":\"123\",\"lookupMeaning\":\"9\"}]\n" +
                "  \n" +
                "}");

        Assert.assertNotNull(service.submitPcProcessTransferToZs(list, controlResult));
    }

    @Test
    public void updatePsWorkorderQty() throws Exception {
        PsEntityPlanBasicDTO entityPlanBasic = new PsEntityPlanBasicDTO();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  {\"strPartcode\":\"123\",\"strInfo\":\"9\",\"bResult\":true}]\n" +
                "  \n" +
                "}");
        Assert.assertNotNull(service.updatePsWorkorderQty(entityPlanBasic));

    }

    @Test
    public void pdaWriteOutPut() throws Exception {
        PsOutputInfo entity = new PsOutputInfo();

        BsWorkTimeSectionDTO currWorkTimeDTO = new BsWorkTimeSectionDTO();
        PowerMockito.when(
                bsWorkTimeSectionService.selectBsWorkTimeSectionByLineWorkShopAndFactory(Mockito.any(),
                        Mockito.any(), Mockito.any())).thenReturn(currWorkTimeDTO);

        PowerMockito.when(psOutputInfoService.insertPsOutputInfoSelective(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.pdaWriteOutPut(entity));
    }

    @Test
    public void updateWorkOrder() throws Exception {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":\"k\"\n" +
                "  \n" +
                "}");
        Assert.assertNotNull(service.updateWorkOrder(entity, "123"));
    }

    @Test
    public void warehouseEntryScan() throws Exception {
        PmScanConditionDTO entity = new PmScanConditionDTO();

        List<PsBarcodeControlInfo> listBarCon = new LinkedList<>();
        PowerMockito.when(psBarcodeControlInfoService.selectPsBarcodeControlInfoByBarcodeList(Mockito.any()))
                .thenReturn(listBarCon);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        PowerMockito.mockStatic(CommonUtils.class);
        Assert.assertNotNull(service.warehouseEntryScan(entity));
    }

    @Test
    public void getWarehouseEntryFlowInfoEx() {
        WarehouseEntryFlowInfoDTO warehouseEntryFlowInfo = new WarehouseEntryFlowInfoDTO();
        FlowControlInfoDTO result = new FlowControlInfoDTO();
        result.setResultType("FAIL");
        PsWipInfo psWipInfo = new PsWipInfo();
        result.setWipInfo(psWipInfo);


        Assert.assertNotNull(service.getWarehouseEntryFlowInfoEx(warehouseEntryFlowInfo, result));
    }

    @Test
    public void warehouseEntryBatch() throws Exception {
        List<WarehouseEntryFlowInfoDTO> list = new LinkedList<>();
        WarehouseEntryFlowInfoDTO a1 = new WarehouseEntryFlowInfoDTO();
        a1.setCurrProcessCode("123");
        list.add(a1);

        List<PsWipInfo> list2 = new LinkedList<>();
        PsWipInfo ps = new PsWipInfo();
        ps.setCurrProcessCode("123");
        list2.add(ps);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(list2);

        PowerMockito.mockStatic(CommonUtils.class);

        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[{\"listDetail\":[{\"routeId\":\"routeId\"}]}]\n" +
                "  \n" +
                "}");
        Assert.assertNotNull(service.warehouseEntryBatch(list, "12"));
    }


    @Test
    public void basicScan() throws Exception {
        BSProcessInfoDTO processInfo = new BSProcessInfoDTO();
        processInfo.setProcessName("123");
        processInfo.setProgramName("123$123$123$123");
        processInfo.setInputParameter(Constant.WORK_ORDER_NO);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(
                "{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": {\n" +
                        "  \n" +
                        "  },\n" +
                        "  \"other\": null\n" +
                        "}");
        service.basicScan(processInfo);

        processInfo.setInputParameter(Constant.USER_ID);
        service.basicScan(processInfo);
        processInfo.setInputParameter(Constant.LPN);
        Assert.assertNotNull(service.basicScan(processInfo));

    }

    @Test
    public void pullModel() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(
                "{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "\t{\"currProcess\":\"3\",\"processGroup\":\"2\"}\n" +
                        "  ]\n" +
                        "}");
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        String transferBySn = "ji";
        PcProcessTransferSimpleDTO ptObj = new PcProcessTransferSimpleDTO();
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setProcessGroup("$1$2");
        psEntityPlanInfo.add(a1);
        String checkDipFinish = "ki";
        dto.setIsBoxupFlag("Y");
        service.pullModel(dto,  transferBySn, ptObj, psEntityPlanInfo, checkDipFinish);
        dto.setIsBoxupFlag("n");
        service.pullModel(dto, transferBySn, ptObj, psEntityPlanInfo, checkDipFinish);

        ptObj.setToProcessCode("2");
        service.pullModel(dto, transferBySn, ptObj, psEntityPlanInfo, checkDipFinish);

        ptObj.setToWorkOrderNo("123");
        service.pullModel(dto, "Y", ptObj, psEntityPlanInfo, checkDipFinish);

        ptObj.setToWorkOrderNo("");
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(
                "{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "\t\n" +
                        "  ]\n" +
                        "}");
        Assert.assertNotNull(service.pullModel(dto, "Y", ptObj, psEntityPlanInfo, checkDipFinish));

    }

    @Test
    public void unLinkSnAndTask() throws Exception {
        String sn = "880542130022220217777777700054";
        String parentSn = "777777700054";
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypeList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2");
        sysLookupTypesDTO.setDescriptionChinV("P0227");
        sysLookupTypesDTO.setDescriptionEngV("S1332");
        sysLookupTypeList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypeList);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWipId("123456");
        psWipInfo.setSn(sn);
        psWipInfo.setParentSn(parentSn);
        psWipInfo.setCurrProcessCode("P0227");
        psWipInfo.setWorkStation("S1332");
        psWipInfo.setWorkOrderNo("628805421300-装配5201");
        psWipInfo.setLastProcess("Y");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(Mockito.eq(sn))).thenReturn(psWipInfo);
        PowerMockito.when(whiteListInfoService.getCountWhiteListInfoDetailBySN(Mockito.anyObject())).thenReturn(1L);


        psWipInfo = new PsWipInfo();
        psWipInfo.setWipId("777896c5-2a33-4af8-9b5d-597449ea08df");
        psWipInfo.setCurrProcessCode("P0227");
        psWipInfo.setWorkStation("S1332");
        psWipInfo.setSn(parentSn);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(Mockito.eq(parentSn))).thenReturn(psWipInfo);


        List<PsWorkOrderBasicDTO> sectionList = new ArrayList<>();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO = new PsWorkOrderBasicDTO();
        String processGroup = "P0227";
        String lineCode = "*********";
        psWorkOrderBasicDTO.setProcessGroup(processGroup);
        psWorkOrderBasicDTO.setLineCode(lineCode);
        sectionList.add(psWorkOrderBasicDTO);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(PlanscheduleRemoteService.getProcessCodeAndLineList("628805421300-装配5201")).thenReturn(sectionList);


        PowerMockito.when(PlanscheduleRemoteService.updateOutPutQtyByWorkOrderNo("628805421300", 1L)).thenReturn(1);
        PowerMockito.when(PlanscheduleRemoteService.updateInputQtyByWorkOrderNo("628805421300", 1L)).thenReturn(1);

        List<String> snList = new ArrayList<>();
        snList.add("880542130022220217777777700054");
        Assert.assertNotNull(service.unLinkSnAndTask(snList, "10266925"));
        psWipInfo.setLastProcess("N");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(Mockito.eq(sn))).thenReturn(psWipInfo);
        Assert.assertNotNull(service.unLinkSnAndTask(snList, "10266925"));
    }

    /*Started by AICoder, pid:oe962jb03cv1350143660b9c137ab55ada024083*/
    @Test
    public void testUnLinkSnAndTaskWithValidData() throws Exception {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        List<String> snList = Arrays.asList("SN1", "SN2");
        String empNo = "EMP1";

        // Mock the checkSn4UnLinkSnAndTask method to return valid data
        ServiceData<List<PsWipInfo>> checkRt =
                new ServiceData<>();
        checkRt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        checkRt.setBo(Arrays.asList(new PsWipInfo(), new PsWipInfo()));
        when(psWipInfoServiceImpl.checkSn4UnLinkSnAndTask(snList)).thenReturn(checkRt);

        // Mock the deleteWipInfoByWipId method to do nothing
        doNothing().when(psWipInfoRepository).deleteWipInfoByWipId(anyList());

        // Mock the batchInsertByWipInfoList method to do nothing

        // Mock the addStandardModeStockInfoByWipInfoList method to do nothing

        ServiceData<Object> result = psWipInfoServiceImpl.unLinkSnAndTask(snList, empNo);
        Assert.assertTrue(Objects.isNull(result));
    }

    @Test
    public void testUnLinkSnAndTaskWithEmptySnList() throws Exception {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        List<String> snList = new ArrayList<>();
        String empNo = "EMP1";

        ServiceData<Object> result = psWipInfoServiceImpl.unLinkSnAndTask(snList, empNo);
        Assert.assertTrue(Objects.isNull(result));
    }

    @Test
    public void testUnLinkSnAndTaskWithLargeSnList() throws Exception {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        List<String> snList = new ArrayList<>(Collections.nCopies(61, "SN"));
        String empNo = "EMP1";

        ServiceData<Object> result = psWipInfoServiceImpl.unLinkSnAndTask(snList, empNo);
        Assert.assertTrue(Objects.isNull(result));
    }

    @Test
    public void testBox() throws Exception{
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        list.add(a1);
        PsWipInfo a2 = new PsWipInfo();
        a2.setSn("123");
        list.add(a2);
        PsWipInfo a3 = new PsWipInfo();
        a3.setSn("123456789123456656");
        list.add(a3);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
        Whitebox.invokeMethod(service, "subInputQtyDeleteKey", "123", 2L, list);
    }
    /*Ended by AICoder, pid:oe962jb03cv1350143660b9c137ab55ada024083*/

    @Test
    public void getPsWipInfoListNoPage() throws Exception {
        PsWipInfoQueryDTO dto = new PsWipInfoQueryDTO();
        dto.setPage(1L);
        dto.setRows(2L);
        dto.setCurrProcessCode("1");
        try {
            service.getPsWipInfoListNoPage(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BARCODE_AVAILABLE_IN_LPN,e.getMessage());
        }
        Assert.assertNotNull(service.getPsWipInfoList(dto));
        List<String> snList = new ArrayList<>();
        for (int i = 0; i < 501; i++) {
            snList.add(""+i);
        }
        dto.setScanSnList(StringUtils.join(snList,Constant.COMMA));
        Assert.assertNotNull(service.getPsWipInfoList(dto));
    }

    @Test
    public void bindCraftEqpVerify()
            throws Exception {

        PowerMockito.mockStatic(CommonUtils.class);
        PmScanConditionDTO conditionDTO = new PmScanConditionDTO();
        service.bindCraftEqpVerify(conditionDTO);

        conditionDTO.setFormSn("123");
        Assert.assertNotNull(service.bindCraftEqpVerify(conditionDTO));

        List<WipEntityInfo> list = new LinkedList<>();
        WipEntityInfo a2 = new WipEntityInfo();
        list.add(a2);
        PowerMockito.when(wipEntityInfoRepository.getPage(Mockito.anyMap()))
                .thenReturn(list)
        ;
    }

    @Test
    public void pmBatchScan() throws Exception {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setSn("123");
        list.add(a1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyObject()))
                .thenReturn(list);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("1");

        Assert.assertNotNull(service.pmBatchScan(entity));
    }

    @Test
    public void setPsWipInfoByScan() {
        FlowControlInfoDTO record = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO red = new PsEntityPlanBasicDTO();
        record.setEntityPlanBasic(red);
        Assert.assertNotNull(service.setPsWipInfoByScan(record));
    }

    @Test
    public void getParentSnBySn() throws Exception {
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        String sn = "742953302315";
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setParentSn("742953302315");
        psWipInfo.setSn("190816CPE005220519742953302315");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getDetailByParentSn(sn)).thenReturn(psWipInfoList);
        Assert.assertNotNull(service.getParentSnBySn(sn));
    }

    @Test
    public void getParentSnBySnTwo() throws Exception {
        String sn = "12459";
        Assert.assertNotNull(service.getParentSnBySn(sn));
    }


    @Test
    public void checkSnInProdplanId() {
        List<PsWipInfo> returnList = new ArrayList<>();
        PsWipInfo record1 = new PsWipInfo();
        PsWipInfo record2 = new PsWipInfo();
        PsWipInfo record3 = new PsWipInfo();
        PsWipInfo record4 = new PsWipInfo();
        record1.setSn("123");
        record2.setSn("123");
        record1.setAttribute1("111");
        record3.setAttribute1("111");
        PowerMockito.when(psWipInfoRepository.checkSnInProdplanId(any(), any())).thenReturn(returnList);
        Assert.assertNotNull(service.checkSnInProdplanId(record1));
    }

    @Test
    public void taskInstructionStatusUpdate() throws Exception{
        List<TaskInstructionStatusDTO> list = new ArrayList<>();
        List<TaskInstructionStatusDTO> stockNums = new ArrayList<>();
        TaskInstructionStatusDTO dto = new TaskInstructionStatusDTO();
        dto.setProdplanId("11");
        dto.setTaskQty(new BigDecimal(11));
        dto.setStockNum(new BigDecimal(20));
        stockNums.add(dto);
        list.add(dto);
        PowerMockito.when(psWipInfoRepository.getStockNum(any())).thenReturn(stockNums);
        Assert.assertNotNull(service.taskInstructionStatusUpdate(list));

    }
    @Test
    public void putDataToMapForSnQuery() throws Exception {
        PsWipInfoDTO dto1 = new PsWipInfoDTO();
        PsWipInfoDTO dto2 = new PsWipInfoDTO();
        dto1.setSn("1");
        dto1.setSort(CraftConstant.CURR_PROCESS_NAME);
        dto1.setInWorkOrderNo("123");
        dto2.setSn("1,2");
        service.putDataToMapForSnQuery(dto1);
        Assert.assertNotNull(service.putDataToMapForSnQuery(dto2));
    }


    @Test
    public void getWorkOrder4SnQuery() throws Exception{
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class,CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        String workOrderNo = "workOrderNo";
        String sourceTask = "sourceTask";
        String inWorkOrderNo = "sourceTask";
        String taskNo = "taskNo";
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        Whitebox.invokeMethod(service, "getWorkOrder4SnQuery", workOrderNo, sourceTask, inWorkOrderNo);

        Assert.assertNull(Whitebox.invokeMethod(service, "getWorkOrder4SnQuery", workOrderNo, sourceTask, inWorkOrderNo, taskNo));

    }

    @Test
    public void getPsWorkOrderDTOSEx() throws Exception {
        String processCode = null;
        String workStation = null;
        PsWipInfoDTO dtoForm = new PsWipInfoDTO();
        List<PsWorkOrderDTO> psWorkOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        dtoForm.setAttribute1("SourceTask");
        dtoForm.setAttribute2("TaskNo");
        psWorkOrderDTOS.add(psWorkOrderDTO);
        psWorkOrderDTO.setTaskNo("TaskNo");
        psWorkOrderDTO.setSourceTask("SourceTask");

        Whitebox.invokeMethod(service, "getPsWorkOrderDTOSEx", processCode, workStation, dtoForm, psWorkOrderDTOS);

        psWorkOrderDTO.setSourceTask("SourceTask1");
        Whitebox.invokeMethod(service, "getPsWorkOrderDTOSEx", processCode, workStation, dtoForm, psWorkOrderDTOS);

        psWorkOrderDTO.setTaskNo("TaskNo1");
        Whitebox.invokeMethod(service, "getPsWorkOrderDTOSEx", processCode, workStation, dtoForm, psWorkOrderDTOS);

        dtoForm.setAttribute2(null);
        Whitebox.invokeMethod(service, "getPsWorkOrderDTOSEx", processCode, workStation, dtoForm, psWorkOrderDTOS);

        dtoForm.setAttribute1(null);
        Whitebox.invokeMethod(service, "getPsWorkOrderDTOSEx", processCode, workStation, dtoForm, psWorkOrderDTOS);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void unbindSnQuery() throws Exception {
        SnUnBindQueryDTO queryDTO = new SnUnBindQueryDTO();
        List<String> snList = new ArrayList<>();
        queryDTO.setSnList(snList);
        queryDTO.setPage(1);
        queryDTO.setRows(10);
        service.unbindSnQuery(queryDTO);
        queryDTO.setSnType("0");
        try {
            service.unbindSnQuery(queryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL,e.getMessage());
        }
        snList.add("77777770000");
        try {
            service.unbindSnQuery(queryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LENGTH_IS_12,e.getMessage());
        }
        queryDTO.setSnType("1");
        snList.clear();
        snList.add("11111111111111111111111111111");
        try {
            service.unbindSnQuery(queryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LENGTH_IS_30,e.getMessage());
        }
        snList.clear();
        snList.add("111111111111111111111111111111");
        PowerMockito.when(psWipInfoRepository.getCount(Mockito.anyMap())).thenReturn(10L);
        List<SnUnBindDTO> dtoList = new ArrayList<>();
        PowerMockito.when(psWipInfoRepository.getWipInfoBySnBatch(Mockito.anyMap())).thenReturn(dtoList);
        try {
            service.unbindSnQuery(queryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_ERROR_INFO,e.getMessage());
        }
        dtoList.add(new SnUnBindDTO(){{
            setCurrProcessCode("N");
            setWorkStation("S1001");
        }});
        service.unbindSnQuery(queryDTO);
        queryDTO.setSnType("0");
        snList.clear();
        snList.add("777777700001");
        PowerMockito.when(psWipInfoRepository.getWipInfoByParentSnBatch(Mockito.anyMap())).thenReturn(dtoList);
        Assert.assertNotNull(service.unbindSnQuery(queryDTO));
    }

    @Test
    public void transferProcessNameAndStationName() throws Exception {
        List<SnUnBindDTO> dtoList = new ArrayList<>();
        Whitebox.invokeMethod(service, "transferProcessNameAndStationName",dtoList);
        dtoList.add(new SnUnBindDTO(){{
            setCurrProcessCode("N");
            setWorkStation("S1001");
        }});
        dtoList.add(new SnUnBindDTO(){{
            setCurrProcessCode("y");
            setWorkStation("S1987");
        }});
        Whitebox.invokeMethod(service, "transferProcessNameAndStationName",dtoList);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<BSProcess> bsProcessList = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.anyList())).thenReturn(bsProcessList);
        Whitebox.invokeMethod(service, "transferProcessNameAndStationName",dtoList);
        bsProcessList.add(new BSProcess(){{
            setProcessCode("N");
            setProcessName("111");
        }});
        bsProcessList.add(new BSProcess(){{
            setProcessCode("S1001");
            setProcessName("222");
        }});
        Whitebox.invokeMethod(service, "transferProcessNameAndStationName",dtoList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryParentSnBySn() throws Exception {
        try {
            service.querySnByParentSn(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL,e.getMessage());
        }
        service.querySnByParentSn(new ArrayList(){{
            add("12345567");
        }});
    }

    @Test
    public void getStringObjectMap() throws Exception {
        PsWipInfoQueryDTO dto = new PsWipInfoQueryDTO();
        dto.setCurrProcessCode("1,2");
        Whitebox.invokeMethod(service, "getStringObjectMap",dto);
        dto.setCurrProcessCode("1");
        Whitebox.invokeMethod(service, "getStringObjectMap",dto);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getStringObjectMap",dto));
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getStringObjectMap",dto));
    }

    @Test
    public void unbindSn() throws Exception {
        List<String> snList = new ArrayList<>();
        snList.add("123");
        try {
            service.unbindSn(snList, "10338918");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ZJ_SN_NOT_EXIST, e.getMessage());
        }

        PowerMockito.when(psWipInfoRepository.getParentSnBySn(Mockito.any())).thenReturn(snList);
        service.unbindSn(snList, "10338918");
    }
}