package com.zte.autoTest.unitTest;

import com.zte.common.utils.RedisUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-06-21 11:04
 */
public class RedisUtilTest extends PowerBaseTestCase {
    @InjectMocks
    private RedisUtil redisUtil;
    @Mock
    private RedisTemplate redisTemplate;
    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void getValueListByScanRedis() {
        Assert.assertNull(redisUtil.getValueListByScanRedis(redisTemplate, "NULL", 100));
    }

    @Test
    public void getKeyListByScanRedis(){
        Assert.assertNull(redisUtil.getKeyListByScanRedis(redisTemplate, "", 100l));
    }

    @Test
    public void getStrKeyListByScanRedis()
    {
        Assert.assertNull(redisUtil.getStrKeyListByScanRedis(stringRedisTemplate, "", 100l));
    }

}
