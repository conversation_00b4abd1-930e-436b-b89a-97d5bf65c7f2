package com.zte.autoTest.unitTest;

import com.zte.application.impl.AgeingInfoHeaderServiceImpl;
import com.zte.application.impl.OemProduceSkipGoodsQueryServiceImpl;
import com.zte.domain.model.PsScanHistory;
import com.zte.interfaces.dto.AgeingDto;
import com.zte.interfaces.dto.OemProduceSkipGoodsQueryDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
public class OemProduceSkipGoodsQueryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private OemProduceSkipGoodsQueryServiceImpl oemProduceSkipGoodsQueryService;

    @Test
    public void getContractNumbers()throws Exception {
        Assert.assertNotNull(oemProduceSkipGoodsQueryService.getContractNumbers(new OemProduceSkipGoodsQueryDTO()));


    }
    @Test
    public void veifyContractNumber()throws Exception {
        oemProduceSkipGoodsQueryService.veifyContractNumber(new OemProduceSkipGoodsQueryDTO());
        List<String> list = new ArrayList<>();
        oemProduceSkipGoodsQueryService.veifyContractNumber(new OemProduceSkipGoodsQueryDTO(){{setContractNumbers(list);}});
        list.add("2");
        oemProduceSkipGoodsQueryService.veifyContractNumber(new OemProduceSkipGoodsQueryDTO(){{setContractNumbers(list);}});

        Assert.assertTrue(oemProduceSkipGoodsQueryService.veifyContractNumber(new OemProduceSkipGoodsQueryDTO(){{setContractNumbers(list);setDoIds(list);}}));

    }
}
