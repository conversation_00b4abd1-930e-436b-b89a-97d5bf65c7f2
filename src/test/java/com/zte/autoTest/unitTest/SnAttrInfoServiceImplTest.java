package com.zte.autoTest.unitTest;

import com.zte.application.impl.SnAttrInfoServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.SnAttrInfo;
import com.zte.domain.model.SnAttrInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.SnAttrInfoEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class})
public class SnAttrInfoServiceImplTest extends PowerBaseTestCase {

    @Mock
    private SnAttrInfoRepository snAttrInfoRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @InjectMocks
    private SnAttrInfoServiceImpl snAttrInfoService;

    @Test
    public void pageList()throws Exception {
        List<SnAttrInfoEntityDTO> snAttrInfoEntityDTOList=new ArrayList<>();
        SnAttrInfoEntityDTO snAttrInfoEntityDTO=new SnAttrInfoEntityDTO();
        snAttrInfoEntityDTOList.add(snAttrInfoEntityDTO);
        PowerMockito.when(snAttrInfoRepository.pageList(anyObject())).thenReturn(snAttrInfoEntityDTOList);

        Assert.assertNotNull(snAttrInfoService.pageList(snAttrInfoEntityDTO));
    }
    @Test
    public void getList()throws Exception {
        List<SnAttrInfoEntityDTO> snAttrInfoEntityDTOList=new ArrayList<>();
        SnAttrInfoEntityDTO snAttrInfoEntityDTO=new SnAttrInfoEntityDTO();
        snAttrInfoEntityDTOList.add(snAttrInfoEntityDTO);
        PowerMockito.when(snAttrInfoRepository.getList(anyObject())).thenReturn(snAttrInfoEntityDTOList);

        Assert.assertNotNull(snAttrInfoService.getList(snAttrInfoEntityDTO));
    }

    @Test
    public void getListByEnCode()throws Exception {
        List<SnAttrInfoEntityDTO> snAttrInfoEntityDTOList=new ArrayList<>();
        SnAttrInfoEntityDTO snAttrInfoEntityDTO=new SnAttrInfoEntityDTO();
        snAttrInfoEntityDTOList.add(snAttrInfoEntityDTO);
        PowerMockito.when(snAttrInfoRepository.getListByEnCode(Mockito.any())).thenReturn(snAttrInfoEntityDTOList);

        Assert.assertNotNull(snAttrInfoService.getListByEnCode(snAttrInfoEntityDTO));
    }
    @Test
    public void batchInsert()throws Exception {
        String factoryId = "52";
        String empNo = "10313234";
        List<SnAttrInfo> insertList = new ArrayList<>();
        List<PsWipInfo> snList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("742953303886");
        snList.add(psWipInfo);
        SnAttrInfo dto = new SnAttrInfo();
        dto.setSn("742953303886");
        insertList.add(dto);
        SnAttrInfo dto1 = new SnAttrInfo();
        dto1.setSn("123123");
        insertList.add(dto1);
        List<String> existList = new ArrayList<>();
        existList.add("123123");
        PowerMockito.when(psWipInfoRepository.getListBy(anyObject())).thenReturn(snList);
        PowerMockito.when(snAttrInfoRepository.getSnBySnList(anyObject())).thenReturn(existList);
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        Assert.assertEquals(0,snAttrInfoService.batchInsert(insertList,factoryId,empNo));
    }

    @Test
    public void setWholeBarCodeTest() throws Exception{
        List<SnAttrInfo> record = new ArrayList<>();
        List<String> snList = new ArrayList<>();
        Whitebox.invokeMethod(snAttrInfoService, "setWholeBarCode", record, snList);
        SnAttrInfo snAttrInfo = new SnAttrInfo();
        record.add(snAttrInfo);
        Whitebox.invokeMethod(snAttrInfoService, "setWholeBarCode", record, snList);
        snList.add("test");
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("test");
        psWipInfo.setParentSn("test");
        PowerMockito.when(psWipInfoRepository.getListByBatchParentSn(anyList())).thenReturn(psWipInfoList);
        Whitebox.invokeMethod(snAttrInfoService, "setWholeBarCode", record, snList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

}
