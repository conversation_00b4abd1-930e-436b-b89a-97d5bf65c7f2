package com.zte.autoTest.unitTest;

import com.zte.application.impl.WipInfoServiceImpl;
import com.zte.domain.model.PsWipInfo;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @date 2021-07-01 20:17
 */
public class WipInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipInfoServiceImpl wipInfoServiceImpl;

    @Before
    public void init(){

    }

    @Test
    public void updateWipInfo() throws Exception {
        Assert.assertEquals(0,wipInfoServiceImpl.updateWipInfo(new PsWipInfo()));
    }

    /**
     * 批量更新wipinfor 信息
     *
     * @return
     * @throws Exception
     */
    @Test
    public void updateWipInfoList() throws Exception {
        Assert.assertEquals(0,wipInfoServiceImpl.updateWipInfo(new LinkedList<>()));
    }

    /**
     * 批量删除wipinfor 信息
     *
     * @return
     * @throws Exception
     */
    @Test
    public void deleteWipInfo() throws Exception {
        Assert.assertEquals(0,wipInfoServiceImpl.deleteWipInfo(new LinkedList<>()));
    }
}
