package com.zte.autoTest.unitTest.scan;

import com.zte.application.ParentsnAndSnService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.scan.CommonSnScanServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.TaskReconfigurationRecordRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.TaskReconfigurationRecordDTO;
import com.zte.interfaces.dto.craft.CtProcessMappingDTO;
import com.zte.interfaces.dto.scan.CommonSnScanDTO;
import com.zte.interfaces.dto.scan.PickListResultDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-31 16:42
 */
@PrepareForTest({PlanscheduleRemoteService.class, CrafttechRemoteService.class,DatawbRemoteService.class})
public class CommonSnScanServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private CommonSnScanServiceImpl commonSnScanServiceImpl;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private ParentsnAndSnService parentsnAndSnService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private TaskReconfigurationRecordRepository taskReconfigurationRecordRepository;
    @Mock
    private FlowControlCommonService flowControlCommonService;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.field(CommonSnScanServiceImpl.class,"standCheckSnConfig")
                .set(commonSnScanServiceImpl,false);
    }

    /* Started by AICoder, pid:087efdf34fv1ebc140f00b16415e8c73df43036b */
    @Test
    public void standSnCheckReConfig() throws IllegalAccessException {
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setSn("123");
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        PowerMockito.field(CommonSnScanServiceImpl.class,"standCheckSnConfig")
                .set(commonSnScanServiceImpl,true);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo p1 = new PsWipInfo();
        p1.setCurrProcessCode("N");
        list.add(p1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(list);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_INBOUND_WORK_NEED_CHOICE, e.getMessage());
        }

        entity.setWorkOrderNo("123");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }

        List<PsEntityPlanBasicDTO> workList = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setRouteId("123");
        a1.setTaskQty(new BigDecimal(1));
        workList.add(a1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(workList);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_BOX_ORDER_ERROR, e.getMessage());
        }

        a1.setTaskNo("123");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_BOX_ORDER_ERROR, e.getMessage());
        }

        p1.setWorkOrderNo("123");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_BOX_ORDER_ERROR, e.getMessage());
        }

        p1.setCurrProcessCode(null);
        entity.setWorkOrderNo(null);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(null);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_BOX_ORDER_ERROR, e.getMessage());
        }


        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(workList);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        List<PsTaskExtendedDTO> taskExtendedDTOList = new LinkedList<>();
        PsTaskExtendedDTO taskExtendedDTO = new PsTaskExtendedDTO();
        taskExtendedDTO.setTaskNo("123");
        taskExtendedDTO.setItemNo("13");
        taskExtendedDTOList.add(taskExtendedDTO);
        PowerMockito.when(centerfactoryRemoteService.bulkQueriesByTaskNos(Mockito.anyList()))
                .thenReturn(taskExtendedDTOList);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);


        taskExtendedDTO.setEntityClass(Constant.FG_DISAS_2);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_IS_NULL, e.getMessage());
        }

        taskExtendedDTO.setEntityClass(Constant.REWORK_2);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_IS_NULL, e.getMessage());
        }


        a1.setLineCode("123");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_MES_TASK_PICK_ERROR, e.getMessage());
        }

        List<PickListResultDTO> pickListResultList = new LinkedList<>();
        PickListResultDTO pickListResultDTO1 = new PickListResultDTO();
        pickListResultList.add(pickListResultDTO1);
        PowerMockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(Mockito.any()))
                .thenReturn(pickListResultList);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CONFIG_TASK_CONTAIN, e.getMessage());
        }

        pickListResultDTO1.setSnList("444,44");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_CONFIG_TASK_CONTAIN, e.getMessage());
        }

        pickListResultDTO1.setSnList("123");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ROUTE_DETAIL_LOST, e.getMessage());
        }

        List<CtRouteInfoDTO> listRoute = new LinkedList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        listRoute.add(ctRouteInfoDTO);
        List<CtRouteDetailDTO> detailByRouteIds = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CtRouteDetailDTO b1 = new CtRouteDetailDTO();
            b1.setNextProcess(String.valueOf(i));
            b1.setProcessSeq(new BigDecimal(i));
            b1.setProcessCode("0");
            if (i > 0) {
                b1.setProcessCode(detailByRouteIds.get(i-1).getNextProcess());
            }
            detailByRouteIds.add(b1);
        }
        CtRouteDetailDTO b1 = new CtRouteDetailDTO();
        b1.setNextProcess("N");
        b1.setProcessSeq(new BigDecimal(9));
        b1.setProcessCode("0");
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),Mockito.any(),Mockito.any(),
                        Mockito.any(),Mockito.any()))
                .thenReturn(listRoute);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.any()))
                .thenReturn(detailByRouteIds);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        entity.setCurrProcessCode("4");
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_ERP_TASK_QTY, e.getMessage());
        }

        detailByRouteIds.add(b1);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_ERP_TASK_QTY, e.getMessage());
        }

        List<ItemListEntityDTO> itemListByTaskList = new ArrayList<>();
        List<TaskReconfigurationRecordDTO> configList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ItemListEntityDTO temp = new ItemListEntityDTO();
            temp.setItemNo(String.valueOf(i));
            temp.setRequiredQuantity(String.valueOf(i));
            TaskReconfigurationRecordDTO temp2 = new TaskReconfigurationRecordDTO();
            temp2.setItemCode(String.valueOf(i));
            temp2.setQuantity(i);
            temp2.setOperationType(2);
            if (i % 2 == 0) {
                temp2.setOperationType(1);
                temp.setRequiredQuantity("-" + i);
            }
            if (i == 5) {
                temp.setRequiredQuantity(null);
            }
            itemListByTaskList.add(temp);
            configList.add(temp2);
        }
        PowerMockito.when(DatawbRemoteService.getItemListByTaskList(Mockito.anyList()))
                .thenReturn(itemListByTaskList);
        PowerMockito.when(taskReconfigurationRecordRepository.queryQtyGroup(Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(new ArrayList<>());
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_RECONFIGURATION_NO_COMPLETED, e.getMessage());
        }

        PowerMockito.when(taskReconfigurationRecordRepository.queryQtyGroup(Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(configList);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO temp1 = new CtRouteDetailDTO();
        listDetail.add(temp1);
        ctRouteInfoDTO.setListDetail(listDetail);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        entity.setCurrProcessCode("0");
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),Mockito.any(),Mockito.any(),
                        Mockito.any(),Mockito.any()))
                .thenReturn(null);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_SN_SCAN_ROUTE, e.getMessage());
        }

        List<CtRouteInfoDTO> listRoute2 = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        listRoute2.add(c1);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),Mockito.any(),Mockito.any(),
                        Mockito.any(),Mockito.any()))
                .thenReturn(listRoute2);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_SN_SCAN_ROUTE, e.getMessage());
        }

        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),Mockito.any(),Mockito.any(),
                        Mockito.any(),Mockito.any()))
                .thenReturn(listRoute);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        List<CtRouteDetailDTO> listDetail2 = new LinkedList<>();
        CtRouteDetailDTO d1 = new CtRouteDetailDTO();
        d1.setNextProcess("2");
        listDetail2.add(d1);
        c1.setListDetail(listDetail2);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),Mockito.any(),Mockito.any(),
                        Mockito.any(),Mockito.any()))
                .thenReturn(listRoute2);
        entity.setWorkStation("3");
        p1.setCurrProcessCode("N");
        entity.setWorkOrderNo("123");
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        entity.setWorkStation("2");
        commonSnScanServiceImpl.standSnCheckReConfig(entity);

        p1.setCurrProcessCode("2");
        p1.setWorkOrderNo(null);
        entity.setWorkOrderNo(null);
        try {
            commonSnScanServiceImpl.standSnCheckReConfig(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_INFO_IS_NONE_BY_TASK_AND_PROCESS, e.getMessage());
        }

        List<PsWorkOrderDTO> psWorkOrderDTOs = new LinkedList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setWorkOrderNo("123");
        psWorkOrderDTOs.add(ps1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(Mockito.any(), Mockito.any()))
                .thenReturn(psWorkOrderDTOs);
        commonSnScanServiceImpl.standSnCheckReConfig(entity);
    }
    /* Ended by AICoder, pid:087efdf34fv1ebc140f00b16415e8c73df43036b */


    @Test
    public void commonSnScanBatch() throws Exception {
        List<PsWorkOrderDTO> workList = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        workList.add(a1);
        PsWorkOrderDTO a2 = new PsWorkOrderDTO();
        a2.setSourceTask("8899722");
        a2.setProcessGroup("2");
        a2.setCraftSection("SMT-B");
        a2.setWorkOrderQty(new BigDecimal(200));
        a2.setLineCode("SMT-HY012");
        a2.setWorkOrderNo("8899722-SMT-B5501");
        workList.add(a2);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap()))
                .thenReturn(workList);

        List<BSProcess> process = new LinkedList<>();
        BSProcess a3 = new BSProcess();
        a3.setProcessCode("2");
        process.add(a3);
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.anyObject()))
                .thenReturn(process);
        CommonSnScanDTO commonSnScanDTO = new CommonSnScanDTO();
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }
        commonSnScanDTO.setLineCode("SMT-HY013");
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_IS_NULL, e.getMessage());
        }
        commonSnScanDTO.setProcessCode("2");
        commonSnScanDTO.setProcessName("2");
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL_VALID, e.getMessage());
        }
        List<String> snList = new LinkedList<>();
        snList.add("88997220002");
        commonSnScanDTO.setSnList(snList);
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_REGULAR_ERROR, e.getMessage());
        }
        commonSnScanDTO.setBatchScanFlag("Y");
        PowerMockito.when(parentsnAndSnService.getAllSubSn(Mockito.anyObject()))
                .thenReturn(null);
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL_VALID, e.getMessage());
        }
        List<String> subSnList = new LinkedList<>();
        subSnList.add("889972200002");
        subSnList.add("889972200003");
        subSnList.add("889972200004");
        subSnList.add("889972200005");
        PowerMockito.when(parentsnAndSnService.getAllSubSn(Mockito.anyObject()))
                .thenReturn(subSnList);

        List<BSProcess> process1 = new LinkedList<>();
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.anyObject()))
                .thenReturn(process1);
        try {
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_IS_NOT_EXISTED, e.getMessage());
        }

        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.anyObject()))
                .thenReturn(process);

        snList.clear();
        snList.add("889972200002");

        List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
        CtRouteDetailDTO b1 = new CtRouteDetailDTO();
        b1.setProcessCode("2");
        b1.setCraftSection(Constant.CRAFTSECTION_SMT_B);
        b1.setNextProcess("1340");
        b1.setSourceImu(new BigDecimal(1));
        b1.setSourceBimu(new BigDecimal(2));
        b1.setSysColName("E5");
        b1.setSourceSysName("SMT-B-B面");
        ctRouteDetailList.add(b1);
        PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
                .thenReturn(ctRouteDetailList)
        ;
        List<PsWipInfo> listByBatchSn = new LinkedList<>();
        PsWipInfo c1 = new PsWipInfo();
        c1.setSn("889972200002");
        c1.setWorkOrderNo("8899722-SMT-B5501");
        c1.setProcessCode("2");
        listByBatchSn.add(c1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(listByBatchSn);

        commonSnScanDTO.setFactoryId("52");
        commonSnScanDTO.setEntityId("2");
        commonSnScanDTO.setProdplanId("8899722");
        commonSnScanDTO.setBatchScanFlag("N");
        commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);

        PsWipInfo c2 = new PsWipInfo();
        c2.setSn("889972200002");
        c2.setWorkOrderNo("8899722-SMT-B5501aaaa");
        c2.setProcessCode("2aaa");
        listByBatchSn.add(c2);
        try{
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SN_DIFF_PROPERTIES, e.getMessage());
        }
        listByBatchSn.remove(1);

        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(null);
        commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);

        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(listByBatchSn);

        b1.setProcessCode("1");
        b1.setMappingProcess("2");
        b1.setMappingSourceImu(new BigDecimal("2"));
        commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);

        b1.setSourceSys("0");

        List<CtProcessMappingDTO> ctProcessMappingDTOS = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.ctProcessMapping(Mockito.anyString(),Mockito.anyString()))
                .thenReturn(ctProcessMappingDTOS);
        try{
            commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
        }catch (Exception e){
        }

        CtProcessMappingDTO ctProcessMappingDTO = new CtProcessMappingDTO();
        ctProcessMappingDTO.setSourceImu(BigDecimal.TEN);
        ctProcessMappingDTOS.add(ctProcessMappingDTO);
        commonSnScanServiceImpl.commonSnScanBatch(commonSnScanDTO);
    }

    @Test
    public void whiteBoxTest() throws Exception {
        CommonSnScanDTO commonSnScanDTO = new CommonSnScanDTO();
        commonSnScanDTO.setSnList(new LinkedList<>());
        commonSnScanDTO.setProdplanId("8899758");
        commonSnScanDTO.setProcessCode("2");
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        commonSnScanDTO.setPsWorkOrderDTO(psWorkOrderDTO);
        try {
            Whitebox.invokeMethod(commonSnScanServiceImpl, "queryLineBodyInfo", commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_MODEL_NOT_FOUND, e.getMessage());
        }
        Whitebox.invokeMethod(commonSnScanServiceImpl, "checkSnRegular", commonSnScanDTO);
        try {
            Whitebox.invokeMethod(commonSnScanServiceImpl, "queryWorkOrderInfo", commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }
        List<PsWorkOrderDTO> workList = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        workList.add(a1);//MessageId.PROCESS_CODE_WORKORDERNO_NOT_EXIST
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList);
        try {

            Whitebox.invokeMethod(commonSnScanServiceImpl, "queryWorkOrderInfo", commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_WORKORDERNO_NOT_EXIST, e.getMessage());
        }
        a1.setProcessGroup("2");
        try {

            Whitebox.invokeMethod(commonSnScanServiceImpl, "queryWorkOrderInfo", commonSnScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_LINE_CODE_EMPTY, e.getMessage());
        }
    }

}
