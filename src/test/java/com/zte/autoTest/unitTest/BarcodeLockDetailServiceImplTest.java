package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.BarcodeLockDetailServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({CrafttechRemoteService.class, MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class,
        HttpRemoteUtil.class, HttpRemoteService.class,PlanscheduleRemoteService.class})
public class BarcodeLockDetailServiceImplTest extends PowerBaseTestCase {

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private BarcodeLockHeadRepository barcodeLockHeadRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @InjectMocks
    private BarcodeLockDetailServiceImpl barcodeLockDetailService;

    @Test public void getCtRouteDetailDTOList()throws Exception{
        Map<String, PsWorkOrderDTO> psWorkOrderDTOMap=new HashMap<>();
        Map<String, List<CtRouteDetailDTO>> ctRouteMap=new HashMap<>();
        Map<String, CtRouteInfoDTO> ctRouteInfoDTOMap=new HashMap<>();
        Map<String, PsTask> psTaskMap=new HashMap<>();
        String prodPlanId="7250525";
        //psWorkOrderDTO为空
        Whitebox.invokeMethod(barcodeLockDetailService,"getCtRouteDetailDTOList",
                psWorkOrderDTOMap,ctRouteMap,ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        //psWorkOrderDTO不为空 ctRouteDetailList1为空
        PsWorkOrderDTO psWorkOrderDTO=new PsWorkOrderDTO();
        psWorkOrderDTO.setRouteId("id1");
        psWorkOrderDTOMap.put("7250525",psWorkOrderDTO);
        Whitebox.invokeMethod(barcodeLockDetailService,"getCtRouteDetailDTOList",
                psWorkOrderDTOMap,ctRouteMap,ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        //ctRouteDetailList1不为空
        List<CtRouteDetailDTO> ctRouteDetailDTOList=new ArrayList<>();
        CtRouteDetailDTO ctRouteInfoDTO=new CtRouteDetailDTO();
        ctRouteInfoDTO.setRouteId("id1");
        ctRouteDetailDTOList.add(ctRouteInfoDTO);
        ctRouteMap.put("id1",ctRouteDetailDTOList);
        Whitebox.invokeMethod(barcodeLockDetailService,"getCtRouteDetailDTOList",
                       psWorkOrderDTOMap,ctRouteMap,ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        //psTaskTemp不为空
        ctRouteDetailDTOList.remove(ctRouteInfoDTO);
        PsTask psTask=new PsTask();
        psTask.setProdplanId("7250525");
        psTask.setItemNo("itemNo1");
        psTaskMap.put("7250525",psTask);
        Whitebox.invokeMethod(barcodeLockDetailService,"getCtRouteDetailDTOList",
                psWorkOrderDTOMap,ctRouteMap,ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        //ctRouteInfoDTOMap不为空
        CtRouteInfoDTO ctRouteInfoDTO1=new CtRouteInfoDTO();
        ctRouteInfoDTOMap.put("itemNo1",ctRouteInfoDTO1);
        Whitebox.invokeMethod(barcodeLockDetailService,"getCtRouteDetailDTOList",
                psWorkOrderDTOMap,ctRouteMap,ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        Assert.assertEquals("7250525", psTask.getProdplanId());
    }

    @Test
    public void batchInsertOrUpdate() throws Exception {
        barcodeLockDetailService.batchInsertOrUpdate(new ArrayList<>());
        List<BarcodeLockDetailEntityDTO> list = new ArrayList<>();
        list.add(new BarcodeLockDetailEntityDTO());
        barcodeLockDetailService.batchInsertOrUpdate(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void updateToFinished() throws Exception {
        barcodeLockDetailService.updateToFinished(new ArrayList<>(),"");
        List<String> barcodeList = new ArrayList<>();
        barcodeList.add("22");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        barcodeLockDetailService.updateToFinished(barcodeList,"");
    }

    @Test
    public void batchInsert() throws Exception {
        List<BarcodeLockDetailEntityDTO> list = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        list.add(barcodeLockDetailEntityDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        barcodeLockDetailService.batchInsert(list);
    }

    @Test
    public void deleteByBillNo() throws Exception {
        Assert.assertEquals(0,barcodeLockDetailService.deleteByBillNo("billNo"));
    }


    @Test
    public void verifyBatchSn() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class);
        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(anyObject())).thenReturn(bsProcessList);
        List<CtRouteInfoDTO> ctRouteInfoDTOList = new ArrayList<>();
        CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
        ctRouteInfoDTO.setItemNo("itemNo");
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        listDetail.add(ctRouteDetailDTO);
        ctRouteInfoDTO.setListDetail(listDetail);
        ctRouteInfoDTOList.add(ctRouteInfoDTO);
        PowerMockito.when(CrafttechRemoteService.getRouteInfo(anyString(), anyString())).thenReturn(ctRouteInfoDTOList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("77788890001");
        psTask.setItemNo("itemNo");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyObject())).thenReturn(psTaskList);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyObject())).thenReturn(psTaskList);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setRouteId("routeId");
        psWipInfo.setSn("77788890001");
        psWipInfo.setWorkOrderNo("77788890001");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        psWipInfos.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(anyList())).thenReturn(psWipInfos);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(psWipInfo);
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetail = new BarcodeLockDetailEntityDTO();
        barcodeLockDetail.setCurrProcessCode("1");
        barcodeLockDetail.setLockProcessCode("1");
        barcodeLockDetail.setPage(1);
        barcodeLockDetail.setCreateBy("1027");
        barcodeLockDetail.setUnlockBy("1027");
        barcodeLockDetail.setCcList("1027,1025");
        barcodeLockDetail.setRows(10);
        barcodeLockDetailEntityDTOList.add(barcodeLockDetail);

        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        sysLookupTypesDTO1.setLookupMeaning("单板测试段");
        sysLookupTypesDTO1.setAttribute1("1,2，3;4；5,");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(barcodeLockDetailRepository.pageList(anyObject())).thenReturn(barcodeLockDetailEntityDTOList);
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(anyObject())).thenReturn(null);
        BarcodeLockDetailEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockHeadEntityDTO.setBatchSn("77788890001");
        barcodeLockHeadEntityDTO.setType("条码锁定");
        barcodeLockHeadEntityDTO.setOperationMode("1");
        barcodeLockHeadEntityDTO.setPage(1);
        barcodeLockHeadEntityDTO.setRows(10);
        barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO);
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(anyObject())).thenReturn(psWorkOrderBasic);
        barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO);
        psWorkOrderBasic.setRouteId("test123");
        barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO);
        barcodeLockHeadEntityDTO.setType("条码锁定");
        barcodeLockHeadEntityDTO.setOperationMode("2");
        barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO);
        barcodeLockHeadEntityDTO.setType("批次锁定");
        barcodeLockHeadEntityDTO.setOperationMode("1");
        barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO);
        barcodeLockHeadEntityDTO.setType("批次锁定");
        barcodeLockHeadEntityDTO.setOperationMode("2");
        Assert.assertNotNull(barcodeLockDetailService.verifyBatchSn(barcodeLockHeadEntityDTO));
    }

    @Test
    public void createBillNo() throws Exception {
        List<String> cfBizCode = new ArrayList<>();
        cfBizCode.add("SD1");
        PowerMockito.when(centerfactoryRemoteService.createCfBizCode(anyObject())).thenReturn(cfBizCode);
        Assert.assertNotNull(barcodeLockDetailService.createBillNo());
    }

    @Test
    public void pageList() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,HttpRemoteService.class);
        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(anyObject())).thenReturn(bsProcessList);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);
        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetail = new BarcodeLockDetailEntityDTO();
        barcodeLockDetail.setCurrProcessCode("1");
        barcodeLockDetail.setLockProcessCode("1");
        barcodeLockDetail.setPage(1);
        barcodeLockDetail.setCreateBy("1027");
        barcodeLockDetail.setUnlockBy("1027");
        barcodeLockDetail.setCcList("1027,1025");
        barcodeLockDetail.setType("条码锁定");
        barcodeLockDetail.setRows(10);
        barcodeLockDetailEntityDTOList.add(barcodeLockDetail);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(barcodeLockDetailRepository.pageList(anyObject())).thenReturn(barcodeLockDetailEntityDTOList);
        BarcodeLockDetailEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockHeadEntityDTO.setType("条码锁定");
        barcodeLockHeadEntityDTO.setOperationMode("1");
        barcodeLockHeadEntityDTO.setPage(1);
        barcodeLockHeadEntityDTO.setRows(10);

        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        psWipInfoDTOList.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(Mockito.any()))
                .thenReturn(psWipInfoDTOList);
        List<PsTask> childTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setSubProdplanId("777");
        childTasks.add(psTask);
        ServiceData serviceData =new ServiceData();
        serviceData.setBo(childTasks);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData)));

        Assert.assertNotNull(barcodeLockDetailService.pageList(barcodeLockHeadEntityDTO));
        barcodeLockHeadEntityDTO.setInLockProcessCode("123");
        Assert.assertNotNull(barcodeLockDetailService.pageList(barcodeLockHeadEntityDTO));

    }

    @Test
    public void testPrivate() throws Exception{
        Map<String, PsWorkOrderDTO> psWorkOrderDTOMap = new HashMap<>();
        Map<String, List<CtRouteDetailDTO>> ctRouteMap = new HashMap<>();
        Map<String, CtRouteInfoDTO> ctRouteInfoDTOMap = new HashMap<>();
        Map<String, PsTask> psTaskMap = new HashMap<>();
        String prodPlanId = "8899750";
        PsTask a1 = new PsTask();
        psTaskMap.put(prodPlanId, a1);
        Whitebox.invokeMethod(barcodeLockDetailService, "getCtRouteDetailDTOList", psWorkOrderDTOMap,ctRouteMap,
                ctRouteInfoDTOMap,psTaskMap,prodPlanId);
        Assert.assertEquals("8899750",prodPlanId);
    }


}
