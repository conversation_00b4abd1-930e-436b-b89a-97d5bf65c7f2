package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PsBarcodeControlInfoService;
import com.zte.application.PsWipInfoService;
import com.zte.application.StandardModeCommonScanService;
import com.zte.application.impl.PsCommonScanServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PcProcessTransferDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsEntityPlanTestDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.scan.BarcodeControlDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zte.common.utils.Constant.FLAG_Y;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2022-01-29 8:59
 */
@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class
        , ObtainRemoteServiceDataUtil.class, CrafttechRemoteService.class, SpringContextUtil.class,
        ProductionmgmtRemoteService.class})
public class PsCommonScanServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private PsCommonScanServiceImpl psCommonScanServiceImpl;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private BsWorkTimeSectionService bsWorkTimeSectionService;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private StandardModeCommonScanService standardModeCommonScanService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private PsBarcodeControlInfoService psBarcodeControlInfoService;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(ProductionmgmtRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
    }


    @Test
    public void pmScan() throws Exception {
        PowerMockito.when(centerfactoryRemoteService.isHomeTerminal(anyString())).thenReturn(false);
        PmScanConditionDTO entity = new PmScanConditionDTO();
        entity.setLineCode("123");
        entity.setCurrProcessCode("2");
        entity.setWorkOrderNo("8888422-SMT-A5501");
        entity.setSn("888842200001");
        entity.setWorkStation("2");
        List<PmScanConditionDTO> childs = new LinkedList<>();
        PmScanConditionDTO child = new PmScanConditionDTO();
        child.setProcessGroup("2$8$3");
        child.setCurrProcessCode("2");
        child.setWorkStation("2");
        child.setWorkOrderNo("8888422-SMT-A5501");
        childs.add(child);
        entity.setProcessInfoList(childs);

        List<PmRepairRcvDetail> pmRepairList = new LinkedList<>();
        PmRepairRcvDetail p1 = new PmRepairRcvDetail();
        p1.setIsAccept(new BigDecimal(1));
        p1.setStatus("10560003");
        pmRepairList.add(p1);
        PowerMockito.when(pmRepairRcvDetailRepository.getList(Mockito.anyObject()))
                .thenReturn(pmRepairList);

        List<BSProcessDTO> listAllProcess = new LinkedList<>();
        BSProcessDTO b1 = new BSProcessDTO();
        b1.setProcessCode("2");
        b1.setProcessName("SMT-B");
        b1.setProcessType("手动测试");
        listAllProcess.add(b1);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap()))
                .thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(listAllProcess));


        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWipId("123");
        psWipInfo.setWorkOrderNo("8888422-SMT-A5501");
        psWipInfo.setSn("888842200001");
        psWipInfo.setCurrProcessCode("2");
        psWipInfo.setCraftSection("dip");
        psWipInfo.setWorkshopCode("2");
        psWipInfo.setOpeTimes(new BigDecimal("1"));
        psWipInfo.setLastProcess("N");
        psWipInfo.setWorkStation("0");
        psWipInfo.setItemNo("1233");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(entity.getSn()))
                .thenReturn(psWipInfo);

        List<PsEntityPlanBasicDTO> psEntityList = new LinkedList<>();
        PsEntityPlanBasicDTO pe1 = new PsEntityPlanBasicDTO();
        pe1.setWorkOrderNo("8888422-SMT-A5501");
        pe1.setProcessGroup("2");
        pe1.setWorkshopCode("3");
        pe1.setRouteId("123");
        pe1.setIsProcessControl("N");
        pe1.setItemNo("1233");
        pe1.setOutputQty(new BigDecimal("2"));
        pe1.setInputQty(new BigDecimal("2"));
        pe1.setWorkOrderQty(new BigDecimal("100"));
        psEntityList.add(pe1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(anyString()))
                .thenReturn(psEntityList);

        List<CtRouteInfoDTO> ctList = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        ctList.add(c1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO ct1 = new CtRouteDetailDTO();
        ct1.setNextProcess("2");
        listDetail.add(ct1);
        c1.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(Mockito.anyObject()))
                .thenReturn(ctList);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ctList);

        List<BSProcess> processList = new LinkedList<>();
        BSProcess bs1 = new BSProcess();
        processList.add(bs1);
        PowerMockito.when(standardModeCommonScanService.getProcess(Mockito.any(), Mockito.any()))
                .thenReturn(processList)
        ;

        PowerMockito.when(planscheduleRemoteService.updatePsWorkorderQty(Mockito.anyObject()))
                .thenReturn("1");


        List<CtRouteDetail> dtlList = new LinkedList<>();
        CtRouteDetail c11 = new CtRouteDetail();
        c11.setLastProcess("Y");
        dtlList.add(c11);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(dtlList);

        PowerMockito.when(CrafttechRemoteService.postScanProcess(Mockito.anyObject())).thenReturn(ctList);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning(FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LookUpKey.LOOK_1890,
                Constant.LookUpKey.LOOK_1890005)).thenReturn(sysLookUpValue);

        ReflectionTestUtils.setField(psCommonScanServiceImpl, "qcSamplingFlag", false);
        // 1.
        psCommonScanServiceImpl.pmScan(entity);

        // 3.

        psWipInfo.setWorkOrderNo("8888422-SMT-A5503");
        psWipInfo.setLastProcess("Y");
        PowerMockito.when(flowControlCommonService.checkZLLastProcess(anyString(), anyString())).thenReturn(true);
        PowerMockito.when(flowControlCommonService.checkZLFirstProcess(anyString(), anyString())).thenReturn(true);

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO b11 = new SysLookupValuesDTO();
        b11.setLookupMeaning("Y");
        b11.setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_SUBMIT_MODEL_ONE));
        lookupValueList.add(b11);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_SUBMIT_MODEL))
                .thenReturn(lookupValueList)
        ;
        psCommonScanServiceImpl.pmScan(entity);

        b11.setLookupType(new BigDecimal(MpConstant.LOOKUP_CODE_SUBMIT_MODEL_TWO));
        psCommonScanServiceImpl.pmScan(entity);


        pe1.setIsProcessControl("Y");
        psCommonScanServiceImpl.pmScan(entity);

        pe1.setItemNo("12334");
        psCommonScanServiceImpl.pmScan(entity);

        // 2.
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(entity.getSn()))
                .thenReturn(null);
        pe1.setIsProcessControl("N");
        Assert.assertEquals("N", pe1.getIsProcessControl());
        psCommonScanServiceImpl.pmScan(entity);


    }

    @Test
    public void checkTestControl() throws Exception {
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
        entity.setWorkStationType(MpConstant.PROCESS_TYPE_AUTOTEST);
        entity.setCurrProcessCode("2");
        entity.setWorkStation("2");
        PsEntityPlanTestDTO p1 = new PsEntityPlanTestDTO();
        p1.setMaxTestCount(new BigDecimal(2));
        entity.setEntityPlanTest(p1);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("2");
        psWipInfo.setOpeTimes(new BigDecimal(1));
        psWipInfo.setWorkStation("2");
        entity.setWipInfo(psWipInfo);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO a1 = new CtRouteDetailDTO();
        a1.setNextProcessName("123");
        listDetail.add(a1);

        psCommonScanServiceImpl.checkTestControl(entity, listDetail);

        psCommonScanServiceImpl.checkTestStationControl(entity, listDetail);

        entity.setProcessType(null);
        entity.setWorkStationType(null);
        try {
            psCommonScanServiceImpl.checkTestControl(entity, listDetail);
        } catch (Exception e) {
            Assert.assertEquals(null, entity.getProcessType());
        }
        try {
            psCommonScanServiceImpl.checkTestStationControl(entity, listDetail);
        } catch (Exception e) {
            Assert.assertEquals(null, entity.getProcessType());
        }
    }

    @Test
    public void buildErrorMsg() throws Exception {
        PsWipInfo currWipInfo = new PsWipInfo();
        currWipInfo.setCurrProcessCode("1");
        PsEntityPlanBasicDTO planBasicDTO = new PsEntityPlanBasicDTO();
        planBasicDTO.setProcessGroup("12");

        List<BSProcessDTO> listAllProcess = new LinkedList<>();
        BSProcessDTO b1 = new BSProcessDTO();
        b1.setProcessCode("2");
        b1.setProcessName("SMT-B");
        b1.setProcessType("手动测试");
        listAllProcess.add(b1);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap()))
                .thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(listAllProcess));


        try {
            psCommonScanServiceImpl.buildErrorMsg(currWipInfo, planBasicDTO);
        } catch (Exception e) {
            Assert.assertEquals("SMT-B", b1.getProcessName());
        }
    }

    @Test
    public void checkWipProcess() throws Exception {
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setCurrProcessCode("2");
        PsWipInfo psWipInfo = new PsWipInfo();
        entity.setWipInfo(psWipInfo);
        psWipInfo.setCurrProcessCode("3");

        PsEntityPlanBasicDTO p1 = new PsEntityPlanBasicDTO();
        entity.setEntityPlanBasic(p1);
        p1.setIsProcessControl("Y");

        List<CtRouteInfoDTO> ctList = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        ctList.add(c1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO ct1 = new CtRouteDetailDTO();
        ct1.setNextProcess("2");
        listDetail.add(ct1);
        c1.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(Mockito.anyObject()))
                .thenReturn(ctList);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ctList);

        List<CtRouteDetailDTO> listObj = new LinkedList<>();
        CtRouteDetailDTO a1 = new CtRouteDetailDTO();
        a1.setProcessCode("2");
        a1.setMappingProcess("2");
        listObj.add(a1);
        PowerMockito.when(
                        CrafttechRemoteService.selectCtProcessMapping(Mockito.any(), Mockito.any(),
                                Mockito.any()))
                .thenReturn(listObj);
        Assert.assertEquals("2", a1.getProcessCode());
        psCommonScanServiceImpl.checkWipProcess(entity);
    }

    @Test
    public void checkWorkStation() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setCurrProcessCode("2");
        flow.setWorkStationType(MpConstant.PROCESS_TYPE_AUTOTEST);
        flow.setWorkStation("0");

        PsWipInfo currWipInfo = new PsWipInfo();
        PsEntityPlanBasicDTO psEntityPlanInfo = new PsEntityPlanBasicDTO();
        psEntityPlanInfo.setIsProcessControl("N");
        psEntityPlanInfo.setWorkGroup("2");
        psEntityPlanInfo.setProcessGroup("2");
        flow.setEntityPlanBasic(psEntityPlanInfo);

        PsEntityPlanTestDTO test1 = new PsEntityPlanTestDTO();
        test1.setMaxTestCount(new BigDecimal("3"));
        flow.setEntityPlanTest(test1);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkStation("0");
        psWipInfo.setOpeTimes(new BigDecimal("1"));
        flow.setWipInfo(psWipInfo);
        psWipInfo.setCurrProcessCode("3");

        List<CtRouteInfoDTO> ctList = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        ctList.add(c1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO ct1 = new CtRouteDetailDTO();
        ct1.setNextProcess("2");
        listDetail.add(ct1);
        c1.setListDetail(listDetail);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(Mockito.anyObject()))
                .thenReturn(ctList);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(ctList);

        List<CtRouteDetailDTO> listObj = new LinkedList<>();
        CtRouteDetailDTO a1 = new CtRouteDetailDTO();
        a1.setProcessCode("2");
        a1.setMappingProcess("2");
        listObj.add(a1);
        PowerMockito.when(
                        CrafttechRemoteService.selectCtProcessMapping(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(listObj);

        List<BSProcessDTO> listAllProcess = new LinkedList<>();
        BSProcessDTO b1 = new BSProcessDTO();
        b1.setProcessCode("2");
        b1.setProcessName("SMT-B");
        b1.setProcessType("手动测试");
        listAllProcess.add(b1);
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap()))
                .thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(listAllProcess));

        try {
            psCommonScanServiceImpl.checkWorkStation(flow, currWipInfo, psEntityPlanInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TO_NEXT_PROCESS_CODE, e.getMessage());
        }
        psWipInfo.setLastProcess("Y");
        currWipInfo.setCurrProcessCode("2");
        currWipInfo.setWorkStation("0");
        Assert.assertEquals("Y", psWipInfo.getLastProcess());
        psCommonScanServiceImpl.checkWorkStation(flow, currWipInfo, psEntityPlanInfo);
    }

    @Test
    public void buildLockDTO() {
        psCommonScanServiceImpl.buildLockDTO(new LinkedList<>(), new LinkedList<>(), new LinkedList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void buildTechnicalControlDTO() {
        List<BarcodeControlDTO> list = new LinkedList<>();
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        List<PsWipInfo> wipList = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        wipList.add(a1);
        dto.setWipList(wipList);
        psCommonScanServiceImpl.buildTechnicalControlDTO(list, dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void buildTestDTO() {
        List<BarcodeControlDTO> list = new LinkedList<>();
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        List<String> snList = new LinkedList<>();

        List<FlowControlInfoDTO> chlidList = new LinkedList<>();
        FlowControlInfoDTO a1 = new FlowControlInfoDTO();
        chlidList.add(a1);
        entity.setProcessInfoList(chlidList);
        psCommonScanServiceImpl.buildTestDTO(list, entity, snList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void buildBindingDTO() throws Exception {
        List<BarcodeControlDTO> list = new LinkedList<>();
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        List<String> snList = new LinkedList<>();

        List<FlowControlInfoDTO> chlidList = new LinkedList<>();
        FlowControlInfoDTO a1 = new FlowControlInfoDTO();
        chlidList.add(a1);
        entity.setProcessInfoList(chlidList);
        psCommonScanServiceImpl.buildBindingDTO(list, entity, snList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkScrappedTest() throws Exception {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCurrProcessCode("P0008");
        try {
            Whitebox.invokeMethod(psCommonScanServiceImpl, "checkScrapped", psWipInfo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_STATUS_IS_REPAIR_SCRAP.equals(e.getExMsgId()));
        }
    }

    @Test
    public void checkUnnecessaryStaion() throws Exception {
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        PsWipInfo currWipInfo = new PsWipInfo();
        flow.setWipInfo(currWipInfo);
        currWipInfo.setLastProcess("N");
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);

        currWipInfo.setLastProcess("Y");
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6823,
                Constant.LOOKUP_CODE_6823002)).thenReturn(sysLookUpValue);
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);


        sysLookUpValue.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6823,
                Constant.LOOKUP_CODE_6823002)).thenReturn(sysLookUpValue);
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);

        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(null,
                null)).thenReturn(ctRouteDetailDTOList);
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);


        flow.setWorkStation("P1234");
        ctRouteDetailDTO.setIsUnnecessary("Y");
        ctRouteDetailDTO.setNextProcess("P1234");
        Whitebox.invokeMethod(psCommonScanServiceImpl, "checkUnnecessaryStaion", flow);
        Assert.assertEquals("Y", flow.getIsUnnecessary());
    }

    @Test
    public void checkRepair() {
        List<String> snlist = new LinkedList<>();
        snlist.add("123");
        psCommonScanServiceImpl.checkRepair(snlist);

        List<PmRepairRcvDetail> pmRepairRcvDetails = new LinkedList<>();
        PmRepairRcvDetail a1 = new PmRepairRcvDetail();
        a1.setIsAccept(new BigDecimal(2));
        a1.setSn("123");
        pmRepairRcvDetails.add(a1);
        PmRepairRcvDetail a2 = new PmRepairRcvDetail();
        a2.setSn("234");
        a2.setIsAccept(new BigDecimal(1));
        a2.setStatus("105600003");
        pmRepairRcvDetails.add(a2);
        PmRepairRcvDetail a3 = new PmRepairRcvDetail();
        a3.setSn("234");
        a3.setIsAccept(new BigDecimal(1));
        a3.setStatus("2");
        pmRepairRcvDetails.add(a3);
        PowerMockito.when(pmRepairRcvDetailRepository.selectPmRepairRcvDetailBatch(Mockito.any()))
                .thenReturn(pmRepairRcvDetails);
        try {
            psCommonScanServiceImpl.checkRepair(snlist);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HAS_NOT_ROLLOVER_PARAM, e.getMessage());
        }
    }

}
