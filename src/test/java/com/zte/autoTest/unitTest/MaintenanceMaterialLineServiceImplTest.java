package com.zte.autoTest.unitTest;

import com.zte.application.impl.MaintenanceMaterialLineServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.MaintenanceMaterialHeadRepository;
import com.zte.domain.model.MaintenanceMaterialLineRepository;
import com.zte.domain.model.PmRepairDetail;
import com.zte.domain.model.PmRepairDetailRepository;
import com.zte.domain.model.WorkorderOnline;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.SrmRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import lombok.ToString;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * $
 *
 * <AUTHOR>
 * @ProjectName develop
 * @PackageName com.zte.autoTest.unitTest
 * @Date 2021-03-05 15:05
 * @user 10275508
 */
public class MaintenanceMaterialLineServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private MaintenanceMaterialLineServiceImpl maintenanceMaterialLineServiceImpl;
    @Mock
    private MaintenanceMaterialLineRepository maintenanceMaterialLineRepository;
    @Mock
    private PmRepairDetailRepository pmRepairDetailRepository;
	@Mock
	private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private MaintenanceMaterialHeadRepository maintenanceMaterialHeadRepository;

    @Test
    public void testInsertBatchLines() throws Exception {
        List<MaintenanceMaterialLineDTO> list = new LinkedList<>();
        MaintenanceMaterialLineDTO a = new MaintenanceMaterialLineDTO();
        list.add(a);
        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.insertBatchLines(list));
    }

    @Test
    public void testDeleteBatchIds() {

        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.deleteBatchIds(new LinkedList<String>() {{
            add("123");
        }}));
    }

    @Test
    public void testQueryByHeadId() {
        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.queryByHeadId(new MaintenanceMaterialHeadDTO()));
    }

    @Test
    public void testUpdateBatchLines() {
        List<MaintenanceMaterialLineDTO> lines = new LinkedList<>();
        MaintenanceMaterialLineDTO a = new MaintenanceMaterialLineDTO();
        lines.add(a);
        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.updateBatchLines(lines));
    }

    @Test
    public void testQueryLinesPageByHeadId() throws Exception {
		PageRowsVO pageRowsVO = new PageRowsVO();
		MaintenanceMaterialHeadDTO dto = new MaintenanceMaterialHeadDTO();
		dto.setUpdateUser("00286569");
		pageRowsVO.setBo(dto);
		List<MaintenanceMaterialLineDTO> rows = new LinkedList<>();
		MaintenanceMaterialLineDTO dto1 = new MaintenanceMaterialLineDTO();
		dto1.setUpdateUser("00286569");
		rows.add(dto1);
		PowerMockito.when(maintenanceMaterialLineRepository.queryPageByHeadId(Mockito.any())).thenReturn(rows);
		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
		PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        Assert.assertThrows(NullPointerException.class, () -> maintenanceMaterialLineServiceImpl.queryLinesPageByHeadId(pageRowsVO));
    }

    @Mock
    private SrmRemoteService srmRemoteService;

    @Test
    public void queryItemBrandName() throws Exception {
        SplitItemNoDTO a1 = new SplitItemNoDTO();
        a1.setItemNo("124");
        SrmPageRows<SplitItemNoDTO> srmPageRows = new SrmPageRows<>();
        srmPageRows.setPages(2);
        srmPageRows.setList(new LinkedList<>());
        PowerMockito.when(srmRemoteService.queryItemBrandName(Mockito.any())).thenReturn(srmPageRows);
        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.queryItemBrandName(a1));
    }

    @Test
    public void returnRepairItem() throws Exception {
        MaintenanceMaterialLineDTO line = new MaintenanceMaterialLineDTO();
        line.setLineId("123");
        line.setHeadId("123");
        line.setItemStatus("5");
        line.setSubSn("123");

        List<PmRepairDetail> repairList = new ArrayList<>();

        PowerMockito.when(pmRepairDetailRepository.getList(Mockito.any())).thenReturn(repairList);
        List<MaintenanceMaterialLineDTO> maintenanceMaterialLineDTOList = new ArrayList<>();
        MaintenanceMaterialLineDTO maintenanceMaterialLineDTO=new MaintenanceMaterialLineDTO();
        maintenanceMaterialLineDTO.setItemStatus(MpConstant.RESOURCE_STATUS_RETURNED_MATERIAL);
        maintenanceMaterialLineDTOList.add(maintenanceMaterialLineDTO);
        PowerMockito.when(maintenanceMaterialLineRepository.queryByHeadId(any())).thenReturn(maintenanceMaterialLineDTOList);
        PowerMockito.when(maintenanceMaterialHeadRepository.updateHeadByHeadId(any())).thenReturn(1);
        Assert.assertNotNull(maintenanceMaterialLineServiceImpl.returnRepairItem(line));


    }

    @Test
    public void updateHeadStatus() throws Exception {
        List<MaintenanceMaterialLineDTO> maintenanceMaterialLineDTOList = new ArrayList<>();
        MaintenanceMaterialLineDTO maintenanceMaterialLineDTO=new MaintenanceMaterialLineDTO();
        maintenanceMaterialLineDTO.setItemStatus(MpConstant.RESOURCE_STATUS_RETURNED_MATERIAL);
        maintenanceMaterialLineDTOList.add(maintenanceMaterialLineDTO);
        PowerMockito.when(maintenanceMaterialLineRepository.queryByHeadId(any())).thenReturn(maintenanceMaterialLineDTOList);
        PowerMockito.when(maintenanceMaterialHeadRepository.updateHeadByHeadId(any())).thenReturn(1);
        Whitebox.invokeMethod(maintenanceMaterialLineServiceImpl, "updateHeadStatus", maintenanceMaterialLineDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void testSetMBom() throws Exception {
        // 创建并初始化PsEntityPlanBasic对象
        MaintenanceMaterialLineDTO dto1 = new MaintenanceMaterialLineDTO();
        dto1.setBomNo("11");
        dto1.setProdplanId("prodplanId_2");

        MaintenanceMaterialLineDTO dto2 = new MaintenanceMaterialLineDTO();
        dto2.setBomNo("11");
        dto2.setProdplanId("prodplanId_3");

        List<MaintenanceMaterialLineDTO> listEntity = new ArrayList<>();
        maintenanceMaterialLineServiceImpl.setMBom(listEntity);
        assertNotNull(listEntity);

        listEntity.add(dto1);
        listEntity.add(dto2);

        // 模拟远程服务调用返回null的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(null);
        maintenanceMaterialLineServiceImpl.setMBom(listEntity);
        assertNotNull(listEntity);

        // 创建并初始化BProdBomHeaderDTO对象
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo1");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_1");
        }});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo2");
            setProductCode("taskNo1_1");
            setProdplanId("prodplanId_2");
        }});

        // 模拟远程服务调用返回有效数据的情况
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOList);
        maintenanceMaterialLineServiceImpl.setMBom(listEntity);
        assertNotNull(listEntity);
    }
}