package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.impl.BarcodeUnlockServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.zte.common.utils.Constant.*;
import static org.mockito.Matchers.*;

@PrepareForTest({RedisHelper.class, ImesExcelUtil.class})
public class BarcodeUnlockServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    BarcodeUnlockServiceImpl service;

    @Mock
    private BarcodeLockHeadRepository headRepository;

    @Mock
    private BarcodeLockDetailRepository detailRepository;

    @Mock
    private BarcodeLockTempRepository tempRepository;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private HttpServletResponse httpServletResponse;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Test
    public void exportExcel()throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setType(LOCK_TYPE_BATCH);
            setCreateBy("2");
        }});

        List<BarcodeLockedPlanDTO> lockedDetail = new ArrayList<>();
        BarcodeLockedPlanDTO barcodeLockedPlanDTO = new BarcodeLockedPlanDTO();
        barcodeLockedPlanDTO.setLockedCrafts("1");
        lockedDetail.add(barcodeLockedPlanDTO);
        Assert.assertEquals("1", barcodeLockedPlanDTO.getLockedCrafts());
        PowerMockito.when(detailRepository.getLockedPlans(any())).thenReturn(lockedDetail);

        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(barcodeLockDetailService.getBsProcessList(any())).thenReturn(bsProcessList);
        BarcodeLockedQueryDTO barcodeLockedQueryDTO = new BarcodeLockedQueryDTO();
        barcodeLockedQueryDTO.setBillNo("12");
        service.exportExcel(httpServletResponse,barcodeLockedQueryDTO);
    }
    @Test
    public void exportExcel2()throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setType(LOCK_TYPE_SN);
            setCreateBy("2");
        }});
        List<BarcodeLockedSnDTO> lockedDetail = new ArrayList<>();
        BarcodeLockedSnDTO barcodeLockedPlanDTO = new BarcodeLockedSnDTO();
        barcodeLockedPlanDTO.setLockedCrafts("1");
        Assert.assertEquals("1", barcodeLockedPlanDTO.getLockedCrafts());
        lockedDetail.add(barcodeLockedPlanDTO);

        PowerMockito.when(detailRepository.getLockedSnByBillNo(any())).thenReturn(lockedDetail);
        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("1");
        bsProcess.setProcessName("贴片A面");
        bsProcessList.add(bsProcess);
        PowerMockito.when(barcodeLockDetailService.getBsProcessList(any())).thenReturn(bsProcessList);

        BarcodeLockedQueryDTO barcodeLockedQueryDTO = new BarcodeLockedQueryDTO();
        barcodeLockedQueryDTO.setBillNo("12");
        service.exportExcel(httpServletResponse,barcodeLockedQueryDTO);
    }
    @Test
    public void getLockedPlans() {
        service.getLockedPlans(new BarcodeLockedQueryDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getLockedSnByBillNo() {
        service.getLockedSnByBillNo(new BarcodeLockedQueryDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getLockedSnByPlan() {
        service.getLockedSnByPlan(new BarcodeLockedQueryDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void unlockSn() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setType(LOCK_TYPE_SN);
            setCreateBy("2");
        }});
        try {
            PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(new HashMap(){{
                put("1", new HrmPersonInfoDTO(){{setEmpName("1");}});
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setSns(Lists.newArrayList("1"));
                setCcTo(Lists.newArrayList("1"));
                setUnlockUser("1");
                setUnlockReason("1");
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals( MessageId.UNLOCK_SN_IS_EMPTY, e.getMessage());
             }
    }

    @Test
    public void unlockBatchSn() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setType(LOCK_TYPE_BATCH);
            setCreateBy("2");
        }});
        try {
            PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(new HashMap(){{
                put("1", new HrmPersonInfoDTO(){{setEmpName("1");}});
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setProdPlanId("1");
                setSns(Lists.newArrayList("1"));
                setCcTo(Lists.newArrayList("1"));
                setUnlockUser("1");
                setUnlockReason("1");
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals( MessageId.UNLOCK_PLAN_IS_EMPTY, e.getMessage());
            }
    }

    @Test
    public void unlockBatchCraft() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setType(LOCK_TYPE_BATCH);
            setCreateBy("2");
        }});
        try {
            PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(new HashMap(){{
                put("2", new HrmPersonInfoDTO(){{setEmpName("1");}});
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setProdPlanId("1");
                setUnlockCrafts(Lists.newArrayList("1"));
                setUnlockCraftNames("1");
                setCcTo(Lists.newArrayList("1"));
                setUnlockUser("1");
                setUnlockReason("1");
            }});
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setProdPlanId("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals( MessageId.UNLOCK_CRAFT_SN_ALL_EMPTY, e.getMessage());
}
    }
    @Test
    public void unlockBatchError() {
        try {
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setProdPlanId("1");
            }});
        } catch (Exception e) {
            String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);}
        PowerMockito.when(headRepository.getHeadInfoByBillNo(anyString())).thenReturn(new BarcodeLockHead(){{
            setStatus(IN_PREPARATION);
        }});
        try {
            service.unlock(new BarcodeUnlockDTO(){{
                setBillNo("1");
                setProdPlanId("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals( MessageId.PREPARATION_CAN_NOT_UNLOCK, e.getMessage());
        }
    }
}