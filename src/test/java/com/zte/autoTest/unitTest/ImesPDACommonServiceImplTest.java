package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.AvlService;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.SmtFeedErrorSkipService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.TransferStrategyInfoService;
import com.zte.application.impl.ImesPDACommonServiceImpl;
import com.zte.application.impl.SmtMachineMTLHistoryHServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.BaItemSupplier;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.PDATransferScanRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.TransferStrategyInfo;
import com.zte.domain.model.WorkorderOnline;
import com.zte.domain.model.WorkorderOnlineRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.ASMTransferScanDTO;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.CtRouteHead;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.interfaces.dto.OneKeySwitchMoutingDTO;
import com.zte.interfaces.dto.OneKeySwitchScanLocationDto;
import com.zte.interfaces.dto.PDAQCSpotCheckDTO;
import com.zte.interfaces.dto.PDAQCSpotCheckPkCodeDTO;
import com.zte.interfaces.dto.PDAReceiveCheckItemDTO;
import com.zte.interfaces.dto.PDAReceiveItemsScanDTO;
import com.zte.interfaces.dto.PDAReelIdQtyModifyDto;
import com.zte.interfaces.dto.PDAReelIdSplitDto;
import com.zte.interfaces.dto.PDATransferScanCommonDto;
import com.zte.interfaces.dto.PDATransferScanDTO;
import com.zte.interfaces.dto.PDATransferScanModuleDto;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SmtMachineMaterialMoutingDTO;
import com.zte.interfaces.dto.StCodeInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.FLAG_Y;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;

@PrepareForTest({HttpRemoteService.class, ConstantInterface.class, CommonUtils.class, BasicsettingRemoteService.class,
        ObtainRemoteServiceDataUtil.class, PlanscheduleRemoteService.class, MicroServiceRestUtil.class, ProductionDeliveryRemoteService.class, EqpmgmtsRemoteService.class})
public class ImesPDACommonServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ImesPDACommonServiceImpl service;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;

    @Mock
    private TransferStrategyInfoService transferStrategyInfoService;

    @Mock
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;

    @Mock
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;

    @Mock
    private PkCodeHistoryService pkCodeHistoryService;

    @Mock
    private AvlService avlService;

    @Mock
    private PsScanHistoryService psScanHistoryService;

    @Mock
    private SmtFeedErrorSkipService smtFeedErrorSkipService;

    @Mock
    private BSmtBomDetailService bSmtBomDetailService;

    @Mock
    private PkCodeInfoService pkCodeInfoService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    private PDATransferScanRepository pdaTransferScanRepository;

    @Mock
    private WorkorderOnlineRepository workorderOnlineRepository;

    @Mock
    private EqpmgmtsRemoteService eqpmgmtsRemoteService;
    @Mock
    private IscpRemoteService iscpRemoteService;

    @Mock
    private DatawbRemoteService datawbRemoteService;


    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void setEntityInfo() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class, PlanscheduleRemoteService.class);
        service.setEntityInfo(new ASMTransferScanDTO(), new String(""), new String("2"), "", "");

        service.getLocationInfos("");

        service.verificationOccupied(new PkCodeInfo());

        service.getPkCodeInfos(new String(""));

        service.getPsWorkOrderDTOS(new String(""), "", "");

        service.getbSmtBomDetails(new ASMTransferScanDTO(), "", "", "");

        service.getSmtMachineMaterialMoutings(new ASMTransferScanDTO());

        Assert.assertNotNull(service.asmTransferScanEvent(new ASMTransferScanDTO(), new PkCodeInfo(), "", ""));
    }

    @Test
    public void execPolarCheckEvent() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, ConstantInterface.class, HttpRemoteService.class);
        String result = JSON.toJSONString(new ServiceData() {{
            setBo(new BaItemSupplier());
        }});
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(result);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setProductTask("7777888");
        pkCodeInfo.setSysLotCode("1");
        pkCodeInfo.setItemCode("item1");
        pkCodeInfo.setItemQty(new BigDecimal("5"));

        PkCodeInfo oldPkCodeInfo = new PkCodeInfo();
        oldPkCodeInfo.setProductTask("7777888");
        oldPkCodeInfo.setSysLotCode("7777888");
        oldPkCodeInfo.setItemCode("item1");
        oldPkCodeInfo.setItemQty(new BigDecimal("5"));
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        entity.setOldQty(new BigDecimal("5"));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new BaItemSupplier() {{
                        setBraidDirection("2");
                        setIsHasDir(Short.parseShort("1"));
                    }});
                }}));
        PowerMockito.when(HttpRemoteService.remoteExe(any(), any(), anyMap(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new BaItemSupplier() {{
                        setBraidDirection("2");
                        setIsHasDir(Short.parseShort("1"));
                    }});
                }}));
        PkCodeInfo pkCodeResult = new PkCodeInfo();
        pkCodeResult.setItemAngle("0");
        List<ItemSplitInfoDTO> newItemSplitList = new ArrayList<>();
        ItemSplitInfoDTO itemSplitInfoDTO = new ItemSplitInfoDTO();
        itemSplitInfoDTO.setBraidDirection("0");
        newItemSplitList.add(itemSplitInfoDTO);
        List<StCodeInfoDTO> stCodeInfoList = new ArrayList<>();
        StCodeInfoDTO stCodeInfoDTO = new StCodeInfoDTO();
        stCodeInfoDTO.setCode("0");
        stCodeInfoDTO.setCodeDesc("0度");
        stCodeInfoList.add(stCodeInfoDTO);
        PowerMockito.when(datawbRemoteService.getStCodeInfoList()).thenReturn(stCodeInfoList);
        PowerMockito.when(iscpRemoteService.getItemSplitInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(new ArrayList<>()).thenReturn(newItemSplitList);
        PowerMockito.when(pkCodeInfoService.getItemDirectionByPkCode(Mockito.any()))
                .thenReturn(pkCodeResult);
        Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void verifyPrepareMachineMaterialForTransferScan() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CommonUtils.class, BasicsettingRemoteService.class);
        PDATransferScanDTO pdaTransferScanDTO = JSON.parseObject("{\"entityId\":\"2\",\"factoryId\":\"55\",\"userId\":\"10270446\",\"pkCode\":\"ZTE202201060000113\",\"formFlag\":\"0\",\"lastScanFlag\":\"True\",\"lineCode\":\"SMT-HY012\",\"workOrder\":\"8888621-SMT-A5502\",\"mWorkOrder\":\"8888621-SMT-A5502\",\"cfgHeaderId\":\"6ef48d1c-feeb-421a-bb2e-3c2871ca7a19\",\"sourceTask\":\"8888621\",\"bomNo\":\"129580160029AAB\",\"drLocationNo\":\"2-39-2\",\"drItemCode\":\"045020100092\",\"drMachineNo\":\"(X4S-2)\",\"moduleNo\":\"(X4S-2)2\",\"lineCode2\":\"\",\"workOrder2\":\"\",\"cfgHeaderId2\":\"\"}",
                PDATransferScanDTO.class);
        List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        prepareList.add(smtMachineMaterialPrepare);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString()))
                .thenReturn("");
        List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
        PsWorkOrderDTO workOrder = new PsWorkOrderDTO();
        workOrder.setCraftSection("SMT-A");
        workOrder.setTaskNo("taskNo1");
        workOrder.setLineCode("SMT-HY012");
        workOrderList.add(workOrder);
        PsWorkOrderDTO workOrder2 = new PsWorkOrderDTO();
        workOrder2.setTaskNo("taskNo1");
        workOrder2.setCraftSection("SMT-B");
        workOrder2.setLineCode("SMT-HY012");
        workOrderList.add(workOrder2);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(), any())).thenReturn(workOrderList);
        PsWorkOrderSmt b1 = new PsWorkOrderSmt();
        b1.setTransferStrategy("1");
        b1.setCfgHeaderId("UUID-1");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderSMTInfo(anyMap())).thenReturn(b1);
        Whitebox.invokeMethod(service, "verifyPrepareMachineMaterialForTransferScan", pdaTransferScanDTO, true, prepareList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void pkCodeSourceTaskCheck() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class, PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(Lists.newArrayList(new PsTask()));
        PowerMockito.when(ObtainRemoteServiceDataUtil.getRouteHeadInfo(any())).thenReturn(Lists.newArrayList(new CtRouteHead() {{
            setCraftStatus(Constant.IS_SUBMITTED);
        }}));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setRouteId("1");
        }}));
        service.pkCodeSourceTaskCheck("", "");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO()));
        Assert.assertThrows(MesBusinessException.class, () -> service.pkCodeSourceTaskCheck("", ""));

        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList());

        try {
            Assert.assertTrue(service.pkCodeSourceTaskCheck("", ""));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getMessage());
        }
    }


    @Test
    public void checkPickUp() throws Exception {
        SmtMachineMaterialMoutingDTO dto = new SmtMachineMaterialMoutingDTO();
        List<SmtMachineMTLHistoryH> list = new ArrayList<>();
        list.add(new SmtMachineMTLHistoryH());
        PowerMockito.when(smtMachineMTLHistoryHService.getList(anyMap(), anyString(), anyString(), anyLong(), any())).thenReturn(list);
        service.checkPickUp(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handFeedingScan() throws Exception {
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setProductTask("7777888");
        pkCodeInfo.setItemCode("item1");
        pkCodeInfo.setItemQty(new BigDecimal("5"));
        PowerMockito.when(pkCodeInfoRepository.getPkCodeInfoByCode(any())).thenReturn(pkCodeInfo);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://test");
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        ServiceData serviceData = new ServiceData();
        Map map = new HashMap<>();
        map.put("status", "E");
        serviceData.setBo(map);
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setSourceTask("7777888");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(anyString())).thenReturn(psWorkOrderBasic);
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("item1");
        bSmtBomDetailList.add(bSmtBomDetail);
        PowerMockito.when(bSmtBomDetailService.getList(anyMap(), anyString(), anyString())).thenReturn(bSmtBomDetailList);
        List<BPcbLocationDetail> bPcbLocationDetails = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetails.add(bPcbLocationDetail);
        PowerMockito.when(centerfactoryRemoteService.getBPcbLocList(any())).thenReturn(bPcbLocationDetails);


        SmtMachineMaterialMoutingDTO dto = new SmtMachineMaterialMoutingDTO();
        dto.setLineCode("1");
        dto.setWorkOrder("777888");
        dto.setCfgHeaderId("222");
        dto.setObjectId("22tf");
        dto.setLastScanFlag("Y");
        try {
            Assert.assertNotNull(service.handFeedingScan(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SMT_MACHINE_MATERIAL_OF_WORKORDER_IS_NULL, e.getMessage());
        }

    }

    @Test
    public void validateVirtualLocation() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SmtMachineMaterialMoutingDTO machineMaterialMoutingDTO = new SmtMachineMaterialMoutingDTO();
        machineMaterialMoutingDTO.setCfgHeaderId("1234");
        machineMaterialMoutingDTO.setLocationNo("1234");
        machineMaterialMoutingDTO.setOperateType("Y");
        List<String> virtualLocationList = new ArrayList<>();
        virtualLocationList.add("Y");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("true");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(bSmtBomDetailRepository.getVirtualFlagByLocationNo(any(), any())).thenReturn(virtualLocationList);
        try {
            Assert.assertNotNull(service.validateVirtualLocation(machineMaterialMoutingDTO));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_LOCATIONNO_IS_VIRTUAL, e.getMessage());
        }
    }

    @Test
    public void polarCheck() throws Exception {
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        PowerMockito.mockStatic(MicroServiceRestUtil.class,BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any()))
                .thenReturn("N");
        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupCode(new BigDecimal("1071001"));
        a1.setLookupMeaning("Y");
        lookupValueList.add(a1);

        PowerMockito.when(MicroServiceRestUtil.invokeService(MicroServiceNameEum.BASICSETTING, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET
                , "/BS/findListByLookupType", "{\"lookupType\":\"1071\"}",
                MESHttpHelper.getHttpRequestHeader())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": null,\n" +
                "  \"other\": null\n" +
                "}");

        Assert.assertNotNull(service.polarCheck(entity));
    }

    @Test
    public void pdaTransferScanEvent() throws Exception {
        service.pdaTransferScanEvent(new PDATransferScanDTO(), new PkCodeInfo(), "1", "1", Pair.of("1", false));
        PowerMockito.when(smtMachineMaterialMoutingService.countLocationMt(any())).thenReturn(1);
        service.pdaTransferScanEvent(new PDATransferScanDTO(), new PkCodeInfo(), "1", "1", Pair.of("1", false));

        service.pdaTransferScanEvent(new PDATransferScanDTO() {{
            setFormFlag(new BigDecimal(0));
            setWorkOrder("w");
        }}, new PkCodeInfo(), "1", "1", Pair.of("1", true));
        service.pdaTransferScanEvent(new PDATransferScanDTO() {{
            setFormFlag(new BigDecimal(0));
            setWorkOrder("w");
            setOldReelId("2");
        }}, new PkCodeInfo(), "1", "1", Pair.of("1", true));
        String s = service.pdaTransferScanEvent(new PDATransferScanDTO() {{
            setFormFlag(new BigDecimal(0));
            setWorkOrder("w");
        }}, new PkCodeInfo(), "1", "1", Pair.of("1", false));
        Assert.assertTrue(StringUtils.isBlank(s));
    }

    @Test
    public void checkPrepare() {
        service.checkPrepare(true, Lists.newArrayList(new SmtMachineMaterialPrepare()), new SysLookupValuesDTO());
        service.checkPrepare(false, Lists.newArrayList(), new SysLookupValuesDTO() {{
            setAttribute1("Y");
        }});
        service.checkPrepare(false, Lists.newArrayList(), new SysLookupValuesDTO());
        service.checkPrepare(false, Lists.newArrayList(new SmtMachineMaterialPrepare()), new SysLookupValuesDTO());
        service.checkPrepare(false, Lists.newArrayList(new SmtMachineMaterialPrepare()), new SysLookupValuesDTO() {{
            setAttribute1("Y");
        }});
        Assert.assertTrue(service.checkPrepare(false, Lists.newArrayList(new SmtMachineMaterialPrepare() {{
            setFeederNo("1");
        }}), new SysLookupValuesDTO() {{
            setAttribute1("Y");
        }}));
    }

    @Test
    public void polarCheckTwo() throws Exception {
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        entity.setOldPkCode("test1");
        entity.setNewPkCode("test2");
        entity.setCheckPkCode("test3");
        entity.setOldQty(new BigDecimal(2));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(1071001));
        sysLookupValuesDTO.setLookupMeaning("Y");
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(lookupValueList);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(anyString())).thenReturn("N");
        List<PkCodeInfo> pkCodeList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("test2");
        pkCodeList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);

        PowerMockito.when(ProductionDeliveryRemoteService.checkWmsHasReelId(anyString())).thenReturn(true);
        Assert.assertNotNull(service.polarCheck(entity));

        PkCodeInfo oldMounting = new PkCodeInfo();
        PDAReceiveItemsScanDTO entity1 = new PDAReceiveItemsScanDTO();
        entity1.setItemCodeFlag(true);
        entity1.setOldQty(new BigDecimal(1));
        service.polarCheck(entity1);

        entity1.setItemCodeFlag(false);
        service.polarCheck(entity1);
    }

    @Test
    public void polarCheckThree() throws Exception {
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        entity.setOldPkCode("test1");
        entity.setNewPkCode("test2");
        entity.setCheckPkCode("test3");
        entity.setOldQty(new BigDecimal(2));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(1071001));
        sysLookupValuesDTO.setLookupMeaning("Y");
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(lookupValueList);
        List<PkCodeInfo> pkCodeList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("test2");
        pkCodeList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);

        PowerMockito.when(ProductionDeliveryRemoteService.checkWmsHasReelId(anyString())).thenReturn(false);
        Assert.assertNotNull(service.polarCheck(entity));
    }

    @Test
    public void updateMountingInfoWithObjectId() {
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        PkCodeInfo newPkCodeObj = new PkCodeInfo();
        try {
            service.updateMountingInfoWithObjectId(entity, newPkCodeObj);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_MOUTING_FAILED, e.getMessage());
        }
    }

    @Test
    public void pkCodeCastleOrAppropriation() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class, CommonUtils.class, PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        List<PDAReceiveItemsScanDTO> entity = new ArrayList<>();
        PDAReceiveItemsScanDTO dto1 = new PDAReceiveItemsScanDTO();
        entity.add(dto1);
        dto1.setNewPkCode("NewPkCode");
        dto1.setSourceTask("SourceTask");
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(null);

        List<PkCodeInfo> pkCodeList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("test2");
        pkCodeList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);

        pkCodeInfo.setProductTask("SourceTask-1");
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);

        List<PsTask> taskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        taskList.add(psTask);
        psTask.setPartsPlanno("ProdplanNo");
        psTask.setProdplanNo("ProdplanNo11");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(taskList);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setRouteId("1");
        }}));
        List<CtRouteDetail> dtlListOne = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        dtlListOne.add(ctRouteDetail);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getCtRouteDetailInfo(any())).thenReturn(dtlListOne);
        PowerMockito.when(transferStrategyInfoService.getList(any())).thenReturn(null);


        Boolean b = service.pkCodeCastleOrAppropriation("newSourceTask", "oldSourceTask", "itemCode", "lineCode", "10317937");
        Assert.assertTrue(!b);

        psTask.setProdplanNo("ProdplanNo");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(taskList);
        b = service.pkCodeCastleOrAppropriation("newSourceTask", "oldSourceTask", "itemCode", "lineCode", "10317937");
        Assert.assertTrue(b);
    }

    @Test
    public void pdaReceiveItemsScan() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class, CommonUtils.class, PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        PowerMockito.when(pkCodeInfoRepository.updatePkCodeInfoById(any())).thenReturn(1);
        PowerMockito.when(transferStrategyInfoService.markUsed(any(),any())).thenReturn(1);

        List<PDAReceiveItemsScanDTO> entity = new ArrayList<>();
        PDAReceiveItemsScanDTO dto1 = new PDAReceiveItemsScanDTO();
        entity.add(dto1);
        dto1.setNewPkCode("NewPkCode");
        dto1.setSourceTask("SourceTask");
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(null);
        RetCode retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());

        List<PkCodeInfo> pkCodeList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("test2");
        pkCodeList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());

        pkCodeInfo.setProductTask("SourceTask-1");
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeList);

        List<PsTask> taskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        taskList.add(psTask);
        psTask.setPartsPlanno("ProdplanNo");
        psTask.setProdplanNo("ProdplanNo11");

        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(any())).thenReturn(taskList);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(any())).thenReturn(Lists.newArrayList(new PsWorkOrderDTO() {{
            setRouteId("1");
        }}));
        List<CtRouteDetail> dtlListOne = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        dtlListOne.add(ctRouteDetail);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getCtRouteDetailInfo(any())).thenReturn(dtlListOne);
        PowerMockito.when(transferStrategyInfoService.getList(any())).thenReturn(null);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());


        List<TransferStrategyInfo> strategies = new ArrayList<>();
        TransferStrategyInfo strategyInfo = new TransferStrategyInfo();
        strategies.add(strategyInfo);
        PowerMockito.when(transferStrategyInfoService.getList(any())).thenReturn(strategies);
        try {
            retCode = service.pdaReceiveItemsScan(entity);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER_OR_NEXT, e.getMessage());
        }

        strategyInfo.setPreProdplanId("SourceTask-1");
        PowerMockito.when(transferStrategyInfoService.getList(any())).thenReturn(strategies);
        List<PkCodeInfo> pkCodeList1 = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        pkCodeList1.add(pkCodeInfo1);
        PowerMockito.when(pkCodeInfoRepository.getNotUsed(any())).thenReturn(pkCodeList1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_RECEIVE_APPROPRIATION)).thenReturn(null);
        try {
            retCode = service.pdaReceiveItemsScan(entity);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MATERIAL_TRANSFER_OPERATION_ALLOWED, e.getMessage());
        }

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("10317937");
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_RECEIVE_APPROPRIATION)).thenReturn(lookupValueList);
        try {
            retCode = service.pdaReceiveItemsScan(entity);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MATERIAL_TRANSFER_OPERATION_ALLOWED, e.getMessage());
        }


        dto1.setEmpNo("10327937");
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_RECEIVE_APPROPRIATION)).thenReturn(lookupValueList);
        try {
            retCode = service.pdaReceiveItemsScan(entity);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NO_MATERIAL_TRANSFER_OPERATION_ALLOWED, e.getMessage());
        }

        dto1.setEmpNo("10317937");
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_RECEIVE_APPROPRIATION)).thenReturn(lookupValueList);
        pkCodeInfo.setItemQty(BigDecimal.ZERO);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());

        PowerMockito.when(pkCodeInfoRepository.getNotUsed(any())).thenReturn(null);
        pkCodeInfo.setItemQty(BigDecimal.ZERO);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());

        pkCodeInfo.setItemQty(BigDecimal.TEN);
        pkCodeInfo.setItemCode("ItemCode");
        dto1.setOldItemCode("ItemCode1");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString()))
                .thenReturn("aaa");
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new BaItemSupplier() {{
                        setBraidDirection("2");
                        setIsHasDir(Short.parseShort("1"));
                    }});
                }}));
        Assert.assertThrows(NullPointerException.class, () -> service.pdaReceiveItemsScan(entity));

        dto1.setOldItemCode("ItemCode");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo("");
        PowerMockito.when(avlService.getAvlDTOList(any()))
                .thenReturn(serviceData);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, retCode.getCode());

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        dto1.setOldQty(BigDecimal.ZERO);
        PowerMockito.when(smtMachineMaterialMoutingRepository.updateSmtMachineMaterialMoutingByOthers(any())).thenReturn(1);
        retCode = service.pdaReceiveItemsScan(entity);
        Assert.assertEquals(RetCode.SUCCESS_CODE, retCode.getCode());
    }

    @Test
    public void getModuleBSmtBomDetailInfo() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("该模组下没有上料信息");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }

        dto.setMountType("1");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }
        dto.setPickStatusString("1");
        dto.setModuleNo("moduleNo");
        List<PDATransferScanModuleDto> moduleList = new ArrayList<>();
        moduleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
            setMaterialTray("materialTray1");
        }});
        dto.setmIsBothSides(true);
        dto.setmOtherSideWorkOrderNo("mWorkOrder");
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
        }});
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo2");
            setLocationNo("locationNo2");
            setMachineNo("machineNo2");
            setMaterialTray("materialTray2");
        }});
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);

        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("已经比对完毕，请换模组");
        ServiceData<List<PDATransferScanModuleDto>> ret2 = service.getModuleBSmtBomDetailInfo(dto);
        Assert.assertEquals(2, ret2.getBo().size());
        Assert.assertEquals("已经比对完毕，请换模组", ret2.getCode().getMsg());

        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("relatedHeaderId");
        dto.setRelatedWorkOrder("relatedWorkOrder");
        dto.setmRelatedLine("relatedLine");

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);
        ServiceData<List<PDATransferScanModuleDto>> ret3 = service.getModuleBSmtBomDetailInfo(dto);
        Assert.assertEquals(2, ret3.getBo().size());
        Assert.assertEquals("relatedLine", ret3.getBo().get(0).getOtherLineCode());
    }

    @Test
    public void getModuleBSmtBomDetailInfo1() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("该模组下没有上料信息");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }

        dto.setMountType("1");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }

        dto.setPickStatusString("1");
        PDATransferScanModuleDto transferScanModuleDto = new PDATransferScanModuleDto();
        transferScanModuleDto.setFactoryId(new BigDecimal(52));
        transferScanModuleDto.setCfgHeaderId("id");
        transferScanModuleDto.setWorkOrder("workOrder");
        transferScanModuleDto.setLineCode("lineCode");
        transferScanModuleDto.setAttr1("attr1");
        transferScanModuleDto.setMountType("1");
        transferScanModuleDto.setPickStatusString("1");
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }

        List<PDATransferScanModuleDto> moduleList0 = new ArrayList<>();
        moduleList0.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
            setMaterialTray("materialTray1");
            setPickStatusString("1");
        }});
        try {
            service.getModuleBSmtBomDetailInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }
        dto.setModuleNo("moduleNo");
        List<PDATransferScanModuleDto> moduleList = new ArrayList<>();
        moduleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
            setMaterialTray("materialTray1");
        }});
        dto.setmIsBothSides(true);
        dto.setmOtherSideWorkOrderNo("mWorkOrder");
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
        }});
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo2");
            setLocationNo("locationNo2");
            setMachineNo("machineNo2");
            setMaterialTray("materialTray2");
        }});
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);

        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("已经比对完毕，请换模组");
        ServiceData<List<PDATransferScanModuleDto>> ret2 = service.getModuleBSmtBomDetailInfo(dto);
        Assert.assertEquals(2, ret2.getBo().size());
        Assert.assertEquals("已经比对完毕，请换模组", ret2.getCode().getMsg());

        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("relatedHeaderId");
        dto.setRelatedWorkOrder("relatedWorkOrder");
        dto.setmRelatedLine("relatedLine");

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);
        ServiceData<List<PDATransferScanModuleDto>> ret3 = service.getModuleBSmtBomDetailInfo(dto);
        Assert.assertEquals(2, ret3.getBo().size());
        Assert.assertEquals("relatedLine", ret3.getBo().get(0).getOtherLineCode());

    }

    @Test
    public void getTransferScanModuleSelectData() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);

        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_TASK_IS_NULL, e.getMessage());
        }

        dto.setSourceTask("sourceTask");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }
        dto.setMountType("XXX");
        dto.setCfgHeaderId("id");
        dto.setLineCode("lineCode");
        dto.setWorkOrder("workOrder");
        dto.setSourceTask("sourceTask");
        dto.setMountType("1");
        dto.setCraftSection("SMT-B");
        dto.setRemark("0");
        dto.setmOtherSideCfgHeaderId("");
        dto.setmIsBothSides(false);
        dto.setRelatedWorkOrder("");
        dto.setPickStatusString("1");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo("");
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, ""));
        PowerMockito.when(smtMachineMTLHistoryHService.checkWorkOrderCanDeliver(any())).thenReturn(serviceData.getCode());

        //非AB同时转机、非双轨线
        List<String> moduleNoList = new ArrayList<>();
        PowerMockito.when(pdaTransferScanRepository.getModuleNoList(any())).thenReturn(moduleNoList);
        List<PDATransferScanCommonDto> baseList = service.getTransferScanModuleSelectData(dto);
        Assert.assertEquals(0, baseList.size());
        moduleNoList.add("(X4S-2)1");
        moduleNoList.add("(X4S-2)2");
        if (dto.getMountType().equals("1")) {
            getModuleForTransferScan();
        }

        List<PDATransferScanCommonDto> selectData1 = service.getTransferScanModuleSelectData(dto);
        selectData1.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)1");
            setCompletedFlag(true);
        }});
        selectData1.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)2");
            setCompletedFlag(false);
        }});
        Assert.assertEquals("(X4S-2)1", selectData1.get(0).getModuleNo());
        Assert.assertEquals(true, selectData1.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", selectData1.get(1).getModuleNo());
        Assert.assertEquals(false, selectData1.get(1).getCompletedFlag());
        if (dto.getMountType().equals("3")) {
            getModuleForQCTransferInspection();
        }
        List<PDATransferScanCommonDto> selectData2 = service.getTransferScanModuleSelectData(dto);
        Assert.assertEquals("(X4S-2)1", selectData2.get(0).getModuleNo());
        Assert.assertEquals(true, selectData2.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", selectData2.get(1).getModuleNo());
        Assert.assertEquals(false, selectData2.get(1).getCompletedFlag());
        if (dto.getMountType().equals("17")) {
            getModuleForQCTransferInspection();
        }
        List<PDATransferScanCommonDto> selectData3 = service.getTransferScanModuleSelectData(dto);
        Assert.assertEquals("(X4S-2)1", selectData3.get(0).getModuleNo());
        Assert.assertEquals(true, selectData3.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", selectData3.get(1).getModuleNo());
        Assert.assertEquals(false, selectData3.get(1).getCompletedFlag());

        dto.setMountType("3");
        service.getTransferScanModuleSelectData(dto);

        dto.setMountType("17");
        service.getTransferScanModuleSelectData(dto);

        dto.setMountType("15");
        service.getTransferScanModuleSelectData(dto);

    }

    @Test
    public void getTransferScanModuleSelectData1() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);

        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_TASK_IS_NULL, e.getMessage());
        }

        dto.setSourceTask("sourceTask");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }
        dto.setCfgHeaderId("id");
        dto.setLineCode("lineCode");
        dto.setWorkOrder("workOrder");
        dto.setSourceTask("sourceTask");
        dto.setMountType("3");
        dto.setCraftSection("SMT-B");
        dto.setRemark("0");
        dto.setmOtherSideCfgHeaderId("");
        dto.setmIsBothSides(false);
        dto.setRelatedWorkOrder("");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo("");
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, ""));
        PowerMockito.when(smtMachineMTLHistoryHService.checkWorkOrderCanDeliver(any())).thenReturn(serviceData.getCode());

        //非AB同时转机、非双轨线
        List<String> moduleNoList = new ArrayList<>();
        PowerMockito.when(pdaTransferScanRepository.getModuleNoList(any())).thenReturn(moduleNoList);
        List<PDATransferScanCommonDto> baseList = service.getTransferScanModuleSelectData(dto);
        Assert.assertEquals(0, baseList.size());
        moduleNoList.add("(X4S-2)1");
        moduleNoList.add("(X4S-2)2");
        if (dto.getMountType().equals("3")) {
            getModuleForQCTransferInspection();
        }
        List<PDATransferScanCommonDto> selectData2 = service.getTransferScanModuleSelectData(dto);
        selectData2.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)1");
            setCompletedFlag(true);
        }});
        selectData2.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)2");
            setCompletedFlag(false);
        }});
        Assert.assertEquals("(X4S-2)1", selectData2.get(0).getModuleNo());
        Assert.assertEquals(true, selectData2.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", selectData2.get(1).getModuleNo());
        Assert.assertEquals(true, selectData2.get(1).getCompletedFlag());
    }
    @Test
    public void getTransferScanModuleSelectData2() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);

        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_TASK_IS_NULL, e.getMessage());
        }

        dto.setSourceTask("sourceTask");
        try {
            service.getTransferScanModuleSelectData(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }
        dto.setMountType("XXX");
        dto.setCfgHeaderId("id");
        dto.setLineCode("lineCode");
        dto.setWorkOrder("workOrder");
        dto.setSourceTask("sourceTask");
        dto.setMountType("17");
        dto.setCraftSection("SMT-B");
        dto.setRemark("0");
        dto.setmOtherSideCfgHeaderId("");
        dto.setmIsBothSides(false);
        dto.setRelatedWorkOrder("");
        dto.setPickStatusString("1");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo("");
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, ""));
        PowerMockito.when(smtMachineMTLHistoryHService.checkWorkOrderCanDeliver(any())).thenReturn(serviceData.getCode());

        //非AB同时转机、非双轨线
        List<String> moduleNoList = new ArrayList<>();
        PowerMockito.when(pdaTransferScanRepository.getModuleNoList(any())).thenReturn(moduleNoList);
        List<PDATransferScanCommonDto> baseList = service.getTransferScanModuleSelectData(dto);
        Assert.assertEquals(0, baseList.size());
        moduleNoList.add("(X4S-2)1");
        moduleNoList.add("(X4S-2)2");
        if (dto.getMountType().equals("17")) {
            getModuleForQCTransferInspection();
        }
        List<PDATransferScanCommonDto> selectData3 = service.getTransferScanModuleSelectData(dto);
        selectData3.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)1");
            setCompletedFlag(true);
        }});
        selectData3.add(new PDATransferScanCommonDto() {{
            setModuleNo("(X4S-2)2");
            setCompletedFlag(false);
        }});
        Assert.assertEquals("(X4S-2)1", selectData3.get(0).getModuleNo());
        Assert.assertEquals(true, selectData3.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", selectData3.get(1).getModuleNo());
        Assert.assertEquals(true, selectData3.get(1).getCompletedFlag());
    }
    @Test
    public void getModuleForQCTransferInspection() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);
        dto.setCfgHeaderId("id");
        dto.setLineCode("lineCode");
        dto.setWorkOrder("workOrder");
        dto.setSourceTask("sourceTask");
        dto.setMountType("3");
        dto.setCraftSection("SMT-B");
        dto.setRemark("0");
        dto.setmOtherSideCfgHeaderId("");
        dto.setmIsBothSides(false);
        dto.setRelatedWorkOrder("");
        List<PDATransferScanModuleDto> moduleList = new ArrayList<>();
        moduleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
            setMaterialTray("materialTray1");
        }});

        dto.setmIsBothSides(true);
        dto.setmOtherSideWorkOrderNo("mWorkOrder");
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
        }});
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo2");
            setLocationNo("locationNo2");
            setMachineNo("machineNo2");
            setMaterialTray("materialTray2");
        }});
        ArrayList<String> moduleNoList = new ArrayList<>();
        moduleNoList.add("moduleNo1");
        moduleNoList.add("moduleNo2");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);
        List<PDATransferScanCommonDto> list1 = Whitebox.invokeMethod(service, "getModuleForQCTransferInspection", moduleNoList, dto);
        Assert.assertEquals("moduleNo1", list1.get(0).getModuleNo());
        Assert.assertEquals("moduleNo2", list1.get(1).getModuleNo());
        Assert.assertEquals(2, list1.size());

        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("relatedHeaderId");
        dto.setRelatedWorkOrder("relatedWorkOrder");
        dto.setmRelatedLine("relatedLine");
        List<PDATransferScanModuleDto> relateModuleList = new ArrayList<>();
        relateModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo3");
            setLocationNo("locationNo3");
            setMachineNo("machineNo3");
        }});
        ArrayList<String> moduleNoList2 = new ArrayList<>();
        moduleNoList2.add("moduleNo1");
        moduleNoList2.add("moduleNo3");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, relateModuleList);
        List<PDATransferScanCommonDto> list2 = Whitebox.invokeMethod(service, "getModuleForQCTransferInspection", moduleNoList2, dto);
        Assert.assertEquals("moduleNo3", list2.get(1).getModuleNo());
        Assert.assertEquals(2, list2.size());
    }

    @Test
    public void getModuleForTransferScan() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);
        dto.setCfgHeaderId("id");
        dto.setLineCode("lineCode");
        dto.setWorkOrder("workOrder");
        dto.setSourceTask("sourceTask");
        dto.setMountType("1");
        dto.setCraftSection("SMT-B");
        dto.setRemark("0");
        dto.setmOtherSideCfgHeaderId("");
        dto.setmIsBothSides(false);
        dto.setRelatedWorkOrder("");

        List<String> moduleNoList = new ArrayList<>();
        moduleNoList.add("(X4S-2)1");
        moduleNoList.add("(X4S-2)2");
        moduleNoList.add("(X4S-2)3");
        PowerMockito.when(pdaTransferScanRepository.getModuleNoList(any())).thenReturn(moduleNoList);

        List<String> emptyMaterialModuleList = new ArrayList<>();
        emptyMaterialModuleList.add("(X4S-2)2");
        PowerMockito.when(pdaTransferScanRepository.getEmptyMaterialModuleList(any())).thenReturn(emptyMaterialModuleList);
        List<PDATransferScanCommonDto> baseList1 = Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        Assert.assertEquals("(X4S-2)1", baseList1.get(0).getModuleNo());
        Assert.assertEquals(true, baseList1.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", baseList1.get(1).getModuleNo());
        Assert.assertEquals(false, baseList1.get(1).getCompletedFlag());

        //AB同时转机
        dto.setmIsBothSides(true);
        dto.setmOtherSideCfgHeaderId("OtherSideCfgHeaderId");
        dto.setmOtherSideWorkOrderNo("OtherSideWorkOrderNo");
        Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        PowerMockito.when(pdaTransferScanRepository.getEmptyMaterialModuleList(any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_DETAIL_NOT_FOUND, e.getMessage());
        }
        List<String> emptyMaterialModuleList2 = new ArrayList<>();
        emptyMaterialModuleList2.add("(X4S-2)2");
        emptyMaterialModuleList2.add("(X4S-2)3");
        PowerMockito.when(pdaTransferScanRepository.getEmptyMaterialModuleList(any())).thenReturn(emptyMaterialModuleList2);
        List<PDATransferScanCommonDto> bothList = Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        Assert.assertEquals("(X4S-2)1", bothList.get(0).getModuleNo());
        Assert.assertEquals(true, bothList.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", bothList.get(1).getModuleNo());
        Assert.assertEquals(false, bothList.get(1).getCompletedFlag());
        Assert.assertEquals("(X4S-2)3", bothList.get(2).getModuleNo());
        Assert.assertEquals(false, bothList.get(2).getCompletedFlag());

        //双轨线
        dto.setmOtherSideCfgHeaderId("");
        dto.setmOtherSideWorkOrderNo("");
        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("RelatedCfgHeaderId");
        dto.setRelatedWorkOrder("RelatedWorkOrder");
        Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        PowerMockito.when(pdaTransferScanRepository.getModuleNoList(any())).thenReturn(moduleNoList);
        PowerMockito.when(pdaTransferScanRepository.getEmptyMaterialModuleList(any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_DETAIL_NOT_FOUND, e.getMessage());
        }
        List<String> emptyMaterialModuleList3 = new ArrayList<>();
        emptyMaterialModuleList3.add("(X4S-2)2");
        emptyMaterialModuleList3.add("(X4S-2)3");
        PowerMockito.when(pdaTransferScanRepository.getEmptyMaterialModuleList(any())).thenReturn(emptyMaterialModuleList3);
        List<PDATransferScanCommonDto> relateList = Whitebox.invokeMethod(service, "getModuleForTransferScan", moduleNoList, dto);
        Assert.assertEquals("(X4S-2)1", relateList.get(0).getModuleNo());
        Assert.assertEquals(true, relateList.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2", relateList.get(1).getModuleNo());
        Assert.assertEquals(false, relateList.get(1).getCompletedFlag());
        Assert.assertEquals("(X4S-2)3", relateList.get(2).getModuleNo());
        Assert.assertEquals(false, relateList.get(2).getCompletedFlag());

    }

    @Test
    public void checkWorkOrderPrepare() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        dto.setRemark("0");
        dto.setCraftSection("DIP");
        Whitebox.invokeMethod(service, "checkWorkOrderPrepare", dto);

        dto.setRemark("1");
        Whitebox.invokeMethod(service, "checkWorkOrderPrepare", dto);

        dto.setCraftSection("SMT-A");
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(anyMap())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "checkWorkOrderPrepare", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FOUND_PRODPLANID, e.getExMsgId());
        }

        dto.setCraftSection("SMT-B");
        List<PsWorkOrderDTO> workOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
        ps1.setRemark("");
        workOrderDTOS.add(ps1);

        ServiceData serviceData1 = new ServiceData();
        serviceData1.setCode(serviceData1.getCode());
        serviceData1.setBo(workOrderDTOS);
        JsonNode treeNode1 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData1));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(anyMap())).thenReturn(treeNode1);
        Whitebox.invokeMethod(service, "checkWorkOrderPrepare", dto);

        PsWorkOrderDTO ps2 = new PsWorkOrderDTO();
        ps2.setRemark("1");

        PsWorkOrderDTO ps3 = new PsWorkOrderDTO();
        ps3.setRemark("0");
        ps3.setWorkOrderNo("待排产");

        PsWorkOrderDTO ps4 = new PsWorkOrderDTO();
        ps4.setRemark("0");
        ps4.setWorkOrderNo("8899855-SMT-B5501");
        ps4.setWorkOrderStatus(Constant.IS_SUBMITTED);

        PsWorkOrderDTO ps5 = new PsWorkOrderDTO();
        ps5.setRemark("0");
        ps4.setWorkOrderNo("8899855-SMT-A5502");
        ps5.setWorkOrderStatus(Constant.IS_DRAW_UP);

        workOrderDTOS.add(ps2);
        workOrderDTOS.add(ps3);
        workOrderDTOS.add(ps4);
        workOrderDTOS.add(ps5);
        ServiceData serviceData2 = new ServiceData();
        serviceData2.setCode(serviceData1.getCode());
        serviceData2.setBo(workOrderDTOS);
        JsonNode treeNode2 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(serviceData2));
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(anyMap())).thenReturn(treeNode2);
        try {
            Whitebox.invokeMethod(service, "checkWorkOrderPrepare", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_ALLOWED_TRANSFER, e.getExMsgId());
        }
    }

    @Test
    public void checkWorkOrderTransfer() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        dto.setWorkOrder("workOrder1");
        ServiceData serviceData = new ServiceData();
        serviceData.setBo("");
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, ""));
        PowerMockito.when(smtMachineMTLHistoryHService.checkWorkOrderCanDeliver(any()))
                .thenReturn(serviceData.getCode());
        try {
            service.checkWorkOrderTransfer(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, serviceData.getCode().getCode());
        }

        dto.setRelatedWorkOrder("relatedWorkOrder");
        serviceData.setBo("");
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, ""));
        SmtMachineMTLHistoryHServiceImpl spy = PowerMockito.spy(new SmtMachineMTLHistoryHServiceImpl());
        PowerMockito.doReturn(serviceData.getCode()).when(spy).checkWorkOrderCanDeliver(any());
        try {
            service.checkWorkOrderTransfer(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, serviceData.getCode().getCode());
        }

        dto.setRelatedWorkOrder("");
        serviceData.setBo("");
        dto.setmOtherSideWorkOrderNo("otherSideWorkOrderNo");
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, ""));
        PowerMockito.doReturn(serviceData.getCode()).when(spy).checkWorkOrderCanDeliver(any());
        try {
            service.checkWorkOrderTransfer(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_CODE, serviceData.getCode().getCode());
        }


    }

    /**
     * 一键切换-扫描站位
     *
     **/
    @Test
    public void scanLocationSnOneKeySwitch() {
        String locationSn =null;
        String factoryId = "52";
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            service.scanLocationSnOneKeySwitch(locationSn,factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_SN_ERROR, e.getMessage());
        }
        locationSn="4070204";
        PowerMockito.when(smtMachineMaterialMoutingService.checkSmtLocationLineInfo(any())).thenReturn(null);
        try {
            service.scanLocationSnOneKeySwitch(locationSn,factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        locationSn="407020413901";
        OneKeySwitchMoutingDTO oneKeySwitchMoutingDTO = new OneKeySwitchMoutingDTO();
        oneKeySwitchMoutingDTO.setLineCode("SMT-96");
        oneKeySwitchMoutingDTO.setLineName("SMT-96");
        oneKeySwitchMoutingDTO.setModuleNo("(X2-S4)2");
        PowerMockito.when(smtMachineMaterialMoutingService.checkSmtLocationLineInfo(any())).thenReturn(oneKeySwitchMoutingDTO);

        List<WorkorderOnline> workOrderByLineCode =new ArrayList<>();
        WorkorderOnline workorderOnline1 = new WorkorderOnline();
        WorkorderOnline workorderOnline2 = new WorkorderOnline();
        WorkorderOnline workorderOnline3 = new WorkorderOnline();
        workorderOnline1.setWorkOrder("workOrder1");
        workorderOnline2.setWorkOrder("workOrder2");
        workorderOnline3.setWorkOrder("workOrder3");
        workOrderByLineCode.add(workorderOnline1);
        workOrderByLineCode.add(workorderOnline2);
        workOrderByLineCode.add(workorderOnline3);
        PowerMockito.when(workorderOnlineRepository.getWorkOrderByLineCode(any())).thenReturn(workOrderByLineCode);
        try {
            service.scanLocationSnOneKeySwitch(locationSn,factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NOT_ALLOW_MORE_THAN_TWO_WORK_ORDER, e.getMessage());
        }

        List<WorkorderOnline> workOrderByLineCode2 =new ArrayList<>();
        WorkorderOnline workorderOnline4 = new WorkorderOnline();
        WorkorderOnline workorderOnline5 = new WorkorderOnline();
        workorderOnline4.setWorkOrder("workOrder4");
        workorderOnline5.setWorkOrder("workOrder5");
        workOrderByLineCode2.add(workorderOnline4);
        workOrderByLineCode2.add(workorderOnline5);
        PowerMockito.when(workorderOnlineRepository.getWorkOrderByLineCode(any())).thenReturn(workOrderByLineCode2);

        List<PsEntityPlanBasic> psEntityPlanBasicList = new ArrayList<>();
        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setWorkOrderNo("workOrder1");
        psEntityPlanBasicList.add(psEntityPlanBasic);
        PowerMockito.when(PlanscheduleRemoteService.getPsEntityPlanBasicList(Mockito.any())).thenReturn(psEntityPlanBasicList);
        service.scanLocationSnOneKeySwitch(locationSn,factoryId);

        List<String> workOrderlist = new ArrayList<>();
        List<PsEntityPlanBasic> basicWorkOrderlist = new ArrayList<>();
        //筛选开工的指令
        if (!CollectionUtils.isEmpty(workOrderByLineCode)) {
            workOrderlist = workOrderByLineCode2.stream()
                    .map(item -> item.getWorkOrder())
                    .collect(Collectors.toList());
        }
        //筛选已提交、挂起指令的指令号、批次
        if (!CollectionUtils.isEmpty(psEntityPlanBasicList)) {
            basicWorkOrderlist = psEntityPlanBasicList.stream()
                    .map(item -> {
                        PsEntityPlanBasic psEntityPlanBasic2 = new PsEntityPlanBasic();
                        psEntityPlanBasic2.setWorkOrderNo(item.getWorkOrderNo());
                        psEntityPlanBasic2.setSourceTask(item.getSourceTask());
                        return psEntityPlanBasic2;
                    })
                    .collect(Collectors.toList());
        }
        OneKeySwitchScanLocationDto dto = new OneKeySwitchScanLocationDto(workOrderlist,basicWorkOrderlist,
                oneKeySwitchMoutingDTO.getLineCode(), oneKeySwitchMoutingDTO.getLineName(), oneKeySwitchMoutingDTO.getModuleNo());
        service.scanLocationSnOneKeySwitch(locationSn,factoryId);
        Assert.assertEquals("SMT-96", dto.getLineCode());
        Assert.assertEquals("SMT-96", dto.getLineName());
        Assert.assertEquals("workOrder4", dto.getWorkOrderByLineCode().get(0));
        Assert.assertEquals("workOrder5", dto.getWorkOrderByLineCode().get(1));
        Assert.assertEquals("SMT-96", dto.getLineName());
        Assert.assertEquals("(X2-S4)2",dto.getModuleNo());
    }

    @Test
    public void scanOldReelIdForReelIdSplit() throws Exception {
        String pkCode = "202404130001";
        String factoryId = "55";
        List<ContainerContentInfoDTO> containerContentInfoList = new ArrayList<>();
        ContainerContentInfoDTO contentInfoDTO = new ContainerContentInfoDTO();
        contentInfoDTO.setLpn("111");
        containerContentInfoList.add(contentInfoDTO);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(EqpmgmtsRemoteService.class);
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoList(any(), any())).thenReturn(containerContentInfoList);
        try {
            service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REEL_ID_EXIST_IN_CONTAINER_NOT_ALLOW_SPLIT, e.getMessage());
        }
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfoList(any(), any())).thenReturn(null);
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(null);
        try {
            service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REELID_NOT_EXISTS, e.getMessage());
        }

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("202404130002");
        pkCodeInfo.setItemQty(new BigDecimal("500"));
        pkCodeInfoList.add(pkCodeInfo);
        int itemQty = pkCodeInfoList.get(0).getItemQty().intValue();
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(null);
        service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        Assert.assertEquals(500, Math.max(itemQty, 0));

        SysLookupTypesDTO sysLookupValuesDTO = new SysLookupTypesDTO();
        sysLookupValuesDTO.setLookupMeaning(Constant.FLAG_N);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupValuesDTO);
        Assert.assertEquals(500, Math.max(itemQty, 0));

        sysLookupValuesDTO.setLookupMeaning(Constant.FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupValuesDTO);
        try {
            service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }
        getSmtMachineMaterialMouting();
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setLocationNo("1-39-1");
        mouting.setLineCode("SMT-CS02");
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setFactoryId(new BigDecimal(factoryId));
        smtMachineMaterialMouting.setObjectId(pkCode);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(smtMachineMaterialMouting)).thenReturn(null);
        try {
            service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }
        mouting.setItemCode("XXXXXX");
        smtMachineMaterialMoutings.add(mouting);
        EmEqpPdcountDTO eqpPdcountDto = new EmEqpPdcountDTO();
        eqpPdcountDto.setLineCode("SMT-001");
        eqpPdcountDto.setMaterialCode("XXXXXX");
        eqpPdcountDto.setFullStationPosition("1-39-1");
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(smtMachineMaterialMouting)).thenReturn(smtMachineMaterialMoutings);
        PowerMockito.when(EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto)).thenReturn(null);
        try {
            service.scanOldReelIdForReelIdSplit(pkCode, factoryId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getMessage());
        }
        List<EmEqpPdcountDTO> emEqpPdcountList = new ArrayList<>();
        EmEqpPdcountDTO emEqpPdcount1 = new EmEqpPdcountDTO();
        emEqpPdcount1.setThrowsNumber(new BigDecimal(50));
        EmEqpPdcountDTO emEqpPdcount2 = new EmEqpPdcountDTO();
        emEqpPdcount2.setThrowsNumber(new BigDecimal(50));
        emEqpPdcountList.add(emEqpPdcount1);
        emEqpPdcountList.add(emEqpPdcount2);
        PowerMockito.when(EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto)).thenReturn(emEqpPdcountList);
        BigDecimal sumThrowsNumber = BigDecimal.valueOf(0);
        if (!CollectionUtils.isEmpty(emEqpPdcountList)) {
            sumThrowsNumber = emEqpPdcountList.stream()
                    .map(EmEqpPdcountDTO::getThrowsNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        itemQty = itemQty - sumThrowsNumber.intValue();
        Assert.assertEquals(400, Math.max(itemQty, 0));
    }

    @Test
    public void scanNewQtyForReelIdSplit() {
        PDAReelIdSplitDto dto = new PDAReelIdSplitDto();
        dto.setNewQty("99");
        dto.setOldReelId("202400010001");
        dto.setNewReelId("202400010002");
        dto.setFactoryId("55");
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        pkCodeInfoDTO.setPkCode(dto.getOldReelId());
        PowerMockito.mockStatic(EqpmgmtsRemoteService.class);
        PowerMockito.when(pkCodeInfoService.getList(pkCodeInfoDTO)).thenReturn(null);
        try {
            service.scanNewQtyForReelIdSplit(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REELID_NOT_EXISTS, e.getMessage());
        }

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode(dto.getOldReelId());
        pkCodeInfo.setItemQty(new BigDecimal("599"));
        pkCodeInfoList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        PowerMockito.doNothing().when(centerfactoryRemoteService).splitReelId(Mockito.any());
        PowerMockito.doNothing().when(pkCodeHistoryService).insertPkCodeHistory(Mockito.any());
        service.scanNewQtyForReelIdSplit(dto);
    }

    @Test
    public void getSmtMachineMaterialMouting() throws Exception {
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();

        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setLocationNo("1-39-1");
        mouting.setLineCode("SMT-CS02");
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setFactoryId(new BigDecimal("55"));
        smtMachineMaterialMouting.setObjectId("202404170001");
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(smtMachineMaterialMouting)).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "getSmtMachineMaterialMouting", "202404170001", "55");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }
        smtMachineMaterialMoutings.add(mouting);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(smtMachineMaterialMoutings);
        try {
            Whitebox.invokeMethod(service, "getSmtMachineMaterialMouting", "202404170001", "55");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SMT_DATA_LINE_LOCATION_ITEM_CODE_IS_NULL, e.getMessage());
        }
        smtMachineMaterialMoutings.remove(0);
        mouting.setItemCode("XXXXXX");
        smtMachineMaterialMoutings.add(mouting);
        Whitebox.invokeMethod(service, "getSmtMachineMaterialMouting", "202404170001", "55");
        Assert.assertEquals(mouting.getLocationNo(), "1-39-1");
        Assert.assertEquals(mouting.getLineCode(), "SMT-CS02");
        Assert.assertEquals(mouting.getItemCode(), "XXXXXX");
    }

    @Test
    public void rcvScanOldPkCode() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class, CommonUtils.class, PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        try {
            service.rcvScanOldPkCode(null, null, null, "55", "10317937");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL, e.getMessage());
        }

        try {
            service.rcvScanOldPkCode("oldPkCode", null, null, "55", "10317937");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OLD_AND_NEW_PK_CODE_CAN_NOT_BE_NULL, e.getMessage());
        }

        try {
            service.rcvScanOldPkCode("oldPkCode", "newPkCode", null, "55", "10317937");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_ID_NULL, e.getMessage());
        }

        //校验新旧料盘物料代码是否一致
        PDAReceiveCheckItemDTO pdaReceiveCheckItemDTO = new PDAReceiveCheckItemDTO();
        pdaReceiveCheckItemDTO.setItemCode("BBBB");
        PowerMockito.when(smtMachineMaterialMoutingService.pdaReceiveCheckItem(any(), any())).thenReturn(pdaReceiveCheckItemDTO);
        service.rcvScanOldPkCode("oldPkCode", "newPkCode", "productTask", "55", "10317937");
        Assert.assertEquals(pdaReceiveCheckItemDTO.getItemCode(), "BBBB");

        pdaReceiveCheckItemDTO.setAttribute1("AAA");
        CFLine cfLine = new CFLine();
        cfLine.setLineName("SMT-ZC01");
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLineCodeInfo(any())).thenReturn(cfLine);
        PowerMockito.when(smtMachineMaterialMoutingService.pdaReceiveCheckItem(any(), any())).thenReturn(pdaReceiveCheckItemDTO);
        service.rcvScanOldPkCode("oldPkCode", "newPkCode", "productTask", "55", "10317937");
        Assert.assertEquals(pdaReceiveCheckItemDTO.getAttribute1(), "AAA");
        Assert.assertEquals(cfLine.getLineName(), "SMT-ZC01");

        PowerMockito.when(smtMachineMaterialMoutingService.pdaReceiveCheckItem(any(), any())).thenReturn(null);
        PowerMockito.when(smtMachineMaterialMoutingService.rcvScanOldPkCode(any(), any())).thenReturn(null);
        //扫描旧料盘的校验，并获取机台在用数据
        try {
            service.rcvScanOldPkCode("oldPkCode", "newPkCode", "productTask", "55", "10317937");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PKCODE_NOT_USED_ERROR, e.getMessage());
        }
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setInPkCode("XXXX");
        smtMachineMaterialMoutings.add(mouting);
        mouting.setSourceTask("oldSourceTask");
        PowerMockito.when(smtMachineMaterialMoutingService.rcvScanOldPkCode(any(), any())).thenReturn(smtMachineMaterialMoutings);

        ImesPDACommonServiceImpl spy1 = PowerMockito.spy(service);
        PowerMockito.doReturn(false).when(spy1).pkCodeCastleOrAppropriation(any(), any(), any(), any(), any());
        try {
            spy1.rcvScanOldPkCode("oldPkCode", "newPkCode", "productTask", "55", "10317937");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OLD_AND_NEW_PKCODE_BATCH_INCONSISTENT, e.getMessage());
        }
        ImesPDACommonServiceImpl spy2 = PowerMockito.spy(service);
        PowerMockito.doReturn(true).when(spy2).pkCodeCastleOrAppropriation(any(), any(), any(), any(), any());
        Assert.assertTrue(true);

        mouting.setSourceTask("newSourceTask");
        //根据旧料盘的机台在用信息获取上料详表料架信息
        String materialRackStr = "materialRackStr";
        PowerMockito.when(bSmtBomDetailRepository.getBomDetailByMouting(any())).thenReturn(materialRackStr);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLineCodeInfo(any())).thenReturn(cfLine);
        service.rcvScanOldPkCode("oldPkCode", "newPkCode", "newSourceTask", "55", "10317937");
        Assert.assertEquals(materialRackStr, "materialRackStr");
        Assert.assertEquals(cfLine.getLineName(), "SMT-ZC01");
    }

    @Test
    public void isMaintainQCReCheck() throws Exception {
        List<SysLookupTypesDTO> itemErrorCtrls = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE)).thenReturn(itemErrorCtrls);
        Assert.assertFalse(service.isMaintainQCReCheck());

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE)).thenReturn(itemErrorCtrls);
        Assert.assertFalse(service.isMaintainQCReCheck());

        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.LOOKUP_VALUE_QC_RE_CHECK));
        itemErrorCtrls.add(sysLookupTypesDTO);
        Assert.assertTrue(service.isMaintainQCReCheck());

    }

    @Test
    public void isMaintainQC() throws Exception {
        List<SysLookupTypesDTO> itemErrorCtrls = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE)).thenReturn(itemErrorCtrls);
        Assert.assertFalse(service.isMaintainQC());

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_ITEM_ERROR_CTRL_TYPE)).thenReturn(itemErrorCtrls);
        Assert.assertFalse(service.isMaintainQC());

        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.LOOKUP_VALUE_QC_TRANSFORM));
        itemErrorCtrls.add(sysLookupTypesDTO);
        Assert.assertTrue(service.isMaintainQC());

    }

    @Test
    public void checkPkCodeQCSpotCheck() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PDAQCSpotCheckDTO dto = new PDAQCSpotCheckDTO();
        List<SmtLocationInfoDTO> listSmtLocation = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any(), any())).thenReturn(listSmtLocation);
        try {
            service.checkPkCodeQCSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        SmtLocationInfoDTO smtLocationInfoDTO1 = new SmtLocationInfoDTO();
        smtLocationInfoDTO1.setAttribute1("Y");
        listSmtLocation.add(smtLocationInfoDTO1);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any(), any())).thenReturn(listSmtLocation);
        try {
            service.checkPkCodeQCSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        SmtLocationInfoDTO smtLocationInfoDTO2 = new SmtLocationInfoDTO();
        smtLocationInfoDTO2.setToSupplierMachine("XXX");
        listSmtLocation.remove(0);
        listSmtLocation.add(smtLocationInfoDTO2);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any(), any())).thenReturn(listSmtLocation);
        try {
            service.checkPkCodeQCSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        SmtLocationInfoDTO smtLocationInfoDTO3 = new SmtLocationInfoDTO();
        smtLocationInfoDTO3.setLocationSn("XXX");
        listSmtLocation.remove(0);
        listSmtLocation.add(smtLocationInfoDTO3);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any(), any())).thenReturn(listSmtLocation);
        try {
            service.checkPkCodeQCSpotCheck(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        SmtLocationInfoDTO smtLocationInfoDTO4 = new SmtLocationInfoDTO();
        smtLocationInfoDTO4.setToSupplierMachine("XXX");
        smtLocationInfoDTO4.setLocationSn("XXX");
        listSmtLocation.remove(0);
        listSmtLocation.add(smtLocationInfoDTO4);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(any(), any(), any())).thenReturn(listSmtLocation);
        dto.setCfgHeaderId("cfgId");
        checkCfgIdChanged();
        PDAQCSpotCheckPkCodeDTO pdaqcSpotCheckPkCodeDTO = service.checkPkCodeQCSpotCheck(dto);
        Assert.assertTrue(pdaqcSpotCheckPkCodeDTO.getCfgIdChangedFlag());

        dto.setWorkOrderNo2("order2");
        dto.setCfgHeaderId2("cfgId2");
        service.checkPkCodeQCSpotCheck(dto);
        Assert.assertTrue(pdaqcSpotCheckPkCodeDTO.getCfgIdChangedFlag());

    }

    @Test
    public void checkCfgIdChanged() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CommonUtils.class);
        Map<String, Object> params = new HashMap<>();
        params.put("workOrderNo", "XXXX");
        List<PsWorkOrderDTO> basicWorkOrder = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(anyMap())).thenReturn(basicWorkOrder);
        PDAQCSpotCheckPkCodeDTO checkCfgIdChangedDto = Whitebox.invokeMethod(service, "checkCfgIdChanged", params, "888");
        Assert.assertTrue(checkCfgIdChangedDto.getCfgIdChangedFlag());
        Assert.assertEquals(checkCfgIdChangedDto.getMsg(), CommonUtils.getLmbMessage(MessageId.WORKORDER_NOT_FIND));

        PsWorkOrderDTO psWorkOrderDTO1 = new PsWorkOrderDTO();
        basicWorkOrder.add(psWorkOrderDTO1);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(anyMap())).thenReturn(basicWorkOrder);
        checkCfgIdChangedDto = Whitebox.invokeMethod(service, "checkCfgIdChanged", params, "888");
        Assert.assertTrue(checkCfgIdChangedDto.getCfgIdChangedFlag());
        Assert.assertEquals(checkCfgIdChangedDto.getMsg(), CommonUtils.getLmbMessage(MessageId.WORK_ORDER_NO_CFG_ID));

        psWorkOrderDTO1.setCfgHeaderId("cfgHeader");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(anyMap())).thenReturn(basicWorkOrder);
        checkCfgIdChangedDto = Whitebox.invokeMethod(service, "checkCfgIdChanged", params, "888");
        Assert.assertTrue(checkCfgIdChangedDto.getCfgIdChangedFlag());
        Assert.assertEquals(checkCfgIdChangedDto.getMsg(), CommonUtils.getLmbMessage(MessageId.WORK_ORDER_CFG_ID_CHANGED));

        psWorkOrderDTO1.setCfgHeaderId("cfgHeader");
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(anyMap())).thenReturn(basicWorkOrder);
        checkCfgIdChangedDto = Whitebox.invokeMethod(service, "checkCfgIdChanged", params, "cfgHeader");
        Assert.assertFalse(checkCfgIdChangedDto.getCfgIdChangedFlag());
        Assert.assertEquals(checkCfgIdChangedDto.getMsg(), "");
    }

    @Test
    public void scanLocationSnRcvScanTwo() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLocationSn("20240617001");
        smtLocationInfoDTO.setMachineNo("X2S");
        smtLocationInfoDTO.setModuleNo("X2S-1");
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationInfoByLocationSn(any())).thenReturn(smtLocationInfoDTO);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setModuleNo("X2S-2");
        mouting.setMachineNo("X2S");
        mouting.setLocationNo("20240617001");
        smtMachineMaterialMoutings.add(mouting);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(smtMachineMaterialMoutings);
        SmtLocationInfo locationInfo = new SmtLocationInfo();
        locationInfo.setLocationNo("1-39-1");
        locationInfo.setMachineNo("X2S");
        locationInfo.setModuleNo("X2S-2");

        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(locationInfo);
        Assert.assertNotNull(service.scanLocationSnRcvScan("20240617001", ""));
    }

    @Test
    public void scanLocationSnRcvScan() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        String locationSn = "";
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationInfoByLocationSn(any())).thenReturn(null);
        try {
            service.scanLocationSnRcvScan(locationSn, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }
        locationSn = "20240617001";
        smtLocationInfoDTO.setLocationSn(locationSn);
        smtLocationInfoDTO.setMachineNo("X2S");
        smtLocationInfoDTO.setModuleNo("X2S-1");
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationInfoByLocationSn(any())).thenReturn(smtLocationInfoDTO);

        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        smtMachineMaterialMouting.setFactoryId(new BigDecimal("55"));
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(null);
        try {
            service.scanLocationSnRcvScan(locationSn, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }

        smtMachineMaterialMouting.setObjectId("20240617001");
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setModuleNo("X2S-1");
        mouting.setMachineNo("X2S");
        smtMachineMaterialMoutings.add(mouting);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(smtMachineMaterialMoutings);
        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(any(), any(), any(), any())).thenReturn(null);
        try {
            service.scanLocationSnRcvScan(locationSn, "20240617001");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NOT_EXIST, e.getMessage());
        }

        SmtLocationInfo locationInfo = new SmtLocationInfo();
        locationInfo.setLocationNo("1-39-1");
        locationInfo.setMachineNo("X2S");
        locationInfo.setModuleNo("X2S-2");

        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(any(), any(), any(), any())).thenReturn(locationInfo);
        service.scanLocationSnRcvScan(locationSn, "20240617001");
        Assert.assertEquals(locationInfo.getModuleNo(), "X2S-2");

        locationInfo.setMachineNo("X2C");
        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(any(), any(), any(), any())).thenReturn(locationInfo);
        service.scanLocationSnRcvScan(locationSn, "20240617001");
        Assert.assertEquals(locationInfo.getMachineNo(), "X2C");

        locationInfo.setLocationNo("1-39-2");
        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(any(), any(), any(), any())).thenReturn(locationInfo);
        service.scanLocationSnRcvScan(locationSn, "20240617001");
        Assert.assertEquals(locationInfo.getLocationNo(), "1-39-2");

        locationInfo.setLocationNo("1-39-1");
        locationInfo.setMachineNo("X2S");
        locationInfo.setModuleNo("X2S-1");
        PowerMockito.when(BasicsettingRemoteService.asmInfoQuery(any(), any(), any(), any())).thenReturn(locationInfo);
        service.scanLocationSnRcvScan(locationSn, "20240617001");
        Assert.assertEquals(locationInfo.getLocationNo(), "1-39-1");
        Assert.assertEquals(locationInfo.getMachineNo(), "X2S");
        Assert.assertEquals(locationInfo.getModuleNo(), "X2S-1");

    }

    @Test
    public void execPolarCheckEvent1() throws Exception {
        PDAReceiveItemsScanDTO entity = new PDAReceiveItemsScanDTO();
        entity.setOldQty(new BigDecimal(1));
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setProductTask("7777888");
        pkCodeInfo.setSysLotCode("1");
        pkCodeInfo.setItemCode("item1");
        pkCodeInfo.setItemQty(new BigDecimal("5"));
        pkCodeInfo.setSupplerCode("111");
        pkCodeInfo.setSysLotCode("111");

        PkCodeInfo oldPkCodeInfo = new PkCodeInfo();
        oldPkCodeInfo.setProductTask("7777888");
        oldPkCodeInfo.setItemCode("item1");
        oldPkCodeInfo.setItemQty(new BigDecimal("5"));
        oldPkCodeInfo.setSupplerCode("111");
        oldPkCodeInfo.setSysLotCode("111");
        PkCodeInfo pkCodeResult = new PkCodeInfo();
        pkCodeResult.setItemAngle("0");
        PowerMockito.when(pkCodeInfoService.getItemDirectionByPkCode(pkCodeInfo))
                .thenReturn(pkCodeResult);
        PkCodeInfo pkCodeResult2 = new PkCodeInfo();
        pkCodeResult2.setItemAngle("1");
        PowerMockito.when(pkCodeInfoService.getItemDirectionByPkCode(oldPkCodeInfo))
                .thenReturn(pkCodeResult2);
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfo1 = new ArrayList<>();

        AgeingInfoFencePointToPointQueryItemInfoDTO itemInfoNew = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        itemInfoNew.setAbcType("123");
        itemInfo1.add(itemInfoNew);

        entity.setItemCodeFlag(true);
        try {
            Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("Y");
        Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);

        entity.setItemCodeFlag(false);

        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("N");

        oldPkCodeInfo.setSupplerCode("1111");
        Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);

        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("Y");
        Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);

        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_TYPE_8889002)).thenReturn("N");
        try {
            Whitebox.invokeMethod(service, "execPolarCheckEvent", entity, pkCodeInfo, oldPkCodeInfo);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void getModuleBSmtBomDetailInfoForQCTransfer() throws Exception {
        PDATransferScanCommonDto dto = new PDATransferScanCommonDto();
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("该模组下没有上料信息");
        try {
            service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CFGHEADERID_IS_EMPTY, e.getMessage());
        }

        dto.setCfgHeaderId("id");
        try {
            service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_CODE_EMPTY, e.getMessage());
        }

        dto.setLineCode("lineCode");
        try {
            service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_EMPTY, e.getMessage());
        }

        dto.setWorkOrder("workOrder");
        try {
            service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MOUNT_TYPE_IS_NULL, e.getMessage());
        }

        dto.setMountType("1");
        try {
            service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }
        dto.setPickStatusString("1");
        dto.setModuleNo("moduleNo");
        dto.setmOtherSideCfgHeaderId("");
        List<PDATransferScanModuleDto> moduleList = new ArrayList<>();
        List<PDATransferScanModuleDto> otherModuleList = new ArrayList<>();

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList,otherModuleList);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("该模组下没有上料信息，请确认");
        ServiceData<List<PDATransferScanModuleDto>> ret1 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(0, ret1.getBo().size());
        Assert.assertEquals("该模组下没有上料信息，请确认", ret1.getCode().getMsg());

        moduleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
            setMaterialTray("materialTray1");
        }});
        dto.setmIsBothSides(true);
        dto.setmOtherSideWorkOrderNo("mWorkOrder");
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, new ArrayList<>());
        ServiceData<List<PDATransferScanModuleDto>> ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(1, ret2.getBo().size());

        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
        }});
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo2");
            setLocationNo("locationNo2");
            setMachineNo("machineNo2");
            setMaterialTray("materialTray2");
        }});

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(new ArrayList<>(), otherModuleList);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret2.getBo().size());

        dto.setmIsBothSides(true);
        dto.setMountType("1");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret2.getBo().size());

        dto.setMountType("15");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList,null);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(0L,0L);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret2.getBo().size());

        dto.setMountType("3");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList,otherModuleList);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(0L,1L);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(3, ret2.getBo().size());
        otherModuleList.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo3");
            setLocationNo("locationNo3");
            setMachineNo("machineNo3");
            setMaterialTray("materialTray3");
        }});
        dto.setMountType("15");
        dto.setLineCode("lineCode");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList,otherModuleList);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(1L,0L);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(5, ret2.getBo().size());


        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("relatedHeaderId");
        dto.setRelatedWorkOrder("relatedWorkOrder");
        dto.setmRelatedLine("relatedLine");
        dto.setMountType("3");

        List<PDATransferScanModuleDto> otherModuleList2 = new ArrayList<>();
        otherModuleList2.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo1");
            setLocationNo("locationNo1");
            setMachineNo("machineNo1");
        }});
        otherModuleList2.add(new PDATransferScanModuleDto() {{
            setModuleNo("moduleNo3");
            setLocationNo("locationNo3");
            setMachineNo("machineNo3");
            setMaterialTray("materialTray3");
        }});

        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(new ArrayList<>(), otherModuleList);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(3, ret2.getBo().size());

        dto.setMountType("1");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList);
        ret2 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(3, ret2.getBo().size());

        dto.setMountType("15");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList,otherModuleList2,otherModuleList2);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(1L,0L);
        ServiceData<List<PDATransferScanModuleDto>> ret3 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret3.getBo().size());

        dto.setMountType("15");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList,otherModuleList2,null);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(0L,1L);
        ret3 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret3.getBo().size());

        dto.setMountType("3");
        PowerMockito.when(pdaTransferScanRepository.getModuleBSmtBomDetailList(any())).thenReturn(moduleList, otherModuleList2,otherModuleList2);
        PowerMockito.when(smtMachineMTLHistoryHService.getCount(Mockito.any())).thenReturn(1L,1L);
        ServiceData<List<PDATransferScanModuleDto>> ret4 = service.getModuleBSmtBomDetailInfoForQCTransfer(dto);
        Assert.assertEquals(2, ret4.getBo().size());

    }

    @Test
    public void updateReelIdQty() throws Exception {
        PDAReelIdQtyModifyDto dto = new PDAReelIdQtyModifyDto();
        dto.setItemQty(new BigDecimal(1));
        dto.setNewQty(new BigDecimal(2));
        dto.setRawQty(new BigDecimal(0));
        dto.setReelId("202503200001");
        dto.setSourceTask("8887771");
        dto.setLoginUser("10317937");
        dto.setFactoryId(new BigDecimal(56));
        service.updateReelIdQty(dto);

        dto.setNewQty(new BigDecimal(0));
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.updatePkCodeInfoByIdSelective(Mockito.any())).thenReturn(1);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(smtMachineMaterialMoutings);
        try{
            service.updateReelIdQty(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }
        SmtMachineMaterialMouting mouting = new SmtMachineMaterialMouting();
        mouting.setNextReelRowid("");
        smtMachineMaterialMoutings.add(mouting);
        service.updateReelIdQty(dto);

        smtMachineMaterialMoutings.remove(0);
        mouting.setMachineMaterialMoutingId("1111111111");
        mouting.setNextReelRowid("nextReelRowid");
        SmtMachineMaterialMouting mouting2 = new SmtMachineMaterialMouting();
        mouting2.setMachineMaterialMoutingId("");
        mouting2.setNextReelRowid("nextReelRowid");
        smtMachineMaterialMoutings.add(mouting);
        smtMachineMaterialMoutings.add(mouting2);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(smtMachineMaterialMoutings);
        PowerMockito.doNothing().when(smtMachineMaterialPrepareService).deleteSmtMachineMaterialPrepareById(Mockito.any());
        service.updateReelIdQty(dto);

        dto.setItemQty(new BigDecimal(0));
        dto.setNewQty(new BigDecimal(-1));
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(null);
        try{
            service.updateReelIdQty(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }

        dto.setItemQty(new BigDecimal(0));
        dto.setNewQty(new BigDecimal(2));
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(null);
        try{
            service.updateReelIdQty(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.MOUNTING_DATA_IS_NULL, e.getMessage());
        }

        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any())).thenReturn(smtMachineMaterialMoutings);
        PowerMockito.when(psScanHistoryService.calSMTMaterialQty(Mockito.any())).thenReturn(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        try{
            service.updateReelIdQty(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.CALL_MATERIAL_QTY_API_FAILED, e.getMessage());
        }
        PowerMockito.when(psScanHistoryService.calSMTMaterialQty(Mockito.any())).thenReturn(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        service.updateReelIdQty(dto);
    }

}