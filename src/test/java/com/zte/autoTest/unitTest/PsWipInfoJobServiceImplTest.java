package com.zte.autoTest.unitTest;

import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.WarehouseInfoService;
import com.zte.application.impl.PsWipInfoJobServiceImpl;
import com.zte.application.impl.WarehouseInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BSProcessInfoDTO;
import com.zte.interfaces.dto.CenterRedisDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.StandardAutomaticSubmitWarehouseDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WarehouseEntryDetailDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2021/8/17
 */
@PrepareForTest({StringUtils.class, CommonUtils.class, BasicsettingRemoteService.class,
        MicroServiceRestUtil.class, HttpRemoteService.class, HttpClientUtil.class, PlanscheduleRemoteService.class})
public class PsWipInfoJobServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PsWipInfoJobServiceImpl service;
    @Mock
    private WarehouseInfoServiceImpl warehouseInfoServiceImpl;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WarehouseInfoService warehouseInfoService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    private EmailUtils emailUtils;

    @Test
    public void sendEmail() throws Exception {
        PowerMockito.mockStatic(StringUtils.class, BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId("52");
        dto.setRecipent("52");
        PsTask psTask = new PsTask();
        psTask.setProdplanId("777889");
        Whitebox.invokeMethod(service, "sendEmail", dto, psTask);
        Assert.assertEquals("52", dto.getFactoryId());
    }

    @Test
    public void standardErpMove() throws Exception {
        PowerMockito.mockStatic(StringUtils.class, BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId("52");
        service.standardErpMove(dto);
        Assert.assertEquals("52", dto.getFactoryId());
        dto.setFactoryId("56");
        service.standardErpMove(dto);
        Assert.assertEquals("56", dto.getFactoryId());
    }


    @Test
    public void standardAutomaticSubmitWarehouse() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        StandardAutomaticSubmitWarehouseDTO standardAutomaticSubmitWarehouseDTO = new StandardAutomaticSubmitWarehouseDTO();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2021-08-01 14:00:00");
        sysLookupTypesDTO.setAttribute2("2");
        sysLookupTypesDTO.setAttribute1("3");
        sysLookupTypesDTO.setAttribute4("2437");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSProcessInfoDTO> bsProcessInfoDTOList = new ArrayList<>();
        BSProcessInfoDTO bsProcessInfoDTO = new BSProcessInfoDTO();
        bsProcessInfoDTO.setProcessCode("N");
        bsProcessInfoDTOList.add(bsProcessInfoDTO);
        PowerMockito.when(
                warehouseInfoServiceImpl.getBSProcessInfoDTO(Mockito.anyString(),
                        Mockito.anyString())).thenReturn(bsProcessInfoDTOList);
        PowerMockito.when(
                BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),
                        Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        List<String> prodIdList = new ArrayList<>();
        prodIdList.add("777889");
        PowerMockito.when(
                psWipInfoRepository.getProdplanIdListStandardAutomaticSubmitWarehouse(Mockito.anyObject())).thenReturn(prodIdList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("777889");
        psTaskList.add(psTask);
        PowerMockito.when(
                warehouseInfoServiceImpl.getPsTaskByProdplanIdList(Mockito.anyList(),
                        Mockito.anyString())).thenReturn(psTaskList);
        List<List<String>> splitList = new ArrayList<>();
        splitList.add(prodIdList);
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(
                psWipInfoRepository.getListForStandardAutomaticSubmitWarehouse(Mockito.anyObject())).thenReturn(psWipInfoList);

        PowerMockito.when(
                warehouseInfoServiceImpl.getBillNo(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn("Rk001");
        try {
            standardAutomaticSubmitWarehouseDTO.setFactoryId("56");
            standardAutomaticSubmitWarehouseDTO.setLookUpCode("6703002");
            service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals("56", standardAutomaticSubmitWarehouseDTO.getFactoryId());
        }

        standardAutomaticSubmitWarehouseDTO.setFactoryId("52");
        standardAutomaticSubmitWarehouseDTO.setLookUpCode(null);
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        standardAutomaticSubmitWarehouseDTO.setFactoryId("54");
        standardAutomaticSubmitWarehouseDTO.setLookUpCode("6703002");
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        standardAutomaticSubmitWarehouseDTO.setFactoryId("52");
        standardAutomaticSubmitWarehouseDTO.setLookUpCode("6703001");
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        sysLookupTypesDTO.setLookupMeaning(null);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(sysLookupTypesDTO);
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        sysLookupTypesDTO.setLookupMeaning("2021-08-01 14:00:00");
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        List<PsTask> tempPsTaskList = new LinkedList<>();
        PsTask p1 = new PsTask();
        p1.setProdplanId("123");
        tempPsTaskList.add(p1);
        PowerMockito.when(warehouseInfoServiceImpl.getPsTaskByProdplanIdList(Mockito.anyList(), Mockito.anyString()))
                .thenReturn(tempPsTaskList);
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);

        PowerMockito.when(psWipInfoRepository.getProdplanIdListStandardAutomaticSubmitWarehouse(Mockito.any()))
                .thenReturn(null);
        service.standardAutomaticSubmitWarehouse(standardAutomaticSubmitWarehouseDTO);
    }
    @Test
    public void setStartAndEndTime() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        StandardAutomaticSubmitWarehouseDTO standardAutomaticSubmitWarehouseDTO = new StandardAutomaticSubmitWarehouseDTO();
        standardAutomaticSubmitWarehouseDTO.setLookUpCode(MpConstant.LOOKUP_CODE_6703001);
        try {
            service.setStartAndEndTime(standardAutomaticSubmitWarehouseDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        standardAutomaticSubmitWarehouseDTO.setLookUpCode(MpConstant.LOOKUP_CODE_6703002);
        try {
            service.setStartAndEndTime(standardAutomaticSubmitWarehouseDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }
    @Test
    public void dealWarehouseInfo() throws Exception {
        PowerMockito.mockStatic(StringUtils.class, BasicsettingRemoteService.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        StandardAutomaticSubmitWarehouseDTO standardAutomaticSubmitWarehouseDTO = new StandardAutomaticSubmitWarehouseDTO();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2021-08-27 14:00:00");
        sysLookupTypesDTO.setAttribute2("2");
        sysLookupTypesDTO.setAttribute1("3");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSProcessInfoDTO> bsProcessInfoDTOList = new ArrayList<>();
        BSProcessInfoDTO bsProcessInfoDTO = new BSProcessInfoDTO();
        bsProcessInfoDTO.setProcessCode("N");
        bsProcessInfoDTOList.add(bsProcessInfoDTO);
        PowerMockito.when(
                warehouseInfoServiceImpl.getBSProcessInfoDTO(Mockito.anyString(),
                        Mockito.anyString())).thenReturn(bsProcessInfoDTOList);
        PowerMockito.when(
                BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),
                        Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        List<String> prodIdList = new ArrayList<>();
        prodIdList.add("777889");
        PowerMockito.when(
                psWipInfoRepository.getProdplanIdListStandardAutomaticSubmitWarehouse(Mockito.anyObject())).thenReturn(prodIdList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("777889");
        psTaskList.add(psTask);
        PowerMockito.when(
                warehouseInfoServiceImpl.getPsTaskByProdplanIdList(Mockito.anyList(),
                        Mockito.anyString())).thenReturn(psTaskList);
        List<List<String>> splitList = new ArrayList<>();
        splitList.add(prodIdList);
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(
                psWipInfoRepository.getListForStandardAutomaticSubmitWarehouse(Mockito.anyObject())).thenReturn(psWipInfoList);

        PowerMockito.when(
                factoryConfig.getCommonEntityId()).thenReturn("01");
        PowerMockito.when(
                warehouseInfoServiceImpl.getBillNo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("Rk001");
        Map<String, PsTask> psTaskMap = new HashMap<>();
        psTaskMap.put("777889", psTask);
        PowerMockito.when(
                centerfactoryRemoteService.createBillNo(Mockito.any())).thenReturn(new CenterRedisDTO());
        standardAutomaticSubmitWarehouseDTO.setFactoryId("52");
        Assert.assertEquals("52", standardAutomaticSubmitWarehouseDTO.getFactoryId());
        standardAutomaticSubmitWarehouseDTO.setLookUpCode(MpConstant.LOOKUP_CODE_6703001);
        service.dealWarehouseInfo(standardAutomaticSubmitWarehouseDTO, "2021", prodIdList, psTaskMap);
        standardAutomaticSubmitWarehouseDTO.setLookUpCode(MpConstant.LOOKUP_CODE_6703002);
        service.dealWarehouseInfo(standardAutomaticSubmitWarehouseDTO, "2021", prodIdList, psTaskMap);
    }

    @Test
    public void standardErpMoveForXA() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId(Constant.FACTORY_ID_XA);
        service.standardErpMoveForXA(dto);
        Assert.assertEquals("56", Constant.FACTORY_ID_XA);
    }

    @Test
    public void standardErpDone() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId(Constant.FACTORY_ID_XA);
        service.standardErpDone(dto);
        Assert.assertEquals("56", Constant.FACTORY_ID_XA);
        dto.setFactoryId(Constant.FACTORY_ID_CS);
        service.standardErpDone(dto);
        Assert.assertEquals("52", Constant.FACTORY_ID_CS);
    }

    @Test
    public void standardErpDoneForCS() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId(Constant.FACTORY_ID_CS);
        service.standardErpDoneForCS(dto);
        Assert.assertEquals("52", Constant.FACTORY_ID_CS);
    }

    @Test
    public void standardErpDoneForXA() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto = new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId(Constant.FACTORY_ID_XA);
        service.standardErpDoneForXA(dto);
        Assert.assertEquals("56", Constant.FACTORY_ID_XA);
    }

    @Test
    public void doTheList() throws Exception {
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        String res = service.doTheList(psWipInfoDTO);
        Assert.assertEquals("", res);
        psWipInfoDTO.setFactoryId(new BigDecimal(Constant.FACTORY_ID_CS));
        service.doTheList(psWipInfoDTO);
        Assert.assertEquals("52", Constant.FACTORY_ID_CS);
        psWipInfoDTO.setFactoryId(new BigDecimal(Constant.FACTORY_ID_XA));
        service.doTheList(psWipInfoDTO);
        Assert.assertEquals("56", Constant.FACTORY_ID_XA);
    }

    @Test
    public void doErpMove() throws Exception {
        String factoryId = Constant.FACTORY_ID_HY;
        String prodplanIds = "8887771";
        service.doErpMove(prodplanIds, factoryId);
        Assert.assertEquals("55", factoryId);
        factoryId = Constant.FACTORY_ID_CS;
        service.doErpMove(prodplanIds, factoryId);
        Assert.assertEquals("52", factoryId);
        factoryId = Constant.FACTORY_ID_XA;
        service.doErpMove(prodplanIds, factoryId);
        Assert.assertEquals("56", factoryId);
    }

    @Test
    public void doErpDone() throws Exception {
        String factoryId = Constant.FACTORY_ID_HY;
        String prodplanIds = "8887771";
        service.doErpDone(prodplanIds, factoryId);
        Assert.assertEquals("55", factoryId);
        factoryId = Constant.FACTORY_ID_CS;
        service.doErpDone(prodplanIds, factoryId);
        Assert.assertEquals("52", factoryId);
        factoryId = Constant.FACTORY_ID_XA;
        service.doErpDone(prodplanIds, factoryId);
        Assert.assertEquals("56", factoryId);
    }

    @Test
    public void doTheList4XA() throws Exception {
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setFactoryId(new BigDecimal(Constant.FACTORY_ID_XA));
        service.doTheList4XA(psWipInfoDTO);
        Assert.assertEquals("56", Constant.FACTORY_ID_XA);
    }

    public void doErpMove4XA() throws Exception {
        String factoryId = Constant.FACTORY_ID_XA;
        String prodplanIds = "8887771";
        service.doErpMove4XA(prodplanIds, factoryId);
        Assert.assertEquals("56", factoryId);
    }

    public void doErpDone4XA() throws Exception {
        String factoryId = Constant.FACTORY_ID_XA;
        String prodplanIds = "8887771";
        service.doErpDone4XA(prodplanIds, factoryId);
        Assert.assertEquals("56", factoryId);
    }

    public void doErpMove4CS() throws Exception {
        String factoryId = Constant.FACTORY_ID_CS;
        String prodplanIds = "8887771";
        service.doErpMove4CS(prodplanIds, factoryId);
        Assert.assertEquals("52", factoryId);
    }

    @Test
    public void doErpDone4CS() throws Exception {
        String factoryId = Constant.FACTORY_ID_CS;
        String prodplanIds = "8887771";
        service.doErpDone4CS(prodplanIds, factoryId);
        Assert.assertEquals("52", factoryId);
    }

    @Test
    public void testGenerateheadInfo() throws Exception {
        // 创建输入参数
        StandardAutomaticSubmitWarehouseDTO record = new StandardAutomaticSubmitWarehouseDTO();
        record.setNowBillNo("123456");
        record.setProdplanIdParam("789012");
        record.setSubStock("SUB_STOCK");
        record.setFactoryId("55");

        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfoZero = new PsWipInfo();
        psWipInfoZero.setItemNo("ITEM_NO_ZERO");

        PsTask psTask = new PsTask();
        psTask.setTaskQty(new BigDecimal("1"));
        psTask.setTaskNo("TASK_NO");
        psTask.setProdplanNo("PRODPLAN_NO");
        psTask.setProdplanId("PRODPLAN_ID");
        psTask.setItemNo("ITEM_NO");
        psTask.setItemName("ITEM_NAME");

        WarehouseEntryInfo head = new WarehouseEntryInfo();
        PowerMockito.when(
                factoryConfig.getCommonEntityId()).thenReturn("01");
        // 调用方法
        Whitebox.invokeMethod(service, "generateheadInfo", record, psWipInfoList, psWipInfoZero, psTask, head);
        // 验证结果
        Assert.assertEquals(head.getWarehouseEntryId(), warehouseInfoServiceImpl.getUUNo());
        Assert.assertEquals(head.getBillNo(), "123456");
        Assert.assertEquals(head.getCreateBy(), Constant.SYSTEM);
        Assert.assertEquals(head.getCommitedQty().compareTo(new BigDecimal(psWipInfoList.size())), 0);
        Assert.assertEquals(head.getTaskQty().compareTo(psTask.getTaskQty()), 0);
        Assert.assertEquals(head.getProdplanNo(), "TASK_NO");
        Assert.assertEquals(head.getTaskNo(), "PRODPLAN_NO");
        Assert.assertEquals(head.getAttribute1(), "ITEM_NO_ZERO");
        Assert.assertEquals(head.getSubStock(), "SUB_STOCK");
        Assert.assertEquals(head.getLastUpdatedBy(), Constant.SYSTEM);
        Assert.assertEquals(head.getStatus(), MpConstant.STATUS_ZERO);
        Assert.assertEquals(head.getFactoryId().compareTo(new BigDecimal(record.getFactoryId())), 0);
        Assert.assertEquals(head.getItemNo(), "ITEM_NO");
        Assert.assertEquals(head.getItemName(), "ITEM_NAME");
    }

    @Test
    public void testGenerateDetailInfo() throws Exception {
        // 创建输入参数
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setAttribute1("ATTR1_1");
        psWipInfo1.setWorkOrderNo("WORKORDERNO_1");
        psWipInfo1.setSn("SN_1");
        psWipInfo1.setFactoryId(new BigDecimal(55));
        psWipInfo1.setOrgId(new BigDecimal(55));
        psWipInfo1.setEntityId(new BigDecimal(2));
        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setAttribute1("ATTR1_2");
        psWipInfo2.setWorkOrderNo("WORKORDERNO_2");
        psWipInfo2.setSn("SN_2");
        psWipInfo2.setFactoryId(new BigDecimal(55));
        psWipInfo2.setOrgId(new BigDecimal(55));
        psWipInfo2.setEntityId(new BigDecimal(2));
        psWipInfoList.add(psWipInfo1);
        psWipInfoList.add(psWipInfo2);

        String nowBillNo = "BILL_NO";

        List<WarehouseEntryDetailDTO> detailList = new ArrayList<>();
        PowerMockito.when(
                factoryConfig.getCommonEntityId()).thenReturn("01");
        // 调用方法
        Whitebox.invokeMethod(service, "generateDetailInfo", psWipInfoList, nowBillNo, detailList);

        // 验证结果
        Assert.assertEquals(detailList.size(), 2);

        WarehouseEntryDetailDTO detailDto1 = detailList.get(0);
        detailDto1.setWarehouseEntryDetailId("66666666666");
        Assert.assertEquals(detailDto1.getWarehouseEntryDetailId(), "66666666666");
        Assert.assertEquals(detailDto1.getBillNo(), nowBillNo);
        Assert.assertEquals(detailDto1.getProdplanId(), "ATTR1_1");
        Assert.assertEquals(detailDto1.getWorkOrderNo(), "WORKORDERNO_1");
        Assert.assertEquals(detailDto1.getSn(), "SN_1");
        Assert.assertEquals(detailDto1.getCreateBy(), Constant.SYSTEM);
        Assert.assertEquals(detailDto1.getFactoryId().toString(), "55");
        Assert.assertEquals(detailDto1.getOrgId().toString(), "55");
        Assert.assertEquals(detailDto1.getEntityId().toString(), "1");

        WarehouseEntryDetailDTO detailDto2 = detailList.get(1);
        detailDto2.setWarehouseEntryDetailId("7777777");
        Assert.assertEquals(detailDto2.getWarehouseEntryDetailId(), "7777777");
        Assert.assertEquals(detailDto2.getBillNo(), nowBillNo);
        Assert.assertEquals(detailDto2.getProdplanId(), "ATTR1_2");
        Assert.assertEquals(detailDto2.getWorkOrderNo(), "WORKORDERNO_2");
        Assert.assertEquals(detailDto2.getSn(), "SN_2");
        Assert.assertEquals(detailDto2.getCreateBy(), Constant.SYSTEM);
        Assert.assertEquals(detailDto2.getFactoryId().toString(), "55");
        Assert.assertEquals(detailDto2.getOrgId().toString(), "55");
        Assert.assertEquals(detailDto2.getEntityId().toString(), "1");

    }


}
