package com.zte.autoTest.unitTest;

import com.zte.application.PsWipInfoService;
import com.zte.application.impl.WhiteListInfoServiceImpl;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.WhiteListInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.ReworkTaskDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WhiteListInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/4
 */
@PrepareForTest({BasicsettingRemoteService.class})
public class WhiteListInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WhiteListInfoServiceImpl whiteListInfoService;

    @Mock
    private WhiteListInfoRepository whitelistInfoRepository;
    @Mock
    private PsWipInfoService psWipInfoService;

    @Test
    public void batchInsertWhiteListInfo() throws Exception {

        List<WhiteListInfoDTO> list = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTO.setSn("777888900001");
        whiteListInfoDTO.setItemNo("41255");
        list.add(whiteListInfoDTO);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        List<BsItemInfo> infoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("41255");
        infoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getStyleInfo(Mockito.anyString())).thenReturn(infoList);
        PowerMockito.when(whitelistInfoRepository.batchInsertWhiteListInfoMerge(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(whiteListInfoService.batchInsertWhiteListInfo(list));

    }


    @Test
    public void batchInsertWhiteListInfoDeleteWipInfo() throws Exception {

        List<WhiteListInfoDTO> list = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTO.setSn("880542130022220119777777700016");
        whiteListInfoDTO.setItemNo("41255");
        list.add(whiteListInfoDTO);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        List<BsItemInfo> infoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("41255");
        infoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getStyleInfo(Mockito.anyString())).thenReturn(infoList);
        PowerMockito.when(whitelistInfoRepository.batchInsertWhiteListInfoMerge(Mockito.any())).thenReturn(1);

        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfos.add(psWipInfo);
        psWipInfo.setSn("880542130022220119777777700016");
        psWipInfo.setParentSn("777888900001");
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfos);
        PowerMockito.when(psWipInfoService.deleteWipInfoBysn(Mockito.any())).thenReturn(1);
        ReworkTaskDTO reworkTaskDTO = new ReworkTaskDTO();
        reworkTaskDTO.setWhiteListInfos(list);

        List<WhiteListInfoDTO> whiteListInfoDetailBySN = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO1 = new WhiteListInfoDTO();
        whiteListInfoDTO1.setItemNo("41255");
        whiteListInfoDetailBySN.add(whiteListInfoDTO1);
        PowerMockito.when(whitelistInfoRepository.getWhiteListInfoDetailBySN(Mockito.any())).thenReturn(whiteListInfoDetailBySN);
        PowerMockito.when(whitelistInfoRepository.deleteWhiteListInfoDetailBySN(Mockito.any())).thenReturn(1);
        PsWipInfo wipInfoBySn = new PsWipInfo();
        wipInfoBySn.setItemNo("41255");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.any())).thenReturn(wipInfoBySn);
        Assert.assertNotNull(whiteListInfoService.batchInsertWhiteListInfoDeleteWipInfo(reworkTaskDTO));
    }
}
