package com.zte.autoTest.unitTest;
import com.zte.application.PmRepairInfoService;
import com.zte.application.impl.RepairStoreDetailServiceImpl;
import com.zte.domain.model.PmRepairInfo;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.math3.analysis.function.Power;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class RepairStoreDetailServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
    private RepairStoreDetailServiceImpl repairStoreDetailService;
	@Mock
	private PmRepairInfoService pmRepairInfoService;

	@Test
	public void downExcel() throws Exception {
		PmRepairInfoDTO record = new PmRepairInfoDTO();
		record.setFromStation("单板生产");
		String [] dateStr = {"2020-12-01 00:00:00","2021-01-31 23:59:59"};
		record.setRepairDate(dateStr);
		List<PmRepairInfo> pmRepairInfoList = new ArrayList<>();
		PmRepairInfo pmRepairInfo = new PmRepairInfo();
		pmRepairInfo.setReturnedDate(new Date());
		pmRepairInfo.setApplicationSection("12");
		pmRepairInfoList.add(pmRepairInfo);
		PowerMockito.when(pmRepairInfoService.listAllRepairStock(record)).thenReturn(pmRepairInfoList);
		Assert.assertNotNull(repairStoreDetailService.downExcel(record));
	}


}
