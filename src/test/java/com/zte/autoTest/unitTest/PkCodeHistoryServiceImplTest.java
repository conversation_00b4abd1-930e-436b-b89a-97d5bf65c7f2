package com.zte.autoTest.unitTest;

import com.zte.application.impl.PkCodeHistoryServiceImpl;
import com.zte.application.impl.SmtMachineMaterialPrepareServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PkCodeHistory;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PkCodeHistoryRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PkCodeHistoryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @Date 2020/10/22 00
 * @description:
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({SmtMachineMaterialPrepareServiceImpl.class,BasicsettingRemoteService.class})
public class PkCodeHistoryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PkCodeHistoryServiceImpl pkCodeHistoryServiceImpl;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;

    @Mock
    private SmtMachineMaterialPrepareServiceImpl smtMachineMaterialPrepareService;
    @Before
    public void before(){
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
    }

    @Test
    public void queryHandoverQty(){
        PkCodeHistoryDTO record = new PkCodeHistoryDTO();
        Assert.assertNotNull(pkCodeHistoryServiceImpl.queryHandoverQty(record));
    }

    @Test
    public void getListAllInfo() throws Exception {
        PkCodeHistoryDTO record =  new PkCodeHistoryDTO();
        List<PkCodeHistoryDTO> pkCodeHistoryDTOS=new ArrayList<>();
        record.setItemCode("20200316Test");
        record.setObjectId("ZTE20200316");
        record.setProgramName(Constant.SCLLJJFROMPDA);
        pkCodeHistoryDTOS.add(record);
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList=new ArrayList<>();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare=new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setItemCode("20200316Test");
        smtMachineMaterialPrepare.setObjectId("ZTE20200316");
        smtMachineMaterialPrepare.setFactoryId(new BigDecimal(52));
        smtMachineMaterialPrepare.setWetLevel("一级");
        smtMachineMaterialPrepare.setItemType("C");
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);
        PowerMockito.when(pkCodeHistoryRepository.getListAllInfo(any())).thenReturn(pkCodeHistoryDTOS);
//        PowerMockito.when(smtMachineMaterialPrepareService.getAbcTypeByItemNo(Mockito.any())).thenReturn(smtMachineMaterialPrepareList);
//        PowerMockito.when(smtMachineMaterialPrepareService.getWetLevelByPkcode(Mockito.any())).thenReturn(smtMachineMaterialPrepareList);

        List<PkCodeHistoryDTO> resultList=pkCodeHistoryServiceImpl.getListAllInfo(record);
        Assert.assertTrue(resultList.size()>0);
    }

    @Test
    public void getListAllInfoPage(){
        Page<PkCodeHistoryDTO> page = new Page<>();
        Assert.assertNotNull(pkCodeHistoryServiceImpl.getListAllInfoPage(page));
    }

    @Test
    public void countExportTotal(){
        PowerMockito.when(pkCodeHistoryRepository.getCount(any())).thenReturn(1L);
        Assert.assertNotNull(pkCodeHistoryServiceImpl.countExportTotal(new PkCodeHistoryDTO()));
    }

    @Test
    public void queryExportData(){
        List<PkCodeHistory> list = new ArrayList<>();
        PkCodeHistory history = new PkCodeHistory();
        history.setHistoryId("123");
        list.add(history);
        PowerMockito.when(pkCodeHistoryRepository.getPage(any())).thenReturn(list);
        try{
            pkCodeHistoryServiceImpl.queryExportData(new PkCodeHistoryDTO(),1,10);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.SEARCH_REELID_ERROR);
        }
    }

    @Test
    public void queryExportData1() throws Exception {
        List<PkCodeHistory> list = new ArrayList<>();
        List<BsItemInfo> abcTypeList = new ArrayList<>();
        PkCodeHistory history = new PkCodeHistory();
        PkCodeHistory history1 = new PkCodeHistory();
        PkCodeHistory history2 = new PkCodeHistory();
        PkCodeHistory history3 = new PkCodeHistory();

        history.setHistoryId("123");
        history.setItemCode("12333");
        history1.setItemCode("12333");
        history2.setHistoryId("123");
        history3.setItemCode("12334");
        list.add(history);
        list.add(history1);
        list.add(history2);
        list.add(history3);

        BsItemInfo bsItemInfo = new BsItemInfo();
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo.setAbcType("A");
        bsItemInfo1.setAbcType("b");
        bsItemInfo.setItemNo("12333");
        bsItemInfo1.setItemNo("12334");

        PowerMockito.when(pkCodeHistoryRepository.getPage(any())).thenReturn(list);
        abcTypeList.add(bsItemInfo);
        abcTypeList.add(bsItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.querySemiItemNo(Mockito.any())).thenReturn(abcTypeList);
        Assert.assertNotNull(pkCodeHistoryServiceImpl.queryExportData(new PkCodeHistoryDTO(),1,10));
    }

    @Test
    public void getPage() throws Exception {
        List<PkCodeHistory> list = new ArrayList<>();
        List<BsItemInfo> abcTypeList = new ArrayList<>();
        pkCodeHistoryServiceImpl.getPage(new PkCodeHistoryDTO());
        PkCodeHistory history = new PkCodeHistory();
        PkCodeHistory history1 = new PkCodeHistory();
        PkCodeHistory history2 = new PkCodeHistory();
        PkCodeHistory history3 = new PkCodeHistory();
        PkCodeHistoryDTO record = new PkCodeHistoryDTO();

        history.setHistoryId("123");
        history.setItemCode("12333");
        history1.setItemCode("12333");
        history2.setHistoryId("123");
        history3.setItemCode("12334");
        list.add(history);
        list.add(history1);
        list.add(history2);
        list.add(history3);

        BsItemInfo bsItemInfo = new BsItemInfo();
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setAbcType("b");
        bsItemInfo.setItemNo("12333");
        bsItemInfo1.setItemNo("12334");

        PowerMockito.when(pkCodeHistoryRepository.getPage(any())).thenReturn(list);
        Assert.assertNotNull(pkCodeHistoryServiceImpl.getPage(record));
        record.setReelidFlag(Constant.TRUE);
        PowerMockito.when(BasicsettingRemoteService.querySemiItemNo(Mockito.any())).thenReturn(abcTypeList);
        try{
            pkCodeHistoryServiceImpl.getPage(record);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.ITEM_NO_IS_NO_EXIST);
        }
        abcTypeList.add(bsItemInfo);
        abcTypeList.add(bsItemInfo1);
        PowerMockito.when(BasicsettingRemoteService.querySemiItemNo(Mockito.any())).thenReturn(abcTypeList);
        pkCodeHistoryServiceImpl.getPage(record);
    }
}
