package com.zte.autoTest.unitTest;

import com.zte.application.*;
import com.zte.application.impl.PmRepairDetailServiceImpl;
import com.zte.application.impl.PmRepairRcvDetailServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Matchers.*;

@PrepareForTest({BasicsettingRemoteService.class, MicroServiceRestUtil.class, CommonUtils.class,PlanscheduleRemoteService.class})
public class PmRepairDetailServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairDetailServiceImpl service;

    @Mock
    PkCodeInfoRepository pkCodeInfoRepository;
  
    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    AvlService avlService;

    @Mock
    WipScanHistoryService wipScanHistoryService;

    @Mock
    PmRepairRcvDetailServiceImpl pmRepairRcvDetailServiceImp;

    @Mock
    PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    WipExtendIdentificationService wipExtendIdentificationService;

    @Mock
    DatawbRemoteService datawbRemoteService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private PmRepairDetailRepository pmRepairInfoRepository;
    @Mock
    private PmRepairRcvServiceImpl pmRepairRcvServiceImpl;

    @Mock
    private MaintenanceMaterialLineRepository maintenanceMaterialLineRepository;
    @Mock
    private String repairCheckItemCode;

    @Test
    public void batchInsertOptRecord()throws Exception {
        List<PmRepairDisplaceDTO> pmRepairDisplaceDTOS = new ArrayList<>();
        pmRepairDisplaceDTOS.add(new PmRepairDisplaceDTO());
        service.batchInsertOptRecord(pmRepairDisplaceDTOS);
        pmRepairDisplaceDTOS.add(new PmRepairDisplaceDTO(){{setRepairSn("22");}});
        service.batchInsertOptRecord(pmRepairDisplaceDTOS);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void checkItemCode()throws Exception {
        try {
            service.checkItemCodeAndReplaceRef(new PmRepairDetailDTO() {{
                setItemCode("itemCode");
            }}, "itemCode");
        }catch (Exception e){
            Assert.assertEquals( MessageId.REPLACE_SN_ITEM_CODE_VEIFY_FAILED, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void checkEnv()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class,PlanscheduleRemoteService.class,BasicsettingRemoteService.class);;
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo(){{
            setAttribute1("1");
        }});
        List<PsTask> psTaskList=new ArrayList<>();
        PsTask psTask=new PsTask();
        psTask.setSourceSys("STEP");
        psTask.setLeadFlag("1");
        psTask.setIsLead("HFS");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        List<PkCodeInfo> replaceSnList=new ArrayList<>();
        PkCodeInfo pkCodeInfo=new PkCodeInfo();
        pkCodeInfo.setIsLead("30");
        replaceSnList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(replaceSnList);
        List<SysLookupTypesDTO> sysLookUpList =new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("35");
        sysLookupTypesDTO.setLookupMeaning("1");
        sysLookupTypesDTO.setDescriptionChinV("HFS");
        sysLookUpList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(sysLookUpList);
        PmRepairDetailDTO pmRepairDetailDTO=new PmRepairDetailDTO();
        pmRepairDetailDTO.setIsZjSub("N");
        service.checkEnv(pmRepairDetailDTO);
        psTask.setSourceSys("WMES");
        service.checkEnv(pmRepairDetailDTO);
        // 测试1
        pmRepairDetailDTO.setIsZsFlag("Y");
        service.checkEnv(pmRepairDetailDTO);
        // 测试2
        pmRepairDetailDTO.setIsZsFlag("N");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(null);
        try {
            service.checkEnv(pmRepairDetailDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.PRODPLAN_IS_NULL, e.getMessage());
        }
        // 测试3
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(new PsWipInfo() {{
            setAttribute1("1");
        }});
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(null);
        try {
            service.checkEnv(pmRepairDetailDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.TASK_NOT_HAVE_DETAILS, e.getMessage());
        }
        // 测试4
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(null);
        Assert.assertNull(service.checkEnv(pmRepairDetailDTO));
        // 测试5
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(replaceSnList);
    }

    @Test
    public void getStyleInfoByPtp()throws Exception {
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(any())).thenReturn(new PkCodeInfo(){{
            setSourceBatchCode("1");
            setBgBrandNo("1");
            setStyle("1");
        }});
        PowerMockito.when(smtSnMtlTracingTService.judgeFromBarCodeCenter(any(),anyString())).thenReturn(true);
        Map<String, BarcodeExpandDTO> map=new HashMap<>();
        BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
        barcodeExpandDTO.setSupplierName("供应商");
        barcodeExpandDTO.setBrandName("品牌");
        barcodeExpandDTO.setSpecModel("品牌");
        map.put("1",barcodeExpandDTO);
        PowerMockito.when(smtSnMtlTracingTService.getBarcodeExpandDTOMap(any())).thenReturn(map);
        Assert.assertNotNull(service.getStyleInfoByPtp(new PmRepairDetailDTO(), "1"));
        try {
            Assert.assertNotNull(service.getStyleInfoByPtp(new PmRepairDetailDTO(), "1"));
        } catch (Exception e) {

        }
    }

    @Test
    public void postRepairInfo() throws Exception{
        PowerMockito.mockStatic(BasicsettingRemoteService.class, MicroServiceRestUtil.class, CommonUtils.class);

        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setPageType("1");
        dto.setRepairDetailId("f7e092ca-1fbd-49c2-9644-ae206f2e1aaa");
        dto.setReplaceItemCode("045020700009");
        dto.setReplaceSn("ZTE202103300002");
        dto.setSn("730638900030");
        dto.setItemSn("730638900030");
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("11");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("11");
        PowerMockito.when(psWipInfoService.getWipInfoBySn("730638900030")).thenReturn(null);
        try {
            service.postRepairInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals("11", e.getMessage());
        }

        dto.setPageType("2");
        PowerMockito.when(pmRepairRcvDetailRepository.getToRepairRecInfo(any())).thenReturn(null);
        try {
            service.postRepairInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals("11", e.getMessage());
        }

        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new ArrayList<>();
        PmRepairRcvDetailDTO rcvDto = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTOs.add(rcvDto);
        rcvDto.setItemCode("127230950329AIB");
        rcvDto.setItemName("ZXRAN A9631A S26 MA0TAC26B");
        rcvDto.setRcvProdplanId("7306389");
        rcvDto.setErrorCode("A1");
        rcvDto.setErrorDescription("测试A1");
        PowerMockito.when(pmRepairRcvDetailRepository.getToRepairRecInfo(any())).thenReturn(pmRepairRcvDetailDTOs);
        Assert.assertThrows(NullPointerException.class, () -> service.postRepairInfo(dto));
        
        PsWipInfo wipInfoDto=new PsWipInfo();
        wipInfoDto.setWorkOrderNo("");
        wipInfoDto.setCraftSection("");
        wipInfoDto.setNextProcess("");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any())).thenReturn(wipInfoDto);
        Assert.assertThrows(NullPointerException.class, () -> service.postRepairInfo(dto));

        dto.setPageType("1");
        dto.setRepairProductMstype("报废");
        List<MaintenanceMaterialInfoDTO> materialList = new ArrayList<>();
        MaintenanceMaterialInfoDTO dto1 = new MaintenanceMaterialInfoDTO();
        dto1.setReceptionDetailId("f7e092ca-1fbd-49c2-9644-ae206f2e1aaa");
        dto1.setItemNo("045020700009");
        dto1.setChangeType("以旧换新");
        dto1.setItemStaus("9");
        materialList.add(dto1);
        PowerMockito.when(maintenanceMaterialLineRepository.getListByReceptionDetailIds(any())).thenReturn(materialList);
        List<PmRepairDetailDTO> repairDetailDTOS = new ArrayList<>();
        repairDetailDTOS.add(dto);
        PowerMockito.when(pmRepairInfoRepository.getListByReceptionDetailId(any())).thenReturn(repairDetailDTOS);
        PowerMockito.when(pmRepairRcvServiceImpl.checkResourceStaus(any())).thenReturn(false);
        try {
            service.postRepairInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_IS_NOT_USED_CANNOT_SCARP, e.getMessage());
        }

        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("730638900030");
        barcodeExpandDTO.setBrandName("730638900030");
        barcodeExpandDTO.setSupplierName("730638900030");
        PowerMockito.when(barcodeCenterRemoteService.expandQueryOneByOne(any())).thenReturn(barcodeExpandDTOList);
        try {
            Assert.assertNotNull(service.postRepairInfo(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_IS_NOT_USED_CANNOT_SCARP, e.getMessage());
        }

        PowerMockito.field(PmRepairDetailServiceImpl.class,"repairCheckItemCode")
                .set(service,"Y");
        try {
            Assert.assertNotNull(service.postRepairInfo(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_IS_NOT_USED_CANNOT_SCARP, e.getMessage());
        }

        dto.setIsLocationNo("Y");
        dto.setItemCode("123");
        try {
            Assert.assertNotNull(service.postRepairInfo(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIAL_IS_NOT_USED_CANNOT_SCARP, e.getMessage());
        }

        dto.setItemCode(null);
        try {
            Assert.assertNotNull(service.postRepairInfo(dto));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_CODE_EMPTY, e.getMessage());
        }
    }

    @Test
    public void validateItemSnAndRepairedSnRefTest() throws Exception {
        String sn = "";
        String itemSn = "";
        try {
            service.validateItemSnAndRepairedSnRef(itemSn, sn);
        }catch (Exception e) {
            Assert.assertEquals( MessageId.REPAIR_FORM_ITEM_SN_NULL, e.getMessage());
        }
        itemSn = "test";
        try {
            service.validateItemSnAndRepairedSnRef(itemSn, sn);
        }catch (Exception e) {
            Assert.assertEquals( MessageId.REPAIR_FORM_REPAIRED_SN_NULL, e.getMessage());
        }
        sn = "test";
        WipExtendIdentification entity = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(anyString(), anyString())).thenReturn(null);
        service.validateItemSnAndRepairedSnRef(itemSn, sn);
        entity.setFormType(Constant.BINDED_AND_PASSWORKSTATION_COMPLETE);
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(anyString(), anyString())).thenReturn(entity);
        Assert.assertTrue(service.validateItemSnAndRepairedSnRef(itemSn, sn));
    }

    @Test
    public void checkBindingRelationshipTest() throws Exception {
        String replaceSn = "";
        WipExtendIdentification entity = new WipExtendIdentification();
        PowerMockito.when(wipExtendIdentificationService.selectWipExtendIdentificationByFormSnAndSn(anyString(), anyString())).thenReturn(entity);
        try {
            Whitebox.invokeMethod(service, "checkBindingRelationship", replaceSn);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.REPAIR_FORM_REPLACED_SN_HAS_BOUND, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkItemCodeAndReplaceRefTest() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        String itemCode = "test";
        dto.setItemCode("test");
        service.checkItemCodeAndReplaceRef(dto, itemCode);
        dto.setItemCode("test1");
        dto.setIsZjSub("Y");
        List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getItemInfoList(itemCode)).thenReturn(mtlRelatedItemsEntityDTOList);
        try{
            service.checkItemCodeAndReplaceRef(dto, itemCode);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.REPLACE_SN_ITEM_CODE_VEIFY_FAILED, e.getMessage());
        }
        MtlRelatedItemsEntityDTO dto1 = new MtlRelatedItemsEntityDTO();
        dto1.setReplaceItemCode("test1");
        mtlRelatedItemsEntityDTOList.add(dto1);
        PowerMockito.when(datawbRemoteService.getItemInfoList(itemCode)).thenReturn(mtlRelatedItemsEntityDTOList);
        service.checkItemCodeAndReplaceRef(dto, itemCode);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleZjNotSubTest() throws Exception {
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setItemCode("test");
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTOList.add(barcodeExpandDTO);
        barcodeExpandDTO.setItemCode("test");
        PowerMockito.when(barcodeCenterRemoteService.expandQueryOneByOne(any())).thenReturn(barcodeExpandDTOList);
        PowerMockito.when(pmRepairRcvDetailService.getZjSn(any())).thenReturn("test");
        PowerMockito.when(pmRepairRcvDetailService.getEnvAttrByTaskNO(any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.ENV_CHECK_FAILED, e.getMessage());
        }
        PowerMockito.when(pmRepairRcvDetailService.getEnvAttrByTaskNO(any())).thenReturn(new BigDecimal("12"));
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.REPLACE_SN_ENV_QUERY_NULL_BY_CODE_CENTER, e.getMessage());
        }
        barcodeExpandDTO.setIsLead("lead");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValuesDTO> sysLookupValuesDTOS = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescriptionChin("lead1");
        sysLookupValuesDTOS.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(sysLookupValuesDTOS);
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.REPLACE_SN_ENV_CONVERT_ERROR_BY_DICT, e.getMessage());
        }
        sysLookupValuesDTO.setDescriptionChin("lead");
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.ENV_CHECK_FAILED, e.getMessage());
        }
        sysLookupValuesDTO.setAttribute1("10");
        try {
            Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.ENV_CHECK_FAILED, e.getMessage());
        }
        sysLookupValuesDTO.setAttribute1("13");
        Whitebox.invokeMethod(service, "handleZjNotSub", dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getRepairDetailBySnList() throws Exception{
        List<String> snList = new ArrayList<>();
        snList.add("1231231");
        List<PmRepairDetailDTO> dtoList = new ArrayList<>();
        PowerMockito.when(pmRepairInfoRepository.getRepairDetailBySnList(anyList())).thenReturn(dtoList);
        Assert.assertNotNull((service.getRepairDetailBySnList(snList)));
    }


    /* Started by AICoder, pid:g8bc0vc711sedb514b890b1e40dbaf8d0c65a9eb */
    @Test
    public void verifyItemSnExist() throws Exception{
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<String> snList = new ArrayList<>();
        snList.add("1231231");
        List<PmRepairDetailDTO> dtoList = new ArrayList<>();
        PowerMockito.when(pmRepairInfoRepository.getRepairDetailBySnList(anyList())).thenReturn(dtoList);
        ReflectUtil.setFieldValue(service,"queryMaterialBarcodeSwitch","N");
        PmRepairDetailDTO pmRepairDetailDTO = new PmRepairDetailDTO();
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        ReflectUtil.setFieldValue(service,"queryMaterialBarcodeSwitch","Y");
        pmRepairDetailDTO.setPageType("2");
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        pmRepairDetailDTO.setPageType("1");
        pmRepairDetailDTO.setItemSn("2");
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        pmRepairDetailDTO.setItemSn("");
        pmRepairDetailDTO.setIsSub("Y");
        PowerMockito.when(PlanscheduleRemoteService.checkIsPartTask(anyString())).thenReturn(false);
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        pmRepairDetailDTO.setSubSn("778899900001");
        PowerMockito.when(PlanscheduleRemoteService.checkIsPartTask(anyString())).thenReturn(true);
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        PowerMockito.when(wipExtendIdentificationService.getList(any())).thenReturn(Arrays.asList(new WipExtendIdentification()));
        pmRepairDetailDTO.setSubSn("778899900001");
        PowerMockito.when(PlanscheduleRemoteService.checkIsPartTask(anyString())).thenReturn(true);
        Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        Assert.assertNotNull(pmRepairDetailDTO);

        pmRepairDetailDTO.setIsSub("N");

        PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(Arrays.asList(new WipExtendIdentification(){{setSn("2");}}));
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("730638900030");
        barcodeExpandDTO.setBrandName("730638900030");
        barcodeExpandDTO.setSupplierName("730638900030");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQueryOneByOne(any())).thenReturn(barcodeExpandDTOList);
        try {
            Whitebox.invokeMethod(service,"verifyItemSnExist",pmRepairDetailDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.PLEASE_SELECT_BARCODE_FOR_REPAIR_MATERIALS, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:g8bc0vc711sedb514b890b1e40dbaf8d0c65a9eb */

}