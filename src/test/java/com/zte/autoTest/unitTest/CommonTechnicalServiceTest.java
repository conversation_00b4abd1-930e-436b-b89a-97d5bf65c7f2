package com.zte.autoTest.unitTest;

import cn.hutool.core.net.URLEncodeUtil;
import com.zte.application.impl.common.CommonTechnicalServiceImpl;
import com.zte.domain.model.DocFilePropertiesRepository;
import com.zte.domain.model.technical.TechnicalChangeDetailRepository;
import com.zte.domain.model.technical.TechnicalChangeHeadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.FilePreViewRemoteService;
import com.zte.interfaces.dto.DocFilePropertiesEntityDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeHeadDTO;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-20 23:34
 */
@PrepareForTest({CenterfactoryRemoteService.class, URLEncodeUtil.class})
public class CommonTechnicalServiceTest extends PowerBaseTestCase {
    @InjectMocks
    CommonTechnicalServiceImpl commonServiceImpl;

    @Mock
    CloudDiskHelper cloudDiskHelper;

    @Mock
    TechnicalChangeHeadRepository headRepository;

    @Mock
    TechnicalChangeDetailRepository detailRepository;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private DocFilePropertiesRepository docFilePropertiesRepository;
    @Mock
    private FilePreViewRemoteService filePreViewService;

    @Before
    public void init() {
        PowerMockito.mockStatic(URLEncodeUtil.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
    }

    @Test
    public void getContent() throws Exception {

        TechnicalChangeHeadDTO byChgReqNo = new TechnicalChangeHeadDTO();
        PowerMockito.when(headRepository.getTechnicalChangeHeadDTOByChgReqNo(Mockito.anyString()))
                .thenReturn(byChgReqNo);

        List<TechnicalChangeDetailDTO> listByBillNo = new LinkedList<>();
        TechnicalChangeDetailDTO b1 = new TechnicalChangeDetailDTO();
        b1.setProdplanId("123");
        listByBillNo.add(b1);
        PowerMockito.when(detailRepository.getListByBillNo(Mockito.anyString()))
                .thenReturn(listByBillNo)
        ;
        List<DocFilePropertiesEntityDTO> fileList = new LinkedList<>();
        DocFilePropertiesEntityDTO c1 = new DocFilePropertiesEntityDTO();
        c1.setDocType("0");
        c1.setDocName("1");
        fileList.add(c1);
        PowerMockito.when(docFilePropertiesRepository.getList(Mockito.any()))
                .thenReturn(fileList);

        Assert.assertNotNull(commonServiceImpl.getContent("123"));
    }

    @Test
    public void buildHrmPersonInfo() {
        Assert.assertEquals("123",commonServiceImpl.buildHrmPersonInfo("123"));
    }

    @Test
    public void getFileUrl() {
        commonServiceImpl.getFileUrl("a", "a", 1);
        Assert.assertNotNull(commonServiceImpl.getFileUrl("a", "a", 2));
    }
}
