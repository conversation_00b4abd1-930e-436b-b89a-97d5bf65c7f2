package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.DailyReportServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.util.Pair;

import java.util.*;

import static org.mockito.Matchers.any;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/9/23 16:46
 */
@PrepareForTest({PlanscheduleRemoteService.class, CrafttechRemoteService.class})
public class DailyReportServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private DailyReportServiceImpl service;

	@Mock
	private PsWipInfoRepository wipInfoRepository;

	@Mock
	private WipDailyStatisticReportRepository wipStatisticRepository;

	@Mock
	private WipScanHistoryRepository wipScanHistoryRepository;

	@Mock
	private WarehouseEntryDetailRepository entryDetailRepository;

	@Mock
	private WarehousehmEntryDetailRepository warehouseDetailRepository;

	@Before
	public void init() {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, CrafttechRemoteService.class);
	}

	@Test
	public void insertSmt() {
		PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(any())).thenReturn(new PageRows<PsTask>(){{
			setRows(Lists.newArrayList(new PsTask() {{
				setProdplanId("1");
			}}));
			setTotal(1L);
		}});
		PowerMockito.when(PlanscheduleRemoteService.getWorkBasicByTask(any(), any(), any())).thenReturn(Lists.newArrayList(
				new PsWorkOrderBasic() {{
					setSourceTask("1");
					setProdPlanId("1");
					setRouteId("11");
					setCraftSection("SMT-A");
					setLineName("123");
				}}
		));
		PowerMockito.when(CrafttechRemoteService.getNextCraftSection(any())).thenReturn(Lists.newArrayList(
				new NextCraftSection() {{
					setProdPlanId("1");
					setCraftSection("SMT-A");
					setNextCraftSection("DIP");
					setRouteId("11");
				}}
		));
		PowerMockito.when(entryDetailRepository.getWipCraftOutQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("SMT-A");
					setQty(1);
				}}
		));
		PowerMockito.when(wipInfoRepository.getWipCraftQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("SMT-A");
					setQty(1);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getCraftOutQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("SMT-A");
					setQty(1);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getCraftInQty(any(), any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("SMT-A");
					setQty(1);
				}}
		));
		PowerMockito.when(wipStatisticRepository.selectByPlanIds(any(), any())).thenReturn(Lists.newArrayList(
				new WipDailyStatisticReport() {{
					setProdplanId("1");
					setSmtaQty(1);
					setSmtbQty(1);
				}}
		));
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
		service.insertSmtPage(Lists.newArrayList(), new Date());
	}

	@Test
	public void insertDip() {
		PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(any())).thenReturn(new PageRows<PsTask>(){{
			setRows(Lists.newArrayList(new PsTask() {{
				setProdplanId("1");
			}}));
			setTotal(1L);
		}});
		PowerMockito.when(PlanscheduleRemoteService.getWorkBasicByTask(any(), any(), any())).thenReturn(Lists.newArrayList(
				new PsWorkOrderBasic() {{
					setSourceTask("1");
					setProdPlanId("1");
					setRouteId("11");
					setCraftSection("DIP");
					setLineName("123");
				}},
				new PsWorkOrderBasic() {{
					setSourceTask("1");
					setProdPlanId("1");
					setRouteId("11");
					setCraftSection("清洗");
					setLineName("1234");
				}},
				new PsWorkOrderBasic() {{
					setSourceTask("1");
					setProdPlanId("1");
					setRouteId("11");
					setCraftSection("背板");
					setLineName("12345");
				}}
		));
		PowerMockito.when(CrafttechRemoteService.getNextCraftSection(any())).thenReturn(Lists.newArrayList(
				new NextCraftSection() {{
					setProdPlanId("1");
					setCraftSection("清洗");
					setNextCraftSection("背板");
					setRouteId("11");
				}}
		));
		PowerMockito.when(CrafttechRemoteService.getNextSubCraftSection(any())).thenReturn(Lists.newArrayList(
				new NextCraftSection() {{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setNextCraftSection("DIP");
					setNextSubCraftSection("CHECK_WELDING");
					setRouteId("11");
				}},
				new NextCraftSection() {{
					setProdPlanId("1");
					setCraftSection("CHECK_WELDING");
					setNextCraftSection("维修");
					setNextSubCraftSection("CHECK_WELDING");
					setRouteId("11");
				}},
				new NextCraftSection() {{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setNextCraftSection("入库");
					setNextSubCraftSection("N");
					setRouteId("11");
				}}
		));
		PowerMockito.when(entryDetailRepository.getWipCraftOutQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
		PowerMockito.when(wipInfoRepository.getWipCraftQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getCraftOutQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getCraftInQty(any(), any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
		PowerMockito.when(wipInfoRepository.getWipSubCraftQty(any(), any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}},
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("COATING");
					setQty(2);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getSubCraftOutQty(any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
		PowerMockito.when(wipScanHistoryRepository.getSubCraftInQty(any(), any(), any())).thenReturn(Lists.newArrayList(
				new WipCraftQtyDTO(){{
					setProdPlanId("1");
					setCraftSection("INSERTION");
					setQty(1);
				}}
		));
        PowerMockito.when(warehouseDetailRepository.queryInboundQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.querySubmitQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
		service.insertDipPage(Lists.newArrayList(), new Date(), Lists.newArrayList(
				new SysLookupValuesDTO(){{
					setLookupMeaning("6");
					setAttribute1("INSERTION");
				}},
				new SysLookupValuesDTO(){{
					setLookupMeaning("+");
					setAttribute1("INSERTION");
				}},
				new SysLookupValuesDTO(){{
					setLookupMeaning("*");
					setAttribute1("MILLING_PLATE");
				}}
		));
	}

	@Test
	public void aggAllInfo() {
		List<PsTask> taskList=new ArrayList<>();
		PsTask psTask=new PsTask();
		psTask.setProdplanId("77");
		taskList.add(psTask);
		Map<String, Map<String, Integer>> craftQty=new HashMap<>();
		Map<String, Integer> pqty=new HashMap<>();
		craftQty.put("77",pqty);
		Map<String, WipDailyStatisticReport> yesterdayQty=new HashMap<>();
		WipDailyStatisticReport wipDailyStatisticReport=new WipDailyStatisticReport();
		yesterdayQty.put("77",wipDailyStatisticReport);
		Map<String, Map<String, Integer>> craftInQty=new HashMap<>();
		Map<String, Integer> inqty=new HashMap<>();
		craftInQty.put("77",inqty);
		Map<String, Map<String, Integer>> craftOutQty=new HashMap<>();
		Map<String, Integer> outqty=new HashMap<>();
		craftOutQty.put("77",outqty);
		Map<String, String> lineName=new HashMap<>();
		Map<String, Integer> submitMap=new HashMap<>();
		Map<String, Integer> inboundMap=new HashMap<>();
		Map<String, Map<String, Integer>> subCraftQty=new HashMap<>();
		Map<String, Map<String, Integer>> subCraftInQty=new HashMap<>();
		Map<String, Map<String, Integer>> subCraftOutQty=new HashMap<>();
		AggAllInfoDTO aggAllInfoDTO = new AggAllInfoDTO();
		aggAllInfoDTO.setTaskList(taskList);
		aggAllInfoDTO.setCraftInQty(craftInQty);
		aggAllInfoDTO.setCraftQty(craftQty);
		aggAllInfoDTO.setYesterdayQty(yesterdayQty);
		aggAllInfoDTO.setCraftOutQty(craftOutQty);
		aggAllInfoDTO.setLineName(lineName);
		aggAllInfoDTO.setSubmitMap(submitMap);
		aggAllInfoDTO.setInboundMap(inboundMap);
		aggAllInfoDTO.setSubCraftQty(subCraftQty);
		aggAllInfoDTO.setSubCraftInQty(subCraftInQty);
		aggAllInfoDTO.setSubCraftOutQty(subCraftOutQty);
		Assert.assertEquals(new HashMap<>(), aggAllInfoDTO.getSubCraftOutQty());
		service.aggAllInfo(aggAllInfoDTO);
		Map<String, Map<String, Integer>> smtInQty=new HashMap<>();
		Map<String, Integer> smtIn=new HashMap<>();
		smtInQty.put("77",smtIn);
		Map<String, Map<String, Integer>> smtOutQty=new HashMap<>();
		Map<String, Integer> outQtymap=new HashMap<>();
		smtOutQty.put("77",outQtymap);

		Map<String, Map<String, String>> lineNames=new HashMap<>();
		Map<String, String> lineMap=new HashMap<>();
		lineNames.put("77",lineMap);

		WipDailyReportInfoDTO wipDailyReportInfoDTO = new WipDailyReportInfoDTO();
		wipDailyReportInfoDTO.setSmtOutQty(smtOutQty);
		wipDailyReportInfoDTO.setLineNameMap(lineNames);
		service.aggAllInfo(taskList, craftQty, yesterdayQty, smtInQty, wipDailyReportInfoDTO);
	}
}
