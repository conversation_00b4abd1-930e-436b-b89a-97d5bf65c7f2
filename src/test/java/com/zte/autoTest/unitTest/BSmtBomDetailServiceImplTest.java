package com.zte.autoTest.unitTest;

import com.zte.application.impl.BSmtBomDetailServiceImpl;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.Matchers.any;

public class BSmtBomDetailServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    BSmtBomDetailServiceImpl service;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Test
    public void getBomQtyByMtInfo() {
        Assert.assertEquals(0,service.getBomQtyByMtInfo(new SmtMachineMaterialMouting()));
        PowerMockito.when(bSmtBomDetailRepository.getBomQtyByMtInfo(any())).thenReturn(1);
    }
}