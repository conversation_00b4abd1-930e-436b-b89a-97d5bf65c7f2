package com.zte.autoTest.unitTest.bytedance;

import com.zte.application.CustomerDataUploadService;
import com.zte.application.PsWipInfoService;
import com.zte.application.kafka.consumer.SentBackBoardConsumer;
import com.zte.domain.model.PsWipInfo;
import com.zte.interfaces.dto.CustomerDataUploadDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-10 17:29
 */
@PrepareForTest({JacksonJsonConverUtil.class})
public class SentBackBoardConsumerTest extends PowerBaseTestCase {
    @InjectMocks
    private SentBackBoardConsumer sentBackBoardConsumer;
    /**
     * 本容器kafka 工厂id 多个工具逗号分隔
     */
    @Mock
    private String factoryId;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private CustomerDataUploadService customerDataUploadService;

    @Before
    public void init() {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void dealKafkaMsg() throws Exception {
        String message = "msg";
        sentBackBoardConsumer.dealKafkaMsg("");

        List<CustomerDataUploadDTO> sentBackBoardDTOList = new LinkedList<>();
        CustomerDataUploadDTO a1 = new CustomerDataUploadDTO();
        a1.setBoardSn("22");
        a1.setCustomerName("t55");
        sentBackBoardDTOList.add(a1);
        sentBackBoardConsumer.dealKafkaMsg(message);

        PowerMockito.field(SentBackBoardConsumer.class, "factoryId").set(sentBackBoardConsumer, "53,54");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(sentBackBoardDTOList);

        sentBackBoardConsumer.dealKafkaMsg(message);

        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.any()))
                .thenReturn("56");
        sentBackBoardConsumer.dealKafkaMsg(message);

        List<PsWipInfo> wipInfoList = new LinkedList<>();
        PsWipInfo b1 = new PsWipInfo();
        b1.setSn("22");
        wipInfoList.add(b1);
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any()))
                .thenReturn(wipInfoList)
        ;
        Assert.assertEquals("msg", message);
        sentBackBoardConsumer.dealKafkaMsg(message);
    }

}
