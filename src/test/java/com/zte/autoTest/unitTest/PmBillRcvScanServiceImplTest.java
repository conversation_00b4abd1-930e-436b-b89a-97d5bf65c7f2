package com.zte.autoTest.unitTest;

import com.zte.application.impl.PmBillRcvScanServiceImpl;
import com.zte.application.scan.PmBillRcvScanService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.PmBillRcvScanDetailRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.SpmRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.scan.*;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021-12-06 17:21
 */
@PrepareForTest({BasicsettingRemoteService.class,CommonUtils.class, ServiceDataBuilderUtil.class})
public class PmBillRcvScanServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PmBillRcvScanServiceImpl pmBillRcvScanService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Mock
    private SpmRemoteService spmRemoteService;
    @Mock
    private PmBillRcvScanDetailRepository pmBillRcvScanDetailRepository;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Mock
    private HrmUserInfoService hrmUserInfoService;

    private RetCode retCode = new RetCode();

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void scanBillNo() throws Exception {

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("123");
        PowerMockito.when(BasicsettingRemoteService
                .getSysLookUpValue(Constant.lookupType.VALUE_1004081, Constant.lookupType.VALUE_1004081001))
                .thenReturn(sysLookUpValue);

        List<SpmBillDetailDTO> result = new LinkedList<>();
        SpmBillDetailDTO a1 = new SpmBillDetailDTO();
        a1.setExternalorderkey2("IMES20211203000004");
        a1.setRef45("10");
        a1.setShippedqty(new BigDecimal(10000));
        a1.setSku("013040100076");
        a1.setDescr("Ф3绿发光管");
        a1.setLottable02("220012030002");
        a1.setLottable08("30");
        result.add(a1);
        SpmBillDetailDTO a2 = new SpmBillDetailDTO();
        a2.setExternalorderkey2("IMES20211203000004");
        a2.setRef45("10");
        a2.setShippedqty(new BigDecimal(10000));
        a2.setSku("005090100001");
        a2.setDescr("八位移位寄存器");
        a2.setLottable02("220012030001");
        a2.setLottable08("30");
        result.add(a2);

        PowerMockito.when(spmRemoteService.queryBillDetailByBillNo(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(result);

        PmBillRcvScanHeadDTO pmBillRcvScanHeadDTO = new PmBillRcvScanHeadDTO();
        pmBillRcvScanHeadDTO.setRcvStatus("10");
        List<PmBillRcvScanDetailDTO> detailDTOS = new LinkedList<>();
        PmBillRcvScanDetailDTO b1 = new PmBillRcvScanDetailDTO();
        b1.setItemNo("013040100076");
        b1.setLeadFlag("30");
        b1.setProdplanId("220012030002");
        b1.setQty(new BigDecimal("10"));
        detailDTOS.add(b1);
        pmBillRcvScanHeadDTO.setDetails(detailDTOS);
        PowerMockito.when(pmBillRcvScanDetailRepository.queryHeadAndDetails(Mockito.anyString())
        ).thenReturn(pmBillRcvScanHeadDTO)
        ;

        List<SysLookupTypesDTO> envList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("30");
        sysLookupTypesDTO.setDescriptionChinV("HSF");
        envList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.ENV_LOOK_UP_TYPES))
                .thenReturn(envList);


        Assert.assertNotNull(pmBillRcvScanService.scanBillNo("123", true));
    }

    @Test
    public void refreshBillStatus() throws Exception {

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(true);
        List<PmBillRcvScanHeadDTO> heads = new LinkedList<>();
        PmBillRcvScanHeadDTO head = new PmBillRcvScanHeadDTO();
        PmBillRcvScanDetailDTO pmBillRcvScanDetailDTO = new PmBillRcvScanDetailDTO();
        pmBillRcvScanDetailDTO.setLeadFlag("30");
        pmBillRcvScanDetailDTO.setItemNo("123");
        pmBillRcvScanDetailDTO.setProdplanId("123");
        pmBillRcvScanDetailDTO.setQty(new BigDecimal(2));
        List<PmBillRcvScanDetailDTO> list = new LinkedList<>();
        list.add(pmBillRcvScanDetailDTO);
        head.setDetails(list);
        head.setRcvStatus("1");
        heads.add(head);
        PowerMockito.when(pmBillRcvScanDetailRepository.queryHeads(Mockito.any()))
                .thenReturn(heads);
        PowerMockito.when(pmBillRcvScanDetailRepository.queryHeadAndDetails(Mockito.any()))
                .thenReturn(head);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("123");
        PowerMockito.when(BasicsettingRemoteService
                .getSysLookUpValue(Constant.lookupType.VALUE_1004081, Constant.lookupType.VALUE_1004081001))
                .thenReturn(sysLookUpValue);

        List<SpmBillDetailDTO> result = new LinkedList<>();
        SpmBillDetailDTO a1 = new SpmBillDetailDTO();
        a1.setExternalorderkey2("IMES20211203000004");
        a1.setRef45("10");
        a1.setShippedqty(new BigDecimal(2));
        a1.setSku("123");
        a1.setDescr("Ф3绿发光管");
        a1.setLottable02("123");
        a1.setLottable08("30");
        a1.setStatus(0);
        result.add(a1);

        PowerMockito.when(spmRemoteService.queryBillDetailByBillNo(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(result);

        pmBillRcvScanService.refreshBillStatus("123", "52", 10, 4);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void scanSnOrPKGId() throws Exception {
        PmBillRcvVO pmBillRcvVO = new PmBillRcvVO();
        pmBillRcvVO.setBillNo("123");
        pmBillRcvVO.setBarcode("123");
        pmBillRcvVO.setBillType("20");
        pmBillRcvVO.setQty(new BigDecimal("2"));


        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.anyObject()))
                .thenReturn(true);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("123");
        PowerMockito.when(BasicsettingRemoteService
                .getSysLookUpValue(Constant.lookupType.VALUE_1004081, Constant.lookupType.VALUE_1004081001))
                .thenReturn(sysLookUpValue);

        List<SpmBillDetailDTO> result = new LinkedList<>();
        SpmBillDetailDTO a1 = new SpmBillDetailDTO();
        a1.setExternalorderkey2("IMES20211203000004");
        a1.setRef45("10");
        a1.setShippedqty(new BigDecimal(2));
        a1.setSku("123");
        a1.setDescr("Ф3绿发光管");
        a1.setLottable02("123");
        a1.setLottable08("30");
        a1.setStatus(0);
        a1.setQty(new BigDecimal(2));
        result.add(a1);

        PowerMockito.when(spmRemoteService.queryBillDetailByBillNo(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(result);

        List<PmBillRcvScanHeadDTO> heads = new LinkedList<>();
        PmBillRcvScanHeadDTO head = new PmBillRcvScanHeadDTO();
        head.setRcvStatus("1");
        PmBillRcvScanDetailDTO pmBillRcvScanDetailDTO = new PmBillRcvScanDetailDTO();
        pmBillRcvScanDetailDTO.setLeadFlag("30");
        pmBillRcvScanDetailDTO.setItemNo("123");
        pmBillRcvScanDetailDTO.setProdplanId("123");
        pmBillRcvScanDetailDTO.setQty(new BigDecimal(2));
        List<PmBillRcvScanDetailDTO> list = new LinkedList<>();
        list.add(pmBillRcvScanDetailDTO);
        head.setDetails(list);
        heads.add(head);
        PowerMockito.when(pmBillRcvScanDetailRepository.queryHeadAndDetails(Mockito.any()))
                .thenReturn(null);

        PowerMockito.when(spmRemoteService.queryBarcodeInfo(Mockito.any(), Mockito.anyString()))
                .thenReturn(a1);


        try {
            pmBillRcvScanService.scanSnOrPKGId(pmBillRcvVO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPM_BILL_SCAN_SN_LOCK_FAIL, e.getMessage());
        }
        PowerMockito.when(pmBillRcvScanDetailRepository.queryHeadAndDetails(Mockito.any()))
                .thenReturn(head);
        pmBillRcvScanService.scanSnOrPKGId(pmBillRcvVO);

        head.setRcvStatus("1");
        Assert.assertNotNull(pmBillRcvScanService.scanPkIdCheck(pmBillRcvVO));
    }


    @Test
    public void testScanBillNoByCondition() throws Exception {
        PmBillRcvScanQueryConditionDTO dto = new PmBillRcvScanQueryConditionDTO();
        dto.setFactoryId("52");
        dto.setEmpNo("123");
        Page<PmBillRcvScanDetailDTO> queryPage = new Page<>(dto.getPage(), dto.getRows());
        queryPage.setParams(dto);

        PowerMockito.when(pmBillRcvScanDetailRepository.queryScanDetailsByCondition(queryPage)).thenReturn(ScanBillNoByConditionMock());
        List<BsPubHrvOrgId> bsPubHrvOrgInfoTemp = new ArrayList<>();
        BsPubHrvOrgId a1 = new BsPubHrvOrgId();
        a1.setUserId("123");
        bsPubHrvOrgInfoTemp.add(a1);

        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyString())).thenReturn(bsPubHrvOrgInfoTemp);

        Page<PmBillRcvScanDetailDTO> result = pmBillRcvScanService.scanBillNoByCondition(dto);
        Assert.assertTrue(result.getTotalPage()>=0);
    }

    @Test
    public void testScanBillExportExcel() throws Exception {
        PmBillRcvScanQueryConditionDTO dto = new PmBillRcvScanQueryConditionDTO();
        Page<PmBillRcvScanDetailDTO> queryPage = new Page<>(dto.getPage(), dto.getRows());
        dto.setFactoryId("52");
        dto.setEmpNo("123");
        queryPage.setParams(dto);


        Page<PmBillRcvScanDetailDTO> page = new Page<>();
        page.setTotalPage(2);
        PowerMockito.whenNew(Page.class).withAnyArguments().thenReturn(page);
        PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);

        PowerMockito.when(pmBillRcvScanDetailRepository.queryScanDetailsByCondition(queryPage)).thenReturn(ScanBillNoByConditionMock());
        pmBillRcvScanService.scanBillExportExcel(dto, null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    private List<PmBillRcvScanDetailDTO> ScanBillNoByConditionMock() throws Exception {
        PmBillRcvScanDetailDTO dto = new PmBillRcvScanDetailDTO();
        List<PmBillRcvScanDetailDTO> mockList = new ArrayList<>();
        dto.setBarcode("123");
        dto.setBillNo("IMES20211203000004");
        dto.setBillType("20");
        dto.setBillTypeDesc("单板");
        dto.setRcvStatus("1");
        dto.setRcvStatusDesc("接收中");
        dto.setCreateBy("system");
        dto.setCreateDate(sdf.parse("2021-08-19 00:04:00"));
        dto.setDetailId(null);
        dto.setEnabledFlag(null);
        dto.setItemName("123");
        dto.setItemNo("013040100076");
        dto.setLastUpdatedBy(null);
        dto.setLastUpdatedDate(sdf.parse("2021-08-20 00:04:00"));
        dto.setLeadFlag(null);
        dto.setLeadFlagDesc(null);
        dto.setProdplanId("220012030002");
        dto.setQty(new BigDecimal(20000));
        mockList.add(dto);

        PmBillRcvScanDetailDTO dto1 = new PmBillRcvScanDetailDTO();
        dto1.setBarcode("1234564");
        dto1.setBillNo("IMES20211203000004");
        dto.setBillType("20");
        dto.setBillTypeDesc("单板");
        dto.setRcvStatus("1");
        dto.setRcvStatusDesc("接收中");
        dto1.setCreateBy("10275508");
        dto1.setCreateDate(sdf.parse("2021-08-16 00:04:00"));
        dto1.setDetailId(null);
        dto1.setEnabledFlag(null);
        dto1.setItemName("123456");
        dto1.setItemNo("013040100078");
        dto1.setLastUpdatedBy(null);
        dto1.setLastUpdatedDate(sdf.parse("2021-08-21 00:04:00"));
        dto1.setLeadFlag(null);
        dto1.setLeadFlagDesc(null);
        dto1.setProdplanId("220012030003");
        dto1.setQty(new BigDecimal(2));

        mockList.add(dto1);

        PmBillRcvScanDetailDTO dto2 = new PmBillRcvScanDetailDTO();
        dto2.setBarcode("ZTE0120629000014");
        dto2.setBillNo("IMES20211203000004");
        dto.setBillType("20");
        dto.setBillTypeDesc("单板");
        dto.setRcvStatus("2");
        dto.setRcvStatusDesc("接收完成");
        dto2.setCreateBy("10275508");
        dto2.setCreateDate(sdf.parse("2021-08-19 00:04:00"));
        dto2.setDetailId(null);
        dto2.setEnabledFlag(null);
        dto2.setItemName("123456");
        dto2.setItemNo("013040100078");
        dto2.setLastUpdatedBy(null);
        dto2.setLastUpdatedDate(sdf.parse("2021-08-21 00:04:00"));
        dto2.setLeadFlag(null);
        dto2.setLeadFlagDesc(null);
        dto2.setProdplanId("220012030996");
        dto2.setQty(new BigDecimal(2));

        mockList.add(dto2);

        PmBillRcvScanDetailDTO dto3 = new PmBillRcvScanDetailDTO();

        dto3.setBarcode("1234572");
        dto3.setBillNo("IMES20211203000004");
        dto.setBillType("10");
        dto.setBillTypeDesc("原材料");
        dto.setRcvStatus("2");
        dto.setRcvStatusDesc("接收完成");
        dto3.setCreateBy("00286569");
        dto3.setCreateDate(sdf.parse("2021-08-05 00:04:00"));
        dto3.setDetailId(null);
        dto3.setEnabledFlag(null);
        dto3.setItemName("123489");
        dto3.setItemNo("013040100111");
        dto3.setLastUpdatedBy(null);
        dto3.setLastUpdatedDate(sdf.parse("2021-08-17 00:04:00"));
        dto3.setLeadFlag(null);
        dto3.setLeadFlagDesc(null);
        dto3.setProdplanId("220012030007");
        dto3.setQty(new BigDecimal(2));

        mockList.add(dto3);
        return mockList;
    }

}
