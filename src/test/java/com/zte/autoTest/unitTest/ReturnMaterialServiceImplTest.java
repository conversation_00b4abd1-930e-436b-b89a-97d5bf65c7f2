package com.zte.autoTest.unitTest;

import com.zte.application.impl.BSmtBomDetailServiceImpl;
import com.zte.application.impl.ReturnMaterialServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;

/**
 * 生产退料测试
 *
 * <AUTHOR>
 */
@PrepareForTest({BSmtBomDetailServiceImpl.class, BasicsettingRemoteService.class, StringHelper.class, CrafttechRemoteService.class})
public class ReturnMaterialServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ReturnMaterialServiceImpl returnMaterialService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;


    @Test
    public void getAndCheckSn4PackingReturning() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);

        BSProcess bsProcessInfo = new BSProcess();
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(anyObject())).thenReturn(bsProcessInfo);

        List<String> sns = new ArrayList<>();
        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_NULL_VALID);
        }


        sns.add("111111");
        List<SysLookupValuesDTO> settings = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSettings(anyObject())).thenReturn(settings);
        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_RETURN_MAX_QTY)).thenReturn("0");
        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_RETURN_PROC_PACKING)).thenReturn("P123,4");
        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_LAST_PROCESS_ZP)).thenReturn("P456");
        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_RETURN_SATION)).thenReturn("S1015");

        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_QTY_OVER);
        }

        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_RETURN_MAX_QTY)).thenReturn("2");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(anyObject())).thenReturn(psWipInfos);
        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_NULL);
        }

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("111111");
        psWipInfos.add(psWipInfo);
        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_ITEMNO_IS_NULL);
        }

        psWipInfo.setItemNo("ItemNo");
        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_PROCESS_NOT_FIT);
        }

        psWipInfo.setCurrProcessCode("P123");
        psWipInfo.setLastProcess("Y");
        psWipInfo.setWorkStation("S1015");
        try {
            List<PsWipInfo> andCheckSn4PackingReturning = returnMaterialService.getAndCheckSn4PackingReturning(sns);
            Assert.assertEquals(andCheckSn4PackingReturning, psWipInfos);
        } catch (MesBusinessException e) {
        }

        PsWipInfo psWipInfo2 = new PsWipInfo();
        psWipInfo2.setSn("222222");
        psWipInfo2.setItemNo("ItemNo1");
        psWipInfos.add(psWipInfo2);

        sns.add("222222");
        try {
            returnMaterialService.getAndCheckSn4PackingReturning(sns);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExMsgId(), MessageId.SN_ITEMNO_NOT_SAME);
        }
    }
}
