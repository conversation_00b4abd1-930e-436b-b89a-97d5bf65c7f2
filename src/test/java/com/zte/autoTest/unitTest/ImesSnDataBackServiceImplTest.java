package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.IMESLogService;
import com.zte.application.impl.ImesSnDataBackServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.WipScanHisExtraInfoDTO;
import com.zte.domain.model.ImesSnDataBackModel;
import com.zte.domain.model.ImesSnDataBackRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Matchers.*;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/10/14 15:54
 */
@PrepareForTest({ConstantInterface.class, HttpRemoteUtil.class})
public class ImesSnDataBackServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private ImesSnDataBackServiceImpl service;

	@Mock
	ImesSnDataBackRepository backRepository;
	@Mock
	private IMESLogService imesLogService;
	@Mock
	private WipScanHisExtraRepository wipScanHisExtraRepository;
	@Mock
	private DatawbRemoteService datawbRemoteService;
	@Value("${imuId.or.workStation:imuId}")
	private String imuIdOrWorkStation;


	@Test
	public void process() {
		PowerMockito.when(backRepository.getSnDataByPage(any()))
				.thenReturn(new ArrayList<>());
		service.process(Maps.newHashMap());
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void dataBack() throws Exception {
		PowerMockito.mockStatic(ConstantInterface.class);
		PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/databack/flow/add");
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
				JSON.toJSONString(new ServiceData() {{
					setBo(Lists.newArrayList(""));
				}})
		);
		List<ImesSnDataBackModel> imesSnDataBackModelList=new ArrayList<>();
		ImesSnDataBackModel imesSnDataBackModel=new ImesSnDataBackModel();
		imesSnDataBackModel.setBoardSn("1");
		imesSnDataBackModel.setProdplanId(7778885);
		imesSnDataBackModelList.add(imesSnDataBackModel);
		Assert.assertEquals("1", imesSnDataBackModel.getBoardSn());
		service.dataBack(imesSnDataBackModelList);
	}

	@Test
	public void syncSnData() throws Exception {
		int preDays = 0;
		int size = 500;
		List<WipScanHisExtraInfoDTO> extraList = new ArrayList<>();
		WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
		dto.setSn("111");
		dto.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"70088860004\",\"imuId\":\"111\"}");
		dto1.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"111\"}");
		extraList.add(dto);
		PowerMockito.when(wipScanHisExtraRepository.getScanHistoryExtraByPage(size, new Date())).thenReturn(extraList);
		PowerMockito.when(datawbRemoteService.snDataBack(extraList)).thenReturn(1);
		PowerMockito.when(wipScanHisExtraRepository.updateByIdBatch(extraList,new Date())).thenReturn(1);
		PowerMockito.when(wipScanHisExtraRepository.updateIgnoreInfoBatch(extraList)).thenReturn(1);
		Assert.assertEquals("111", dto.getSn());
		service.syncSnData(preDays,size);
	}

	@Test
	public void syncSnData1() throws Exception {
		int preDays = 0;
		int size = 500;
		List<WipScanHisExtraInfoDTO> extraList = new ArrayList<>();
		WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto2 = new WipScanHisExtraInfoDTO();
		dto.setSn("111");
		dto.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"70088860004\",\"imuId\":\"111\"}");
		dto1.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"111\"}");
		extraList.add(dto);
		extraList.add(dto1);
		extraList.add(dto2);
		PowerMockito.when(wipScanHisExtraRepository.getScanHistoryExtraByPage(size,new Date())).thenReturn(extraList);
		PowerMockito.when(datawbRemoteService.snDataBack(extraList)).thenReturn(1);
		PowerMockito.when(wipScanHisExtraRepository.updateByIdBatch(extraList,new Date())).thenReturn(1);
		PowerMockito.when(wipScanHisExtraRepository.updateIgnoreInfoBatch(extraList)).thenReturn(1);
		ReflectionTestUtils.setField(service, "imuIdOrWorkStation", "imuId");
		Assert.assertEquals("111", dto.getSn());
		service.syncSnData(preDays,size);
	}

	@Test
	public void checkSnInfo() throws Exception {
		List<WipScanHisExtraInfoDTO> extraList = new ArrayList<>();
		List<WipScanHisExtraInfoDTO> ignoreList = new ArrayList<>();
		List<WipScanHisExtraInfoDTO> infoList = new ArrayList<>();
		WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto2 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto3 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto4 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto5 = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto6 = new WipScanHisExtraInfoDTO();
		dto1.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"imuId\":\"111\"}");
		dto2.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"70088860004\",\"imuId\":\"111\"}");
		dto3.setExtraData("{\"prodplanId\":\"Ttttttt\",\"sn\":\"************\",\"imuId\":\"111\"}");
		dto4.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"77777010004\",\"imuId\":\"111\"}");
		dto5.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"tttttttttttt\",\"imuId\":\"111\"}");
		dto6.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"77777010004\",\"imuId\":null}");
		extraList.add(dto);
		extraList.add(dto1);
		extraList.add(dto2);
		extraList.add(dto3);
		extraList.add(dto4);
		extraList.add(dto5);
		extraList.add(dto6);
		ReflectionTestUtils.setField(service, "imuIdOrWorkStation", "imuId");
		service.checkSnInfo(extraList,ignoreList,infoList);
		dto1.setExtraData("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"workStation\":\"null\"}");
		dto2.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"70088860004\",\"workStation\":\"111\"}");
		dto3.setExtraData("{\"prodplanId\":\"7777700001\",\"sn\":\"70088860004\",\"workStation\":\"0\"}");
		ReflectionTestUtils.setField(service, "imuIdOrWorkStation", "workStation");
		service.checkSnInfo(extraList,ignoreList,infoList);
		Assert.assertEquals("{\"prodplanId\":\"7777701\",\"sn\":\"************\",\"workStation\":\"null\"}", dto1.getExtraData());
		service.checkSnInfo(extraList,ignoreList,infoList);
	}

	@Test
	public void snDataBack() throws Exception {
		List<WipScanHisExtraInfoDTO> infoList = new ArrayList<>();
		WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
		dto.setCreateDate(new Date());
		service.snDataBack(infoList);
		PowerMockito.when(datawbRemoteService.snDataBack(infoList)).thenReturn(2);
		infoList.add(dto);
		service.snDataBack(infoList);
		infoList.clear();
		infoList.add(dto1);
		service.snDataBack(infoList);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void updateErrMsg() throws Exception {
		List<WipScanHisExtraInfoDTO> infoList = new ArrayList<>();
		WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
		WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
		String errMsg = "777";
		dto1.setExtraValue("N");
		dto.setExtraValue("1");
		infoList.add(dto);
		infoList.add(dto1);
		PowerMockito.when(wipScanHisExtraRepository.updateErrMsgByIdBatch(infoList)).thenReturn(2);
		Assert.assertEquals("777", errMsg);
		service.updateErrMsg(infoList,errMsg);
	}
}
