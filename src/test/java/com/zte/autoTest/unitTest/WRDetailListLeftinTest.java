package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.AssemblyResultServiceImpl;
import com.zte.application.impl.strategy.WRDetailListLeftin;
import com.zte.domain.model.AssemblyResultHisRepository;
import com.zte.domain.model.AssemblyResultRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.interfaces.dto.PsTaskInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.WipDailyReportInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class})
public class WRDetailListLeftinTest extends PowerBaseTestCase {

    @InjectMocks
    private WRDetailListLeftin wrDetailListLeftin;

    @Test
    public void updateForCenter()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        Map requestMap = new HashMap();
        WipDailyReportInfoDTO wipDailyReportInfoDTO = new WipDailyReportInfoDTO();
        wipDailyReportInfoDTO.setProdplanId("7778889");
        requestMap.put("record",wipDailyReportInfoDTO);

        List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
        PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
        workOrderList.add(workOrder);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(workOrderList);}}));
        wrDetailListLeftin.getDetailList(requestMap);
        wipDailyReportInfoDTO.setTaskQty(5L);
        Assert.assertNull(wrDetailListLeftin.getDetailList(requestMap));
    }
}
