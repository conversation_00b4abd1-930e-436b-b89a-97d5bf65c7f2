package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.BarcodeLockHeadServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BarcodeLockHead;
import com.zte.domain.model.BarcodeLockHeadRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.BarcodeLockExportDTO;
import com.zte.interfaces.dto.BarcodeLockHeadEntityDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class,
        HttpRemoteUtil.class, HttpRemoteService.class, RedisHelper.class})
public class BarcodeLockHeadServiceImplTest extends PowerBaseTestCase {

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private BarcodeLockHeadRepository barcodeLockHeadRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private HttpServletResponse httpServletResponse;

    @InjectMocks
    private BarcodeLockHeadServiceImpl barcodeLockHeadService;
    @Test
    public void batchInsert() throws Exception {
        List<BarcodeLockHeadEntityDTO> billNoList = new ArrayList<>();
        barcodeLockHeadService.batchInsert(billNoList);
        billNoList.add(new BarcodeLockHeadEntityDTO());
        barcodeLockHeadService.batchInsert(billNoList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void getHeadInfoByBillNoList() throws Exception {
        List<String> billNoList = new ArrayList<>();
        barcodeLockHeadService.getHeadInfoByBillNoList(billNoList);
        billNoList.add("2");
        barcodeLockHeadService.getHeadInfoByBillNoList(billNoList);
        List<BarcodeLockHeadEntityDTO> tempList = new ArrayList<>();
        tempList.add(new BarcodeLockHeadEntityDTO());
        PowerMockito.when(barcodeLockHeadRepository.getHeadInfoByBillNoList(any())).thenReturn(tempList);
        Assert.assertNotNull(barcodeLockHeadService.getHeadInfoByBillNoList(billNoList));
    }
    @Test
    public void queryLockInfoByProdPlanId() throws Exception {
        Assert.assertNotNull(barcodeLockHeadService.queryLockInfoByProdPlanId(""));
    }
    @Test
    public void deleteSpmBarcodeLockData() throws Exception {
        Assert.assertNotNull(barcodeLockHeadService.deleteSpmBarcodeLockData());
    }
    @Test
    public void saveSPMLockData() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        List<CtRouteDetailDTO> ctRouteDetailDTOList  = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("341");
        ctRouteDetailDTO.setProcessCode("S");
        ctRouteDetailDTOList.add(ctRouteDetailDTO);
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(ctRouteDetailDTOList);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(serviceData));
        List<QaKxonlineBomlockedEntityDTO> qaKxonlineBomlockedEntityDTOList = JSON.parseArray("[{\"bomid\":2450873,\"bomlockid\":\"6A670D9DE0785DB9E04400144F1EB62C\",\"detailList\":[{\"bomlockid\":\"6A670D9DE0785DB9E04400144F1EB62C\",\"imuid\":341,\"imulockdetailid\":\"6A670D9DE0795DB9E04400144F1EB62C\",\"imuname\":\"联机测试\",\"locker\":\"卢筛嫦00025881\",\"locktime\":1242886454000,\"page\":0,\"rows\":0,\"state\":0,\"unlocker\":\"卢筛嫦00025881\",\"unlockremark\":\"解锁\",\"unlocktime\":1245834936000}],\"factoryId\":\"55\",\"page\":0,\"problemdesc\":\"因032010600026代码电源模块使用了014030100064代码INFINEON品牌物料存在质量隐患，现在电源平台正在组织分析中，要求隔离以下批次的单板。\",\"rows\":0,\"sender\":\"孙虎30004800\"}]",QaKxonlineBomlockedEntityDTO.class);
        barcodeLockHeadService.saveSPMLockData(qaKxonlineBomlockedEntityDTOList,"55");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void exportExcel() throws Exception {
        List<BarcodeLockExportDTO> barcodeLockHeadlist = new ArrayList<>();
        BarcodeLockExportDTO barcodeLockHead = new BarcodeLockExportDTO();
        barcodeLockHead.setCurrProcessCode("1");
        barcodeLockHead.setLockProcessCode("1");
        barcodeLockHead.setPage(1);
        barcodeLockHead.setCreateBy("1027");
        barcodeLockHead.setCcList("1027,1025");
        barcodeLockHead.setRows(10);
        barcodeLockHeadlist.add(barcodeLockHead);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockHeadEntityDTO();
        barcodeLockHeadEntityDTO.setSn("77788890001");
        barcodeLockHeadEntityDTO.setPage(1);
        barcodeLockHeadEntityDTO.setRows(10);
        barcodeLockHeadEntityDTO.setStartDate("2022-04-23 23:59:59");
        barcodeLockHeadEntityDTO.setEndDate("2022-04-24 23:59:59");
//        PowerMockito.when(barcodeLockHeadRepository.exportExcel(barcodeLockHeadEntityDTO)).thenReturn(barcodeLockHeadlist);
        try {
            barcodeLockHeadService.exportExcel(httpServletResponse, barcodeLockHeadEntityDTO);
        }catch (Exception e){String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);}
    }

    @Test
    public void delete() throws Exception {
        BarcodeLockHeadEntityDTO barcodeLockHead = new BarcodeLockHeadEntityDTO();
        barcodeLockHead.setSn("77788890001");
        barcodeLockHead.setBillNo("11");
        barcodeLockHead.setCreateBy("1027");
        barcodeLockHead.setCcList("1027,1025");
        barcodeLockHead.setRows(10);
        barcodeLockHead.setStartDate("2022-04-23 23:59:59");
        barcodeLockHead.setEndDate("2022-04-24 23:59:59");
        Assert.assertNotNull(barcodeLockHeadService.delete(barcodeLockHead));
    }

    @Test
    public void save() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadlist = new ArrayList<>();
        BarcodeLockHeadEntityDTO barcodeLockHead = new BarcodeLockHeadEntityDTO();
        barcodeLockHead.setSn("77788890001");
        barcodeLockHead.setPage(1);
        barcodeLockHead.setCreateBy("1027");
        barcodeLockHead.setCcList("1027,1025");
        barcodeLockHead.setRows(10);
        barcodeLockHead.setStartDate("2022-04-23 23:59:59");
        barcodeLockHead.setEndDate("2022-04-24 23:59:59");
        barcodeLockHeadlist.add(barcodeLockHead);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        BarcodeLockHead barcodeLock = new BarcodeLockHead();
        barcodeLock.setStatus(Constant.IN_PREPARATION);
        PowerMockito.when(barcodeLockHeadRepository.getHeadInfoByBillNo(anyObject())).thenReturn(barcodeLock);
        PowerMockito.when(barcodeLockHeadRepository.pageList(anyObject())).thenReturn(barcodeLockHeadlist);
        BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockHeadEntityDTO();
        barcodeLockHeadEntityDTO.setSn("77788890001");
        barcodeLockHeadEntityDTO.setBillNo("billNo");
        barcodeLockHeadEntityDTO.setType("条码锁定");
        barcodeLockHeadEntityDTO.setReason("条码锁定");
        barcodeLockHeadEntityDTO.setCcList("102746,145556");
        barcodeLockHeadEntityDTO.setPage(1);
        barcodeLockHeadEntityDTO.setRows(10);
        barcodeLockHeadEntityDTO.setStartDate("2022-04-23 23:59:59");
        barcodeLockHeadEntityDTO.setEndDate("2022-04-24 23:59:59");

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        barcodeLockHeadEntityDTO.setBarcodeLockDetailEntityDTOList(barcodeLockDetailEntityDTOList);
        barcodeLockHeadEntityDTO.setOperationType("save");
        barcodeLockHeadService.save(barcodeLockHeadEntityDTO);
        barcodeLockHeadEntityDTO.setOperationType("submit");
        barcodeLockHeadEntityDTO.setId("submit");
        Assert.assertNotNull(barcodeLockHeadService.save(barcodeLockHeadEntityDTO));
    }

    @Test
   public void pageList() throws Exception {
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadlist = new ArrayList<>();
        BarcodeLockHeadEntityDTO barcodeLockHead = new BarcodeLockHeadEntityDTO();
        barcodeLockHead.setSn("77788890001");
        barcodeLockHead.setPage(1);
        barcodeLockHead.setCreateBy("1027");
        barcodeLockHead.setCcList("1027,1025");
        barcodeLockHead.setRows(10);
        barcodeLockHead.setStartDate("2022-04-23 23:59:59");
        barcodeLockHead.setEndDate("2022-04-24 23:59:59");
        barcodeLockHeadlist.add(barcodeLockHead);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(barcodeLockHeadRepository.pageList(anyObject())).thenReturn(barcodeLockHeadlist);
        BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO = new BarcodeLockHeadEntityDTO();
        barcodeLockHeadEntityDTO.setSn("77788890001");
        barcodeLockHeadEntityDTO.setPage(1);
        barcodeLockHeadEntityDTO.setRows(10);
        barcodeLockHeadEntityDTO.setStartDate("2022-04-23 23:59:59");
        barcodeLockHeadEntityDTO.setEndDate("2022-04-24 23:59:59");
        Assert.assertNotNull(barcodeLockHeadService.pageList(barcodeLockHeadEntityDTO));
    }

    @Test
    public void getLockBillNoAndReasonByProdPlanIdAndProcessCodesTest() throws Exception {
        BarcodeLockDetailEntityDTO dto = new BarcodeLockDetailEntityDTO();
        barcodeLockHeadService.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(null);
        try{
            barcodeLockHeadService.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(dto);
        }catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.LOCK_BARCODE_PRODPLANID_ID_NULL));
        }
        dto.setBatchSn("123123213");
        try{
            barcodeLockHeadService.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(dto);
        }catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.IN_PROCESS_CODE_NOT_EXIST));
        }
        List<String> processCodeLockList = new ArrayList<>();
        processCodeLockList.add("232313");
        dto.setProcessCodeLockList(processCodeLockList);
        List<BarcodeLockHead> list = new ArrayList<>();
        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setCreateBy("10001");
        list.add(barcodeLockHead);
        PowerMockito.when(barcodeLockHeadRepository.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(anyString(),anyList())).thenReturn(list);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("沐凌雪");
        hrmPersonInfoDTOMap.put("10001", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        barcodeLockHeadService.getLockBillNoAndReasonByProdPlanIdAndProcessCodes(dto);

    }

}
