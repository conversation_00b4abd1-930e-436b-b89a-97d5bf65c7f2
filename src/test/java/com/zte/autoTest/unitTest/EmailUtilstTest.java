package com.zte.autoTest.unitTest;

import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2021-05-19 15:10
 */
@PrepareForTest({BasicsettingRemoteService.class})
public class EmailUtilstTest extends PowerBaseTestCase {
    @InjectMocks
    private EmailUtils emailUtils;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
    }

    @Test
    public void sendMail() {
//        emailUtils.sendMail("123", "123", "123", "123", "123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }
}
