package com.zte.autoTest.unitTest;

import com.zte.application.impl.ImesSnDataBackServiceImpl;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.WipScanHisExtraInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3 10:37
 */
public class SnInfoBackToStepTest extends PowerBaseTestCase {

    @InjectMocks
    private ImesSnDataBackServiceImpl service;
    @Autowired
    private WipScanHisExtraRepository wipScanHisExtraRepository;

    @Test
    public void snDataBack() throws Exception {
        List<WipScanHisExtraInfoDTO> infoList = new ArrayList<>();
        service.snDataBack(infoList);
        WipScanHisExtraInfoDTO dto = new WipScanHisExtraInfoDTO();
        WipScanHisExtraInfoDTO dto1 = new WipScanHisExtraInfoDTO();
        dto1.setExtraValue("N");
        dto.setExtraValue("1");
        infoList.add(dto);
        infoList.add(dto1);
        try {
            service.snDataBack(infoList);
        }catch (Exception e){
            Assert.assertEquals("2", dto.getExtraValue());
        }
    }
}
