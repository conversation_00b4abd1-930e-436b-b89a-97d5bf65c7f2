package com.zte.autoTest.unitTest.warehouse;

import com.zte.application.impl.warehouse.WarehouseSubmitStockOutSourceServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.GenerateSNUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.warehouse.WarehouseStockInfoDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2022-12-30 16:58
 */
@PrepareForTest({PlanscheduleRemoteService.class, CrafttechRemoteService.class, DatawbRemoteService.class, GenerateSNUtil.class,
        BeanUtils.class, BasicsettingRemoteService.class})
public class WarehouseSubmitStockOutSourceServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WarehouseSubmitStockOutSourceServiceImpl warehouseSubmitStockOutSourceServiceImpl;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(GenerateSNUtil.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(BeanUtils.class);

    }

    @Test
    public void saveWarehouseEntryInfo() throws Exception {

        List<PsTask> psTaskList = new LinkedList<>();
        PsTask t1 = new PsTask();
        t1.setPartsPlanno("45");
        psTaskList.add(t1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.anyMap()))
                .thenReturn(psTaskList)
        ;

        WarehouseStockInfoDTO warehouseEntryInfoQueryDTO = new WarehouseStockInfoDTO();
        warehouseEntryInfoQueryDTO.setCommitedQty(new BigDecimal(1));
        warehouseEntryInfoQueryDTO.setFactoryId("52");
        warehouseEntryInfoQueryDTO.setProdplanNo("123");
        warehouseEntryInfoQueryDTO.setProdplanId("8899737");
        warehouseEntryInfoQueryDTO.setTaskNo("889958-1");
        warehouseEntryInfoQueryDTO.setOrgId(new BigDecimal("53"));
        warehouseEntryInfoQueryDTO.setCommitedQty(new BigDecimal(1));
        List<PsWorkOrderDTO> orderList = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        a1.setTaskQty(new BigDecimal(20));
        orderList.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(Mockito.anyString())).thenReturn(orderList)
        ;

        List<CtRouteDetailDTO> detail = new LinkedList<>();
        CtRouteDetailDTO cr1 = new CtRouteDetailDTO();
        cr1.setNextProcess("P02040");
        detail.add(cr1);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Mockito.anyList()))
                .thenReturn(detail)
        ;
        PowerMockito.when(CrafttechRemoteService.routeContainProcess(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(Arrays.asList("123"));

        List<BSProcess> processList = new LinkedList<>();
        BSProcess b1 = new BSProcess();
        b1.setProcessCode("P02040");
        processList.add(b1);
        PowerMockito.when(CrafttechRemoteService.getProcess(Mockito.any()))
                .thenReturn(processList)
        ;

        List<BBomHeaderDTO> headerDTOList = new LinkedList<>();
        BBomHeaderDTO c1 = new BBomHeaderDTO();
        c1.setZjSubcardFlag("N");
        headerDTOList.add(c1);
        PowerMockito.when(centerfactoryRemoteService.queryBomHeadByProductCode(Mockito.any()))
                .thenReturn(headerDTOList)
        ;

        List<CFFactory> factoryList = new LinkedList<>();
        CFFactory d1 = new CFFactory();
        d1.setOrgId(53L);
        d1.setErpCode("erpCode");
        factoryList.add(d1);
        PowerMockito.when(BasicsettingRemoteService.getFactory(Mockito.anyMap())).thenReturn(factoryList)
        ;

        List<SysLookupTypesDTO> subLookupTypes = new LinkedList<>();
        SysLookupTypesDTO d2 = new SysLookupTypesDTO();
        d2.setLookupMeaning("53");
        d2.setAttribute1("53");
        subLookupTypes.add(d2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap()))
                .thenReturn(subLookupTypes)
        ;
        PowerMockito.when(BasicsettingRemoteService.getFactoryByFactoryCode(Mockito.any()))
                .thenReturn(factoryList);
        Page<BoardOnline> boardOnlinePage = new Page<>();
        boardOnlinePage.setTotal(100000);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.anyObject()))
                .thenReturn(boardOnlinePage)
        ;
        warehouseSubmitStockOutSourceServiceImpl.saveWarehouseEntryInfo(warehouseEntryInfoQueryDTO);

        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.anyObject()))
                .thenReturn(new Page<>())
        ;
        try {
            warehouseSubmitStockOutSourceServiceImpl.saveWarehouseEntryInfo(warehouseEntryInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SUBMIT_OUTSOURCE_QTY_SUB.equals(e.getMessage()));
        }
    }


    @Test
    public void addDetail() throws Exception {
        WarehouseStockInfoDTO dto = new WarehouseStockInfoDTO();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        BigDecimal committedQty = BigDecimal.TEN;
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        dto.setCommitedQty(BigDecimal.TEN);
        dto.setProdplanId("ProdplanId");
        dto.setTaskNo("TaskNo-1");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString()))
                .thenReturn(null);
        PowerMockito.when(GenerateSNUtil.generateSN("ProdplanId", 11, 20))
                .thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(warehouseSubmitStockOutSourceServiceImpl, "addDetail",
                dto, psWorkOrderDTO, committedQty, warehouseEntryInfo);
        Assert.assertEquals("TaskNo", warehouseEntryInfo.getParentProdplanNo());

        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTasks.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString()))
                .thenReturn(psTasks);
        Whitebox.invokeMethod(warehouseSubmitStockOutSourceServiceImpl, "addDetail",
                dto, psWorkOrderDTO, committedQty, warehouseEntryInfo);
        Assert.assertEquals("TaskNo", warehouseEntryInfo.getParentProdplanNo());


        psTask.setZbjprodplanNo("ZbjprodplanNo");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString()))
                .thenReturn(psTasks);
        Whitebox.invokeMethod(warehouseSubmitStockOutSourceServiceImpl, "addDetail",
                dto, psWorkOrderDTO, committedQty, warehouseEntryInfo);
        Assert.assertEquals("ZbjprodplanNo", warehouseEntryInfo.getParentProdplanNo());
    }

    @Test
    public void dealBillTypeList() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("4");
        list.add("10");
        Assert.assertNotNull(list);
    }
}
