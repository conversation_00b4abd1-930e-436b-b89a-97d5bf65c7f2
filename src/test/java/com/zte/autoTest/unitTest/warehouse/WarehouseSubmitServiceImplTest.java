/*Started by AICoder, pid:8024e22f83c54d0a909c8a667f8bf8aa*/
package com.zte.autoTest.unitTest.warehouse;

import com.google.common.collect.Lists;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.application.impl.warehouse.WarehouseSubmitServiceImpl;
import com.zte.application.warehouse.WarehouseSubmitService;
import com.zte.application.warehouse.WarehouseSubmitStockService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.domain.model.BoardOnline;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WarehouseEntryDetailDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.interfaces.dto.warehouse.WarehouseBarcodeQueryDTO;
import com.zte.interfaces.dto.warehouse.WarehouseStockInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.CommonRedisUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 * @date 2022-12-29 15:12
 */
@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class,ProductionDeliveryRemoteService.class, DatawbRemoteService.class, MesBusinessException.class, CommonUtils.class, StringUtils.class})
public class WarehouseSubmitServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WarehouseSubmitServiceImpl warehouseSubmitServiceImpl;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock DatawbRemoteService datawbRemoteService;

    @Mock
    private PsWipInfoServiceImpl psWipInfoService;
    @Mock
    private List<WarehouseSubmitStockService> warehouseSubmitStockServiceList;
    @Mock
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;

    @Mock
    ErpRemoteService erpRemoteService;

    @Mock
    private CommonRedisUtil commonRedisUtil;
    @Mock
    private WarehouseSubmitService warehouseSubmitService;

    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MesBusinessException.class);
    }

    @Test
    public void queryWarehouseBarcode() throws Exception {
        WarehouseBarcodeQueryDTO queryDTO = new WarehouseBarcodeQueryDTO();
        queryDTO.setProdplanId("8899720");

        List<PsTask> psTaskList = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setTaskId(UUID.randomUUID().toString());
        a1.setTaskQty(new BigDecimal(2));
        psTaskList.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.any()))
                .thenReturn(psTaskList)
        ;
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.anyList(),
                Mockito.anyList())).thenReturn(new WarehouseEntryInfoDTO());

        Assert.assertEquals("8899720", queryDTO.getProdplanId());
        warehouseSubmitServiceImpl.queryWarehouseBarcode(queryDTO);
    }

    @Test
    public void saveWarehouseEntryInfo() throws Exception {
        WarehouseStockInfoDTO warehouseEntryInfoQueryDTO = new WarehouseStockInfoDTO();
        warehouseEntryInfoQueryDTO.setBillType(Constant.STR_10);
        try {
            warehouseSubmitServiceImpl.saveWarehouseEntryInfo(warehouseEntryInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals("10", warehouseEntryInfoQueryDTO.getBillType());
        }
    }

    @Test(timeout = 8000)
    public void boardCodeInStock_Success() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setErpErrorInfo("");
        dto.setBillType("9");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setLpn("LPN1");
        detailDTO.setSn("****************");
        detailDTO.setWorkOrderNo("3444");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        try {
            warehouseSubmitServiceImpl.boardCodeInStock(dto);
        }catch (Exception e) {
            Assert.assertEquals("10", dto.getBillType());
        }

        dto.setBillType("5");
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        try {
            warehouseSubmitServiceImpl.boardCodeInStock(dto);
        }catch (Exception e) {
            Assert.assertEquals("10", dto.getBillType());
        }

        dto.setProdplanId("9");
        PowerMockito.when(warehouseEntryDetailRepository.getDetailByBillNoList(Mockito.any()))
                .thenReturn(detailDTOList);
        datawbRemoteService.updateBoardOnline(Mockito.any());
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        Assert.assertTrue(true);

        dto.setBillType("10");
        PowerMockito.when(warehouseEntryDetailRepository.getDetailByBillNoList(Mockito.any()))
                .thenReturn(detailDTOList);
        datawbRemoteService.updateBoardOnline(Mockito.any());
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        Assert.assertTrue(true);

        PowerMockito.when(warehouseEntryDetailRepository.getDetailByBillNoList(Mockito.any()))
                .thenReturn(detailDTOList);
        datawbRemoteService.updateBoardOnline(Mockito.any());
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any()))
                .thenReturn(null);
        String routeId="";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        try {
            warehouseSubmitServiceImpl.boardCodeInStock(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LAST_OPERATION_NOT_FOUND, e.getMessage());
        }

        routeId="23444";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        try {
            warehouseSubmitServiceImpl.boardCodeInStock(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LAST_OPERATION_NOT_FOUND, e.getMessage());
        }

        PowerMockito.when(warehouseEntryDetailRepository.getDetailByBillNoList(Mockito.any()))
                .thenReturn(detailDTOList);
        datawbRemoteService.updateBoardOnline(Mockito.any());
        CtRouteDetail lastProcess=new CtRouteDetail();
        lastProcess.setRouteDetailId("324332");
        lastProcess.setNextProcess("1");
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any()))
                .thenReturn(lastProcess);
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        Assert.assertTrue(true);

        PowerMockito.when(warehouseEntryDetailRepository.getDetailByBillNoList(Mockito.any()))
                .thenReturn(detailDTOList);
        datawbRemoteService.updateBoardOnline(Mockito.any());
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any()))
                .thenReturn(lastProcess);
        List<PsWipInfo> listPsWipInfo=new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setCurrProcessCode("1");
        psWipInfo.setSn("1");
        listPsWipInfo.add(psWipInfo);
        PsWipInfo psWipInfo1=new PsWipInfo();
        psWipInfo1.setCurrProcessCode("1");
        psWipInfo1.setSn("1");
        listPsWipInfo.add(psWipInfo1);
        warehouseEntryInfoService.batchInsertPsScanHistory(Mockito.any());
        warehouseSubmitServiceImpl.boardCodeInStock(dto);
        Assert.assertTrue(true);
    }

    @Test(timeout = 8000)
    public void boardCodeInStock_WithError() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setErpErrorInfo("Error");
        dto.setBillType("9");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setLpn("LPN1");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        try {
            warehouseSubmitServiceImpl.boardCodeInStock(dto);
        }catch (Exception e) {
            Assert.assertEquals("10", dto.getBillType());
        }
    }

    @Test(timeout = 8000)
    public void testGetErpUrl_Success() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        String lookupCode = String.valueOf(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        try {
            Mockito.when(
                            BasicsettingRemoteService.getSysLookUpValue(
                                    MpConstant.LOOKUP_TYPE_ERP_WS, lookupCode))
                    .thenReturn(sysLookupTypesDTO);
            String erpUrl = warehouseSubmitServiceImpl.getErpUrl();
            assertEquals("http://localhost:8080", erpUrl);
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }
    }

    @Test(timeout = 8000, expected = Exception.class)
    public void testGetErpUrl_NullSetting() throws Exception {
        try {
            Mockito.when(
                            BasicsettingRemoteService.getSysLookUpValue(
                                    MpConstant.LOOKUP_TYPE_ERP_WS,
                                    String.valueOf(MpConstant.LOOKUP_CODE_ERP_WS_TWO)))
                    .thenReturn(null);
            warehouseSubmitServiceImpl.getErpUrl();
        } catch (Exception e) {
            throw e;
        }
    }

    @Test(timeout = 8000)
    public void towToErp_NonDirectOutbound_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("2");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any()))
                .thenReturn(null);
        try {
            warehouseSubmitServiceImpl.towToErp(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void towToErp_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("2");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(2));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any()))
                .thenReturn(null);
        try {
            warehouseSubmitServiceImpl.towToErp(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void towToErp_DirectOutbound_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("3");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any()))
                .thenReturn(null);
        try {
            warehouseSubmitServiceImpl.towToErp(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void towToErp_InterOrganizationTransfer_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("2");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any()))
                .thenReturn(null);
        try {
            warehouseSubmitServiceImpl.towToErp(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void towToErp_WithErpErrorInfo_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("2");
        dto.setErpErrorInfo("Error");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        dto.setOutboundType("2");
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.towToErp(dto);
        Assert.assertNotNull(dto);

        dto.setErpErrorInfo("");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.towToErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test(timeout = 8000)
    public void testThreeToErp_OutboundTypeIs2() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("2");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.threeToErp(dto);
        Assert.assertNotNull(dto);

        dto.setErpErrorInfo("Error");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.threeToErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test(timeout = 8000)
    public void testThreeToErp_ErpErrorInfoIsNotEmpty() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("5");
        warehouseSubmitServiceImpl.threeToErp(dto);
        Assert.assertNotNull(dto);
        dto.setOutboundType("3");
        dto.setErpErrorInfo("Error");
        warehouseSubmitServiceImpl.threeToErp(dto);
        Assert.assertNotNull(dto);

        dto.setBillType("5");
        warehouseSubmitServiceImpl.threeToErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test(timeout = 8000)
    public void testUpdateFirstWarehouseDate_Null() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseSubmitServiceImpl.updateFirstWarehouseDate(warehouseEntryInfo);
        Assert.assertNotNull(warehouseEntryInfo);
    }

    @Test(timeout = 8000)
    public void testUpdateFirstWarehouseDate_NotNull() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setProdplanId("123");
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(6031));
        sysLookupTypesDTO.setLookupMeaning("Y");
        sysLookupTypesDTO.setLookupCode(new BigDecimal(6031001));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        long count = 0;
        PowerMockito.when(psWipInfoService.getCount(Mockito.any())).thenReturn(count);
        warehouseSubmitServiceImpl.updateFirstWarehouseDate(warehouseEntryInfo);
        Assert.assertNotNull(warehouseEntryInfo);
        count = 1;
        PowerMockito.when(psWipInfoService.getCount(Mockito.any())).thenReturn(count);
        warehouseSubmitServiceImpl.updateFirstWarehouseDate(warehouseEntryInfo);
        Assert.assertNotNull(warehouseEntryInfo);
    }

    @Test(timeout = 8000)
    public void testGetTransactiontypeId_OutboundTypeIs1() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOutboundType("1");
        String result = warehouseSubmitServiceImpl.getTransactiontypeId(warehouseEntryInfo);
        assertEquals("44,31", result);
    }

    @Test(timeout = 8000)
    public void testGetTransactiontypeId_OutboundTypeIsNot1AndOrgIdEqualsSourceOrgId() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOutboundType("2");
        warehouseEntryInfo.setOrgId(new BigDecimal("1"));
        warehouseEntryInfo.setSourceOrgId(new BigDecimal("1"));
        String result = warehouseSubmitServiceImpl.getTransactiontypeId(warehouseEntryInfo);
        assertEquals("44,2,31", result);
    }

    @Test(timeout = 8000)
    public void testGetTransactiontypeId_OutboundTypeIsNot1AndOrgIdNotEqualsSourceOrgId() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOutboundType("2");
        warehouseEntryInfo.setOrgId(new BigDecimal("2"));
        warehouseEntryInfo.setSourceOrgId(new BigDecimal("1"));
        String result = warehouseSubmitServiceImpl.getTransactiontypeId(warehouseEntryInfo);
        assertEquals("44,3,31", result);
    }

    @Test(timeout = 8000)
    public void testGetTransactiontypeIdTest() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOutboundType("3");
        warehouseEntryInfo.setOrgId(new BigDecimal("2"));
        warehouseEntryInfo.setSourceOrgId(new BigDecimal("1"));
        String result = warehouseSubmitServiceImpl.getTransactiontypeId(warehouseEntryInfo);
        assertEquals("44,3", result);
    }

    @Test(timeout = 8000)
    public void testGetTransactiontypeId_OutboundTypeIs2AndOrgIdEqualsSourceOrgId() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setOutboundType("2");
        warehouseEntryInfo.setOrgId(new BigDecimal("1"));
        warehouseEntryInfo.setSourceOrgId(new BigDecimal("1"));
        String result = warehouseSubmitServiceImpl.getTransactiontypeId(warehouseEntryInfo);
        assertEquals("44,2,31", result);
    }

    @Test(timeout = 8000)
    public void testCheckLastProcess_NonRepair() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillType("1");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setWorkOrderNo("123");
        detailDTO.setSn("***********");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        String routeId="";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        try {
            warehouseSubmitServiceImpl.checkLastProcess(dto);
            fail("Should have thrown a MesBusinessException");
        } catch (Exception e) {
            assertEquals("last.operation.not.found", e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void testCheckLastProcess_Repair() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillType("9");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setWorkOrderNo("123");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        assertNotNull(warehouseSubmitServiceImpl.checkLastProcess(dto));
    }

    @Test(timeout = 8000)
    public void checkBoardOnlineNumber_Success() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setProdplanId("1");
        dto.setBillType("2");
        dto.setCommitedQty(new BigDecimal("3"));
        WarehouseEntryInfoDTO countByCondition =new WarehouseEntryInfoDTO();
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.any(), Mockito.any())) .thenReturn(null);
        Page<BoardOnline> boardOnlinePage=new Page<>();
        BoardOnline boardOnline=new BoardOnline();
        boardOnline.setBoardSn(new BigDecimal(1));
        boardOnlinePage.setTotal(1);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.any())).thenReturn(boardOnlinePage);
        try {
            warehouseSubmitServiceImpl.checkBoardOnlineNumber(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUBMIT_OUTSOURCE_QTY_SUB, e.getMessage());
        }

        countByCondition.setCommitedQty(new BigDecimal("3"));
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.any(), Mockito.any())) .thenReturn(countByCondition);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.any())).thenReturn(boardOnlinePage);
        try {
            warehouseSubmitServiceImpl.checkBoardOnlineNumber(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUBMIT_OUTSOURCE_QTY_SUB, e.getMessage());
        }

        boardOnlinePage.setTotal(100);
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.any(), Mockito.any())) .thenReturn(countByCondition);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.any())).thenReturn(boardOnlinePage);
        warehouseSubmitServiceImpl.checkBoardOnlineNumber(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void boardPackageErpWebServiceTest() throws Exception {
        PowerMockito.mockStatic(StringUtils.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setOutboundType("1");
        dto.setErpErrorInfo("");
        dto.setBoardPackErpSucceNode("2");
        warehouseSubmitServiceImpl.boardPackageErpWebService(dto);
        Assert.assertNotNull(dto);

        dto.setBoardPackErpSucceNode("1");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.boardPackageErpWebService(dto);
        Assert.assertNotNull(dto);
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenReturn(true);
        PowerMockito.when(StringUtils.isEmpty(Mockito.any())).thenReturn(false);
        warehouseSubmitServiceImpl.boardPackageErpWebService(dto);
        Assert.assertNotNull(dto);
        PowerMockito.when(StringUtils.isEmpty(Mockito.any())).thenReturn(true);

        dto.setOutboundType("2");
        dto.setErpErrorInfo("343434 ERROR");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        dto.setSourceOrgId(new BigDecimal(2));
        PowerMockito.when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        warehouseSubmitServiceImpl.boardPackageErpWebService(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErpJob_EmptyList() throws Exception {
        List<WarehouseEntryInfo> list=new ArrayList<>();
        WarehouseEntryInfo dto=new WarehouseEntryInfo();
        dto.setBillNo("324434");
        list.add(dto);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErpJob();
        Assert.assertNotNull(list);

        PowerMockito.when(warehouseEntryInfoRepository.getBoardPackageWarehouseInfoJob(Mockito.any())).thenReturn(list);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErpJob();
        Assert.assertNotNull(list);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_Case0() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("0");
        dto.setOutboundType("1");
        dto.setErpErrorInfo("");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_Case1() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("1");
        dto.setOutboundType("1");
        dto.setErpErrorInfo("");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_Case2() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("2");
        dto.setOutboundType("1");
        dto.setErpErrorInfo("");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_Case3() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("3");
        dto.setBillType("5");
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_DefaultCase() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("4");
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void testWarehouseBoardPackageSubmitErp_Test() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBoardPackErpSucceNode("4");
        dto.setErpErrorInfo("324343");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.warehouseBoardPackageSubmitErp(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void updatePsWipInfos() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        WarehouseEntryInfo dto=new WarehouseEntryInfo();
        dto.setBillType("2");
        dto.setCarryAccount("1");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setLpn("LPN1");
        detailDTO.setSn("****************");
        detailDTO.setWorkOrderNo("3444");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        String routeId="23444";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        CtRouteDetail lastProcess=new CtRouteDetail();
        lastProcess.setRouteDetailId("324332");
        lastProcess.setNextProcess("1");
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any())).thenReturn(lastProcess);
        List<PsWipInfo> listPsWipInfo = new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setCurrProcessCode("1");
        psWipInfo.setCraftSection("1");
        psWipInfo.setWorkStation("1");
        listPsWipInfo.add(psWipInfo);
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any())).thenReturn(listPsWipInfo);
        warehouseSubmitServiceImpl.updatePsWipInfos(dto);
        Assert.assertNotNull(dto);

        dto.setCarryAccount("0");
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any())).thenReturn(null);
        warehouseSubmitServiceImpl.updatePsWipInfos(dto);
        Assert.assertNotNull(dto);

        List<PsWipInfo> listPsWipInfo1 = new ArrayList<>();
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any())).thenReturn(listPsWipInfo1);
        warehouseSubmitServiceImpl.updatePsWipInfos(dto);
        Assert.assertNotNull(dto);
        dto.setBillType("9");
        warehouseSubmitServiceImpl.updatePsWipInfos(dto);

    }

    @Test
    public void updatePsWipInfos_Test() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        WarehouseEntryInfo dto=new WarehouseEntryInfo();
        dto.setBillType("2");
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setLpn("LPN1");
        detailDTO.setSn("****************");
        detailDTO.setWorkOrderNo("3444");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        String routeId="23444";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        CtRouteDetail lastProcess=new CtRouteDetail();
        lastProcess.setRouteDetailId("324332");
        lastProcess.setNextProcess("2");
        lastProcess.setCraftSection("2");
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any())).thenReturn(lastProcess);
        List<PsWipInfo> listPsWipInfo = new ArrayList<>();
        PsWipInfo psWipInfo=new PsWipInfo();
        psWipInfo.setCurrProcessCode("1");
        psWipInfo.setCraftSection("1");
        psWipInfo.setWorkStation("1");
        listPsWipInfo.add(psWipInfo);
        PowerMockito.when(psWipInfoService.queryWipSnBatch(Mockito.any())).thenReturn(listPsWipInfo);
        warehouseSubmitServiceImpl.updatePsWipInfos(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void saveWarehouseBoardPackage() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillType("9");
        dto.setFactoryId(new BigDecimal(55));
        long sumCommitedQty=1;
        PowerMockito.when(warehouseEntryInfoRepository.getSumCommitedQty(Mockito.any())).thenReturn(sumCommitedQty);
        try {
            warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BILL_SUBMITTED_ERROR, e.getMessage());
        }

        dto.setBillType("10");
        sumCommitedQty=0;
        PowerMockito.when(warehouseEntryInfoRepository.getSumCommitedQty(Mockito.any())).thenReturn(sumCommitedQty);
        WarehouseEntryInfoDTO countByCondition =new WarehouseEntryInfoDTO();
        countByCondition.setCommitedQty(new BigDecimal("3"));
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.any(), Mockito.any())) .thenReturn(countByCondition);
        Page<BoardOnline> boardOnlinePage=new Page<>();
        BoardOnline boardOnline=new BoardOnline();
        boardOnline.setBoardSn(new BigDecimal(1));
        boardOnlinePage.setTotal(100);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatchPage(Mockito.any())).thenReturn(boardOnlinePage);
        long count = 1;
        PowerMockito.when(psWipInfoService.getCount(Mockito.any())).thenReturn(count);
        List<WarehouseEntryDetailDTO> detailDTOList = new ArrayList<>();
        WarehouseEntryDetailDTO detailDTO = new WarehouseEntryDetailDTO();
        detailDTO.setLpn("LPN1");
        detailDTO.setSn("****************");
        detailDTO.setWorkOrderNo("3444");
        detailDTOList.add(detailDTO);
        dto.setDetailDTOList(detailDTOList);
        String routeId="23444";
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        CtRouteDetail lastProcess=new CtRouteDetail();
        lastProcess.setRouteDetailId("324332");
        lastProcess.setNextProcess("2");
        lastProcess.setCraftSection("2");
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any())).thenReturn(lastProcess);
        String applyNo = "23423444";
        PowerMockito.when(DatawbRemoteService.warehouseEntryInfoWriteBackStep(Mockito.any())).thenReturn(applyNo);
        dto.setAttribute2("342");
        dto.setOrgId(new BigDecimal(1));
        dto.setEntityId(new BigDecimal(1));
        dto.setCreateBy("324432");
        dto.setCommitedQty(new BigDecimal(10));
        dto.setProdplanId("9");
        List<List<WarehouseEntryDetailDTO>> splitList =new ArrayList<>();
        splitList.add(dto.getDetailDTOList());
        PowerMockito.when(CommonUtils.splitList((List<WarehouseEntryDetailDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(splitList);
        WarehouseEntryInfo dtoId = new WarehouseEntryInfo();
        dtoId.setWarehouseEntryId("9");
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(Mockito.any())).thenReturn(dtoId);
        dto.setCarryAccount("0");
        warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        Assert.assertTrue(true);

        PowerMockito.when(warehouseEntryInfoRepository.getSumCommitedQty(Mockito.any())).thenReturn(null);
        PowerMockito.when(warehouseEntryInfoRepository.getCountByCondition(Mockito.any(), Mockito.any())) .thenReturn(countByCondition);
        PowerMockito.when(psWipInfoService.getCount(Mockito.any())).thenReturn(count);
        PowerMockito.when(CommonUtils.getRouteIdByWip(Mockito.any())) .thenReturn(routeId);
        PowerMockito.when(warehouseEntryInfoService.getLastProcess(Mockito.any())).thenReturn(lastProcess);
        PowerMockito.when(DatawbRemoteService.warehouseEntryInfoWriteBackStep(Mockito.any())).thenReturn(applyNo);
        PowerMockito.when(CommonUtils.splitList((List<WarehouseEntryDetailDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(splitList);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(Mockito.any())).thenReturn(dtoId);
        warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        Assert.assertTrue(true);

        dto.setAttribute2("");
        dto.setItemNo("2343434");
        List<BsItemInfo>  infoList=new ArrayList<>();
        BsItemInfo bsItemInfo=new BsItemInfo();
        bsItemInfo.setVersion("345");
        infoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getStyleInfo(SqlUtils.convertStrCollectionToSqlType(Lists.newArrayList(dto.getItemNo())))).thenReturn(infoList);
        PowerMockito.when(CommonUtils.splitList((List<WarehouseEntryDetailDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(splitList);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(Mockito.any())).thenReturn(dtoId);
        warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        Assert.assertTrue(true);

        PowerMockito.when(BasicsettingRemoteService.getStyleInfo(SqlUtils.convertStrCollectionToSqlType(Lists.newArrayList(dto.getItemNo())))).thenReturn(null);
        PowerMockito.when(CommonUtils.splitList((List<WarehouseEntryDetailDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(splitList);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryInfoByBillNo(Mockito.any())).thenReturn(dtoId);
        warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        Assert.assertTrue(true);

        dto.setAttribute2("21333");
        dto.setCarryAccount("1");
        dto.setOutboundType("1");
        dto.setErpErrorInfo("");
        dto.setOrgId(new BigDecimal(1));
        dto.setSourceOrgId(new BigDecimal(1));
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(1019));
        sysLookupTypesDTO.setLookupMeaning("http://localhost:8080");
        sysLookupTypesDTO.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        Assert.assertTrue(true);

        sysLookupTypesDTO.setLookupType(new BigDecimal(1018));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        try {
            warehouseSubmitServiceImpl.saveWarehouseBoardPackage(dto);
        } catch (Exception e) {
            throw e;
        }
    }

    @Test
    public void checkDetailSnCount() throws Exception{
        List<String> list=new ArrayList<>();
        list.add("12323232");
        PowerMockito.when(warehouseEntryInfoRepository.checkDetailSnCount(Mockito.any())).thenReturn(1);
        try {
            warehouseSubmitServiceImpl.checkDetailSnCount(list,"2131",list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOX_IS_WAREHOUSE, e.getMessage());
        }

        PowerMockito.when(warehouseEntryInfoRepository.checkDetailSnCount(Mockito.any())).thenReturn(0);
        warehouseSubmitServiceImpl.checkDetailSnCount(list,"2131",list);
        Assert.assertTrue(true);
    }

    @Test
    public void checkSubCard() throws Exception {
        WarehouseStockInfoDTO dto = new WarehouseStockInfoDTO();
        dto.setItemNo("123321");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(warehouseSubmitServiceImpl, "checkSubCard", dto);
    }
}
/*Ended by AICoder, pid:8024e22f83c54d0a909c8a667f8bf8aa*/
