package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.impl.WorkorderOnlineServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

/**
 * @Author:
 * @Date: 2020/12/17 11:04
 */
@PrepareForTest({BasicsettingRemoteService.class, HttpRemoteService.class})
public class WorkOrderOnlineServiceImplLZTest extends PowerBaseTestCase {

    @InjectMocks
    private WorkorderOnlineServiceImpl service;

    @Test
    public void checkWorkOrderSolder() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, HttpRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_10312))
                .thenReturn(null);
        service.checkWorkOrderSolder(new PsWorkOrderDTO());

        SysLookupTypesDTO types = new SysLookupTypesDTO();
        types.setLookupMeaning("Y");
        types.setLookupCode(new BigDecimal(Constant.LOOKUP_VALUE_1031201));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_10312))
                .thenReturn(Lists.newArrayList(types));
        PowerMockito.when(HttpRemoteService.pointToPointSelective(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);
        try {
            service.checkWorkOrderSolder(new PsWorkOrderDTO());
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.GET_SOLDER_OPEN_ADD_FAIL, e.getMessage());
        }
        PowerMockito.when(HttpRemoteService.pointToPointSelective(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(null);
                        }})));

        try {
            service.checkWorkOrderSolder(new PsWorkOrderDTO(){{setProcessGroup("1");}});
        } catch (Exception e) {
        }
        PowerMockito.when(HttpRemoteService.pointToPointSelective(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(false);
                        }})));
        try {
            service.checkWorkOrderSolder(new PsWorkOrderDTO(){{setProcessGroup("1");}});
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.SOLDER_NO_OPEN_ADD, e.getMessage());
        }
    }
}
