package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.Matchers.any;

@PrepareForTest({HttpRemoteService.class})
public class ProductionDeliveryRemoteServiceTest extends PowerBaseTestCase {

    @Before
    public void before(){
        PowerMockito.mockStatic(HttpRemoteService.class);
    }

    @Test
    public void batchInsertContainerContentInfo() {
        try {
            ProductionDeliveryRemoteService.batchInsertContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                    any(),any(),any(),any(),any()))
                    .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                            JSON.toJSONString(new ServiceData(){{
                                setCode(new RetCode("1","1"));
                            }})));
            ProductionDeliveryRemoteService.batchInsertContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                    any(),any(),any(),any(),any()))
                    .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(new ServiceData())));
            ProductionDeliveryRemoteService.batchInsertContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void batchUpdateContainerContentInfo() {
        try {
            ProductionDeliveryRemoteService.batchUpdateContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                    any(),any(),any(),any(),any()))
                    .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                            JSON.toJSONString(new ServiceData(){{
                                setCode(new RetCode("1","1"));
                            }})));
            ProductionDeliveryRemoteService.batchUpdateContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                    any(),any(),any(),any(),any()))
                    .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(new ServiceData())));
            ProductionDeliveryRemoteService.batchUpdateContainerContentInfo(
                    Lists.newArrayList(new ContainerContentInfoDTO())
            );
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}