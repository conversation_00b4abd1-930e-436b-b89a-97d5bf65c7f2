package com.zte.autoTest.unitTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.TransferStrategyInfoService;
import com.zte.application.impl.SmtMachineMaterialPrepareServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.EmEqpInfo;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.SmtLocationInfo;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.PdaCompositePrepareDTO;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.LXBGQ;
import static org.mockito.Matchers.any;

@PrepareForTest({EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class, CommonUtils.class, ProductionDeliveryRemoteService.class, MESHttpHelper.class, ObtainRemoteServiceDataUtil.class, CenterfactoryRemoteService.class, BasicsettingRemoteService.class})
public class SmtMachineMaterialPrepareServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtMachineMaterialPrepareServiceImpl service;

    @Mock
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;
    @Mock
    private TransferStrategyInfoService transferStrategyInfoService;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Mock
    private PkCodeHistoryService pkCodeHistoryService;

    @Mock
    private PkCodeInfoService pkCodeInfoService;

    @Mock
    private BSmtBomDetailService bSmtBomDetailService;

    @Mock
    private ImesPDACommonService imesPdaCommonService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;


    @Test
    public void saveOrUpdateSmtMachineMaterialPrepareByFeederRef() throws Exception{
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setDirection("0");
        bSmtBomDetailList.add(bSmtBomDetail);
        PowerMockito.when(bSmtBomDetailRepository.getList(any())).thenReturn(bSmtBomDetailList);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemAngle("0度");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(pkCodeInfo);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("Y");
        service.saveOrUpdateSmtMachineMaterialPrepareByFeederRef(new SmtMachineMaterialPrepareDTO(){{
            setHasUnbind(true);
        }});
        PowerMockito.when(smtMachineMaterialPrepareRepository.unbindFeederPrepare(any())).thenReturn(Constant.INT_1);
        service.saveOrUpdateSmtMachineMaterialPrepareByFeederRef(new SmtMachineMaterialPrepareDTO(){{
            setHasUnbind(true);
        }});
        Assert.assertEquals(null,service.saveOrUpdateSmtMachineMaterialPrepareByFeederRef(new SmtMachineMaterialPrepareDTO(){{
            setHasUnbind(false);
        }}));
    }

    @Test
    public void unbindFeederByNo() throws Exception{
        try {
            service.unbindFeederByNo(new SmtMachineMaterialPrepareDTO());
            PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(any())).thenReturn(Constant.INT_1);
            Assert.assertEquals(1,service.unbindFeederByNo(new SmtMachineMaterialPrepareDTO()));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FEEDER_IN_USE, e.getMessage());
        }
    }

    @Test
    public void unbindFeederByReelId() throws Exception{
        try {
            service.unbindFeederByReelId(new SmtMachineMaterialPrepareDTO());
            PowerMockito.when(smtMachineMaterialMoutingRepository.countByFeederNoOrReelId(any())).thenReturn(Constant.INT_1);
            Assert.assertEquals(1,service.unbindFeederByReelId(new SmtMachineMaterialPrepareDTO()));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IN_USE, e.getMessage());
        }
    }

    @Test
    public void bindFeeder() throws Exception{
        try {
            service.bindFeeder(new SmtMachineMaterialPrepareDTO());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FEEDER_NO_IS_NULL, e.getMessage());
        }
        try {
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setFeederNo("1");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IS_NULL, e.getMessage());
        }

        PowerMockito.when(smtMachineMaterialPrepareRepository.updateSmtMachineMaterialPrepareByOnlyId(any())).thenReturn(Constant.INT_1);
        service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});

        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.updateSmtMachineMaterialPrepareByOnlyId(any())).thenReturn(Constant.INT_0);
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("Y");setObjectId("1");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IN_USE, e.getMessage());
        }

        PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("E");setObjectId("1");}})
        );
        service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});
        service.checkFeederAndReelId(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});

        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("1");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");setLocationSn("3");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IN_USE, e.getMessage());
        } try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("Y");setFeederNo("2");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FEEDER_IN_USE, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("E");setFeederNo("2");}})
            );
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByLocation(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("Y");setFeederNo("2");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");setLocationSn("3");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IN_USE, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setFeederNo("2");}})
            );
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByLocation(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("1");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");setLocationSn("3");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_ID_IN_USE, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setFeederNo("2");}})
            );
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByLocation(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("2");setFeederNo("1");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");setLocationSn("3");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_FEEDER_BOTH_REPLACE, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByLocation(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("2");setFeederNo("1");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");setLocationSn("3");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_FEEDER_BOTH_REPLACE, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("1");setFeederNo("2");}})
            );
            service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("1");setFeederNo("2");}});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_FEEDER_BOTH_BOUND, e.getMessage());
        }
        try {
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByLocation(any())).thenReturn(
                    Lists.newArrayList(new SmtMachineMaterialPrepare(){{setEnabledFlag("N");setObjectId("1");setFeederNo("2");}})
            );
            Assert.assertEquals(null,service.bindFeeder(new SmtMachineMaterialPrepareDTO(){{setObjectId("2");setFeederNo("2");setLocationSn("3");}}));
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REEL_FEEDER_BOTH_REPLACE, e.getMessage());
        }
    }

    @Test
    public void bothReplace() {
        service.bothReplace(new SmtMachineMaterialPrepareDTO(), new SmtMachineMaterialPrepare());
        service.bothReplace(new SmtMachineMaterialPrepareDTO(){{setUseUpFlag("Y");}}, new SmtMachineMaterialPrepare());
        service.bothReplace(new SmtMachineMaterialPrepareDTO(){{setUseUpFlag("Y");}}, new SmtMachineMaterialPrepare(){{setNextReelId("1");}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkFeederNo()  {
        try {
            PowerMockito.mockStatic(EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class);
            PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(Lists.newArrayList(
                    new SmtMachineMaterialPrepare()
            ));
            service.checkFeederNo("1");
            PowerMockito.when(EqpmgmtsRemoteService.getByEqpCode(any())).thenReturn(new EmEqpInfo());
            service.checkFeederNo("1");
            PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(new PsEntityPlanBasic());
            service.checkFeederNo("1");
            PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoByWorkOrderNo(any())).thenReturn(new PsEntityPlanBasic(){{setWorkOrderStatus(LXBGQ);}});
            Assert.assertNotNull(service.checkFeederNo("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_NOT_FINISHED, e.getMessage());
        }
    }

    @Test
    public void getModuleSelectData() throws Exception{
        PdaCompositePrepareDTO dto = new PdaCompositePrepareDTO();
        List<PdaCompositePrepareDTO> list = service.getModuleSelectData(dto);
        Assert.assertEquals(0,list.size());

        PowerMockito.doNothing().when(transferStrategyInfoService).checkCompositePrepareTransferStrategy(Mockito.any());
        dto.setCfgHeaderId("cfgHeaderId");
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        PowerMockito.when(bSmtBomDetailService.getList(any(), any(), any())).thenReturn(bSmtBomDetailList);
        List<PdaCompositePrepareDTO> list1 = service.getModuleSelectData(dto);
        Assert.assertEquals(0,list1.size());

        bSmtBomDetailList.add(new BSmtBomDetail(){{setModuleNo("(X4S-2)1");}});
        bSmtBomDetailList.add(new BSmtBomDetail(){{setModuleNo("(X4S-2)2");}});
        bSmtBomDetailList.add(new BSmtBomDetail(){{setModuleNo("(X4S-2)4");}});
        PowerMockito.when(bSmtBomDetailService.getList(any(), any(), any())).thenReturn(bSmtBomDetailList);
        List<PdaCompositePrepareDTO> list2 = service.getModuleSelectData(dto);
        Assert.assertEquals(3,list2.size());

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        List<SmtMachineMaterialPrepareDTO> prepareList  = new ArrayList<>();
        prepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        prepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setMaterialTray("objectId1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList);
        List<PdaCompositePrepareDTO> list3 = service.getModuleSelectData(dto);
        Assert.assertEquals("(X4S-2)1",list3.get(0).getModuleNo());
        Assert.assertEquals(false,list3.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2",list3.get(1).getModuleNo());
        Assert.assertEquals(true,list3.get(1).getCompletedFlag());
        prepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)4");setLocationNo("locationNo4");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList);
        List<SmtLocationInfo> smtLocationInfoList = new ArrayList<>();
        smtLocationInfoList.add(new SmtLocationInfo(){{setLocationNo("locationNo4");}});
        PowerMockito.when(ObtainRemoteServiceDataUtil.getSmtLocationInfoListByPost(any())).thenReturn(smtLocationInfoList);
        List<PdaCompositePrepareDTO> list6 = service.getModuleSelectData(dto);
        Assert.assertEquals(true,list6.get(2).getCompletedFlag());

        List<SmtMachineMaterialPrepareDTO> prepareList1  = new ArrayList<>();
        prepareList1.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setMaterialTray("objectId1");setItemCode("itemCode1");setLocationNo("locationNo1");}});

        dto.setmIsBothSides(true);
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");
        List<SmtMachineMaterialPrepareDTO> otherPrepareList  = new ArrayList<>();
        otherPrepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setItemCode("itemCode");setLocationNo("locationNo");}});
        otherPrepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList1,otherPrepareList);
        List<PdaCompositePrepareDTO> list4 = service.getModuleSelectData(dto);
        Assert.assertEquals("(X4S-2)1",list3.get(0).getModuleNo());
        Assert.assertEquals(false,list3.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2",list3.get(1).getModuleNo());
        Assert.assertEquals(true,list3.get(1).getCompletedFlag());

        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("1");
        dto.setRelatedWorkOrder("1");
        dto.setmRelatedLine("1");
        List<SmtMachineMaterialPrepareDTO> otherPrepareList1  = new ArrayList<>();
        otherPrepareList1.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");}});
        otherPrepareList1.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setMaterialTray("1");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList1,otherPrepareList1);
        List<PdaCompositePrepareDTO> list5 = service.getModuleSelectData(dto);
        Assert.assertEquals("(X4S-2)1",list3.get(0).getModuleNo());
        Assert.assertEquals(false,list3.get(0).getCompletedFlag());
        Assert.assertEquals("(X4S-2)2",list3.get(1).getModuleNo());
        Assert.assertEquals(true,list3.get(1).getCompletedFlag());
    }

    @Test
    public void getModulePrepareList() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("已比对完成");
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);

        PdaCompositePrepareDTO dto = new PdaCompositePrepareDTO();
        dto.setFactoryId(new BigDecimal(55));
        dto.setSmtDeviceType("ASM");
        ServiceData<List<SmtMachineMaterialPrepareDTO>> ret = service.getModulePrepareList(dto);
        Assert.assertEquals(0,ret.getBo().size());

        List<SmtMachineMaterialPrepareDTO> prepareList  = new ArrayList<>();
        prepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList);
        ServiceData<List<SmtMachineMaterialPrepareDTO>> ret1 = service.getModulePrepareList(dto);
        Assert.assertEquals(1,ret1.getBo().size());
        Assert.assertEquals("(X4S-2)1",ret1.getBo().get(0).getModuleNo());


        List<SmtMachineMaterialPrepareDTO> prepareList1  = new ArrayList<>();
        prepareList1.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setMaterialTray("objectId1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        dto.setmIsBothSides(true);
        dto.setmOtherSideCfgHeaderId("mOtherCfgHeaderId");
        List<SmtMachineMaterialPrepareDTO> otherPrepareList  = new ArrayList<>();
        otherPrepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        otherPrepareList.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setItemCode("itemCode2");setLocationNo("locationNo2");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList1,otherPrepareList);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", prepareList1, dto.getSmtDeviceType()));

        ServiceData<List<SmtMachineMaterialPrepareDTO>> ret2 = service.getModulePrepareList(dto);
        Assert.assertEquals(2,ret2.getBo().size());
        Assert.assertEquals("已比对完成",ret2.getCode().getMsg());
        Assert.assertEquals("locationNo2", ret2.getBo().get(0).getLocationNo());

        List<SmtMachineMaterialPrepareDTO> otherPrepareListT  = new ArrayList<>();
        otherPrepareListT.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setItemCode("itemCode2");setLocationNo("locationNo2-1");}});
        otherPrepareListT.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)2");setItemCode("itemCode2");setLocationNo("locationNo2-2");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any()))
                .thenReturn(prepareList1,otherPrepareListT);
        service.getModulePrepareList(dto);

        dto.setmIsBothSides(false);
        dto.setRelatedCfgHeaderId("1");
        dto.setRelatedWorkOrder("1");
        dto.setmRelatedLine("relatedLine");
        dto.setSmtDeviceType("FUJI");
        List<SmtMachineMaterialPrepareDTO> otherPrepareList1  = new ArrayList<>();
        otherPrepareList1.add(new SmtMachineMaterialPrepareDTO(){{setModuleNo("(X4S-2)1");setItemCode("itemCode1");setLocationNo("locationNo1");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareList(any())).thenReturn(prepareList1,otherPrepareList1);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", prepareList1, dto.getSmtDeviceType()));

        ServiceData<List<SmtMachineMaterialPrepareDTO>> ret3 = service.getModulePrepareList(dto);
        Assert.assertEquals(1,ret3.getBo().size());
        Assert.assertEquals("relatedLine",ret3.getBo().get(0).getRelatedLine());
        Assert.assertEquals("locationNo1", ret3.getBo().get(0).getLocationNo());

        List<SmtMachineMaterialPrepareDTO> listSmtMachineMaterialPrepareDTO = new ArrayList<>();
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", listSmtMachineMaterialPrepareDTO, ""));

        ServiceData<List<SmtMachineMaterialPrepareDTO>> ret4 = service.getModulePrepareList(dto);
        Assert.assertEquals(1, ret4.getBo().size());
        Assert.assertEquals("relatedLine", ret4.getBo().get(0).getRelatedLine());
        Assert.assertEquals(0, listSmtMachineMaterialPrepareDTO.size());


    }

    @Test
    public void checkMaterialTray() throws Exception{
        PdaCompositePrepareDTO dto = new PdaCompositePrepareDTO();
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REELID_EMPTY, e.getMessage());
        }

        dto.setMaterialTray("reelId");
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(ProductionDeliveryRemoteService.checkWmsHasReelId(Mockito.anyString())).thenReturn(true);
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WMS_HAS_THE_REELID, e.getMessage());
        }

        PowerMockito.when(ProductionDeliveryRemoteService.checkWmsHasReelId(Mockito.anyString())).thenReturn(false);

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUNT_MATRIALTRAY, e.getMessage());
        }

        dto.setSourceTask("sourceTask");
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setProductTask("sourceTask1");
        pkCodeInfoList.add(pkCodeInfo);
        PowerMockito.when(imesPdaCommonService.pkCodeSourceTaskCheck(any(),any())).thenReturn(false);
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER, e.getMessage());
        }

        pkCodeInfoList.remove(0);
        pkCodeInfo.setProductTask("sourceTask");
        pkCodeInfoList.add(pkCodeInfo);
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new ArrayList<>();
        smtMachineMaterialPrepareList.add(new SmtMachineMaterialPrepare(){{setItemCode("itemCode");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(any())).thenReturn(smtMachineMaterialPrepareList);
        service.checkMaterialTray(dto);

        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setItemCode("ItemCode");
        smtMachineMaterialPrepare.setFeederNo("FeederNo");
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIALS_HAS_PREPARED, e.getMessage());
        }

        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(any())).thenReturn(null);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = new ArrayList<>();
        smtMachineMaterialMoutingList.add(new SmtMachineMaterialMouting(){{setWorkOrder("workOrder");}});
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(smtMachineMaterialMoutingList);
        try {
            service.checkMaterialTray(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIALS_IS_USE, e.getMessage());
        }

        smtMachineMaterialMoutingList.remove(0);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_CODE_652310001)).thenReturn("Y");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(null);
        Boolean f1 = service.checkMaterialTray(dto);
        Assert.assertEquals(false, f1);

        PkCodeInfo itemDirection = new PkCodeInfo();
        itemDirection.setPkCode("66666");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(itemDirection);
        Boolean f2 = service.checkMaterialTray(dto);
        Assert.assertEquals(false, f2);

        itemDirection.setItemAngle("90度");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(itemDirection);
        Boolean t = service.checkMaterialTray(dto);
        Assert.assertEquals(true, t);

        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Constant.LOOKUP_CODE_652310001)).thenReturn("N");
        Boolean b = service.checkMaterialTray(dto);
        Assert.assertEquals(true,b);
    }

    @Test
    public void saveCompositePrepareInfo() throws Exception{
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUNT_MATRIALTRAY, e.getMessage());
        }

        dto.setFeederNo("feederNo");
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = new ArrayList<>();
        smtMachineMaterialMoutingList.add(new SmtMachineMaterialMouting(){{setWorkOrder("workOrder");}});
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(smtMachineMaterialMoutingList);
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIALS_IS_USE, e.getMessage());
        }

        dto.setObjectId("reelId");
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(new ArrayList<>(),smtMachineMaterialMoutingList);
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATRIAL_HAS_USED, e.getMessage());
        }

        dto.setObjectId("");
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(new ArrayList<>());
        List<SmtMachineMaterialPrepare> prepareList = new ArrayList<>();
        prepareList.add(new SmtMachineMaterialPrepare(){{setWorkOrder("workOrder");setItemCode("itemCode");setFeederNo("feederNo");}});
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(any())).thenReturn(prepareList);
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MATERIALS_IS_USE, e.getMessage());
        }

        dto.setObjectId("reelId");
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(any())).thenReturn(new ArrayList<>(),prepareList);
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HAS_BINGED_TO_MATRIAL, e.getMessage());
        }

        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(any())).thenReturn(new ArrayList<>());
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        try {
            service.saveCompositePrepareInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUNT_MATRIALTRAY, e.getMessage());
        }


        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemCode("itemCode1");
        pkCodeInfoList.add(pkCodeInfo);

        dto.setItemCode("itemCode");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("站位物料跟上料表物料不一致，请确认！");
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        headerMap.put("x-emp-no","10308742");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(any())).thenReturn("N");
        PowerMockito.when(bSmtBomDetailRepository.getList(any())).thenReturn(new ArrayList<>());
        PkCodeInfo pkCodeResult = new PkCodeInfo();
        pkCodeResult.setItemAngle("0度");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(pkCodeResult);
        ServiceData ret = service.saveCompositePrepareInfo(dto);
        Assert.assertEquals(MessageId.LOCATION_INCONSISTENT, ret.getCode().getMsgId());
    }

    @Test
    public void deleteSmtMachineMaterialPrepareById() {
        service.deleteSmtMachineMaterialPrepareById(Mockito.any());
        Assert.assertTrue(true);
    }

    @Test
    public void deleteSmtMachineMaterialPrepareByDoubleReelId() {
        service.deleteSmtMachineMaterialPrepareByDoubleReelId(Mockito.any());
        Assert.assertTrue(true);

    }

    @Test
    public void updateSmtMachineMaterialPrepareById() {
        service.updateSmtMachineMaterialPrepareById(Mockito.any());
        Assert.assertTrue(true);
    }

    @Test
    public void updateSmtMachineMaterialPrepareByIdSelective() {
        PowerMockito.when(service.updateSmtMachineMaterialPrepareByIdSelective(any())).thenReturn(0);
        List<SmtMachineMaterialPrepareDTO> list = new ArrayList<>();
        int i = service.insertSmtMachineMaterialPrepareBatch(list);
        Assert.assertEquals(i, 0);
    }

    @Test
    public void deleteSmtMachineMaterialPrepareBySelective() {
        PowerMockito.when(service.deleteSmtMachineMaterialPrepareBySelective(any())).thenReturn(0);
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        int i = service.deleteSmtMachineMaterialPrepareBySelective(prepare);
        Assert.assertEquals(i, 0);
    }

    @Test
    public void selectSmtMachineMaterialPrepareById() {
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setMachineNo("XXX");
        PowerMockito.when(service.selectSmtMachineMaterialPrepareById(any())).thenReturn(prepare);
        Assert.assertNotNull(prepare);
    }

    @Test
    public void updateSmtMachineMaterialPrepareByOthers() {
        PowerMockito.when(service.updateSmtMachineMaterialPrepareByOthers(any())).thenReturn(0);
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        int i = service.updateSmtMachineMaterialPrepareByOthers(prepare);
        Assert.assertEquals(i, 0);
    }

    @Test
    public void pdaSelectCountSmtMachineMaterialPrepare() {
        PowerMockito.when(service.pdaSelectCountSmtMachineMaterialPrepare(any())).thenReturn(0L);
        SmtMachineMaterialPrepareDTO prepare = new SmtMachineMaterialPrepareDTO();
        Long i = service.pdaSelectCountSmtMachineMaterialPrepare(prepare);
        Assert.assertNotNull(i);
    }

    @Test
    public void selectByWorkOrderCode() {
        List<SmtMachineMaterialPrepare> list = new ArrayList<>();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setMachineNo("XXX");
        list.add(prepare);
        PowerMockito.when(service.selectByWorkOrderCode(any())).thenReturn(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void selectByObjectId() {
        List<SmtMachineMaterialPrepare> list = new ArrayList<>();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setMachineNo("XXX");
        list.add(prepare);
        PowerMockito.when(service.selectByObjectId(any())).thenReturn(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void selectByWorkOrderAndLineCode() {
        List<SmtMachineMaterialPrepare> list = new ArrayList<>();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setMachineNo("XXX");
        list.add(prepare);
        PowerMockito.when(service.selectByWorkOrderAndLineCode(any())).thenReturn(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void insertOperationLog() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("logId", "XXXX");
        map.put("omName", "abc");
        map.put("omParam", "XXXXX");
        map.put("returnMsg", Constant.MSG_OK);
        map.put("omUrl", "xxxxx");
        map.put("factoryId", "55");
        map.put("createBy", "11122");
        String responseStr = "{}";
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
        PowerMockito.when(centerfactoryRemoteService.writeOmLog(any())).thenReturn(json);
        centerfactoryRemoteService.writeOmLog(map);
        Assert.assertNotNull(json);
    }

    @Test
    public void getListSort() throws Exception {
        List<SmtMachineMaterialPrepareDTO> list  =new ArrayList<>();
        String smtDeviceType = "13";
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setLocationNo("112233");
        SmtMachineMaterialPrepareDTO dto1 = new SmtMachineMaterialPrepareDTO();
        dto1.setLocationNo("112234");
        list.add(dto);
        list.add(dto1);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", list,smtDeviceType));
        smtDeviceType = BusinessConstant.DEVICE_TYPE_ASM;
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", list,smtDeviceType));

        List<SmtMachineMaterialPrepareDTO> list1  =new ArrayList<>();
        SmtMachineMaterialPrepareDTO dto2 = new SmtMachineMaterialPrepareDTO();
        dto2.setLocationNo("");
        SmtMachineMaterialPrepareDTO dto3 = new SmtMachineMaterialPrepareDTO();
        dto3.setLocationNo("");
        list1.add(dto2);
        list1.add(dto3);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", list1,smtDeviceType));
        smtDeviceType = "13";
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getListSort", list1,smtDeviceType));
    }

    @Test
    public void setPkCodeInfoTest() throws Exception {
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        Whitebox.invokeMethod(service, "setPkCodeInfo", entity, null);
        Assert.assertTrue(entity.getQty() == null);

        pkCodeInfo.setItemQty(new BigDecimal("2"));
        Whitebox.invokeMethod(service, "setPkCodeInfo", entity, pkCodeInfo);
        Assert.assertTrue(entity.getQty().intValue() == 2);
    }
}