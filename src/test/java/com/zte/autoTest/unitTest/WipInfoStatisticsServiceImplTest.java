package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.WipInfoStatisticsServiceImpl;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;
@PrepareForTest({ObtainRemoteServiceDataUtil.class})
public class WipInfoStatisticsServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    WipInfoStatisticsServiceImpl service;
    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Test
    public void getBsPubHrvInfo() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(anyString()))
                .thenReturn(Lists.newArrayList(new BsPubHrvOrgId()));
        Assert.assertNotNull(service.getBsPubHrvInfo(new StringBuilder("1")));
    }
}
