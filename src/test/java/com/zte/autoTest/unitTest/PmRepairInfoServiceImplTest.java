package com.zte.autoTest.unitTest;

import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PmRepairDetailService;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PmRepairRcvService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.PmRepairInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.PmRepairInfo;
import com.zte.domain.model.PmRepairInfoRepository;
import com.zte.domain.model.PmRepairInfoStatistics;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.interfaces.dto.PmRepairInfoStatisticsDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.any;



@PrepareForTest({ExcelCommonUtils.class, ObtainRemoteServiceDataUtil.class,BasicsettingRemoteService.class,CommonUtils.class})
public class PmRepairInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairInfoServiceImpl service;

    @Mock
    private PmRepairInfoRepository pmRepairInfoRepository;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private PsScanHistoryService psScanHistoryService;
    @Mock
    private PmRepairDetailService pmRepairDetailService;
    @Mock
    private BsWorkTimeSectionService bsWorkTimeSectionService;
    @Mock
    private PsOutputInfoService psOutputInfoService;
    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;
    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(ExcelCommonUtils.class,BasicsettingRemoteService.class,
                ObtainRemoteServiceDataUtil.class);
    }

    @Test
    public void getPmRcvInfo()throws Exception{
        PmRepairInfoServiceImpl pmRepairInfoServiceImpl=PowerMockito.spy(new PmRepairInfoServiceImpl());
        PmRepairInfoDTO dto=new PmRepairInfoDTO();
        dto.setItemCode("123");
        dto.setPage(new Long(1));
        dto.setRows(new Long(10));
        dto.setFactoryId(new BigDecimal(52));

        Long total=1L;
        PowerMockito.doReturn(total).when(pmRepairInfoServiceImpl).getRelOneCount(Mockito.any());

        List<PmRepairInfo> list=new ArrayList<>();
        PmRepairInfo pmDto=new PmRepairInfo();
        dto.setReasonCode("123");
        list.add(pmDto);
        PowerMockito.doReturn(list).when(pmRepairInfoServiceImpl).getRelOneDetailPage(Mockito.any());


        pmRepairInfoServiceImpl.getPmRcvInfo(dto);

        dto.setRepairRcvBeginDate("2020-08-16 10:48:34");
        dto.setRepairRcvEndDate("2021-08-23 10:48:34");
        Assert.assertNotNull(pmRepairInfoServiceImpl.getPmRcvInfo(dto));


    }

    @Test
    public void writeExcel() {
        try {
            PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(any(), any())).thenReturn(new HashMap(){{put("1", new HashMap(){{put("lookupMeaning", "1");}});}});
            service.writeExcel(new PmRepairInfoDTO(){{setExportType(1);}}, "", 2, null);
            service.writeExcel(new PmRepairInfoDTO(){{setExportType(1); setFactoryId(BigDecimal.ONE);}}, "", 2, null);
            String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);
        } catch (Exception e) {
            Assert.assertNull( e.getMessage());
        }
    }

    @Test
    public void listRepairStockPage()  throws Exception{
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal("52"));

        List<PmRepairInfo> list = new LinkedList<>();
        PmRepairInfo a1 = new PmRepairInfo();
        a1.setApplicationSection("2020");
        list.add(a1);
        PowerMockito.when( pmRepairInfoRepository.listRepairStockPage(record)).thenReturn(list);

        List<SysLookupTypesDTO> sysLookUpValueList = new LinkedList<>();
        SysLookupTypesDTO b1 = new SysLookupTypesDTO();
        b1.setLookupCode(new BigDecimal("2020"));
        b1.setLookupMeaning("123");
        sysLookUpValueList.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(sysLookUpValueList);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.DATA_DICTIONARY_OVER_STOCK_TIME_IS_NULL);
        try {
            Assert.assertNotNull(service.listRepairStockPage(record));
        }catch (RuntimeException returnMessage){
            Assert.assertTrue(returnMessage instanceof NullPointerException);
        }
    }


    @Test
    public void getSendRepairCount() throws Exception {
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal("52"));

        List<PmRepairInfoStatistics> rss = new ArrayList<>();
        PmRepairInfoStatistics pmRepairInfoStatistics = new PmRepairInfoStatistics();
        rss.add(pmRepairInfoStatistics);
        PowerMockito.when(pmRepairInfoRepository.getSendRepairCount(record)).thenReturn(rss);


        try {
            service.getSendRepairCount(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.REPAIR_SOURCE_IS_EMPTY, e.getExMsgId());
        }

        record.setFromStation("aaaa");
        record.setBuilding("aaaa");
        record.setStartTime("aaaa");
        try {
            service.getSendRepairCount(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.TIME_FORMAT_IS_WRONG, e.getExMsgId());
        }

        record.setStartTime("2022-04-20 10:55:21");
        record.setEndTime("2023-04-21 17:55:21");
        try {
            service.getSendRepairCount(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.DURATION_IS_MORE_THAN_MONTH, e.getExMsgId());
        }

        record.setEndTime("2022-04-21 17:55:21");
        List<PmRepairInfoStatisticsDTO> sendRepairCount = service.getSendRepairCount(record);

        Assert.assertEquals(1, rss.size());
    }

    @Test
    public void getRepairReturnCount() throws Exception {
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal("52"));

        List<PmRepairInfoStatistics> rss = new ArrayList<>();
        PmRepairInfoStatistics pmRepairInfoStatistics = new PmRepairInfoStatistics();
        rss.add(pmRepairInfoStatistics);
        PowerMockito.when(pmRepairInfoRepository.getRepairReturnCount(record)).thenReturn(rss);

        record.setFromStation("aaaa");
        record.setBuilding("aaaa");
        record.setStartTime("2022-04-20 10:55:21");
        record.setEndTime("2022-04-21 17:55:21");
        List<PmRepairInfoStatisticsDTO> sendRepairCount = service.getRepairReturnCount(record);

        Assert.assertEquals(1, rss.size());
    }


    @Test
    public void getMultiRepairCount() throws Exception {
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal("52"));

        List<PmRepairInfoStatistics> rss = new ArrayList<>();
        PmRepairInfoStatistics pmRepairInfoStatistics = new PmRepairInfoStatistics();
        rss.add(pmRepairInfoStatistics);
        PowerMockito.when(pmRepairInfoRepository.getMultiRepairCount(record)).thenReturn(rss);

        record.setFromStation("aaaa");
        record.setBuilding("aaaa");
        record.setStartTime("2022-04-20 10:55:21");
        record.setEndTime("2022-04-21 17:55:21");
        List<PmRepairInfoStatisticsDTO> sendRepairCount = service.getMultiRepairCount(record);

        Assert.assertEquals(1, rss.size());
    }


    @Test
    public void getRepairResultCount() throws Exception {
        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal("52"));

        List<PmRepairInfoStatistics> rss = new ArrayList<>();
        PmRepairInfoStatistics pmRepairInfoStatistics = new PmRepairInfoStatistics();
        rss.add(pmRepairInfoStatistics);
        PowerMockito.when(pmRepairInfoRepository.getRepairResultCount(record)).thenReturn(rss);

        record.setFromStation("aaaa");
        record.setBuilding("aaaa");
        record.setStartTime("2022-04-20 10:55:21");
        record.setEndTime("2022-04-21 17:55:21");
        List<PmRepairInfoStatisticsDTO> sendRepairCount = service.getRepairResultCount(record);

        Assert.assertEquals(1, rss.size());
    }

}