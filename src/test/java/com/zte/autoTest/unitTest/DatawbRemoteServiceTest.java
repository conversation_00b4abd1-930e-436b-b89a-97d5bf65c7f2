package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WsmAssembleHeaders;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.scan.PickListResultDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Mockito.anyString;

@PrepareForTest({HttpRemoteUtil.class, HttpRemoteService.class, HttpClientUtil.class,
        ConstantInterface.class, ServiceDataBuilderUtil.class,CommonUtils.class,InterfaceEnum.class,
        JacksonJsonConverUtil.class, JSON.class,MESHttpHelper.class,MicroServiceRestUtil.class})
public class DatawbRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private ConstantInterface constantInterface;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
    }

    @Test
    public void queryMaterialOrderNoByTaskNo(){
        PowerMockito.mockStatic(JSON.class,HttpClientUtil.class);
        List<PickListResultDTO> fr = DatawbRemoteService.queryMaterialOrderNoByTaskNo("fr");
        Assert.assertTrue(CollectionUtils.isEmpty(fr));
    }

    @Test
    public void getReelidInfoList() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        VReelidInfoDTO infoDTO = new VReelidInfoDTO();
        datawbRemoteService.getReelidInfoList(infoDTO);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        Assert.assertNull(datawbRemoteService.getReelidInfoList(infoDTO));
    }

    @Test
    public void getZteBarcodeInfo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        datawbRemoteService.getZteBarcodeInfo("123");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertNotNull(datawbRemoteService.getZteBarcodeInfo("123"));
    }

    @Test
    public void getItemNameByItemNo(){
        datawbRemoteService.getItemNameByItemNo(null);
        PowerMockito.mockStatic(ConstantInterface.class);
        Assert.assertNull(datawbRemoteService.getItemNameByItemNo("dd"));
    }


    @Test
    public void getItemInfoListBatch() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("11", 222);
        PowerMockito.mockStatic(HttpRemoteService.class, ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/boardFirstwarehouse/selectGetDateByProdplanNo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList = new ArrayList<>();
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO.setReplaceItemCode("replaceItemZNo");
        mtlRelatedItemsEntityDTOList.add(mtlRelatedItemsEntityDTO);
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(mtlRelatedItemsEntityDTOList);
                }})
        );
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://");

        List<String> itemCodeList = new ArrayList<>();
        itemCodeList.add("itemNo");
        Assert.assertNotNull(datawbRemoteService.getItemInfoListBatch(itemCodeList));
    }

    @Test
    public void querySemisBoxInfo() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("11", "222");
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/SemisBoxInfo/getSemisBoxInfoByApplyIds");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<SemisBoxInfoDTO> semisBoxInfoDTOS = new ArrayList<>();
        SemisBoxInfoDTO semisBoxInfoDTO = new SemisBoxInfoDTO();
        semisBoxInfoDTO.setApplyId(new Long(20220303));
        semisBoxInfoDTOS.add(semisBoxInfoDTO);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), anyMap())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(semisBoxInfoDTOS);
                }})
        );

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(JSON.toJSONString(semisBoxInfoDTOS));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(semisBoxInfoDTOS);
        List<Long> billNoList = new ArrayList<>();
        List<Long> billNos = new ArrayList<>();
        billNoList.add(new Long(20220303));
        Assert.assertNotNull(datawbRemoteService.querySemisBoxInfo(billNoList));
    }

    @Test
    public void queryBarSubmitInfo() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("11", "222");
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/barSubmitController/queryBarSubmitInfo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<BarSubmitDTO> barSubmitDTOS = new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setBillNo("20220303");
        barSubmitDTOS.add(barSubmitDTO);

        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), anyMap())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(barSubmitDTOS);
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(JSON.toJSONString(barSubmitDTOS));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(barSubmitDTOS);
        List<String> billNoList = new ArrayList<>();
        List<String> billNos = new ArrayList<>();
        billNoList.add("20220303");
        Assert.assertNotNull(datawbRemoteService.queryBarSubmitInfo(billNoList));
    }

    @Test
    public void getStItemBarcode() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("11", 222);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/boardFirstwarehouse/selectGetDateByProdplanNo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        Assert.assertNotNull(DatawbRemoteService.getStItemBarcode(map));
    }

    @Test
    public void assembleWriteBack() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/AssembleHeader/assembleWriteBack");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        AssemblyWriteBackDto assemblyWriteBackDto = new AssemblyWriteBackDto();
        Assert.assertNotNull(DatawbRemoteService.assembleWriteBack(assemblyWriteBackDto));
    }

    @Test
    public void getItemCode() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/AssembleHeader/assembleWriteBack");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<SnValidateDto> dtoList = new ArrayList<>();
        SnValidateDto snValidateDto = new SnValidateDto();
        snValidateDto.setSubSn("7778882201");
        dtoList.add(snValidateDto);
        Assert.assertNotNull(DatawbRemoteService.getItemCode(dtoList));
    }

    @Test
    public void generateTaskBarcode() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/basBarcodeInfo/generateTaskBarcode");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        TaskBarcodeQueryDTO taskBarcodeQueryDTO = new TaskBarcodeQueryDTO();
        taskBarcodeQueryDTO.setParentSn("778888");
        taskBarcodeQueryDTO.setBarcodeRightLimit(new BigDecimal("22"));
        Assert.assertNotNull(DatawbRemoteService.generateTaskBarcode(taskBarcodeQueryDTO));
    }


    @Test
    public void getByItemBarCode() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/basBarcodeInfo/generateTaskBarcode");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        WsmAssembleHeaders wsmAssembleHeaders = new WsmAssembleHeaders();
        wsmAssembleHeaders.setAssembleHeadersId(new BigDecimal("778888"));
        wsmAssembleHeaders.setItemCode("5515");
        Assert.assertNotNull(DatawbRemoteService.getByItemBarCode(wsmAssembleHeaders));
    }

    @Test
    public void writeToErp() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/zteWipMoveMtlTxnCtrl/writeErpTransaction");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setCommitedQty(new BigDecimal("888"));
        warehouseEntryInfo.setApplyNo("RK2325515");
        Assert.assertNotNull(DatawbRemoteService.writeToErp(warehouseEntryInfo));
    }


    @Test
    public void writeToErpAndUpdateImu() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/zteWipMoveMtlTxnCtrl/writeErpAndUpdateImu");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setCommitedQty(new BigDecimal("888"));
        warehouseEntryInfo.setApplyNo("RK2325515");
        List<Map<String, String>> boards = new ArrayList<>();
        Map<String, String> mapBoard = new HashMap<>();
        mapBoard.put("sn", "222");
        boards.add(mapBoard);
        DatawbRemoteService.writeToErpAndUpdateImu(warehouseEntryInfo, boards);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateImuForSubCard() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/zteWipMoveMtlTxnCtrl/writeErpAndUpdateImu");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        List<Map<String, String>> boards = new ArrayList<>();
        Map<String, String> mapBoard = new HashMap<>();
        mapBoard.put("sn", "222");
        boards.add(mapBoard);
        Assert.assertNotNull(DatawbRemoteService.updateImuForSubCard(boards));
    }

    @Test
    public void writeErpIdempotent() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/zteWipMoveMtlTxnCtrl/writeErpAndUpdateImu");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setCommitedQty(new BigDecimal("888"));
        warehouseEntryInfo.setApplyNo("RK2325515");
        DatawbRemoteService.writeErpIdempotent(warehouseEntryInfo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void onlyWriteErp() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/zteWipMoveMtlTxnCtrl/writeErpAndUpdateImu");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setCommitedQty(new BigDecimal("888"));
        warehouseEntryInfo.setApplyNo("RK2325515");
        Assert.assertNotNull(DatawbRemoteService.onlyWriteErp(warehouseEntryInfo));
    }

    @Test
    public void getList() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/basBarcodeInfo/getList");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        BasBarcodeInfoDTO basBarcodeInfoDTO = new BasBarcodeInfoDTO();
        basBarcodeInfoDTO.setItemBarcode("4515566");
        Assert.assertNull(DatawbRemoteService.getList(basBarcodeInfoDTO));
    }

    @Test
    public void selectMpsNetQty() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/selectMpsNetQty");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        Assert.assertNull(DatawbRemoteService.selectMpsNetQty("s4555南京"));
    }

    @Test
    public void getMovableQuantity() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getMovableQuantity");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        Assert.assertNull(DatawbRemoteService.getMovableQuantity("s4555南京"));
    }

    @Test
    public void updateImuBatch() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBatch");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<Map<String, String>> boards = new ArrayList<>();
        Map<String, String> mapBoard = new HashMap<>();
        mapBoard.put("sn", "222");
        boards.add(mapBoard);
        DatawbRemoteService.updateImuBatch(boards);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void deleteByBillNoAndProdplanIdBatch() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBatch");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<WarehouseEntryInfo> warehouseEntryInfoList = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setApplyNo("8822");
        warehouseEntryInfoList.add(warehouseEntryInfo);
        DatawbRemoteService.deleteByBillNoAndProdplanIdBatch(warehouseEntryInfoList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void updateImuAndDelBillBatch() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBatch");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<WarehouseEntryInfo> warehouseEntryInfoList = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setApplyNo("8822");
        warehouseEntryInfoList.add(warehouseEntryInfo);
        DatawbRemoteService.updateImuAndDelBillBatch(warehouseEntryInfoList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getErpSmReportCount() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getSmReportCount");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<String> tasks = new ArrayList<>();
        tasks.add("2222");
        Assert.assertNotNull(DatawbRemoteService.getErpSmReportCount(tasks, 2));
    }

    @Test
    public void getErpSmReport() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getSmReport");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<String> tasks = new ArrayList<>();
        tasks.add("2222");
        Assert.assertNull(DatawbRemoteService.getErpSmReport(tasks, 2, 1, 100));
    }

    @Test
    public void updateImuStatusBatch() throws Exception {
        List<WarehouseEntryInfo> entryList = new LinkedList<>();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(Mockito.any())).thenReturn("123");
        PowerMockito
                .when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"Success\"\n" +
                        "  },\n" +
                        "  \"bo\": [],\n" +
                        "  \"other\": null\n" +
                        "}");
        datawbRemoteService.updateImuStatusBatch(entryList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getBomVerByTaskNo() throws Exception {
        PDMProductMaterialResultDTO dto = new PDMProductMaterialResultDTO();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito
                .when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"Success\"\n" +
                        "  },\n" +
                        "  \"bo\": [],\n" +
                        "  \"other\": null\n" +
                        "}");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(Mockito.any())).thenReturn("123");
        Assert.assertNotNull(DatawbRemoteService.getBomVerByTaskNo(dto));
    }

    @Test
    public void insertZteLmsOrgTransInterBatch() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("11", 222);

        PowerMockito.mockStatic(HttpRemoteService.class, ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        Integer bo = 1;
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(bo);
                }})
        );
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://");

        List<String> itemCodeList = new ArrayList<>();
        itemCodeList.add("itemNo");
        List<ZteLmsOrgTransInterDTO> zteDTOList = new ArrayList<>();
        ZteLmsOrgTransInterDTO zteDTO = new ZteLmsOrgTransInterDTO();
        zteDTOList.add(zteDTO);

        Assert.assertNotNull(datawbRemoteService.insertZteLmsOrgTransInterBatch(zteDTOList));
    }


    @Test
    public void querySemisBoxInfo1() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        ServiceData<List<SemisBoxInfoDTO>> rt = new ServiceData<>();
        List<SemisBoxInfoDTO> list = new ArrayList<>();
        list.add(new SemisBoxInfoDTO());
        rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        rt.setBo(list);
        String msg = JSONObject.toJSONString(rt);

        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(msg);
        List<Long> billNoList = new ArrayList<>();
        Assert.assertNull(datawbRemoteService.querySemisBoxInfo(billNoList));
    }

    @Test
    public void getIsHasDirFlag() {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        list.add(pkCodeInfo);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("1");
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setCode(new RetCode("1", "1"));
                    }}));
            datawbRemoteService.getIsHasDirFlag(list);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList());
                    }}));
            Assert.assertNotNull(datawbRemoteService.getIsHasDirFlag(list));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void warehouseEntryInfoWriteBackStep() throws Exception {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setProdplanId("8899720");
        dto.setCommitedQty(new BigDecimal(2));
        dto.setStatus("2");
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        Assert.assertNotNull(DatawbRemoteService.warehouseEntryInfoWriteBackStep(dto));
    }

    @Test
    public void insertWmesMachineOnlineInfo() throws Exception {
        List<WmesMachineOnlineInfoDTO> wmesMachineOnlineInfoDTOS = new ArrayList<>();
        WmesMachineOnlineInfoDTO wmesMachineOnlineInfoDTO = new WmesMachineOnlineInfoDTO();
        wmesMachineOnlineInfoDTOS.add(wmesMachineOnlineInfoDTO);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getSmReport");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                }})
        );
        List<String> tasks = new ArrayList<>();
        tasks.add("2222");
        datawbRemoteService.insertWmesMachineOnlineInfo(wmesMachineOnlineInfoDTOS, true);

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(null);
        try {
            datawbRemoteService.insertWmesMachineOnlineInfo(wmesMachineOnlineInfoDTOS, false);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                    setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                }})
        );
        try {
            Assert.assertNotNull(datawbRemoteService.insertWmesMachineOnlineInfo(wmesMachineOnlineInfoDTOS, false));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void insertBatchSpmComDailyreport() throws Exception {
        List<SpmComDailyreportDTO> dtos = new ArrayList<>();
        SpmComDailyreportDTO dto1 = new SpmComDailyreportDTO();
        dtos.add(dto1);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getSmReport");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                }})
        );
        List<String> tasks = new ArrayList<>();
        tasks.add("2222");
        datawbRemoteService.insertBatchSpmComDailyreport(dtos, true);

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(null);
        try {
            datawbRemoteService.insertBatchSpmComDailyreport(dtos, false);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                    setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                }})
        );
        try {
            Assert.assertNotNull(datawbRemoteService.insertBatchSpmComDailyreport(dtos, false));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void snDataBack() throws Exception {
        List<WipScanHisExtraInfoDTO> list = new ArrayList<>();
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                    setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                }})
        );
        try {
            Assert.assertNotNull(datawbRemoteService.snDataBack(list));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void rollBackBarSubmitData() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        DatawbRemoteService.rollBackBarSubmitData("123");
        DatawbRemoteService.rollBackBarSubmitData(null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void queryItemNoAndNameBatch() {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        List<String> bomList = new LinkedList<>();
        bomList.add("11");
        datawbRemoteService.queryItemNoAndNameBatch(null);
        datawbRemoteService.queryItemNoAndNameBatch(bomList);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("ok");
        Assert.assertNull(datawbRemoteService.queryItemNoAndNameBatch(bomList));
    }

    @Test
    public void queryMaterialMessageBatch() {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        List<String> bomList = new LinkedList<>();
        bomList.add("11");
        datawbRemoteService.queryMaterialMessageBatch(null);
        datawbRemoteService.queryMaterialMessageBatch(bomList);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("ok");
        Assert.assertNull(datawbRemoteService.queryMaterialMessageBatch(bomList));
    }

    @Test
    public void getCmReportErpInfo() throws RouteException {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/erpdt/getSmReport");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        List<String> tasks = new ArrayList<>();
        tasks.add("2222");
        Assert.assertNull(DatawbRemoteService.getCmReportErpInfo(tasks, 2, 1));
    }


    @Test
    public void getReelidInfoListTest() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class,CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("123");

        VReelidInfoDTO infoDTO = new VReelidInfoDTO();

        String result="{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null," +
                "\"other\":\"test\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(),Mockito.anyMap(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(result);
        Assert.assertNull(datawbRemoteService.getReelidInfoList(infoDTO));
    }

    @Test
    public void getZteBarcodeInfoTest() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("123");

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null," +
                "\"other\":\"test\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(result);
        datawbRemoteService.getZteBarcodeInfo("111");

        result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"}," +
                "\"other\":\"test\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(result);
        Assert.assertNull(datawbRemoteService.getZteBarcodeInfo("111"));
    }
    @Test
    public void getListByTaskList(){
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("123");

        String result="{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null," +
                "\"other\":\"test\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(),Mockito.anyMap(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(result);

        Assert.assertNull(datawbRemoteService.getListByTaskList(Collections.singletonList("111")));
    }
    @Test
    public void getItemNameByItemNoTest() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class,CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        datawbRemoteService.getItemNameByItemNo("");

        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(null)));
        try {
            datawbRemoteService.getItemNameByItemNo("111");
        } catch (Exception e) {
            Assert.assertEquals( MessageId.INTERFACE_ERROR, e.getMessage());
        }

        ServiceData serviceData = new ServiceData();
        RetCode code = new RetCode();
        code.setCode(RetCode.SERVERERROR_CODE);
        serviceData.setCode(code);
        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));
        try {
            datawbRemoteService.getItemNameByItemNo("111");
        } catch (Exception e) {
            Assert.assertEquals( MessageId.INTERFACE_ERROR, e.getMessage());
        }

        code.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(code);
        serviceData.setBo("1111");
        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                        any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(serviceData)));
        Assert.assertNull(datawbRemoteService.getItemNameByItemNo("111"));
    }
}
