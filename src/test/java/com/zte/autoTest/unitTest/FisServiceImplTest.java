package com.zte.autoTest.unitTest;

import com.zte.application.impl.FisServiceImpl;
import com.zte.client.fis.FisClient;
import com.zte.common.model.MessageId;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

@PrepareForTest({BasicsettingRemoteService.class})
public class FisServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    FisServiceImpl service;

    @Mock
    private FisClient fisClient;


    @Test
    public void deleteTestData() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);

        List<SysLookupValuesDTO> settings = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(11490006));
        sysLookupValuesDTO.setAttribute2("http://10.18.178.167:9080/fis/services/FirstRepairProcessData?wsdl");
        settings.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSettings(anyObject())).thenReturn(settings);

        // 测试1
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOS = new ArrayList<>();
        try {
            service.deleteTestData(null);
            service.deleteTestData(pmRepairRcvDetailDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_DELETE_FIS_ERROR, e.getMessage());
        }

        // 测试2
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setSn("730638900033");
        pmRepairRcvDetailDTO.setBuilding("11490006");
        pmRepairRcvDetailDTOS.add(pmRepairRcvDetailDTO);
        try {
            service.deleteTestData(pmRepairRcvDetailDTOS);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_DELETE_FIS_ERROR, e.getMessage());
        }

        // 测试3
        sysLookupTypesDTO.setLookupMeaning("Y");
        try {
            service.deleteTestData(pmRepairRcvDetailDTOS);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        // 测试4
        pmRepairRcvDetailDTO.setFromStation("单板测试段");
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        sysLookupTypesDTO1.setLookupMeaning("单板测试段");
        sysLookupTypesDTO1.setAttribute1("1,2，3;4；5,");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        PowerMockito.when(fisClient.deleteDataFromFIS(anyString(), anyString(), anyString())).thenReturn(true);
        service.deleteTestData(pmRepairRcvDetailDTOS);


        // 测试5
        sysLookupTypesDTO.setAttribute2("Y");
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO1 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO1.setSn("730638900034");
        pmRepairRcvDetailDTO1.setFromStation("单板测试段");
        pmRepairRcvDetailDTO1.setBuilding("11490006");
        pmRepairRcvDetailDTOS.add(pmRepairRcvDetailDTO1);
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO2 = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO2.setSn("730638900035");
        pmRepairRcvDetailDTO2.setFromStation("SMT段");
        pmRepairRcvDetailDTO2.setBuilding("11490006");
        pmRepairRcvDetailDTOS.add(pmRepairRcvDetailDTO2);
        Assert.assertTrue(service.deleteTestData(pmRepairRcvDetailDTOS));

    }
}
