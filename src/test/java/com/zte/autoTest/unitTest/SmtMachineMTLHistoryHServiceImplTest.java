package com.zte.autoTest.unitTest;

import com.zte.application.impl.SmtMachineMTLHistoryHServiceImpl;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;

public class SmtMachineMTLHistoryHServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    SmtMachineMTLHistoryHServiceImpl service;

    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;

    @Test
    public void getListByDetail() throws Exception{
        List<SmtMachineMTLHistoryH> list = new ArrayList<>();
        SmtMachineMTLHistoryH info = new SmtMachineMTLHistoryH();
        info.setAttr1("11");
        PowerMockito.when(smtMachineMTLHistoryHRepository.getListByDetail(Mockito.anyMap())).thenReturn(list);
        Assert.assertNotNull(service.getListByDetail(new HashMap(), "", "", 1L, 1L));
    }
}