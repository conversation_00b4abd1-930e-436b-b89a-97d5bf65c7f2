package com.zte.autoTest.unitTest;

import com.zte.common.utils.Constant;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.aop.IMESDataSourceAspect;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.util.PowerBaseTestCase;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-10-08 13:38
 */

@PrepareForTest({RequestContextHolder.class, MESHttpHelper.class, RequestHeaderUtil.class,
        DatabaseContextHolder.class})
public class IMESDataSourceAspectTest extends PowerBaseTestCase {
    @InjectMocks
    private IMESDataSourceAspect imesDataSourceAspect;
    @Mock
    JoinPoint point;
    @Mock
    private MethodSignature signature;
    @Mock
    private Method m;
    @Mock
    private Object target;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private HttpServletRequest request;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(RequestHeaderUtil.class);
        PowerMockito.mockStatic(RequestContextHolder.class);
        PowerMockito.mockStatic(DatabaseContextHolder.class);
        PowerMockito.when(point.getTarget()).thenReturn(target);
        PowerMockito.when(point.getSignature()).thenReturn(signature);
        PowerMockito.when(signature.getMethod()).thenReturn(m);
    }

    @Test
    public void setDataSourceKey() {
        imesDataSourceAspect.setDataSourceKey(point);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(SysConst.HTTP_HEADER_X_FACTORY_ID.toLowerCase(), "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        imesDataSourceAspect.setDataSourceKey(point);

        PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.any()))
                .thenReturn("ok");
        imesDataSourceAspect.setDataSourceKey(point);

        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        PowerMockito.when(RequestContextHolder.getRequestAttributes()).thenReturn(servletRequestAttributes);
        imesDataSourceAspect.setDataSourceKey(point);

        PowerMockito.when(request.getHeader(Mockito.any())).thenReturn("54");
        imesDataSourceAspect.setDataSourceKey(point);

        PowerMockito.when(request.getHeader(Mockito.any())).thenReturn(null);
        headerMap.put(SysConst.HTTP_HEADER_X_FACTORY_ID.toLowerCase(), "");
        PowerMockito.when(request.getRequestURI()).thenReturn("/jiji/jiji/jiji");
        PowerMockito.when(request.getRequestURL()).thenReturn(new StringBuffer("http://cs.apimes.zte.com.cn/jiji/jiji/jiji"));
        imesDataSourceAspect.setDataSourceKey(point);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


}
