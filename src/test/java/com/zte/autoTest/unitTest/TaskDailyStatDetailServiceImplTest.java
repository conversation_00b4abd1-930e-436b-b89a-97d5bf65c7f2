package com.zte.autoTest.unitTest;

import com.zte.application.impl.TaskDailyStatDetailServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.TaskDailyStatDetailRepository;
import com.zte.domain.model.TaskDailyStatHeadRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-23 11:11
 */
@PrepareForTest({})
public class TaskDailyStatDetailServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private TaskDailyStatDetailServiceImpl taskDailyStatDetailService;
    @Mock
    private TaskDailyStatDetailRepository taskDailyStatDetailRepository;
    @Mock
    private TaskDailyStatHeadRepository taskDailyStatHeadRepository;

    @Before
    public void init(){

    }

    @Test
    public void deleteYesterdayConciseDaily() {
        List<String> prodPlanIds = new LinkedList<>();
        Integer preDay = 1;
        taskDailyStatDetailService.deleteYesterdayConciseDaily(prodPlanIds, preDay);

        List<String> list = new LinkedList<>();
        list.add("1");
        PowerMockito.when(taskDailyStatHeadRepository.selectHeadIdByStatisticDate(Mockito.any(), Mockito.any()))
                .thenReturn(list);
        taskDailyStatDetailService.deleteYesterdayConciseDaily(prodPlanIds, preDay);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

}
