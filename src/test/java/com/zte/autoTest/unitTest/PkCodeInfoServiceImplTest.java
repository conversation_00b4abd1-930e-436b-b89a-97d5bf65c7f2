package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.impl.PkCodeInfoServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;
@PrepareForTest({BasicsettingRemoteService.class,ProductionDeliveryRemoteService.class,HttpRemoteUtil.class, ConstantInterface.class})
public class PkCodeInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PkCodeInfoServiceImpl service;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
    }

    @Test
    public void savePkCodeInfoForPcb() throws Exception{
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setProductTask("7777888");
        pkCodeInfo.setItemCode("item1");
        pkCodeInfo.setItemQty(new BigDecimal("5"));
        Assert.assertNotNull(service.savePkCodeInfoForPcb(pkCodeInfo));
    }

    @Test
    public void getNextPkCode() {
        PowerMockito.when(pkCodeInfoRepository.getNextPkCode(any())).thenReturn(Lists.newArrayList(new PkCodeInfo() {{
            setPkCode("1");
        }}));
        Assert.assertNotNull(service.getNextPkCode(Lists.newArrayList(new SmtMachineMaterialMouting() {{
            setNextReelRowid("1");
        }})));
    }


    @Test
    public void getPkCodeInfo() {
        Assert.assertNull(service.getPkCodeInfo(new HashMap<>(), "test"));
    }

    @Test
    public void dipContainerSplit() {
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setLpn("oldLpn");
        pkCodeInfo.setNewLpn("newLpn");
        pkCodeInfo.setNewLpnQty("1");
        try {
            service.dipContainerSplit(pkCodeInfo);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<SmtMachineMaterialMouting> machineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        machineMaterialMoutings.add(smtMachineMaterialMouting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(machineMaterialMoutings);
        try {
            service.dipContainerSplit(pkCodeInfo);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(new ArrayList() {{
            add(null);
        }});
        Assert.assertThrows(NullPointerException.class, () -> service.dipContainerSplit(pkCodeInfo));
        try {
            service.dipContainerSplit(new PkCodeInfo());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setIsLeadWithChin() throws Exception {
        PkCodeInfoDTO pkCodeInfo = new PkCodeInfoDTO();
        pkCodeInfo.setPkCode("0000140751");
        pkCodeInfo.setFactoryId(new BigDecimal(52));
        pkCodeInfo.setIsLead("30");
        List<PkCodeInfoDTO> printInfoList =new ArrayList<PkCodeInfoDTO>();
        printInfoList.add(pkCodeInfo);

        List<SysLookupValuesDTO> listSys =new ArrayList<SysLookupValuesDTO>();
        SysLookupValuesDTO sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("2");
        sysLookupValuesDTO.setAttribute1("30");
        sysLookupValuesDTO.setLookupType(new BigDecimal(1036));
        sysLookupValuesDTO.setDescriptionChin("无铅");
        listSys.add(sysLookupValuesDTO);

        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(listSys);
        service.setIsLeadWithChin(printInfoList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void splitContainer() throws Exception {
        Assert.assertThrows(NullPointerException.class, () -> service.splitContainer(new PkCodeInfo()));
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<SmtMachineMaterialMouting> machineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        machineMaterialMoutings.add(smtMachineMaterialMouting);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(any())).thenReturn(machineMaterialMoutings);
        ServiceData serviceData = new ServiceData();
        List<ContainerContentInfoDTO> containerContentInfoDTOList = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setItemCode("itemCode1");
        containerContentInfoDTO.setContentQty(new BigDecimal("3"));
        containerContentInfoDTO.setCreateDate(new Date());
        containerContentInfoDTO.setLpn("lpn1");
        ContainerContentInfoDTO containerContentInfoDTO1 = new ContainerContentInfoDTO();
        containerContentInfoDTO1.setCreateDate(new Date());
        containerContentInfoDTO1.setItemCode("itemCode1");
        containerContentInfoDTO1.setLpn("lpn2");
        containerContentInfoDTO1.setContentQty(new BigDecimal("3"));
        containerContentInfoDTOList.add(containerContentInfoDTO);
        containerContentInfoDTOList.add(containerContentInfoDTO1);
        serviceData.setBo(containerContentInfoDTOList);
        String str = JacksonJsonConverUtil.beanToJson(serviceData);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfo(anyMap())).thenReturn(jsonNode);

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        pkCodeInfo1.setItemQty(new BigDecimal("2"));
        pkCodeInfo1.setRawQty(new BigDecimal("2"));

        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        pkCodeInfo2.setItemQty(new BigDecimal("2"));
        pkCodeInfo2.setRawQty(new BigDecimal("2"));

        pkCodeInfoList.add(pkCodeInfo1);
        pkCodeInfoList.add(pkCodeInfo2);

        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setFactoryId(new BigDecimal("55"));
        pkCodeInfo.setEmpNo("102204445");
        pkCodeInfo.setLpn("oldLpn");
        pkCodeInfo.setNewLpn("newLpn");
        pkCodeInfo.setNewLpnQty("3");
        try {
            service.splitContainer(pkCodeInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PK_CODE_INFO_NOT_FOUND, e.getMessage());
        }
        PowerMockito.when(pkCodeInfoRepository.getPickCodeList(any())).thenReturn(pkCodeInfoList);
        List<String> reelidList = new ArrayList<>();
        reelidList.add("reelid1");

        PowerMockito.when(centerfactoryRemoteService.createReelIds(any(), any(), any())).thenReturn(reelidList);


        service.splitContainer(pkCodeInfo);
        pkCodeInfo.setNewLpnQty("1");
        service.splitContainer(pkCodeInfo);
        pkCodeInfo.setNewLpnQty("2");
        service.splitContainer(pkCodeInfo);


        PowerMockito.when(ProductionDeliveryRemoteService.getContainerContentInfo(anyMap()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(Lists.newArrayList(
                                    new ContainerContentInfoDTO() {{
                                        setItemCode("1");
                                    }},
                                    new ContainerContentInfoDTO() {{
                                        setItemCode("2");
                                    }}));
                        }})
                ));
        try {
            service.splitContainer(pkCodeInfo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.COMBINED_MATERIALS_ARE_NOT_ALLOWED_TO_BE_SPLIT, e.getMessage());
        }String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void extractedSync() {
        service.extractedSync(Lists.newArrayList(new PkCodeInfo() {{
                    setPkCode("1");
                    setLastUpdatedDate(new Date());
                }}),
                Lists.newArrayList(new PkCodeInfo() {{
                    setPkCode("1");
                    setLastUpdatedDate(new Date());
                }}),
                Lists.newArrayList("1"),
                Lists.newArrayList(new PkCodeInfo() {{
                    setPkCode("1");
                    setLastUpdatedDate(new Date());
                }}),
                new PkCodeInfo() {{
                    setPkCode("1");
                    setLastUpdatedDate(new Date());
                }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void calculate() {
        try {
            CalculatePkCodeDTO calculatePkCodeDTO = new CalculatePkCodeDTO();
            calculatePkCodeDTO.setNewLpn("");
            calculatePkCodeDTO.setNewLpnQty(new BigDecimal(1));
            calculatePkCodeDTO.setContainerContentInfoDTOList(Lists.newArrayList());
            calculatePkCodeDTO.setPkCodeInfoMap(new HashMap<>());
            calculatePkCodeDTO.setNeedInsertContainerContentInfoDTOList( Lists.newArrayList());
            calculatePkCodeDTO.setNeedUpdateContainerContentInfoDTOList( Lists.newArrayList());
            calculatePkCodeDTO.setNeedUpdatePkCodeInfoList( Lists.newArrayList());
            calculatePkCodeDTO.setNeedInsertPkCodeInfoList( Lists.newArrayList());
            calculatePkCodeDTO.setNeedDeleteSmtMachineMaterialMoutingList( Lists.newArrayList());
            calculatePkCodeDTO.setEmpNo("");

            service.calculate(calculatePkCodeDTO);
            calculatePkCodeDTO.setContainerContentInfoDTOList(Lists.newArrayList(new ContainerContentInfoDTO()));
            service.calculate(calculatePkCodeDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NEW_LPN_QTY_MUST_LESS_OLD_LPN_QTY, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void syncPkCodeInfo() throws Exception {
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setPkCode("123");
        List<PkCodeInfo> centerReelIds = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        a1.setPkCode("123");
        centerReelIds.add(a1);
        PowerMockito.when(centerfactoryRemoteService.getCenterPkCodeListOrigin(Mockito.anyString()))
                .thenReturn(centerReelIds);

        service.syncPkCodeInfo(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void syncPkCode() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{
                    setBo(new Page(1001, 100){{setRows(Lists.newArrayList(new PkCodeInfo()));}});
                }}));
        Assert.assertNotNull(service.syncPkCode(new PkCodeInfoSyncQueryDTO()));
    }
    @Test
    public void getIssueByProductTask() {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setSupplerCode("123");
        list.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getIssueByProductTask(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getIssueByProductTask("123"));
    }

	@Test
	public void getList() throws Exception {
		List<PkCodeInfo> list = new ArrayList<>();
		PkCodeInfoDTO dto = new PkCodeInfoDTO();
		dto.setPkCode("test");
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setPkCode("test");
		list.add(dto1);
		PowerMockito.when(pkCodeInfoRepository.getListByPkCodeAndParentPk(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getList(dto));
	}
	@Test
	public void getPage() throws Exception {
		Page<PkCodeInfoDTO> queryPage = new Page<>();
		List<PkCodeInfo> list = new ArrayList<>();
		PkCodeInfoDTO dto = new PkCodeInfoDTO();
		dto.setPage(1L);
		dto.setRows(10L);
		dto.setPkCode("test");
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setPkCode("test");
		list.add(dto1);
		queryPage.setParams(dto);
		queryPage.setCurrent(dto.getPage().intValue());
		queryPage.setPageSize(dto.getRows().intValue());
		PowerMockito.when(pkCodeInfoRepository.getPageByPkCodeAndParentPk(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getPage(dto));
	}

	@Test
	public void getListTwo() throws Exception {
		List<PkCodeInfo> list = new ArrayList<>();
		PkCodeInfoDTO dto = new PkCodeInfoDTO();
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setPkCode("test");
		list.add(dto1);
		PowerMockito.when(pkCodeInfoRepository.getList(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getList(dto));
	}
	@Test
	public void getPageTwo() throws Exception {
		Page<PkCodeInfoDTO> queryPage = new Page<>();
		List<PkCodeInfo> list = new ArrayList<>();
		PkCodeInfoDTO dto = new PkCodeInfoDTO();
		dto.setPage(1L);
		dto.setRows(10L);
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setPkCode("test");
		list.add(dto1);
		queryPage.setParams(dto);
		queryPage.setCurrent(dto.getPage().intValue());
		queryPage.setPageSize(dto.getRows().intValue());
		PowerMockito.when(pkCodeInfoRepository.getPage(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getPage(dto));
	}
	@Test
	public void transLead() throws Exception {
		List<SysLookupTypesDTO> valueByTypeCodes = new LinkedList<>();
		SysLookupTypesDTO sysLookupValuesDTO = new SysLookupTypesDTO();

		sysLookupValuesDTO.setAttribute1("30");
		sysLookupValuesDTO.setDescriptionChin("TEST");
		valueByTypeCodes.add(sysLookupValuesDTO);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOK_UP_TYPE_LEAD)).thenReturn(valueByTypeCodes);
		List<PkCodeInfo> list = new ArrayList<>();
		PkCodeInfoDTO dto = new PkCodeInfoDTO();
		dto.setIsLead("30");
		PkCodeInfo dto1 = new PkCodeInfo();
		dto1.setPkCode("test");
		list.add(dto1);
        Assert.assertNotNull(service.transLead(list));
	}

}