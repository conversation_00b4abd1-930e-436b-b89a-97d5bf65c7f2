package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineDistributeScanService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.impl.SmtMachineMTLHistoryLServiceImpl;
import com.zte.application.impl.StItemBarcodeServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryL;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.FeederInsertExtractionDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO;
import com.zte.interfaces.dto.SmtMachineMTLHistoryWorkLoadDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

@PrepareForTest({BasicsettingRemoteService.class, ObtainRemoteServiceDataUtil.class,
        PlanscheduleRemoteService.class, CommonUtils.class, RedisHelper.class, MESHttpHelper.class, SpringContextUtil.class})
public class SmtMachineMTLHistoryLServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    SmtMachineMTLHistoryLServiceImpl service;

    @Mock
    BSmtBomDetailService bSmtBomDetailService;

    @Mock
    SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;
    @Mock
    private StItemBarcodeServiceImpl stItemBarcodeService;
    @Mock
    PkCodeInfoRepository pkCodeInfoRepository;
    @Mock
    RedisTemplate<String, Object> redisTemplate;
    @Mock
    ValueOperations<String, Object> valueOperations;
    @Mock
    SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;
    @Mock
    SmtMachineDistributeScanService smtMachineDistributeScanService;
    @Mock
    SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    SmtMachineMaterialPrepareService smtMachineMaterialPrepareService;
    @Mock
    ImesPDACommonService imesPDACommonService;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    PkCodeInfoService pkCodeInfoService;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Test
    public void getNoVirtualMountType() throws Exception {
        service.setNoVirtualMountType(new BSmtBomDetailDTO(), new SmtMachineMTLHistoryL());
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");}}));
        service.setNoVirtualMountType(new BSmtBomDetailDTO(), new SmtMachineMTLHistoryL(){{setMountType("1");}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertSmtMachineMTLHistoryAllAfter() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
                .thenReturn(Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");}}));
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(any()))
                .thenReturn(Lists.newArrayList(new BSmtBomDetail(){{setMaterialTray("1");}}));
        service.insertSmtMachineMTLHistoryAllAfter(new SmtMachineMTLHistoryL(){{setMountType("1");}}, "1");
        service.insertSmtMachineMTLHistoryAllAfter(new SmtMachineMTLHistoryL(){{setMountType("5");}}, "1");
        service.insertSmtMachineMTLHistoryAllAfter(new SmtMachineMTLHistoryL(){{setMountType("8");}}, "1");
        List<SmtLocationInfoDTO> smtLocationInfoDTOList = new ArrayList<>();
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setMachineNo("1");
        smtLocationInfoDTO.setModuleNo("2");
        smtLocationInfoDTO.setLocationNo("3");
        smtLocationInfoDTOList.add(smtLocationInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getSmtLocationByLineCodeAndLocationType(anyString(), anyString())).thenReturn(smtLocationInfoDTOList);
        List<BSmtBomDetail> dList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setMaterialTray("1");
        bSmtBomDetail.setMachineNo("1");
        bSmtBomDetail.setModuleNo("2");
        bSmtBomDetail.setLocationNo("3");
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        dList.add(bSmtBomDetail);
        dList.add(bSmtBomDetail1);
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(any())).thenReturn(dList);
        service.insertSmtMachineMTLHistoryAllAfter(new SmtMachineMTLHistoryL(){{setMountType("8");}}, "1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertSmtMachineMTLHistoryLSelective() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        service.insertSmtMachineMTLHistoryLSelective(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void selectSmtMachineMTLHistoryLById() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        Assert.assertNull(service.selectSmtMachineMTLHistoryLById(record));
    }

    @Test
    public void insertSmtMachineMTLHistoryL() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        Assert.assertNotNull(service.insertSmtMachineMTLHistoryL(record));
    }

    @Test
    public void deleteSmtMachineMTLHistoryLById() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        service.deleteSmtMachineMTLHistoryLById(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateSmtMachineMTLHistoryLById() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        Assert.assertNotNull(service.updateSmtMachineMTLHistoryLById(record));
    }

    @Test
    public void updateSmtMachineMTLHistoryLByIdSelective() {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        service.updateSmtMachineMTLHistoryLByIdSelective(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getList() throws Exception {
        service.getList(new HashMap<>(), "123", "123", 1L, 2L);
        service.getSmtMachineHisDetailAll(new HashMap<>(), "123", "123", 1L, 2L);
        service.getCount(new HashMap<>());
        service.isQcReCheck("123","123","123","123","2");
        SmtMachineMTLHistoryLDTO record = new SmtMachineMTLHistoryLDTO();
        record.setItemCode("123");
        record.setLineCode("123");
        record.setModuleNo("123");
        record.setLocationNo("123");
        record.setWorkOrder("123");
        record.setMountType(Constant.MOUNT_TYPE_RECEIVE);
        record.setObjectId("2");
        service.isReceive(record);
        service.getCanQcReCheckItem(new SmtMachineMTLHistoryLDTO());
        List<SmtMachineMTLHistoryL> list = new ArrayList<>();
        SmtMachineMTLHistoryL smtMachineMTLHistoryL = new SmtMachineMTLHistoryL();
        smtMachineMTLHistoryL.setSourceBatchCode("test");
        list.add(smtMachineMTLHistoryL);
        PowerMockito.when(smtMachineMTLHistoryLRepository.getSmtMachineHisDetailAll(anyMap())).thenReturn(list);
        Map<String, Map<String, String>> colMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("style", "test1111");
        colMap.put("test", map);
        PowerMockito.when(stItemBarcodeService.getItemSupName(Mockito.any())).thenReturn(colMap);


        Assert.assertNotNull(service.getSmtMachineHisDetailAll(new HashMap<>(), "123", "123", 1L, 2L));

    }

    @Test
    public void getWorkloadList() throws Exception {
        HashMap<String, Object> objectHashMap = new HashMap<String,Object>();
        objectHashMap.put("factoryId", "52");
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        List<SmtMachineMTLHistoryWorkLoadDTO> list = new LinkedList<>();
        SmtMachineMTLHistoryWorkLoadDTO a1 = new SmtMachineMTLHistoryWorkLoadDTO();
        a1.setLineCode("123");
        list.add(a1);
        PowerMockito.when(smtMachineMTLHistoryLRepository.getWorkloadList(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getWorkloadList(objectHashMap, "123", "123", 1L, 2L));
    }

    @Test
    public void getTimeRange() throws Exception {
        SmtMachineMTLHistoryLDTO dto = new SmtMachineMTLHistoryLDTO();
        SmtMachineMTLHistoryL entity = new SmtMachineMTLHistoryL();
        dto.setCreateDate(new Date());
        dto.setLastUpdatedDate(new Date());
        entity.setCreateDate(new Date());
        entity.setLastUpdatedDate(new Date());
        // 根据查询条件查询时间范围
        PowerMockito.when(smtMachineMTLHistoryLRepository.getMinAndMaxCreateDate(Mockito.any()))
                .thenReturn(entity);
        Assert.assertNotNull(service.getTimeRange(dto));
    }

    @Test
    public void saveDistributionInfo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        SmtMachineMTLHistoryLDTO hisRecord = new SmtMachineMTLHistoryLDTO();
        hisRecord.setWorkOrder("123");
        hisRecord.setOtherWorkOrder("123");
        hisRecord.setLineCode("123");
        hisRecord.setRelatedWorkOrder("123");
        hisRecord.setRelatedLine("123");
        hisRecord.setObjectId("1234");

        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        record.setObjectId("1234");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.any())).thenReturn(true);
        List<PkCodeInfo> pkCodeList = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        a1.setPkCode("1234");
        pkCodeList.add(a1);
        PowerMockito.when(pkCodeInfoRepository.getList(Mockito.any())).thenReturn(pkCodeList);

        Assert.assertNotNull(service.saveDistributionInfo(hisRecord));
    }

    @Test
    public void equalMaterialExceptErrorSkip() throws  Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic planBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(planBasic);
        Assert.assertTrue(service.equalMaterialExceptErrorSkip("123","123"));
    }

    @Test
    public void handleFeederExtractionOperationTest1() throws Exception {
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("");
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.MODULE_NO_EMPTY, e.getMessage());
        }
        dto.setLocationNo("test");
        dto.setLineName("");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NAME_NOT_NULL, e.getMessage());

        }
        dto.setLocationNo("");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_NO_EMPTY, e.getMessage());
        }
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("");
        dto.setModuleNo("test");
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.FEEDER_NO_IS_NULL, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleFeederExtractionOperationTest2() throws Exception {
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        // 线体打桩
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(null);
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NAME_NOT_EXISTING, e.getMessage());
        }
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("smt-cs1");
        // 线体打桩
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NOT_SUPPORT_INTELLIGENT_FEEDER, e.getMessage());
        }
        cfLine.setLineCode("smt-cs1");
        cfLine.setIntelligentFeederFlag("N");
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.LINE_NOT_SUPPORT_INTELLIGENT_FEEDER, e.getMessage());
        }
        cfLine.setIntelligentFeederFlag("Y");
        // redis打桩
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(false);
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {}
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        List<SmtMachineMaterialMouting> matMoutingList = new ArrayList<>();
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        entity.setLocationNo("test");
        entity.setLineName("test");
        entity.setFeederNo("test");
        entity.setModuleNo("test");
        entity.setEnabledFlag("Y");
        entity.setMachineMaterialMoutingId("dsadasd");
        matMoutingList.add(entity);
        PowerMockito.when(smtMachineMaterialMoutingService.getList(any(), any(), any())).thenReturn(null);
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.FEEDER_NOT_EXIST_MOUTING, e.getMessage());
        }
        PowerMockito.when(smtMachineMaterialMoutingService.getList(any(), any(), any())).thenReturn(matMoutingList);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(any())).thenReturn(null);
        try {
            service.handleFeederExtractionOperation(dto);
        }catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void handleFeederInsertOperationTest() throws Exception {
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("smt-cs1");
        cfLine.setIntelligentFeederFlag("Y");
        // 线体打桩
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
        // redis打桩
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        // 备料和mouting同时为空。
        PowerMockito.when(smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(any())).thenReturn(null);
        PowerMockito.when(smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederNo(any())).thenReturn(null);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PREPARE_INFO_OF_FEEDER_NOT_EXIST, e.getMessage());
        }
        // matMoutingList 为空， prepare不为空。正常为数据保存类型0
        List<SmtMachineMaterialPrepare> prePares = new ArrayList<>();
        PowerMockito.when(smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederNo(any())).thenReturn(prePares);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PREPARE_INFO_OF_FEEDER_NOT_EXIST, e.getMessage());
        }
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setLocationNo("test");
        prepare.setLineCode("smt-cs1");
        prepare.setModuleNo("test1");
        prePares.add(prepare);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_NOT_MATCH_PREPARE_DATA, e.getMessage());
        }
        prePares.add(prepare);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.FEEDER_HAS_MULTIPLE_RECORD, e.getMessage());
        }
        prePares.remove(prePares.size() - 1);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_NOT_MATCH_PREPARE_DATA, e.getMessage());
        }
        prepare.setModuleNo("test");
        prepare.setWorkOrder("work");
        prepare.setObjectId("123");
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(null);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.REEL_ID_NOT_REGISTER, e.getMessage());
        }
        List<PkCodeInfo> pkCodeInfos = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getList(any())).thenReturn(pkCodeInfos);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.REELID_PRODPLANID_IS_NULL, e.getMessage());
        }
        pkCodeInfo.setProductTask("7112233");
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.WORK_ORDER_NO_OF_PREPARE_ERROR, e.getMessage());
        }
        prepare.setWorkOrder("7112343-SMT-A");
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_OF_REELID_NOT_MATCH_PREPARE, e.getMessage());
        }
        prepare.setWorkOrder("7112233-SMT-A");
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(any())).thenReturn(null);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.REELID_OF_PRODPLANID_IS_NULL, e.getMessage());
        }
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("12345");
        psTasks.add(psTask);
        // 重新打桩
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(any())).thenReturn(psTasks);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
        String returnMsg = "123";
        PowerMockito.when(imesPDACommonService.avlCheckEvent(any(), any(), anyString())).thenReturn(returnMsg);
        Assert.assertThrows(NullPointerException.class, () -> service.handleFeederInsertOperation(dto));
        String returnMsg1 = "";
        PowerMockito.when(imesPDACommonService.avlCheckEvent(any(), any(), anyString())).thenReturn(returnMsg1);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(any())).thenReturn(null);
        Assert.assertThrows(NullPointerException.class, () -> service.handleFeederInsertOperation(dto));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleFeederInsertOperationTest1() throws Exception{
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("smt-cs1");
        cfLine.setIntelligentFeederFlag("Y");

        // 线体打桩
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
        // redis打桩
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        // 请求头打桩
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        // matMoutingList 不为空， prepare为空。
        List<SmtMachineMaterialMouting> matMoutings = new ArrayList<>();
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        entity.setLocationNo("test");
        entity.setLineName("test");
        entity.setFeederNo("test");
        entity.setModuleNo("test");
        entity.setEnabledFlag("Y");
        entity.setMachineMaterialMoutingId("dsadasd");
        entity.setLastUpdatedDate(new Date());
        matMoutings.add(entity);
        // 打桩，
        PowerMockito.when(smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(any())).thenReturn(matMoutings);
        // 类型为 2，因为feederNo是和dto相同
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertNull( e.getMessage());
        }
        // 设置feeder和传入不相同，则会继续判断是否是类型1，
        entity.setFeederNo("test1");
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.PREPARE_INFO_OF_FEEDER_NOT_EXIST, e.getMessage());
        }
        SmtMachineMaterialMouting entity1 = new SmtMachineMaterialMouting();
        entity1.setLocationNo("test");
        entity1.setLineName("test");
        entity1.setFeederNo("test");
        entity1.setModuleNo("test");
        entity1.setEnabledFlag("N");
        entity1.setMachineMaterialMoutingId("dsadasd");
        entity1.setLastUpdatedDate(new Date());
        matMoutings.add(entity1);
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals("站位存在有效机台在用", e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleFeederInsertOperationTest2() throws Exception{
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        dto.setLocationNo("test");
        dto.setLineName("test");
        dto.setFeederNo("test");
        dto.setModuleNo("test");
        CFLine cfLine = new CFLine();
        cfLine.setLineCode("smt-cs1");
        cfLine.setIntelligentFeederFlag("Y");

        // 线体打桩
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLine);
        // redis打桩
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        // 请求头打桩
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        // mouting表不为空，prepare表也不为空。

        List<SmtMachineMaterialMouting> matMoutings = new ArrayList<>();
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        entity.setLocationNo("test");
        entity.setLineName("test");
        entity.setFeederNo("test");
        entity.setModuleNo("test");
        entity.setEnabledFlag("Y");
        entity.setMachineMaterialMoutingId("dsadasd");
        entity.setLastUpdatedDate(new Date());
        matMoutings.add(entity);
        // mouting打桩，
        PowerMockito.when(smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(any())).thenReturn(matMoutings);
        List<SmtMachineMaterialPrepare> prePares = new ArrayList<>();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setLocationNo("test");
        prepare.setLineCode("smt-cs1");
        prepare.setModuleNo("test");
        prePares.add(prepare);
        // prepare打桩，
        PowerMockito.when(smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederNo(any())).thenReturn(prePares);
        entity.setWorkOrder("7112233-SMT");
        prepare.setWorkOrder("7112233-SMT");
        entity.setMachineNo("test");
        prepare.setMachineNo("test");
        entity.setObjectId("123");
        prepare.setObjectId("321");
        entity.setFeederNo("11111");
        prepare.setFeederNo("22222");
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals("站位存在有效机台在用", e.getMessage());
        }
        prepare.setObjectId("123");
        try {
            service.handleFeederInsertOperation(dto);
        }catch (Exception e) {
            Assert.assertEquals("站位存在有效机台在用", e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void isQcReCheck() {
        PowerMockito.when(smtMachineMTLHistoryLRepository.getLineCount(any())).thenReturn(0L);
        boolean result = service.isQcReCheck("1","2","3", "4", "5");
        Assert.assertEquals(false, result);
    }

    @Test
    public void handleFeederInsertErrorInfoTest() throws Exception {
        FeederInsertExtractionDTO dto = new FeederInsertExtractionDTO();
        String opsResult = "test";
        // 线体打桩
        CFLine cfLineEntity = new CFLine();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(null);
        service.handleFeederInsertErrorInfo(dto, opsResult);
        cfLineEntity.setIntelligentFeederFlag("N");
        PowerMockito.when(BasicsettingRemoteService.getLineByName(any())).thenReturn(cfLineEntity);
        service.handleFeederInsertErrorInfo(dto, opsResult);
        cfLineEntity.setIntelligentFeederFlag("Y");
        cfLineEntity.setLineCode("line");
        service.handleFeederInsertErrorInfo(dto, opsResult);

        List<SmtMachineMaterialMouting> matMoutings = new ArrayList<>();
        SmtMachineMaterialMouting entity = new SmtMachineMaterialMouting();
        entity.setLocationNo("test");
        entity.setLineName("test");
        entity.setFeederNo("test");
        entity.setModuleNo("test");
        entity.setEnabledFlag("N");
        entity.setMachineMaterialMoutingId("dsadasd");
        entity.setLastUpdatedDate(new Date());
        matMoutings.add(entity);
        // 打桩，
        PowerMockito.when(smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(any())).thenReturn(null);
        service.handleFeederInsertErrorInfo(dto, opsResult);

        PowerMockito.when(smtMachineMaterialMoutingService.getAllByParamsOfFeederInsertion(any())).thenReturn(matMoutings);
        PowerMockito.when(smtMachineMTLHistoryHRepository.selectSmtMachineMTLHistoryH(any())).thenReturn(null);
        // redis打桩
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        // 请求头打桩
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        try {
            service.handleFeederInsertErrorInfo(dto, opsResult);
        }catch (Exception e) {
            Assert.assertNull( e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void addItemModelInfoTest() {
        List<SmtMachineMTLHistoryL> list = new ArrayList<>();
        service.addItemModelInfo(list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void getSmtMachineHisDetailBatchTest() throws Exception {
        SmtMachineMTLHistoryLDTO dto = new SmtMachineMTLHistoryLDTO();
        dto.setHeaderId("da8eb19c-5090-45e0-8185-cec470df1921");
        dto.setPage("1");
        dto.setRows("10");
        PowerMockito.when(smtMachineMTLHistoryLRepository.getCountBatch(anyMap())).thenReturn(0L);
        PageRows<SmtMachineMTLHistoryL> detailBatch = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals(10L,detailBatch.getTotal());

        PowerMockito.when(smtMachineMTLHistoryLRepository.getCountBatch(anyMap())).thenReturn(1L);

        List<SmtMachineMTLHistoryL> list = new ArrayList<>();
        PowerMockito.when(smtMachineMTLHistoryLRepository.getSmtMachineHisDetailBatch(anyMap())).thenReturn(list);
        dto.setStartTime("2023-11-20 00:00:00");
        dto.setEndTime("2023-11-21 00:00:00");
        PageRows<SmtMachineMTLHistoryL> detailBatch1 = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals(1L,detailBatch1.getTotal());

        list.add(new SmtMachineMTLHistoryL(){{setMountType("3");}});
        PowerMockito.when(smtMachineMTLHistoryLRepository.getSmtMachineHisDetailBatch(anyMap())).thenReturn(list);
        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(null);
        PowerMockito.when(smtMachineMTLHistoryLRepository.getListByHeaderIdBatch(Mockito.any())).thenReturn(new ArrayList<>());
        PageRows<SmtMachineMTLHistoryL> detailBatch5 = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals(null,detailBatch5.getRows().get(0).getMountTypeName());
        Assert.assertEquals(null,detailBatch5.getRows().get(0).getLineName());
        Assert.assertEquals(null,detailBatch5.getRows().get(0).getQcConfirmUser());

        list.remove(0);
        SmtMachineMTLHistoryL historyL = new SmtMachineMTLHistoryL();
        historyL.setHeaderId("da8eb19c-5090-45e0-8185-cec470df1921");
        historyL.setWorkOrder("7184202-SMT-B5202");
        historyL.setSourceBatchCode("220020116459");
        historyL.setMountType("2");
        list.add(historyL);
        list.add(new SmtMachineMTLHistoryL(){{setMountType("2");}});
        PowerMockito.when(smtMachineMTLHistoryLRepository.getSmtMachineHisDetailBatch(anyMap())).thenReturn(list);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10350003");
        map.put("lookupMeaning","2");
        map.put("descriptionChin","接料扫描");
        map.put("descriptionEng","接料扫描");
        map.put("lookupType","1035");
        map.put("editableFlag","Y");
        map.put("sortSeq","2");
        map.put("enabledFlag","Y");
        mountTypeMap.put("2",map);
        PowerMockito.when(ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> lineMap = new HashMap<>();
        lineMap.put("lineCode1", "lineName1");
        lineMap.put("lineCode2", "lineName2");
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(any())).thenReturn(lineMap);

        Map<String, Map<String, String>> colMap = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        map.put("style", "test1111");
        map.put("bgBrandNo", "bgBrandNo");
        colMap.put("220020116459", map1);
        PowerMockito.when(stItemBarcodeService.getItemSupName(Mockito.any())).thenReturn(colMap);

        List<SmtMachineMTLHistoryL> idList = new ArrayList<>();
        idList.add(new SmtMachineMTLHistoryL(){{
            setHeaderId("da8eb19c-5090-45e0-8185-cec470df1921");
            setQcConfirmDate(new Date());
            setQcConfirmUser("lk");
        }});
        PowerMockito.when(smtMachineMTLHistoryLRepository.getListByHeaderIdBatch(Mockito.any())).thenReturn(idList);

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.getListByPkCodes(any())).thenReturn(pkCodeInfoList);
        PageRows<SmtMachineMTLHistoryL> detailBatch2 = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals("lk",detailBatch2.getRows().get(0).getQcConfirmUser());
        Assert.assertEquals(true,null != detailBatch2.getRows().get(0).getQcConfirmDate());
        Assert.assertEquals(null,detailBatch2.getRows().get(0).getBgBrandNo());
        pkCodeInfoList.add(new PkCodeInfo(){{setSysLotCode("sysLotCode");setSupplerCode("supplerCode");}});
        List<TaskMaterialIssueSeqEntityDTO> packSpecList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(anyList())).thenReturn(packSpecList);
        PageRows<SmtMachineMTLHistoryL> detailBatch3 = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals(null,detailBatch3.getRows().get(0).getIsDir());

        packSpecList.add(new TaskMaterialIssueSeqEntityDTO(){{setSysLotCode("sysLotCode");setSupplerCode("supplerCode");setIsDir("是");setBraidDirection("braid");}});
        PageRows<SmtMachineMTLHistoryL> detailBatch4 = service.getSmtMachineHisDetailBatch(dto);
        Assert.assertEquals("是",detailBatch4.getRows().get(0).getIsDir());
        Assert.assertEquals("braid",detailBatch4.getRows().get(0).getBraidDirection());
    }

    @Test
    public void getSmtMachineHisInfo() throws Exception {
        Assert.assertNotNull(service.getSmtMachineHisInfo(new ArrayList<>(),""));
    }

    @Test
    public void lowLevelGetSmtMachineL() {
        Assert.assertNotNull(service.lowLevelGetSmtMachineL(new SmtMachineMTLHistoryL()));
    }
}