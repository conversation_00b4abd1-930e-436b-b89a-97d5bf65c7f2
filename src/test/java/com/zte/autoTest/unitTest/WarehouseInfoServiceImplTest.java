package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.ErpLogService;
import com.zte.application.IMESLogService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.WarehouseInfoService;
import com.zte.application.WarehousehmEntryInfoService;
import com.zte.application.impl.WarehouseInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.RedisUtil;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.WarehousehmEntryInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.StandardAutomaticSubmitWarehouseDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.apache.cxf.endpoint.Client;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2021-05-21 15:39
 */
@PrepareForTest({WarehouseInfoServiceImpl.class, CommonUtils.class,BasicsettingRemoteService.class,
        MicroServiceRestUtil.class, HttpRemoteService.class, HttpClientUtil.class, RedisHelper.class, ErpRemoteService.class, PlanscheduleRemoteService.class})
public class WarehouseInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WarehouseInfoServiceImpl warehouseInfoServiceImpl;
    @Mock
    private WarehouseInfoService warehouseInfoService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    ValueOperations<String, Object> valueOperationsImpl;
    @Mock
    private RedisUtil redisUtil;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private ErpLogService erpLogService;

    @Mock
    private ErpRemoteService erpRemoteService;

    @Mock
    private WarehousehmEntryInfoService warehousehmEntryInfoService;

    @Mock
    Client client;

    @Mock
    private RedisLock redisLock;



    @Before
    public void init(){
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ErpRemoteService.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
    }

    @Test
    public void setRecentDay() throws Exception {
        Whitebox.invokeMethod(warehouseInfoServiceImpl,"setRecentDay",new PsWipInfoDTO(),new HashMap<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void doErpDoneForStandardModel() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto=new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId("52");
        PowerMockito.mockStatic(RedisHelper.class,CommonUtils.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO  sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(("10190002")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);

        SysLookupValuesDTO  sysLookupValuesDTO1 =new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupCode(new BigDecimal(("10190013")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO1);

        SysLookupValuesDTO  sysLookupValuesDTO2 =new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupCode(new BigDecimal(("10190012")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO2);

        SysLookupValuesDTO  sysLookupValuesDTO3 =new SysLookupValuesDTO();
        sysLookupValuesDTO3.setLookupCode(new BigDecimal(("10190011")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO3);

        SysLookupValuesDTO  sysLookupValuesDTO4 =new SysLookupValuesDTO();
        sysLookupValuesDTO4.setLookupCode(new BigDecimal(("10190010")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO4);

        SysLookupValuesDTO  sysLookupValuesDTO5 =new SysLookupValuesDTO();
        sysLookupValuesDTO5.setLookupCode(new BigDecimal(("10190007")));
        sysLookupValuesDTO5.setLookupMeaning("on");
        sysLookupValuesDTOList.add(sysLookupValuesDTO5);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(sysLookupValuesDTOList);}}));

        List<WarehouseEntryInfo> warehouseEntryInfoList=new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo=new WarehouseEntryInfo();
        warehouseEntryInfoList.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.getNeedToDoErpMoveOrDone(any()))
                .thenReturn(warehouseEntryInfoList);
        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryInfoStatusBatch(any()))
                .thenReturn(1);

        PowerMockito.when(erpLogService.insertErpLogBatch(any()))
                .thenReturn(1);
        PowerMockito.when(warehouseEntryInfoService.updateWarehouseEntryInfoById(any()))
                .thenReturn(1);
        warehouseInfoServiceImpl.doErpDoneForStandardModel(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void doErpMoveForStandardModel() throws Exception {
        StandardAutomaticSubmitWarehouseDTO dto=new StandardAutomaticSubmitWarehouseDTO();
        dto.setFactoryId("52");
        PowerMockito.mockStatic(RedisHelper.class,CommonUtils.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO  sysLookupValuesDTO =new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(("10190001")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);

        SysLookupValuesDTO  sysLookupValuesDTO1 =new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupCode(new BigDecimal(("10190013")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO1);

        SysLookupValuesDTO  sysLookupValuesDTO2 =new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupCode(new BigDecimal(("10190012")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO2);

        SysLookupValuesDTO  sysLookupValuesDTO3 =new SysLookupValuesDTO();
        sysLookupValuesDTO3.setLookupCode(new BigDecimal(("10190011")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO3);

        SysLookupValuesDTO  sysLookupValuesDTO4 =new SysLookupValuesDTO();
        sysLookupValuesDTO4.setLookupCode(new BigDecimal(("10190010")));
        sysLookupValuesDTOList.add(sysLookupValuesDTO4);

        SysLookupValuesDTO  sysLookupValuesDTO5 =new SysLookupValuesDTO();
        sysLookupValuesDTO5.setLookupCode(new BigDecimal(("10190007")));
        sysLookupValuesDTO5.setLookupMeaning("on");
        sysLookupValuesDTOList.add(sysLookupValuesDTO5);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(sysLookupValuesDTOList);}}));

        List<WarehouseEntryInfo> warehouseEntryInfoList=new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo=new WarehouseEntryInfo();
        warehouseEntryInfoList.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.getNeedToDoErpMoveOrDone(any()))
                .thenReturn(warehouseEntryInfoList);
        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryInfoStatusBatch(any()))
                .thenReturn(1);

        PowerMockito.when(erpLogService.insertErpLogBatch(any()))
                .thenReturn(1);
        PowerMockito.when(warehouseEntryInfoService.updateWarehouseEntryInfoById(any()))
                .thenReturn(1);
        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(true);
        warehouseInfoServiceImpl.doErpMoveForStandardModel(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void doErpMove() throws Exception {
        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(false);
        warehouseInfoServiceImpl.doErpMove("", "52");

        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(true);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        warehouseInfoServiceImpl.doErpMove("", "56");

        PowerMockito.when(redisLock.lock()).thenReturn(true);
        List<WarehousehmEntryInfo> list = new ArrayList<>();
        PowerMockito.when(warehouseInfoService.getList4CS(Mockito.anyMap())).thenReturn(list);
        PowerMockito.when(warehouseInfoService.getList4XA(Mockito.anyMap())).thenReturn(list);
        warehouseInfoServiceImpl.doErpMove("", "55");

        list.add(new WarehousehmEntryInfo());

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_ONE);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_THREE);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_FOUR);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        SysLookupValuesDTO s4 = new SysLookupValuesDTO();
        s4.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_FIVE);
        s4.setLookupMeaning("5");
        lookupValueList.add(s4);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        warehouseInfoServiceImpl.doErpMove("", "52");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void doErpDone() throws Exception {
        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(false);
        warehouseInfoServiceImpl.doErpDone("", "52");

        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(true);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        warehouseInfoServiceImpl.doErpDone("", "56");

        PowerMockito.when(redisLock.lock()).thenReturn(true);
        List<WarehousehmEntryInfo> list = new ArrayList<>();
        PowerMockito.when(warehouseInfoService.getList4CS(Mockito.anyMap())).thenReturn(list);
        PowerMockito.when(warehouseInfoService.getList4XA(Mockito.anyMap())).thenReturn(list);
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SIX);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_FOUR);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        SysLookupValuesDTO s4 = new SysLookupValuesDTO();
        s4.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_FIVE);
        s4.setLookupMeaning("5");
        lookupValueList.add(s4);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        warehouseInfoServiceImpl.doErpDone("", "55");

        list.add(new WarehousehmEntryInfo());
        WarehousehmEntryInfo warehousehmEntryInfo = new WarehousehmEntryInfo();
        warehousehmEntryInfo.setProdplanId("7777666");
        list.add(warehousehmEntryInfo);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTask.setItemNo("123456789012ABC");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.anyMap())).thenReturn(psTaskList);
        warehouseInfoServiceImpl.doErpDone("", "52");

        PowerMockito.when(PlanscheduleRemoteService.getPsTask(Mockito.anyMap())).thenReturn(null);
        warehouseInfoServiceImpl.doErpDone("", "52");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void doTheList() throws Exception {
        PsWipInfoDTO dto = new PsWipInfoDTO();
        dto.setFactoryId(new BigDecimal("52"));
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperationsImpl);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"}," +
                "\"bo\":[{\"processId\":\"iMESTEST00000044\",\"processCode\":\"N\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"入库\",\"remark\":\"河源测试，勿删\",\"createBy\":\"10226739\",\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-10-11 00:00:00\",\"lastUpdatedBy\":\"10226739\",\"lastUpdatedDate\":\"2018-10-11 00:00:00\",\"craftSection\":\"入库\",\"processControlGroupName\":\"单板投入\"     }   ] }";
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(),
                anyString(), anyString(),
                anyString(),
                Mockito.anyMap())).thenReturn(result);

        List<PsWipInfoDTO> headMapList = new LinkedList<>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        a1.setAttribute1("123");
        headMapList.add(a1);
        PowerMockito.when(warehouseInfoService.getheadMapList4CS(Mockito.anyMap())).thenReturn(headMapList);
        Assert.assertNotNull(warehouseInfoServiceImpl.doTheList(dto));
    }

}
