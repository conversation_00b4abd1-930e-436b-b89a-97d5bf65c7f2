package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.CtRouteDetail;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteDetailParamDTO;
import com.zte.interfaces.dto.CtRouteHead;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.interfaces.dto.NextCraftSection;
import com.zte.interfaces.dto.craft.CtProcessMappingDTO;
import com.zte.interfaces.dto.craft.RouteDetailForSnScanPushDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/10/14 14:21
 */
@PrepareForTest({MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class,
        MESHttpHelper.class, ServiceDataBuilderUtil.class,
        JacksonJsonConverUtil.class, JSON.class, JSONObject.class})
public class CrafttechRemoteServiceTest extends PowerBaseTestCase {
    @Mock
    private JsonNode jsonNode;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(JSONObject.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
    }

    @Test
    public void getBsProcessByItemCode() {
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(jsonNode);
        JsonNode bsProcessByItemCode = CrafttechRemoteService.getBsProcessByItemCode(new HashMap<>(), "123");
        Assert.assertTrue(Objects.nonNull(bsProcessByItemCode));
    }

    @Test
    public void getProcessInfo2() {
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(jsonNode);
        JsonNode processInfo = CrafttechRemoteService.getProcessInfo(new HashMap<>());
        Assert.assertTrue(Objects.nonNull(processInfo));
    }

    @Test
    public void getBatchProcessName() throws Exception {
        PowerMockito.when(HttpRemoteService.postHandler(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(jsonNode);
        JsonNode processInfo = CrafttechRemoteService.getBatchProcessName(null);
        Assert.assertTrue(Objects.nonNull(processInfo));
    }

    @Test
    public void getRouteHeadInfo() throws Exception {
        CtRouteHead ctRouteHead = new CtRouteHead();
        List<CtRouteHead> routeHeadInfo = CrafttechRemoteService.getRouteHeadInfo(ctRouteHead, null, null);
        Assert.assertTrue(CollectionUtils.isEmpty(routeHeadInfo));

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        List<CtRouteHead> routeHeadList = CrafttechRemoteService.getRouteHeadInfo(ctRouteHead, null, null);
        Assert.assertTrue(CollectionUtils.isEmpty(routeHeadInfo));
    }

    @Test
    public void getCtRouteDetailInfo() throws Exception {
        CtRouteDetailParamDTO ctRouteHead = new CtRouteDetailParamDTO();
        List<CtRouteDetail> ctRouteDetailInfo = CrafttechRemoteService.getCtRouteDetailInfo(ctRouteHead);
        Assert.assertTrue(CollectionUtils.isEmpty(ctRouteDetailInfo));

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        List<CtRouteDetail> list2 = CrafttechRemoteService.getCtRouteDetailInfo(ctRouteHead);
        Assert.assertTrue(CollectionUtils.isEmpty(list2));
    }

    @Test
    public void getCtRouteDetailByRouteIds() throws Exception {
        List<CtRouteDetailDTO> ctRouteDetailByRouteIds = CrafttechRemoteService.getCtRouteDetailByRouteIds(null);
        Assert.assertTrue(CollectionUtils.isEmpty(ctRouteDetailByRouteIds));

        List<String> routeIdList = new LinkedList<>();
        routeIdList.add("123");
        List<CtRouteDetailDTO> list1 = CrafttechRemoteService.getCtRouteDetailByRouteIds(routeIdList);
        Assert.assertTrue(CollectionUtils.isEmpty(list1));

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        list1 = CrafttechRemoteService.getCtRouteDetailByRouteIds(routeIdList);
        Assert.assertTrue(CollectionUtils.isEmpty(list1));
    }

    @Test
    public void getWorkCodeStation() {
        try {
            CrafttechRemoteService.getWorkCodeStation(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_IS_NOT_EXISTED, e.getMessage());
        }
    }

    @Test
    public void getCraftSectionWithRouteIdListByRouteIdList() throws Exception {
        List<Map<String, String>> maps = CrafttechRemoteService.getCraftSectionWithRouteIdListByRouteIdList(null);
        Assert.assertTrue(CollectionUtils.isEmpty(maps));
    }

    @Test
    public void getProcess() throws Exception {
        List<BSProcess> process = CrafttechRemoteService.getProcess(new BSProcess());
        Assert.assertTrue(CollectionUtils.isEmpty(process));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        process = CrafttechRemoteService.getProcess(new BSProcess());
        Assert.assertTrue(CollectionUtils.isEmpty(process));
    }

    @Test
    public void bsProcess4() {
        List<BSProcess> process = CrafttechRemoteService.bsProcess4(new BSProcessDTO());
        Assert.assertTrue(CollectionUtils.isEmpty(process));
    }

    @Test
    public void getBsProcessList() {
        List<BSProcess> process = CrafttechRemoteService.getBsProcessList(new BSProcessDTO());
        Assert.assertTrue(CollectionUtils.isEmpty(process));
    }

    @Test
    public void getProcessAll() {
        List<BSProcess> process = CrafttechRemoteService.getProcessAll();
        Assert.assertTrue(CollectionUtils.isEmpty(process));
    }

    @Test
    public void getBSProcessInfo() {
        BSProcess ni = CrafttechRemoteService.getBSProcessInfo("ni");
        Assert.assertTrue(Objects.isNull(ni));
    }

    @Test
    public void getCtRouteDetailByItemAndProcessCode() {
        List<CtRouteDetail> list = CrafttechRemoteService.getCtRouteDetailByItemAndProcessCode(null, null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getRouteHeadByRouteIds() {
        List<CtRouteHead> list = CrafttechRemoteService.getRouteHeadByRouteIds(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getRouteByItems() {
        List<CtBasicRouteDTO> list = CrafttechRemoteService.getRouteByItems(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getProcessInfoByItemOrTask() {
        List<BSProcess> list = CrafttechRemoteService.getProcessInfoByItemOrTask(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getCtRouteDetailInfoNew() {
        List<CtRouteDetail> list = CrafttechRemoteService.getCtRouteDetailInfoNew(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        list = CrafttechRemoteService.getCtRouteDetailInfoNew(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void getNextCraftSection() {
        List<NextCraftSection> list = CrafttechRemoteService.getNextCraftSection(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getNextSubCraftSection() {
        List<NextCraftSection> list = CrafttechRemoteService.getNextSubCraftSection(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void getScanProcessNew() {
        List<CtRouteInfoDTO> list = CrafttechRemoteService.getScanProcessNew(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getProcessListByRouteId() {
        PowerMockito.when(HttpRemoteService.pointToPointCall(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(new ServiceData<>() );
        List<CtRouteDetailDTO> list = CrafttechRemoteService.getProcessListByRouteId(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void listWorkStationByLineAndProcess() {
        PowerMockito.when(HttpRemoteService.pointToPointCall(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(new ServiceData<>() );
        List<CtRouteDetailDTO> list = CrafttechRemoteService.listWorkStationByLineAndProcess(null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void postScanProcess() {
        List<CtRouteInfoDTO> list = CrafttechRemoteService.postScanProcess(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        list = CrafttechRemoteService.postScanProcess(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void selectCtProcessMapping() {
        List<CtRouteDetailDTO> list = CrafttechRemoteService.selectCtProcessMapping(null,null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getCtRouteDetailInfo2() {
        List<CtRouteDetail> list = CrafttechRemoteService.getCtRouteDetailInfo(null,null,null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getRouteInfo() {
        List<CtRouteInfoDTO> list = CrafttechRemoteService.getRouteInfo(null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getCtRouteByRouteId() {
        try {
            CrafttechRemoteService.getCtRouteByRouteId(null, null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_ROUTE, e.getMessage());
        }

        List<CtRouteDetailDTO> ctRouteDetailDTOList = new LinkedList<>();
        CtRouteDetailDTO a1 = new CtRouteDetailDTO();
        ctRouteDetailDTOList.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(ctRouteDetailDTOList);
        List<CtRouteDetailDTO> list = CrafttechRemoteService.getCtRouteByRouteId(null, "null");
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void routeContainProcess() {
        CrafttechRemoteService.routeContainProcess(null, null);

        List<String> list = new LinkedList<>();
        list.add("123");
        CrafttechRemoteService.routeContainProcess(list, null);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(list);
        List<String> list1 = CrafttechRemoteService.routeContainProcess(list, null);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list1));

    }
    @Test
    public void getRouteAndSeqByItemNos() {
        CrafttechRemoteService.getRouteAndSeqByItemNos(null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtBasicRouteDTO> itemNos = CrafttechRemoteService.getRouteAndSeqByItemNos(null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }

    @Test
    public void getRouteAndSeqByRouteIds() {
        CrafttechRemoteService.getRouteAndSeqByRouteIds(null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtBasicRouteDTO> itemNos = CrafttechRemoteService.getRouteAndSeqByRouteIds(null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }

    @Test
    public void getCraftRouteAndSeqByItemNos() {
        CrafttechRemoteService.getCraftRouteAndSeqByItemNos(null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtBasicRouteDTO> itemNos = CrafttechRemoteService.getCraftRouteAndSeqByItemNos(null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }

    @Test
    public void getCraftRouteAndSeqByItemNosChild() {
        CrafttechRemoteService.getCraftRouteAndSeqByItemNosChild(null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtBasicRouteDTO> itemNos = CrafttechRemoteService.getCraftRouteAndSeqByItemNosChild(null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }
    @Test
    public void getCraftRouteAndSeqByRouteIds() {
        CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtBasicRouteDTO> itemNos = CrafttechRemoteService.getCraftRouteAndSeqByRouteIds(null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }
    @Test
    public void getLineBodyListBatch() {
        CrafttechRemoteService.getLineBodyListBatch(null);
        CrafttechRemoteService.getLineBodyListBatch("null");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtRouteDetailDTO> itemNos = CrafttechRemoteService.getLineBodyListBatch("null");
        Assert.assertTrue(CollectionUtils.isEmpty(itemNos));
    }
    @Test
    public void ctProcessMapping() {
        CrafttechRemoteService.ctProcessMapping(null,null);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        List<CtProcessMappingDTO> itemList = CrafttechRemoteService.ctProcessMapping(null, null);
        Assert.assertTrue(CollectionUtils.isEmpty(itemList));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        PageRows<CtProcessMappingDTO> pageRows = new PageRows<>();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(pageRows);
        List<CtProcessMappingDTO> rows = CrafttechRemoteService.ctProcessMapping(null, null);
        Assert.assertTrue(CollectionUtils.isEmpty(rows));
    }
    @Test
    public void getProcessCode() {
        String processCode = CrafttechRemoteService.getProcessCode(null);
        Assert.assertTrue(Objects.isNull(processCode));

        List<BSProcess> list = new LinkedList<>();
        BSProcess a1 = new BSProcess();
        list.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(list);
        CrafttechRemoteService.getProcessCode(null);
    }
    @Test
    public void getRouteDetailForSnScanPush() {
        List<RouteDetailForSnScanPushDTO> list = CrafttechRemoteService.getRouteDetailForSnScanPush(null);
        Assert.assertTrue(Objects.isNull(list));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        CrafttechRemoteService.getRouteDetailForSnScanPush(null);
    }

    @Test
    public void getLineBodyModelingData() {
        List<CtRouteDetailDTO> list = CrafttechRemoteService.getLineBodyModelingData(null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void getProcessByProList() {
        List<BSProcess> list = CrafttechRemoteService.getProcessByProList(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void getBsProcessListByTestCraft() {
        List<BSProcess> list = CrafttechRemoteService.getBsProcessListByTestCraft(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    @Test
    public void getProcessList() {
        List<CtRouteDetailDTO> list = CrafttechRemoteService.getProcessList(null,null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getScanProcess() {
        List<CtRouteInfoDTO> list = CrafttechRemoteService.getScanProcess(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
}
