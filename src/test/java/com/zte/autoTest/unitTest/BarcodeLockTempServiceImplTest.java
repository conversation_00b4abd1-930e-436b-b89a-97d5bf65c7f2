package com.zte.autoTest.unitTest;

import com.zte.application.BarcodeLockDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.BarcodeLockHeadServiceImpl;
import com.zte.application.impl.BarcodeLockTempServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.domain.model.BarcodeLockHead;
import com.zte.domain.model.BarcodeLockHeadRepository;
import com.zte.domain.model.BarcodeLockTempRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.BarcodeLockHeadEntityDTO;
import com.zte.interfaces.dto.BarcodeLockTempEntityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class})
public class BarcodeLockTempServiceImplTest extends PowerBaseTestCase {

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private BarcodeLockTempRepository barcodeLockTempRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @InjectMocks
    private BarcodeLockTempServiceImpl barcodeLockTempService;



    @Test
    public void pageList() throws Exception {
        List<BarcodeLockTempEntityDTO> barcodeLockHeadlist = new ArrayList<>();
        BarcodeLockTempEntityDTO barcodeLockTempEntityDTO = new BarcodeLockTempEntityDTO();
        barcodeLockTempEntityDTO.setSn("77788890001");
        barcodeLockTempEntityDTO.setPage(1);
        barcodeLockTempEntityDTO.setCreateBy("1027");
        barcodeLockTempEntityDTO.setCcList("1027,1025");
        barcodeLockTempEntityDTO.setRows(10);
        barcodeLockHeadlist.add(barcodeLockTempEntityDTO);
        PowerMockito.when(psWipInfoService.getWipInfoBySn(anyObject())).thenReturn(null);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1027", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("1025", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(barcodeLockTempRepository.pageList(anyObject())).thenReturn(barcodeLockHeadlist);
        Assert.assertNotNull(barcodeLockTempService.pageList(barcodeLockTempEntityDTO));
    }

}
