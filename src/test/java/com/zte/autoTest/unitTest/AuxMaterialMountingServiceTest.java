package com.zte.autoTest.unitTest;

import com.zte.application.impl.AuxMaterialMountingServiceImpl;
import com.zte.domain.model.AuxMaterialMouting;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;

public class AuxMaterialMountingServiceTest extends PowerBaseTestCase {

    @Mock
    private AuxMaterialMountingServiceImpl auxMaterialMountingService ;

    @Test
    public void addAuxMaterialMounting(){
        AuxMaterialMouting addAuxMaterialMounting = new AuxMaterialMouting() ;
        Assert.assertNotNull(auxMaterialMountingService.addAuxMaterialMounting(addAuxMaterialMounting));
    }


    @Test
    public void deleteAuxMaterialMounting(){
        Assert.assertNotNull(auxMaterialMountingService.deleteAuxMaterialMounting("123"));
    }
}
