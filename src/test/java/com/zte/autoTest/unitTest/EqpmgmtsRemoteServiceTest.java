package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.common.model.MessageId;
import com.zte.domain.model.EmEqpInfo;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.EmEqpScannerRecordDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-09 8:27
 */
@PrepareForTest({MicroServiceRestUtil.class})
public class EqpmgmtsRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private EqpmgmtsRemoteService eqpmgmtsRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
    }

    @Test
    public void emEqpBatchUpdate() throws Exception {
        List<EmEqpPdcountDTO> emEqpPdcountUpdateList = new LinkedList<>();
        eqpmgmtsRemoteService.emEqpBatchUpdate(emEqpPdcountUpdateList);
        Assert.assertNull(null);

        EmEqpPdcountDTO a1 = new EmEqpPdcountDTO();
        emEqpPdcountUpdateList.add(a1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"Success\"\n" +
                        "  },\n" +
                        "  \"bo\": [],\n" +
                        "  \"other\": null\n" +
                        "}");
        eqpmgmtsRemoteService.emEqpBatchUpdate(emEqpPdcountUpdateList);
        Assert.assertNotNull(eqpmgmtsRemoteService.getEpqPdcountInfo(new HashMap<>()));
    }

    @Test
    public void batchUpdateEqpPdcountStatus() {
        List<EmEqpPdcountDTO> emEqpPdcountUpdateList = new LinkedList<>();
        int count = EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(emEqpPdcountUpdateList);
        Assert.assertEquals(0, count);

        EmEqpPdcountDTO a1 = new EmEqpPdcountDTO();
        emEqpPdcountUpdateList.add(a1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(null);
        try {
            EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(emEqpPdcountUpdateList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        count = EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(emEqpPdcountUpdateList);
        Assert.assertEquals(1, count);

        serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(emEqpPdcountUpdateList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void updateSpiSeq() {

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(null);
        try {
            EqpmgmtsRemoteService.updateSpiSeq(1L, "777766600001", "Y");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        ServiceData<Integer> serviceData = new ServiceData<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        EqpmgmtsRemoteService.updateSpiSeq(1L, "777766600001", "Y");

        serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.updateSpiSeq(1L, "777766600001", "Y");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void getByEqpCode() {
        List<EmEqpPdcountDTO> emEqpPdcountUpdateList = new LinkedList<>();
        EmEqpPdcountDTO a1 = new EmEqpPdcountDTO();
        emEqpPdcountUpdateList.add(a1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new EmEqpInfo());}}));
        Assert.assertNotNull(eqpmgmtsRemoteService.getByEqpCode("21"));

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenThrow();
        try {
            EqpmgmtsRemoteService.getByEqpCode(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void insertScannerRecord() throws Exception {
        EqpmgmtsRemoteService.insertScannerRecord(null);
        EmEqpScannerRecordDTO scannerRecord = new EmEqpScannerRecordDTO();

        ServiceData serviceData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.insertScannerRecord(scannerRecord);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        EqpmgmtsRemoteService.insertScannerRecord(scannerRecord);
    }

    @Test
    public void checkSpiResult() {
        ServiceData<Boolean> serviceData = new ServiceData<>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(true);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));
        List<String> snList = new ArrayList<>();
        snList.add("751008500742");
        Boolean pass = EqpmgmtsRemoteService.checkSpiResult("XAS405", snList);
        Assert.assertEquals(true, pass);
    }

    @Test
    public void checkAoiResult() {
        ServiceData<Boolean> serviceData = new ServiceData<>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(true);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));
        List<String> snList = new ArrayList<>();
        snList.add("751008500742");
        Boolean pass = EqpmgmtsRemoteService.checkAoiResult("XAS405", "aoi1",snList);
        Assert.assertEquals(true, pass);
    }

    @Test
    public void checkPasterResult() {
        ServiceData<Boolean> serviceData = new ServiceData<>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(true);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(),
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));
        List<String> snList = new ArrayList<>();
        snList.add("751008500742");
        Boolean pass = EqpmgmtsRemoteService.checkPasterResult("XAS405", "m3",snList);
        Assert.assertEquals(true, pass);
    }
}
