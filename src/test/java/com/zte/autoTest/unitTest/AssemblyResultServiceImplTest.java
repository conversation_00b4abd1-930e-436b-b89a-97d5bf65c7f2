package com.zte.autoTest.unitTest;

import com.zte.application.impl.AssemblyResultServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.domain.model.AssemblyResultHisRepository;
import com.zte.domain.model.AssemblyResultRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
public class AssemblyResultServiceImplTest extends PowerBaseTestCase {

    @Mock
    private AssemblyResultRepository assemblyResultRepository;

    @Mock
    private AssemblyResultHisRepository assemblyResultHisRepository;

    @InjectMocks
    private AssemblyResultServiceImpl assemblyResultService;

    @Test
    public void updateForCenter()throws Exception {
        List<AssemblyResultEntityDTO> assemblyResultEntityDTOList=new ArrayList<>();
        AssemblyResultEntityDTO assemblyResultEntityDTO=new AssemblyResultEntityDTO();
        assemblyResultEntityDTO.setCombineFlag("N");
        assemblyResultEntityDTO.setItemCode("itemCode");
        assemblyResultEntityDTO.setItemVersion("ver");
        assemblyResultEntityDTOList.add(assemblyResultEntityDTO);
        PowerMockito.when(assemblyResultRepository.getListByItemAndVersion(anyObject())).thenReturn(assemblyResultEntityDTOList);

        Assert.assertNotNull(assemblyResultService.updateForCenter(assemblyResultEntityDTO));
    }
}
