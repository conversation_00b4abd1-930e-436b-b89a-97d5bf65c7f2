package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/10/14 15:54
 */

@PrepareForTest({JSON.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class,
        ServiceDataBuilderUtil.class, MESHttpHelper.class, HttpRemoteService.class, ConstantInterface.class, HttpRemoteUtil.class})
public class ObtainRemoteServiceDataUtilTest extends PowerBaseTestCase {

    @Before
    public void init() {
        mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void getSmtLocationInfoListByPost() {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNull(ObtainRemoteServiceDataUtil.getSmtLocationInfoListByPost(new SmtLocationInfoDTO()));
    }



}
