package com.zte.autoTest.unitTest.technical;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BarcodeLockDetailService;
import com.zte.application.BarcodeLockHeadService;
import com.zte.application.BarcodeUnlockService;
import com.zte.application.CommonTechnicalService;
import com.zte.application.impl.technical.TechnicalHeadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.technical.*;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.technical.TechnicalChangeDetailDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeExcelAnalysisDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeExcelImportDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeHeadDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeRetentionListQueryDTO;
import com.zte.interfaces.dto.technical.TechnicalChangeSnDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2022-09-29 14:03
 */
@PrepareForTest({BasicsettingRemoteService.class, ImesExcelUtil.class, RedisHelper.class,
        EasyExcelFactory.class, BeanUtils.class, CommonUtils.class, CrafttechRemoteService.class,
        PlanscheduleRemoteService.class, RequestHeadValidationUtil.class, ExcelCommonUtils.class})
public class TechnicalHeadServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private TechnicalHeadServiceImpl technicalHeadServiceImpl;
    @Mock
    private TechnicalChangeHeadRepository technicalChangeHeadRepository;
    @Mock
    private TechnicalChangeDetailRepository technicalChangeDetailRepository;
    @Mock
    private TechnicalChangeExecInfoRepository technicalChangeExecInfoRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private HttpServletResponse response;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;

    @Mock
    private BarcodeLockDetailService barcodeLockDetailService;

    @Mock
    private BarcodeLockHeadService barcodeLockHeadService;

    @Mock
    private BarcodeLockHeadRepository barcodeLockHeadRepository;
    @Mock
    private BarcodeUnlockService barcodeUnlockService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private CommonTechnicalService commonTechnicalService;
    @Mock
    private TechnicalChangeDetailHisRepository technicalChangeDetailHisRepository;
    @Mock
    private BarcodeLockDetailRepository barcodeLockDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private MultipartFile file;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void getCcList() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        Assert.assertNotNull(Whitebox.invokeMethod(technicalHeadServiceImpl, "getCcList"));
        List<String> ccList = new ArrayList<>();
        for (int i = 0; i < 201; i++) {
            ccList.add("11");
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "setCcList", ccList, new BarcodeLockHeadEntityDTO());

    }

    @Test
    public void insertLockDetail() throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("123");
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setCraftSection("DIP");
        ctRouteDetailDTO.setNextProcess("8");
        listDetail.add(ctRouteDetailDTO);
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setCtRouteDetailDTOList(listDetail);
            pdmTechnicalChangeInfoEntityDTO.setProdplanId("7778889");
        }
        List<String> needInsertProdPlanIdList = new ArrayList<>();
        needInsertProdPlanIdList.add("7778889");
        Assert.assertNotNull(Whitebox.invokeMethod(technicalHeadServiceImpl, "insertLockDetail", pdmTechnicalChangeInfoEntityDTOList, needInsertProdPlanIdList));
    }

    @Test
    public void insertLockHeadAndDetail() throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("123");
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setProcessSeq(new BigDecimal("1"));
        ctRouteDetailDTO.setCraftSection("DIP");
        ctRouteDetailDTO.setNextProcess("8");
        listDetail.add(ctRouteDetailDTO);
        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setCtRouteDetailDTOList(listDetail);
        }
        Whitebox.invokeMethod(technicalHeadServiceImpl, "insertLockHeadAndDetail", "name", pdmTechnicalChangeInfoEntityDTOList);
        Assert.assertEquals("DIP", ctRouteDetailDTO.getCraftSection());
    }

    @Test
    public void technicalChangeInfoDeal4() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(null);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        Assert.assertEquals("500001630338", pdmTechnicalDealForKafkaDTO.getChgReqNo());
    }

    @Test
    public void technicalChangeInfoDeal3() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.ISSUED);
        List<TechnicalChangeDetailDTO> technicalChangeDetailDTOList = new ArrayList<>();
        TechnicalChangeDetailDTO changeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTOList.add(changeDetailDTO);
        PowerMockito.when(technicalChangeDetailRepository.getUnCompleteByBillNo(anyString())).thenReturn(technicalChangeDetailDTOList);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        Assert.assertEquals("500001630338", pdmTechnicalDealForKafkaDTO.getChgReqNo());
    }

    @Test
    public void technicalChangeInfoDeal() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        for (PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO : pdmTechnicalChangeInfoEntityDTOList) {
            pdmTechnicalChangeInfoEntityDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.UNISSUED);
        }
        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setUpdatedBy("10270445");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.UNISSUED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        Assert.assertEquals("500001630338", pdmTechnicalDealForKafkaDTO.getChgReqNo());
    }

    @Test
    public void technicalChangeInfoDeal2() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList =
                JSON.parseArray("[{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"S\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"page\":1,\"prodplanId\":\"7771120\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665478020000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000},{\"changeReason\":\"1\",\"chgReqName\":\"500001630338\",\"chgReqNo\":\"500001630338\",\"contentType\":\"1\",\"createdBy\":\"10274973\",\"createdDate\":1665476324000,\"dealResult\":\"\",\"dealStatus\":\"F\",\"executeRequest\":\"1\",\"executeScope\":\"1\",\"factoryId\":\"55\",\"newItemNo\":\"1\",\"newItemVersion\":\"1\",\"objectType\":\"1\",\"oldItemNo\":\"1\",\"oldItemVersion\":\"1\",\"oldMartial\":\"1\",\"otherRequest\":\"1\",\"page\":1,\"prodplanId\":\"8888777\",\"productName\":\"1\",\"rows\":10,\"sendDate\":1665476324000,\"sendStatus\":\"已删除\",\"syncTime\":1665476324000,\"team1Name\":\"1\",\"team2Name\":\"1\",\"team3Name\":\"1\",\"updatedBy\":\"10270446\",\"updatedDate\":1665476324000}]",
                        PdmTechnicalChangeInfoEntityDTO.class);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> listDetail = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        ctRouteDetailDTO.setNextProcess("1");
        ctRouteDetailDTO.setProcessSeq(new BigDecimal(1));
        ctRouteDetailDTO.setCraftSection("SMT-A");
        listDetail.add(ctRouteDetailDTO);
        routeMap.put("7771120", listDetail);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setHeadId(UUID.randomUUID().toString());
        technicalChangeHeadDTO.setTechnicalStatus(Constant.TechnicalChangeStatus.IN_PREPARATION);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        BarcodeLockHead barcodeLockHead = new BarcodeLockHead();
        barcodeLockHead.setStatus(Constant.LOCKING);
        PowerMockito.when(barcodeLockHeadService.getHeadByBillNo(anyString())).thenReturn(barcodeLockHead);

        List<BarcodeLockDetailEntityDTO> barcodeLockDetailEntityDTOList = new ArrayList<>();
        BarcodeLockDetailEntityDTO barcodeLockDetailEntityDTO = new BarcodeLockDetailEntityDTO();
        barcodeLockDetailEntityDTO.setBatchSn("7771120");
        barcodeLockDetailEntityDTOList.add(barcodeLockDetailEntityDTO);
        PowerMockito.when(barcodeLockDetailService.getListByBillNoAndStatus(anyString(), anyString())).thenReturn(barcodeLockDetailEntityDTOList);

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO.setAttribute1(Constant.MAIN_SEND);
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1265001"));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1265002"));
        sysLookupTypesDTO.setLookupMeaning("1027");
        sysLookupTypesDTO1.setAttribute1(Constant.CC_SEND);
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);

        PdmTechnicalDealForKafkaDTO pdmTechnicalDealForKafkaDTO = new PdmTechnicalDealForKafkaDTO();
        pdmTechnicalDealForKafkaDTO.setChgReqNo("500001630338");
        pdmTechnicalDealForKafkaDTO.setNeedDealList(pdmTechnicalChangeInfoEntityDTOList);
        pdmTechnicalDealForKafkaDTO.setSendStatus(Constant.PdmTechnicalChangeStatus.DELETED);
        technicalHeadServiceImpl.technicalChangeInfoDeal(pdmTechnicalDealForKafkaDTO);
        Assert.assertEquals("500001630338", pdmTechnicalDealForKafkaDTO.getChgReqNo());
    }

    @Test
    public void queryTechnicalHeadByPage() throws Exception {
        Page<TechnicalChangeHeadDTO> pageParams = new Page<>();
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("sn", "889952400001");
        pageParams.setParams(linkedHashMap);

        PowerMockito.when(psWipInfoRepository.selectAttribute1BySn(Mockito.anyString()))
                .thenReturn("8899512");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap);

        List<TechnicalChangeHeadDTO> list = new LinkedList<>();
        TechnicalChangeHeadDTO b1 = new TechnicalChangeHeadDTO();
        b1.setCreatedBy("00000");
        b1.setLastUpdatedBy("0000");
        b1.setTechnicalStatus("0");
        list.add(b1);
        TechnicalChangeHeadDTO b2 = new TechnicalChangeHeadDTO();
        b2.setCreatedBy("00000");
        b2.setLastUpdatedBy("0000");
        b2.setTechnicalStatus("1");
        list.add(b2);
        TechnicalChangeHeadDTO b3 = new TechnicalChangeHeadDTO();
        b3.setCreatedBy("00000");
        b3.setLastUpdatedBy("0000");
        b3.setTechnicalStatus("2");
        list.add(b3);
        TechnicalChangeHeadDTO b4 = new TechnicalChangeHeadDTO();
        b4.setCreatedBy("00000");
        b4.setLastUpdatedBy("0000");
        b4.setTechnicalStatus("3");
        list.add(b4);
        TechnicalChangeHeadDTO b5 = new TechnicalChangeHeadDTO();
        b5.setCreatedBy("00000");
        b5.setLastUpdatedBy("0000");
        b5.setTechnicalStatus("4");
        list.add(b5);

        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalHeadByPage(any())).thenReturn(list);

        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);

        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalHeadByPage(pageParams));
    }

    @Test
    public void queryTechnicalDetailByPage() throws Exception {
        Page<TechnicalChangeDetailDTO> pageParams = new Page<>();
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("sn", "889952400001");
        pageParams.setParams(linkedHashMap);

        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setTechnicalStatus("1");
        d1.setUnlockType("1");
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("0");
        d2.setTechnicalStatus("0");
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("0");
        d3.setTechnicalStatus("2");
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("0");
        d4.setTechnicalStatus("3");
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("0");
        d5.setTechnicalStatus("4");
        resultList.add(d5);
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalDetailByPage(any()))
                .thenReturn(resultList);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
        b1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("0025", b1);
        HrmPersonInfoDTO b2 = new HrmPersonInfoDTO();
        b2.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("0026", b2);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap);
        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);
        Assert.assertNotNull(technicalHeadServiceImpl.queryTechnicalDetailByPage(pageParams));
    }

    @Test
    public void exportTechnicalDetails() throws Exception {
        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);
        try {
            technicalHeadServiceImpl.exportTechnicalSn(response, new TechnicalChangeDetailDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTER_QUERY_CRITERIA_BEFORE_EXPORTING, e.getMessage());
        }
        TechnicalChangeHeadDTO queryParam = new TechnicalChangeHeadDTO();

        List<SysLookupValuesDTO> valueByTypeCodes = new LinkedList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setAttribute1(Constant.TECHNICAL);
        sysLookupValuesDTO.setLookupMeaning("200");
        valueByTypeCodes.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Constant.lookupType.VALUE_1004041))
                .thenReturn(valueByTypeCodes)
        ;

        PowerMockito.when(EasyExcelFactory.writerSheet(0, Constant.TECHNICAL_EXPORT_SHEET_NAME)).thenReturn(excelWriterSheetBuilder);
        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);

        PowerMockito.when(EasyExcelFactory.write(response.getOutputStream(), TechnicalChangeDetailDTO.class)).thenReturn(write);
        PowerMockito.when(write.excludeColumnFiledNames(Mockito.anyList())).thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
        PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);


        Page<TechnicalChangeHeadDTO> pageParams = new Page<>();
        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setSn("889952400001");
        pageParams.setParams(technicalChangeHeadDTO);

        PowerMockito.when(psWipInfoRepository.selectAttribute1BySn(Mockito.anyString()))
                .thenReturn("8899512");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap)
        ;
        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setUnlockType("0");
        d1.setTechnicalStatusHead("0");
        d1.setTechnicalStatus("0");
        d1.setUnlockStatus(0);
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("1");
        d2.setTechnicalStatusHead("1");
        d2.setTechnicalStatus("1");
        d2.setUnlockStatus(1);
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("2");
        d3.setTechnicalStatusHead("2");
        d3.setTechnicalStatus("2");
        d3.setUnlockStatus(2);
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("3");
        d4.setTechnicalStatusHead("3");
        d4.setTechnicalStatus("3");
        d4.setUnlockStatus(3);
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("4");
        d5.setTechnicalStatusHead("4");
        d5.setTechnicalStatus("4");
        d5.setUnlockStatus(4);
        resultList.add(d5);
        PowerMockito.when(technicalChangeHeadRepository.exportDetailByPage(Mockito.any())).thenReturn(resultList);
        technicalHeadServiceImpl.exportTechnicalDetails(queryParam, response);

        TechnicalChangeDetailDTO param = new TechnicalChangeDetailDTO();
        param.setSnList(Arrays.asList("889973000001"));
        PowerMockito.when(technicalChangeHeadRepository.querySnDetailSnByPage(Mockito.any()))
                .thenReturn(resultList);
        PowerMockito.when(write.includeColumnFiledNames(Mockito.anyList())).thenReturn(write);
        technicalHeadServiceImpl.exportTechnicalSn(response, param);
    }

    @Test
    public void batchDeleteByHeadId() {
        List<String> list = new LinkedList<>();
        list.add("123");
        technicalHeadServiceImpl.batchDeleteByHeadId(list, "system");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void querySnDetailByPage() throws Exception {
        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setLookupMeaning("0");
        c1.setDescriptionChin("0");
        batchSysValueByCode.add(c1);
        SysLookupValuesDTO c2 = new SysLookupValuesDTO();
        c2.setLookupMeaning("1");
        c2.setDescriptionChin("0");
        batchSysValueByCode.add(c2);
        SysLookupValuesDTO c3 = new SysLookupValuesDTO();
        c3.setLookupMeaning("2");
        c3.setDescriptionChin("0");
        batchSysValueByCode.add(c3);
        SysLookupValuesDTO c4 = new SysLookupValuesDTO();
        c4.setLookupMeaning("3");
        c4.setDescriptionChin("0");
        batchSysValueByCode.add(c4);
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4");
        c5.setDescriptionChin("0");
        batchSysValueByCode.add(c5);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);
        List<TechnicalChangeDetailDTO> resultList = new LinkedList<>();
        TechnicalChangeDetailDTO d1 = new TechnicalChangeDetailDTO();
        d1.setOperator("0025");
        d1.setLastUpdatedBy("0026");
        d1.setUnlockType("0");
        d1.setTechnicalStatusHead("0");
        d1.setTechnicalStatus("0");
        d1.setUnlockStatus(0);
        resultList.add(d1);
        TechnicalChangeDetailDTO d2 = new TechnicalChangeDetailDTO();
        d2.setOperator("0025");
        d2.setLastUpdatedBy("0026");
        d2.setUnlockType("1");
        d2.setTechnicalStatusHead("1");
        d2.setTechnicalStatus("1");
        d2.setUnlockStatus(1);
        resultList.add(d2);
        TechnicalChangeDetailDTO d3 = new TechnicalChangeDetailDTO();
        d3.setOperator("0025");
        d3.setLastUpdatedBy("0026");
        d3.setUnlockType("2");
        d3.setTechnicalStatusHead("2");
        d3.setTechnicalStatus("2");
        d3.setUnlockStatus(2);
        resultList.add(d3);
        TechnicalChangeDetailDTO d4 = new TechnicalChangeDetailDTO();
        d4.setOperator("0025");
        d4.setLastUpdatedBy("0026");
        d4.setUnlockType("3");
        d4.setTechnicalStatusHead("3");
        d4.setTechnicalStatus("3");
        d4.setUnlockStatus(3);
        resultList.add(d4);
        TechnicalChangeDetailDTO d5 = new TechnicalChangeDetailDTO();
        d5.setOperator("0025");
        d5.setLastUpdatedBy("0026");
        d5.setUnlockType("4");
        d5.setTechnicalStatusHead("4");
        d5.setTechnicalStatus("4");
        d5.setUnlockStatus(4);
        resultList.add(d5);

        // 1. 查条码明细
        PowerMockito.when(technicalChangeHeadRepository.querySnDetailByPage(Mockito.any())).thenReturn(resultList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        a1.setEmpName("蛋蛋");
        hrmPersonInfoMap.put("00000", a1);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoMap);
        Page<TechnicalChangeDetailDTO> pageParams = new Page<>();
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("sn", "889952400001");
        pageParams.setParams(linkedHashMap);

        Assert.assertNotNull(technicalHeadServiceImpl.querySnDetailByPage(pageParams));
    }


    @Test
    public void testQueryTechnicalDetailList() {
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(null);
        try {
            technicalHeadServiceImpl.queryTechnicalDetailList("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHICAL_CHANGE_NOT_EXITS, e.getMessage());
        }

        TechnicalChangeHeadDTO technicalChangeHeadDTO = new TechnicalChangeHeadDTO();
        technicalChangeHeadDTO.setTechnicalStatus(NumConstant.STRING_THREE);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);
        try {
            technicalHeadServiceImpl.queryTechnicalDetailList("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHICAL_CHANGE_UNLOCKED, e.getMessage());
        }
        technicalChangeHeadDTO.setTechnicalStatus(NumConstant.STRING_TWO);
        PowerMockito.when(technicalChangeHeadRepository.getTechnicalChangeHeadDTOByChgReqNo(anyString())).thenReturn(technicalChangeHeadDTO);

        List<TechnicalChangeDetailDTO> detailList = new ArrayList<>();
        PowerMockito.when(technicalChangeDetailRepository.getListByBillNo(anyString())).thenReturn(detailList);
        technicalHeadServiceImpl.queryTechnicalDetailList("");
    }

    @Test
    public void testQueryRetentionPage() throws Exception {
        List<TechnicalChangeSnDTO> detailList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTO.setCurrProcess("test");
        detailList.add(technicalChangeSnDTO);
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(detailList);

        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));
        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        param.setProdplanId("test");
        technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
            setParams(param);
        }});

        param.setSn("5666");
        try {
            technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
                setParams(param);
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_BELONG_PRODPLANID, e.getMessage());
        }

        param.setSn("test5666");
        try {
            technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
                setParams(param);
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }
    }

    @Test
    public void testQueryRetentionPage2() throws Exception {
        List<TechnicalChangeSnDTO> detailList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTO.setCurrProcess("test");
        detailList.add(technicalChangeSnDTO);
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(detailList);

        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));

        TechnicalChangeRetentionListQueryDTO param = new TechnicalChangeRetentionListQueryDTO();
        param.setSn("test");
        param.setProdplanId("test");
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("test");
        wipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(wipInfoList);
        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(new ArrayList<TechnicalChangeExecInfo>() {
        });
        Assert.assertNotNull(technicalHeadServiceImpl.queryRetentionPage(new Page<TechnicalChangeRetentionListQueryDTO>() {{
            setParams(param);
        }}));
    }

    @Test
    public void queryTechnicalDetail() throws Exception {
        TechnicalChangeDetailDTO technicalChangeDetailDTO = new TechnicalChangeDetailDTO();
        technicalChangeDetailDTO.setCraftSection("SMT-A");
        technicalChangeDetailDTO.setProdplanId("8899720");
        technicalChangeDetailDTO.setTaskNo("kk");


        List<PsTask> childTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setProdplanId("8899721");
        childTasks.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByPartsPlanNo(Mockito.any()))
                .thenReturn(childTasks);

        List<PsWorkOrderDTO> workOrderDTOS = new LinkedList<>();
        PsWorkOrderDTO b1 = new PsWorkOrderDTO();
        b1.setCraftSection("SMT-A");
        b1.setChildFlag(true);
        b1.setSourceTask("8899721");
        workOrderDTOS.add(b1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(Mockito.anyString()))
                .thenReturn(workOrderDTOS);

        List<TechnicalChangeDetailDTO> detailDTOList = new LinkedList<>();
        TechnicalChangeDetailDTO c1 = new TechnicalChangeDetailDTO();
        c1.setChgReqNo("req9987");
        detailDTOList.add(c1);
        PowerMockito.when(technicalChangeHeadRepository.queryTechnicalDetail(Mockito.any()))
                .thenReturn(detailDTOList);
        Assert.assertNull(technicalHeadServiceImpl.queryTechnicalDetail(technicalChangeDetailDTO));
    }

    @Test
    public void handleSubmitAllTest() throws Exception {
        TechnicalChangeRetentionListQueryDTO dto = new TechnicalChangeRetentionListQueryDTO();
        try {
            technicalHeadServiceImpl.handleSubmitAll(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PARAM_IS_NULL));
        }
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TECH_SUBMIT_ALL_CHG_NULL));
        }
        dto.setChgReqNo("test");
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TECH_SUBMIT_ALL_CRAFT_SECTION_NULL));
        }
        dto.setCraftSection("craft");
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TECH_SUBMIT_ALL_PROD_NULL));
        }
        dto.setProdplanId("prodId");
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TECH_SUBMIT_ALL_PRODUCT_CODE_NULL));
        }
        dto.setProductCode("product");
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TECH_SUBMIT_ALL_UNLOCK_TYPE_NULL));
        }
        dto.setUnlockType("1");
        List<TechnicalChangeSnDTO> tempList = new ArrayList<>();
        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(tempList);
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.SUBMIT_ALL_SN_NULL));
        }
        TechnicalChangeSnDTO dto1 = new TechnicalChangeSnDTO();
        tempList.add(dto1);
        try {
            technicalHeadServiceImpl.handleSubmitAll(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void sendMailForDeleteOrVoidedTest() throws Exception {
        String content = "test";
        PowerMockito.when(commonTechnicalService.getContent(any())).thenReturn("test");
        PdmTechnicalDealForKafkaDTO dto = new PdmTechnicalDealForKafkaDTO();
        dto.setSendStatus("已删除");
        List<PdmTechnicalChangeInfoEntityDTO> needList = new ArrayList<>();
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        dto.setNeedDealList(needList);
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sendMailForDeleteOrVoided", dto);
        dto.setSendStatus("test");
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        needList.add(new PdmTechnicalChangeInfoEntityDTO());
        Assert.assertEquals("test",dto.getSendStatus());
        Whitebox.invokeMethod(technicalHeadServiceImpl, "sendMailForDeleteOrVoided", dto);
    }

    @Test
    public void getContentForUnissuedTest() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        Whitebox.invokeMethod(technicalHeadServiceImpl, "getContentForUnissued", pdmTechnicalChangeInfoEntityDTOList);
        PdmTechnicalChangeInfoEntityDTO dto1 = new PdmTechnicalChangeInfoEntityDTO();
        dto1.setErrorMsg("test");
        pdmTechnicalChangeInfoEntityDTOList.add(dto1);
        Assert.assertNotNull(Whitebox.invokeMethod(technicalHeadServiceImpl, "getContentForUnissued", pdmTechnicalChangeInfoEntityDTOList));
    }

    @Test
    public void downloadTemplate() throws Exception {
        PowerMockito.mockStatic(ImesExcelUtil.class);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
        technicalHeadServiceImpl.downloadTemplate(null, null);
    }


    @Test
    public void excelAnalysis() throws Exception {
        List<BSProcessInfoDTO> processInfoDTOS = new ArrayList<>();
        BSProcessInfoDTO processInfoDTO = new BSProcessInfoDTO();
        processInfoDTO.setProcessCode("test");
        processInfoDTO.setProcessName("test");
        processInfoDTOS.add(processInfoDTO);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBatchProcessName(Mockito.any())).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_CODE)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        PowerMockito.when(jsonNode.asText()).thenReturn(RetCode.SUCCESS_CODE);
        PowerMockito.when(jsonNode.toString()).thenReturn(JSON.toJSONString(processInfoDTOS));


        ResultData resultData = new ResultData();
        resultData.setCode(RetCode.BUSINESSERROR_CODE);
        resultData.setData(null);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ExcelCommonUtils.class);
        PowerMockito.when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);

        TechnicalChangeExcelImportDTO importDto = new TechnicalChangeExcelImportDTO();
        importDto.setFile(file);
        importDto.setSourceSys("STEP");
        importDto.setProdplanId("7010120");
        importDto.setChgReqNo("500010309592");
        try {
            technicalHeadServiceImpl.excelAnalysis(importDto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_EXCELANALYSIS_ERROR, e.getMessage());
        }

        resultData.setCode(RetCode.SUCCESS_CODE);
        resultData.setData(null);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ExcelCommonUtils.class);
        PowerMockito.when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);
        try {
            technicalHeadServiceImpl.excelAnalysis(importDto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_EXCELANALYSIS_ERROR, e.getMessage());
        }

        resultData.setData(new ArrayList());
        try {
            technicalHeadServiceImpl.excelAnalysis(importDto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_EMPTY, e.getMessage());
        }

        List<TechnicalChangeSnDTO> technicalChangeSnDTOS = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO);
        technicalChangeSnDTO.setSn("7778889001");
        technicalChangeSnDTO.setChgReqNo(null);

        TechnicalChangeSnDTO technicalChangeSnDTO1 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO1);
        technicalChangeSnDTO1.setSn(null);
        technicalChangeSnDTO.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO1a = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO1a);
        technicalChangeSnDTO1a.setSn(null);
        technicalChangeSnDTO1a.setChgReqNo("500010309592a");

        TechnicalChangeSnDTO technicalChangeSnDTO2a = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO2a);
        technicalChangeSnDTO2a.setSn("701012000001");
        technicalChangeSnDTO2a.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO2b = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO2b);
        technicalChangeSnDTO2b.setSn(null);
        technicalChangeSnDTO2b.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO2c = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO2c);
        technicalChangeSnDTO2c.setSn("701012000001aa");
        technicalChangeSnDTO2c.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO2 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO2);
        technicalChangeSnDTO2.setSn("701012000001");
        technicalChangeSnDTO2.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO2d = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO2d);
        technicalChangeSnDTO2d.setSn("701015000001");
        technicalChangeSnDTO2d.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO3 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO3);
        technicalChangeSnDTO3.setSn("7778889002");
        technicalChangeSnDTO3.setChgReqNo("500010309593");

        TechnicalChangeSnDTO technicalChangeSnDTO4 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO4);
        technicalChangeSnDTO4.setSn("7778889002");
        technicalChangeSnDTO4.setChgReqNo("500010309593");

        resultData.setCode(RetCode.SUCCESS_CODE);
        resultData.setData(technicalChangeSnDTOS);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, ExcelCommonUtils.class);
        PowerMockito.when(ExcelCommonUtils.resolveExcel(any(), any(), any())).thenReturn(resultData);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");

        TechnicalChangeSnDTO technicalChangeSnDTO5a = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO5a);
        technicalChangeSnDTO5a.setSn("701012000002");
        technicalChangeSnDTO5a.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO5b = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO5b);
        technicalChangeSnDTO5b.setSn("701012000003");
        technicalChangeSnDTO5b.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO5c = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO5c);
        technicalChangeSnDTO5c.setSn("701012000004");
        technicalChangeSnDTO5c.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO5d = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO5d);
        technicalChangeSnDTO5d.setSn("701012000005");
        technicalChangeSnDTO5d.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO6a = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO6a);
        technicalChangeSnDTO6a.setSn("701012000006");
        technicalChangeSnDTO6a.setChgReqNo("500010309592");

        TechnicalChangeSnDTO technicalChangeSnDTO6b = new TechnicalChangeSnDTO();
        technicalChangeSnDTOS.add(technicalChangeSnDTO6b);
        technicalChangeSnDTO6b.setSn("701012000007");
        technicalChangeSnDTO6b.setChgReqNo("500010309592");

        List<TechnicalChangeExecInfo> execInfoList = new ArrayList<>();
        TechnicalChangeExecInfo technicalChangeExecInfo1 = new TechnicalChangeExecInfo();
        execInfoList.add(technicalChangeExecInfo1);
        technicalChangeExecInfo1.setSn("701012000001");
        TechnicalChangeExecInfo technicalChangeExecInfo2 = new TechnicalChangeExecInfo();
        execInfoList.add(technicalChangeExecInfo2);
        technicalChangeExecInfo2.setSn("701012100099");
        PowerMockito.when(technicalChangeExecInfoRepository.select(any())).thenReturn(execInfoList);

        List<String> wipInfoList = new ArrayList<>();
        wipInfoList.add("701012000002");
        wipInfoList.add("701012000003");
        wipInfoList.add("701012000003-99");

        PowerMockito.when(psWipInfoRepository.checkSnExist(any())).thenReturn(wipInfoList);

        List<TechnicalChangeSnDTO> technicalChangeSnDTOArrayList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTOR1 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOArrayList.add(technicalChangeSnDTOR1);
        technicalChangeSnDTOR1.setSn("701012000004-99");
        TechnicalChangeSnDTO technicalChangeSnDTOR2 = new TechnicalChangeSnDTO();
        technicalChangeSnDTOArrayList.add(technicalChangeSnDTOR2);
        technicalChangeSnDTOR2.setSn("701012000004");


        PowerMockito.when(technicalChangeHeadRepository.selectRetentionPage(any())).thenReturn(technicalChangeSnDTOArrayList);

        List<BarcodeExpandDTO> expandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO1 = new BarcodeExpandDTO();
        expandDTOList.add(barcodeExpandDTO1);
        barcodeExpandDTO1.setBarcode("701012000006");
        BarcodeExpandDTO barcodeExpandDTO2 = new BarcodeExpandDTO();
        expandDTOList.add(barcodeExpandDTO2);
        barcodeExpandDTO2.setBarcode("701012000007");
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(expandDTOList);


        List<TechnicalChangeSnDTO> technicalChangeSnDTORetentionList = new ArrayList<>();
        TechnicalChangeSnDTO technicalChangeSnDTO5 = new TechnicalChangeSnDTO();
        technicalChangeSnDTORetentionList.add(technicalChangeSnDTO5);
        technicalChangeSnDTO5.setSn("701012000006");
        TechnicalChangeSnDTO technicalChangeSnDTO6 = new TechnicalChangeSnDTO();
        technicalChangeSnDTORetentionList.add(technicalChangeSnDTO6);
        technicalChangeSnDTO6.setSn("701012000006-99");


        PowerMockito.when(technicalChangeHeadRepository.selectRetentionBarcodeCenter(any())).thenReturn(technicalChangeSnDTORetentionList);


        TechnicalChangeExcelAnalysisDTO technicalChangeExcelAnalysisDTO = technicalHeadServiceImpl.excelAnalysis(importDto);
        Assert.assertEquals(16, technicalChangeExcelAnalysisDTO.getDetailList().size());


        importDto.setSourceSys("STEP");
        importDto.setProdplanId(null);
        importDto.setChgReqNo(null);
        technicalHeadServiceImpl.excelAnalysis(importDto);

        importDto.setSourceSys("STEP");
        importDto.setProdplanId("7010199");
        importDto.setChgReqNo("500010309599");
        technicalHeadServiceImpl.excelAnalysis(importDto);



        for (int i = 0; i < 1000; i++) {
            technicalChangeSnDTOS.add(technicalChangeSnDTO2);
        }
        try {
            technicalHeadServiceImpl.excelAnalysis(importDto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BATCH_SCAN_NOT_MORETHAN_500, e.getMessage());
        }
    }

    @Test
    public void checkSnLocal() throws Exception {
        Map<String, TechnicalChangeSnDTO> detailMap = new HashMap<>();
        TechnicalChangeSnDTO first = new TechnicalChangeSnDTO();
        String sourceSys = "111";
        Whitebox.invokeMethod(technicalHeadServiceImpl, "checkSnLocal", detailMap, first, sourceSys);
        Assert.assertEquals(0, detailMap.size());
    }

    @Test
    public void checkSnBarcodeCenter() throws Exception {
        Map<String, TechnicalChangeSnDTO> detailMap = new HashMap<>();
        TechnicalChangeSnDTO first = new TechnicalChangeSnDTO();
        String sourceSys = "111";
        Whitebox.invokeMethod(technicalHeadServiceImpl, "checkSnBarcodeCenter", detailMap, first, sourceSys);
        Assert.assertEquals(0, detailMap.size());
    }

}
