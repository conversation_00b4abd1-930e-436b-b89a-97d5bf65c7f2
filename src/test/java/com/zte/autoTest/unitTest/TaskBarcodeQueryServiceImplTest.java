package com.zte.autoTest.unitTest;

import com.zte.application.PsOutputInfoService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.TaskBarcodeQueryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.CtRouteDetailParamDTO;
import com.zte.interfaces.dto.CtRouteHead;
import com.zte.interfaces.dto.PDMProductMaterialResultDTO;
import com.zte.interfaces.dto.TaskBarcodeQueryDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.omg.CORBA.Any;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class,CommonUtils.class,
        DatawbRemoteService.class, PlanscheduleRemoteService.class,
        CrafttechRemoteService.class, BarcodeCenterRemoteService.class,
        HttpRemoteUtil.class,HttpRemoteService.class,ConstantInterface.class,BasicsettingRemoteService.class})
public class TaskBarcodeQueryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    TaskBarcodeQueryServiceImpl service;
    @Mock
    private TaskBarcodeQueryRepository taskBarcodeQueryRepository;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private CrafttechRemoteService crafttechRemoteService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Test
    public void taskBarcodeGenerate() throws Exception {
        TaskBarcodeQueryDTO dto = new TaskBarcodeQueryDTO();
        dto.setAttribute2("ZZ20210430CPE008-XA意大利");
        dto.setRegisterQty(new BigDecimal("1"));
        dto.setFactoryId(52L);
        dto.setRegisterQty(new BigDecimal("1"));
        dto.setOrgId(52L);
        dto.setSourceItemId(new BigDecimal("146465"));
        dto.setItemNo("127196231096");
        String taskNo = "ZZ20210430CPE008-XA意大利";

        Map<String, Object> map = new HashMap<>();
        map.put("taskNo", taskNo);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);

        PsTask psTask = new PsTask();
        List<PsTask> psTaskList = new ArrayList<>();
        psTask.setItemNo("127196231096");
        psTask.setTaskQty(new BigDecimal("1"));
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(map))
                .thenReturn(psTaskList);

        List<String> tempList = new ArrayList<String>();
        List<PDMProductMaterialResultDTO> versionList = new ArrayList<>();

        PDMProductMaterialResultDTO pdmProductMaterialResultDTO = new PDMProductMaterialResultDTO();
        tempList.add("ZZ20210430CPE008-XA意大利");
        pdmProductMaterialResultDTO.setEntityNameList(tempList);
        pdmProductMaterialResultDTO.setBomRevision("AJ");
        versionList.add(pdmProductMaterialResultDTO);
        PowerMockito.when(DatawbRemoteService.getBomVerByTaskNo(Mockito.any()))
                .thenReturn(versionList);


        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemName("测试");
        bsItemInfo.setSourceItemId(new BigDecimal("1132123"));
        itemInfoList.add(bsItemInfo);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyString()))
                .thenReturn(itemInfoList);

        List<CFFactory> factoryList = new ArrayList<>();
        CFFactory cfFactory = new CFFactory();
        cfFactory.setOrgId(52L);
        factoryList.add(cfFactory);
        PowerMockito.when(BasicsettingRemoteService.getFactoryInfo(Mockito.anyString()))
                .thenReturn(factoryList);

        CtRouteHead ctRouteHead = new CtRouteHead();
        ctRouteHead.setItemNo("127196231096");
        ctRouteHead.setProcessType("主板");
        ctRouteHead.setRouteId("bff096d7-f8b1-468a-89a8-74c46fa079a3");
        List<CtRouteHead> routeHeadList = new ArrayList();
        routeHeadList.add(ctRouteHead);
        PowerMockito.when(CrafttechRemoteService.getRouteHeadInfo(Mockito.any(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(routeHeadList);

        CtRouteDetailParamDTO routeDetail = new CtRouteDetailParamDTO();
        routeDetail.setRouteId("bff096d7-f8b1-468a-89a8-74c46fa079a3");
        List<CtRouteDetail> routeDetailList = new ArrayList();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setNextProcess("P0229");
        ctRouteDetail.setRouteDetailId("e3763ce8-6d85-49a0-9dfb-89b3d526a9df");
        routeDetailList.add(ctRouteDetail);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.any()))
                .thenReturn(routeDetailList);

        ServiceData serviceData = new ServiceData();
        serviceData.setBo(1L);
        PowerMockito.when(DatawbRemoteService.generateTaskBarcode(Mockito.any()))
                .thenReturn(serviceData);

        PowerMockito.when(psWipInfoRepository.batchInsertTaskBarcode(Mockito.any()))
                .thenReturn(1);

        PowerMockito.when(psWipInfoRepository.batchInsertScanHistory(Mockito.any()))
                .thenReturn(1);


        Assert.assertNotNull(service.taskBarcodeGenerate(dto));
    }

}
