package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.PmRepairRcvServiceRecodeImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.*;
@PrepareForTest({RedisHelper.class, CommonUtils.class})
public class PmRepairRcvServiceRecodeImplTest extends PowerBaseTestCase {

    @InjectMocks
    PmRepairRcvServiceRecodeImpl serviceRecode;

    @Mock
    PmRepairRcvRepository pmRepairRcvRepository;

    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    PsWipInfoRepository psWipInfoRepository;

    @Mock
    private RedisTemplate<String,Object> redisTemplate;

    @Mock
    private ValueOperations<String, String> opsValue;


    @Test
    public void checkWorkOrderNoEmpty() throws Exception{
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs=new ArrayList<>();
        PmRepairRcvDetailDTO dto=new PmRepairRcvDetailDTO();
        dto.setSn("777888899200");
        PowerMockito.when(psWipInfoRepository.getWorkOrderNoEmptyWipInfoBySns(any())).thenReturn(new ArrayList<>());
        serviceRecode.checkWorkOrderNoEmpty(pmRepairRcvDetailDTOs);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkPmRepairStatus() {
        try {
            List<PmRepairRcvDetailDTO>  pmRepairRcvDetailDTOs=new ArrayList<>();
            PmRepairRcvDetailDTO pmRepairRcvDetailDTO=new PmRepairRcvDetailDTO();
            pmRepairRcvDetailDTO.setSn("sn1");
            pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
            serviceRecode.checkPmRepairStatus("1",pmRepairRcvDetailDTOs);
            PowerMockito.when(pmRepairRcvRepository.getListRepairPcvAndDeatilByDto(any())).thenReturn(Lists.newArrayList(new PmRepairRcvVo(){
                {
                    setStatus("1");
                }}
            ));
            serviceRecode.checkPmRepairStatus("1",pmRepairRcvDetailDTOs);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_STATUS_ERROR, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertOrUpdate() {
        try {
            Assert.assertNotNull(serviceRecode.insertOrUpdate(new PmRepairRcv(), Lists.newArrayList(), Lists.newArrayList(), 1, false));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_SOURCE_IS_EMPTY, e.getMessage());
        }
    }

    @Test
    public void submitRepairOrderEx() throws Exception{
        PowerMockito.mockStatic(RedisHelper.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(),anyString())).thenReturn("");
        try{
            PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
            serviceRecode.submitRepairOrderEx(new PmRepairRcv(),
                    Lists.newArrayList(new SemiManufactureDealInfo()),
                    Lists.newArrayList(new RedisLock("")),
                    Lists.newArrayList(new SysLookupTypesDTO()),
                    new PmRepairRcvDetailDTO(){{setSn("1");}});
        } catch (Exception e){
            Assert.assertEquals("1", e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        try{
            serviceRecode.submitRepairOrderEx(new PmRepairRcv(),
                    Lists.newArrayList(new SemiManufactureDealInfo()),
                    Lists.newArrayList(new RedisLock("")),
                    Lists.newArrayList(new SysLookupTypesDTO()),
                    new PmRepairRcvDetailDTO());
        } catch (Exception e){
            Assert.assertNotNull( e.getMessage());
        }
        try{
            serviceRecode.submitRepairOrderEx(new PmRepairRcv(){{setFromStation(Constant.FROM_STATION_MACHINE);}},
                    Lists.newArrayList(new SemiManufactureDealInfo()),
                    Lists.newArrayList(new RedisLock("")),
                    Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setAttribute1("1");}}),
                    new PmRepairRcvDetailDTO(){{
                        setStatus(Constant.REPAIR_STATUS_FICTION);
                        setApplicationDepartment("1"); setApplicationSection("1");}});
        } catch (Exception e){
            Assert.assertNull( e.getMessage());
        }
        PowerMockito.when(psWipInfoService.getWipInfoBySn(any()))
                .thenReturn(new PsWipInfo(){{setWipId("1");}});
        try{
            serviceRecode.submitRepairOrderEx(new PmRepairRcv(){{setFromStation(Constant.FROM_STATION_MACHINE);}},
                    Lists.newArrayList(new SemiManufactureDealInfo()),
                    Lists.newArrayList(new RedisLock("")),
                    Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setAttribute1("1");}}),
                    new PmRepairRcvDetailDTO(){{
                        setStatus(Constant.REPAIR_STATUS_FICTION);
                        setApplicationDepartment("1"); setApplicationSection("1");}});
        } catch (Exception e){
            Assert.assertEquals(MessageId.WIP_INFO_DATA_NOT_FOUND, e.getMessage());
        }
        try{
            serviceRecode.submitRepairOrderEx(new PmRepairRcv(){{setFromStation(Constant.FROM_STATION_MACHINE);}},
                    Lists.newArrayList(new SemiManufactureDealInfo()),
                    Lists.newArrayList(new RedisLock("")),
                    Lists.newArrayList(new SysLookupTypesDTO(){{setLookupMeaning("1");setAttribute1("1");}}),
                    new PmRepairRcvDetailDTO(){{
                        setStatus(Constant.REPAIR_STATUS_FICTION);
                        setApplicationDepartment("2"); setApplicationSection("1");}});
        } catch (Exception e){
            Assert.assertEquals(MessageId.WIP_INFO_DATA_NOT_FOUND, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void returnValid() {
        Assert.assertNotNull(serviceRecode.returnValid(new PmRepairRcvDetailDTO()));
    }
}