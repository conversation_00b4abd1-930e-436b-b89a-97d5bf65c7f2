package com.zte.autoTest;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

/**
 * <AUTHOR>
 * @date 2023-05-23 16:11
 */
@PrepareForTest({DataServiceClientV1.class, SecureEncryptorUtils.class,BasicsettingRemoteService.class})
public class DigitalPlatformRemoteServiceTest extends PowerBaseTestCase {
    /* Started by AICoder, pid:48991r8d90y8d9c14b2a09375009406fd329e3cb */
    @Mock
    private String defaultEmpNo;
    @Mock
    private String dbEnv;
    @Mock
    private Long rmaBoardAppId;
    @Mock
    private Long faultInformationAppId;
    @Mock
    private Long boardRepairStationAppId;
    @Mock
    private String appKey;
    @Mock
    private String appSecret;
    @InjectMocks
    private DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Mock
    private DataServiceClientV1 dataServiceClient;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class);
    }

    @Test
    public void getMaterialInformation() throws Exception {
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        PowerMockito.field(DigitalPlatformRemoteService.class, "defaultEmpNo")
                .set(digitalPlatformRemoteService, "33");
        ReflectionTestUtils.setField(digitalPlatformRemoteService, "appCode", "mRshSs4EemGV3ZnrOg36T7zc4NMRPbzENHoFfHk7vfrxk6p52sKzdBNzrSXxYYJqiaAaMR2mBuYzAeh9HGXkNA==");
        ReflectionTestUtils.setField(digitalPlatformRemoteService, "appEnName", "2");

        ReflectionTestUtils.setField(digitalPlatformRemoteService, "vendorName", "2");
        ReflectionTestUtils.setField(digitalPlatformRemoteService, "dataServiceHost", "2");
        PowerMockito.when(SecureEncryptorUtils.decrypt(any(),any())).thenReturn("2");
        PowerMockito.when(DataServiceClientV1.getInstance(any())).thenReturn(dataServiceClient);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when( BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6732),eq(Constant.LOOKUP_TYPE_6732005))).thenReturn(sysLookupTypesDTO);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("2");
        PowerMockito.when( BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6732),eq(Constant.LOOKUP_TYPE_6732005))).thenReturn(sysLookupTypesDTO);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setAttribute3("2");
        PowerMockito.when( BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6732),eq(Constant.LOOKUP_TYPE_6732005))).thenReturn(sysLookupTypesDTO);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setAttribute2("2");
        PowerMockito.when( BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6732),eq(Constant.LOOKUP_TYPE_6732005))).thenReturn(sysLookupTypesDTO);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setAttribute1("2");
        PowerMockito.when( BasicsettingRemoteService.getSysLookUpValue(eq(Constant.LOOKUP_TYPE_6732),eq(Constant.LOOKUP_TYPE_6732005))).thenReturn(sysLookupTypesDTO);
        try {
            digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_MATERIAL_INFORMATION, e.getMessage());
        }

        QueryResult queryResult = new QueryResult();
        PowerMockito.when(dataServiceClient.invoke(any()))
                .thenReturn(queryResult);
        digitalPlatformRemoteService.getMaterialInformation("", Arrays.asList("1"));
        Assert.assertNotNull(queryResult);
    }
    /* Ended by AICoder, pid:48991r8d90y8d9c14b2a09375009406fd329e3cb */

}
