package com.zte.common;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.HolderEnum;
import com.alibaba.excel.metadata.Holder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder;
import com.alibaba.excel.read.metadata.property.ExcelReadHeadProperty;
import com.alibaba.excel.read.processor.AnalysisEventProcessor;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.zte.common.excel.SimpleAnalysisEventListener;
import com.zte.interfaces.dto.WipPrintConfigExcelDTO;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 6396001283
 * @description
 * @date 2023/11/10
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpHeaderUtil.class})
public class SimpleAnalysisEventListenerTest {

    @InjectMocks
    private SimpleAnalysisEventListener<WipPrintConfigExcelDTO> simpleAnalysisEventListener;

    @Test
    public void invokeHeadMap() {
        Map<Integer, String> rowData = new HashMap<>();
        rowData.put(0, "marketinDivision");
        rowData.put(1, "222");

        Assert.assertThrows(RuntimeException.class, () -> simpleAnalysisEventListener.invokeHeadMap(rowData, new AnalysisContext() {
            @Override
            public void currentSheet(ReadSheet readSheet) {

            }

            @Override
            public ReadWorkbookHolder readWorkbookHolder() {
                ReadWorkbookHolder readWorkbookHolder = new ReadWorkbookHolder();
                readWorkbookHolder.setExcelReadHeadProperty(new ExcelReadHeadProperty(new Holder() {
                    @Override
                    public HolderEnum holderType() {
                        return HolderEnum.SHEET;
                    }
                }, WipPrintConfigExcelDTO.class, new ArrayList<>()));
                return readWorkbookHolder;
            }

            @Override
            public ReadSheetHolder readSheetHolder() {
                return null;
            }

            @Override
            public void readRowHolder(ReadRowHolder readRowHolder) {

            }

            @Override
            public ReadRowHolder readRowHolder() {
                return null;
            }

            @Override
            public ReadHolder currentReadHolder() {
                return null;
            }

            @Override
            public Object getCustom() {
                return null;
            }

            @Override
            public AnalysisEventProcessor analysisEventProcessor() {
                return null;
            }

            @Override
            public List<ReadSheet> readSheetList() {
                return null;
            }

            @Override
            public void readSheetList(List<ReadSheet> list) {

            }

            @Override
            public ExcelTypeEnum getExcelType() {
                return null;
            }

            @Override
            public InputStream getInputStream() {
                return null;
            }

            @Override
            public Integer getCurrentRowNum() {
                return null;
            }

            @Override
            public Integer getTotalCount() {
                return null;
            }

            @Override
            public Object getCurrentRowAnalysisResult() {
                return null;
            }

            @Override
            public void interrupt() {

            }
        }));
        rowData.put(4, "整机条码");
        rowData.put(5, "预留字段1");
        rowData.put(6, "预留字段2");
        rowData.put(7, "预留字段3");
        rowData.put(8, "预留字段4");
        rowData.put(9, "预留字段5");
        rowData.put(10, "预留字段6");
        rowData.put(11, "预留字段7");
        rowData.put(12, "预留字段8");
        rowData.put(13, "预留字段9");
        rowData.put(13, "预留字段10");
        rowData.put(13, "模板名称");
        Assert.assertThrows(RuntimeException.class, () -> simpleAnalysisEventListener.invokeHeadMap(rowData, new AnalysisContext() {
            @Override
            public void currentSheet(ReadSheet readSheet) {

            }

            @Override
            public ReadWorkbookHolder readWorkbookHolder() {
                ReadWorkbookHolder readWorkbookHolder = new ReadWorkbookHolder();
                readWorkbookHolder.setExcelReadHeadProperty(new ExcelReadHeadProperty(new Holder() {
                    @Override
                    public HolderEnum holderType() {
                        return HolderEnum.SHEET;
                    }
                }, WipPrintConfigExcelDTO.class, new ArrayList<>()));
                return readWorkbookHolder;
            }

            @Override
            public ReadSheetHolder readSheetHolder() {
                return null;
            }

            @Override
            public void readRowHolder(ReadRowHolder readRowHolder) {

            }

            @Override
            public ReadRowHolder readRowHolder() {
                return null;
            }

            @Override
            public ReadHolder currentReadHolder() {
                return null;
            }

            @Override
            public Object getCustom() {
                return null;
            }

            @Override
            public AnalysisEventProcessor analysisEventProcessor() {
                return null;
            }

            @Override
            public List<ReadSheet> readSheetList() {
                return null;
            }

            @Override
            public void readSheetList(List<ReadSheet> list) {

            }

            @Override
            public ExcelTypeEnum getExcelType() {
                return null;
            }

            @Override
            public InputStream getInputStream() {
                return null;
            }

            @Override
            public Integer getCurrentRowNum() {
                return null;
            }

            @Override
            public Integer getTotalCount() {
                return null;
            }

            @Override
            public Object getCurrentRowAnalysisResult() {
                return null;
            }

            @Override
            public void interrupt() {

            }
        }));
    }
}
