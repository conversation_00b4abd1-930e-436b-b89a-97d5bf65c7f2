package com.zte.common;

import com.zte.application.impl.AssemblyOptRecordServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class DateUtilTest  extends PowerBaseTestCase {
    @InjectMocks
    private DateUtil dateUtil;
    @Test
    public void addDateTimeSecond()  {
        Assert.assertNotNull(DateUtil.addDateTimeSecond(new Date(),2));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testCalHoursByDate() {
        Assert.assertEquals(BigDecimal.valueOf(0), DateUtil.calHoursByDate(null, null));
        Assert.assertEquals(BigDecimal.valueOf(0), DateUtil.calHoursByDate(null, new Date()));
        Assert.assertEquals(BigDecimal.valueOf(0), DateUtil.calHoursByDate(new Date(), null));
        Assert.assertEquals(BigDecimal.valueOf(0, 2), DateUtil.calHoursByDate(new Date(), new Date()));
    }
}