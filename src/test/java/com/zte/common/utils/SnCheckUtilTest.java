package com.zte.common.utils;

import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

public class SnCheckUtilTest extends PowerBaseTestCase {

    @Test
    public void testSnStart7Or2AndLength12() {
        Assert.assertTrue(SnCheckUtil.snStart7Or2AndLength12("212345678902"));
        Assert.assertTrue(SnCheckUtil.snStart7Or2AndLength12("712345678902"));
        Assert.assertFalse(SnCheckUtil.snStart7Or2AndLength12("112345678902"));
        Assert.assertFalse(SnCheckUtil.snStart7Or2AndLength12("8902"));
        Assert.assertFalse(SnCheckUtil.snStart7Or2AndLength12(""));
    }
}