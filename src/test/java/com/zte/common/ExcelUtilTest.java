package com.zte.common;

import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.util.PowerBaseTestCase;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@RunWith(PowerMockRunner.class)
@PrepareForTest({XSSFWorkbook.class, XSSFCell.class, XSSFSheet.class, XSSFRow.class,XSSFCellStyle.class,
        ExcelName.class, CellRangeAddress.class,XSSFRow.class,BorderStyle.class})
public class ExcelUtilTest extends PowerBaseTestCase {
    @Mock
    private XSSFWorkbook wb;
    @Mock
    private XSSFSheet sheet;
    @Mock
    private XSSFCellStyle cellStyle;
    @Mock
    private XSSFFont font;

    @Mock
    private XSSFRow row;
    @Mock
    private XSSFCell cell;
    @Mock
    private XSSFCellStyle Style;
    @InjectMocks
    private ExcelUtil excelUtil;
    @Test
    public void getOriginalXSSFWorkbook() throws Exception {
        PowerMockito.when(wb.createSheet(Mockito.anyString())).thenReturn(sheet);
        PowerMockito.when(sheet.createRow(Mockito.anyInt())).thenReturn(row);
        PowerMockito.when(row.createCell(Mockito.anyInt())).thenReturn(cell);
        PowerMockito.when(wb.createCellStyle()).thenReturn(cellStyle);
        PowerMockito.when(wb.createFont()).thenReturn(font);
        PowerMockito.when(row.getLastCellNum()).thenReturn(Short.valueOf("4"));
        List<BSmtBomDetail> details = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail=new BSmtBomDetail();
        bSmtBomDetail.setMachineNo("test123");
        bSmtBomDetail.setItemCode("test123");
        bSmtBomDetail.setLocationNo("test123");
        bSmtBomDetail.setAmQty(new BigDecimal("1"));
        bSmtBomDetail.setBmQty(new BigDecimal("1"));
        bSmtBomDetail.setQty(new BigDecimal("1"));
        details.add(bSmtBomDetail);
        List<List<BSmtBomDetail>> exportList = new ArrayList<>();
        exportList.add(details);
        Map<String, Object> mapConquery = new HashMap<String, Object>();
        mapConquery.put("factoryId", 55);
        mapConquery.put("attr1", "test123");
        mapConquery.put("lineCode", "test123");
        mapConquery.put("pbNum",2);
        mapConquery.put("verNo","2");
        excelUtil.getOriginalXSSFWorkbook( ExcelName.ORIGINAL_BSMTBOMHEADER_EXCEL_TITLE, exportList,wb,ExcelName.ORIGINAL_BSMTBOMHEADER_EXECL_PROPS,mapConquery);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        mapConquery.put("programNameA", "A");
        excelUtil.getOriginalXSSFWorkbook( ExcelName.ORIGINAL_BSMTBOMHEADER_EXCEL_TITLE, exportList,wb,ExcelName.ORIGINAL_BSMTBOMHEADER_EXECL_PROPS,mapConquery);
        mapConquery.put("programNameB", "B");
        excelUtil.getOriginalXSSFWorkbook( ExcelName.ORIGINAL_BSMTBOMHEADER_EXCEL_TITLE, exportList,wb,ExcelName.ORIGINAL_BSMTBOMHEADER_EXECL_PROPS,mapConquery);
        mapConquery.put("programNameA", null);
        excelUtil.getOriginalXSSFWorkbook( ExcelName.ORIGINAL_BSMTBOMHEADER_EXCEL_TITLE, exportList,wb,ExcelName.ORIGINAL_BSMTBOMHEADER_EXECL_PROPS,mapConquery);
    }

    /* Started by AICoder, pid:j76dcd7badtafb714de7085c400c6e1194a1e803 */
    @Test
    public void TestsetXSSFCellValueForEach() throws Exception {
        Row row1 = PowerMockito.mock(XSSFRow.class);
        String[] props = {"itemCode"};
        int celli = 0;
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("046050200038");
        PowerMockito.when(row1.createCell(celli)).thenReturn(cell);
        Assert.assertNull(Whitebox.invokeMethod(excelUtil, "setXSSFCellValueForEach", row1, props, 0, bSmtBomDetail));
        bSmtBomDetail.setItemCode("null");
        Assert.assertNull(Whitebox.invokeMethod(excelUtil, "setXSSFCellValueForEach", row1, props, 0, bSmtBomDetail));
    }
    /* Ended by AICoder, pid:j76dcd7badtafb714de7085c400c6e1194a1e803 */

    /* Started by AICoder, pid:x9cafr7331r7cd314b02094ef0086419c451e9ee */
    @Test
    public void TestsetCellValue() throws Exception {
        Row row1 = PowerMockito.mock(XSSFRow.class);
        String[] props = {"itemCode"};
        int celli = 0;
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("046050200038");
        PowerMockito.when(row1.createCell(celli)).thenReturn(cell);
        Assert.assertNull(Whitebox.invokeMethod(excelUtil, "setCellValue", props, row1, Style, bSmtBomDetail));
        bSmtBomDetail.setItemCode("null");
        Assert.assertNull(Whitebox.invokeMethod(excelUtil, "setCellValue", props, row1, Style, bSmtBomDetail));
    }
    /* Ended by AICoder, pid:x9cafr7331r7cd314b02094ef0086419c451e9ee */

}