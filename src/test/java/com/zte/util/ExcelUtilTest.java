package com.zte.util;

import com.zte.common.ExcelUtil;
import com.zte.common.utils.ExcelName;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AssemblyRelaScanDTO;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ExcelUtilTest {

    @Test
    public void getValue() throws Exception {
        String[] sheetName = {"1", "2", "3"};
        List<List<BSmtBomDetail>> detaiss = new ArrayList<>();
        List<BSmtBomDetail> detais = new ArrayList<>();
        detaiss.add(detais);
        BSmtBomDetail detai = new BSmtBomDetail();
        detais.add(detai);
        detai.setFactoryId(new BigDecimal("5"));
        detai.setQty(new BigDecimal("5.587"));

        Assert.assertNotNull(ExcelUtil.getXSSFWorkbook(sheetName, ExcelName.BSMTBOMHEADER_EXCEL_TITLE, detaiss, null, ExcelName.BSMTBOMHEADER_EXECL_PROPS));
    }
}
