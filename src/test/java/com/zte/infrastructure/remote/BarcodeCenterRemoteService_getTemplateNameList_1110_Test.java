/*Started by AICoder, pid:e03c4mebe19fcfb1460809b8b04b9a7ae1c8a331*/
package com.zte.infrastructure.remote;
import com.zte.application.IMESLogService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, BasicsettingRemoteService.class})
public class BarcodeCenterRemoteService_getTemplateNameList_1110_Test {
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    BasicsettingRemoteService basicsettingRemoteService;
    @Mock
    IMESLogService imesLogService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class,BasicsettingRemoteService.class);
        barcodeCenterRemoteService = new BarcodeCenterRemoteService();
    }

    @After
    public void tearDown() throws Exception {}

    @Test(expected = MesBusinessException.class)
    public void testGetTemplateNameList_NoConfig() throws Exception {
        String templateContnet = "NoConfig";
        barcodeCenterRemoteService.getAllTemplateName(templateContnet);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetTemplateNameList_ErrorResponse() throws Exception {
        String templateContnet = "ErrorResponse";
        barcodeCenterRemoteService.getAllTemplateName(templateContnet);
    }

    @Test(expected = Exception.class)
    public void testGetTemplateNameList_SuccessResponse() throws Exception {
        String templateContnet = "SuccessResponse";
        List<SysLookupTypesDTO>  lookupValues = new ArrayList<>();
        SysLookupTypesDTO value = new SysLookupTypesDTO();
        value.setLookupCode(BigDecimal.ONE);
        value.setLookupMeaning("url");
        lookupValues.add(value);
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(value);
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
        List<String> result = barcodeCenterRemoteService.getAllTemplateName(templateContnet);
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
/*Ended by AICoder, pid:e03c4mebe19fcfb1460809b8b04b9a7ae1c8a331*/