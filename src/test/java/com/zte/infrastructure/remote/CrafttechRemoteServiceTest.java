package com.zte.infrastructure.remote;

import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-02-23 14:31
 */
@PrepareForTest({JacksonJsonConverUtil.class, MESHttpHelper.class, MicroServiceRestUtil.class
, ServiceDataBuilderUtil.class})
public class CrafttechRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private CrafttechRemoteService crafttechRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
    }

    /* Started by AICoder, pid:n639db6590afdf414b1c081ea02b0940f3c11f59 */
    @Test
    public void  queryProcessInfo(){
        Map<String, String> map = new HashMap<>();
        List<BSProcessDTO> bsProcessDTOS = CrafttechRemoteService.queryProcessInfo(map);
        Assert.assertTrue(CollectionUtils.isEmpty(bsProcessDTOS));
        List<BSProcessDTO> proList = new LinkedList<>();
        BSProcessDTO a1 = new BSProcessDTO();
        proList.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(proList);
        CrafttechRemoteService.queryProcessInfo(map);
    }
    /* Ended by AICoder, pid:n639db6590afdf414b1c081ea02b0940f3c11f59 */

    @Test
    public void getProcessCodeBylineAndStationInfo() {
        CtRouteDetailDTO list = CrafttechRemoteService.getProcessCodeBylineAndStationInfo(null, null, null);
        Assert.assertNull(list);

        List<CtRouteDetailDTO> detailDTOS = new LinkedList<>();
        CtRouteDetailDTO a1 = new CtRouteDetailDTO();
        detailDTOS.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(detailDTOS);
        CrafttechRemoteService.getProcessCodeBylineAndStationInfo(null, null, null);
    }
}
