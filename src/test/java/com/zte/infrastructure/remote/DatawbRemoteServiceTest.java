package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BaItem;
import com.zte.domain.model.BoardOnline;
import com.zte.interfaces.dto.BaItemBrandstyle;
import com.zte.interfaces.dto.Lotxlocxid;
import com.zte.interfaces.dto.ReelIdAndGoodDieInfoDTO;
import com.zte.interfaces.dto.SendAssemblyToMesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, JacksonJsonConverUtil.class,
        MicroServiceRestUtil.class, InterfaceEnum.class,HttpRemoteService.class, CommonUtils.class, HttpClientUtil.class, ServiceDataBuilderUtil.class})
public class DatawbRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private DatawbRemoteService service;
    @Mock
    private ConstantInterface constantInterface;
    @Test
    public void batchInsertOrDeleteAssemble() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        SendAssemblyToMesDTO sendAssemblyToMesDTO = new SendAssemblyToMesDTO();
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), anyString(), anyString(), anyString(),any())).thenReturn(jsonNode);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://ip/zte-mes-manufactureshare-datawbsys/AssembleHeader/batchInsertOrDeleteAssemble");
        service.batchInsertOrDeleteAssemble(sendAssemblyToMesDTO);
        service.batchInsertOrDeleteAssemble(null);
        Assert.assertEquals("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}", str);
    }

    @Test
    public void testGetSubmitStatusBatch() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("true");
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testGetItemListByTaskList() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getItemListByTaskList(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("true");
        DatawbRemoteService.getItemListByTaskList(new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void getItemInfo() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(JSON.toJSONString(new BaItem()));
        service.getItemInfo("2");
        Assert.assertEquals(Constant.STR_Y, "Y");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");
        service.getItemInfo("2");
        Assert.assertEquals(Constant.STR_Y, "Y");
    }

    @Test
    public void getStockInfo() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(JSON.toJSONString(new BaItem()));
        service.getStockInfo("2");
        Assert.assertEquals(Constant.STR_Y, "Y");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");
        service.getStockInfo("2");
        Assert.assertEquals(Constant.STR_Y, "Y");
    }

    @Test
    public void getStockPowerModuleIncluded() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(JSON.toJSONString(new BaItem()));
        service.getStockPowerModuleIncluded("2","{");
        Assert.assertEquals(Constant.STR_Y, "Y");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");
        service.getStockPowerModuleIncluded("2","");
        Assert.assertEquals(Constant.STR_Y, "Y");
    }

    @Test
    public void getTheRegion() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(JSON.toJSONString(new BaItem()));
        service.getTheRegion("2","{");
        Assert.assertEquals(Constant.STR_Y, "Y");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");
        service.getTheRegion("2","");
        Assert.assertEquals(Constant.STR_Y, "Y");
    }

    @Test
    public void getMpnByItembarcode() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        DatawbRemoteService.getSubmitStatusBatch(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(JSON.toJSONString(new BaItem()));
        service.getMpnByItembarcode("");
        Assert.assertEquals(Constant.STR_Y, "Y");

        service.getMpnByItembarcode("2");
        Assert.assertEquals(Constant.STR_Y, "Y");

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");
        service.getMpnByItembarcode("2");
        Assert.assertEquals(Constant.STR_Y, "Y");
    }

    @Test
    public void getStockQtyWithWisId() {
        PowerMockito.mockStatic(ConstantInterface.class, HttpClientUtil.class, ServiceDataBuilderUtil.class);
        ServiceData serviceData = new ServiceData<>();
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(), Mockito.anyMap())).thenReturn(JSONObject.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("");
        List<String> wisIdList = new ArrayList<>();
        List<String> itemNoList = new ArrayList<>();
        String status = Constant.STATUS_OK;
        DatawbRemoteService.getStockQtyWithWisId(wisIdList, itemNoList, status);
        itemNoList.add("123");
        List<Lotxlocxid> result = DatawbRemoteService.getStockQtyWithWisId(wisIdList, itemNoList, status);
        Assert.assertTrue(result.isEmpty());

        List<Lotxlocxid> lotxlocxidList = new ArrayList<>();
        Lotxlocxid lotxlocxid = new Lotxlocxid();
        lotxlocxid.setSku("101111");
        lotxlocxid.setQty(10);
        lotxlocxidList.add(lotxlocxid);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSONObject.toJSONString(lotxlocxidList));
        result = DatawbRemoteService.getStockQtyWithWisId(wisIdList, itemNoList, status);
        Assert.assertTrue(!result.isEmpty());
    }
    @Test
    public void getStockQtyWithBrand() {
        PowerMockito.mockStatic(ConstantInterface.class, HttpClientUtil.class, ServiceDataBuilderUtil.class);
        ServiceData serviceData = new ServiceData<>();
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(), Mockito.anyMap())).thenReturn(JSONObject.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("");
        List<String> wisIdList = new ArrayList<>();
        List<String> itemNoList = new ArrayList<>();
        String status = Constant.STATUS_OK;
        List<Lotxlocxid> result = DatawbRemoteService.getStockQtyWithBrand(wisIdList, itemNoList, status);
        Assert.assertTrue(result.isEmpty());
        itemNoList.add("123");
        DatawbRemoteService.getStockQtyWithBrand(wisIdList, itemNoList, status);

        List<Lotxlocxid> lotxlocxidList = new ArrayList<>();
        Lotxlocxid lotxlocxid = new Lotxlocxid();
        lotxlocxid.setSku("101111");
        lotxlocxid.setQty(10);
        lotxlocxidList.add(lotxlocxid);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSONObject.toJSONString(lotxlocxidList));
        result = DatawbRemoteService.getStockQtyWithBrand(wisIdList, itemNoList, status);
        Assert.assertTrue(!result.isEmpty());
    }
    @Test
    public void queryItemBrandInfo() {
        String itemNo = "";
        String brandName = "";
        List<BaItemBrandstyle> list = DatawbRemoteService.queryItemBrandInfo(itemNo, brandName);
        Assert.assertTrue(list.isEmpty());

        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        itemNo = "1234567";
        List<BaItemBrandstyle> resultList = new ArrayList<>();
        BaItemBrandstyle brandstyle = new BaItemBrandstyle();
        brandstyle.setItemUuid(111L);
        resultList.add(brandstyle);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSONObject.toJSONString(resultList));
        list = DatawbRemoteService.queryItemBrandInfo(itemNo, brandName);
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void getReelIdAndGoodDieQtyInfoTest() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        List<ReelIdAndGoodDieInfoDTO> test = service.getReelIdAndGoodDieQtyInfo("test");
        Assert.assertTrue(test == null);

        List<ReelIdAndGoodDieInfoDTO> reelIdAndGoodDieInfoDTOS = new ArrayList<>();
        reelIdAndGoodDieInfoDTOS.add(new ReelIdAndGoodDieInfoDTO());
        String bo = JSON.toJSONString(reelIdAndGoodDieInfoDTOS);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(bo);
        List<ReelIdAndGoodDieInfoDTO> test1 = service.getReelIdAndGoodDieQtyInfo("test");
        Assert.assertTrue(test1.size() == 1);
    }

    @Test
    public void updateBoardOnline() throws Exception{
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.updateBoardOnline)).thenReturn("ewr");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        BoardOnline dto=new BoardOnline();
        dto.setCardNo("232");
        service.updateBoardOnline(dto);
        Assert.assertNotNull(dto);

        String bo = JSON.toJSONString(1);
        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.updateBoardOnline)).thenReturn("ewr");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(bo);
        service.updateBoardOnline(dto);
        Assert.assertNotNull(dto);
    }

    /*Started by AICoder, pid:x1ebbb5b46pbca314003081840138a7480488c89*/
    @Test(timeout = 8000)
    public void testGetRepairCountFromBoardOnline() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("qweqw");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("qweqw");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        List<String> prodList = new ArrayList<>();
        List<BoardOnline> result = service.getRepairCountFromBoardOnline(prodList);
        assertTrue(result.isEmpty());
        List<BoardOnline> list = new ArrayList<>();
        BoardOnline online = new BoardOnline();
        online.setProdplanId(new BigDecimal(1));
        list.add(online);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(list));
        List<BoardOnline> result1 = service.getRepairCountFromBoardOnline(prodList);
        Assert.assertFalse(CollectionUtils.isEmpty(result1));
    }

    @Test
    public void getStCodeInfoList() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(new ArrayList<>());
                        }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(new ArrayList<>()));
        Assert.assertTrue(service.getStCodeInfoList().size() >= 0);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertTrue(service.getStCodeInfoList().size() >= 0);
    }
    /*Ended by AICoder, pid:x1ebbb5b46pbca314003081840138a7480488c89*/

    /* Started by AICoder, pid:4b3e5d0959fa4a9e883e8dba6f765bf2 */
    @Test
    public void getItemTransfer() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("");
        Assert.assertNotNull(service.getItemTransfer("2"));
    }
    /* Ended by AICoder, pid:4b3e5d0959fa4a9e883e8dba6f765bf2 */
}