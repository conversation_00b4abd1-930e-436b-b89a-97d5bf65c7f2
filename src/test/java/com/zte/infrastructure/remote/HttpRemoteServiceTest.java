package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.PDMSendInfoDTO;
import com.zte.interfaces.dto.PdmBomItemQueryDTO;
import com.zte.interfaces.dto.PdmBomItemResultDTO;
import com.zte.interfaces.dto.PdmReceiveResultDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.http.HttpMethod;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({MESHttpHelper.class, MicroServiceRestUtil.class})
public class HttpRemoteServiceTest extends PowerBaseTestCase {

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class, MicroServiceRestUtil.class);
    }

    @Test
    public void pointToPointCall() {

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(null);
        try {
            HttpRemoteService.pointToPointCall(MicroServiceNameEum.BASICSETTING,
                    HttpMethod.GET, "/BS/sysLookupValues/111", null,
                    new TypeReference<ServiceData<SysLookupValues>>() {
                    });
        } catch (MesBusinessException e) {
            assert MessageId.CALL_SERVICE_FAILED.equals(e.getExMsgId());
        }

        Map<String, String> headerParamsMap = new HashMap<>();
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerParamsMap);
        ServiceData<SysLookupValues> serviceData = new ServiceData<>();
        serviceData.setCode(null);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSONObject.toJSONString(serviceData));
        try {
            HttpRemoteService.pointToPointCall(MicroServiceNameEum.BASICSETTING,
                    HttpMethod.GET, "/BS/sysLookupValues/111", null,
                    new TypeReference<ServiceData<SysLookupValues>>() {
                    });
        } catch (MesBusinessException e) {
            assert MessageId.CALL_SERVICE_FAILED.equals(e.getExMsgId());
        }

        headerParamsMap.put("X-Emp-No", "10266925");
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSONObject.toJSONString(serviceData));
        try {
            HttpRemoteService.pointToPointCall(MicroServiceNameEum.BASICSETTING,
                    HttpMethod.GET, "/BS/sysLookupValues/111", null,
                    new TypeReference<ServiceData<SysLookupValues>>() {
                    });
        } catch (MesBusinessException e) {
            assert retCode.BUSINESSERROR_MSGID.equals(e.getExMsgId());
        }

        retCode.setCode(RetCode.SUCCESS_CODE);
        retCode.setMsgId(RetCode.SUCCESS_MSGID);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("test");
        serviceData.setBo(sysLookupValues);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(JSONObject.toJSONString(serviceData));
        ServiceData<SysLookupValues> result = HttpRemoteService.pointToPointCall(MicroServiceNameEum.BASICSETTING,
                HttpMethod.GET, "/BS/sysLookupValues/111", null,
                new TypeReference<ServiceData<SysLookupValues>>() {
                });
        assert "test".equals(result.getBo().getLookupMeaning());
    }

    /**
     * <AUTHOR>
     * @date 2021/9/14
     */
    @PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class, HttpRemoteUtil.class})
    public static class PdmRemoteServiceTest extends PowerBaseTestCase {
        @InjectMocks
        private PdmRemoteService service;

        @Before
        public void init(){
            PowerMockito.mockStatic(BasicsettingRemoteService.class,MESHttpHelper.class,HttpRemoteUtil.class);
        }

        @Test
        public void bomItemQuery() throws Exception {
            List<SysLookupTypesDTO> sysLookupTypesDTOS=new ArrayList<>();
            SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
            sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
            sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO);
            SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
            sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678002));
            sysLookupTypesDTO1.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO1.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO1);
            SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
            sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678003));
            sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO2.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO2);
            SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
            sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
            sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO3);
            SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
            sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
            sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO4);
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
            PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                    JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList(""));
                    }})
            );
            PdmBomItemQueryDTO pdmBomItemResultDTO=new PdmBomItemQueryDTO();
            try {
                service.bomItemQuery(pdmBomItemResultDTO);
            } catch (MesBusinessException e) {
                assert MessageId.FAILED_TO_CALL_PDM.equals(e.getExMsgId());
            }
        }
        @Test
        public void query() throws Exception {
            List<SysLookupTypesDTO> sysLookupTypesDTOS=new ArrayList<>();
            SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
            sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
            sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO);
            SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
            sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678002));
            sysLookupTypesDTO1.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO1.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO1);
            SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
            sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678003));
            sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO2.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO2);
            SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
            sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
            sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO3);
            SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
            sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
            sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
            sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
            sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
            sysLookupTypesDTOS.add(sysLookupTypesDTO4);
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
            PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                    JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList(""));
                    }})
            );
            PdmBomItemResultDTO pdmBomItemResultDTO=new PdmBomItemResultDTO();
            pdmBomItemResultDTO.setNo("123456789012ABB");
            try {
                service.query(pdmBomItemResultDTO);
            } catch (MesBusinessException e) {
                assert MessageId.FAILED_TO_CALL_PDM.equals(e.getExMsgId());
            }
        }

        @Test
        public void sendToPdm() throws Exception {
            List<PDMSendInfoDTO> pdmSendInfoDTOList = new ArrayList<>();
            pdmSendInfoDTOList.add(new PDMSendInfoDTO());
            String url = "/zte-plm-pdm-api/receive/assemblyInfo";

            ServiceData serviceData = new ServiceData();
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
            List<PDMSendInfoDTO> result = service.sendToPdm(pdmSendInfoDTOList, url);
            assert result.size() == 1;

            serviceData.setBo(new PdmReceiveResultDTO());
            serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
            result = service.sendToPdm(pdmSendInfoDTOList, url);
            assert result.size() == 1;

            serviceData.getCode().setCode(RetCode.SUCCESS_CODE);
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
            Assert.assertEquals("/zte-plm-pdm-api/receive/assemblyInfo", url);
            result = service.sendToPdm(pdmSendInfoDTOList, url);
            assert result.size() == 1;
        }
    }
}
