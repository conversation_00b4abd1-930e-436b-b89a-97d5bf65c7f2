/*Started by AICoder, pid:y79edseb5fbec5614dc009fd60bd13674bf66fe4*/
package com.zte.infrastructure.remote;

import com.zte.interfaces.dto.iepms.ConvertPdfDTO;
import com.zte.interfaces.dto.iepms.ConvertPdfResultDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.io.IOException;

@PrepareForTest({HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class})
public class IepmsRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private IepmsRemoteService iepmsRemoteService;
    @Mock private HttpRemoteUtil httpRemoteUtil;
    @Mock private ServiceDataBuilderUtil serviceDataBuilderUtil;
    @Mock private JacksonJsonConverUtil jacksonJsonConverUtil;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test(timeout = 8000)
    public void testConvertWordToPdfSuccess() throws IOException {
        String docId = "123";
        String empNo = "456";
        ConvertPdfDTO convertPdfDTO = new ConvertPdfDTO();
        convertPdfDTO.setDocId(docId);

        ConvertPdfDTO.DocAuthInfoDTO docAuthInfoDTO = new ConvertPdfDTO.DocAuthInfoDTO();
        docAuthInfoDTO.setEmpNo(empNo);
        docAuthInfoDTO.setAuthValue("authValue");
        docAuthInfoDTO.setOriginServiceName("originServiceName");
        docAuthInfoDTO.setOrgId("orgId");

        ConvertPdfResultDTO convertPdfResultDTO = new ConvertPdfResultDTO();
        convertPdfResultDTO.setEngineDocId("engineDocId");
        try {
            iepmsRemoteService.convertWordToPdf(docId, empNo);
        } catch (Exception e) {
            Assert.assertTrue( e instanceof NullPointerException);
        }

    }
}
/*Ended by AICoder, pid:y79edseb5fbec5614dc009fd60bd13674bf66fe4*/