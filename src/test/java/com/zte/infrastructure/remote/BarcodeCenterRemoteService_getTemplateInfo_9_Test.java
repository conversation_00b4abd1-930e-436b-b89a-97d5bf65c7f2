/*Started by AICoder, pid:c3953c653aw2879147190920104c3567d3d80be7*/
package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.BarcodeTemplateDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import ognl.ASTList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class,HttpRemoteUtil.class, BasicsettingRemoteService.class})
public class BarcodeCenterRemoteService_getTemplateInfo_9_Test {
    @InjectMocks
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock private BasicsettingRemoteService basicsettingRemoteService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class,HttpRemoteUtil.class, BasicsettingRemoteService.class);
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetTemplateInfo_NoConfig() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = null;
        barcodeCenterRemoteService.getTemplateInfo("templateName");
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetTemplateInfo_HttpRemoteUtilFailed() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://testurl.com");
        PowerMockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(JSON.toJSONString(new ServiceData<>()));
        barcodeCenterRemoteService.getTemplateInfo("templateName");
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetTemplateInfo_JsonIsNull() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://testurl.com");
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        Mockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        barcodeCenterRemoteService.getTemplateInfo("templateName");
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetTemplateInfo_RetCodeIsNotSuccess() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://testurl.com");
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        String json = "{\"code\": {\"code\": \"failed\"}}";
        Mockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(json);
        barcodeCenterRemoteService.getTemplateInfo("templateName");
    }

    @Test(timeout = 8000)
    public void testGetTemplateInfo_Success() throws Exception {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://testurl.com");
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        String json = "{\"code\": {\"code\": \"success\"}, \"bo\": {\"templateName\": \"test\"}}";
        json = "{\"code\": {\"code\": \"0000\"}, \"bo\": {\"templateName\": \"test\"}}";
        Mockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(json);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        sysLookupTypesDTO.setLookupCode(BigDecimal.ONE);
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "10308742");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        Mockito.when(basicsettingRemoteService.getSysLookUpValue(Mockito.eq(Constant.MP_CODE_KEY_1004052))).thenReturn(sysLookupTypesDTOList);
        BarcodeTemplateDTO result = barcodeCenterRemoteService.getTemplateInfo("templateName");
        assertEquals("test", result.getTemplateName());
    }
}
/*Ended by AICoder, pid:c3953c653aw2879147190920104c3567d3d80be7*/