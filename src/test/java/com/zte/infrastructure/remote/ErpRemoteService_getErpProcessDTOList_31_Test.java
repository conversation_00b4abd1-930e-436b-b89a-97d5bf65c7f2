/*Started by AICoder, pid:9e1a75e72c2dc1d14942094140c9179329776882*/
package com.zte.infrastructure.remote;
import com.alibaba.fastjson.JSON;
import com.zte.application.IMESLogService;
import com.zte.interfaces.dto.ErpProcessDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.any;
import static org.junit.Assert.assertTrue;

@PrepareForTest({HttpRemoteUtil.class})
@RunWith(PowerMockRunner.class)
public class ErpRemoteService_getErpProcessDTOList_31_Test {
    @InjectMocks
    private ErpRemoteService erpRemoteService;
    @Mock
    IMESLogService imesLogService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<ErpProcessDTO> erpProcessDTOList = new ArrayList<>();
        erpProcessDTOList.add(new ErpProcessDTO(){{setOperationSeqNum(1);}});
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(erpProcessDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(),Mockito.any(),Mockito.anyString(),Mockito.any())).thenReturn(JSON.toJSONString(serviceData));

    }

    @Test(expected = MesBusinessException.class)
    public void testGetErpProcessDTOList_EmptyTaskNo() {
        List<ErpProcessDTO> result = erpRemoteService.getErpProcessDTOList("", "1");
    }

    @Test(expected = MesBusinessException.class)
    public void testGetErpProcessDTOList_EmptyOrgId() {
        List<ErpProcessDTO> result = erpRemoteService.getErpProcessDTOList("TASKNO1", "1");
    }

    @Test(expected = MesBusinessException.class)
    public void testGetErpProcessDTOList_NullTaskNo() {
        List<ErpProcessDTO> result = erpRemoteService.getErpProcessDTOList(null, "1");
    }

    @Test(expected = MesBusinessException.class)
    public void testGetErpProcessDTOList_NullOrgId() {
        List<ErpProcessDTO> result = erpRemoteService.getErpProcessDTOList("TASKNO1", "1");
    }
}
/*Ended by AICoder, pid:9e1a75e72c2dc1d14942094140c9179329776882*/