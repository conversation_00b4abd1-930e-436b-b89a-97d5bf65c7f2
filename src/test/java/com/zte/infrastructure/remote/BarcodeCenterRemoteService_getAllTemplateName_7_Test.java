/*Started by AICoder, pid:78052ea42918ac01486108bfd063648b8fc54743*/
package com.zte.infrastructure.remote;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, BasicsettingRemoteService.class})
public class BarcodeCenterRemoteService_getAllTemplateName_7_Test {
    @Mock private BasicsettingRemoteService basicsettingRemoteService;
    @Mock private HttpRemoteUtil httpRemoteUtil;
    @InjectMocks private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void setup() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, BasicsettingRemoteService.class);
        MockitoAnnotations.initMocks(this);
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetAllTemplateName_NoLookupValue() throws Exception {
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);
        barcodeCenterRemoteService.getAllTemplateName();
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetAllTemplateName_GetTemplateError() throws Exception {
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(eq(MpConstant.LOOKUP_6679), eq(MpConstant.LOOKUP_6679001))).thenReturn(new SysLookupTypesDTO());
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(eq(MpConstant.LOOKUP_6677))).thenReturn(Arrays.asList(new SysLookupTypesDTO()));
        Mockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.anyMap(), anyString(), Mockito.any())).thenReturn("{}");
        barcodeCenterRemoteService.getAllTemplateName();
    }

    @Test(expected = Exception.class)
    public void testGetAllTemplateName_Success() throws Exception {
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(eq(MpConstant.LOOKUP_6679), eq(MpConstant.LOOKUP_6679001))).thenReturn(new SysLookupTypesDTO());
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(eq(MpConstant.LOOKUP_6677))).thenReturn(Arrays.asList(new SysLookupTypesDTO()));
        Mockito.when(BasicsettingRemoteService.getSysLookUpValue(eq(Constant.MP_CODE_KEY_1004052))).thenReturn(Arrays.asList(new SysLookupTypesDTO(){{setLookupCode(BigDecimal.ONE);setLookupMeaning("2");}}));

        Mockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.anyMap(), anyString(), Mockito.any())).thenReturn("{\"code\": {\"code\": \"SUCCESS\"}, \"bo\": []}");
        List<String> result = barcodeCenterRemoteService.getAllTemplateName();
        assertNotNull(result);
    }
}
/*Ended by AICoder, pid:78052ea42918ac01486108bfd063648b8fc54743*/