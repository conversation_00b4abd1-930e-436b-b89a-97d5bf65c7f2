package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WorkOrderOperateHis;
import com.zte.interfaces.dto.ConciseDailyDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsTaskExtraDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.PsWorkOrderSmtDTO;
import com.zte.interfaces.dto.SmtLocationInfoDTO;
import com.zte.interfaces.dto.scan.PlanSysIdempotnalInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, MicroServiceRestUtil.class, HttpRemoteService.class, InterfaceEnum.class,
        MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, MESHttpHelper.class})
public class PlanscheduleRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private PlanscheduleRemoteService service;

    @Before
    public void init() {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    /*Started by AICoder, pid:na4898bdc413292141c80b65f1efa94b37f1e50b*/
    @Test
    public void updateWorkOrderStatusBatch(){
        List<String> workOrderNoList = new ArrayList<>();
        String currentWorkOrderStatus = "1";
        String newWorkOrderStatus = "2";
        PlanscheduleRemoteService.updateWorkOrderStatusBatch(workOrderNoList, currentWorkOrderStatus, newWorkOrderStatus);
        workOrderNoList.add("123");
        PlanscheduleRemoteService.updateWorkOrderStatusBatch(workOrderNoList, currentWorkOrderStatus, newWorkOrderStatus);
        Assert.assertTrue(true);
    }
    /*Ended by AICoder, pid:na4898bdc413292141c80b65f1efa94b37f1e50b*/


    /* Started by AICoder, pid:a10fcz058fx98ed148830a93a05a7d4888029c3c */
    @Test
    public void getBasicWorkerOrderInfo(){
        PsWorkOrderDTO basic = PlanscheduleRemoteService.getBasicWorkerOrderInfo("");
        Assert.assertTrue(Objects.isNull(basic));
        basic = PlanscheduleRemoteService.getBasicWorkerOrderInfo("333");
        List<PsWorkOrderDTO> workList = new LinkedList<>();
        PsWorkOrderDTO a1 = new PsWorkOrderDTO();
        workList.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(workList);
        PlanscheduleRemoteService.getBasicWorkerOrderInfo("333");
    }

    /* Ended by AICoder, pid:a10fcz058fx98ed148830a93a05a7d4888029c3c */

    /*Started by AICoder, pid:a38bbn40a8rdff314f2309ac4166eb20b8f59bf4*/
    @Test
    public void testUpdateInPutQtyThenDeleteKey() {
        // Given
        PlanSysIdempotnalInfoDTO planSysIdempotnalInfoDTO = new PlanSysIdempotnalInfoDTO();
        String responseStr = "{\"status\": \"success\"}";

        // Mock the behavior of MicroServiceRestUtil.invokeService
        when(MicroServiceRestUtil.invokeService(
                com.zte.microservice.name.MicroServiceNameEum.PLANSCHEDULE,
                com.zte.microservice.name.MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST,
                "/PsWorkOrderBasicCtrl/updateInPutQtyThenDeleteKey",
                JacksonJsonConverUtil.beanToJson(planSysIdempotnalInfoDTO),
                MESHttpHelper.getHttpRequestHeader()))
                .thenReturn(responseStr);

        // When
        PlanscheduleRemoteService.updateInPutQtyThenDeleteKey(planSysIdempotnalInfoDTO);
        Assert.assertNull(planSysIdempotnalInfoDTO.getAlertCount());

    }
    /*Ended by AICoder, pid:a38bbn40a8rdff314f2309ac4166eb20b8f59bf4*/

    @Test
    public void findWorkOrder() {
        PsWorkOrderBasic workOrder = PlanscheduleRemoteService.findWorkOrder("123");
        Assert.assertTrue(Objects.isNull(workOrder));
    }

    @Test
    public void findWorkOrderTwo() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("1");
        PsWorkOrderBasic workOrder = PlanscheduleRemoteService.findWorkOrder("123");
        Assert.assertTrue(Objects.isNull(workOrder));
    }

    @Test
    public void getWorkOrderInfo() {
        service.getWorkOrderInfo(null, null, null, null);
        List<PsWorkOrderDTO> workOrderInfo = service.getWorkOrderInfo("kk", "dd", "dd", "dd");
        Assert.assertNull(workOrderInfo);
    }


    @Test
    public void getBasicWorkOrderInfo() {
        PlanscheduleRemoteService.getBasicWorkOrderInfo(null, null, null);

        PlanscheduleRemoteService.getBasicWorkOrderInfo("ddd", null, null);

        List<PsWorkOrderDTO> ddd = PlanscheduleRemoteService.getBasicWorkOrderInfo("ddd", "22", "22");
        Assert.assertTrue(CollectionUtils.isEmpty(ddd));
    }

    @Test
    public void getSourceTaskPage() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, BigDecimal> map = new HashMap<>();
        ServiceData<List<String>> serviceData = new ServiceData<>();
        List<String> psTaskList = new ArrayList<>();
        psTaskList.add("1");
        serviceData.setBo(psTaskList);
        map.put("777889", BigDecimal.ONE);
        Assert.assertTrue(CollectionUtils.isEmpty(service.getSourceTaskPage("", new Date(), 3, "")));
    }

    @Test
    public void batchHandleCompleteQty() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, BigDecimal> map = new HashMap<>();
        ServiceData<List<PsTask>> serviceData = new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        map.put("777889", BigDecimal.ONE);
        Assert.assertEquals("N", psTask.getWorkOrderNo());
        service.batchHandleCompleteQty(map);
    }

    @Test
    public void getPsTaskByPost() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, Object> map = new HashMap<>();
        ServiceData<List<PsTask>> serviceData = new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        Assert.assertTrue(CollectionUtils.isEmpty(service.getPsTaskByPost(map)));
    }

    @Test
    public void getWorkOrderInfoByWorkOrderStatusAndLineCode() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, Object> map = new HashMap<>();
        ServiceData<List<PsTask>> serviceData = new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        try {
            Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkOrderInfoByWorkOrderStatusAndLineCode("", "2")));
        } catch (Exception e) {
            Assert.assertEquals("call.service.failed", e.getMessage());
        }
    }

    @Test
    public void pageSelectForConciseDailyTest() throws Exception {
        List<String> prodPlanIds = new ArrayList<>();
        int page = 1;
        int row = 100;
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        ServiceData<List<PsTask>> serviceData = new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        try {
            Assert.assertTrue(CollectionUtils.isEmpty(service.pageSelectForConciseDaily(prodPlanIds, page, row)));
        } catch (Exception e) {
            Assert.assertEquals("call.service.failed", e.getMessage());
        }
    }

    @Test
    public void queryRouteIdByProdIdListTest() throws Exception {
        List<String> prodPlanIdList = new ArrayList<>();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        ServiceData<List<PsWorkOrderBasic>> serviceData = new ServiceData<>();
        List<PsWorkOrderBasic> psTaskList = new ArrayList<>();
        PsWorkOrderBasic psTask = new PsWorkOrderBasic();
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        Assert.assertTrue(CollectionUtils.isEmpty(service.queryRouteIdByProdIdList(prodPlanIdList)));
    }

    @Test
    public void getWorkFirstStartTimeByWorkNoListTest() throws Exception {
        List<String> workOrderNoList = new ArrayList<>();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        ServiceData<List<WorkOrderOperateHis>> serviceData = new ServiceData<>();
        List<WorkOrderOperateHis> psTaskList = new ArrayList<>();
        WorkOrderOperateHis psTask = new WorkOrderOperateHis();
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkFirstStartTimeByWorkNoList(workOrderNoList)));
    }

    @Test
    public void pageSelectForConciseDailyReelTimeTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        ServiceData<Page<PsTask>> serviceData = new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("test");
        psTaskList.add(psTask);
        Page<PsTask> pagePsTask = new Page<>();
        pagePsTask.setRows(psTaskList);
        serviceData.setBo(pagePsTask);
        Page<PsTask> result = service.pageSelectForConciseDailyReelTime(conciseDailyDTO);
        Assert.assertEquals(0, result.getTotalPage());
    }

    @Test
    public void selectPsTaskByProdIdSetTest() throws Exception {
        Set<String> set = new HashSet<>();
        set.add("ssss");

        Assert.assertTrue(CollectionUtils.isEmpty(service.selectPsTaskByProdIdSet(set)));
    }

    @Test
    public void getWorkWorkInfoForCal() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);

        Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkWorkInfoForCal("test")));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkWorkInfoForCal("test")));
    }

    @Test
    public void getWorkOrderInfoByLineCode() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PsWorkOrderDTO dto = new PsWorkOrderDTO();
        dto.setLineCode("test");
        dto.setWorkOrderStatusList(new ArrayList<>());
        Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkOrderInfoByLineCode(dto)));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        Assert.assertTrue(CollectionUtils.isEmpty(service.getWorkOrderInfoByLineCode(dto)));
    }

    @Test
    public void getInfoByWorkOrderNo() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);

        Assert.assertTrue(CollectionUtils.isEmpty(service.getInfoByWorkOrderNo("test")));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        Assert.assertTrue(CollectionUtils.isEmpty(service.getInfoByWorkOrderNo("test")));
    }

    @Test
    public void queryRouteIdDistinctByProdIdListTest() throws Exception {
        List<String> set = new ArrayList<>();
        set.add("ssss");

        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        Assert.assertTrue(CollectionUtils.isEmpty(service.queryRouteIdDistinctByProdIdList(set)));
    }

    @Test
    public void updateInputOutQtyForRefresh() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        List<String> prodPlanIdList = new ArrayList<>();
        JsonNode json = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":true\n" +
                "  \n" +
                "}", JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(json);
        Assert.assertTrue(StringUtils.isBlank(PlanscheduleRemoteService.updateInputOutQtyForRefresh(prodPlanIdList)));
    }

    @Test
    public void getPsTask() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, Object> map = new HashMap<>();
        map.put("page", new Long("1"));
        map.put("rows", new Long("1"));
        ServiceData<PageRows<PsTask>> serviceData = new ServiceData<>();

        PageRows<PsTask> psTaskPage = new PageRows<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        psTaskPage.setRows(psTaskList);
        serviceData.setBo(psTaskPage);
        Assert.assertTrue(CollectionUtils.isEmpty(service.getPsTask(map)));
    }

    @Test
    public void getLineCodeInfoBySourceTask() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        JsonNode json = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  \t{\n" +
                "  \t\t\"prodplanId\":\"123456\"\n" +
                "  \t}\n" +
                "  ]\n" +
                "}", JsonNode.class);

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        any(), any(), any(), any(), any()))
                .thenReturn(json);
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getLineCodeInfoBySourceTask("test")));
    }

    @Test
    public void getTaskListByProdPlanId() {
        Assert.assertTrue(CollectionUtils.isEmpty(service.getTaskListByProdPlanId(null)));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        Assert.assertTrue(CollectionUtils.isEmpty(service.getTaskListByProdPlanId(Arrays.asList("12345"))));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("kk");
        Assert.assertTrue(CollectionUtils.isEmpty(service.getTaskListByProdPlanId(Arrays.asList("12345"))));
    }

    @Test
    public void batchUpdatePsTask() {
        service.batchUpdatePsTask(null);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        List<PsTask> list = new LinkedList<>();
        PsTask a1 = new PsTask();
        list.add(a1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service.batchUpdatePsTask(list);
    }

    @Test
    public void finishWithChildForScan() throws Exception {
        PsEntityPlanBasicDTO planBasicDTO = new PsEntityPlanBasicDTO();
        planBasicDTO.setWorkOrderNo("test");
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        Assert.assertEquals("test", planBasicDTO.getWorkOrderNo());
        service.finishWithChildForScan(planBasicDTO);
    }

    @Test
    public void getLineList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        JsonNode json = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":true\n" +
                "  \n" +
                "}", JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(json);
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getLineList(new ArrayList<>())));
        JsonNode json1 = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  \t{\n" +
                "  \t\t\"prodplanId\":\"123456\"\n" +
                "  \t}\n" +
                "  ]\n" +
                "}", JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(json1);
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getLineList(new ArrayList<>())));
    }

    @Test
    public void testGetWorkOrderInfoByTaskNoAndProcess() {
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(null, null)));
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess("1234", null)));
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess(null, "P0123")));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("kk");
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getWorkOrderInfoByTaskNoAndProcess("1234", "P0123")));
    }

    @Test
    public void getPstask() {
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getPstaskNew(new HashMap<>())));
    }

    @Test
    public void getPsTaskByTaskNoList() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add("test123");
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getPsTaskByTaskNoList(new ArrayList<>())));
        JsonNode json = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":true\n" +
                "  \n" +
                "}", JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(json);
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getPsTaskByTaskNoList(prodPlanIdList)));
        JsonNode json1 = JacksonJsonConverUtil.jsonToBean("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":[\n" +
                "  \t{\n" +
                "  \t\t\"prodplanId\":\"123456\"\n" +
                "  \t}\n" +
                "  ]\n" +
                "}", JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(json1);
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getPsTaskByTaskNoList(prodPlanIdList)));
    }

    @Test
    public void getWorkOrderInfoWithPsTaskPage() {
        Assert.assertNull(PlanscheduleRemoteService.getWorkOrderInfoWithPsTaskPage(new HashMap<>()));
    }

    @Test
    public void getProgramStatusByProdplanIds() {
        Assert.assertTrue(CollectionUtils.isEmpty(PlanscheduleRemoteService.getProgramStatusByProdplanIds(new ArrayList<>())));
    }

    @Test
    public void getLineCodeBySourceTask() {
        ServiceData<List<String>> serviceData = new ServiceData<>();
        List<String> list = new ArrayList<>();
        serviceData.setBo(list);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertEquals(0, PlanscheduleRemoteService.getLineCodeBySourceTask("8899885").size());

        list.add("test");
        serviceData.setBo(list);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(1, PlanscheduleRemoteService.getLineCodeBySourceTask("8899885").size());
    }

    @Test
    public void getSourceTaskByLineCode() {
        ServiceData<List<String>> serviceData = new ServiceData<>();
        List<String> list = new ArrayList<>();
        serviceData.setBo(list);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertEquals(0, PlanscheduleRemoteService.getSourceTaskByLineCode("SMT-TZ002").size());

        list.add("8899855");
        serviceData.setBo(list);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(1, PlanscheduleRemoteService.getSourceTaskByLineCode("SMT-TZ002").size());
    }

    @Test
    public void getByWorkOrders() {
        ServiceData<List<PsWorkOrderSmtDTO>> serviceData = new ServiceData<>();
        List<String> workOrderNoList = new ArrayList<>();
        List<PsWorkOrderSmtDTO> list = new ArrayList<>();
        serviceData.setBo(list);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertEquals(0, PlanscheduleRemoteService.getByWorkOrders(workOrderNoList).size());

        PsWorkOrderSmtDTO workOrderSmtDTO = new PsWorkOrderSmtDTO();
        workOrderSmtDTO.setWorkOrderNo("8899855-A550001");
        list.add(workOrderSmtDTO);
        serviceData.setBo(list);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(1, PlanscheduleRemoteService.getByWorkOrders(workOrderNoList).size());
    }

    @Test
    public void getWorkOrderInfoByQuery() {
        ServiceData<List<PsWorkOrderDTO>> serviceData = new ServiceData<>();
        List<PsWorkOrderDTO> list = new ArrayList<>();
        serviceData.setBo(list);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertEquals(0, PlanscheduleRemoteService.getWorkOrderInfoByQuery(new HashMap()).size());

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("8899855-SMT-B0001");
        list.add(psWorkOrderDTO);
        serviceData.setBo(list);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(1, PlanscheduleRemoteService.getWorkOrderInfoByQuery(new HashMap()).size());
    }

    @Test
    public void getTaskByWorkOrderNo() {
        ServiceData<PsWorkOrderBasic> serviceData = new ServiceData<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        serviceData.setBo(psWorkOrderBasic);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertNull(PlanscheduleRemoteService.getTaskByWorkOrderNo("workOrderNo"));

        serviceData.setBo(null);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(psWorkOrderBasic);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        Assert.assertNull(PlanscheduleRemoteService.getTaskByWorkOrderNo("workOrderNo"));
    }

    @Test
    public void getExtraAttr() {
        ServiceData<PsTaskExtraDTO> serviceData = new ServiceData<>();
        PsTaskExtraDTO psTaskExtraDTO = new PsTaskExtraDTO();
        String prodplanId = "workOrderNo";
        String extraType = "extraType";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("serviceDataStr");
        Assert.assertNotNull(PlanscheduleRemoteService.getExtraAttr("workOrderNo", "extraType"));
        serviceData.setBo(null);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(psTaskExtraDTO);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PlanscheduleRemoteService.getExtraAttr("workOrderNo", "extraType");
    }

    @Test
    public void getTaskAndItemUsageCountByProCodeAndItemNo() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        Assert.assertNotNull(PlanscheduleRemoteService.getTaskAndItemUsageCountByProCodeAndItemNo("TEST"));
    }

    @Test
    public void getTaskAndItemUsageCountByProCodeAndItemNoTwo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        Assert.assertNull(PlanscheduleRemoteService.getTaskAndItemUsageCountByProCodeAndItemNo("test"));
    }

    @Test
    public void queryPreProcessInfo() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        Assert.assertNotNull(PlanscheduleRemoteService.queryPreProcessInfo("test","test"));
    }

    @Test
    public void queryPreProcessInfoTwo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        Assert.assertNull(PlanscheduleRemoteService.queryPreProcessInfo("test","test"));
    }

    @Test
    public void getPstaskByProdIdTest() {
        PsTask psTask = service.getPstaskByProdId("");
        Assert.assertTrue(psTask == null);

        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        ServiceData<List<PsTask>> serviceData = new ServiceData<>();
        ArrayList<PsTask> psTasks = new ArrayList<PsTask>() {{
            add(new PsTask() {{
                setProdplanId("123");
            }});
        }};
        serviceData.setBo(psTasks);
        String serviceDataStr1 = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(psTasks);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(psTasks);
        PsTask psTask1 = service.getPstaskByProdId("test");

        Assert.assertTrue(psTask1 != null);
    }

    @Test
    public void selectSmtWorkOrderBySourceTask() {
        ServiceData<PsTaskExtraDTO> serviceData = new ServiceData<>();
        Map<String, String> params = new HashMap<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("serviceDataStr");
        Assert.assertNotNull(PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(params));
        serviceData.setBo(null);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);

        List<PsEntityPlanBasic> list = new ArrayList<>();
        PsEntityPlanBasic planBasic = new PsEntityPlanBasic();
        list.add(planBasic);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PlanscheduleRemoteService.selectSmtWorkOrderBySourceTask(params);
    }

    @Test
    public void checkIsPartTask() {
        ServiceData<PsTaskExtraDTO> serviceData = new ServiceData<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("serviceDataStr");
        Assert.assertNotNull(PlanscheduleRemoteService.checkIsPartTask("1"));
        Assert.assertNotNull(serviceData);
        serviceData.setBo(null);
        String serviceDataStr1 = JSON.toJSONString(serviceData);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);

        List<PsEntityPlanBasic> list = new ArrayList<>();
        PsEntityPlanBasic planBasic = new PsEntityPlanBasic();
        list.add(planBasic);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PlanscheduleRemoteService.checkIsPartTask("params");
        Assert.assertNotNull(planBasic);
    }

    @Test
    public void getItemNoByWorkOrderNoList() {
        ServiceData<List<PsWorkOrderBasicDTO>> serviceData = new ServiceData<>();
        List<String> workOrderNoList = new ArrayList<>();
        Assert.assertEquals(0, PlanscheduleRemoteService.getItemNoByWorkOrderNoList(workOrderNoList).size());

        workOrderNoList.add("8899855-A550001");
        List<PsWorkOrderBasicDTO> list = new ArrayList<>();
        serviceData.setBo(null);
        String serviceDataStr = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        Assert.assertEquals(0, PlanscheduleRemoteService.getItemNoByWorkOrderNoList(workOrderNoList).size());

        PsWorkOrderBasicDTO workOrderSmtDTO = new PsWorkOrderBasicDTO();
        workOrderSmtDTO.setWorkOrderNo("8899855-A550001");
        list.add(workOrderSmtDTO);
        serviceData.setBo(list);
        String serviceDataStr1 = JSON.toJSONString(serviceData);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(1, PlanscheduleRemoteService.getItemNoByWorkOrderNoList(workOrderNoList).size());
    }
    /* Started by AICoder, pid:n93a8b13d17650814fe80b785070d410b5e84915 */
    @Test
    public void getSubTaskInfoByZbjPlanNoListTest() {
        List<String> zbjPlanNoList = new ArrayList<>();

        String serviceDataStr1 = "";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(serviceDataStr1);
        List<PsTask> list = new ArrayList<>();
        String bo = JSON.toJSONString(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        List<PsTask> subTaskInfoByZbjPlanNoList = service.getSubTaskInfoByZbjPlanNoList(zbjPlanNoList);
        Assert.assertTrue(subTaskInfoByZbjPlanNoList.size() == 0);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        list.add(new PsTask());
        List<PsTask> subTaskInfoByZbjPlanNoList1 = service.getSubTaskInfoByZbjPlanNoList(zbjPlanNoList);
        Assert.assertTrue(subTaskInfoByZbjPlanNoList1.size() == 1);
    }
    /* Ended by AICoder, pid:n93a8b13d17650814fe80b785070d410b5e84915 */

    @Test
    public void test_getPsTaskExtras() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.getProductNameByNum)).thenReturn("343543545454");
        List<PsTaskExtraDTO> psTaskExtraDTOS = new ArrayList<>();
        Assert.assertTrue(PlanscheduleRemoteService.getPsTaskExtras(psTaskExtraDTOS).isEmpty());
        PsTaskExtraDTO dto = new PsTaskExtraDTO();
        dto.setTaskNo("1");
        psTaskExtraDTOS.add(dto);
        String msg = JSON.toJSONString(new ServiceData<List<PsTaskExtraDTO>>() {{
            setBo(psTaskExtraDTOS);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(msg);
        Assert.assertNull(PlanscheduleRemoteService.getPsTaskExtras(psTaskExtraDTOS));
    }
}
