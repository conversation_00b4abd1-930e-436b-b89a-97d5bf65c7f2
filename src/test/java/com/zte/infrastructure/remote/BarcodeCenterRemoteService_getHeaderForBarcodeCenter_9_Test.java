/*Started by AICoder, pid:c95ceoe703z801c14e030825c18e270d88f4a236*/
package com.zte.infrastructure.remote;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, BasicsettingRemoteService.class})
public class BarcodeCenterRemoteService_getHeaderForBarcodeCenter_9_Test {
    @Mock private BasicsettingRemoteService sysLookupValuesService;

    @InjectMocks private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class,BasicsettingRemoteService.class);
        MockitoAnnotations.initMocks(this);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetHeaderForBarcodeCenter_NoValues() throws Exception {
        Mockito.when(sysLookupValuesService.getSysLookUpValue(Mockito.anyString())).thenReturn(Collections.emptyList());
        barcodeCenterRemoteService.getHeaderForBarcodeCenter();
    }

    @Test(expected = MesBusinessException.class)
    public void testGetHeaderForBarcodeCenter_NullValue() throws Exception {
        List<SysLookupTypesDTO> lookupValues = new ArrayList<>();
        lookupValues.add(null);
        Mockito.when(sysLookupValuesService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
        barcodeCenterRemoteService.getHeaderForBarcodeCenter();
    }

    @Test(expected = MesBusinessException.class)
    public void testGetHeaderForBarcodeCenter_NullMeaning() throws Exception {
        List<SysLookupTypesDTO>  lookupValues = new ArrayList<>();
        SysLookupTypesDTO value = new SysLookupTypesDTO();
        value.setLookupCode(BigDecimal.ONE);
        lookupValues.add(value);
        Mockito.when(sysLookupValuesService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
        barcodeCenterRemoteService.getHeaderForBarcodeCenter();
    }

    @Test(expected = MesBusinessException.class)
    public void testGetHeaderForBarcodeCenter_NullCode() throws Exception {
        List<SysLookupTypesDTO>  lookupValues = new ArrayList<>();
        SysLookupTypesDTO value = new SysLookupTypesDTO();
        value.setLookupMeaning("meaning");
        lookupValues.add(value);
        Mockito.when(sysLookupValuesService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
        barcodeCenterRemoteService.getHeaderForBarcodeCenter();
    }

    @Test
    public void testGetHeaderForBarcodeCenter_ValidValues() throws Exception {
        List<SysLookupTypesDTO>  lookupValues = new ArrayList<>();
        SysLookupTypesDTO value = new SysLookupTypesDTO();
        value.setLookupCode(BigDecimal.ONE);
        value.setLookupMeaning("meaning");
        lookupValues.add(value);
        Mockito.when(sysLookupValuesService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
        Map<String, String> result = barcodeCenterRemoteService.getHeaderForBarcodeCenter();
        assertNull(result);
    }
}
/*Ended by AICoder, pid:c95ceoe703z801c14e030825c18e270d88f4a236*/