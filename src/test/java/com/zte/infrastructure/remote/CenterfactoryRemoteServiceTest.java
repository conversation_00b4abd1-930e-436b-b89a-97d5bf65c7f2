package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.FixBomDetailDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteService.class, JacksonJsonConverUtil.class, CommonUtils.class, MicroServiceDiscoveryInvoker.class,
        HttpClientUtil.class, ConstantInterface.class, HttpRemoteUtil.class, JSON.class, ServiceDataBuilderUtil.class, MicroServiceRestUtil.class})
public class CenterfactoryRemoteServiceTest {
    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private HttpRemoteUtil httpRemoteUtil;

    @Mock
    private ServiceDataBuilderUtil serviceDataBuilderUtil;
    @Mock
    private ConstantInterface constantInterface;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    /* Started by AICoder, pid:mbd123e2efsf29414802088c40f33e335f75df94 */
    @Test
    public void queryPushDataListTest(){
        List<PushBoardDataProcessDTO> list = centerfactoryRemoteService.queryPushDataList(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
        centerfactoryRemoteService.pushDataToB2BByKafka(null);
        List<CustomerDataLogDTO> dataList = new LinkedList<>();
        for (int i = 0; i < 5; i++) {
            CustomerDataLogDTO da = new CustomerDataLogDTO();
            dataList.add(da);
        }
        centerfactoryRemoteService.pushDataToB2BByKafka(dataList);
        list = new LinkedList<>();
        centerfactoryRemoteService.updateBoardDataProcessBatch(null);
        for (int i = 0; i < 4; i++) {
            PushBoardDataProcessDTO a1 = new PushBoardDataProcessDTO();
            list.add(a1);
        }
        centerfactoryRemoteService.updateBoardDataProcessBatch(list);
    }
    /* Ended by AICoder, pid:mbd123e2efsf29414802088c40f33e335f75df94 */

    @Test
    public void testGetSpecificTaskExtended() {
        PowerMockito.mockStatic(ConstantInterface.class);
        List<String> taskNos = new ArrayList<>();
        assertTrue(centerfactoryRemoteService.getSpecificTaskExtended(taskNos).isEmpty());
        taskNos = Arrays.asList("task1", "task2");
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,MicroServiceRestUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("{}");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),any())).thenReturn(Collections.emptyList());
        List<PsTaskExtendedDTO> result = centerfactoryRemoteService.getSpecificTaskExtended(taskNos);
        assertTrue(org.apache.commons.collections4.CollectionUtils.isEmpty(result));
        }

    @Test
    public void test_getCustomerItemsInfo() throws Exception {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        try {
            centerfactoryRemoteService.getCustomerItemsInfo(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(true);
        }
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        String msg = JSON.toJSONString(new ServiceData<List<CustomerItemsDTO>>() {{
            setBo(Collections.singletonList(customerItemsDTO));
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(msg);
        centerfactoryRemoteService.getCustomerItemsInfo(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void testQueryByTaskNos() {
        List<String> taskNos = Arrays.asList("task1", "task2");
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,MicroServiceRestUtil.class);
        when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("{}");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),any())).thenReturn(Collections.emptyList());
        List<PsTaskExtendedDTO> result = centerfactoryRemoteService.queryByTaskNos(taskNos);
        assertTrue(org.apache.commons.collections4.CollectionUtils.isEmpty(result));
    }

    @Test
    public void getReconfigurationFlag() {
        PowerMockito.mockStatic(ConstantInterface.class);
        Assert.assertFalse(centerfactoryRemoteService.getReconfigurationFlag(""));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(true);
                }})
        );
        centerfactoryRemoteService.getReconfigurationFlag("123");
        Assert.assertTrue(true);
    }

    @Test
    public void getFixBomByTaskNo() {
        Assert.assertTrue(CollectionUtils.isEmpty(centerfactoryRemoteService.getFixBomByTaskNo("")));
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");

        List<FixBomDetailDTO> fixBomDetailDTOS = new ArrayList<>();
        FixBomDetailDTO dto = new FixBomDetailDTO();
        fixBomDetailDTOS.add(dto);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(fixBomDetailDTOS);
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(fixBomDetailDTOS));
        Assert.assertTrue(CollectionUtils.isNotEmpty(centerfactoryRemoteService.getFixBomByTaskNo("123")));
    }

    /*Started by AICoder, pid:r2322fba03s5531143be0905107d4d6e36186f2d*/
    @Test
    public void testPushStdModelSnData_EmptyList() {
        List<PushStdModelSnDataDTO> mergeList = Arrays.asList();

        centerfactoryRemoteService.pushStdModelSnData(mergeList);

        verifyZeroInteractions(httpRemoteUtil);
        verifyZeroInteractions(serviceDataBuilderUtil);
        Assert.assertNotNull(mergeList);
    }

    @Test
    public void testPushStdModelSnData_NonEmptyList() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        List<PushStdModelSnDataDTO> mergeList = Arrays.asList(new PushStdModelSnDataDTO());
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.mergeSnToPushStdModelSnData);
        String result = "{\"status\":\"success\"}";

        Mockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(com.zte.microservice.name.MicroServiceNameEum.SENDTYPEPOST)))
                .thenReturn(result);

        centerfactoryRemoteService.pushStdModelSnData(mergeList);
        Assert.assertNotNull(mergeList);
    }
    /*Ended by AICoder, pid:r2322fba03s5531143be0905107d4d6e36186f2d*/
}
