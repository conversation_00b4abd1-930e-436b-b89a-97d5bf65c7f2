package com.zte.infrastructure.remote;

import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashMap;

@PrepareForTest({HttpRemoteUtil.class, JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore("javax.management.*")
public class InoneRemoteServiceTest extends PowerBaseTestCase {
    InoneRemoteService inoneRemoteService = new InoneRemoteService();

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void testSendToInone() throws Exception {
        String result = inoneRemoteService.sendToInone("url", "sendMethod", new HashMap<>());
        Assert.assertNull(result);

    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme