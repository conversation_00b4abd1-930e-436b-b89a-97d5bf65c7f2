package com.zte.infrastructure.remote;

import com.zte.common.HttpClientUtils;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.storage.StorageDetailsDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class, JacksonJsonConverUtil.class,
        HttpRemoteUtil.class, ServiceDataBuilderUtil.class, HttpRemoteService.class, HttpClientUtils.class})
public class StorageCenterRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private StorageCenterRemoteService storageCenterRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void queryStockFlowCustomized() {
        StorageDetailsDTO storageDetailsDTO = new StorageDetailsDTO();
        try {
            storageCenterRemoteService.queryStockFlowCustomized(storageDetailsDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.LOOKUP_NO_IS_NULL.equals(e.getMessage()));
        }

        List<SysLookupValuesDTO> valuesDTOList = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        valuesDTOList.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any()))
                .thenReturn(valuesDTOList);
        List<StorageDetailsDTO> list = storageCenterRemoteService.queryStockFlowCustomized(storageDetailsDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(list));

        List<StorageDetailsDTO> resultList = new ArrayList<>();
        resultList.add(new StorageDetailsDTO());

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(resultList);
        list = storageCenterRemoteService.queryStockFlowCustomized(storageDetailsDTO);
        Assert.assertTrue(!CollectionUtils.isEmpty(list));
    }

    @Test
    public void getInforDemandInfo() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(HttpClientUtils.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> valuesDTOList = new LinkedList<>();
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.any()))
                .thenReturn(valuesDTOList);
        Assert.assertNotNull(storageCenterRemoteService.getInforDemandInfo("test"));
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupCode(new BigDecimal("6926003"));
        a1.setLookupMeaning("233");
        valuesDTOList.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.any()))
                .thenReturn(valuesDTOList);
        try{
            storageCenterRemoteService.getInforDemandInfo("test");
        }catch(Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValuesDTO a2 = new SysLookupValuesDTO();
        a2.setLookupCode(new BigDecimal("6926001"));
        a2.setLookupMeaning("233");
        valuesDTOList.add(a2);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.any()))
                .thenReturn(valuesDTOList);
        try{
            storageCenterRemoteService.getInforDemandInfo("test");
        }catch(Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValuesDTO a3 = new SysLookupValuesDTO();
        a3.setLookupCode(new BigDecimal("6926002"));
        a3.setLookupMeaning("233");
        valuesDTOList.add(a3);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.any()))
                .thenReturn(valuesDTOList);
        String bo = "{\"bo\":[{\"EDITDATE\":\"2012-11-07T09:28:12.000Z\",\"EXTERNALORDERKEY2\":\"lzw121105CF13-z-1\"},{\"EDITDATE\":\"2012-11-09T19:01:50.000Z\",\"EXTERNALORDERKEY2\":\"lzw121105CF13-z-1\"}],\"code\":{\"code\":\"0000\",\"msg\":\"操作成功\",\"msgId\":\"RetCode.Success\"}}\n";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(bo);
        Assert.assertNotNull(storageCenterRemoteService.getInforDemandInfo("test"));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        Assert.assertNull(storageCenterRemoteService.getInforDemandInfo("test"));
    }

    @Test
    public void getStockFlowAndErp() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(HttpClientUtils.class);

        StorageDetailsDTO storageDetailsDTO = new StorageDetailsDTO();
        List<SysLookupValuesDTO> valuesDTOList = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupCode(new BigDecimal("1004037016"));
        a1.setLookupMeaning("233");
        valuesDTOList.add(a1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any()))
                .thenReturn(valuesDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("OK");
        Assert.assertNotNull(storageCenterRemoteService.getStockFlowAndErp(storageDetailsDTO));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("[]");
        Assert.assertNull(storageCenterRemoteService.getStockFlowAndErp(storageDetailsDTO));
    }
}