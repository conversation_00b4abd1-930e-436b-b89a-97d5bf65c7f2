/*Started by AICoder, pid:fa17b8c340d54cdf841e7785b8c51837*/
package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.application.ErpLogService;
import com.zte.application.IMESLogService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.eai.common.input.Input;
import com.zte.interfaces.dto.ErpProcessDTO;
import com.zte.interfaces.dto.LocatorInfoDTO;
import com.zte.interfaces.dto.PDMProductMaterialResultDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WipOperationsDto;
import com.zte.interfaces.dto.erp.GetOnhandQtyDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import oerp_m04_importmaterialtrxinfosvr.ArrayOfError;
import oerp_m04_importmaterialtrxinfosvr.ImportMaterialTrxInfoSvrResponse;
import oerp_m04_importmaterialtrxinfosvr.ObjectOfErrorRec;
import oerp_m04_importmaterialtrxinfosvr.ObjectOfOutput;
import oerp_m12_importoperationmoveinfosrv.ArrayOfInputItem;
import oerp_m12_importoperationmoveinfosrv.OERPM12ImportOperationMoveInfoSrvResponse;
import oerp_m12_importoperationmoveinfosrv.ObjectFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.cxf.endpoint.ClientImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.anyMap;
import static org.powermock.api.mockito.PowerMockito.when;


@PrepareForTest({ErpRemoteService.class, BasicsettingRemoteService.class, DatawbRemoteService.class,
        PlanscheduleRemoteService.class, HttpRemoteService.class,JSON.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class, CommonUtils.class})
public class ErpRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    ErpRemoteService service;
    @Mock
    ClientImpl client;
    @Mock
    private RedisLock redisLock;
    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Mock
    private ErpLogService erpLogService;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private String jsonString;
    @Mock
    private IMESLogService imesLogService;

    ObjectFactory objectFactory = new ObjectFactory();
    oerp_m04_importmaterialtrxinfosvr.ObjectFactory impMatTrxFactory = new oerp_m04_importmaterialtrxinfosvr.ObjectFactory();
    @Before
    public void init () {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, DatawbRemoteService.class,
                PlanscheduleRemoteService.class, HttpRemoteService.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
    }

    @Test
    public void erpSwitchOn() {
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.anyString())).thenReturn("off");
        boolean switchOn = ErpRemoteService.erpSwitchOn();
        assert !switchOn;
        Assert.assertFalse(switchOn);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.anyString())).thenReturn("on");
        switchOn = ErpRemoteService.erpSwitchOn();
        assert switchOn;
    }
    @Test
    public void setTheStartOfTheProcessNewTest()throws Exception {
        ReflectionTestUtils.setField(service, "erpTaskStartProcessSwitch", false);
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        service.setTheStartOfTheProcessNew("warehouseEntryInfo","",new ArrayOfInputItem());
        Assert.assertNotNull(warehouseEntryInfo);

        ReflectionTestUtils.setField(service, "erpTaskStartProcessSwitch", true);
        List<ErpProcessDTO> erpProcessDTOList = new ArrayList<>();
        when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        try {
            service.setTheStartOfTheProcessNew("warehouseEntryInfo","1",new ArrayOfInputItem());
        } catch (MesBusinessException e) {
            assert MessageId.THE_ERP_START_OF_THE_QUERY_TASK_IS_INCORRECT.equals(e.getExMsgId());
        }
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(erpProcessDTOList));
        try {
            service.setTheStartOfTheProcessNew("warehouseEntryInfo","1",new ArrayOfInputItem());
        } catch (MesBusinessException e) {
            assert MessageId.THE_ERP_START_OF_THE_QUERY_TASK_IS_INCORRECT.equals(e.getExMsgId());
        }
        erpProcessDTOList.clear();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(erpProcessDTOList));
        try {
            service.setTheStartOfTheProcessNew("warehouseEntryInfo","1",new ArrayOfInputItem());
        } catch (MesBusinessException e) {
            assert MessageId.THE_ERP_START_OF_THE_QUERY_TASK_IS_INCORRECT.equals(e.getExMsgId());
        }
        erpProcessDTOList.add(new ErpProcessDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(erpProcessDTOList));
        service.setTheStartOfTheProcessNew("warehouseEntryInfo","1",new ArrayOfInputItem());
        Assert.assertNotNull(warehouseEntryInfo);
    }

    @Test
    public void invokeErpOutbound() throws Exception {
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_EIGHT);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SEVEN);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_TYPE_ERP_ATT_PROCESS);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        SysLookupValuesDTO s4 = new SysLookupValuesDTO();
        s4.setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_STOCK_ERP_TIME));
        s4.setLookupMeaning("-5");
        lookupValueList.add(s4);
        SysLookupValuesDTO s5 = new SysLookupValuesDTO();
        s5.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SIX);
        s5.setLookupMeaning("-5");
        lookupValueList.add(s5);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyMap())).thenReturn(lookupValueList);
        WarehouseEntryInfo warehouseEntryInfo = getWarehouseEntryInfo();
        ServiceData ret = service.invokeErpOutbound(warehouseEntryInfo);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        s2.setLookupMeaning(MpConstant.ERP_SWITCH_ON);
        ret = service.invokeErpOutbound(warehouseEntryInfo);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        ret = service.invokeErpOutbound(warehouseEntryInfo);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        ret = service.invokeErpOutbound(warehouseEntryInfo);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        warehouseEntryInfo.setItemNo("");
        try {
            service.invokeErpOutbound(warehouseEntryInfo);
        } catch (MesBusinessException e) {
            assert MessageId.ITEM_NO_NULL.equals(e.getExMsgId());
        }
        warehouseEntryInfo.setItemNo("123");
        try {
            service.invokeErpOutbound(warehouseEntryInfo);
        } catch (MesBusinessException e) {
            assert MessageId.ITEM_NO_NULL.equals(e.getExMsgId());
        }
    }

    private ImportMaterialTrxInfoSvrResponse getErrorResponse() {
        ImportMaterialTrxInfoSvrResponse svrResponse = new ImportMaterialTrxInfoSvrResponse();
        ObjectOfOutput output = new ObjectOfOutput();
        svrResponse.setOutput(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseOutput(output));
        output.setRETURNSTATUS(impMatTrxFactory.createObjectOfOutputRETURNSTATUS("E"));
        ObjectOfErrorRec errorRec = new ObjectOfErrorRec();
        ArrayOfError error = new ArrayOfError();
        error.setERRORMESSAGE(impMatTrxFactory.createArrayOfErrorERRORMESSAGE("error msg"));
        errorRec.getError().add(error);
        svrResponse.setErrorRec(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseErrorRec(errorRec));
        return svrResponse;
    }

    private ImportMaterialTrxInfoSvrResponse getSuccessResponse() {
        ImportMaterialTrxInfoSvrResponse svrResponse = new ImportMaterialTrxInfoSvrResponse();
        ObjectOfOutput output = new ObjectOfOutput();
        output.setRETURNSTATUS(impMatTrxFactory.createObjectOfOutputRETURNSTATUS(Constant.ERP_WSDL_MSG_STATUS_SUCCESS));
        svrResponse.setOutput(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseOutput(output));
        return svrResponse;
    }

    private static WarehouseEntryInfo getWarehouseEntryInfo() {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("012345567890123abc");
        warehouseEntryInfo.setCommitedQty(new BigDecimal(2));
        warehouseEntryInfo.setWarehouseEntryId("123");
        warehouseEntryInfo.setErpMoveQty(BigDecimal.TEN);
        return warehouseEntryInfo;
    }

    @Test
    public void invokeErpImportForZk() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = getWarehouseEntryInfo();

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        ServiceData ret = service.invokeErpImportForZk(warehouseEntryInfo);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(redisLock.lock()).thenReturn(true);

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_EIGHT);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SEVEN);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_TYPE_ERP_ATT_PROCESS);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);

        ret = service.invokeErpImportForZk(warehouseEntryInfo);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        s2.setLookupMeaning(MpConstant.ERP_SWITCH_ON);
        ret = service.invokeErpImportForZk(warehouseEntryInfo);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        ret = service.invokeErpImportForZk(warehouseEntryInfo);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        ret = service.invokeErpImportForZk(warehouseEntryInfo);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());
    }

    @Test
    public void invokeErpImportForZkBatch() throws Exception {
        PowerMockito.mockStatic(ErpRemoteService.class);
        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(false);
        List<WarehouseEntryInfo> list = new ArrayList<>();
        list.add(getWarehouseEntryInfo());
        ServiceData ret = service.invokeErpImportForZkBatch(list);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(ErpRemoteService.erpSwitchOn()).thenReturn(true);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        ret = service.invokeErpImportForZkBatch(list);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.anyString())).thenReturn("http://test");
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        ret = service.invokeErpImportForZkBatch(list);
        assert RetCode.BUSINESSERROR_CODE.equals(ret.getCode().getCode());

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        ret = service.invokeErpImportForZkBatch(list);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.mockStatic(JSON.class);
        PowerMockito.when(JSON.toJSONString(Mockito.any()))
                .thenReturn(jsonString);
        PowerMockito.when(jsonString.length()).thenReturn(5000);
        PowerMockito.when(jsonString.substring(Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn("123");
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        service.invokeErpImportForZkBatch(list);

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        ret = service.invokeErpImportForZkBatch(list);

    }

    @Test
    public void invokeErpImport() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = getWarehouseEntryInfo();
        StringBuffer errMsg = new StringBuffer();
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        boolean result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == false;

        PowerMockito.when(redisLock.lock()).thenReturn(true);

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_EIGHT);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SEVEN);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_TYPE_ERP_ATT_PROCESS);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == true;

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        s2.setLookupMeaning(MpConstant.ERP_SWITCH_ON);
        result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == false;

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        warehouseEntryInfo.setOrgId(null);
        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("777766600001");
        warehouseEntryInfo.setSnList(Arrays.asList(psWipInfoDTO));
        result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == true;

        ServiceData<List<String>> serviceData = new ServiceData<>();
        PowerMockito.when(HttpRemoteService.remoteExePostForRequestJson(any(InterfaceEnum.class), any(), any(), any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            service.invokeErpImport(warehouseEntryInfo, errMsg);
        } catch (MesBusinessException e) {
            assert MessageId.CREATE_BILLNO_ERROR.equals(e.getExMsgId());
        }

        List<String> list = Arrays.asList("111");
        serviceData.setBo(list);
        PowerMockito.when(HttpRemoteService.remoteExePostForRequestJson(any(InterfaceEnum.class), any(), any(), any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == true;

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        result = service.invokeErpImport(warehouseEntryInfo, errMsg);
        assert result == false;
    }

    @Test
    public void invokeErpMove() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = getWarehouseEntryInfo();
        StringBuffer errMsg = new StringBuffer();
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        boolean result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        PowerMockito.when(redisLock.lock()).thenReturn(true);

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_EIGHT);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SEVEN);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_ONE);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        SysLookupValuesDTO s4 = new SysLookupValuesDTO();
        s4.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_NINE);
        s4.setLookupMeaning("5");
        lookupValueList.add(s4);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == true;

        s2.setLookupMeaning(MpConstant.ERP_SWITCH_ON);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == true;

        PowerMockito.mockStatic(ErpRemoteService.class);
        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = getMoveErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);
        List<WarehouseEntryInfo> moveList = new ArrayList<>();
        WarehouseEntryInfo entryInfo = new WarehouseEntryInfo();
        entryInfo.setErpMoveQty(BigDecimal.ONE);
        entryInfo.setCommitedQty(BigDecimal.ONE);
        moveList.add(entryInfo);
        PowerMockito.when(warehouseEntryInfoService.getWarehouseEntryInfoList(Mockito.anyMap())).thenReturn(moveList);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        entryInfo.setErpMoveStatus(Constant.ERP_UNKNOWN_ERROR_STATUS);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        PowerMockito.mockStatic(ErpRemoteService.class);
        svrResponse = getMoveSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == true;

        warehouseEntryInfo.setOrgId(null);
        s4.setLookupMeaning("Y");
        try {
            service.invokeErpMove(warehouseEntryInfo, errMsg);
        } catch (MesBusinessException e) {
            assert MessageId.GET_ERP_MOVE_QTY_ERROR.equals(e.getExMsgId());
        }

        WipOperationsDto wipOperationsDto = new WipOperationsDto();
        wipOperationsDto.setQuantityInQueue("0");
        warehouseEntryInfo.setTaskNo("taskno");
        PowerMockito.when(DatawbRemoteService.getMovableQuantity(Mockito.any())).thenReturn(wipOperationsDto);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == true;

        wipOperationsDto.setQuantityInQueue("11");
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenThrow(new Exception("call erp error"));
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        wipOperationsDto.setQuantityInQueue("1");
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;

        sysLookupTypesDTO.setLookupMeaning("10");
        result = service.invokeErpMove(warehouseEntryInfo, errMsg);
        assert result == false;
    }

    @Test
    public void invokeErpDone() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = getWarehouseEntryInfo();
        StringBuffer errMsg = new StringBuffer();
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        boolean result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        PowerMockito.when(redisLock.lock()).thenReturn(true);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO s1 = new SysLookupValuesDTO();
        s1.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_EIGHT);
        s1.setLookupMeaning("aa");
        lookupValueList.add(s1);
        SysLookupValuesDTO s2 = new SysLookupValuesDTO();
        s2.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_SEVEN);
        lookupValueList.add(s2);
        SysLookupValuesDTO s3 = new SysLookupValuesDTO();
        s3.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_TWO);
        s3.setLookupMeaning("5");
        lookupValueList.add(s3);
        SysLookupValuesDTO s4 = new SysLookupValuesDTO();
        s4.setLookupCode(MpConstant.LOOKUP_CODE_ERP_WS_NINE);
        s4.setLookupMeaning("5");
        lookupValueList.add(s4);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == true;

        s2.setLookupMeaning(MpConstant.ERP_SWITCH_ON);
        warehouseEntryInfo.setOrgId(null);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == true;

        List<WarehouseEntryInfo> moveList = new ArrayList<>();
        WarehouseEntryInfo entryInfo = new WarehouseEntryInfo();
        entryInfo.setErpMoveQty(BigDecimal.ONE);
        entryInfo.setCommitedQty(BigDecimal.ONE);
        entryInfo.setTaskNo("taskno");
        entryInfo.setWarehouseEntryId("1");
        moveList.add(entryInfo);
        PowerMockito.when(warehouseEntryInfoService.getWarehouseEntryInfoList(Mockito.anyMap())).thenReturn(moveList);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        PsTask psTask = new PsTask();
        psTask.setTaskNo("taskno");
        psTask.setItemNo("");
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(Mockito.any())).thenReturn(psTask);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        psTask.setItemNo("111");
        entryInfo.setSnList(Arrays.asList(new PsWipInfoDTO()));
        List<PDMProductMaterialResultDTO> bomRevisions = new ArrayList<>();
        PDMProductMaterialResultDTO pdmProductMaterialResultDTO = new PDMProductMaterialResultDTO();
        pdmProductMaterialResultDTO.setEntityName("123");
        PowerMockito.when(DatawbRemoteService.getBomVerByTaskNo(Mockito.any())).thenReturn(bomRevisions);

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        entryInfo.setErpDoneStatus(Constant.ERP_UNKNOWN_ERROR_STATUS);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        svrResponse.getErrorRec().getValue().getError().clear();
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;

        psTask.setItemNo("123456789012ABC");
        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == true;

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        result = service.invokeErpDone(warehouseEntryInfo, errMsg);
        assert result == false;
    }

    private OERPM12ImportOperationMoveInfoSrvResponse getMoveErrorResponse() {
        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = new OERPM12ImportOperationMoveInfoSrvResponse();
        svrResponse.setPROCESSMESSAGE(objectFactory.createOERPM12ImportOperationMoveInfoSrvResponsePROCESSMESSAGE("call error"));
        svrResponse.setPROCESSSTATUS(objectFactory.createOERPM12ImportOperationMoveInfoSrvResponsePROCESSSTATUS("E"));
        return svrResponse;
    }

    private OERPM12ImportOperationMoveInfoSrvResponse getMoveSuccessResponse() {
        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = new OERPM12ImportOperationMoveInfoSrvResponse();
        svrResponse.setPROCESSSTATUS(objectFactory.createOERPM12ImportOperationMoveInfoSrvResponsePROCESSSTATUS(Constant.ERP_WSDL_MSG_STATUS_SUCCESS));
        return svrResponse;
    }

    @Test
    public void erpWebService4move2() throws Exception {
        // erp完成接口地址
        String erpUrl = "http://test";
        // erp组织code
        String orgCode = "2";
        // erp组织ID
        String orgID = "2";
        // 子库存代码
        String subCode = "1";

        PowerMockito.mockStatic(ErpRemoteService.class);
        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = getMoveErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);

        WarehousehmEntryInfo warehousehmEntryInfo = new WarehousehmEntryInfo();
        warehousehmEntryInfo.setFactoryId(52);
        warehousehmEntryInfo.setEntityId(2);
        warehousehmEntryInfo.setCommitedQty(1);
        warehousehmEntryInfo.setWarehouseEntryId("1");
        service.erpWebService4move(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehousehmEntryInfo.getErpMoveStatus());

        svrResponse = getMoveSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);
        service.erpWebService4move(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_DONE_STATUS.equals(warehousehmEntryInfo.getErpMoveStatus());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenThrow(new Exception("call erp error"));
        service.erpWebService4move(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehousehmEntryInfo.getErpMoveStatus());
    }

    @Test
    public void erpWebService4done() throws Exception {
        // erp完成接口地址
        String erpUrl = "http://test";
        // erp组织code
        String orgCode = "2";
        // erp组织ID
        String orgID = "2";
        // 子库存代码
        String subCode = "1";

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);

        WarehousehmEntryInfo warehousehmEntryInfo = new WarehousehmEntryInfo();
        warehousehmEntryInfo.setFactoryId(52);
        warehousehmEntryInfo.setEntityId(2);
        warehousehmEntryInfo.setCommitedQty(1);
        warehousehmEntryInfo.setWarehouseEntryId("1");
        service.erpWebService4done(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehousehmEntryInfo.getErpDoneStatus());

        svrResponse.getErrorRec().getValue().getError().clear();
        service.erpWebService4done(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehousehmEntryInfo.getErpDoneStatus());

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        service.erpWebService4done(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_DONE_STATUS.equals(warehousehmEntryInfo.getErpDoneStatus());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        service.erpWebService4done(warehousehmEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehousehmEntryInfo.getErpDoneStatus());
    }

    @Test
    public void erpWebService4move() throws Exception {
        // erp完成接口地址
        String erpUrl = "http://test";
        // erp组织code
        String orgCode = "2";
        // erp组织ID
        String orgID = "2";
        // 子库存代码
        String subCode = "1";

        PowerMockito.mockStatic(ErpRemoteService.class);
        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = getMoveErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setFactoryId(BigDecimal.TEN);
        warehouseEntryInfo.setEntityId(BigDecimal.ONE);
        warehouseEntryInfo.setCommitedQty(BigDecimal.ONE);
        warehouseEntryInfo.setWarehouseEntryId("1");
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());

        svrResponse.setPROCESSMESSAGE(objectFactory.createOERPM12ImportOperationMoveInfoSrvResponsePROCESSMESSAGE("call erp error call erp " +
                "error call erp errorcall erp error call erp error call erp error call erp error call erp error call erp error  " +
                "call erp error call erp error call erp error call erp error call erp error call erp error call"));
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());

        svrResponse = getMoveSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_DONE_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any()))
                .thenThrow(new Exception("call erp error call erp error call erp errorcall erp error call erp error call erp error call erp error call erp error call erp error  call erp error call erp error call erp error call erp error call erp error call erp error call"));
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());
    }

    @Test
    public void erpWebService4move3() throws Exception {
        // erp完成接口地址
        String erpUrl = "http://test";
        // erp组织code
        String orgCode = "2";
        // erp组织ID
        String orgID = "2";
        // 子库存代码
        String subCode = "1";

        PowerMockito.mockStatic(ErpRemoteService.class);
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setFactoryId(BigDecimal.TEN);
        warehouseEntryInfo.setEntityId(BigDecimal.ONE);
        warehouseEntryInfo.setCommitedQty(BigDecimal.ONE);
        warehouseEntryInfo.setWarehouseEntryId("1");

        OERPM12ImportOperationMoveInfoSrvResponse svrResponse = getMoveSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(svrResponse);
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_DONE_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any(), Mockito.any()))
                .thenThrow(new Exception());
        service.erpWebService4move(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpMoveStatus());
    }

    @Test
    public void erpWebService4done2() throws Exception {
        // erp完成接口地址
        String erpUrl = "http://test";
        // erp组织code
        String orgCode = "2";
        // erp组织ID
        String orgID = "2";
        // 子库存代码
        String subCode = "1";

        PowerMockito.mockStatic(ErpRemoteService.class);
        ImportMaterialTrxInfoSvrResponse svrResponse = getErrorResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);

        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setFactoryId(BigDecimal.TEN);
        warehouseEntryInfo.setEntityId(BigDecimal.ONE);
        warehouseEntryInfo.setCommitedQty(BigDecimal.ONE);
        warehouseEntryInfo.setWarehouseEntryId("1");
        service.erpWebService4done(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpDoneStatus());

        svrResponse.getErrorRec().getValue().getError().clear();
        service.erpWebService4done(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpDoneStatus());

        svrResponse = getSuccessResponse();
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        service.erpWebService4done(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_DONE_STATUS.equals(warehouseEntryInfo.getErpDoneStatus());

        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        service.erpWebService4done(warehouseEntryInfo, erpUrl, orgCode, orgID, subCode);
        assert Constant.ERP_ERROR_STATUS.equals(warehouseEntryInfo.getErpDoneStatus());
    }

    @Test
    public void getInput() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setItemNo("056472000025");
        warehouseEntryInfo.setWarehouseEntryId("11");
        Object getInput = Whitebox.invokeMethod(service, "getInput", warehouseEntryInfo);
        Assert.assertEquals("3", ((Input)getInput).getTRANSACTIONMODE());

        warehouseEntryInfo.setOrgId(BigDecimal.TEN);
        getInput = Whitebox.invokeMethod(service, "getInput", warehouseEntryInfo);
        Assert.assertEquals("3", ((Input)getInput).getTRANSACTIONMODE());
    }

    @Test
    public void getSns() throws Exception {
        List<PsWipInfoDTO> snList = new ArrayList<>();
        Object getSns = Whitebox.invokeMethod(service, "getSns", snList);
        Assert.assertEquals("", ((String)getSns));

        PsWipInfoDTO psWipInfoDTO = new PsWipInfoDTO();
        psWipInfoDTO.setSn("1111");
        snList.add(psWipInfoDTO);
        getSns = Whitebox.invokeMethod(service, "getSns", snList);
        Assert.assertEquals("'1111'", ((String)getSns));
    }

    @Test
    public void getErpMoveQty() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        WarehouseEntryInfo headInfo = new WarehouseEntryInfo();
        headInfo.setCommitedQty(BigDecimal.TEN);
        Object qty = Whitebox.invokeMethod(service, "getErpMoveQty", warehouseEntryInfo, headInfo);
        Assert.assertEquals(BigDecimal.TEN, ((BigDecimal)qty));

        List<PsWipInfoDTO>  snList= new ArrayList<>();
        snList.add(new PsWipInfoDTO());
        warehouseEntryInfo.setSnList(snList);
        qty = Whitebox.invokeMethod(service, "getErpMoveQty", warehouseEntryInfo, headInfo);
        Assert.assertEquals(BigDecimal.ONE, ((BigDecimal)qty));
    }

    @Test
    public void dealErrorStr() throws Exception {
        String errorStr = "call erp error ";
        Object res = Whitebox.invokeMethod(service, "dealErrorStr", errorStr);
        Assert.assertEquals(errorStr, ((String) res));

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 20; i++) {
            sb.append(errorStr);
        }
        res = Whitebox.invokeMethod(service, "dealErrorStr", sb.toString());
        Assert.assertEquals(MpConstant.ERROR_MSG_MAX_LENGTH, ((String) res).length());
    }

    /*Started by AICoder, pid:fdfdecbf059943be9e3102d4766a16b9*/
    @Test(timeout = 8000)
    public void testBuildBoardPackageErpLog_Success() {
        WarehouseEntryInfo info = new WarehouseEntryInfo();
        info.setBillNo("testBillNo");
        info.setFactoryId(new BigDecimal(55));
        info.setEntityId(new BigDecimal(55));
        info.setErpErrorInfo("testErrorInfo");
        String erpStatus = "testStatus";
        assertNotNull(service.buildBoardPackageErpLog(info, erpStatus));
    }

    @Test(timeout = 8000)
    public void boardPackageErpWebService_Success() throws Exception {
        PowerMockito.mockStatic(ErpRemoteService.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setApplyNo("456");
        dto.setProdplanId("789");
        dto.setProdplanNo("012");
        dto.setTaskQty(new BigDecimal("3.14"));
        dto.setCommitedQty(new BigDecimal("2.71"));
        dto.setSourceOrgId(new BigDecimal("3.14"));
        dto.setSourceSubStock("3.14");
        dto.setSourceLocatorId(new BigDecimal("3.14"));
        dto.setSourceLocatorName("5433455");
        dto.setAttribute1("5433455");
        dto.setAttribute2("5433455");
        dto.setCarryAccountName("24344");
        dto.setOrgId(new BigDecimal("3.14"));
        dto.setSubStock("43545");//账号别名出库不填写
        dto.setLocatorName("4355");
        dto.setLocatorId(new BigDecimal("3.14"));
        dto.setWarehouseEntryId("324342");

        ImportMaterialTrxInfoSvrResponse request = getErrorResponse();
        assertNotNull(request);

        ObjectOfOutput output = new ObjectOfOutput();
        output.setRETURNSTATUS(impMatTrxFactory.createObjectOfOutputRETURNSTATUS("OK"));
        request.setOutput(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseOutput(output));
        ObjectOfErrorRec errorRec1 = new ObjectOfErrorRec();
        request.setErrorRec(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseErrorRec(errorRec1));
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(request);
        service.boardPackageErpWebService(dto, 44, "http://test.com", "1");
        assertNotNull(dto.getErpErrorInfo());
        ArrayOfError error1 = new ArrayOfError();
        error1.setERRORMESSAGE(impMatTrxFactory.createArrayOfErrorERRORMESSAGE("error msg"));
        errorRec1.getError().add(error1);
        request.setErrorRec(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseErrorRec(errorRec1));
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(request);
        service.boardPackageErpWebService(dto, 44, "http://test.com", "1");
        assertNotNull(dto.getErpErrorInfo());

        error1.setERRORMESSAGE(impMatTrxFactory.createArrayOfErrorERRORMESSAGE("无误温热热"));
        errorRec1.getError().add(error1);
        request.setErrorRec(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseErrorRec(errorRec1));
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(request);
        service.boardPackageErpWebService(dto, 44, "http://test.com", "1");
        assertNotNull(dto.getErpErrorInfo());

        ImportMaterialTrxInfoSvrResponse response = new ImportMaterialTrxInfoSvrResponse();
        output.setRETURNSTATUS(impMatTrxFactory.createObjectOfOutputRETURNSTATUS("S"));
        response.setOutput(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseOutput(output));
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(response);
        service.boardPackageErpWebService(dto, 44, "http://test.com", "1");
        Assert.assertTrue(true);

        service.boardPackageErpWebService(dto, 31, "http://test.com", "1");
        Assert.assertTrue(true);

        service.boardPackageErpWebService(dto, 2, "http://test.com", "1");
        Assert.assertTrue(true);

        service.boardPackageErpWebService(dto, 3, "http://test.com", "1");
        Assert.assertTrue(true);
    }

    @Test(timeout = 8000)
    public void boardPackageErpWebService_Error() throws Exception {
        PowerMockito.mockStatic(ErpRemoteService.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setApplyNo("456");
        dto.setProdplanId("789");
        dto.setProdplanNo("012");
        dto.setTaskQty(new java.math.BigDecimal("3.14"));
        dto.setCommitedQty(new java.math.BigDecimal("2.71"));

        ImportMaterialTrxInfoSvrResponse request = getErrorResponse();
        assertNotNull(request);

        ObjectOfOutput output = new ObjectOfOutput();
        ImportMaterialTrxInfoSvrResponse svrResponse = new ImportMaterialTrxInfoSvrResponse();
        svrResponse.setOutput(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseOutput(output));
        ObjectOfErrorRec errorRec = new ObjectOfErrorRec();
        ArrayOfError error = new ArrayOfError();
        error.setERRORMESSAGE(impMatTrxFactory.createArrayOfErrorERRORMESSAGE("error msg"));
        errorRec.getError().add(error);
        request.setErrorRec(impMatTrxFactory.createImportMaterialTrxInfoSvrResponseErrorRec(errorRec));
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenReturn(svrResponse);
        service.boardPackageErpWebService(dto, 1, "http://test.com", "1");
        assertNotNull(dto.getErpErrorInfo());
    }

    @Test(timeout = 8000)
    public void boardPackageErpWebService_Exception() throws Exception {
        PowerMockito.mockStatic(ErpRemoteService.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setApplyNo("456");
        dto.setProdplanId("789");
        dto.setProdplanNo("012");
        dto.setTaskQty(new java.math.BigDecimal("3.14"));
        dto.setCommitedQty(new java.math.BigDecimal("2.71"));

        ImportMaterialTrxInfoSvrResponse request = getErrorResponse();
        assertNotNull(request);
        PowerMockito.when(ErpRemoteService.invokeErp(Mockito.anyString(), Mockito.any())).thenThrow(new Exception("call erp error"));
        service.boardPackageErpWebService(dto, 1, "http://test.com", "1");
        assertNotNull(dto.getErpErrorInfo());
    }

    @Test
    public void getImportMaterialTrxInfoSvrBoardPackageRequest() {
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillNo("123");
        dto.setApplyNo("456");
        dto.setProdplanId("789");
        dto.setProdplanNo("012");
        dto.setTaskQty(new BigDecimal("3.14"));
        dto.setCommitedQty(new BigDecimal("2.71"));
        dto.setSourceOrgId(new BigDecimal("3.14"));
        dto.setSourceSubStock("3.14");
        dto.setSourceLocatorId(new BigDecimal("3.14"));
        dto.setSourceLocatorName("5433455");
        dto.setAttribute1("5433455");
        dto.setAttribute2("5433455");
        dto.setCarryAccountName("24344");
        dto.setOrgId(new BigDecimal("3.14"));
        dto.setSubStock("43545");//账号别名出库不填写
        dto.setLocatorName("4355");
        dto.setLocatorId(new BigDecimal("3.14"));
        dto.setWarehouseEntryId("324342");
        service.getImportMaterialTrxInfoSvrBoardPackageRequest(dto,44);
        Assert.assertNotNull(dto);

        service.getImportMaterialTrxInfoSvrBoardPackageRequest(dto,31);
        Assert.assertNotNull(dto);

        service.getImportMaterialTrxInfoSvrBoardPackageRequest(dto,2);
        Assert.assertNotNull(dto);

        service.getImportMaterialTrxInfoSvrBoardPackageRequest(dto,3);
        Assert.assertNotNull(dto);
    }
    /*Ended by AICoder, pid:fdfdecbf059943be9e3102d4766a16b9*/

    /*Started by AICoder, pid:i855dtcba9sdd0014d7b0ad0d00c3d82f4c04e9c*/
    @Test
    public void testGetLocatorInfoWithEmptyUrl() throws Exception {
        LocatorInfoDTO dto = new LocatorInfoDTO();
        dto.setInventoryLocationCode("locationCode");
        dto.setOrganizationId(new BigDecimal(1));
        List<LocatorInfoDTO> locatorInfo1 = service.getLocatorInfo(dto, "");
        Assert.assertTrue(CollectionUtils.isEmpty(locatorInfo1));
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.any(),Mockito.any(),Mockito.anyString())).thenReturn("12312");
        List<LocatorInfoDTO> list = new ArrayList<>();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{\"current\":1,\"total\":1,\"totalPage\":1,\"pageSize\":10}");
        List<LocatorInfoDTO> locatorInfo = service.getLocatorInfo(dto, "");
        Assert.assertTrue(CollectionUtils.isEmpty(locatorInfo));
        LocatorInfoDTO dto1 = new LocatorInfoDTO();
        list.add(dto1);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{\"current\":1,\"total\":1,\"rows\":[{\"inventoryLocationId\":186096,\"inventoryLocationCode\":\"30_CP.CLS\",\"lastUpdateDate\":\"2019-08-31 11:43:40\",\"lastUpdatedBy\":\"65882\",\"creationDate\":\"2019-08-31 11:43:40\",\"createdBy\":\"65882\",\"organizationId\":635,\"subinventoryCode\":\"30_CP\",\"enabledFlag\":\"Y\",\"endDateActive\":null,\"description\":null}],\"totalPage\":1,\"pageSize\":10}");
        List<LocatorInfoDTO> locatorInfo2 = service.getLocatorInfo(dto, "");
        Assert.assertTrue(CollectionUtils.isNotEmpty(locatorInfo2));
    }
    /*Ended by AICoder, pid:i855dtcba9sdd0014d7b0ad0d00c3d82f4c04e9c*/

    /*Started by AICoder, pid:39dd6ze0b0712c71452508e34102002add815464*/
    @Test
    public void testErrorAlarm_NotEqual() {
        PowerMockito.mockStatic(CommonUtils.class);
        WarehouseEntryInfo dto = new WarehouseEntryInfo();
        dto.setBillType("123");
        dto.setErpErrorInfo("Error");
        service.errorAlarm(dto, "Node1");
        dto.setBillType("5");
        dto.setErpErrorInfo("");
        service.errorAlarm(dto, "Node1");
        dto.setBillType("5");
        dto.setErpErrorInfo("Error");
        service.errorAlarm(dto, "Node2");
        service.errorAlarm(dto, "1");
        Assert.assertNotNull(dto.getErpErrorInfo());
    }
    /*Ended by AICoder, pid:39dd6ze0b0712c71452508e34102002add815464*/

    @Test
    public void getOnhandQty () {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        try {
            service.getOnhandQty(new GetOnhandQtyDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.any())).thenReturn(new SysLookupValues());
        try {
            service.getOnhandQty(new GetOnhandQtyDTO());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
}
/*Ended by AICoder, pid:fa17b8c340d54cdf841e7785b8c51837*/
