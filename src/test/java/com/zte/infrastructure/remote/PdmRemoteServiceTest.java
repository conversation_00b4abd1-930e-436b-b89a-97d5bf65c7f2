package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

/**
 * <AUTHOR>
 * @date 2021/9/14
 */
@PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class,HttpRemoteUtil.class})
public class PdmRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private PdmRemoteService service;

    @Before
    public void init() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,MESHttpHelper.class,HttpRemoteUtil.class);
    }

    @Test
    public void bomItemQuery() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOS=new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678002));
        sysLookupTypesDTO1.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO1.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678003));
        sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO2.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO2);
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
        sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO3);
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
        sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO4);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PdmBomItemQueryDTO dto = new PdmBomItemQueryDTO();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(dto);
                }})
        );
        PdmBomItemQueryDTO pdmBomItemResultDTO=new PdmBomItemQueryDTO();
        Assert.assertNotNull(service.bomItemQuery(pdmBomItemResultDTO));
    }

    /* Started by AICoder, pid:k12532dc1clc7b51437f0a9c10d86f8de68378eb */
    @Test
    public void TestgetPdmTemplateInfoList() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678002));
        sysLookupTypesDTO1.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO1.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678003));
        sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO2.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO2);
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
        sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO3);
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
        sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO4);

        SysLookupTypesDTO sysLookupTypesDTO7 = new SysLookupTypesDTO();
        sysLookupTypesDTO7.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO7.setLookupCode(new BigDecimal(6678007));
        sysLookupTypesDTO7.setLookupMeaning("http://test.pdmweb.zte.com.cn/zte-plm-pdm-nameplate/nameplate/mes/v1/MES/Nameplate");
        sysLookupTypesDTO7.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO7);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PdmTemplateInfoQueryDTO pdmTemplateInfoQueryDTO = new PdmTemplateInfoQueryDTO();
        List<String> partNoList = new ArrayList<>();
        partNoList.add("130000164282");
        partNoList.add("180000395543");
        pdmTemplateInfoQueryDTO.setPartNoList(partNoList);
        Assert.assertNotNull(service.getPdmTemplateInfoList(pdmTemplateInfoQueryDTO));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(null);
        try {
            service.getPdmTemplateInfoList(pdmTemplateInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTOS.remove(sysLookupTypesDTO7);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
        try {
            service.getPdmTemplateInfoList(pdmTemplateInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO7.setLookupMeaning("");
        sysLookupTypesDTOS.add(sysLookupTypesDTO7);
        try {
            service.getPdmTemplateInfoList(pdmTemplateInfoQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:k12532dc1clc7b51437f0a9c10d86f8de68378eb */

    @Test
    public void query() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesDTOS=new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678002));
        sysLookupTypesDTO1.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO1.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678003));
        sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO2.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO2);
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
        sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO3);
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
        sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOS.add(sysLookupTypesDTO4);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOS);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PdmBomItemResultDTO pdmBomItemResultDTO=new PdmBomItemResultDTO();
        pdmBomItemResultDTO.setNo("123456789012ABB");
        Assert.assertNotNull(service.query(pdmBomItemResultDTO));
    }

    @Test
    public void sendToPdm() throws Exception {
        List<PDMSendInfoDTO> pdmSendInfoDTOList = new ArrayList<>();
        String url = "http://";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(null);
        List<PDMSendInfoDTO> result = service.sendToPdm(pdmSendInfoDTOList, url);
        assert result.isEmpty();

        ServiceData<Object> serviceData = new ServiceData<>();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        result = service.sendToPdm(pdmSendInfoDTOList, url);
        assert result.isEmpty();

        serviceData.setBo("");
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        result = service.sendToPdm(pdmSendInfoDTOList, url);
        assert result.isEmpty();

        retCode.setCode(RetCode.SUCCESS_CODE);
        retCode.setMsgId(RetCode.SUCCESS_MSGID);
        PdmReceiveResultDTO pdmReceiveResultDTO = new PdmReceiveResultDTO();
        List<PDMSendInfoDTO> list = new ArrayList<>();
        list.add(new PDMSendInfoDTO());
        pdmReceiveResultDTO.setReceiveRespInfoList(list);
        serviceData.setBo(pdmReceiveResultDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        result = service.sendToPdm(pdmSendInfoDTOList, url);
        assert !result.isEmpty();
    }
    @Test
    public void queryEnCode() throws Exception {
        PdmBomItemResultDTO dto = new PdmBomItemResultDTO();
        try{
            service.queryEnCode(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARMS_ERR, e.getMessage());
        }
        dto.setNo("test");
        try{
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(new ArrayList<>());
            service.queryEnCode(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO.setLookupCode(new BigDecimal(6678001));
        sysLookupTypesDTO.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        try{
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
            service.queryEnCode(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO1.setLookupCode(new BigDecimal(6678006));
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        try{
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
            service.queryEnCode(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO2.setLookupCode(new BigDecimal(6678006));
        sysLookupTypesDTO2.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        List<SysLookupTypesDTO> sysLookupTypesDTOList1 = new ArrayList<>();
        sysLookupTypesDTOList1.add(sysLookupTypesDTO2);
        sysLookupTypesDTOList1.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO3.setLookupCode(new BigDecimal(6678004));
        sysLookupTypesDTO3.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO3.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOList1.add(sysLookupTypesDTO3);
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupType(new BigDecimal(6678));
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(6678005));
        sysLookupTypesDTO4.setLookupMeaning("http://srmapp.zte.com.cn/zte-scm-supplier-supinfoservice");
        sysLookupTypesDTO4.setDescriptionChinV("供应商接口服务");
        sysLookupTypesDTOList1.add(sysLookupTypesDTO4);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList1);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        Assert.assertNotNull(service.queryEnCode(dto));
    }

    @Test
    public void getLastPdmEnCode() throws Exception {
        PdmEnCodeDTO dto = new PdmEnCodeDTO();
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.ITEM_NO_NULL.equals(e.getExMsgId());
        }

        dto.setPartNo("129749931011");
        dto.setPageNo(1L);
        dto.setPageSize(10L);
        ServiceData<Object> serviceData1 = null;
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.anyString())).thenReturn("http://test");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(String.valueOf(serviceData1));
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE.equals(e.getExMsgId());
        }

        ServiceData<Object> serviceData = new ServiceData<>();
        serviceData.setCode(null);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE.equals(e.getExMsgId());
        }
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE.equals(e.getExMsgId());
        }
        PdmPageDTO<Object> pdmPageDTO = new PdmPageDTO<>();
        serviceData.setBo(pdmPageDTO);
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE.equals(e.getExMsgId());
        }

        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        try {
            service.getLastPdmEnCode(dto);
        } catch (MesBusinessException e) {
            assert MessageId.FAILED_TO_OBTAIN_PDM_ENGLISH_CODE.equals(e.getExMsgId());
        }
        List<Object> pdmEnCodeDTOList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("EN_CODE", "test");
        jsonObject.put("LAST_UPDATE_DATE", "2023-01-01 00:00:00");
        pdmEnCodeDTOList.add(jsonObject);
        pdmPageDTO.setData(pdmEnCodeDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSONObject.toJSONString(serviceData));
        PdmEnCodeDTO result = service.getLastPdmEnCode(dto);
        assert "test".equals(result.getEnCode());
    }
}
