/*Started by AICoder, pid:xd559q858arb476146da0b20e0691e67ee183b1f*/
package com.zte.infrastructure.remote;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class,BasicsettingRemoteService.class})
public class BarcodeCenterRemoteService_serverTemplatePrint_12_Test {
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void setup() {
        barcodeCenterRemoteService = new BarcodeCenterRemoteService();
    }

    @Test(timeout = 8000, expected = Exception.class)
    public void testServerTemplatePrint_Exception() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class,BasicsettingRemoteService.class);
        BarcodeCenterTemplatePrintDTO dto = new BarcodeCenterTemplatePrintDTO();
        barcodeCenterRemoteService.serverTemplatePrint(dto);
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetHeaderForBarcodeCenter() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class );
        Map<String, String> result = barcodeCenterRemoteService.getHeaderForBarcodeCenter();
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test(timeout = 8000, expected = Exception.class)
    public void testServerTemplatePrint_NotException() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class );
        PowerMockito.mockStatic(BasicsettingRemoteService.class );
        BarcodeCenterTemplatePrintDTO dto = new BarcodeCenterTemplatePrintDTO();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(BigDecimal.ONE);
        sysLookupTypesDTO.setAttribute1("1");
        sysLookupTypesDTO.setLookupMeaning("1");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(Lists.list(sysLookupTypesDTO));
        barcodeCenterRemoteService.serverTemplatePrint(dto);
    }
}
/*Ended by AICoder, pid:xd559q858arb476146da0b20e0691e67ee183b1f*/