package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.MpConstant.DEFAULT_EXPORT_CONTROL_METHOD;

@PrepareForTest({MESHttpHelper.class, HttpRemoteUtil.class, BasicsettingRemoteService.class})
public class IscpRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private IscpRemoteService iscpRemoteService;

    @Before
    public void init(){
        PowerMockito.mockStatic(MESHttpHelper.class, HttpRemoteUtil.class, BasicsettingRemoteService.class);
    }

    @Test
    public void getCivControlInfo() throws Exception {
        List<String> itemNos = new ArrayList<>();
        List<CivControlInfoDTO> result = iscpRemoteService.getCivControlInfo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        itemNos.add("046050500166");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://test.iscp.zte.com.cn/zte-scm-iscp-bff-service");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.any()))
                .thenReturn(sysLookupValues);

        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSONObject.toJSONString(serviceData));

        result = iscpRemoteService.getCivControlInfo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        CivControlInfoDTO dto = new CivControlInfoDTO();
        dto.setExportcontrolmethodName(DEFAULT_EXPORT_CONTROL_METHOD);
        infoDTOList.add(dto);
        sysLookupValues.setAttribute1("/external/item/ItemSplitInfo");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = iscpRemoteService.getCivControlInfo(itemNos);
        Assert.assertEquals(false, result.isEmpty());
    }

    @Test
    public void getCivControlInfoTwo() throws Exception {
        List<String> itemNos = new ArrayList<>();
        itemNos.add("046050500166");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.any()))
                .thenReturn(null);

        List<CivControlInfoDTO> result = iscpRemoteService.getCivControlInfo(itemNos);
        Assert.assertEquals(true, result.isEmpty());
    }


    @Test
    public void getItemSplitInfo() throws Exception {
        Assert.assertTrue(iscpRemoteService.getItemSplitInfo("","","").size()>=0);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.any()))
                .thenReturn(null);
        Assert.assertTrue(iscpRemoteService.getItemSplitInfo("test","10","1010").size()>=0);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.any()))
                .thenReturn(sysLookupValues);
        ServiceData serviceData = new ServiceData();
        List<ItemSplitInfoDTO> infoDTOList = new ArrayList<>();
        ItemSplitInfoDTO itemSplitInfoDTO = new ItemSplitInfoDTO();
        infoDTOList.add(itemSplitInfoDTO);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, new ArrayList<>());
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        Assert.assertTrue(iscpRemoteService.getItemSplitInfo("test","10","1010").size()>=0);

        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        Assert.assertTrue(iscpRemoteService.getItemSplitInfo("test","10","1010").size()>=0);
    }
}
