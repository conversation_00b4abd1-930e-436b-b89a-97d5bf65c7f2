package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.IMESLogService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.mds.MdsAccessTokenDTO;
import com.zte.interfaces.dto.mds.MdsGetTestStationLogDTO;
import com.zte.interfaces.dto.mds.MdsRepairInfoResponseDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.interfaces.dto.mds.MdsRepairRecordDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.compress.utils.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class,RedisTemplate.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class,HttpHeaderUtil.class})
public class MdsRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private MdsRemoteService service;

    @Mock
    private RedisTemplate<String,Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;
    @Spy
    private MdsRemoteService serviceSpy = new MdsRemoteService();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class);
        // 模拟 getAccessToken() 返回 "token"
        PowerMockito.doReturn("token").when(serviceSpy).getAccessToken();
    }
    @Mock
    private IMESLogService imesLogService;

    @Test
    public void test_getRepairCountFromMDS() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,RedisTemplate.class,HttpRemoteUtil.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class);
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        dto1.setLookupCode(new BigDecimal("6732003"));
        dto1.setLookupMeaning("10351947");
        SysLookupTypesDTO dto2 = new SysLookupTypesDTO();
        dto2.setLookupCode(new BigDecimal("6732004"));
        dto2.setLookupMeaning("1111");
        SysLookupTypesDTO dto3 = new SysLookupTypesDTO();
        dto3.setLookupCode(new BigDecimal("6732002"));
        dto3.setLookupMeaning("http://asdad");
        sysLookupTypesDTOList.add(dto1);
        sysLookupTypesDTOList.add(dto2);
        sysLookupTypesDTOList.add(dto3);
        when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        MdsAccessTokenDTO mdsAccessTokenDTO = new MdsAccessTokenDTO();
        mdsAccessTokenDTO.setAssessToken("dsdsafdseadfaefwe");
        String bo = JSON.toJSONString(new ServiceData<MdsAccessTokenDTO>() {{
            setBo(mdsAccessTokenDTO);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(),anyMap(),anyString(),anyString())).thenReturn(bo);
        when(JacksonJsonConverUtil.jsonToBean(anyString(), (Class<Object>) any())).thenReturn(mdsAccessTokenDTO);
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(null);
        try {
            service.getRepairCountFromMDS("1234");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("");
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        try {
            service.getRepairCountFromMDS("1234");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("url");
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        List<MdsRepairRecordDTO> list = new ArrayList<>();
        MdsRepairRecordDTO mdsRepairRecordDTO = new MdsRepairRecordDTO();
        list.add(mdsRepairRecordDTO);
        String msg = JSON.toJSONString(new ServiceData<List<MdsRepairRecordDTO>>() {{
            setBo(list);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(msg);
        service.getRepairCountFromMDS("1234");
        Assert.assertTrue(true);
    }

    @Test
    public void testGetStationLogFromMDS() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class);
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(null);
        try {
            service.getStationLogFromMDS("1234", "FT");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("url");
        when(BasicsettingRemoteService.getSysLookUpValue(any(), any())).thenReturn(sysLookupTypesDTO);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10337580");
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        when(valueOps.get(Mockito.anyString())).thenReturn("123");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString("msg"));
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn("bo");
        when(JacksonJsonConverUtil.jsonToListBean("bo", new TypeReference<ArrayList<MdsGetTestStationLogDTO>>(){})).thenReturn(new ArrayList<>());
        service.getStationLogFromMDS("1234", "FT");
        ArrayList<MdsGetTestStationLogDTO> tempList = new ArrayList<>();
        MdsGetTestStationLogDTO mdsGetTestStationLogDTO = new MdsGetTestStationLogDTO();
        mdsGetTestStationLogDTO.setStationId("FT");
        mdsGetTestStationLogDTO.setStationLog("1-2-3-4");
        MdsGetTestStationLogDTO mdsGetTestStationLogDTO1 = new MdsGetTestStationLogDTO();
        mdsGetTestStationLogDTO1.setStationId("FT测试工序");
        mdsGetTestStationLogDTO1.setStationLog("1-2-3-4");
        tempList.add(mdsGetTestStationLogDTO);
        tempList.add(mdsGetTestStationLogDTO1);

        when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(tempList);
        service.getStationLogFromMDS("1234", "FT");

        service.getStationLogFromMDS("1234", "FT");
    }

    @Test
    public void getMdsErrorMsg() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class, ServiceDataBuilderUtil.class);
        try {
            service.getMdsErrorMsg("","131546");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_TYPE_CAN_NOT_BE_NULL, e.getMessage());
        }
        List<SysLookupValuesDTO> batchSysValueByCode = new ArrayList<>();
        SysLookupValuesDTO c5 = new SysLookupValuesDTO();
        c5.setLookupMeaning("4564564");
        c5.setAttribute1("52");
        c5.setAttribute2("1345");
        batchSysValueByCode.add(c5);

        try {
            when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                    .thenReturn(new ArrayList<>());
            service.getMdsErrorMsg("1345","131546");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                    .thenReturn(batchSysValueByCode);
            when(HttpHeaderUtil.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("1");
            service.getMdsErrorMsg("1345","131546");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(batchSysValueByCode);
        when(HttpHeaderUtil.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("52");
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        try {
            service.getMdsErrorMsg("1345","131546");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void getAccessTokenForRepair() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class, ServiceDataBuilderUtil.class);
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        when(valueOps.get(Mockito.anyString())).thenReturn("123");
        String accessToken = service.getAccessTokenForRepair();
        Assert.assertEquals("123", accessToken);

        when(valueOps.get(Mockito.anyString())).thenReturn("");
        List<SysLookupTypesDTO> batchSysValueByCode = new ArrayList<>();
        when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(batchSysValueByCode);
        try {
            service.getAccessTokenForRepair();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupTypesDTO c1 = new SysLookupTypesDTO();
        c1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6917002));
        batchSysValueByCode.add(c1);
        try {
            service.getAccessTokenForRepair();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        c1.setLookupMeaning("4564564");
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupTypesDTO c2 = new SysLookupTypesDTO();
        c2.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6917003));
        batchSysValueByCode.add(c2);
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        c2.setLookupMeaning("4564564");
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupTypesDTO c3 = new SysLookupTypesDTO();
        c3.setLookupCode(new BigDecimal("6917001"));
        batchSysValueByCode.add(c3);
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        c3.setLookupMeaning("4564564");
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
        MdsAccessTokenDTO mds = new MdsAccessTokenDTO();
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSON.toJSONString(mds));
        try {
            service.getAccessTokenForRepair();
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    /* Started by AICoder, pid:q082c993b4rdc671446e0a2910ced084e5d8d946 */
    @Test
    public void testGetRepairInfoByPartCode_Success() throws Exception {
        String sn = "testSn";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://example.com/api");

        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020012))
                .thenReturn(sysLookupTypesDTO);

        String responseJson = "[{\"inspectId\": 1, \"smallBoardPartcode\": \"testSn\"}]";
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn(responseJson);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseJson)).thenReturn(responseJson);
        List<MdsRepairInfoResponseDTO> list = Lists.newArrayList();
        MdsRepairInfoResponseDTO mdsRepairInfoResponseDTO = new MdsRepairInfoResponseDTO();
        mdsRepairInfoResponseDTO.setBoardSn("testSn");
        list.add(mdsRepairInfoResponseDTO);
        when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(TypeReference.class))).thenReturn(list);

        List<MdsRepairInfoResponseDTO> result = serviceSpy.getRepairInfoByPartCode(sn);

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testGetRepairInfoByPartCode_NoLookupType() {
        String sn = "testSn";
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020012))
                .thenReturn(null);

        MesBusinessException exception = Assert.assertThrows(MesBusinessException.class, () -> {
            serviceSpy.getRepairInfoByPartCode(sn);
        });

        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testGetRepairInfoByPartCode_EmptyLookupMeaning() {
        String sn = "testSn";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("");

        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020012))
                .thenReturn(sysLookupTypesDTO);

        MesBusinessException exception = Assert.assertThrows(MesBusinessException.class, () -> {
            serviceSpy.getRepairInfoByPartCode(sn);
        });

        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testGetRepairInfoByPartCode_EmptyHttpResponse() {
        String sn = "testSn";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://example.com/api");

        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020012))
                .thenReturn(sysLookupTypesDTO);

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn("");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo("")).thenReturn("");

        MesBusinessException exception = Assert.assertThrows(MesBusinessException.class, () -> {
            serviceSpy.getRepairInfoByPartCode(sn);
        });

        Assert.assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testGetRepairInfoByPartCode_EmptyResultList() throws Exception {
        String sn = "testSn";
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("http://example.com/api");

        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020012))
                .thenReturn(sysLookupTypesDTO);

        String responseJson = "[]";
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn(responseJson);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseJson)).thenReturn(responseJson);
        when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(TypeReference.class))).thenReturn(new ArrayList<>());

        List<MdsRepairInfoResponseDTO> result = serviceSpy.getRepairInfoByPartCode(sn);

        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:q082c993b4rdc671446e0a2910ced084e5d8d946 */
}
