package com.zte.infrastructure.remote;

import com.zte.common.model.MessageId;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

/**
 * <AUTHOR>
 * @date 2023/9/7
 */
@PrepareForTest({HttpRemoteUtil.class})
public class FilePreViewRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private FilePreViewRemoteService service;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;

    @Test
    public void queryCrossInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(),Mockito.any(), Mockito.anyString(), Mockito.any()))
                .thenReturn("");
        try {
            service.getPreViewUrl("", "", "", "");
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GENERATE_PREVIEW_HEADER.equals(e.getExMsgId()));
        }
    }
}
