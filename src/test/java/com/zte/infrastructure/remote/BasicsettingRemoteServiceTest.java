package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, JacksonJsonConverUtil.class, MESHttpHelper.class,
        MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, InterfaceEnum.class, HttpRemoteService.class, CommonUtils.class})
public class BasicsettingRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private BasicsettingRemoteService service;
    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
    }

    @Test
    public void getLine() {
        CFLine line = BasicsettingRemoteService.getLine("");
        Assert.assertTrue(Objects.isNull(line));

        BasicsettingRemoteService.getLine("ff");

        List<CFLine> cfList = new LinkedList<>();
        CFLine a1 = new CFLine();
        cfList.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(cfList);
        BasicsettingRemoteService.getLine("ff");
    }

    @Test
    public void getSysLookUpValue() {
        Map<String, Object> map = new HashMap<>();
        List<SysLookupTypesDTO> sysLookUpValue = BasicsettingRemoteService.getSysLookUpValue(map);
        Assert.assertTrue(CollectionUtils.isEmpty(sysLookUpValue));
    }

    @Test
    public void getBManufactureCapacityInfoByPost() {
        List<String> paramList = new ArrayList<>();
        paramList.add("test");
        List<BManufactureCapacityDTO> list = service.getBManufactureCapacityInfoByPost(paramList, paramList, paramList, "");
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void getVerNoInfoByItemNos() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        Set<String> itemNos = new HashSet<>();
        itemNos.add("test");
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("test", "test");
        Assert.assertEquals(new HashMap<>(), BasicsettingRemoteService.getVerNoInfoByItemNos(itemNos));
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo(resultMap);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }}));
        Assert.assertNotNull(BasicsettingRemoteService.getVerNoInfoByItemNos(itemNos));
    }

    @Test
    public void getFactoryByFactoryCode() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        List<CFFactory> list = new ArrayList<>();
        CFFactory cfFactory = new CFFactory();
        cfFactory.setFactoryId(52);
        list.add(cfFactory);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<CFFactory>>() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        Assert.assertEquals(new ArrayList<>(), service.getFactoryByFactoryCode("52"));
    }

    @Test
    public void getSysLookupValues() {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(new LinkedList<>());
        Assert.assertNotNull(BasicsettingRemoteService.getSysLookupValues("123"));
    }

    @Test
    public void getProcessPositionsCheckIn() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData()));
        Assert.assertNotNull(BasicsettingRemoteService.getProcessPositionsCheckIn("123"));
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(),
                Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo(Lists.newArrayList(new ProcessPositionsCheckInDTO() {{
                setProcessPosition("1");
                setEmpNo("1");
            }}));
        }}));
        Assert.assertNotNull(BasicsettingRemoteService.getProcessPositionsCheckIn("123"));
    }

    @Test
    public void getSmtLocationByLineCodeAndLocationType() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        service.getSmtLocationByLineCodeAndLocationType("", "");
        List<SmtLocationInfoDTO> smtLocationInfoDTOS = new ArrayList<>();
        smtLocationInfoDTOS.add(new SmtLocationInfoDTO());
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo(smtLocationInfoDTOS);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        Assert.assertEquals(null, service.getSmtLocationByLineCodeAndLocationType("lineCode", ""));
    }

    @Test
    public void getLookupMeaningByCode() {
        String lookupMeaning = BasicsettingRemoteService.getLookupMeaningByCode(null);
        assert StringUtils.isEmpty(lookupMeaning);

        PowerMockito.mockStatic(HttpRemoteService.class);
        ServiceData<SysLookupValues> serviceData = new ServiceData<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("test");
        serviceData.setBo(sysLookupValues);
        PowerMockito.when(HttpRemoteService.pointToPointCall(anyString(), any(), anyString(), any(),
                Mockito.any(TypeReference.class))).thenReturn(serviceData);
        lookupMeaning = BasicsettingRemoteService.getLookupMeaningByCode(MpConstant.LOOKUP_6678001);
        assert "test".equals(lookupMeaning);
        Assert.assertNotNull(BasicsettingRemoteService.getLookupMeaningByCode(MpConstant.LOOKUP_6678001));
    }

    @Test
    public void querySemiItemNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(null);
        List<String> itemCodeList = new ArrayList<>();
        Assert.assertNotNull(BasicsettingRemoteService.querySemiItemNo(itemCodeList));
    }

    @Test
    public void querySemiItemNo1() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        List<String> itemCodeList = new ArrayList<>();
        String bo = JSON.toJSONString(new ServiceData<List<String>>() {{
            setBo(itemCodeList);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(new LinkedList<>());
        Assert.assertNotNull(BasicsettingRemoteService.querySemiItemNo(itemCodeList));
    }


    @Test
    public void getLinesByCodeList() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<CFLine>>() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        List<String> lineCodeList = new ArrayList<>();
        lineCodeList.add("SMT-TZ002");
        try {
            Assert.assertEquals(0, service.getLinesByCodeList(lineCodeList).size());
        } catch (Exception e) {
        }

        List<CFLine> list = new ArrayList<>();
        CFLine line = new CFLine();
        line.setLineCode("SMT-TZ002");
        list.add(line);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<CFLine>>() {{
                    setBo(list);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        try {
            Assert.assertEquals(1, service.getLinesByCodeList(lineCodeList).size());
        } catch (Exception e) {
        }
    }

    @Test
    public void testQueryLineInWarehouseCode() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        List<String> itemCodeList = new ArrayList<>();
        String bo = JSON.toJSONString(new ServiceData<List<String>>() {{
            setBo(itemCodeList);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(bo);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(new LinkedList<>());
        Assert.assertNotNull(BasicsettingRemoteService.queryLineInWarehouseCode(itemCodeList));
    }

    @Test
    public void getItemInfoByItemNoList() {
        List<String> itemNoList = new ArrayList<>();
        List<BsItemInfo> itemInfoList = BasicsettingRemoteService.getItemInfoByItemNoList(itemNoList);
        Assert.assertTrue(itemInfoList.isEmpty());
        List<BsItemInfo> list = new ArrayList<>();

        itemNoList.add("test123");
        ServiceData<List<BsItemInfo>> serviceData = new ServiceData<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        itemInfoList = BasicsettingRemoteService.getItemInfoByItemNoList(itemNoList);
        Assert.assertTrue(itemInfoList.isEmpty());


        serviceData.setBo(list);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        itemInfoList = BasicsettingRemoteService.getItemInfoByItemNoList(itemNoList);
        Assert.assertTrue(itemInfoList.isEmpty());

        BsItemInfo itemInfo = new BsItemInfo();
        itemInfo.setItemNo("test123");
        list.add(itemInfo);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);
        itemInfoList = BasicsettingRemoteService.getItemInfoByItemNoList(itemNoList);
        Assert.assertTrue(!itemInfoList.isEmpty());

    }

    @Test
    public void getItemByAmbiguityNos() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<CFLine>>() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);
        List<String> itemList = new ArrayList<>();
        itemList.add("test123");
        try {
            BasicsettingRemoteService.getItemByAmbiguityNos(itemList);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

        List<BsItemInfo> list = new ArrayList<>();
        BsItemInfo line = new BsItemInfo();
        line.setItemNo("SMT-TZ002");
        list.add(line);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<BsItemInfo>>() {{
                    setBo(list);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(list);

        try {
            BasicsettingRemoteService.getItemByAmbiguityNos(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void getListSmtLocation() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<CFLine>>() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(null);

        String lineCode = "";
        String moduleNo = "";
        String locationSn = "";
        List<SmtLocationInfoDTO> dtoLinkedList = BasicsettingRemoteService.getListSmtLocation(lineCode, moduleNo, locationSn);
        Assert.assertEquals(dtoLinkedList.size(), 0);

        LinkedList<SmtLocationInfoDTO> dtoLinkedList2 = new LinkedList<>();
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLocationSn("locationSn");
        dtoLinkedList2.add(smtLocationInfoDTO);
        lineCode = "SMT-ZC01";

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData<List<SmtLocationInfoDTO>>() {{
                    setBo(dtoLinkedList2);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(dtoLinkedList2));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any())).thenReturn(dtoLinkedList2);
        Assert.assertNotNull(BasicsettingRemoteService.getListSmtLocation(lineCode, moduleNo, locationSn));
        Assert.assertEquals(dtoLinkedList2.size(), 1);
    }


    @Test
    public void getStyleInfoPost() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        Assert.assertNull(BasicsettingRemoteService.getStyleInfoPost("test"));
    }

    @Test
    public void getLocationInfoByLineCode() throws Exception {
        mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        Assert.assertNotNull(BasicsettingRemoteService.getLocationInfoByLineCode(smtLocationInfoDTO));
    }

    @Test
    public void getLocationInfoByLineCodeTwo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        Assert.assertNull(service.getLocationInfoByLineCode(smtLocationInfoDTO));
    }

    @Test
    public void getSmtLocationInfoByLocationSn() throws Exception {
        String locationSn = "";
        Assert.assertNull(BasicsettingRemoteService.getSmtLocationInfoByLocationSn(locationSn));
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setLocationSn("202406170001");
        smtLocationInfoDTO.setMachineNo("X4S");
        smtLocationInfoDTO.setModuleNo("X4S-1");
        ServiceData<SmtLocationInfoDTO> serviceData = new ServiceData<>();
        serviceData.setBo(null);
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        String jsonStr = JSON.toJSONString(serviceData);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(jsonStr);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(json);
        BasicsettingRemoteService.getSmtLocationInfoByLocationSn(locationSn);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(null);
        BasicsettingRemoteService.getSmtLocationInfoByLocationSn(locationSn);
        Assert.assertEquals(smtLocationInfoDTO.getLocationSn(), "202406170001");
    }


    @Test
    public void updateOrInsertItemAngle() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        ServiceData serviceData = new ServiceData<>();
        serviceData.setBo(null);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        PolarItemInfoDTO polarItemInfoDTO = new PolarItemInfoDTO();
        BasicsettingRemoteService.updateOrInsertItemAngle(polarItemInfoDTO);
        Assert.assertNull(polarItemInfoDTO.getBrand());
    }

    /* Started by AICoder, pid:32bc2g63cf8f3aa140860a65c021711b35d520a9 */
    @Test
    public void TestqueryMultiBrandByItemNo() {
        String itemNo = "456789";
        ServiceData serviceData = new ServiceData();
        List<PolarItemInfo> list = new ArrayList<>();
        PolarItemInfo polarItemInfo = new PolarItemInfo();
        polarItemInfo.setBrand("123");
        list.add(polarItemInfo);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(JSON.toJSONString(serviceData));
        Assert.assertNotNull(BasicsettingRemoteService.queryMultiBrandByItemNo(Collections.singletonList(itemNo)));
        serviceData.setBo(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        Assert.assertNull(BasicsettingRemoteService.queryMultiBrandByItemNo(Collections.singletonList(itemNo)));
    }
    /* Ended by AICoder, pid:32bc2g63cf8f3aa140860a65c021711b35d520a9 */

    @Test
    public void getPolarItemInfoList() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        ServiceData serviceData = new ServiceData<>();
        List<PolarItemInfoDTO> list = new ArrayList<>();
        PolarItemInfoDTO polarItemInfoDTO = new PolarItemInfoDTO();
        list.add(polarItemInfoDTO);
        serviceData.setBo(list);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("111");
        BasicsettingRemoteService.getPolarItemInfoList(new PolarItemInfoDTO());
        serviceData.setBo(null);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        PolarItemInfoDTO infoDTO = new PolarItemInfoDTO();
        BasicsettingRemoteService.getPolarItemInfoList(infoDTO);
        Assert.assertNull(infoDTO.getBrand());
    }

    /* Started by AICoder, pid:07ad0db20dda43e79c60e5c731d3e504 */
    @Test
    public void getLookupTypesByValue() throws Exception
    {
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupMeaning("234324");
        BasicsettingRemoteService.getLookupTypesByValue(dto);
        List<SysLookupValuesDTO> list=new ArrayList<>();
        list.add(dto);
        Assert.assertNotNull(list);
    }
    /* Ended by AICoder, pid:07ad0db20dda43e79c60e5c731d3e504 */

    @Test
    public void getErrorCodeInfoList() throws Exception {
        BsErrorCodeInfoDTO dto = new BsErrorCodeInfoDTO();
        dto.setErrorCode("11");
        BasicsettingRemoteService.getErrorCodeInfoList();
        List<BsErrorCodeInfoDTO> list = new ArrayList<>();
        list.add(dto);
        Assert.assertNotNull(list);
    }

    @Test
    public void getBManufactureCapacityInfo() throws Exception {
        BManufactureCapacityDTO dto = new BManufactureCapacityDTO();
        dto.setItemCode("11");
        dto.setUph("11");
        BasicsettingRemoteService.getBManufactureCapacityInfo("",new ArrayList<>());
        List<BManufactureCapacityDTO> list = new ArrayList<>();
        list.add(dto);
        Assert.assertNotNull(list);
    }


    @Test
    public void getBrandItemDirection() throws Exception {
        BasicsettingRemoteService.getBrandItemDirection(new ArrayList<>());

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("11");
        List<PolarItemInfoDTO> list = new ArrayList<>();
        BasicsettingRemoteService.getBrandItemDirection(list);
        Assert.assertEquals(new ArrayList<>(), list);
    }

    @Test
    public void getBomDetailTagNo() throws Exception {
        BasicsettingRemoteService.getBomDetailTagNo("");

        String productCode = "123";
        BasicsettingRemoteService.getBomDetailTagNo(productCode);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("11");
        BasicsettingRemoteService.getBomDetailTagNo(productCode);
        Assert.assertEquals("123", productCode);
    }

    @Test
    public void queryDirectionByItemNo() {
        BasicsettingRemoteService.queryDirectionByItemNo(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("123");
        List<String> list = new ArrayList<>();
        BasicsettingRemoteService.queryDirectionByItemNo(list);
        Assert.assertEquals(new ArrayList<>(), list);
    }

    @Test
    public void getMtlSecondaryInventories() {
        BasicsettingRemoteService.getMtlSecondaryInventories(new MtlSecondaryInventories());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("123");
        List<MtlSecondaryInventories> list = BasicsettingRemoteService.getMtlSecondaryInventories(new MtlSecondaryInventories());
        Assert.assertNull(list);
    }

    @Test
    public void selectBsBomHierarchicalByPage() {
        BasicsettingRemoteService.selectBsBomHierarchicalByPage(new Page<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("123");
        Page<BsBomHierarchicalDetail> list = BasicsettingRemoteService.selectBsBomHierarchicalByPage(new Page<>());
        Assert.assertNull(list);
    }
}