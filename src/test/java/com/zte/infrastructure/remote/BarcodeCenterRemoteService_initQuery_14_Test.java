/*Started by AICoder, pid:h302ay9e69aba2714e1d0bed10c0a19fde719b5b*/
package com.zte.infrastructure.remote;

import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;

public class BarcodeCenterRemoteService_initQuery_14_Test {
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void setUp() {
        barcodeCenterRemoteService = new BarcodeCenterRemoteService();
    }

    @Test
    public void testInitQuery_NullQueryDTO() throws Exception {
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testInitQuery_EmptyBarcodeList() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setParentCategoryCode("category");
        queryDTO.setBarcodeList(Collections.emptyList());
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(queryDTO);
        assertTrue(result.isEmpty());
    }
    @Test
    public void testInitQuery_EmptyParentCategoryCodet() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(new ArrayList<String>(Arrays.asList("barcode1", "barcode2")));
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(queryDTO);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testInitQuery_BarcodeListSizeGreaterThan100() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setParentCategoryCode("category");
        List<String> snList = new ArrayList<>();
        for (int i = 0; i < 120; i++) {
            snList.add("sn"+i);
        }
        queryDTO.setBarcodeList(snList);
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(queryDTO);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testInitQuery_BarcodeListSizeLessThan5() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setParentCategoryCode("category");
        queryDTO.setBarcodeList(
                new ArrayList<String>(
                        Arrays.asList("barcode1", "barcode2", "barcode3", "barcode4")));
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(queryDTO);
        assertTrue(result.isEmpty());
    }
    @Test
    public void testInitQuery_EmptyParentCategoryCode2() throws Exception {
        BarcodeExpandQueryDTO queryDTO = new BarcodeExpandQueryDTO();
        queryDTO.setBarcodeList(Arrays.asList("123", "456", "456", "456", "456", "456"));
        queryDTO.setParentCategoryCode("2");
        List<BarcodeExpandDTO> result = barcodeCenterRemoteService.initQuery(queryDTO);
        assertTrue(result.isEmpty());
    }
}
/*Ended by AICoder, pid:h302ay9e69aba2714e1d0bed10c0a19fde719b5b*/