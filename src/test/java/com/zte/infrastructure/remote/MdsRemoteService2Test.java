package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({HttpRemoteUtil.class, BasicsettingRemoteService.class})
public class MdsRemoteService2Test extends PowerBaseTestCase {

    @InjectMocks
    private MdsRemoteService service;

    @Mock
    private RedisTemplate<String,Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;

    @Test
    public void queryCrossInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, BasicsettingRemoteService.class);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("123");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732011)).thenReturn(null);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732010)).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});
        try
        {
            service.queryCrossInfo(Lists.newArrayList(new HashMap()));
        }
        catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        service.queryItemInfo(null);
        try {
            service.queryItemInfo(Lists.newArrayList(new HashMap()));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732011)).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});

        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString(new ServiceData()));
        try {
            service.queryItemInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);
        }
        try {
            service.queryCrossInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap(){{put("list", null);}});}}));
        try {
            service.queryItemInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList(new HashMap(){{put("1","1");put("procedure","FT");}}));}}));
        try {
            service.queryCrossInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void queryItemInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, BasicsettingRemoteService.class);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("123");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732010)).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732011)).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});

        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap(){{put("list", Lists.newArrayList(new HashMap(){{put("1","1");}}));}});}}));
        try {
            service.queryItemInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  any(),anyString(), anyString())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList(new HashMap(){{put("1","1");}}));}}));
        try {
            service.queryCrossInfo(Lists.newArrayList(new HashMap(){{put("item_code", "1");}}));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }
}