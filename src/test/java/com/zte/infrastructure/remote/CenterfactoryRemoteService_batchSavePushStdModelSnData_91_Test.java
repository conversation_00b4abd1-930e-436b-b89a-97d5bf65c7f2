/*Started by AICoder, pid:ja90dn0ea9wa401147430a388141c7160923cb26*/
package com.zte.infrastructure.remote;

import com.zte.common.ConstantInterface;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, ConstantInterface.class})
public class CenterfactoryRemoteService_batchSavePushStdModelSnData_91_Test {

    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);

        when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
    }

    @Test
    public void testBatchSavePushStdModelSnData_Success() throws Exception {
        // Given
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Arrays.asList(new PushStdModelSnDataDTO());
        String result = "{\"status\":\"success\"}";
        
        when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), eq(MicroServiceNameEum.SENDTYPEPOST)))
                .thenReturn(result);

        // When
        centerfactoryRemoteService.batchSavePushStdModelSnData(pushStdModelSnDataList);

        // Then
        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    @Test
    public void testBatchSavePushStdModelSnData_Exception() throws Exception {
        // Given
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Arrays.asList(new PushStdModelSnDataDTO());
        String result = "{\"status\":\"error\"}";

        when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), eq(MicroServiceNameEum.SENDTYPEPOST)))
                .thenReturn(result);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(eq(result))).thenThrow(new RuntimeException());

        // When & Then
        assertThrows(Exception.class, () -> {
            centerfactoryRemoteService.batchSavePushStdModelSnData(pushStdModelSnDataList);
        });

        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }
}
/*Ended by AICoder, pid:ja90dn0ea9wa401147430a388141c7160923cb26*/