/*Started by AICoder, pid:h90062502cv594414fd50900d10118364a073053*/
package com.zte.infrastructure.remote;

import com.zte.common.ConstantInterface;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, ConstantInterface.class})
public class CenterfactoryRemoteService_handlePushStdModelSnData_93_Test {

    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
    }

    @Test
    public void testHandlePushStdModelSnData_Success() throws Exception {
        // Given
        String pushStdModelSnDataId = "123";
        String sbuStock = "stock1";
        String currProcess = "process1";
        List<WipExtendIdentification> wipExtendIdentifications = Arrays.asList(new WipExtendIdentification());

        Map<String, Object> params = new HashMap<>();
        params.put("pushStdModelSnDataId", pushStdModelSnDataId);
        params.put("sbuStock", sbuStock);
        params.put("currProcess", currProcess);
        params.put("wipExtendIdentifications", wipExtendIdentifications);

        String url = "http://example.com/api";
        String result = "{\"status\":\"success\"}";

        when(ConstantInterface.getUrlStatic(any())).thenReturn(url);
        when(HttpRemoteUtil.remoteExe(anyString(), any(), anyString(), anyString())).thenReturn(result);

        // When
        centerfactoryRemoteService.handlePushStdModelSnData(pushStdModelSnDataId, sbuStock, currProcess, wipExtendIdentifications);

        // Then
        PowerMockito.verifyStatic(HttpRemoteUtil.class);
        HttpRemoteUtil.remoteExe(anyString(), any(), anyString(), anyString());
        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    @Test
    public void testHandlePushStdModelSnData_Exception() throws Exception {
        // Given
        String pushStdModelSnDataId = "123";
        String sbuStock = "stock1";
        String currProcess = "process1";
        List<WipExtendIdentification> wipExtendIdentifications = Arrays.asList(new WipExtendIdentification());

        Map<String, Object> params = new HashMap<>();
        params.put("pushStdModelSnDataId", pushStdModelSnDataId);
        params.put("sbuStock", sbuStock);
        params.put("currProcess", currProcess);
        params.put("wipExtendIdentifications", wipExtendIdentifications);

        String url = "http://example.com/api";
        String result = "{\"status\":\"error\"}";

        when(ConstantInterface.getUrlStatic(any())).thenReturn(url);
        when(HttpRemoteUtil.remoteExe(anyString(), any(), anyString(), anyString())).thenReturn(result);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(eq(result))).thenThrow(new RuntimeException());

        // When & Then
        assertThrows(Exception.class, () -> {
            centerfactoryRemoteService.handlePushStdModelSnData(pushStdModelSnDataId, sbuStock, currProcess, wipExtendIdentifications);
        });

        PowerMockito.verifyStatic(HttpRemoteUtil.class);
        HttpRemoteUtil.remoteExe(anyString(), any(), anyString(), anyString());
        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }
}
/*Ended by AICoder, pid:h90062502cv594414fd50900d10118364a073053*/