/*Started by AICoder, pid:0cb899119caf45d6a24ac0ec6413d6ec*/
package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.storage.StoragePageDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;

@PrepareForTest({HttpRemoteService.class,InterfaceEnum.class,MicroServiceRestUtil.class, ConstantInterface.class,
        HttpClientUtil.class,MESHttpHelper.class,ServiceDataBuilderUtil.class,HttpRemoteUtil.class})
public class ProductionDeliveryRemoteServiceTest extends PowerBaseTestCase {
    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ConstantInterface.class,
                HttpClientUtil.class,MESHttpHelper.class,ServiceDataBuilderUtil.class,HttpRemoteUtil.class);
    }

    @Test
    public void semiManufactureDealInfoInsertBatch(){
        ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatch(null);
        ProductionDeliveryRemoteService.semiManufactureDealInfoInsertBatchAndWriteMode(null);
        Assert.assertTrue(true);
    }

    @Test
    public void getPageContainerContent() throws Exception {

        Map<String,Object> map=new HashMap<>();
        Map<String,String> headerMap=new HashMap<>();
        headerMap.put("X-Factory-Id","52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        PowerMockito.when(ConstantInterface.getUrlStatic(Mockito.any())).thenReturn("/containerContentInfoCtrl/getPageContainerContentPage");
        String result="{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"itemBarcode\": \"220015044049\",\n" +
                "      \"qtyChange\": 18246\n" +
                "  \n" +
                "    }\n" +
                "  ],\n" +
                "  \"other\": null\n" +
                "}";
        String msg = JSON.toJSONString(new ServiceData<List<PsWipInfoDTO>>() {{
            setBo(null);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }});
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(),Mockito.anyMap(),Mockito.anyString(),Mockito.anyString())).thenReturn(result);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(msg);
        try{
            ProductionDeliveryRemoteService.getPageContainerContent(map);
        }catch(Exception e) {
            Assert.assertNotNull(ProductionDeliveryRemoteService.getPageContainerContent(map));
        }
    }


    @Test
    public void getContainerContentInfoBatch() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);

        ProductionDeliveryRemoteService.getContainerContentInfoBatch(null);

        ServiceData<List<PsTask>> serviceData=new ServiceData<>();
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setWorkOrderNo("N");
        psTaskList.add(psTask);
        serviceData.setBo(psTaskList);
        PowerMockito.when(MicroServiceRestUtil.invokeService(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                .thenReturn(JSON.toJSONString(serviceData));
        ProductionDeliveryRemoteService.getContainerContentInfoBatch(Arrays.asList("LPN00"));
        Assert.assertEquals("N", psTask.getWorkOrderNo());

    }

    @Test
    public void getPageContainerContentPage () throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);

        ProductionDeliveryRemoteService.getPageContainerContentPage(new ContainerContentInfoSnQueryDTO());

        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(
                JSON.toJSONString(new ServiceData() {{
                    Page<ContainerContentInfoSnQueryDTO> page = new Page<>();
                    List<ContainerContentInfoSnQueryDTO> dtoList = new ArrayList<>();
                    ContainerContentInfoSnQueryDTO dto = new ContainerContentInfoSnQueryDTO();
                    dto.setLpn("123");
                    dtoList.add(dto);
                    page.setRows(dtoList);
                    setBo(page);
                }}));
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(jsonNode);
        ProductionDeliveryRemoteService.getPageContainerContentPage(new ContainerContentInfoSnQueryDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getWarehouseName () throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);
        try {
            ProductionDeliveryRemoteService.getWarehouseName();
        } catch (Exception e){
            Assert.assertTrue(MessageId.CALL_SERVICE_FAILED.equals(e.getMessage()));
        }

        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(
                JSON.toJSONString(new ServiceData() {{
                    Page<ContainerContentInfoSnQueryDTO> page = new Page<>();
                    List<ContainerContentInfoSnQueryDTO> dtoList = new ArrayList<>();
                    ContainerContentInfoSnQueryDTO dto = new ContainerContentInfoSnQueryDTO();
                    dto.setLpn("123");
                    dtoList.add(dto);
                    page.setRows(dtoList);
                    setBo(page);
                }}));
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(jsonNode);
        ProductionDeliveryRemoteService.getWarehouseName();
    }


    @Test
    public void queryIsStock() throws Exception{
        List<String> snList = new ArrayList<>();
        ProductionDeliveryRemoteService.queryIsStock(snList);
        snList.add("123");
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.queryIsStock(snList);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(
                JSON.toJSONString(new ServiceData() {{
                    setBo("123");
                }}));
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(jsonNode);
        ProductionDeliveryRemoteService.queryIsStock(snList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void testGetSolderUseBill() {
        ProductionDeliveryRemoteService.getSolderUseBill(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getSolderUseBill(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getSolderUseBill(new ArrayList<>());
        Assert.assertEquals("bo", bo);
    }

    @Test
    public void getWarehouseAndAreaByList() {
        List<String> locationCodes = new ArrayList<>();
        ProductionDeliveryRemoteService.getWarehouseAndAreaByList(Collections.singletonList(null));
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        locationCodes.add("123");
        ProductionDeliveryRemoteService.getWarehouseAndAreaByList(locationCodes);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getWarehouseAndAreaByList(locationCodes);
        Assert.assertEquals("bo", bo);
    }
    @Test
    public void testGetFixtureUseBill() {
        ProductionDeliveryRemoteService.getFixtureUseBill(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getFixtureUseBill(new ArrayList<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getFixtureUseBill(new ArrayList<>());
        Assert.assertEquals("bo", bo);
    }

    @Test
    public void testGetFixtureRelation() throws Exception {
        ProductionDeliveryRemoteService.getRelationInfoByProductList(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getRelationInfoByProductList(new FixtureReceiveRelationshipDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getRelationInfoByProductList(new FixtureReceiveRelationshipDTO());
        Assert.assertEquals("bo", bo);
    }

	@Test
	public void getLowPositionInfoByWorkOrder(){
		ProductionDeliveryRemoteService.getLowPositionInfoByWorkOrder(null);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		String bo = "bo";
		PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
				.thenReturn(null);
		ProductionDeliveryRemoteService.getLowPositionInfoByWorkOrder(new OutboundOrderLineDTO());
		PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
				.thenReturn(bo);
		ProductionDeliveryRemoteService.getLowPositionInfoByWorkOrder(new OutboundOrderLineDTO());
		Assert.assertEquals("bo", bo);
	}

    @Test
    public void testGetReqHead() {
        ProductionDeliveryRemoteService.getReqHead(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getReqHead(new WmsReqHeadDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getReqHead(new WmsReqHeadDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getReqHead(new WmsReqHeadDTO());
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(result);
        ProductionDeliveryRemoteService.getReqHead(new WmsReqHeadDTO());
        Assert.assertEquals("bo", bo);
    }
    @Test
    public void testGetPdRequirementHead() throws Exception {
        ProductionDeliveryRemoteService.getPdRequirementHead(null);
        List<String> workOrders = new ArrayList<>();
        workOrders.add("123");
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getPdRequirementHead(workOrders);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getPdRequirementHead(workOrders);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        Assert.assertNull(ProductionDeliveryRemoteService.getPdRequirementHead(workOrders));
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(result);
        ProductionDeliveryRemoteService.getPdRequirementHead(workOrders);
    }
    @Test
    public void testGetPdReHeadForRedPoint() throws Exception {
        ProductionDeliveryRemoteService.getPdReHeadForRedPoint(null);
        Map<String, Object> map = new HashMap<>();
        map.put("workOrderNo", "123");
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        ProductionDeliveryRemoteService.getPdReHeadForRedPoint(map);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.getPdReHeadForRedPoint(map);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        Assert.assertNull(ProductionDeliveryRemoteService.getPdReHeadForRedPoint(map));
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(result);
        ProductionDeliveryRemoteService.getPdReHeadForRedPoint(map);
    }

    @Test
    public void selectPackInfoOfTubeCoreTest() {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointCall(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(new ServiceData<>() );
        List<PackingInfoDTO> test = ProductionDeliveryRemoteService.selectPackInfoOfTubeCore("test");
        Assert.assertTrue(CollectionUtils.isEmpty(test));
    }

    @Test
    public void savePackInfoOfTubeCoreTest() {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointCall(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(new ServiceData<>() );
        ProductionDeliveryRemoteService.savePackInfoOfTubeCore(new PackingInfoDTO());
        Assert.assertTrue(1==1);
    }

    @Test
    public void submitSupplementBill() {
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("");
        PdRequirementHeaderDTO dto = new PdRequirementHeaderDTO();
        try {
            ProductionDeliveryRemoteService.submitSupplementBill(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        PdRequirementHeaderDTO result = new PdRequirementHeaderDTO();
        result.setReqNo("BLD20240528");
        String rtStr = JSONObject.toJSONString(result);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(rtStr);
        PdRequirementHeaderDTO rt = ProductionDeliveryRemoteService.submitSupplementBill(dto);
        Assert.assertEquals("BLD20240528", rt.getReqNo());
    }

    @Test
    public void isKeyDevices() {
        String itemNo = "";
        String keyDevices = ProductionDeliveryRemoteService.isKeyDevices(itemNo);
        Assert.assertEquals(Constant.FLAG_N, keyDevices);

        itemNo = "1111111";
        ServiceData serviceData = new ServiceData<>();
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointCall(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(serviceData);
        keyDevices = ProductionDeliveryRemoteService.isKeyDevices(itemNo);
        Assert.assertEquals(Constant.FLAG_N, keyDevices);

        serviceData.setBo(0);
        keyDevices = ProductionDeliveryRemoteService.isKeyDevices(itemNo);
        Assert.assertEquals(Constant.FLAG_N, keyDevices);

        serviceData.setBo(1);
        keyDevices = ProductionDeliveryRemoteService.isKeyDevices(itemNo);
        Assert.assertEquals(Constant.FLAG_Y, keyDevices);

    }

    @Test(timeout = 8000)
    public void testUpdateConInfoIdByLpnStatus_WithValidList() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);

        List<String> list = Arrays.asList("LPN1", "LPN2", "LPN3");
        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.updateConInfoIdByLpnStatus)).thenReturn("ewr");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("");
        ProductionDeliveryRemoteService.updateConInfoIdByLpnStatus(list);
        Assert.assertNotNull(list);

        String bo = JSON.toJSONString(1);
        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.updateConInfoIdByLpnStatus)).thenReturn("ewr");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(bo);
        ProductionDeliveryRemoteService.updateConInfoIdByLpnStatus(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void getMtlSecondaryInventories() {
        ProductionDeliveryRemoteService.queryStockByWarehouseId(new StorageQueryPageDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("123");
        StoragePageDTO<WmsReportStockDTO> result = ProductionDeliveryRemoteService.queryStockByWarehouseId(new StorageQueryPageDTO());
        Assert.assertNull(result);
    }
}
/*Ended by AICoder, pid:0cb899119caf45d6a24ac0ec6413d6ec*/