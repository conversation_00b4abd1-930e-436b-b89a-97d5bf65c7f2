/*Started by AICoder, pid:o62c3vdf7fh4f95145980b6640b917599a1061bb*/
package com.zte.infrastructure.remote;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;

public class FilePreViewRemoteService_getPreViewUrl_0_Test {
    private FilePreViewRemoteService filePreViewRemoteService;

    @Before
    public void setup() {
        filePreViewRemoteService = new FilePreViewRemoteService();
    }

    @Test(expected = MesBusinessException.class)
    public void testGetPreViewUrlWithEmptyFileDownloadUrl() throws Exception {
        String fileDownloadUrl = "";
        String fileId = "123";
        String fileName = "test.pdf";
        String empNo = "emp123";
        filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetPreViewUrlWithNullFileDownloadUrl() throws Exception {
        String fileDownloadUrl = null;
        String fileId = "123";
        String fileName = "test.pdf";
        String empNo = "emp123";
        filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
    }
}
/*Ended by AICoder, pid:o62c3vdf7fh4f95145980b6640b917599a1061bb*/