package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.interfaces.dto.AgeingInfoFenceDatabaseQueryDTO;
import com.zte.interfaces.dto.AgeingInfoInDateParamDTO;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({MicroServiceRestUtil.class, ServiceDataBuilderUtil.class, HttpRemoteService.class,})
public class EqpmgmtsRemoteServiceTest extends PowerBaseTestCase {

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void testGetTemperatureList() {
        EqpmgmtsRemoteService.getTemperatureList(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        EqpmgmtsRemoteService.getTemperatureList(new HashMap<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        EqpmgmtsRemoteService.getTemperatureList(new HashMap<>());
        Assert.assertEquals("bo", bo);
    }

    @Test
    public void testGetEmSmtStencilReceive() {
        EqpmgmtsRemoteService.getEmSmtStencilReceive(null);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String bo = "bo";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        EqpmgmtsRemoteService.getEmSmtStencilReceive(new HashMap<>());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(bo);
        EqpmgmtsRemoteService.getEmSmtStencilReceive(new HashMap<>());
        Assert.assertEquals("bo", bo);
    }

    @Test
    public void getInforOfPdcount() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        EmEqpPdcountDTO eqpPdcountDto = new EmEqpPdcountDTO();
        List<EmEqpPdcountDTO> list = new ArrayList<>();
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(CollectionUtils.isEmpty(list));
        }

        eqpPdcountDto.setMaterialCode("10");
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(null);
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        ServiceData<List<EmEqpPdcountDTO>> serviceData = new ServiceData<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        serviceData.setCode(null);
        serviceData.setBo(null);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        serviceData.setCode(null);
        serviceData.setBo(list);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        EmEqpPdcountDTO eqpPdcountDto2 = new EmEqpPdcountDTO();
        eqpPdcountDto2.setThrowsNumber(new BigDecimal("10"));
        list.add(eqpPdcountDto2);
        serviceData.setBo(list);
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        List<EmEqpPdcountDTO> bo = EqpmgmtsRemoteService.getInforOfPdcount(eqpPdcountDto);
        Assert.assertNotNull(bo);
    }

    @Test
    public void getAgeingOutDate() {
        AgeingInfoInDateParamDTO dto = new AgeingInfoInDateParamDTO();
        dto.setKey(1);

        JsonNode jsonNodeMock = Mockito.mock(JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn(String.valueOf(1));
        int res = EqpmgmtsRemoteService.getAgeingOutDate(dto);
        Assert.assertEquals(1, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("");
        res = EqpmgmtsRemoteService.getAgeingOutDate(dto);
        Assert.assertEquals(0, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("a");
        res = EqpmgmtsRemoteService.getAgeingOutDate(dto);
        Assert.assertEquals(0, res);

        jsonNodeMock = null;
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        res = EqpmgmtsRemoteService.getAgeingOutDate(dto);
        Assert.assertEquals(0, res);
    }

    @Test
    public void getAgeingInDate() {
        AgeingInfoInDateParamDTO dto = new AgeingInfoInDateParamDTO();
        dto.setKey(1);

        JsonNode jsonNodeMock = Mockito.mock(JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn(String.valueOf(1));
        int res = EqpmgmtsRemoteService.getAgeingInDate(dto);
        Assert.assertEquals(1, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("");
        res = EqpmgmtsRemoteService.getAgeingInDate(dto);
        Assert.assertEquals(0, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("a");
        res = EqpmgmtsRemoteService.getAgeingInDate(dto);
        Assert.assertEquals(0, res);

        jsonNodeMock = null;
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        res = EqpmgmtsRemoteService.getAgeingInDate(dto);
        Assert.assertEquals(0, res);
    }

    @Test
    public void countTotalByAllTime() {
        AgeingInfoFenceDatabaseQueryDTO dto = new AgeingInfoFenceDatabaseQueryDTO();
        dto.setStartTimeLength(1);

        JsonNode jsonNodeMock = Mockito.mock(JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn(String.valueOf(1));
        int res = EqpmgmtsRemoteService.countTotalByAllTime(dto);
        Assert.assertEquals(1, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("");
        res = EqpmgmtsRemoteService.countTotalByAllTime(dto);
        Assert.assertEquals(0, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("a");
        res = EqpmgmtsRemoteService.countTotalByAllTime(dto);
        Assert.assertEquals(0, res);

        jsonNodeMock = null;
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        res = EqpmgmtsRemoteService.countTotalByAllTime(dto);
        Assert.assertEquals(0, res);
    }

    @Test
    public void queryAgeingInfoFence() {
        AgeingInfoFenceDatabaseQueryDTO dto = new AgeingInfoFenceDatabaseQueryDTO();
        dto.setLastProcess("1");

        JsonNode jsonNodeMock = Mockito.mock(JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn(String.valueOf(1));
        int res = EqpmgmtsRemoteService.queryAgeingInfoFence(dto);
        Assert.assertEquals(1, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("");
        res = EqpmgmtsRemoteService.queryAgeingInfoFence(dto);
        Assert.assertEquals(0, res);

        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO)).thenReturn(jsonNodeMock);
        PowerMockito.when(jsonNodeMock.get(MpConstant.JSON_BO).toString()).thenReturn("a");
        res = EqpmgmtsRemoteService.queryAgeingInfoFence(dto);
        Assert.assertEquals(0, res);

        jsonNodeMock = null;
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNodeMock);
        res = EqpmgmtsRemoteService.queryAgeingInfoFence(dto);
        Assert.assertEquals(0, res);
    }

    @Test
    public void getEpqPdcountInfo() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        HashMap<String, Object> map = new HashMap<>();
        List<EmEqpPdcountDTO> list = new ArrayList<>();
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(null);
        try {
            EqpmgmtsRemoteService.getEpqPdcountInfo(map);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        ServiceData<List<EmEqpPdcountDTO>> serviceData = new ServiceData<>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            EqpmgmtsRemoteService.getEpqPdcountInfo(map);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getExMsgId());
        }

        EmEqpPdcountDTO eqpPdcountDto2 = new EmEqpPdcountDTO();
        eqpPdcountDto2.setThrowsNumber(new BigDecimal("10"));
        list.add(eqpPdcountDto2);
        serviceData.setBo(list);
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        List<EmEqpPdcountDTO> bo = EqpmgmtsRemoteService.getEpqPdcountInfo(map);
        Assert.assertEquals(10, bo.get(0).getThrowsNumber().intValue());
    }


}