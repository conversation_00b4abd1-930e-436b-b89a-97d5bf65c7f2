/*Started by AICoder, pid:gac21a63f3ta14f14b4b0b0e812b9d189b6126ee*/
package com.zte.infrastructure.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.PushStdModelDataQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, ConstantInterface.class})
public class CenterfactoryRemoteService_getPushStdModelData_90_Test {

    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
    }

    @Test
    public void testGetPushStdModelData() throws Exception {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        String url = "url";
        when(ConstantInterface.getUrlStatic(any())).thenReturn(url);
        String jsonResponse = "{\"data\": []}";
        String bo = "true";

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(MicroServiceNameEum.SENDTYPEPOST)))
                .thenReturn(jsonResponse);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResponse)).thenReturn(bo);
        PageRows<Object> pageRows = new PageRows<>();
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean(eq(bo), any(TypeReference.class))).thenReturn(pageRows);

        // When
        PageRows<PushStdModelDataDTO> result = centerfactoryRemoteService.getPushStdModelData(query);

        // Then
        assertEquals(pageRows, result);
        PowerMockito.verifyStatic(HttpRemoteUtil.class);
        HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(MicroServiceNameEum.SENDTYPEPOST));
        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResponse);
        PowerMockito.verifyStatic(JacksonJsonConverUtil.class);
        JacksonJsonConverUtil.jsonToBean(eq(bo), any(TypeReference.class));
    }
}
/*Ended by AICoder, pid:gac21a63f3ta14f14b4b0b0e812b9d189b6126ee*/