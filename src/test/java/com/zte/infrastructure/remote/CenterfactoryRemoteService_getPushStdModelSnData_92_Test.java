/*Started by AICoder, pid:i37a8tc3darc7e114eea0a4601cdde2cf808919a*/
package com.zte.infrastructure.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.common.ConstantInterface;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, ConstantInterface.class})
public class CenterfactoryRemoteService_getPushStdModelSnData_92_Test {

    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
    }

    @Test
    public void testGetPushStdModelSnData_Success() throws Exception {
        // Given
        PushStdModelSnDataQueryDTO query = new PushStdModelSnDataQueryDTO();
        String jsonResult = "{\"data\": []}";
        String bo = "true";
        String url = "url";

        when(ConstantInterface.getUrlStatic(any())).thenReturn(url);
        when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(MicroServiceNameEum.SENDTYPEPOST)))
                .thenReturn(jsonResult);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResult)).thenReturn(bo);
        PageRows<Object> pageRows = new PageRows<>();
        when(JacksonJsonConverUtil.jsonToBean(eq(bo), any(TypeReference.class))).thenReturn(pageRows);

        // When
        PageRows<PushStdModelSnDataDTO> result = centerfactoryRemoteService.getPushStdModelSnData(query);

        // Then
        assertEquals(pageRows, result);
        PowerMockito.verifyStatic(HttpRemoteUtil.class);
        HttpRemoteUtil.remoteExe(anyString(), anyMap(), eq(url), eq(MicroServiceNameEum.SENDTYPEPOST));
        PowerMockito.verifyStatic(ServiceDataBuilderUtil.class);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResult);
        PowerMockito.verifyStatic(JacksonJsonConverUtil.class);
        JacksonJsonConverUtil.jsonToBean(eq(bo), any(TypeReference.class));
    }
}
/*Ended by AICoder, pid:i37a8tc3darc7e114eea0a4601cdde2cf808919a*/