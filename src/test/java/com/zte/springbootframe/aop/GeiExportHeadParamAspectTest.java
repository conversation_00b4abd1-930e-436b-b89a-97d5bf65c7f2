/*Started by AICoder, pid:gb7c8hd198c21b4140bd09b250ec295a76e58f1b*/
package com.zte.springbootframe.aop;

import com.zte.gei.common.utils.ExportThreadLocalUtils;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.MapUtils;
import org.aspectj.lang.JoinPoint;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.mock;

public class GeiExportHeadParamAspectTest extends PowerBaseTestCase {
    private GeiExportHeadParamAspect aspect;
    private JoinPoint joinPoint;

    @Before
    public void setUp() {
        aspect = new GeiExportHeadParamAspect();
        joinPoint = mock(JoinPoint.class);
    }

    @Test
    public void testSetDataSourceKey_WithEmptyMap() {
        // Given
        Map<String, String> emptyMap = new HashMap<>();
        ExportThreadLocalUtils.setLocalValue(emptyMap);

        // When
        aspect.setDataSourceKey(joinPoint);
        Assert.assertTrue(MapUtils.isEmpty(emptyMap));
    }

    @Test
    public void testSetDataSourceKey_WithNonEmptyMap() {
        // Given
        Map<String, String> nonEmptyMap = new HashMap<>();
        nonEmptyMap.put("key", "value");
        ExportThreadLocalUtils.setLocalValue(nonEmptyMap);

        // When
        aspect.setDataSourceKey(joinPoint);
        Assert.assertTrue(MapUtils.isNotEmpty(nonEmptyMap));
    }
}
/*Ended by AICoder, pid:gb7c8hd198c21b4140bd09b250ec295a76e58f1b*/