package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m04_importmaterialtrxinfosvr.ObjectOfInputRec;
import oerp_m12_importoperationmoveinfosrv.ObjectOfErrorCollection;
import oerp_m12_importoperationmoveinfosrv.ObjectOfInputCollection;
import oerp_m12_importoperationmoveinfosrv.ObjectOfResponseCollection;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class ObjectOfErrorCollectionTest extends PowerBaseTestCase {

    @InjectMocks
    private ObjectOfErrorCollection service;

    @InjectMocks
    private ObjectOfInputCollection objectOfInputCollection;

    @InjectMocks
    private ObjectOfResponseCollection objectOfResponseCollection;

    @InjectMocks
    private ObjectOfInputRec objectOfInputRec;


    /* Started by AICoder, pid:h0858t0b4100ba014a3008c6405cbc33f6f04801 */
    @Test
    public void test() {
        // 调用service的getErrorItem方法两次
        service.getErrorItem();
        service.getErrorItem();

        // 验证getErrorItem方法是否返回非null值
        Assert.assertTrue(service.getErrorItem() != null);

        // 调用objectOfInputCollection的getInputItem方法两次
        objectOfInputCollection.getInputItem();
        objectOfInputCollection.getInputItem();

        // 验证getInputItem方法是否返回非null值
        Assert.assertTrue(objectOfInputCollection.getInputItem() != null);

        // 调用objectOfResponseCollection的getResponseItem方法两次
        objectOfResponseCollection.getResponseItem();
        objectOfResponseCollection.getResponseItem();

        // 验证getResponseItem方法是否返回非null值
        Assert.assertTrue(objectOfResponseCollection.getResponseItem() != null);

        // 调用objectOfInputRec的getInput方法两次
        objectOfInputRec.getInput();
        objectOfInputRec.getInput();

        // 验证getInput方法是否返回非null值
        Assert.assertTrue(objectOfInputRec.getInput() != null);
    }
    /* Ended by AICoder, pid:h0858t0b4100ba014a3008c6405cbc33f6f04801 */
}
