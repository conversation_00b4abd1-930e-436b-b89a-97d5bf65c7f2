package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m12_importoperationmoveinfosrv.ObjectFactory;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class ObjectFactoryTest extends PowerBaseTestCase {

    @InjectMocks
    private ObjectFactory service;

    @Test
    public void test() {
        /* Started by AICoder, pid:ef4805a61403f7714da50a447021a2593d03df4f */
// 假设service是你定义的一个类实例，并且这些方法都是你的类中的一部分
        service.createOERPM12ImportOperationMoveInfoSrvResponse();
        service.createOERPM12ImportOperationMoveInfoSrvRequest();
        service.createObjectOfInputCollection();
        service.createArrayOfResponseItem();
        service.createObjectOfMsgHeader();
        service.createArrayOfInputItem();
        service.createArrayOfErrorItem();
        service.createObjectOfErrorCollection();
        service.createObjectOfResponseCollection();
        service.createFault();
        service.createOERPM12ImportOperationMoveInfoSrvRequest();
        service.createObjectOfInputCollection();
        service.createArrayOfInputItemATTRIBUTE15("");
        service.createArrayOfInputItemRESERVED8("");
        service.createArrayOfInputItemRESERVED9("");
        service.createArrayOfInputItemATTRIBUTECATEGORY("");
        service.createArrayOfInputItemTOOPERATIONSEQNUM("");
        service.createArrayOfInputItemATTRIBUTE8("");

        service.createArrayOfInputItemATTRIBUTE9("");
        service.createArrayOfInputItemATTRIBUTE4("");
        service.createArrayOfInputItemATTRIBUTE5("");
        service.createArrayOfInputItemATTRIBUTE6("");
        service.createArrayOfInputItemATTRIBUTE7("");
        service.createArrayOfInputItemATTRIBUTE1("");
        service.createArrayOfInputItemRESERVED1("");
        service.createArrayOfInputItemATTRIBUTE2("");
        service.createArrayOfInputItemRESERVED2("");
        service.createArrayOfInputItemATTRIBUTE3("");
        service.createArrayOfInputItemRESERVED3("");

        service.createArrayOfInputItemRESERVED4("");
        service.createArrayOfInputItemRESERVED5("");
        service.createArrayOfInputItemRESERVED6("");
        service.createArrayOfInputItemRESERVED7("");
        service.createArrayOfInputItemREASON("");
        service.createArrayOfInputItemRESERVED10("");
        service.createArrayOfInputItemTRANSACTIONUOM("");

        service.createArrayOfInputItemATTRIBUTE12("");
        service.createArrayOfInputItemATTRIBUTE13("");
        service.createArrayOfInputItemATTRIBUTE14("");
        service.createArrayOfErrorItemENTITYNAME("");
        service.createArrayOfErrorItemRECORDNUMBER("");

        service.createArrayOfErrorItemERRORMESSAGE("");
        service.createArrayOfResponseItemREQUESTID("");
        service.createArrayOfResponseItemRECORDNUMBER("");
        service.createFaultDetail("");
        service.createFaultFaultcode("");
        service.createFaultFaultstring("");
        Assert.assertNotNull(service.createFaultFaultactor(""));



        /* Ended by AICoder, pid:ef4805a61403f7714da50a447021a2593d03df4f */
    }
}
