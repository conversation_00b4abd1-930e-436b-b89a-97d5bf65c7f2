package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m12_importoperationmoveinfosrv.ArrayOfErrorItem;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class ArrayOfErrorItemTest extends PowerBaseTestCase {

    @InjectMocks
    private ArrayOfErrorItem service;

    @Test
    public void test() {
        service.setENTITYNAME(service.getENTITYNAME());
        service.setERRORMESSAGE(service.getERRORMESSAGE());
        service.setRECORDNUMBER(service.getRECORDNUMBER());
        Assert.assertNull(service.getENTITYNAME());
    }
}
