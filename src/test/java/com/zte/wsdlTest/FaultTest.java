package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m12_importoperationmoveinfosrv.Fault;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class FaultTest extends PowerBaseTestCase {

    @InjectMocks
    private Fault service;

    @Test
    public void test() {
        service.setDetail(service.getDetail());
        service.setFaultactor(service.getFaultactor());
        service.setFaultcode(service.getFaultcode());
        service.setFaultstring(service.getFaultstring());
        Assert.assertNull(service.getFaultstring());
    }
}
