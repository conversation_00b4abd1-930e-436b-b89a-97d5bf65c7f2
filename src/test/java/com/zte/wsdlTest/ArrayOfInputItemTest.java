package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m12_importoperationmoveinfosrv.ArrayOfInputItem;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class ArrayOfInputItemTest extends PowerBaseTestCase {

    @InjectMocks
    private ArrayOfInputItem service;

    @Test
    public void test() {
        /* Started by AICoder, pid:03007p903cf935614cc108ad504a3741ee0236c6 */
// 假设service是你定义的一个类实例，并且这些setter方法和getter方法都是你的类中的一部分

        service.setPKNAME(service.getPKNAME());
        service.setORGANIZATIONCODE(service.getORGANIZATIONCODE());
        service.setORGANIZATIONID(service.getORGANIZATIONID());
        service.setWIPENTITYNAME(service.getWIPENTITYNAME());
        service.setTRANSCATIONQTY(service.getTRANSCATIONQTY());
        service.setTRANSACTIONUOM(service.getTRANSACTIONUOM());
        service.setTRANSACTIONDATE(service.getTRANSACTIONDATE());
        service.setFMOPERATIONSEQNUM(service.getFMOPERATIONSEQNUM());
        service.setFMINTRAOPERATIONSTEPTYPE(service.getFMINTRAOPERATIONSTEPTYPE());
        service.setTOOPERATIONSEQNUM(service.getTOOPERATIONSEQNUM());
        service.setLASTOPERATIONFLAG(service.getLASTOPERATIONFLAG());
        service.setTOINTRAOPERATIONSTEPTYPE(service.getTOINTRAOPERATIONSTEPTYPE());
        service.setREASON(service.getREASON());
        service.setTRANSACTIONREFERENCE(service.getTRANSACTIONREFERENCE());
        service.setSOURCECODE(service.getSOURCECODE());
        service.setATTRIBUTECATEGORY(service.getATTRIBUTECATEGORY());
        service.setATTRIBUTE1(service.getATTRIBUTE1());
        service.setATTRIBUTE2(service.getATTRIBUTE2());
        service.setATTRIBUTE3(service.getATTRIBUTE3());
        service.setATTRIBUTE4(service.getATTRIBUTE4());
        service.setATTRIBUTE5(service.getATTRIBUTE5());
        service.setATTRIBUTE6(service.getATTRIBUTE6());
        service.setATTRIBUTE7(service.getATTRIBUTE7());
        service.setATTRIBUTE8(service.getATTRIBUTE8());
        service.setATTRIBUTE9(service.getATTRIBUTE9());
        service.setATTRIBUTE10(service.getATTRIBUTE10());
        service.setATTRIBUTE11(service.getATTRIBUTE11());
        service.setATTRIBUTE12(service.getATTRIBUTE12());
        service.setATTRIBUTE13(service.getATTRIBUTE13());
        service.setATTRIBUTE14(service.getATTRIBUTE14());
        service.setATTRIBUTE15(service.getATTRIBUTE15());
        service.setRESERVED1(service.getRESERVED1());
        service.setRESERVED2(service.getRESERVED2());
        service.setRESERVED3(service.getRESERVED3());
        service.setRESERVED4(service.getRESERVED4());
        service.setRESERVED5(service.getRESERVED5());
        service.setRESERVED6(service.getRESERVED6());
        service.setRESERVED7(service.getRESERVED7());
        service.setRESERVED8(service.getRESERVED8());
        service.setRESERVED9(service.getRESERVED9());
        service.setRESERVED10(service.getRESERVED10());
        Assert.assertNull(service.getRESERVED10());

        /* Ended by AICoder, pid:03007p903cf935614cc108ad504a3741ee0236c6 */
    }
}
