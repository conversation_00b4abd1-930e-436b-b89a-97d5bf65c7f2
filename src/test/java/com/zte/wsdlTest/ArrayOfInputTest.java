package com.zte.wsdlTest;

import com.zte.util.PowerBaseTestCase;
import oerp_m04_importmaterialtrxinfosvr.ArrayOfInput;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest()
public class ArrayOfInputTest extends PowerBaseTestCase {

    @InjectMocks
    private ArrayOfInput service;

    @Test
    public void test() {
        /* Started by AICoder, pid:rc28d0fc03ff6c11420b085c50af7d54ee421e38 */
// 假设service是你定义的一个类实例，并且这些setter方法和getter方法都是你的类中的一部分

        service.setPKNAME(service.getPKNAME());
        service.setAPIVERSION(service.getAPIVERSION());
        service.setTRANSACTIONTYPEID(service.getTRANSACTIONTYPEID());
        service.setACCOUNTALIAS(service.getACCOUNTALIAS());
        service.setWIPENTITYNAME(service.getWIPENTITYNAME());
        service.setORGANIZATIONID(service.getORGANIZATIONID());
        service.setORGANIZATIONCODE(service.getORGANIZATIONCODE());
        service.setSUBINVENTORYCODE(service.getSUBINVENTORYCODE());
        service.setLOCATORNAME(service.getLOCATORNAME());
        service.setLOCATORID(service.getLOCATORID());
        service.setITEMNUMBER(service.getITEMNUMBER());
        service.setINVENTORYITEMID(service.getINVENTORYITEMID());
        service.setITEMREVISION(service.getITEMREVISION());
        service.setTRANSACTIONQUANTITY(service.getTRANSACTIONQUANTITY());
        service.setTRANSACTIONDATE(service.getTRANSACTIONDATE());
        service.setTRANSFERORGANIZATIONID(service.getTRANSFERORGANIZATIONID());
        service.setTRANSFERORGANIZATIONCODE(service.getTRANSFERORGANIZATIONCODE());
        service.setTRANSFERSUBINVENTORY(service.getTRANSFERSUBINVENTORY());
        service.setTRANSFERLOCATOR(service.getTRANSFERLOCATOR());
        service.setTRANSFERLOCATORID(service.getTRANSFERLOCATORID());
        service.setREFRESHPRICEFLAG(service.getREFRESHPRICEFLAG());
        service.setUNITPRICE(service.getUNITPRICE());
        service.setTRANSACTIONREFERENCE(service.getTRANSACTIONREFERENCE());
        service.setSOURCECODE(service.getSOURCECODE());
        service.setSOURCEHEADERID(service.getSOURCEHEADERID());
        service.setSOURCELINEID(service.getSOURCELINEID());
        service.setTRXSOURCELINEID(service.getTRXSOURCELINEID());
        service.setTRANSACTIONMODE(service.getTRANSACTIONMODE());
        service.setEMPLOYEENUMBER(service.getEMPLOYEENUMBER());
        service.setUOMCODE(service.getUOMCODE());
        service.setATTRIBUTECATEGORY(service.getATTRIBUTECATEGORY());
        service.setATTRIBUTE1(service.getATTRIBUTE1());
        service.setATTRIBUTE2(service.getATTRIBUTE2());
        service.setATTRIBUTE3(service.getATTRIBUTE3());
        service.setATTRIBUTE4(service.getATTRIBUTE4());
        service.setATTRIBUTE5(service.getATTRIBUTE5());
        service.setATTRIBUTE6(service.getATTRIBUTE6());
        service.setATTRIBUTE7(service.getATTRIBUTE7());
        service.setATTRIBUTE8(service.getATTRIBUTE8());
        service.setATTRIBUTE9(service.getATTRIBUTE9());
        service.setATTRIBUTE10(service.getATTRIBUTE10());
        service.setATTRIBUTE11(service.getATTRIBUTE11());
        service.setATTRIBUTE12(service.getATTRIBUTE12());
        service.setATTRIBUTE13(service.getATTRIBUTE13());
        service.setATTRIBUTE14(service.getATTRIBUTE14());
        service.setATTRIBUTE15(service.getATTRIBUTE15());
        service.setPPARAMETER1(service.getPPARAMETER1());
        service.setPPARAMETER2(service.getPPARAMETER2());
        service.setPPARAMETER3(service.getPPARAMETER3());
        service.setPPARAMETER4(service.getPPARAMETER4());
        service.setPPARAMETER5(service.getPPARAMETER5());
        Assert.assertNull(service.getPPARAMETER5());


        /* Ended by AICoder, pid:rc28d0fc03ff6c11420b085c50af7d54ee421e38 */
    }
}
