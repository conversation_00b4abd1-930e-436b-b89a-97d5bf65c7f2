package com.zte.validate.impl;
/* Started by AICoder, pid:ca24123c34jf11014b520ab1e0d7336745343e38 */

import com.zte.common.utils.NumConstant;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.RepairApprovalDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.validation.ConstraintValidatorContext;

import java.util.ArrayList;
import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class RepairApprovalValidatorTest extends PowerBaseTestCase {

    @InjectMocks
    private RepairApprovalValidator repairApprovalValidator;

    @Mock
    private ConstraintValidatorContext context;

    @Before
    public void setUp() {
        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(mock(ConstraintValidatorContext.ConstraintViolationBuilder.class));
        when(context.buildConstraintViolationWithTemplate(anyString()).addPropertyNode(anyString())).thenReturn(mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext.class));
        when(context.buildConstraintViolationWithTemplate(anyString()).addPropertyNode(anyString()).addConstraintViolation()).thenReturn(context);
    }

    @Test
    public void testIsValid_NullValue() {
        assertTrue(repairApprovalValidator.isValid(null, context));
    }

    @Test
    public void testIsValid_ValidPositionNumber() {
        RepairApprovalDTO dto = new RepairApprovalDTO();
        dto.setApprovalType(NumConstant.STRING_ONE);
        dto.setPositionNumber("123");
        ArrayList<ApprovalProcessInfoEntityDTO> approvalOperateList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setApproverId("10349620");
        approvalProcessInfoEntityDTO.setSeq(0);
        approvalOperateList.add(approvalProcessInfoEntityDTO);
        dto.setApprovalOperator1(Collections.singletonList("10349620"));

        assertTrue(repairApprovalValidator.isValid(dto, context));
        dto.setApprovalType(NumConstant.STRING_ZERO);
        assertTrue(repairApprovalValidator.isValid(dto, context));
    }

    @Test
    public void testIsValid_InvalidPositionNumber() {
        RepairApprovalDTO dto = new RepairApprovalDTO();
        dto.setApprovalType(NumConstant.STRING_ONE);
        dto.setPositionNumber("");
        ArrayList<ApprovalProcessInfoEntityDTO> approvalOperateList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setSeq(0);
        approvalOperateList.add(approvalProcessInfoEntityDTO);
//        dto.setApprovalOperateList(approvalOperateList);

        assertFalse(repairApprovalValidator.isValid(dto, context));

        verify(context).buildConstraintViolationWithTemplate("{repairApproval.positionNumber.required}");

        approvalProcessInfoEntityDTO.setApproverId("10349620");
        approvalProcessInfoEntityDTO.setSeq(null);
        assertFalse(repairApprovalValidator.isValid(dto, context));
    }

    @Test
    public void testIsValid_InvalidApprovalOperator1() {
        RepairApprovalDTO dto = new RepairApprovalDTO();
        dto.setApprovalType("0");
        dto.setApprovalOperator1(new ArrayList<>());
        dto.setApprovalOperator2(new ArrayList<>());
        assertFalse(repairApprovalValidator.isValid(dto, context));
    }
    @Test
    public void testIsValid_InvalidApprovalOperator2() {
        RepairApprovalDTO dto = new RepairApprovalDTO();
        dto.setApprovalType("0");
        dto.setApprovalOperator1(null);
        dto.setApprovalOperator2(null);
        assertFalse(repairApprovalValidator.isValid(dto, context));
    }
}

/* Ended by AICoder, pid:ca24123c34jf11014b520ab1e0d7336745343e38 */