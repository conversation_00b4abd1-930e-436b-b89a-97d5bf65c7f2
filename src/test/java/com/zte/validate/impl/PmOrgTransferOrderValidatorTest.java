package com.zte.validate.impl;/* Started by AICoder, pid:j777f50b70keb0d147070a4780db1c55c86530ff */
import com.zte.domain.model.PmOrgTransferOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PmOrgTransferOrderValidatorTest {

    @InjectMocks
    private PmOrgTransferOrderValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIsValid_NullObject() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    public void testIsValid_EmptyItemCode() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setItemCode("");
        assertTrue(validator.isValid(order, context));
    }

    @Test(expected = NullPointerException.class)
    public void testIsValid_NonEmptyItemCodeAndNullTransferQuantity() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setItemCode("123");
        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(mock(ConstraintValidatorContext.ConstraintViolationBuilder.class));
        assertFalse(validator.isValid(order, context));
    }

    @Test
    public void testIsValid_NonEmptyItemCodeAndNonEmptyTransferQuantity() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setItemCode("123");
        order.setTransferQuantity(new BigDecimal(10));
        assertTrue(validator.isValid(order, context));
    }
    @Test(expected = NullPointerException.class)
    public void testIsValid_NonEmptyItemCodeAndZeroTransferQuantity() {
        PmOrgTransferOrder order = new PmOrgTransferOrder();
        order.setItemCode("123");
        order.setTransferQuantity(new BigDecimal(0));
        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(mock(ConstraintValidatorContext.ConstraintViolationBuilder.class));
        assertFalse(validator.isValid(order, context));
    }
}

/* Ended by AICoder, pid:j777f50b70keb0d147070a4780db1c55c86530ff */