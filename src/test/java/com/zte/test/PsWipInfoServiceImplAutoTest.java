package com.zte.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.anyMap;

@PrepareForTest({ BasicsettingRemoteService.class,PlanscheduleRemoteService.class,CommonUtils.class,SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class,CrafttechRemoteService.class,
        BarcodeCenterRemoteService.class})
public class PsWipInfoServiceImplAutoTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl psWipInfoServiceImpl;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Before
    public void init() {

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void getMaxSnByWorkOrderNo() throws Exception{
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("7000012-SMT-A5301");
        psWipInfo.setSn("700001200001");
        List<PsWipInfo> list = new ArrayList<>();
        list.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(anyMap())).thenReturn(list);

        Map<String, Object> record = new HashMap<>();
        record.put("orderField","sn");
        record.put("workOrderNo","7000012-SMT-A5301");
        record.put("order","desc");
        Assert.assertNotNull(psWipInfoServiceImpl.getMaxSnByWorkOrderNo(record));
    }

    @Test
    public void updateQty() throws Exception{
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(new ObjectMapper());
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(),
                anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": 790\n" +
                "}");
        try{
            psWipInfoServiceImpl.updatePsWorkorderQty(new PsEntityPlanBasicDTO());
        }catch (Exception e){
            String runNormal = "Y";
            Assert.assertEquals(Constant.STR_Y, runNormal);
        }



    }

    @Test
    public void insertPsWipInfoByScanTest() throws Exception {
        FlowControlInfoDTO dto = new FlowControlInfoDTO();
        dto.setSn("1");
        PsEntityPlanBasicDTO psDto = new PsEntityPlanBasicDTO();
        psDto.setWorkOrderNo("12323");
        psDto.setItemNo("1111");
        psDto.setItemName("sss");
        psDto.setRouteId("123123213");
        psDto.setWorkshopCode("213213");
        psDto.setCraftSection("221313");
        psDto.setSourceTask("ssss");
        dto.setEntityPlanBasic(psDto);
        dto.setaProcessCode("232323");
        dto.setErrorCode("231313");
        dto.setLineCode("22222");
        dto.setaSourceSysName("sssss");
        dto.setaSourceImu(new BigDecimal(1.2D));

        List<FlowControlInfoDTO> listFlow = new LinkedList<>();

        listFlow.add(dto);
        listFlow.add(dto);
        psWipInfoServiceImpl.insertPsWipInfoByScanBatch(listFlow,2);
        psWipInfoServiceImpl.insertPsWipInfoByScan(dto);
        psWipInfoServiceImpl.updatePsWipInfoByScan(dto);
        Assert.assertEquals("231313", dto.getErrorCode());
    }


}
