package com.zte.test;

import com.zte.application.AvlService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.impl.ImesPDACommonServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class,BasicsettingRemoteService.class,RedisHelper.class,PlanscheduleRemoteService.class,CommonUtils.class})
public class ImesPDACommonServiceImplTest {
    @InjectMocks
    private ImesPDACommonServiceImpl imesPDACommonServiceImpl;

    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private RedisLock redisLock;
	@Mock
	private AvlService avlService;
    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;
    @Mock
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;


    @Before
    public void init(){
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(RedisHelper.class);
    }

    @Test
    public void insertPrepareInfoForPda(){
        List<SmtMachineMaterialPrepare> preList=new ArrayList<>();
        SmtMachineMaterialPrepare preDto=new SmtMachineMaterialPrepare();
        preList.add(preDto);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfo(Mockito.any()))
                .thenReturn(preList);

        PDAReceiveItemsScanDTO entity=new PDAReceiveItemsScanDTO();
        entity.setLineCode("123");
        PkCodeInfo newPkCodeObj=new PkCodeInfo();
        newPkCodeObj.setPkCode("123");
        imesPDACommonServiceImpl.insertPrepareInfoForPda(entity,newPkCodeObj);

        List<SmtMachineMaterialPrepare> preList2=new ArrayList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfo(Mockito.any()))
                .thenReturn(preList2);
        imesPDACommonServiceImpl.insertPrepareInfoForPda(entity,newPkCodeObj);
        Assert.assertEquals("123", newPkCodeObj.getPkCode());
    }

    @Test
    public void pdaTransferScan() throws Exception{
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PDATransferScanDTO entity = new PDATransferScanDTO();
        entity.setSourceTask("8899515");
        entity.setDrItemCode("0400123456");
        entity.setBomNo("123ABCHUJJI");
        entity.setFormFlag(new BigDecimal(1));
        entity.setWorkOrder("8899515-SMT-5201");
        entity.setLineCode("SMT-CS3");
        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);

        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.any(), Mockito.anyInt()))
                .thenReturn(true);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setSourceTask("8899515");
        psWorkOrderBasic.setWorkOrderStatus(Constant.SUBMITTED);
        psWorkOrderBasic.setWorkOrderNo("8899515-SMT-5201");
        psWorkOrderBasic.setActualEndDate(new Date());
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);

        List<PkCodeInfo> pkCodeList = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        a1.setProductTask("8899515");
        a1.setItemCode("0400123456");
        a1.setIsLead("2");
        pkCodeList.add(a1);
        PowerMockito.when(pkCodeInfoRepository.getList(Mockito.any())).thenReturn(pkCodeList)
        ;

        List<SysLookupValuesDTO> lookupValueList = new LinkedList<>();
        SysLookupValuesDTO b1 = new SysLookupValuesDTO();
        b1.setLookupMeaning("HSF");
        b1.setAttribute1("2");
        lookupValueList.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_ISLEAD))
                .thenReturn(lookupValueList);

        List<SysLookupValuesDTO> feederFlagList = new LinkedList<>();
        SysLookupValuesDTO c1 = new SysLookupValuesDTO();
        c1.setAttribute1("Y");
        c1.setLookupCode(new BigDecimal("10320001"));
        feederFlagList.add(c1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(MpConstant.LOOKUP_TYPE_FEEDER_SWITCH))
                .thenReturn(feederFlagList)
        ;

        List<SmtMachineMaterialPrepare> prepareList = new LinkedList<>();
        SmtMachineMaterialPrepare d1 = new SmtMachineMaterialPrepare();
        prepareList.add(d1);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.any()))
                .thenReturn(prepareList)
        ;

        List<SmtLocationInfoDTO> listSmtLocation = new LinkedList<>();
        SmtLocationInfoDTO e1 = new SmtLocationInfoDTO();
        e1.setLocationType("1");
        listSmtLocation.add(e1);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(listSmtLocation);

        Assert.assertNotNull(imesPDACommonServiceImpl.pdaTransferScan(entity));
    }

	@Test
	public void checkAvl() throws Exception{
		PDATransferScanDTO entity= new PDATransferScanDTO();
		entity.setFormFlag(new BigDecimal("2"));
		PkCodeInfo newPkCodeObj = new PkCodeInfo();
		String remark = "";
		Assert.assertNotNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkAvl", entity ,newPkCodeObj,remark));
		entity.setFormFlag(new BigDecimal("0"));
		ServiceData serviceData = new ServiceData();
		serviceData.setBo("");
		PowerMockito.when(avlService.getAvlDTOList(any()))
				.thenReturn(serviceData);
		Assert.assertNotNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkAvl", entity ,newPkCodeObj,remark));
	}

    @Test
    public void checkBothLineWorkOrderStatus() throws Exception{
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CommonUtils.class);
        PDATransferScanDTO entity = new PDATransferScanDTO();
        entity.setWorkOrder("8899515-SMT-5201");
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setSourceTask("8899515");
        psWorkOrderBasic.setWorkOrderStatus(Constant.DONE);
        psWorkOrderBasic.setWorkOrderNo("8899515-SMT-5201");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        Assert.assertNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkBothLineWorkOrderStatus", entity));
        PsWorkOrderBasic psWorkOrderBasic2 = new PsWorkOrderBasic();
        psWorkOrderBasic2.setSourceTask("8899515");
        psWorkOrderBasic2.setWorkOrderStatus(Constant.DONE);
        psWorkOrderBasic2.setWorkOrderNo("8899515-SMTA-5202");
        entity.setWorkOrder2("8899515-SMTA-5202");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic2);
        Assert.assertNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkBothLineWorkOrderStatus", entity));
        PDATransferScanDTO entity1 = new PDATransferScanDTO();
        entity1.setWorkOrder2("8899515-SMT-5201");
        Assert.assertNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkBothLineWorkOrderStatus", entity1));
        PsWorkOrderBasic psWorkOrderBasic3 = new PsWorkOrderBasic();
        psWorkOrderBasic3.setSourceTask("8899515");
        psWorkOrderBasic3.setWorkOrderStatus(Constant.SUBMITTED);
        psWorkOrderBasic3.setWorkOrderNo("8899515-SMT-5201");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic3);
        Assert.assertNotNull(Whitebox.invokeMethod(imesPDACommonServiceImpl, "checkBothLineWorkOrderStatus", entity1));
    }
}
