package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.impl.AssemblyRelaScanServiceImpl;
import com.zte.application.impl.ProdBindingSettingServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BomNoBindListDTO;
import com.zte.interfaces.dto.BomNoBindListParamsDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.dto.ProdBindSettingDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.StItemMessage;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.machine.MachineSnBindingDTO;
import com.zte.interfaces.dto.machine.MachineSnBindingSubDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.aop.framework.AopContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.MpConstant.PROCESS_X_TYPE_P;
import static com.zte.common.utils.MpConstant.PROCESS_X_TYPE_S;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class,
        PlanscheduleRemoteService.class, AopContext.class, BasicsettingRemoteService.class, HttpRemoteUtil.class,MESHttpHelper.class,
        MicroServiceDiscoveryInvoker.class, CommonUtils.class,CenterfactoryRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class})
public class ProdBindingSettingServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ProdBindingSettingServiceImpl service;
    @Mock
    private ProdBindingSettingRepository repository;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private DefaultRedisScript<Long> redisScript;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, String> redisOpsValue;
    @Mock
    private ClassPathResource classPathResource;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
    }

    @Test
    public void getProdBindingSettingDTOList() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        service.getProdBindingSettingDTOList(dto);
        verify(repository, times(1)).getProdBindingSettingDTOList(dto);
    }

    @Test
    public void getProdBindingSettingList() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setProcessCode("11");
        dto.setMainSn("123");
        dto.setProdPlanId("");
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        bindingList.add(dto);
        PowerMockito.when(centerfactoryRemoteService.postNeedBindList(any())).thenReturn(bindingList);
        List<PsTask> psTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setTaskNo("tasknotaksmno-1");
        a1.setItemNo("123");
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);

        List<WipExtendIdentification> wipBindList = new LinkedList<>();
        WipExtendIdentification c1 = new WipExtendIdentification();
        wipBindList.add(c1);
        PowerMockito.when(wipExtendIdentificationRepository.getBindCntList(any())).thenReturn(wipBindList);
        PowerMockito.when(centerfactoryRemoteService.postNeedBindSectionList(any())).thenReturn(bindingList);
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class, CommonUtils.class,MicroServiceRestUtil.class,
                JacksonJsonConverUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("2213123");
                }})
        );
        ProdBindingSettingDTO prodBindingSettingDTO1 = new ProdBindingSettingDTO();
        prodBindingSettingDTO1.setProcessCode("11");
        prodBindingSettingDTO1.setProdPlanId("123");
        List<ProdBindingSettingDTO> bindingList1 = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.postNeedBindSectionList(any())).thenReturn(bindingList1);
        service.getProdBindingSettingList(dto);
        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("11");
        List<WipExtendIdentification> list = Arrays.asList(wipExtendIdentification);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getWipExtendIdentification", list, "11"));
        psTasks.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        dto.setProdPlanId("123");
        bindingList1.add(prodBindingSettingDTO1);
        PowerMockito.when(centerfactoryRemoteService.postNeedBindSectionList(any())).thenReturn(bindingList1);
        service.getProdBindingSettingList(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getWipExtendIdentification", list, "11"));
        prodBindingSettingDTO1.setMainProductCode("123");
        PowerMockito.when(centerfactoryRemoteService.postNeedBindSectionList(any())).thenReturn(bindingList1);
        a1.setPartsPlanno("123");
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        service.getProdBindingSettingList(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getWipExtendIdentification", list, "11"));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("[{\"bomChangeDetailId\":null,\"bomHeaderId\":null,\"itemCode\":null," +
                            "\"originalItemCode\":\"123\",\"usageCount\":null,\"changeId\":null,\"createBy\":null," +
                            "\"createDate\":null,\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2024-12-13 14:00:22\"," +
                            "\"enabledFlag\":null,\"factoryId\":null,\"productCode\":\"productCode\",\"originalProductCode\":\"org\"" +
                            ",\"prodplanId\":\"prodPlanId\",\"prodplanIdList\":null}]");
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("123");
        List<BProdBomChangeDetailDTO> prodBomChangeDetailDTOList = new ArrayList<>();
        BProdBomChangeDetailDTO dto2 = new BProdBomChangeDetailDTO();
        dto2.setProdplanId("prodPlanId");
        dto2.setLastUpdatedDate(new Date());
        dto2.setProductCode("productCode");
        dto2.setOriginalProductCode("org");
        prodBomChangeDetailDTOList.add(dto2);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any()))
                .thenReturn(prodBomChangeDetailDTOList);
        service.getProdBindingSettingList(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getWipExtendIdentification", list, "11"));


    }

    @Test
    public void getPartialProdBindingSettingList() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setProcessCode("11");
        dto.setMainSn("123");
        dto.setProdPlanId("7776119");
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        bindingList.add(dto);
        PowerMockito.when(centerfactoryRemoteService.postNeedBindSectionList(any())).thenReturn(bindingList);
        List<PsTask> psTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setPartsPlanno("123");
        a1.setItemNo("123");
        psTasks.add(a1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);

        List<WipExtendIdentification> wipBindList = new LinkedList<>();
        WipExtendIdentification c1 = new WipExtendIdentification();
        wipBindList.add(c1);
        PowerMockito.when(wipExtendIdentificationRepository.getBindCntList(any())).thenReturn(wipBindList);

        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("11");
        List<WipExtendIdentification> list = Arrays.asList(wipExtendIdentification);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getWipExtendIdentification", list, "11"));
    }

    @Test
    public void getBindList() throws Exception {
        BomNoBindListParamsDTO dto = new BomNoBindListParamsDTO();
        dto.setProcessName("QC抽检");
        dto.setWorkStationName("QC抽检");
        List<BSProcess> processList = null;
        PowerMockito.when(assemblyRelaScanService.getProcessInfo(any())).thenReturn(processList);
        List<BomNoBindListDTO> listDto = new ArrayList<>();
        BomNoBindListDTO bindListDTO = new BomNoBindListDTO();
        bindListDTO.setItemCode("321123123123");
        PowerMockito.when(repository.getBindList(any())).thenReturn(listDto);
        List<MtlRelatedItemsEntityDTO> list = new ArrayList<>();
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO1 = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO1.setItemCode("321123123123");
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(any(), Mockito.anyBoolean())).thenReturn(list);
        try {
            service.getBindList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BINDLIST_IS_NULL, e.getMessage());
        }
        listDto.add(bindListDTO);
        PowerMockito.when(repository.getBindList(any())).thenReturn(listDto);
        processList = new ArrayList<>();
        BSProcess bsProcess1 = new BSProcess();
        BSProcess bsProcess2 = new BSProcess();
        bsProcess1.setProcessName("QC抽检1");
        bsProcess2.setProcessName("QC抽检1");
        PowerMockito.when(assemblyRelaScanService.getProcessInfo(any())).thenReturn(processList);
        try {
            service.getBindList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_ITEM_INFO_FAIL, e.getMessage());
        }
        list.add(mtlRelatedItemsEntityDTO1);
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(any(), Mockito.anyBoolean())).thenReturn(list);
        processList.add(bsProcess1);
        processList.add(bsProcess2);
        PowerMockito.when(assemblyRelaScanService.getProcessInfo(any())).thenReturn(processList);
        bsProcess1.setProcessName("QC抽检");
        bsProcess1.setxType("子工序");
        bsProcess2.setProcessName("QC抽检");
        bsProcess2.setxType("工站");
        bindListDTO.setItemCode("123123123123");
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(any(), Mockito.anyBoolean())).thenReturn(list);
        service.getBindList(dto);
        processList.clear();
        BSProcess bsProcess3 = new BSProcess();
        processList.add(bsProcess3);
        service.getBindList(dto);
    }

    @Test
    public void submitProdBindSetting() {
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_IS_NULL, e.getMessage());
            System.out.println("1" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
            }});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.BIND_TYPE_IS_NULL, e.getMessage());
            System.out.println("2" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
            }});
        } catch (MesBusinessException e) {
            System.out.println("2" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setProcessCode("1");
                setWorkStation("1");
            }});
        } catch (MesBusinessException e) {
            System.out.println("3" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setProcessCode("1");
            }});
        } catch (MesBusinessException e) {
            System.out.println("4" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setProcessCode("1");
                setDtoList(Lists.newArrayList(new ProdBindingSettingDTO()));
            }});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EXISTS_WORK_STATION_SETTING, e.getMessage());
            System.out.println("5" + e.getMessage());
        }
        PowerMockito.when(repository.getCountBySettingType("1", PROCESS_X_TYPE_S, 1)).thenReturn(1);
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setProcessCode("1");
                setDtoList(Lists.newArrayList(new ProdBindingSettingDTO()));
            }});
        } catch (MesBusinessException e) {
            System.out.println("5.1" + e.getMessage());
        }
        PowerMockito.when(repository.getCountBySettingType("1", PROCESS_X_TYPE_P, 1)).thenReturn(1);
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setProcessCode("1");
                setWorkStation("1");
                setDtoList(Lists.newArrayList(new ProdBindingSettingDTO()));
            }});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EXISTS_PROCESS_SETTING, e.getMessage());
            System.out.println("6" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(1);
                setDtoList(Lists.newArrayList(new ProdBindingSettingDTO()));
            }});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_IS_NOT_NULL, e.getMessage());
            System.out.println("7" + e.getMessage());
        }
        try {
            service.submitProdBindSetting("1", new ProdBindSettingDTO() {{
                setProductCode("1");
                setBindType(2);
                setDtoList(Lists.newArrayList(new ProdBindingSettingDTO()));
            }});
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_IS_NOT_NULL, e.getMessage());
            System.out.println("7" + e.getMessage());
        }
    }

    @Test
    public void snBatchBinding() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setEmpNo("10275590");
        machineSnBindingDTO.setFactoryId("52");
        machineSnBindingDTO.setProcessCode("B");
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_EXIST, e.getMessage());
        }
        List<MachineSnBindingSubDTO> childList = new LinkedList<>();
        MachineSnBindingSubDTO a1 = new MachineSnBindingSubDTO();
        a1.setSn("789973100001");
        childList.add(a1);
        childList.add(a1);
        machineSnBindingDTO.setChildSnList(childList);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_REPEAT, e.getMessage());
        }
        childList.clear();
        childList.add(a1);
        PowerMockito.whenNew(DefaultRedisScript.class).withNoArguments().thenReturn(redisScript);
        PowerMockito.whenNew(ClassPathResource.class).withAnyArguments().thenReturn(classPathResource);
        PowerMockito.when(redisTemplate.execute(any(), any(), any()))
                .thenReturn(0L);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REDIS_LOCK_FAIL_MSG, e.getMessage());
        }
        PowerMockito.when(redisTemplate.execute(any(), any(), any()))
                .thenReturn(-1L);
        machineSnBindingDTO.setFormSn("789973000001");
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }

        MachineSnBindingSubDTO a2 = new MachineSnBindingSubDTO();
        a2.setSn("789973100002");
        childList.add(a2);
        MachineSnBindingSubDTO a3 = new MachineSnBindingSubDTO();
        a3.setSn("78997310000266");
        childList.add(a3);
        MachineSnBindingSubDTO a4 = new MachineSnBindingSubDTO();
        a4.setSn("78997310000267");
        childList.add(a4);
        MachineSnBindingSubDTO a5 = new MachineSnBindingSubDTO();
        a5.setSn("789973200002");
        childList.add(a5);
        MachineSnBindingSubDTO a6 = new MachineSnBindingSubDTO();
        a6.setSn("789973200003");
        childList.add(a6);
        List<PsWipInfo> wipInfoList = new LinkedList<>();
        PsWipInfo b1 = new PsWipInfo();
        b1.setSn("789973000001");
        b1.setAttribute1("7899730");
        b1.setItemNo("129206751186AJB");
        b1.setAssembleFlag("Y");
        wipInfoList.add(b1);
        PsWipInfo b2 = new PsWipInfo();
        b2.setSn("789973000003");
        b2.setWorkOrderNo("1244");
        b2.setAttribute1("7899730");
        b2.setItemNo("1");
        b2.setAssembleFlag("Y");
        wipInfoList.add(b2);
        PsWipInfo g1 = new PsWipInfo();
        g1.setSn("789973100001");
        g1.setItemNo("1");
        g1.setWorkOrderNo("1244");
        g1.setAttribute1("7899731");
        wipInfoList.add(g1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList()))
                .thenReturn(wipInfoList)
        ;
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, e.getMessage());
        }

        List<PsTask> psTasks = new LinkedList<>();
        PsTask c1 = new PsTask();
        c1.setPartsPlanno("1234");
        c1.setItemNo("129206751186AJB");
        psTasks.add(c1);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap()))
                .thenReturn(psTasks)
        ;

        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOUND_LIST_IS_NULL, e.getMessage());
        }

        List<ProdBindingSettingDTO> bindList = new LinkedList<>();
        ProdBindingSettingDTO d1 = new ProdBindingSettingDTO();
        d1.setUsageCount(new BigDecimal(4));
        d1.setItemCode("1");
        d1.setProductCode("129206751186AJB");
        d1.setProcessCode("B");
        bindList.add(d1);
        ProdBindingSettingDTO d2 = new ProdBindingSettingDTO();
        d2.setUsageCount(new BigDecimal(4));
        d2.setItemCode("2");
        d2.setProductCode("129206751186AJB");
        d2.setMainProductCode("129206751186AJB");
        d2.setProcessCode("B");
        bindList.add(d2);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(bindList);
        List<WipExtendIdentification> wipBindList = new LinkedList<>();
        WipExtendIdentification e1 = new WipExtendIdentification();
        e1.setFormQty(new BigDecimal(4));
        e1.setItemNo("1");
        wipBindList.add(e1);
        WipExtendIdentification e2 = new WipExtendIdentification();
        e2.setFormQty(new BigDecimal(4));
        e2.setItemNo("2");
        wipBindList.add(e2);
        PowerMockito.when(wipExtendIdentificationRepository.getBindCntList(any()))
                .thenReturn(wipBindList);
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("2213123");
                }})
        );
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_SN_IS_BINDED_COMPLETED, e.getMessage());
        }

        e1.setFormQty(new BigDecimal(1));
        e2.setFormQty(new BigDecimal(2));

        List<WipExtendIdentification> list = new LinkedList<>();
        WipExtendIdentification f1 = new WipExtendIdentification();
        f1.setSn("789973100002");
        list.add(f1);
        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(list)
        ;
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_BARCODE_HAVE_BIND_RELATION, e.getMessage());
        }

        PowerMockito.when(wipExtendIdentificationRepository.getList(any()))
                .thenReturn(null)
        ;
        List<PsTask> centerList = new LinkedList<>();
        PsTask h1 = new PsTask();
        h1.setProdplanId("7899732");
        h1.setItemNo("129206751186AJB2");
        h1.setTaskNo("gy");
        centerList.add(h1);
        PowerMockito.when(CenterfactoryRemoteService.selectPsTaskByProdIdSet(any()))
                .thenReturn(centerList)
        ;

        List<StItemMessage> itemMessages = new LinkedList<>();
        StItemMessage i1 = new StItemMessage();
        i1.setBomNo("129206751186AJB2");
        i1.setItemNo("1");
        i1.setSeriesNo("789973100002");
        itemMessages.add(i1);
        StItemMessage i2 = new StItemMessage();
        i2.setBomNo("129206751186AJB2");
        i2.setItemNo("12");
        i2.setSeriesNo("789973100001");
        itemMessages.add(i2);
        PowerMockito.when(datawbRemoteService.queryMaterialMessageBatch(any()))
                .thenReturn(itemMessages);
        PowerMockito.when(datawbRemoteService.queryItemNoAndNameBatch(any()))
                .thenReturn(itemMessages);

        List<BarcodeExpandDTO> barcodeExpandDTOList = new LinkedList<>();
        BarcodeExpandDTO j1 = new BarcodeExpandDTO();
        j1.setBarcode("78997310000266");
        j1.setItemCode("1");
        barcodeExpandDTOList.add(j1);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any()))
                .thenReturn(barcodeExpandDTOList);


        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_SN_NOT_BIND, e.getMessage());
        }

        BarcodeExpandDTO j2 = new BarcodeExpandDTO();
        j2.setBarcode("78997310000267");
        j2.setItemCode("1");
        barcodeExpandDTOList.add(j2);


        List<CtRouteDetailDTO> ctRouteDetailDTOList = new LinkedList<>();
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(any()))
                .thenReturn(ctRouteDetailDTOList)
        ;
        List<PsWorkOrderBasic> psWorkOrderBasics = new ArrayList<>();
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setRouteId("test123");
        psWorkOrderBasics.add(psWorkOrderBasic);
        PowerMockito.when(PlanscheduleRemoteService.getListByWorkOrderNos(anyMap())).thenReturn(psWorkOrderBasics);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ROUTE_DETAIL_LOST, e.getMessage());
        }

        CtRouteDetailDTO k1 = new CtRouteDetailDTO();
        k1.setNextProcess("456");
        ctRouteDetailDTOList.add(k1);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOKUPTYPE_1252_IS_NULL, e.getMessage());
        }

        List<SysLookupTypesDTO> sysLookupTypesDTOList = new LinkedList<>();
        SysLookupTypesDTO l1 = new SysLookupTypesDTO();
        l1.setLookupMeaning("Y");
        sysLookupTypesDTOList.add(l1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap()))
                .thenReturn(sysLookupTypesDTOList)
        ;

        g1.setAssembleFlag(Constant.FLAG_Y);
        g1.setSecAssembleFlag(Constant.FLAG_Y);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_BIND_OVER, e.getMessage());
        }

        d1.setUsageCount(new BigDecimal(7));
        d2.setUsageCount(new BigDecimal(20));
        PowerMockito.when(AopContext.currentProxy()).thenReturn(service);
        service.snBatchBinding(machineSnBindingDTO);
        g1.setAssembleFlag(Constant.FLAG_N);
        g1.setSecAssembleFlag(Constant.FLAG_Y);
        try {
            service.snBatchBinding(machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.AN_ASSEMBLY_IS_NOT_COMPLETED, e.getMessage());
        }
    }

    @Test
    public void addProcessNameTest() throws Exception {
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        ProdBindingSettingDTO dto1 = new ProdBindingSettingDTO();
        dto1.setProcessCode("code1");
        bindingList.add(dto1);


        List<BSProcess> bsProcessList = new ArrayList<>();

        PowerMockito.when(CrafttechRemoteService.getBsProcessList(any()))
                .thenReturn(bsProcessList);

        service.addProcessName(bindingList);

        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("code1");
        bsProcess.setProcessName("name1");
        bsProcessList.add(bsProcess);
        service.addProcessName(bindingList);
        Assert.assertTrue(bindingList.size() > 0);
    }

    @Test
    public void getUsageCountByProductCode() throws Exception {
        Map<String, Object> map = new HashMap<>(16);
        map.put(ProdBindingSetting.PRODUCT_CODE, "");
        List<ProdBindingSetting> dd = service.getUsageCountByProductCode(map);
        Assert.assertEquals(new ArrayList<>(), dd);

        map = new HashMap<>(16);
        List<ProdBindingSetting> list = new ArrayList<>();
        PowerMockito.when(repository.getUsageCountByProductCode(anyMap()))
                .thenReturn(list);
        map.put(ProdBindingSetting.PRODUCT_CODE, "33");
        List<ProdBindingSetting> dv = service.getUsageCountByProductCode(map);
        Assert.assertEquals(new ArrayList<>(), dv);


        map = new HashMap<>(16);
        list = new ArrayList<>();
        ProdBindingSetting model = new ProdBindingSetting();
        list.add(model);
        PowerMockito.when(repository.getUsageCountByProductCode(anyMap()))
                .thenReturn(list);
        map.put(ProdBindingSetting.PRODUCT_CODE, "33");
        dv = service.getUsageCountByProductCode(map);
        Assert.assertEquals(list, dv);

    }


    @Test
    public void testQueryBindingList_EmptyPsTaskList() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_TASK_INFO_BY_PRODPLANID, e.getMessage());
        }
    }

    @Test
    public void testQueryBindingList_BindingListNull() throws Exception {
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOUND_LIST_IS_NULL, e.getMessage());
        }
    }

    @Test
    public void testQueryBindingList_NoProcessMatch() throws Exception {
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        List<ProdBindingSettingDTO> bindList = new LinkedList<>();
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(bindList);
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOUND_LIST_IS_NULL, e.getMessage());
        }
    }

    @Test
    public void testQueryBindingList_AllBound() throws Exception {
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceRestUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("");
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        List<ProdBindingSettingDTO> bindingList = Collections.singletonList(new ProdBindingSettingDTO());
        bindingList.get(0).setProcessCode("processCode");
        bindingList.get(0).setBoundNo(BigDecimal.ONE);
        bindingList.get(0).setUsageCount(BigDecimal.ONE);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(bindingList);
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
            Assert.assertEquals(bindingList.get(0).getProcessCode(),"processCode");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_SN_IS_BINDED_COMPLETED, e.getMessage());
        }
    }

    @Test
    public void testQueryBindingList_Success() throws Exception {
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceRestUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("2213123");
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn(null);
        List<ProdBindingSettingDTO> bindingList = Collections.singletonList(new ProdBindingSettingDTO());
        bindingList.get(0).setProcessCode("processCode");
        bindingList.get(0).setBoundNo(BigDecimal.ZERO);
        bindingList.get(0).setUsageCount(BigDecimal.ZERO);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(bindingList);
        when(wipExtendIdentificationRepository.getBindCntList(anyString())).thenReturn(Collections.emptyList());
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_SN_IS_BINDED_COMPLETED, e.getMessage());
        }
    }

    @Test
    public void testQueryBindingList_WithWipBindList() throws Exception {
        MachineSnBindingDTO machineSnBindingDTO = new MachineSnBindingDTO();
        machineSnBindingDTO.setProductCode("productCode");
        machineSnBindingDTO.setProdplanId("prodplanId");
        machineSnBindingDTO.setProcessCode("processCode");
        machineSnBindingDTO.setFormSn("formSn");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("123");
        psTasks.add(psTask);
        PowerMockito.mockStatic(BasicsettingRemoteService.class, ConstantInterface.class, CrafttechRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class, CommonUtils.class,MicroServiceRestUtil.class,
                JacksonJsonConverUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTasks);
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProcessCode("processCode");
        prodBindingSettingDTO.setItemCode("itemCode");
        prodBindingSettingDTO.setBoundNo(BigDecimal.ZERO);
        prodBindingSettingDTO.setUsageCount(BigDecimal.ZERO);
        bindingList.add(prodBindingSettingDTO);
        PowerMockito.when(centerfactoryRemoteService.queryProdBindingBatch(any()))
                .thenReturn(bindingList);

        WipExtendIdentification wipExtendIdentification = new WipExtendIdentification();
        wipExtendIdentification.setItemNo("itemCode");
        wipExtendIdentification.setFormQty(BigDecimal.ONE);
        when(wipExtendIdentificationRepository.getBindCntList(anyString())).thenReturn(Collections.singletonList(wipExtendIdentification));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("BProdBomChangeDetailCtrl/queryBProdBomDetailChangeList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("[{\"bomChangeDetailId\":null,\"bomHeaderId\":null,\"itemCode\":null," +
                            "\"originalItemCode\":\"123\",\"usageCount\":null,\"changeId\":null,\"createBy\":null," +
                            "\"createDate\":null,\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2024-12-13 14:00:22\"," +
                            "\"enabledFlag\":null,\"factoryId\":null,\"productCode\":\"productCode\",\"originalProductCode\":\"org\"" +
                            ",\"prodplanId\":\"prodPlanId\",\"prodplanIdList\":null}]");
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("123");
        List<BProdBomChangeDetailDTO> prodBomChangeDetailDTOList = new ArrayList<>();
        BProdBomChangeDetailDTO dto = new BProdBomChangeDetailDTO();
        dto.setProdplanId("prodPlanId");
        dto.setLastUpdatedDate(new Date());
        dto.setProductCode("productCode");
        dto.setOriginalProductCode("org");
        prodBomChangeDetailDTOList.add(dto);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any()))
                .thenReturn(prodBomChangeDetailDTOList);
        try {
            Whitebox.invokeMethod(service, "queryBindingList", machineSnBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAIN_SN_IS_BINDED_COMPLETED, e.getMessage());
        }
    }


    @Test
    public void itemReplace() {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> bomDetailDTOS = new ArrayList<>();

        try {
            Whitebox.invokeMethod(service, "itemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        resultList.add(prodBindingSettingDTO);
        try {
            Whitebox.invokeMethod(service, "itemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 1);
        resultList.clear();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bomDetailDTOS.add(bProdBomChangeDetailDTO);
        try {
            Whitebox.invokeMethod(service, "itemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);
        // 集合不为空
        bProdBomChangeDetailDTO.setOriginalProductCode("orig");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        prodBindingSettingDTO.setLastUpdatedDate(new Date());
        prodBindingSettingDTO.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO.setItemCode("orig");
        resultList.add(prodBindingSettingDTO);
        ProdBindingSettingDTO prodBindingSettingDTO2 = new ProdBindingSettingDTO();
        prodBindingSettingDTO2.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO2.setItemCode("orig");
        prodBindingSettingDTO2.setLastUpdatedDate(new Date());

        resultList.add(prodBindingSettingDTO2);
        try {
            Whitebox.invokeMethod(service, "itemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size()>0 );
    }

    @Test
    public void oneItemReplace() {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> bomDetailDTOS = new ArrayList<>();
        MachineSnBindingSubDTO machineSnBindingSubDTO = new MachineSnBindingSubDTO();
        machineSnBindingSubDTO.setProdplanId("1234567");
        machineSnBindingSubDTO.setSubItemNo("subitemno");
        try {
            Whitebox.invokeMethod(service, "oneItemReplace", resultList,bomDetailDTOS,machineSnBindingSubDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);

        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("orig");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        bProdBomChangeDetailDTO.setProdplanId("7654321");
        bomDetailDTOS.add(bProdBomChangeDetailDTO);
        try {
            Whitebox.invokeMethod(service, "oneItemReplace", resultList,bomDetailDTOS,machineSnBindingSubDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);
        // resultList
        bProdBomChangeDetailDTO.setProdplanId("1234567");
        prodBindingSettingDTO.setLastUpdatedDate(new Date());
        prodBindingSettingDTO.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO.setItemCode("orig");
        prodBindingSettingDTO.setProductCode("subitemno");
        resultList.add(prodBindingSettingDTO);
        ProdBindingSettingDTO prodBindingSettingDTO2 = new ProdBindingSettingDTO();
        prodBindingSettingDTO2.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO2.setItemCode("orig");
        prodBindingSettingDTO2.setLastUpdatedDate(new Date());
        resultList.add(prodBindingSettingDTO2);
        try {
            Whitebox.invokeMethod(service, "oneItemReplace", resultList,bomDetailDTOS,machineSnBindingSubDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size()>0 );
    }



    @Test
    public void sonItemReplace() {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> bomDetailDTOS = new ArrayList<>();
        MachineSnBindingSubDTO machineSnBindingSubDTO = new MachineSnBindingSubDTO();
        machineSnBindingSubDTO.setProdplanId("1234567");
        machineSnBindingSubDTO.setSubItemNo("subitemno");
        try {
            Whitebox.invokeMethod(service, "sonItemReplace", resultList,bomDetailDTOS,machineSnBindingSubDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);

        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("orig");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        bProdBomChangeDetailDTO.setProdplanId("7654321");
        bomDetailDTOS.add(bProdBomChangeDetailDTO);
        // resultList
        bProdBomChangeDetailDTO.setProdplanId("1234567");
        prodBindingSettingDTO.setLastUpdatedDate(new Date());
        prodBindingSettingDTO.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO.setItemCode("orig");
        prodBindingSettingDTO.setProductCode("subitemno");
        resultList.add(prodBindingSettingDTO);
        ProdBindingSettingDTO prodBindingSettingDTO2 = new ProdBindingSettingDTO();
        prodBindingSettingDTO2.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO2.setItemCode("orig");
        prodBindingSettingDTO2.setLastUpdatedDate(new Date());
        resultList.add(prodBindingSettingDTO2);
        try {
            Whitebox.invokeMethod(service, "sonItemReplace", resultList,bomDetailDTOS,machineSnBindingSubDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size()>0 );
    }


    @Test
    public void mainItemReplace() {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> bomDetailDTOS = new ArrayList<>();
        try {
            Whitebox.invokeMethod(service, "mainItemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size() == 0);

        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("orig");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        bProdBomChangeDetailDTO.setProdplanId("7654321");
        bomDetailDTOS.add(bProdBomChangeDetailDTO);
        // resultList
        bProdBomChangeDetailDTO.setProdplanId("1234567");
        prodBindingSettingDTO.setLastUpdatedDate(new Date());
        prodBindingSettingDTO.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO.setItemCode("orig");
        prodBindingSettingDTO.setProductCode("subitemno");
        resultList.add(prodBindingSettingDTO);
        ProdBindingSettingDTO prodBindingSettingDTO2 = new ProdBindingSettingDTO();
        prodBindingSettingDTO2.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO2.setItemCode("orig");
        prodBindingSettingDTO2.setLastUpdatedDate(new Date());
        resultList.add(prodBindingSettingDTO2);
        try {
            Whitebox.invokeMethod(service, "mainItemReplace", resultList,bomDetailDTOS);
        } catch (Exception e) {
        }
        Assert.assertTrue(resultList.size()>0 );
    }

    @Test
    public void renewBindingList() {
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> oneProdBomChangeDetailDTOS = new ArrayList<>();
        List<BProdBomChangeDetailDTO> twoProdBomChangeDetailDTOS = new ArrayList<>();
        List<ProdBindingSettingDTO> sonSettingDTO = new ArrayList<>();
        List<ProdBindingSettingDTO> mainSettingDTO = new ArrayList<>();

        try {
            Whitebox.invokeMethod(service, "renewBindingList",
                    bindingList, oneProdBomChangeDetailDTOS, twoProdBomChangeDetailDTOS, sonSettingDTO, mainSettingDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(bindingList.size() == 0);
        oneProdBomChangeDetailDTOS.add(new BProdBomChangeDetailDTO());
        try {
            Whitebox.invokeMethod(service, "renewBindingList",
                    bindingList, oneProdBomChangeDetailDTOS, twoProdBomChangeDetailDTOS, sonSettingDTO, mainSettingDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(bindingList.size() == 0);
        twoProdBomChangeDetailDTOS.add(new BProdBomChangeDetailDTO());
        try {
            Whitebox.invokeMethod(service, "renewBindingList",
                    bindingList, oneProdBomChangeDetailDTOS, twoProdBomChangeDetailDTOS, sonSettingDTO, mainSettingDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(bindingList.size() == 0);
        oneProdBomChangeDetailDTOS.clear();
        try {
            Whitebox.invokeMethod(service, "renewBindingList",
                    bindingList, oneProdBomChangeDetailDTOS, twoProdBomChangeDetailDTOS, sonSettingDTO, mainSettingDTO);
        } catch (Exception e) {
        }
        Assert.assertTrue(bindingList.size() == 0);
    }

    @Test
    public void queryBProdBomDetailChangeList() {
        List<BProdBomChangeDetailDTO> detailDTOS = Collections.emptyList();
        try {
            detailDTOS = Whitebox.invokeMethod(service, "queryBProdBomDetailChangeList",
                    Collections.emptyList());
        } catch (Exception e) {
        }
        Assert.assertTrue(detailDTOS.size() == 0);
    }
}
