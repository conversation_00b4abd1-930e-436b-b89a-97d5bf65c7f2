package com.zte.test;

import com.zte.application.BSmtBomDetailService;
import com.zte.application.ParentsnAndSnService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.WipScanHistoryService;
import com.zte.application.impl.SmtTraceServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtSnMtlTracingT;
import com.zte.domain.model.SmtSnMtlTracingTRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.EmPcbItemInfoDetailDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.TraceCalDTO;
import com.zte.interfaces.dto.TraceFixDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@PrepareForTest({BasicsettingRemoteService.class, EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class, ObtainRemoteServiceDataUtil.class})
public class SmtTraceServiceImplTest extends PowerBaseTestCase {
	@InjectMocks
	private SmtTraceServiceImpl service;

	@Mock
	private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
	@Mock
	private BSmtBomDetailService bSmtBomDetailService;
	@Mock
	private SmtSnMtlTracingTRepository smtSnMtlTracingTRepository;
	@Mock
	private EqpmgmtsRemoteService eqpmgmtsRemoteService;
	@Mock
	private StringRedisTemplate redisTemplate;
	@Mock
	private SmtSnMtlTracingTService smtSnMtlTracingTService;
	@Mock
	private PkCodeInfoService pkCodeInfoService;
	@Mock
	private ParentsnAndSnService parentsnAndSnService;
	@Mock
	private PsScanHistoryService psScanHistoryService;
	@Mock
	private WipScanHistoryService wipScanHistoryService;
	@Mock
	private ValueOperations<String, String> redisOpsValue;

	@Test
	public void traceCal() {
		PowerMockito.mockStatic(BasicsettingRemoteService.class, EqpmgmtsRemoteService.class, PlanscheduleRemoteService.class, ObtainRemoteServiceDataUtil.class);
		TraceCalDTO traceCalDTO = new TraceCalDTO();
		traceCalDTO.setFactoryId("53");
		traceCalDTO.setCalInMachine(true);
		traceCalDTO.setMachineNo("(X4S-1)");
		traceCalDTO.setWorkOrderNo("7777666-SMT-B5201");
		traceCalDTO.setIsEndBoard(true);
		traceCalDTO.setLineCode("SMT-4");
		PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
		PowerMockito.when(redisOpsValue.get(Mockito.any())).thenReturn(null);
		PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(null);
		PowerMockito.when(smtMachineMaterialMoutingService.getMoutingWithPkCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyList())).thenReturn(null);
		service.traceCal(traceCalDTO);

		PowerMockito.when(redisOpsValue.get(Mockito.any()))
				.thenReturn(Constant.VIRTUAL_PREFIX + Constant.V_MACHINE_NO);
		List<SmtMachineMaterialMouting> moutingList = new ArrayList<>();
		SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(X4S-1)");
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(X4S-1)");
		smtMachineMaterialMouting.setLocationNo("1-2-1");
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(X4S-1)");
		smtMachineMaterialMouting.setLocationNo("1-2-1");
		smtMachineMaterialMouting.setObjectId("ZTE001");
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(XXX-1)");
		smtMachineMaterialMouting.setLocationNo("1-2-1");
		smtMachineMaterialMouting.setObjectId("ZTE001");
		smtMachineMaterialMouting.setQty(BigDecimal.ONE);
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(X4S-1)");
		smtMachineMaterialMouting.setLocationNo("1-2-1");
		smtMachineMaterialMouting.setObjectId("ZTE001");
		smtMachineMaterialMouting.setItemCode("046030200050");
		smtMachineMaterialMouting.setQty(new BigDecimal("1000"));
		moutingList.add(smtMachineMaterialMouting);
		smtMachineMaterialMouting = new SmtMachineMaterialMouting();
		smtMachineMaterialMouting.setMachineNo("(X4S-1)");
		smtMachineMaterialMouting.setLocationNo("1-2-2");
		smtMachineMaterialMouting.setObjectId("ZTE002");
		smtMachineMaterialMouting.setItemCode("046050200038");
		smtMachineMaterialMouting.setQty(new BigDecimal("2"));
		smtMachineMaterialMouting.setNextReelRowid("ZTE003");
		moutingList.add(smtMachineMaterialMouting);
		PowerMockito.when(smtMachineMaterialMoutingService.getMoutingWithPkCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyList())).thenReturn(moutingList);
		PsWorkOrderDTO workOrder = new PsWorkOrderDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(Mockito.anyString())).thenReturn(workOrder);
		service.traceCal(traceCalDTO);

		workOrder.setCfgHeaderId("123");
		workOrder.setProdplanId("7777666");
		workOrder.setItemNo("123456789012ABC");
		List<BSmtBomDetail> bomDetailList = new ArrayList<>();
		PowerMockito.when(bSmtBomDetailService.getLocationAndQty(Mockito.anyString(), Mockito.any())).thenReturn(bomDetailList);
		service.traceCal(traceCalDTO);
		BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
		bSmtBomDetail.setVirtualFlag("Y");
		bSmtBomDetail.setQty(BigDecimal.ONE);
		bSmtBomDetail.setMachineNo("(X4S-1)");
		bSmtBomDetail.setLocationNo("1-1-1");
		bomDetailList.add(bSmtBomDetail);
		bSmtBomDetail = new BSmtBomDetail();
		bSmtBomDetail.setQty(BigDecimal.TEN);
		bSmtBomDetail.setMachineNo("(X4S-1)");
		bSmtBomDetail.setLocationNo("1-2-1");
		bomDetailList.add(bSmtBomDetail);
		bSmtBomDetail = new BSmtBomDetail();
		bomDetailList.add(bSmtBomDetail);
		bSmtBomDetail = new BSmtBomDetail();
		bSmtBomDetail.setQty(BigDecimal.TEN);
		bSmtBomDetail.setMachineNo("(X4S-1)");
		bSmtBomDetail.setLocationNo("1-2-2");
		bomDetailList.add(bSmtBomDetail);
		List<String> trayLocations = new ArrayList<>();
		trayLocations.add("(X4S-1)--1-1-1");
		PowerMockito.when(BasicsettingRemoteService.getListByLocationType(Mockito.any())).thenReturn(trayLocations);
		List<EmEqpPdcountDTO> pdCountList = new ArrayList<>();
		EmEqpPdcountDTO emEqpPdcountDTO = new EmEqpPdcountDTO();
		emEqpPdcountDTO.setFullStationPosition("(X4S-1)1-2-1");
		emEqpPdcountDTO.setMaterialCode("046030200050");
		emEqpPdcountDTO.setThrowsNumber(new BigDecimal("6"));
		pdCountList.add(emEqpPdcountDTO);
		PowerMockito.when(eqpmgmtsRemoteService.getEpqPdcountInfo(Mockito.any())).thenReturn(pdCountList);
		PkCodeInfo pkCodeInfo= new PkCodeInfo();
		pkCodeInfo.setItemQty(new BigDecimal("100"));
		PowerMockito.when(pkCodeInfoService.getPkCodeInfoByCode(Mockito.anyString())).thenReturn(pkCodeInfo);
		PowerMockito.when(EqpmgmtsRemoteService.batchUpdateEqpPdcountStatus(Mockito.anyList())).thenReturn(1);
		traceCalDTO.setSn("77776660001");
		traceCalDTO.setSubSn(false);
		service.traceCal(traceCalDTO);
		Assert.assertEquals("046030200050", emEqpPdcountDTO.getMaterialCode());

		traceCalDTO.setSn(null);
		service.traceCal(traceCalDTO);
		Assert.assertEquals("046030200050", emEqpPdcountDTO.getMaterialCode());

		traceCalDTO.setSeqId(123L);
		service.traceCal(traceCalDTO);
		Assert.assertEquals("046030200050", emEqpPdcountDTO.getMaterialCode());

		traceCalDTO.setSn("77776660001");
		traceCalDTO.setSubSn(true);
		traceCalDTO.setTraceSource("SPI");
		service.traceCal(traceCalDTO);
		Assert.assertEquals("046030200050", emEqpPdcountDTO.getMaterialCode());

		PowerMockito.when(psScanHistoryService.getParentSnBySnAndSourceSys(Mockito.anyString(), Mockito.anyString())).thenReturn("P77776660001");
		service.traceCal(traceCalDTO);
		Assert.assertEquals("046030200050", emEqpPdcountDTO.getMaterialCode());
	}

	@Test
	public void traceFix() {
		TraceFixDTO traceFixDTO = new TraceFixDTO();
		service.traceFix(traceFixDTO);

		traceFixDTO.setSn("777766600001");
		service.traceFix(traceFixDTO);

		traceFixDTO.setSeqId(1L);
		service.traceFix(traceFixDTO);

		traceFixDTO.setSubSn(true);
		traceFixDTO.setSpiWithPmj(false);
		PowerMockito.when(psScanHistoryService.getParentSnBySnAndSourceSys(Mockito.anyString(), Mockito.anyString())).thenReturn("P777766600001");
		service.traceFix(traceFixDTO);

		traceFixDTO.setSpiWithPmj(true);
		service.traceFix(traceFixDTO);
		Assert.assertEquals(null, traceFixDTO.getSn());
	}

	@Test
	public void smtTraceCal() throws Exception {
		TraceCalDTO traceCalDTO = new TraceCalDTO();
		traceCalDTO.setEventType("1111");
		int res1 = service.smtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res1);
		SmtTraceServiceImpl service1 = PowerMockito.spy(new SmtTraceServiceImpl());

		traceCalDTO.setEventType(Constant.SMT_TRACE_PRODSTART);
		PowerMockito.doReturn(1).when(service1, "startSmtTraceCal", traceCalDTO);
		int res2 = service1.smtTraceCal(traceCalDTO);
		Assert.assertEquals(1, res2);

		traceCalDTO.setEventType(Constant.SMT_TRACE_PRODEND);
		PowerMockito.doReturn(2).when(service1, "endSmtTraceCal", traceCalDTO);
		int res3 = service1.smtTraceCal(traceCalDTO);
		Assert.assertEquals(2, res3);
	}


	@Test
	public void startSmtTraceCal() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);

		TraceCalDTO traceCalDTO = new TraceCalDTO();
		traceCalDTO.setWorkOrderNo("WorkOrderNo");
		traceCalDTO.setSn("sn1");
		traceCalDTO.setFactoryId("56");

		int res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		List<EmPcbItemInfoDetailDTO> pcbTtemDetails = new ArrayList<>();
		EmPcbItemInfoDetailDTO pcbItemInfoDetailDTO1 = new EmPcbItemInfoDetailDTO();
		pcbItemInfoDetailDTO1.setModuleNo("1");
		pcbItemInfoDetailDTO1.setLocationNo("1-9");
		pcbItemInfoDetailDTO1.setQty(500L);
		pcbItemInfoDetailDTO1.setReelId("7788");
		pcbTtemDetails.add(pcbItemInfoDetailDTO1);
		EmPcbItemInfoDetailDTO pcbItemInfoDetailDTO2 = new EmPcbItemInfoDetailDTO();
		pcbItemInfoDetailDTO2.setModuleNo("2");
		pcbItemInfoDetailDTO2.setLocationNo("5");
		pcbItemInfoDetailDTO2.setQty(500L);
		pcbItemInfoDetailDTO2.setReelId("7777");
		pcbTtemDetails.add(pcbItemInfoDetailDTO2);
		pcbItemInfoDetailDTO2.setModuleNo("2");
		pcbItemInfoDetailDTO2.setLocationNo("6");
		pcbItemInfoDetailDTO2.setQty(1L);
		pcbTtemDetails.add(pcbItemInfoDetailDTO2);
		traceCalDTO.setPcbTtemDetails(pcbTtemDetails);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(Mockito.anyString())).thenReturn(null);
		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		PsWorkOrderDTO workOrder = new PsWorkOrderDTO();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(Mockito.anyString())).thenReturn(workOrder);
		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		workOrder.setCfgHeaderId("CfgHeaderId");
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkerOrderInfo(Mockito.anyString())).thenReturn(workOrder);
		PowerMockito.when(parentsnAndSnService.getParentsn(Mockito.anyString())).thenReturn(null);
		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		PowerMockito.when(parentsnAndSnService.getParentsn(Mockito.anyString())).thenReturn("sn1");
		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		List<BSmtBomDetail> bomDetailList = new ArrayList<>();
		BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
		bSmtBomDetail1.setLocationNo("1-9");
		bSmtBomDetail1.setQty(BigDecimal.TEN);
		bomDetailList.add(bSmtBomDetail1);
		BSmtBomDetail bSmtBomDetail2 = new BSmtBomDetail();
		bSmtBomDetail2.setLocationNo("2-4");
		bSmtBomDetail2.setQty(BigDecimal.TEN);
		bomDetailList.add(bSmtBomDetail2);
		BSmtBomDetail bSmtBomDetail3 = new BSmtBomDetail();
		bSmtBomDetail3.setLocationNo("2-4");
		bSmtBomDetail3.setQty(null);
		bomDetailList.add(bSmtBomDetail3);
		BSmtBomDetail bSmtBomDetail4 = new BSmtBomDetail();
		bSmtBomDetail4.setLocationNo("2-6");
		bSmtBomDetail4.setQty(BigDecimal.TEN);
		bomDetailList.add(bSmtBomDetail4);
		List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
		PowerMockito.when(bSmtBomDetailService.getLocationAndQty(Mockito.anyString(), Mockito.any())).thenReturn(bomDetailList);
		PowerMockito.when(pkCodeInfoService.getList(Mockito.any())).thenReturn(pkCodeInfoList);
		PowerMockito.when(smtSnMtlTracingTRepository.insertBatch(Mockito.anyString(), Mockito.any())).thenReturn(1);
		PowerMockito.when(smtSnMtlTracingTService.insertBatch(Mockito.any())).thenReturn(2);
		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(2, res);

		PowerMockito.when(bSmtBomDetailService.getLocationAndQty(Mockito.anyString(), Mockito.any())).thenReturn(bomDetailList);
		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setPkCode("7788");
		pkCodeInfo.setSourceBatchCode("220");

		pkCodeInfoList.add(pkCodeInfo);
		PowerMockito.when(pkCodeInfoService.getList(Mockito.any())).thenReturn(pkCodeInfoList);


		PowerMockito.when(smtSnMtlTracingTRepository.insertBatch(Mockito.anyString(), Mockito.any())).thenReturn(1);
		PowerMockito.when(smtSnMtlTracingTService.insertBatch(Mockito.any())).thenReturn(2);

		res = service.startSmtTraceCal(traceCalDTO);
		Assert.assertEquals(2, res);
	}

	@Test
	public void checkPcbDetail() throws Exception {
		EmPcbItemInfoDetailDTO pcbDetail = new EmPcbItemInfoDetailDTO();
		Object checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));

		pcbDetail.setModuleNo("1");
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));

		pcbDetail.setLocationNo("9");
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));

		pcbDetail.setQty(5L);
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail);
		Assert.assertEquals(true, (Boolean) (checkPcbDetail));

		EmPcbItemInfoDetailDTO pcbDetail1 = new EmPcbItemInfoDetailDTO();
		pcbDetail1.setLocationNo("9");
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail1);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));

		pcbDetail1.setQty(5L);
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail1);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));

		EmPcbItemInfoDetailDTO pcbDetail2 = new EmPcbItemInfoDetailDTO();
		pcbDetail2.setQty(5L);
		checkPcbDetail = Whitebox.invokeMethod(service, "checkPcbDetail", pcbDetail2);
		Assert.assertEquals(false, (Boolean) (checkPcbDetail));
	}

	@Test
	public void endSmtTraceCal() throws Exception {
		TraceCalDTO traceCalDTO = new TraceCalDTO();
		traceCalDTO.setWorkOrderNo("WorkOrderNo");
		traceCalDTO.setSn("sn1");
		traceCalDTO.setFactoryId("56");
		int res = service.endSmtTraceCal(traceCalDTO);
		Assert.assertEquals(0, res);

		List<EmPcbItemInfoDetailDTO> pcbTtemDetails = new ArrayList<>();
		EmPcbItemInfoDetailDTO pcbItemInfoDetailDTO1 = new EmPcbItemInfoDetailDTO();
		pcbItemInfoDetailDTO1.setModuleNo("1");
		pcbItemInfoDetailDTO1.setLocationNo("1-9");
		pcbItemInfoDetailDTO1.setQty(500L);
		pcbItemInfoDetailDTO1.setReelId("7788");
		pcbTtemDetails.add(pcbItemInfoDetailDTO1);
		EmPcbItemInfoDetailDTO pcbItemInfoDetailDTO2 = new EmPcbItemInfoDetailDTO();
		pcbItemInfoDetailDTO2.setModuleNo("2");
		pcbItemInfoDetailDTO2.setLocationNo("5");
		pcbItemInfoDetailDTO2.setQty(500L);
		pcbTtemDetails.add(pcbItemInfoDetailDTO2);
		pcbItemInfoDetailDTO2.setModuleNo("2");
		pcbItemInfoDetailDTO2.setLocationNo("6");
		pcbItemInfoDetailDTO2.setQty(1L);
		pcbTtemDetails.add(pcbItemInfoDetailDTO2);
		traceCalDTO.setPcbTtemDetails(pcbTtemDetails);

		List<SmtSnMtlTracingT> endTempList = new ArrayList<>();
		SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
		smtSnMtlTracingT.setLocationNo("1-9");
		endTempList.add(smtSnMtlTracingT);
		PowerMockito.when(smtSnMtlTracingTRepository.getTracingProdend(Mockito.anyString())).thenReturn(endTempList);
		List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
		PowerMockito.when(pkCodeInfoService.getList(Mockito.any())).thenReturn(pkCodeInfoList);
		PowerMockito.when(smtSnMtlTracingTService.insertBatch(Mockito.any())).thenReturn(1);
		res = service.endSmtTraceCal(traceCalDTO);
		Assert.assertEquals(1, res);

		PkCodeInfo pkCodeInfo = new PkCodeInfo();
		pkCodeInfo.setPkCode("7788");
		pkCodeInfo.setSourceBatchCode("220");
		pkCodeInfoList.add(pkCodeInfo);
		PowerMockito.when(pkCodeInfoService.getList(Mockito.any())).thenReturn(pkCodeInfoList);
		PowerMockito.when(smtSnMtlTracingTService.insertBatch(Mockito.any())).thenReturn(1);
		res = service.endSmtTraceCal(traceCalDTO);
		Assert.assertEquals(1, res);
	}
}
