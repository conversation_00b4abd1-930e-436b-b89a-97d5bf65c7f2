package com.zte.test;


import com.zte.application.impl.WarehouseRequirementInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.doReturn;

@PrepareForTest({CommonUtils.class, ProductionDeliveryRemoteService.class})
public class WarehouseRequirementInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WarehouseRequirementInfoServiceImpl warehouseRequirementInfoServiceImpl;
    @Mock
    private WarehouseRequirementInfoRepository warehouseRequirementInfoRepository;
    @Mock
    private WarehouseRequirementDetailRepository warehouseRequirementDetailRepository;



    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void searchDetaiLInfoByHeadIdPage() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<WarehouseRequirementDetail> detailList = new ArrayList<>();
        WarehouseRequirementDetail detail = new WarehouseRequirementDetail();
        detail.setWarehouseCode("CS0121");
        detailList.add(detail);
        doReturn(detailList).when(warehouseRequirementDetailRepository).searchDetaiLInfoByHeadIdPage(Mockito.any());
        List<LinesideWarehouseInfo> warehouseNameList= new ArrayList<>();
        LinesideWarehouseInfo warehouseInfo = new LinesideWarehouseInfo();
        warehouseInfo.setWarehouseCode("CS0121");
        warehouseInfo.setWarehouseName("河源仓库02");
        warehouseNameList.add(warehouseInfo);
        PowerMockito.when(ProductionDeliveryRemoteService.queryWarehouseListByCode(Mockito.any())).thenReturn(warehouseNameList);
        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        Assert.assertNotNull(warehouseRequirementInfoServiceImpl.searchDetaiLInfoByHeadIdPage(null));
    }

    @Test
    public void queryRequirmentListPage() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        List<WarehouseRequirementInfo> detailList = new ArrayList<>();
        WarehouseRequirementInfo detail = new WarehouseRequirementInfo();
        detail.setWarehouseCode("CS0121");
        detailList.add(detail);
        doReturn(detailList).when(warehouseRequirementDetailRepository).searchDetaiLInfoByHeadIdPage(Mockito.any());
        List<LinesideWarehouseInfo> warehouseNameList= new ArrayList<>();
        LinesideWarehouseInfo warehouseInfo = new LinesideWarehouseInfo();
        warehouseInfo.setWarehouseCode("CS0121");
        warehouseInfo.setWarehouseName("河源仓库02");
        warehouseNameList.add(warehouseInfo);
        PowerMockito.when(ProductionDeliveryRemoteService.queryWarehouseListByCode(Mockito.any())).thenReturn(warehouseNameList);
        PmRepairInfoDTO dto = new PmRepairInfoDTO();
        Assert.assertNotNull(warehouseRequirementInfoServiceImpl.queryRequirmentListPage(null));
    }

    @Test
    public void selectByItemCodeAndLeadFlag() throws Exception {
        WarehouseRequirementInfo requirementInfo = new WarehouseRequirementInfo();
        try{
            warehouseRequirementInfoServiceImpl.selectByItemCodeAndLeadFlag(requirementInfo);
        }catch(Exception e){
            Assert.assertEquals(MessageId.REQUIREMENTINFO_NOT_ALLOW_EMPTY, e.getMessage());
        }
        requirementInfo.setItemCode("test");
        try{
            warehouseRequirementInfoServiceImpl.selectByItemCodeAndLeadFlag(requirementInfo);
        }catch(Exception e){
            Assert.assertEquals(MessageId.REQUIREMENTINFO_NOT_ALLOW_EMPTY, e.getMessage());
        }
        requirementInfo.setLeadFlag("10");
        PowerMockito.when(warehouseRequirementInfoRepository.selectByItemCodeAndLeadFlag(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(warehouseRequirementInfoServiceImpl.selectByItemCodeAndLeadFlag(requirementInfo));
        requirementInfo.setQty(10);
        PowerMockito.when(warehouseRequirementInfoRepository.selectByItemCodeAndLeadFlag(Mockito.any())).thenReturn(Collections.singletonList(requirementInfo));
        PowerMockito.when(warehouseRequirementInfoRepository.getWarehouseDetailQtyByBillNo(Mockito.any())).thenReturn(0);
        Assert.assertNotNull(warehouseRequirementInfoServiceImpl.selectByItemCodeAndLeadFlag(requirementInfo));
    }



}
