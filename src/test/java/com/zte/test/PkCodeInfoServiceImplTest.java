package com.zte.test;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.PkCodeInfoService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.StItemBarcodeService;
import com.zte.application.TaskMaterialIssueSeqService;
import com.zte.application.WorkorderOnlineService;
import com.zte.application.impl.PkCodeInfoServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.WorkorderOnline;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.EqpmgmtsRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.EmEqpPdcountDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.UpdatePkCodeQtyDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.anyMap;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
public class PkCodeInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PkCodeInfoServiceImpl pkCodeInfoServiceImpl;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private StItemBarcodeService stItemBarcodeService;

    @Mock
    TaskMaterialIssueSeqService taskMaterialIssueSeqService;
    @Mock
    private WorkorderOnlineService workorderOnlineService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;

    @Mock
    private EqpmgmtsRemoteService eqpmgmtsRemoteService;

    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test(expected = MesBusinessException.class)
    @PrepareForTest({PkCodeInfoServiceImpl.class, BasicsettingRemoteService.class, HttpRemoteService.class, HttpClientUtil.class})
    public void getPrintInfo() throws Exception {
        List<PkCodeInfoDTO> queryList = new ArrayList<>();
        PkCodeInfoDTO info1 = new PkCodeInfoDTO();
        info1.setLpn("111");
        info1.setLastUpdatedBy("10266925");
        info1.setPkCode("123456");
        queryList.add(info1);
        PowerMockito.mockStatic(HttpRemoteService.class);

        String getUrl = "/containerContentInfoCtrl/getContainerContentInfoList";
        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("code", RetCode.SUCCESS_CODE);
        jsonObj1.put("bo", queryList);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(jsonObj1.toJSONString());
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(json);


        List<PkCodeInfoDTO> printInfoList = new ArrayList<>();
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        printInfoList.add(pkCodeInfoDTO);
        String Url = "http://123.com";
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn(Url);


        PowerMockito.mockStatic(HttpClientUtil.class);
        JSONObject jsonObj = new JSONObject();
        RetCode retCode = new RetCode();
        retCode.setCode(RetCode.SUCCESS_CODE);
        jsonObj.put("code", retCode);
        jsonObj.put("bo", printInfoList);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(jsonObj.toJSONString());

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<BsPubHrvOrgId> list = new ArrayList<>();
        BsPubHrvOrgId bsPubHrvOrgId = new BsPubHrvOrgId();
        bsPubHrvOrgId.setUserName("姚伟");
        bsPubHrvOrgId.setUserId("10266925");
        list.add(bsPubHrvOrgId);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyList())).thenReturn(list);

        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        pkCodeInfoServiceImpl.getPrintInfo(dto);
    }

    @Test
    public void insertPkCodeInfoAutoTest() throws Exception {

        PowerMockito.when(pkCodeInfoRepository.insertPkCodeInfoAutoTest(Mockito.anyObject())).thenReturn(1);

        PkCodeInfo record = new PkCodeInfo();
        record.setProductTask("7000001");
        record.setEmpNo("6606000002");
        record.setFactoryId(new BigDecimal(52));
        record.setEntityId(new BigDecimal(2));
        Assert.assertEquals(1,pkCodeInfoServiceImpl.insertPkCodeInfoAutoTest(record));
    }

    @Test
    public void downExcel() throws Exception {
        List<PkCodeInfo> dataList = new ArrayList<>();
        PkCodeInfo info = new PkCodeInfo();
        info.setPkCode("12");
        dataList.add(info);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(dataList)).thenReturn(dataList);
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setBomItemQty(new BigDecimal(52));
        Assert.assertNotNull(pkCodeInfoServiceImpl.downExcel(dto));
    }

    @Test
    @PrepareForTest({EqpmgmtsRemoteService.class})
    public void updatePkCode() throws Exception {
        PsEntityPlanBasicDTO dto = new PsEntityPlanBasicDTO();

        List<WorkorderOnline> workorderOnlineList = new LinkedList<>();
        WorkorderOnline a1 = new WorkorderOnline();
        workorderOnlineList.add(a1);
        PowerMockito.when(workorderOnlineService.getlist(Mockito.any())).thenReturn(workorderOnlineList);

        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.any()))
                .thenReturn(Lists.newArrayList(new SmtMachineMaterialMouting(){{
                    setObjectId("1");
                    setMachineNo("1");
                    setLocationNo("1");
                    setItemCode("1");
                }},new SmtMachineMaterialMouting(){{
                    setObjectId("2");
                    setMachineNo("2");
                    setLocationNo("1");
                    setItemCode("2");
                }}));

        PowerMockito.mockStatic(EqpmgmtsRemoteService.class);

        PowerMockito.when(eqpmgmtsRemoteService.getEpqPdcountInfo(anyMap())).thenReturn(Lists.newArrayList(
                new EmEqpPdcountDTO(){{setFullStationPosition("11");setMaterialCode("1");setThrowsNumber(BigDecimal.ONE);}},
                new EmEqpPdcountDTO(){{setFullStationPosition("11");setMaterialCode("1");setThrowsNumber(BigDecimal.TEN);}},
                new EmEqpPdcountDTO(){{setFullStationPosition("21");setMaterialCode("2");setThrowsNumber(BigDecimal.ONE);}},
                new EmEqpPdcountDTO(){{setFullStationPosition("21");setMaterialCode("2");setThrowsNumber(BigDecimal.ONE);}}));

        Assert.assertNotNull(pkCodeInfoServiceImpl.updatePkCode(dto));
    }

    @Test
    public void batchUpdateQty() {
        pkCodeInfoServiceImpl.batchUpdateQty(null);

        List<UpdatePkCodeQtyDTO> updatePkCodeQtyList = new ArrayList<>();
        Assert.assertEquals(new ArrayList<>(),updatePkCodeQtyList);
        UpdatePkCodeQtyDTO updatePkCodeQtyDTO = new UpdatePkCodeQtyDTO();
        updatePkCodeQtyList.add(updatePkCodeQtyDTO);
        pkCodeInfoServiceImpl.batchUpdateQty(updatePkCodeQtyList);
    }

    @Test
    public void batchUpdateQtyTaskByPkCodes() {
        List<PkCodeInfo> needUpdateQtyPkCodeList = new ArrayList<>();
        int i = pkCodeInfoServiceImpl.batchUpdateQtyTaskByPkCodes(needUpdateQtyPkCodeList);
        Assert.assertEquals(0, i);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        needUpdateQtyPkCodeList.add(pkCodeInfo);
        i = pkCodeInfoServiceImpl.batchUpdateQtyTaskByPkCodes(needUpdateQtyPkCodeList);
        Assert.assertEquals(1, i);
    }

    /* Started by AICoder, pid:c9da5ec05f2393c85d5b6f10a3dc7c4a */
    @Test
    public void testGetRawQtySumBatchByProductTaskAndItemCode() {
        List<PkCodeInfoDTO> res = new ArrayList();
        PowerMockito.when(pkCodeInfoRepository.getRawQtySumBatchByProductTaskAndItemCode(Mockito.anyObject())).thenReturn(res);
        Assert.assertEquals(pkCodeInfoServiceImpl.getRawQtySumBatchByProductTaskAndItemCode(new ArrayList<>()),res);
    }
    /* Ended by AICoder, pid:c9da5ec05f2393c85d5b6f10a3dc7c4a */


}
