package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PsBarcodeControlInfoService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.WipRepairSnService;
import com.zte.application.WipTestRecodeService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipTestRecord;
import com.zte.interfaces.dto.FlowControlConditionDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

@PrepareForTest({CommonUtils.class,MicroServiceDiscoveryInvoker.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
public class PsWipInfoServiceImplFourTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl service;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WipTestRecodeService wipTestRecodeService;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private WipRepairSnService wipRepairSnService;

    @Mock
    private PsScanHistoryService psScanHistoryService;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Mock
    BsWorkTimeSectionService bsWorkTimeSectionService;

    @Mock
    PsOutputInfoService psOutputInfoService;

    @Mock
    PsBarcodeControlInfoService psBarcodeControlInfoService;

    @Test
    public void setScanSource() throws Exception {
        service.setSourceSys(new FlowControlConditionDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service.setSourceSys(new FlowControlConditionDTO(),new FlowControlInfoDTO());
    }
    @Test
    public void updateOrInsert() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        try {
            service.updateOrInsert(new FlowControlInfoDTO(){{setOperation(Constant.INSERT);}},
                    Lists.newArrayList(new FlowControlInfoDTO()), 1);
        } catch (Exception e){
            Assert.assertEquals("", e.getMessage());
        }
        try {
            PowerMockito.when(psWipInfoRepository.insertPsWipInfoBatch(any())).thenReturn(1);
            service.updateOrInsert(new FlowControlInfoDTO(){{setOperation(Constant.INSERT);}},
                    Lists.newArrayList(new FlowControlInfoDTO()), 1);
        } catch (Exception e){
            Assert.assertEquals("", e.getMessage());
        }

            service.updateOrInsert(new FlowControlInfoDTO(){{
                                       setCurrProcessCode("1");
                                       setWipInfo(new PsWipInfo(){{
                                           setCurrProcessCode("1");
                                           setOpeTimes(BigDecimal.ONE);
                                       }});
                                       setOperation(Constant.UPDATE);}},
                    Lists.newArrayList(new FlowControlInfoDTO()), 1);

    }

    @Test
    public void checkInputQty() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.IN_QTY_BIG_THAN_WO_QTY);
            service.checkInputQty(
                    1,
                    new FlowControlInfoDTO(){{setIsFirstProcess("Y");}},
                    5,1,1
            );
            service.checkInputQty(
                    1,
                    new FlowControlInfoDTO(){{setIsFirstProcess("Y");}},
                    1,1,1
            );
            service.checkInputQty(
                    1,
                    new FlowControlInfoDTO(){{setIsZLLastProcess("Y");}},
                    5,1,1
            );
        Assert.assertNotNull(service.checkInputQty(
                    1,
                    new FlowControlInfoDTO(){{setIsZLLastProcess("Y");}},
                    1,1,1
            ));
    }

    @Test
    public void isbUpdate() {
        service.isbUpdate(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                        setIsProcessControl("Y");
                    }});
        }});
        service.isbUpdate(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                        setIsProcessControl("Y");
                        setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    }});
                }});
        service.isbUpdate(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                        setIsProcessControl("N");
                    }});
                }});
        service.isbUpdate(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                        setIsProcessControl("N");
                        setWorkStation(MpConstant.PROCESS_TYPE_AUTOTEST);
                    }});
                }});
        Assert.assertFalse(service.isbUpdate(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                        setIsProcessControl("N");
                        setWorkStation("1");
                    }});
                }}));
    }

    @Test
    public void checkProcessTest() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE);

        try {
            service.checkProcessTest(new FlowControlInfoDTO(),
                    Lists.newArrayList(new FlowControlInfoDTO()), false,1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE, e.getMessage());
        }
        try {
            service.checkProcessTest(new FlowControlInfoDTO(){{
                                         setCurrProcessCode("1");
                                         setWipInfo(new PsWipInfo(){{
                                             setCurrProcessCode("1");
                                         }});}},
                    Lists.newArrayList(new FlowControlInfoDTO()), true,1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE, e.getMessage());
        }
        PowerMockito.when(psScanHistoryService.insertPsScanHistoryByScanBatch(any(), anyInt())).thenReturn(1);
        try {
            service.checkProcessTest(new FlowControlInfoDTO(),
                    Lists.newArrayList(new FlowControlInfoDTO()), false,1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            service.checkProcessTest(new FlowControlInfoDTO(){{
                                         setCurrProcessCode("1");
                                         setWipInfo(new PsWipInfo(){{
                                             setCurrProcessCode("1");
                                         }});}},
                    Lists.newArrayList(new FlowControlInfoDTO()), true, 1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE, e.getMessage());
        }
        PowerMockito.when(wipTestRecodeService.insertWipTestRecodeBatch(any(), anyInt())).thenReturn(1);
        try {
            service.checkProcessTest(new FlowControlInfoDTO(){{
                                         setCurrProcessCode("1");
                                         setWipInfo(new PsWipInfo(){{
                                             setCurrProcessCode("1");
                                         }});}},
                    Lists.newArrayList(new FlowControlInfoDTO()), true, 1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE, e.getMessage());
        }
    }

    @Test
    public void doLastProcess() throws Exception {
        ReflectionTestUtils.setField(service, "cleanMountingCraftSection", "DIP,背板");
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class,CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.CUSTOMIZE_MSG);
        try {
            service.doLastProcess(
                    1, new FlowControlInfoDTO(){{setDate(new Date());setDelMounting(true);}}, new PsEntityPlanBasicDTO(),
                    2,1
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList(new FlowControlInfoDTO() {{
                            setWorkOrderNo("1");
                        }}));
                    }}));
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
            service.doLastProcess(
                    1,  new FlowControlInfoDTO(){{
                        setDate(new Date());setDelMounting(false);
                        setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                            setCraftSection("SMT-A");
                        }});
                        setIsZLLastProcess("Y");}},
                     new PsEntityPlanBasicDTO(),
                    2,1
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        try {
            service.doLastProcess(
                    1,  new FlowControlInfoDTO(){{
                        setDate(new Date());setDelMounting(false);
                        setEntityPlanBasic(new PsEntityPlanBasicDTO(){{

                        }});
                        setIsZLLastProcess("Y");}},
                    new PsEntityPlanBasicDTO(),
                    2,1
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        try {
            service.doLastProcess(
                    1, new FlowControlInfoDTO(){{
                        setDate(new Date());setDelMounting(true);
                        setEntityPlanBasic(new PsEntityPlanBasicDTO(){{

                        }});
                        setIsZLLastProcess("Y");}},
                     new PsEntityPlanBasicDTO(),
                    2,1
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        try {
            service.doLastProcess(
                    1,  new FlowControlInfoDTO(){{
                        setDate(new Date());setDelMounting(true);
                        setEntityPlanBasic(new PsEntityPlanBasicDTO(){{

                        }});
                        setIsZLLastProcess("Y");}},
                    new PsEntityPlanBasicDTO(),
                    2,1
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
    }

    @Test
    public void doLastProcess1() throws Exception {
        FlowControlInfoDTO infoDTO = new FlowControlInfoDTO() {{
            setDate(new Date());
            setDelMounting(true);
            setEntityPlanBasic(new PsEntityPlanBasicDTO() {{

            }});
            setIsZLLastProcess("Y");
            setIsUnnecessary("Y");
        }};
        service.doLastProcess(1, infoDTO, new PsEntityPlanBasicDTO(), 2, 1);

        infoDTO.setIsDoOrNot("Y");
        Assert.assertEquals(Constant.STR_Y, infoDTO.getIsDoOrNot());
        service.doLastProcess(1, infoDTO, new PsEntityPlanBasicDTO(), 2, 1);

    }

    @Test
    public void doFirstProcess() throws Exception {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class,CommonUtils.class,MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE);
        try {
            service.doFirstProcess(
                    1,  new FlowControlInfoDTO(){{setDate(new Date());}}, new PsEntityPlanBasicDTO(),
                    2,true
            );
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

            PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                    .thenReturn(JSON.toJSONString(new ServiceData() {{
                        setBo(Lists.newArrayList(new FlowControlInfoDTO() {{
                            setWorkOrderNo("1");
                        }}));
                    }}));
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        try {
            service.doFirstProcess(1, new FlowControlInfoDTO() {{
                        setDate(new Date());
                        setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                            setCraftSection("DIP");
                        }});
                        setIsFirstProcess("Y");
                    }}, new PsEntityPlanBasicDTO(),
                    2, true
            );
        }catch (Exception e){
            Assert.assertEquals(MessageId.UPDATE_WORK_ORDER_FAILURE, e.getMessage());
        }

            service.setOpen(new FlowControlInfoDTO(){{
                                setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                                    setCraftSection("SMT-A");
                                }});
                                setIsFirstProcess("Y");}},
                    new Date(), new PsEntityPlanBasicDTO(), false);
            service.setOpen(new FlowControlInfoDTO(){{
                                setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                                    setCraftSection("DIP");
                                }});
                                setIsFirstProcess("Y");}},
                    new Date(), new PsEntityPlanBasicDTO(), false);
            service.setOpen(new FlowControlInfoDTO(){{
                                setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                                    setCraftSection("DIP");
                                }});
                                setIsFirstProcess("Y");}},
                    new Date(), new PsEntityPlanBasicDTO(), true);
        try {
            service.doFirstProcess(
                    1, new FlowControlInfoDTO() {{
                        setDate(new Date());
                        setEntityPlanBasic(new PsEntityPlanBasicDTO() {{
                            setCraftSection("DIP");
                        }});
                        setIsFirstProcess("Y");
                    }},
                    new PsEntityPlanBasicDTO(),
                    2, false
            );
        }catch (Exception e){
            Assert.assertEquals(MessageId.UPDATE_WORK_ORDER_FAILURE, e.getMessage());
        }
        try {
            service.doFirstProcess(
                    1, new FlowControlInfoDTO(){{
                        setDate(new Date());
                        setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                        }});
                        setIsFirstProcess("Y");}},
                      new PsEntityPlanBasicDTO(),
                    2,false
            );
    }catch (Exception e){
        Assert.assertEquals(MessageId.UPDATE_WORK_ORDER_FAILURE, e.getMessage());
    }
    }

    @Test
    public void addTestRecode() throws Exception {
        try {
            service.addTestRecode(
                    new FlowControlInfoDTO(){{
                        setEntityPlanBasic(new PsEntityPlanBasicDTO());
                    }},
                    new Date());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INSERT_WIP_TEST_RECORD_FAILURE, e.getMessage());
        }
        PowerMockito.when(wipTestRecodeService.insertWipTestRecodeSelective(any())).thenReturn(1);
        service.addTestRecode(
                new FlowControlInfoDTO(){{
                    setEntityPlanBasic(new PsEntityPlanBasicDTO());
                    setTestRecode(new WipTestRecord());
                    setaProcessCode("1");
                    setaWorkStation("1");
                    setaSourceSysName("1");
                }},
                new Date());
    }

    @Test
    public void doTestProcess() {
        try {
            service.doTestProcess(new FlowControlInfoDTO(){{
                setCurrProcessCode("1");
                setWipInfo(new PsWipInfo(){{
                    setCurrProcessCode("1");
                }});
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_WIP_SCAN_HISTORY_FAILURE, e.getMessage());
        }
        try {
            service.doTestProcess(new FlowControlInfoDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(psScanHistoryService.updatePsScanHistoryByScan(any())).thenReturn(1);
        PowerMockito.when(psScanHistoryService.insertPsScanHistoryByScan(any())).thenReturn(1);
        try {
            service.doTestProcess(new FlowControlInfoDTO(){{
                setCurrProcessCode("1");
                setWipInfo(new PsWipInfo(){{
                    setCurrProcessCode("1");
                }});
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            service.doTestProcess(new FlowControlInfoDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void updateOrInsertSingle() throws Exception{
        FlowControlInfoDTO infoDTO = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO dto = new PsEntityPlanBasicDTO();
        dto.setWorkOrderNo("11111");
        dto.setItemNo("11111");
        dto.setItemName("11111");
        dto.setRouteId("11111");

        infoDTO.setSn("11111");
        infoDTO.setEntityPlanBasic(dto);
        infoDTO.setWorkOrderNo("11111");
        infoDTO.setOperation(Constant.INSERT);
        try {
            service.updateOrInsert(infoDTO);
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.INSER_WIP_INFO_ERROR, returnMessage.getMessage());
        }

        PowerMockito.when(psWipInfoRepository.insertPsWipInfoSelective(any())).thenReturn(1);
        PowerMockito.when(psWipInfoRepository.updatePsWipInfoByScan(any())).thenReturn(1);

        service.updateOrInsert(infoDTO);
        infoDTO.setOperation(Constant.UPDATE);
        try {
            service.updateOrInsert(new FlowControlInfoDTO() {{
                setOperation(Constant.UPDATE);
                setCurrProcessCode("1");
                setWipInfo(new PsWipInfo() {{
                    setCurrProcessCode("1");
                    setOpeTimes(BigDecimal.ONE);
                }});
            }});
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.UPDATE_WIP_FAILURE, returnMessage.getMessage());
        }
    }

    @Test
    public void getListByBatchSnList() throws Exception {
        List<String> listSn = new LinkedList<>();
        listSn.add("123");

        List<PsWipInfoDTO> psWipInfoDTOList = new LinkedList<PsWipInfoDTO>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        psWipInfoDTOList.add(a1);
        PowerMockito.when(psWipInfoRepository.getListByBatchSnList(Mockito.any()))
       .thenReturn(psWipInfoDTOList);

        Assert.assertNotNull(service.getListByBatchSnList(listSn));
    }
    @Test
    public void checkCraftSectionTest() throws Exception {
        ReflectionTestUtils.setField(service, "cleanMountingCraftSection", "DIP,背板");
        boolean o = Whitebox.invokeMethod(service, "checkCraftSection", "test");
        Assert.assertFalse(o);
        boolean o1 = Whitebox.invokeMethod(service, "checkCraftSection", "DIP");
        Assert.assertTrue(o1);
    }

    @Test
    public void setCompleteTest() throws Exception {
        ReflectionTestUtils.setField(service, "cleanMountingCraftSection", "DIP,背板");
        int count = 1;
        FlowControlInfoDTO infoDTO = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO planBasicDTO = new PsEntityPlanBasicDTO();
        int workOrderQty = 2;
        int outputQty = 1;

        planBasicDTO.setCraftSection("DIP");
        planBasicDTO.setIsFirstProcess("Y");

        infoDTO.setEntityPlanBasic(planBasicDTO);
        service.setComplete(count, infoDTO, planBasicDTO, workOrderQty, outputQty);
        Assert.assertTrue(planBasicDTO.getIsFirstProcess() == null);
        planBasicDTO.setCraftSection("test");
        planBasicDTO.setIsFirstProcess("Y");
        service.setComplete(count, infoDTO, planBasicDTO, workOrderQty, outputQty);
        Assert.assertTrue("Y".equals(planBasicDTO.getIsFirstProcess()));
    }
}