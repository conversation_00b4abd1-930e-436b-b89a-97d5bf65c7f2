package com.zte.test;

import com.zte.application.AssemblyResultRecordService;
import com.zte.application.AssemblyResultService;
import com.zte.application.impl.CommonSyncServiceImpl;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/9/14
 */
public class CommonSyncServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private CommonSyncServiceImpl service;

    @Mock
    private AssemblyResultService assemblyResultService;

    @Mock
    private PdmRemoteService pdmRemoteService;

    @Mock
    private AssemblyResultRecordService assemblyResultRecordService;

    @Test
    public void sendToPdmAndUpdateResult() throws Exception {
        List<AssemblyResultEntityDTO> assemblyResultEntityDTOList=new ArrayList<>();
        AssemblyResultEntityDTO assemblExecLogEntityDTO=new AssemblyResultEntityDTO();
        assemblExecLogEntityDTO.setLastUpdatedDate(new Date());
        assemblExecLogEntityDTO.setItemCode("222");
        assemblyResultEntityDTOList.add(assemblExecLogEntityDTO);
        PowerMockito.when(assemblyResultService.getListByStatus(anyObject())).thenReturn(assemblyResultEntityDTOList);
        PowerMockito.when(pdmRemoteService.sendToPdm(anyObject(), anyObject())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.sendToPdmAndUpdateResult(new Date(),"http://","logId"));
    }
}
