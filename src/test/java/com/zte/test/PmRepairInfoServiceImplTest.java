package com.zte.test;


import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PmRepairRcvService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.PmRepairInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.PmRepairInfo;
import com.zte.domain.model.PmRepairInfoRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsTask;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmRepairInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@PrepareForTest({PlanscheduleRemoteService.class,ObtainRemoteServiceDataUtil.class,CommonUtils.class})
public class PmRepairInfoServiceImplTest extends PowerBaseTestCase {

    @Mock
    private PmRepairInfoServiceImpl pmRepairInfoServiceImpl;
    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private ServiceData serviceData;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Mock
    private PmRepairInfoRepository pmRepairInfoRepository;

    @InjectMocks
    private PmRepairInfoServiceImpl serviceImpl;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void duplicateIds() throws Exception {
        List<PmRepairInfo> list=new ArrayList<>();
        PmRepairInfo info=new PmRepairInfo();
        info.setRepairBy("10270446");
        list.add(info);
        Assert.assertNotNull(serviceImpl.duplicateIds(list));
    }

    @Test
    public void setUserName() throws Exception {
        List<PmRepairInfo> list=new ArrayList<>();
        PmRepairInfo info=new PmRepairInfo();
        info.setRepairBy("10270446");
        list.add(info);
        Map<String, String> bsPubHrMap=new HashMap<>();
        bsPubHrMap.put("10270446","zlk");
        Assert.assertNotNull(serviceImpl.setUserName(info,bsPubHrMap));
    }
    @Test
    public void getRelOneList() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setAttribute1("7310035");
        pmRepairInfo.setBuilding("1149005");
        pmRepairInfo.setBuildingName("16栋3楼");
        pmRepairInfo.setCraftSection("维修");
        pmRepairInfo.setCreateBy("00263453");
        pmRepairInfo.setDeliveryBy("00263453");
        pmRepairInfo.setDeliveryByName("陈昭君");
        pmRepairInfo.setDeliveryNo("RP522020082600003");
        pmRepairInfo.setEnabledFlag("Y");
//        pmRepairInfo.setEntityId(2L);
        pmRepairInfo.setEpAttrName("HSF-S");
        pmRepairInfo.setErrorDescription("99");
        pmRepairInfo.setFactoryId(new BigDecimal(52));
        List<PmRepairInfo> list = new ArrayList<PmRepairInfo>();
        list.add(pmRepairInfo);
        PowerMockito.when(pmRepairInfoRepository.getRelOneList(Mockito.any())).thenReturn(list);

        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        record.setDeliveryNo("RP522020082600003");
        Assert.assertNotNull(PowerMockito.when(pmRepairInfoServiceImpl.getRelOneList(record)).thenReturn(list));
//        pmRepairInfoServiceImpl.getRelOneList(record);

    }

    @Test
    public void getRelOnePage() throws Exception {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setRepairId("81dacc55-d0ca-4400-b50d-a3b0fdbf7499");
        pmRepairInfo.setSn("731003300193");
        pmRepairInfo.setItemCode("129692851029zhq");
        pmRepairInfo.setItemName("ZXR10 9916 ISFSD");
        pmRepairInfo.setProdplanId("7310033");
        pmRepairInfo.setWorkOrderNo("7310033-DIP5212");
        pmRepairInfo.setFromStation("整机");
        pmRepairInfo.setItemNo("129692851029zhq");
        pmRepairInfo.setPrdItemCode("129692851029zhq");
        pmRepairInfo.setPrdItemnName("ZXR10 9916 ISFSD");

        List<PmRepairInfo> list = new ArrayList<>();
        list.add(pmRepairInfo);

        PowerMockito.when(pmRepairInfoRepository.getRelOnePage(Mockito.anyObject())).thenReturn(list);

        PsTask psTask = new PsTask();
        psTask.setTaskId("AAAcsbAAFAAAACA33");
        psTask.setTaskNo("zhq180310CP33-zhq-1");
        psTask.setItemNo("129692851029zhq");
        psTask.setItemName("ZXR10 9916 ISFSD");
        psTask.setIsLead("无铅");
        psTask.setTaskQty(new BigDecimal(3000));
        psTask.setCompleteQty(new BigDecimal(4));
        psTask.setTaskStatus("已完工");
        psTask.setExternalType("DHOME");
        psTask.setSourceSys("STEP");
        psTask.setProdplanId("7310033");
        psTask.setProdplanNo("zhq180310CP33-zhq-1");


        List<PsTask> psTaskList = new ArrayList<>();
        psTaskList.add(psTask);

//        List<String> splitList =new ArrayList<>();
//        splitList.add("7310033");
//        splitList.add("7310035");
//        splitList.add("7310037");

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getEnvAttrBatchByIds(Mockito.anyList())).thenReturn(psTaskList);

        PowerMockito.when(pmRepairInfoRepository.getRelRepairCountBySn(Mockito.any())).thenReturn(10L);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10560005");
        map.put("lookupMeaning","拟制中");
        map.put("descriptionChin","拟制中");
        map.put("descriptionEng","拟制中");
        map.put("lookupType","1056");
        map.put("editableFlag","Y");
        map.put("sortSeq","5");
        map.put("enabledFlag","Y");

        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        mountTypeMap.put("10560005",map);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when( ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> mapGetLine = new HashMap<String, String>();
        mapGetLine.put("000013","DIP1");
        mapGetLine.put("C0002","测试2线");
        mapGetLine.put("111111","111");
        mapGetLine.put("DIP2","复线测试1");
        mapGetLine.put("DIP3","DIP3");
        mapGetLine.put("SMT-94","SMT-94");
        mapGetLine.put("FL060010","SMT1-4线");
        mapGetLine.put("SMT-95","SMT-95");
        mapGetLine.put("DIP4","DIP4");

        PowerMockito.when( ObtainRemoteServiceDataUtil.getLineAll(Mockito.any())).thenReturn(mapGetLine);

        PmRepairInfoDTO record =new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        record.setStartTime("2019-01-17 07:06:33");
        record.setEndTime("2020-06-24 07:06:33");

        Assert.assertNotNull(pmRepairInfoServiceImpl.getRelOnePage(record));
    }

    @Test
    public void getRelOnePageNoChange() throws Exception {
        PowerMockito.when(pmRepairInfoRepository.getRelOnePageRecent(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(pmRepairInfoRepository.getRelOnePage(Mockito.any())).thenReturn(new ArrayList<>());

        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setInSns("test");
        Assert.assertNotNull(pmRepairInfoServiceImpl.getRelOnePageNoChange(record));
    }

    @Test
    public void getRelPageChange() throws Exception {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setRepairId("1f612b03-45c6-467f-a981-78651792a967");
        pmRepairInfo.setSn("711611400013");
        pmRepairInfo.setItemCode("129692851088ATB");
        pmRepairInfo.setItemName("ZXSDR R8968E M1920 R02TRU1920A");
        pmRepairInfo.setProdplanId("7116114");
        pmRepairInfo.setWorkOrderNo("ZL05171852055");
        pmRepairInfo.setCraftSection("DIP");
        pmRepairInfo.setWorkStation("S1054");
        pmRepairInfo.setRepairProducetType("制造");
        pmRepairInfo.setRepairProductStype("12");
        pmRepairInfo.setRepairProuctMstype("123");
        pmRepairInfo.setStatusName("已提交");
        pmRepairInfo.setCsvLocationNo("\"D1,D19\"");
        pmRepairInfo.setIsLocationNo("Y");
        pmRepairInfo.setPrdItemCode("129692851088ATB");
        pmRepairInfo.setPrdItemnName("ZXSDR R8968E M1920 R02TRU1920A");

        List<PmRepairInfo> list = new ArrayList<>();
        list.add(pmRepairInfo);

        PowerMockito.when(pmRepairInfoRepository.getRelPageChange(Mockito.anyObject())).thenReturn(list);

        PowerMockito.when(pmRepairInfoRepository.getRelChangeRepairCount(Mockito.any(),Mockito.any())).thenReturn(10L);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10560005");
        map.put("lookupMeaning","拟制中");
        map.put("descriptionChin","拟制中");
        map.put("descriptionEng","拟制中");
        map.put("lookupType","1056");
        map.put("editableFlag","Y");
        map.put("sortSeq","5");
        map.put("enabledFlag","Y");

        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        mountTypeMap.put("10560005",map);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when( ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> mapGetLine = new HashMap<String, String>();
        mapGetLine.put("000013","DIP1");
        mapGetLine.put("C0002","测试2线");
        mapGetLine.put("111111","111");
        mapGetLine.put("DIP2","复线测试1");
        mapGetLine.put("DIP3","DIP3");
        mapGetLine.put("SMT-94","SMT-94");
        mapGetLine.put("FL060010","SMT1-4线");
        mapGetLine.put("SMT-95","SMT-95");
        mapGetLine.put("DIP4","DIP4");

        PowerMockito.when( ObtainRemoteServiceDataUtil.getLineAll(Mockito.any())).thenReturn(mapGetLine);

        PmRepairInfoDTO record =new PmRepairInfoDTO();
        record.setWorkStation("S1054");
        record.setStartTime("2019-01-17 07:06:33");
        record.setEndTime("2020-06-24 07:06:33");

        Assert.assertNotNull(pmRepairInfoServiceImpl.getRelPageChange(record));
    }

    @Test
    public void getRelOneDetailPage() throws Exception {
        PmRepairInfo pmRepairInfo =new PmRepairInfo();
        pmRepairInfo.setRepairId("44580e60-1cfa-4551-b13b-88f424bfef2b");
        pmRepairInfo.setSn("730638900004");
        pmRepairInfo.setItemCode("127230950329AIB");
        pmRepairInfo.setItemName("ZXRAN A9631A S26 MA0TAC26B");
        pmRepairInfo.setProdplanId("7306389");
        pmRepairInfo.setWorkOrderNo("7306389-SMT-A5501");
        pmRepairInfo.setStatus(new BigDecimal(20));
        pmRepairInfo.setWorkStation("S1053");
        pmRepairInfo.setReceptionId("54556dbe-ce51-4ff4-9326-2cce4f3078cd");
        pmRepairInfo.setRepairStatus("10560003");
        pmRepairInfo.setPrdItemCode("127230950329AIB");
        pmRepairInfo.setPrdItemnName("ZXRAN A9631A S26 MA0TAC26B");

        List<PmRepairInfo> list= new ArrayList<>();
        list.add(pmRepairInfo);

        PowerMockito.when(pmRepairInfoRepository.getRelOnePage(Mockito.anyObject())).thenReturn(list);

        PowerMockito.when(pmRepairInfoRepository.getDetailPage(Mockito.anyObject())).thenReturn(list);

        PsTask psTask = new PsTask();
        psTask.setTaskId("AAAcsbAAFAAAACA33");
        psTask.setTaskNo("zhq180310CP33-zhq-1");
        psTask.setItemNo("129692851029zhq");
        psTask.setItemName("ZXR10 9916 ISFSD");
        psTask.setIsLead("无铅");
        psTask.setTaskQty(new BigDecimal(3000));
        psTask.setCompleteQty(new BigDecimal(4));
        psTask.setTaskStatus("已完工");
        psTask.setExternalType("DHOME");
        psTask.setSourceSys("STEP");
        psTask.setProdplanId("7310033");
        psTask.setProdplanNo("zhq180310CP33-zhq-1");


        List<PsTask> psTaskList = new ArrayList<>();
        psTaskList.add(psTask);

//        List<String> splitList =new ArrayList<>();
//        splitList.add("7310033");
//        splitList.add("7310035");
//        splitList.add("7310037");

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getEnvAttrBatchByIds(Mockito.anyList())).thenReturn(psTaskList);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10560005");
        map.put("lookupMeaning","52");
        map.put("descriptionChin","拟制中");
        map.put("descriptionEng","拟制中");
        map.put("lookupType","1056");
        map.put("editableFlag","Y");
        map.put("sortSeq","5");
        map.put("enabledFlag","Y");

        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        mountTypeMap.put("10560005",map);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when( ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> mapGetLine = new HashMap<String, String>();
        mapGetLine.put("000013","DIP1");
        mapGetLine.put("C0002","测试2线");
        mapGetLine.put("111111","111");
        mapGetLine.put("DIP2","复线测试1");
        mapGetLine.put("DIP3","DIP3");
        mapGetLine.put("SMT-94","SMT-94");
        mapGetLine.put("FL060010","SMT1-4线");
        mapGetLine.put("SMT-95","SMT-95");
        mapGetLine.put("DIP4","DIP4");

        PowerMockito.when( ObtainRemoteServiceDataUtil.getLineAll(Mockito.any())).thenReturn(mapGetLine);

        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        record.setStartTime("2019-01-17 07:06:33");
        record.setEndTime("2020-06-24 07:06:33");
        record.setOnlyNewRecord(true);
        Assert.assertNotNull(serviceImpl.getRelOneDetailPage(record));
    }

    @Test
    public void getRelListChange() throws Exception {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setRepairId("1f612b03-45c6-467f-a981-78651792a967");
        pmRepairInfo.setSn("711611400013");
        pmRepairInfo.setItemCode("129692851088ATB");
        pmRepairInfo.setItemName("ZXSDR R8968E M1920 R02TRU1920A");
        pmRepairInfo.setProdplanId("7116114");
        pmRepairInfo.setWorkOrderNo("ZL05171852055");
        pmRepairInfo.setCraftSection("DIP");
        pmRepairInfo.setWorkStation("S1054");
        pmRepairInfo.setRepairProducetType("制造");
        pmRepairInfo.setRepairProductStype("12");
        pmRepairInfo.setRepairProuctMstype("123");
        pmRepairInfo.setStatusName("已提交");
        pmRepairInfo.setCsvLocationNo("\"D1,D19\"");
        pmRepairInfo.setIsLocationNo("Y");
        pmRepairInfo.setPrdItemCode("129692851088ATB");
        pmRepairInfo.setPrdItemnName("ZXSDR R8968E M1920 R02TRU1920A");

        List<PmRepairInfo> list = new ArrayList<>();
        list.add(pmRepairInfo);

        PowerMockito.when(pmRepairInfoRepository.getRelListChange(Mockito.anyObject())).thenReturn(list);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10560005");
        map.put("lookupMeaning","拟制中");
        map.put("descriptionChin","拟制中");
        map.put("descriptionEng","拟制中");
        map.put("lookupType","1056");
        map.put("editableFlag","Y");
        map.put("sortSeq","5");
        map.put("enabledFlag","Y");

        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        mountTypeMap.put("10560005",map);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when( ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> mapGetLine = new HashMap<String, String>();
        mapGetLine.put("000013","DIP1");
        mapGetLine.put("C0002","测试2线");
        mapGetLine.put("111111","111");
        mapGetLine.put("DIP2","复线测试1");
        mapGetLine.put("DIP3","DIP3");
        mapGetLine.put("SMT-94","SMT-94");
        mapGetLine.put("FL060010","SMT1-4线");
        mapGetLine.put("SMT-95","SMT-95");
        mapGetLine.put("DIP4","DIP4");

        PowerMockito.when( ObtainRemoteServiceDataUtil.getLineAll(Mockito.any())).thenReturn(mapGetLine);

        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        record.setStartTime("2019-01-17 07:06:33");
        record.setEndTime("2020-06-24 07:06:33");
        Assert.assertNotNull(pmRepairInfoServiceImpl.getRelListChange(record));
    }

    @Test
    public void getRelOneDetailList() throws Exception {
        PmRepairInfo pmRepairInfo = new PmRepairInfo();
        pmRepairInfo.setAttribute1("7310035");
        pmRepairInfo.setBuilding("1149005");
        pmRepairInfo.setBuildingName("16栋3楼");
        pmRepairInfo.setCraftSection("维修");
        pmRepairInfo.setCreateBy("00263453");
        pmRepairInfo.setDeliveryBy("00263453");
        pmRepairInfo.setDeliveryByName("陈昭君");
        pmRepairInfo.setDeliveryNo("RP522020082600003");
        pmRepairInfo.setEnabledFlag("Y");
//        pmRepairInfo.setEntityId(2L);
        pmRepairInfo.setEpAttrName("HSF-S");
        pmRepairInfo.setErrorDescription("99");
        pmRepairInfo.setFactoryId(new BigDecimal(52));
        List<PmRepairInfo> list = new ArrayList<PmRepairInfo>();
        list.add(pmRepairInfo);

        PowerMockito.when(pmRepairInfoRepository.getRelOneList(Mockito.anyObject())).thenReturn(list);

        PowerMockito.when(pmRepairInfoRepository.getRelOneDetailList(Mockito.anyObject())).thenReturn(list);

        Map<String,String> map = new HashMap<>();
        map.put("lookupCode","10560005");
        map.put("lookupMeaning","拟制中");
        map.put("descriptionChin","拟制中");
        map.put("descriptionEng","拟制中");
        map.put("lookupType","1056");
        map.put("editableFlag","Y");
        map.put("sortSeq","5");
        map.put("enabledFlag","Y");

        Map<String, Map<String, String>> mountTypeMap = new HashMap<String, Map<String, String>>();
        mountTypeMap.put("10560005",map);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when( ObtainRemoteServiceDataUtil.getLookupTypeByType(Mockito.any(),Mockito.any())).thenReturn(mountTypeMap);

        Map<String, String> mapGetLine = new HashMap<String, String>();
        mapGetLine.put("000013","DIP1");
        mapGetLine.put("C0002","测试2线");
        mapGetLine.put("111111","111");
        mapGetLine.put("DIP2","复线测试1");
        mapGetLine.put("DIP3","DIP3");
        mapGetLine.put("SMT-94","SMT-94");
        mapGetLine.put("FL060010","SMT1-4线");
        mapGetLine.put("SMT-95","SMT-95");
        mapGetLine.put("DIP4","DIP4");

        PowerMockito.when( ObtainRemoteServiceDataUtil.getLineAll(Mockito.any())).thenReturn(mapGetLine);

        PmRepairInfoDTO record = new PmRepairInfoDTO();
        record.setFactoryId(new BigDecimal(52));
        record.setStartTime("2019-01-17 07:06:33");
        record.setEndTime("2020-06-24 07:06:33");
        Assert.assertNotNull(pmRepairInfoServiceImpl.getRelOneDetailList(record));
    }


    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(serviceImpl, "setMBom", null);
        Assert.assertTrue(1==1);
        List<PmRepairInfo> list = new ArrayList<>();
        PmRepairInfo entity = new PmRepairInfo();
        entity.setProdplanId("1234567");
        list.add(entity);
        PmRepairInfo entity1 = new PmRepairInfo();
        entity1.setProdplanId("12345671");
        entity1.setItemCode("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(serviceImpl, "setMBom", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertTrue(list.get(1).getMbom().equals("itemNo"));
    }

}
