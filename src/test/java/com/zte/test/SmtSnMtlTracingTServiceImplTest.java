package com.zte.test;

import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.StItemBarcodeService;
import com.zte.application.impl.SmtSnMtlTracingTServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.DateUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelBigDataExportManage;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.datasource.DynamicDataSource;
import com.zte.itp.msa.datasource.MsaDataSource;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@PrepareForTest({CommonUtils.class, RedisCacheUtils.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class,
        EmailUtils.class})
public class SmtSnMtlTracingTServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SmtSnMtlTracingTServiceImpl smtSnMtlTracingTServiceImpl;
    @Mock
    private SmtSnMtlTracingTRepository smtSnMtlTracingTRepository;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Mock
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    private StItemBarcodeService stItemBarcodeService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private SXSSFWorkbook wb;

    @Mock
    private SXSSFSheet sheet;

    @Mock
    private SXSSFRow row;

    @Mock
    private SXSSFCell cell;

    @Mock
    private ExcelBigDataExportManage excelBigDataExportManage;

    @Mock
    PsWipInfoRepository psWipInfoRepository;

    @Mock
    private DynamicDataSource msaDataSource;

    @Mock
    private Map<Object, DataSource> datasourceMap;

    @Mock
    private DataSource dataSource;

    @Mock
    private Connection connection;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    /* Started by AICoder, pid:kcc8dlc22994865141280a5e52ab36578a42ae9e */
    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
        HashMap<String, Object> lookupMap = new HashMap<>();
        lookupMap.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_TYPE_2868);
        ArrayList<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTOSMT = new SysLookupTypesDTO();
        sysLookupTypesDTOSMT.setLookupMeaning("SMT");
        sysLookupTypesDTOSMT.setAttribute1("SMT-A,SMT-B");
        sysLookupTypesDTOS.add(sysLookupTypesDTOSMT);
        SysLookupTypesDTO sysLookupTypesDTODIP = new SysLookupTypesDTO();
        sysLookupTypesDTODIP.setLookupMeaning("DIP");
        sysLookupTypesDTODIP.setAttribute1("DIP,背板,电源模块");
        sysLookupTypesDTOS.add(sysLookupTypesDTODIP);
        when(centerfactoryRemoteService.getSysLookUpValue(lookupMap)).thenReturn(sysLookupTypesDTOS);
    }
    @Test
    public void dealParam() throws Exception {
        SmtSnMtlTracingTDTO smtSnMtlTracingTDTO = new SmtSnMtlTracingTDTO();
        smtSnMtlTracingTDTO.setProductBatchCode("7778889");
        smtSnMtlTracingTDTO.setTraceType("PCB");
        try {
            Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"dealParam",smtSnMtlTracingTDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_BATCH_FIRST_COMMAND, e.getMessage());
        }
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasicDTO dto = new PsWorkOrderBasicDTO();
        PowerMockito.when(PlanscheduleRemoteService.queryFirstWorkOrderByProdplanId(any())).thenReturn(dto);
        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"dealParam",smtSnMtlTracingTDTO);
        Assert.assertEquals("PCB", smtSnMtlTracingTDTO.getTraceType());
    }
    @Test
    public void setLineName() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<CFLine> cfLineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineName("name");
        cfLine.setLineCode("code");
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        List<SmtSnMtlTracingT> retList = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        retList.add(smtSnMtlTracingT);
        smtSnMtlTracingTServiceImpl.setLineName(retList);
        Assert.assertEquals("name", cfLine.getLineName());
    }

    @Test
    public void dealExcel() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SmtSnMtlTracingTDTO dto = new SmtSnMtlTracingTDTO();
        dto.setQueryStartTimeStr("2020-03-04 08:10:00");
        dto.setQueryEndTimeStr("2020-03-04 08:10:00");
        dto.setEmail("xxx.zte.com.cn");
        dto.setFactoryId(52L);
        smtSnMtlTracingTServiceImpl.setSubmeterStartDate("52_2018-11-22,53_2018-11-22,55_2018-11-22,56_2019-12-11,58_2019-12-11");

        // 可取到缓存数据情况
        PowerMockito.when(RedisCacheUtils.get(Mockito.any(), Mockito.any())).thenReturn("[\"93d451fd-aa41-4bed-95c9-fe07bbccb049\"]");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(any(), any(), any()))
                .thenReturn("https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/MES_IMES/93d451fd-aa41-4bed-95c9-fe07bbccb049");
        PowerMockito.when(emailUtils.sendMail(dto.getEmail(), "", "", "", "")).thenReturn(true);
        smtSnMtlTracingTServiceImpl.dealExcel(dto);
        List<String> reelidList = new ArrayList<>();
        reelidList.add("reelid");
        dto.setObjectIdList(reelidList);
        smtSnMtlTracingTServiceImpl.dealExcel(dto);

        // 无缓存数据情况
        PowerMockito.when(RedisCacheUtils.get(Mockito.any(), Mockito.any())).thenReturn(null);
        PowerMockito.when(smtSnMtlTracingTRepository.getTracingCountOfSmt(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1L);
        PowerMockito.whenNew(SXSSFWorkbook.class).withAnyArguments().thenReturn(wb);
        PowerMockito.when(wb.getSheetAt(Mockito.anyInt()))
                .thenReturn(sheet);
        PowerMockito.when(sheet.createRow(Mockito.anyInt()))
                .thenReturn(row);
        List<SmtSnMtlTracingT> queryList = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        smtSnMtlTracingT.setObjectId("ZTE001");
        queryList.add(smtSnMtlTracingT);
        PowerMockito.when(smtSnMtlTracingTRepository.getTracingOfSmt(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(queryList);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        List<CFLine> cfLineList = new ArrayList<>();
        CFLine cfLine = new CFLine();
        cfLine.setLineName("name");
        cfLine.setLineCode("code");
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        Pair<String, String> fileInfo = Pair.of("fileKey", "url");
        PowerMockito.when(excelBigDataExportManage.uploadToCloudDisk(any(), any())).thenReturn(fileInfo);
        Assert.assertEquals("name", cfLine.getLineName());
        smtSnMtlTracingTServiceImpl.dealExcel(dto);
    }

    @Test
    public void sendEmailTest() throws Exception {
        String email = "<EMAIL>";
        List<String> fileUrls = new ArrayList<>();
        String file = "user/00263453";
        fileUrls.add(file);
        PowerMockito.mock(EmailUtils.class);
        PowerMockito.when(emailUtils.sendMail(email, "", "", "", "")).thenReturn(true);
        smtSnMtlTracingTServiceImpl.sendEmail(email, fileUrls);
        Assert.assertEquals("user/00263453", file);
    }

    @Test
    public void insertBatch()  {
        List<SmtSnMtlTracingT> itemList = new ArrayList<>();
        int count = smtSnMtlTracingTServiceImpl.insertBatch(itemList);
        Assert.assertEquals(0, count);

        PowerMockito.when(smtSnMtlTracingTRepository.insertBatch(any(), any())).thenReturn(1);
        itemList.add(new SmtSnMtlTracingT());
        count = smtSnMtlTracingTServiceImpl.insertBatch(itemList);
        Assert.assertEquals(1, count);
    }

    @Test
    public void getTracingDataCount() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);
        PowerMockito.when(smtSnMtlTracingTRepository.getTracingCountOfSmt(Mockito.any(), any(), any())).thenReturn(10L);
        // 反射设值
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "52_2018-11-22,53_2018-11-22,55_2018-11-22,56_2019-12-11,58_2019-12-11");

        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setTraceType("SMT");
        item.setWorkOrder("7368539-SMT-A5501");
        item.setQueryStartTimeStr("2021-05-16");
        item.setQueryEndTimeStr("2021-05-11");
        item.setPage("1");
        item.setRows("10");
        item.setOrderField("snScanDate");
        item.setOrder("desc");
        item.setFactoryId(55L);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-01-01");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");
        Assert.assertTrue(smtSnMtlTracingTServiceImpl.getTracingDataCount(item) > 0L);
        item.setTraceType("DIP");
        Assert.assertTrue(smtSnMtlTracingTServiceImpl.getTracingDataCount(item).compareTo(0L) == 0L);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "");
        Assert.assertTrue(smtSnMtlTracingTServiceImpl.getTracingDataCount(item).compareTo(0L) == 0L);
    }

    @Test
    public void getTraceDataBySubSn() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        // 反射设值
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "52_2018-11-22,53_2018-11-22,55_2018-11-22,56_2019-12-11,58_2019-12-11");
        List<SmtSnMtlTracingT> tracingList = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        smtSnMtlTracingT.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT.setAttribute1("20");
        smtSnMtlTracingT.setObjectId("00");
        tracingList.add(smtSnMtlTracingT);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(tracingList);
        PowerMockito.when(stItemBarcodeService.queryListChange(any())).thenReturn(tracingList);

        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setTraceType("SMT");
        item.setWorkOrder("7368539-SMT-A5501");
        item.setQueryStartTimeStr("2021-05-11");
        item.setQueryEndTimeStr("2021-05-16");
        item.setPage("1");
        item.setRows("10");
        item.setOrderField("snScanDate");
        item.setOrder("desc");
        item.setFactoryId(55L);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), any())).thenReturn(sysLookupTypesDTO);
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("00");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(barcodeExpandDTOList);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-01-01");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");
        smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        BarcodeExpandDTO barcodeExpandDTO1 = new BarcodeExpandDTO();
        barcodeExpandDTO1.setBarcode("00");
        barcodeExpandDTOList.add(barcodeExpandDTO1);
        BarcodeExpandDTO barcodeExpandDTO2 = new BarcodeExpandDTO();
        barcodeExpandDTO2.setBarcode("001");
        barcodeExpandDTO2.setSourceBatchNo("004");
        barcodeExpandDTO2.setSpecModel("111");
        barcodeExpandDTOList.add(barcodeExpandDTO2);
        BarcodeExpandDTO barcodeExpandDTO3 = new BarcodeExpandDTO();
        barcodeExpandDTO3.setBarcode("002");
        barcodeExpandDTO3.setSpecModel("111");
        barcodeExpandDTO3.setSupplierName("123");
        barcodeExpandDTO3.setSourceBatchNo("005");
        barcodeExpandDTOList.add(barcodeExpandDTO3);
        BarcodeExpandDTO barcodeExpandDTO4 = new BarcodeExpandDTO();
        barcodeExpandDTO4.setBarcode("003");
        barcodeExpandDTO4.setSpecModel("111");
        barcodeExpandDTO4.setSupplierName("123");
        barcodeExpandDTO4.setBrandName("321");
        barcodeExpandDTOList.add(barcodeExpandDTO4);
        BarcodeExpandDTO barcodeExpandDTO5= new BarcodeExpandDTO();
        barcodeExpandDTO5.setBarcode("004");
        barcodeExpandDTO5.setSpecModel("111");
        barcodeExpandDTO5.setSupplierName("123");
        barcodeExpandDTO5.setBrandName("321");
        barcodeExpandDTOList.add(barcodeExpandDTO5);
        BarcodeExpandDTO barcodeExpandDTO6 = new BarcodeExpandDTO();
        barcodeExpandDTO6.setBarcode("006");
        barcodeExpandDTO6.setSupplierName("123");
        barcodeExpandDTO6.setBrandName("321");
        barcodeExpandDTOList.add(barcodeExpandDTO6);
        PowerMockito.when(barcodeCenterRemoteService.expandQueryOneByOne(any())).thenReturn(barcodeExpandDTOList);
        List<SmtSnMtlTracingT> tracingList1 = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT1 = new SmtSnMtlTracingT();
        smtSnMtlTracingT1.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT1.setAttribute1("20");
        smtSnMtlTracingT1.setObjectId("001");
        tracingList1.add(smtSnMtlTracingT1);
        SmtSnMtlTracingT smtSnMtlTracingT2 = new SmtSnMtlTracingT();
        smtSnMtlTracingT2.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT2.setAttribute1("20");
        smtSnMtlTracingT2.setObjectId("002");
        tracingList1.add(smtSnMtlTracingT2);
        SmtSnMtlTracingT smtSnMtlTracingT3 = new SmtSnMtlTracingT();
        smtSnMtlTracingT3.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT3.setAttribute1("20");
        smtSnMtlTracingT3.setObjectId("003");
        tracingList1.add(smtSnMtlTracingT3);
        SmtSnMtlTracingT smtSnMtlTracingT4 = new SmtSnMtlTracingT();
        smtSnMtlTracingT4.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT4.setAttribute1("20");
        smtSnMtlTracingT4.setObjectId("004");
        tracingList1.add(smtSnMtlTracingT4);
        SmtSnMtlTracingT smtSnMtlTracingT5 = new SmtSnMtlTracingT();
        smtSnMtlTracingT4.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT5.setAttribute1("20");
        smtSnMtlTracingT5.setObjectId("006");
        tracingList1.add(smtSnMtlTracingT5);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(tracingList1);
        smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(tracingList);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(new ArrayList<>());
        smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        sysLookupTypesDTO.setLookupMeaning("N");
        PowerMockito.when(stItemBarcodeService.queryListChange(any())).thenReturn(new ArrayList<>());
        smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(new ArrayList<>());
        smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        item.setQueryStartTimeStr("2024-05-11");
        item.setQueryEndTimeStr("2024-05-16");
        Assert.assertNotNull(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item));
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "55_ q");
        Assert.assertNotNull(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item));
        try {
            smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCAN_START_TIME_CANNOT_LATER, e.getMessage());
        }
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "52_2018-11-22,53_2018-11-22,55_2024-05-20,56_2019-12-11,58_2019-12-11");
        try {
            smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCAN_START_TIME_CANNOT_LATER, e.getMessage());
        }
        item.setQueryEndTimeStr("3024-05-16");
        Assert.assertNotNull(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item));
        item.setQueryEndTimeStr("");
        try {
            smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MUST_SPECIFY_TIME, e.getMessage());
        }
        item.setQueryStartTimeStr("");
        try {
            smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MUST_SPECIFY_TIME, e.getMessage());
        }
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "55_ q");
        try {
            smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MUST_SPECIFY_TIME, e.getMessage());
        }

    }

    @Test
    public void checkAndDealTracingParam() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setWorkOrderNo("7368539-SMT-A5501");
        psWorkOrderBasic.setSourceTask("7368539");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(anyString())).thenReturn(psWorkOrderBasic);

        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setTraceType("DIP");
        item.setWorkOrder("DIP-A5501");
        item.setQueryStartTimeStr("2021-05-16");
        item.setQueryEndTimeStr("2021-05-11");
        item.setObjectId("111");
        smtSnMtlTracingTServiceImpl.checkAndDealTracingParam(item);

        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("DIP-A5501");
        psWorkOrderDTO.setSourceTask("7368539");
        psWorkOrderDTO.setCraftSection("DIP");
        workOrderList.add(psWorkOrderDTO);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(anyString())).thenReturn(workOrderList);

        item.setWorkOrder("DIP-A5501");
        item.setProductBatchCode("7368539");
        item.setObjectId("111");
        Assert.assertEquals("111", item.getObjectId());
        smtSnMtlTracingTServiceImpl.checkAndDealTracingParam(item);

    }

    @Test
    public void getTracingData() throws Exception {
        // 反射设值
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "52_2018-11-22,53_2018-11-22,55_2018-11-22,56_2019-12-11,58_2019-12-11");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-01-01");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");

        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);

        List<PsWorkOrderSmt> workOrderSmtList = new ArrayList<>();
        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        psWorkOrderSmt.setWorkOrderNo("7368539-SMT-A5501");
        psWorkOrderSmt.setPcbQty(BigDecimal.TEN);
        workOrderSmtList.add(psWorkOrderSmt);
        PowerMockito.when(PlanscheduleRemoteService.selectPsWorkOrderSmtList(any())).thenReturn(workOrderSmtList);
        List<SmtSnMtlTracingT> tracingList = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        smtSnMtlTracingT.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT.setAttribute1("20");
        tracingList.add(smtSnMtlTracingT);
        PowerMockito.when(smtSnMtlTracingTRepository.getTracingOfSmt(Mockito.any(), any(), any())).thenReturn(tracingList);
        PowerMockito.when(stItemBarcodeService.queryListChange(any())).thenReturn(tracingList);

        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setTraceType("SMT");
        item.setWorkOrder("7368539-SMT-A5501");
        item.setQueryStartTimeStr("2021-05-11");
        item.setQueryEndTimeStr("2021-05-16");
        item.setPage("1");
        item.setRows("10");
        item.setOrderField("snScanDate");
        item.setOrder("desc");
        item.setFactoryId(55L);
        item.setObjectId("111");
        Assert.assertNotNull(smtSnMtlTracingTServiceImpl.getTracingData(item));
        item.setTraceType("DIP");
        Assert.assertTrue(CollectionUtils.isEmpty(smtSnMtlTracingTServiceImpl.getTracingData(item)));
        //Assert.assertEquals(list.size(), 1L);
        //Assert.assertEquals(list.get(0).getAttribute1(), String.valueOf(20));
    }
    /* Ended by AICoder, pid:kcc8dlc22994865141280a5e52ab36578a42ae9e */
    /* Started by AICoder, pid:q5a18k671c9b0d5145ba0b6c90d54939af77b621 */
    @Test(timeout = 8000)
    public void testGetTraceDataBySubSn_PsWipInfoNotNull() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("2");
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        item.setSubSn("123");
        item.setItemCode("ABC");
        List<String> tableNameSuffixList = new ArrayList<>();
        tableNameSuffixList.add("SUFFIX");
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setAttribute2("ZZ河源VCS190619036-V11-包装5502");
        wipInfo.setItemNo("125000360031");
        wipInfo.setAttribute3("HFS");
        List<WipExtendIdentification> tempList = new ArrayList<>();
        SmtSnMtlTracingT smtSnMtlTracingT = new SmtSnMtlTracingT();
        smtSnMtlTracingT.setWorkOrder("7368539-SMT-A5501");
        smtSnMtlTracingT.setAttribute1("20");
        smtSnMtlTracingT.setObjectId("00");
        item.setQueryStartTimeStr("2024-07-10 10:10:10");
        item.setQueryEndTimeStr("2024-07-10 10:10:10");
        item.setFactoryId(52L);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(Arrays.asList());
        when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-01-01");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "submeterStartDate", "52_2018-11-22,53_2018-11-22,55_2018-11-22,56_2019-12-11,58_2019-12-11");
        List<SmtSnMtlTracingT> smtSnMtlTracingTS = smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item);
        assertTrue(smtSnMtlTracingTS.size()==0);
        wipInfo = new PsWipInfo();
        wipInfo.setAttribute2("ZZ河源VCS190619036-V11-包装5502");
        wipInfo.setItemNo("125000360031AAB");
        wipInfo.setAttribute3("HFS");
        when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
        assertTrue(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item).size()==0);
        PowerMockito.when(smtSnMtlTracingTRepository.getTraceDataForRepairInput(any(), any(), any())).thenReturn(Arrays.asList());
        assertTrue(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item).isEmpty());
        PowerMockito.when(digitalPlatformRemoteService.getMaterialInformation(anyString(),any())).thenReturn(Arrays.asList(new BarcodePriceApiDTO()));
        item.setRepairInputSwitch(true);
        assertTrue(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item).size()>0);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "N");
        assertTrue(smtSnMtlTracingTServiceImpl.getTraceDataBySubSn(item).size() > 0);
    }
    /* Ended by AICoder, pid:q5a18k671c9b0d5145ba0b6c90d54939af77b621 */

    /*Started by AICoder, pid:a47abmbaebz1eb8145980842a1962e07a1c985ed*/
    @Test
    public void testSelectSmtSnMtlTracingTById_WithEmptyAffix() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        SmtSnMtlTracingT item = new SmtSnMtlTracingT();
        try {
            smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PK_KEY_NOT_NULL, e.getMessage());
        }
    }

    @Test
    public void testSelectSmtSnMtlTracingTById_WithPlaceOnFileFlagFalse() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        SmtSnMtlTracingT item = new SmtSnMtlTracingT();
        item.setRecordId("qweewqeqwewq");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-01-01");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");
        try {
            smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_MSGID, e.getMessage());
        }
    }

    @Test
    public void testSelectSmtSnMtlTracingTById_WithQueryYearLessThanYear() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        SmtSnMtlTracingT item = new SmtSnMtlTracingT();
        item.setRecordId("201801");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "");
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "N");
        SmtSnMtlTracingT result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileFlag", "Y");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-02-02");
        PowerMockito.when(msaDataSource.getResolvedDataSources()).thenReturn(datasourceMap);
        PowerMockito.when(datasourceMap.get(Mockito.any())).thenReturn(null);
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        PowerMockito.when(datasourceMap.get(Mockito.any())).thenReturn(dataSource);
        PowerMockito.when(dataSource.getConnection()).thenReturn(connection);
        PowerMockito.when(connection.getSchema()).thenReturn("qweewq");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        item.setRecordId("2018");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNotNull(result);
        item.setRecordId("201905");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        item.setRecordId("202001");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        item.setRecordId("201901");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
        ReflectUtil.setFieldValue(smtSnMtlTracingTServiceImpl, "placeOnFileDate", "2019-05-02");
        result = smtSnMtlTracingTServiceImpl.selectSmtSnMtlTracingTById(item);
        assertNull(result);
    }
    /*Ended by AICoder, pid:a47abmbaebz1eb8145980842a1962e07a1c985ed*/

    @Test
    public void testSetWorkOrder_TraceTypeIsDIPAndWorkOrderListIsNotEmpty() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class, PlanscheduleRemoteService.class);
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 当 traceType 为 "DIP_FLAG" 并且 workOrderList 不为空时，应该筛选并设置 inWorkOrder
        item.setProductBatchCode("batchCode");
        item.setWorkOrder("11");
        item.setTraceType(Constant.DIP_FLAG);

        PsWorkOrderDTO mockOrder1 = new PsWorkOrderDTO();
        mockOrder1.setCraftSection("BACKPLANE");
        mockOrder1.setWorkOrderNo("workOrder1");
        List<PsWorkOrderDTO> mockList = Arrays.asList(mockOrder1);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(any())).thenReturn(mockList);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"setWorkOrder",item);
        Assert.assertTrue(true);// 验证 setInWorkOrder 被调用
    }

    @Test
    public void testGetFilteredList_WithMatchingWorkOrders() throws Exception {
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 设置 traceType 和 CraftSection 匹配的情况
        item.setTraceType(Constant.DIP_FLAG);

        PsWorkOrderDTO workOrder1 = new PsWorkOrderDTO();
        workOrder1.setCraftSection("BACKPLANE");

        PsWorkOrderDTO workOrder2 = new PsWorkOrderDTO();
        workOrder2.setCraftSection("BACKPLANE");

        List<PsWorkOrderDTO> workOrderList = Arrays.asList(workOrder1, workOrder2);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"getFilteredList",workOrderList,item);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetFilteredList_WithNoMatchingWorkOrders() throws Exception {
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 设置 traceType 和 CraftSection 不匹配的情况
        item.setTraceType(Constant.DIP_FLAG);

        PsWorkOrderDTO workOrder1 = new PsWorkOrderDTO();
        workOrder1.setCraftSection("OTHER_SECTION");

        PsWorkOrderDTO workOrder2 = new PsWorkOrderDTO();
        workOrder2.setCraftSection("OTHER_SECTION");

        List<PsWorkOrderDTO> workOrderList = Arrays.asList(workOrder1, workOrder2);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"getFilteredList",workOrderList,item);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetFilteredList_WithEmptyWorkOrderList() throws Exception {
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 设置 traceType 和 CraftSection 匹配的情况
        item.setTraceType(Constant.DIP_FLAG);

        List<PsWorkOrderDTO> workOrderList = Collections.emptyList();

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"getFilteredList",workOrderList,item);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetFilteredList_WithNonMatchingTraceType() throws Exception {
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 设置 traceType 和 CraftSection 不匹配的情况
        item.setTraceType("NON_MATCH_TRACE_TYPE");

        PsWorkOrderDTO workOrder1 = new PsWorkOrderDTO();
        workOrder1.setCraftSection("BACKPLANE");

        List<PsWorkOrderDTO> workOrderList = Collections.singletonList(workOrder1);

        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl, "getFilteredList", workOrderList, item));
    }

    @Test
    public void testGetFilteredList_WithDIPFlagAndNonBackplaneCraftSection() throws Exception {
        SmtSnMtlTracingTDTO item = new SmtSnMtlTracingTDTO();
        // 设置 traceType 为 DIP_FLAG，但 craftSection 不匹配 BACKPLANE 的情况
        item.setTraceType(Constant.DIP_FLAG);

        PsWorkOrderDTO workOrder1 = new PsWorkOrderDTO();
        workOrder1.setCraftSection("SOME_OTHER_SECTION");

        List<PsWorkOrderDTO> workOrderList = Collections.singletonList(workOrder1);

        Whitebox.invokeMethod(smtSnMtlTracingTServiceImpl,"getFilteredList",workOrderList,item);
        Assert.assertTrue(true);
    }

}
