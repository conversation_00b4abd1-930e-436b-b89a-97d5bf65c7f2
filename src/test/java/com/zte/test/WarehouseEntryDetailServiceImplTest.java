package com.zte.test;

import com.zte.application.impl.WarehouseEntryDetailServiceImpl;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WarehouseEntryErpInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.springbootframe.common.model.FactoryConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class WarehouseEntryDetailServiceImplTest {

    @InjectMocks
    WarehouseEntryDetailServiceImpl warehouseEntryDetailServiceImpl;

    @Mock
    WarehouseEntryErpInfoRepository warehouseEntryErpInfoRepository;

    @Mock
    WarehouseEntryDetailRepository warehouseEntryDetailRepository;

    @Mock
    FactoryConfig factoryConfig;


    @Before
    public void init(){
        PowerMockito.mockStatic(DatawbRemoteService.class);
    }

    @Test
    public void getFirstStockDate(){
        List<String> prodplanIdList = new ArrayList<>();
        prodplanIdList.add("11");
        PowerMockito.when(warehouseEntryDetailRepository.getFirstStockDate(prodplanIdList))
                .thenReturn(new ArrayList<>());
        warehouseEntryDetailServiceImpl.getFirstStockDate(prodplanIdList);
        prodplanIdList = null;
        Assert.assertNotNull(warehouseEntryDetailServiceImpl.getFirstStockDate(prodplanIdList));
    }

    @Test
    public void getStock90Date(){
        List<BoardInstructionCycleDataCreateDTO> dtoList = new ArrayList<>();
        dtoList.add(new BoardInstructionCycleDataCreateDTO());
        PowerMockito.when(warehouseEntryDetailRepository.getStock90Date(dtoList))
                .thenReturn(new ArrayList<>());
        warehouseEntryDetailServiceImpl.getStock90Date(dtoList);
        dtoList = null;
        Assert.assertNotNull(warehouseEntryDetailServiceImpl.getStock90Date(dtoList));
    }
}
