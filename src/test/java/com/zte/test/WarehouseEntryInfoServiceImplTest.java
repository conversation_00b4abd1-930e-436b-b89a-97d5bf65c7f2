package com.zte.test;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.PsWipInfoService;
import com.zte.application.WarehouseEntryDetailService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WarehouseEntryDetail;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.WarehouseRequirementDetailRepository;
import com.zte.domain.model.WarehouseRequirementInfo;
import com.zte.domain.model.WarehouseRequirementInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BBomDetailResponseDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoQueryDTO;
import com.zte.interfaces.dto.export.WarehouseEntryExportDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.BILL_TYPE_OUT_CARD;
import static com.zte.common.utils.Constant.BILL_TYPE_SON_CARD;
import static com.zte.common.utils.Constant.WAREHOUSE_EXPORT;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;

@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class, DatawbRemoteService.class, ConstantInterface.class,
        RedisHelper.class, SpringUtil.class, MicroServiceRestUtil.class, CrafttechRemoteService.class,
        SpringContextUtil.class,JSON.class,JSONObject.class
        , JacksonJsonConverUtil.class, CommonUtils.class, ImesExcelUtil.class, EasyExcelFactory.class})
public class WarehouseEntryInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;

    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private WarehouseEntryDetailService warehouseEntryDetailService;
    @Mock
    private ErpRemoteService erpRemoteService;
    @Mock
    private WarehouseRequirementDetailRepository warehouseRequirementDetailRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImp;
    @Mock
    private RedisLock redisLock;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private JsonNode json;
    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
    @Mock
    private MockHttpServletResponse response;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Mock
    private WarehouseRequirementInfoRepository warehouseRequirementInfoRepository;

    @Mock
    HrmUserInfoService hrmUserInfoService;
    @Before
    public void init() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(SpringContextUtil.class,JSONObject.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,EasyExcelFactory.class,ConstantInterface.class,DatawbRemoteService.class,JSON.class);
    }


    @Test
    public void batchUpdateImuAndDelBill()throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_DBFX);
        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_K2);
        list.add(warehouseEntryInfo);
        warehouseEntryInfoServiceImpl.batchUpdateImuAndDelBill(list);
        Assert.assertNotNull(list);
        WarehouseEntryInfo warehouseEntryInfo2 = new WarehouseEntryInfo();
        warehouseEntryInfo2.setBillType(MpConstant.BILL_TYPE_TEN);
        warehouseEntryInfo2.setStockType(Constant.STOCK_TYPE_K2);
        list.add(warehouseEntryInfo2);
        warehouseEntryInfoServiceImpl.batchUpdateImuAndDelBill(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void receiptByBillType()throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_SINGLE);
        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_K2);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"receiptByBillType",warehouseEntryInfo,sysLookupTypesDTO,"",new StringBuffer());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
   @Test
    public void checkErpResult() {
        try {
            warehouseEntryInfoServiceImpl.checkErpResult(new JSONObject());
            warehouseEntryInfoServiceImpl.checkErpResult(JSON.parseObject(JSON.toJSONString(new ServiceData())));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ERP_WAREHOUSING_FAILED, e.getMessage());
        }
        try {
            warehouseEntryInfoServiceImpl.checkErpResult(JSON.parseObject(JSON.toJSONString(
                    new ServiceData() {{
                        setCode(new RetCode() {{
                            setCode("1");
                            setMsg("1");
                        }});
                    }})));
        } catch (Exception e) {
        }
    }


    @Test
    public void validReqBillAndSaveWarehouse() throws Exception{
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setBillTypeCode(new BigDecimal(123));
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.validReqBillAndSaveWarehouse(dto,
                "", "", "", new HashMap<>()));
    }

    @Test
    public void unlock() {
        WarehouseRequirementInfo warehouseRequirementInfo = new WarehouseRequirementInfo();
        PowerMockito.when(warehouseRequirementInfoRepository.getWarehouseReqByBillNo(any())).thenReturn(warehouseRequirementInfo);
        PowerMockito.when(warehouseRequirementDetailRepository.insert(any())).thenReturn(1);
        warehouseEntryInfoServiceImpl.unlock(new WarehouseEntryInfoQueryDTO(), "",
                true, new RedisLock("1"));
        warehouseEntryInfoServiceImpl.unlock(new WarehouseEntryInfoQueryDTO(), "",
                false, new RedisLock("1"));
        warehouseEntryInfoServiceImpl.unlock(new WarehouseEntryInfoQueryDTO(), "",
                false, null);
        warehouseEntryInfoServiceImpl.unlock(new WarehouseEntryInfoQueryDTO(), "",
                true, null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void submitWareEntryInfo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        String updateWorkStation = "";
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_SON);

        WarehouseEntryInfo warehouseEntryInfo1 = new WarehouseEntryInfo();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        String updateWorkStation1 = "";
        warehouseEntryInfo1.setBillType(MpConstant.BILL_TYPE_FIVE);
        warehouseEntryInfo1.setStockType(Constant.STOCK_TYPE_MODE_WORKSTATION);

        WarehouseEntryInfo warehouseEntryInfo2 = new WarehouseEntryInfo();
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        String updateWorkStation2 = "";
        warehouseEntryInfo2.setBillType(MpConstant.BILL_TYPE_FIVE);
        warehouseEntryInfo2.setStockType(Constant.STOCK_TYPE_K2);

        List<PsWipInfoDTO> psList = new LinkedList<>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        a1.setSn("123");
        psList.add(a1);
        warehouseEntryInfo.setSnList(psList);

        PowerMockito.when(warehouseEntryInfoRepository.selectDetailCas(any()))
                .thenReturn(new LinkedList<String>() {{
                    add(
                            "234");
                }});
        PowerMockito.when(warehouseEntryInfoServiceImpl.updateWarehouseEntryInfoById(any()))
                .thenReturn(1);
        PowerMockito.doNothing().when(PlanscheduleRemoteService.class ,"batchHandleCompleteQty", any());
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_TASK_OF_PRODPLANNO_NOT_EXIST, e.getMessage());
        }
    }

    /* Started by AICoder, pid:3a9f6x2a89lc0d714122090fb0a3408678938633 */
    @Test
    public void submitWareEntryInfo_BILL_TYPE_CONFIRMATION() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_CONFIRMATION);
        warehouseEntryInfo.setTaskNo("taskNo1");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();

        PowerMockito.when(erpRemoteService.invokeErpImport(Mockito.any(), Mockito.any())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ERP_MOVE_ERROR, e.getExMsgId());
        }

        PowerMockito.when(erpRemoteService.invokeErpImport(Mockito.any(), Mockito.any())).thenReturn(true);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.anyString())).thenReturn(null);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValueByCode(Mockito.anyString())).thenReturn(sysLookupValues);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupValues.setAttribute2("C0001");
        PowerMockito.when(warehouseEntryDetailService.getNotComfirmedSnCount(Mockito.anyString(), Mockito.anyList())).thenReturn(1);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, "");

        PowerMockito.when(warehouseEntryDetailService.getNotComfirmedSnCount(Mockito.anyString(), Mockito.anyList())).thenReturn(0);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, "");
    }
    /* Ended by AICoder, pid:3a9f6x2a89lc0d714122090fb0a3408678938633 */

    @Test
    public void receiveWarehouseEntryInfoBatch() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        warehouseEntryInfo.setErrSnFlag("N");
        list.add(warehouseEntryInfo);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyObject(), anyObject())).thenReturn(sysLookupTypesDTO);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);


        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyObject(), anyObject(), anyObject())).thenReturn(Boolean.TRUE);
        PowerMockito.doNothing().when(PlanscheduleRemoteService.class ,"batchHandleCompleteQty", any());
        try {
            warehouseEntryInfoServiceImpl.receiveWarehouseEntryInfoBatch(list, "test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WAREHOUSEENTRYINFO_IS_LOCKED, e.getMessage());
        }


        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), Mockito.anyInt())).thenReturn(true);

        try {
            warehouseEntryInfoServiceImpl.receiveWarehouseEntryInfoBatch(list, "test");
        } catch (Exception e) {

        }

    }

    @Test
    public void rejectWarehouseEntryInfoBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");

        WarehouseEntryInfo warehouseEntryInfo1 = new WarehouseEntryInfo();
        warehouseEntryInfo1.setBillNo("RK552020071802642");
        warehouseEntryInfo1.setBillType("9");
        List<PsWipInfo> getListChildSn = new ArrayList<>();
        PsWipInfo dto = new PsWipInfo();
        dto.setSn("123131");
        getListChildSn.add(dto);
        warehouseEntryInfo1.setPsWipInfoList(getListChildSn);
        warehouseEntryInfo.setPsWipInfoList(getListChildSn);
        list.add(warehouseEntryInfo1);
        list.add(warehouseEntryInfo);

        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("123");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(any())).thenReturn(psWipInfoList);

        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), Mockito.anyInt())).thenReturn(true);

        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyString()
                , any(), any())).thenReturn(true);
        List<String> existList = new ArrayList<>();
        existList.add("Qwe");
        PowerMockito.when(warehouseEntryInfoRepository.getExistInforBillList(Mockito.anyList())).thenReturn(existList);
        try {
            warehouseEntryInfoServiceImpl.rejectWarehouseEntryInfoBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INFOR_BILL_PACKING_EXCEPTION, e.getMessage());
        }
        PowerMockito.when(warehouseEntryInfoRepository.getExistInforBillList(Mockito.anyList())).thenReturn(new ArrayList<>());
        try {
            warehouseEntryInfoServiceImpl.rejectWarehouseEntryInfoBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
        }

        ReflectionTestUtils.setField(warehouseEntryInfoServiceImpl, "checkErpStatus", true);
        List<String> erpMoveOrDoneErrorList = new ArrayList<>();
        PowerMockito.when(warehouseEntryInfoRepository.getErpMoveOrDoneErrorList(Mockito.anyList())).thenReturn(erpMoveOrDoneErrorList);
        try {
            warehouseEntryInfoServiceImpl.rejectWarehouseEntryInfoBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
        }

        erpMoveOrDoneErrorList.add("123");
        try {
            warehouseEntryInfoServiceImpl.rejectWarehouseEntryInfoBatch(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ERP_MOVE_OR_DONE_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void closeWarehouseEntryInfoBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        list.add(warehouseEntryInfo);

        List<PsWipInfoDTO> psList = new LinkedList<>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        a1.setSn("123");
        psList.add(a1);
        warehouseEntryInfo.setSnList(psList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("123");
        psWipInfoList.add(psWipInfo);
        warehouseEntryInfo.setPsWipInfoList(psWipInfoList);

        List<WarehouseEntryInfoDTO> warehouseEntryInfoDTOS = new LinkedList<>();
        WarehouseEntryInfoDTO w1 = new WarehouseEntryInfoDTO();
        w1.setSn("234");
        warehouseEntryInfoDTOS.add(w1);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(any())).thenReturn(warehouseEntryInfoDTOS);

        PowerMockito.when(psWipInfoService.getListByBatchSn(any())).thenReturn(psWipInfoList);

        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), Mockito.anyInt())).thenReturn(true);

        try {
            warehouseEntryInfoServiceImpl.closeWarehouseEntryInfoBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_STATUS_NOT_ALLOW_RECEIVE, e.getMessage());
        }
    }


    @Test
    public void closeWarehouseEntryInfoBatch1() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        list.add(warehouseEntryInfo);

        List<PsWipInfoDTO> psList = new LinkedList<>();
        PsWipInfoDTO a1 = new PsWipInfoDTO();
        a1.setSn("123");
        psList.add(a1);
        warehouseEntryInfo.setSnList(psList);
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("123");
        psWipInfoList.add(psWipInfo);
        warehouseEntryInfo.setPsWipInfoList(psWipInfoList);

        List<WarehouseEntryInfoDTO> warehouseEntryInfoDTOS = new LinkedList<>();
        WarehouseEntryInfoDTO w1 = new WarehouseEntryInfoDTO();
        w1.setSn("234");
        warehouseEntryInfoDTOS.add(w1);
        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(any())).thenReturn(warehouseEntryInfoDTOS);

        PowerMockito.when(psWipInfoService.getListByBatchSn(any())).thenReturn(psWipInfoList);

        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), Mockito.anyInt())).thenReturn(true);

        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyString()
                , any(), any())).thenReturn(true);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString()))
                .thenReturn(sysLookupTypesDTO);
        Assert.assertEquals(0,warehouseEntryInfoServiceImpl.closeWarehouseEntryInfoBatch(list));

    }

    @Test
    public void getMtlSecondaryInventories() throws Exception{

        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn("kk");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(anyString())).thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO)).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("[]");
        Assert.assertNull(warehouseEntryInfoServiceImpl.getMtlSecondaryInventories(new HashMap<>()));
    }

    @Test
    public void checkSn4WarehouseEntryInfo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        Map<String, Object> record = new HashMap<>();
        Map result = new HashMap();
        List<PsWipInfo> wipInfolist = new LinkedList<>();

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn("kk");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("[{\"lineCode\":\"123\"}]");

        PsWipInfo a1 = new PsWipInfo();
        a1.setCraftSection(BusinessConstant.OUT_WAREHOUSE);
        wipInfolist.add(a1);
        PowerMockito.when(psWipInfoService.getList(any()))
                .thenReturn(wipInfolist)
        ;
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.checkSn4WarehouseEntryInfo(record));

    }

    @Test
    public void saveWarehouseEntryInfo() throws Exception {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        List<PsWipInfo> psWipInfoList = new LinkedList<>();
        PsWipInfo p1 = new PsWipInfo();
        p1.setSn("123");
        psWipInfoList.add(p1);
        dto.setPsWipInfoList(psWipInfoList);
        dto.setBillType("2");
        dto.setTaskNo("ki");
        dto.setItemNo("o0");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        List<PsTask> treeList = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setTaskNo("ki");
        List<PsTask> childList = new LinkedList<>();
        PsTask a2 = new PsTask();
        a2.setTaskNo("45");
        a2.setItemNo("45");
        childList.add(a2);
        treeList.add(a1);
        a1.setSubChildList(childList);
        PowerMockito.when(planscheduleRemoteService.getSubTaskTreeByTaskNo(Mockito.anyList(), anyString()))
                .thenReturn(treeList)
        ;
        List<BBomDetailResponseDTO> bBomDetails = new LinkedList<>();
        BBomDetailResponseDTO b1 = new BBomDetailResponseDTO();
        b1.setItemCode("45");
        b1.setUsageCount(new BigDecimal("1"));
        bBomDetails.add(b1);
        PowerMockito.when(centerfactoryRemoteService.centerFactoryBBomInfoList(anyString()))
                .thenReturn(bBomDetails)
        ;

        List<WarehouseEntryDetail> childDetails = new LinkedList<>();
        WarehouseEntryDetail c1 = new WarehouseEntryDetail();
        c1.setTaskNo("45");
        c1.setCountQty(5);
        childDetails.add(c1);
        PowerMockito.when(warehouseEntryDetailRepository.queryDetailsCount(Mockito.anyList(), Mockito.anyList(),
                Mockito.anyList())).thenReturn(childDetails)
        ;

        PowerMockito.when(RedisHelper.setnx(anyString(), any(), Mockito.anyInt())).thenReturn(true);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfo(Mockito.any()))
                .thenReturn(new LinkedList<>())
        ;
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getProcessCode(Constant.OUTSOURCE))
                .thenReturn("#");


        PowerMockito.when(SpringContextUtil.getBean(MpConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);

        List<CtRouteDetailDTO> details = new LinkedList<>();
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(Arrays.asList(anyString())))
                .thenReturn(details)
        ;
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            if (e instanceof MesBusinessException) {
                String exMsgId = ((MesBusinessException) e).getExMsgId();

            }
        }
        dto.setBillType("10");
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SUBMIT_OUTSOURCE_FORBIDDEN_THREE.equals(e.getMessage()));
        }
        dto.setStockType("半成品K2库");
        dto.setBillType("1");
        dto.setBillTypeCode(new BigDecimal("44"));
        warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        dto.setBillType("2");
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CANNOT_SUBMIT_K2_BILL, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(), Mockito.any())).thenReturn(sysLookupTypesDTO);
        List<String> extraAttr = new ArrayList<>();
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        extraAttr.add("123123");
        PowerMockito.when(PlanscheduleRemoteService.getExtraAttr(Mockito.any(), Mockito.any())).thenReturn(extraAttr);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_EXIST_220_SN, e.getMessage());
        }
    }
    @Test
    public void saveWarehouseEntryInfo1() throws Exception {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        redisOpsValue.set("123", "123");
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn("123");
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfo(dto, "52", "2", "system");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_BATCH_IS_BEING_CHANGED, e.getMessage());
        }
    }
    @Test
    public void exportWarehouseInfo() throws Exception {
        WarehouseEntryInfoDTO dtoForm = new WarehouseEntryInfoDTO();
        dtoForm.setLpn("23434");
        List<SysLookupValuesDTO> sys = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupMeaning("1");
        a1.setDescriptionChin("2");
        a1.setLookupType(new BigDecimal("1048"));
        sys.add(a1);
        SysLookupValuesDTO a2 = new SysLookupValuesDTO();
        a2.setLookupMeaning("1");
        a2.setDescriptionChin("2");
        a2.setLookupType(new BigDecimal("1004097"));
        sys.add(a2);
        SysLookupValuesDTO a3 = new SysLookupValuesDTO();
        a3.setLookupMeaning("1");
        a3.setDescriptionChin("2");
        a3.setLookupType(new BigDecimal("1114"));
        sys.add(a3);
        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList())).thenReturn(sys);
        PowerMockito.when(EasyExcelFactory.writerSheet(0, Constant.WAREHOUSE_EXPORT_SHEET_NAME))
                .thenReturn(excelWriterSheetBuilder);
        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + Constant.CONCISE_DAILY_EXPORT_FILE_NAME;
        String filePath = FileUtils.tempPath + 1L+Constant.GANG+ fileName;
        PowerMockito.when(EasyExcelFactory.write(anyString(), any()))
                .thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
        PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);

        List<String> billNoList = Arrays.asList("RK123");
        PowerMockito.when(warehouseEntryInfoRepository.getBillNo(dtoForm)).thenReturn(billNoList);

        List<WarehouseEntryExportDTO> warehouseEntryExportDTOS = new LinkedList<>();
        PowerMockito.when(warehouseEntryInfoRepository.exportWarehouseInfo(any())).thenReturn(warehouseEntryExportDTOS);
        warehouseEntryInfoServiceImpl.exportWarehouseInfo(response,dtoForm);
        Assert.assertTrue(1 == 1);

        WarehouseEntryExportDTO b1 = new WarehouseEntryExportDTO();
        b1.setBillStatus("1");
        b1.setErpStatus("1");
        b1.setBillType("1");
        warehouseEntryExportDTOS.add(b1);

        WarehouseEntryExportDTO b2 = new WarehouseEntryExportDTO();
        b2.setBillStatus("1");
        b2.setErpStatus("1");
        b2.setBillType("1");
        warehouseEntryExportDTOS.add(b2);
        warehouseEntryInfoServiceImpl.exportWarehouseInfo(response,dtoForm);
        Assert.assertTrue(1 == 1);


        SysLookupValuesDTO a4 = new SysLookupValuesDTO();
        a4.setLookupMeaning("1");
        a4.setDescriptionChin("2");
        a4.setLookupType(new BigDecimal("1115"));
        a4.setAttribute1(WAREHOUSE_EXPORT);
        sys.add(a4);
        warehouseEntryInfoServiceImpl.exportWarehouseInfo(response,dtoForm);
        Assert.assertTrue(1 == 1);
    }

    /* Started by AICoder, pid:e465e628e50e4d08a3cc290d796aa1cc */
    @Test
    public void exportWarehouseInfoHead() throws Exception {
        WarehouseEntryInfoDTO dtoForm = new WarehouseEntryInfoDTO();
        dtoForm.setPropsList(new ArrayList<>());
        dtoForm.setTitleList(new ArrayList<>());
        List<SysLookupValuesDTO> sys = new LinkedList<>();
        SysLookupValuesDTO a1 = new SysLookupValuesDTO();
        a1.setLookupMeaning("1");
        a1.setDescriptionChin("2");
        a1.setLookupType(new BigDecimal("1048"));
        sys.add(a1);
        SysLookupValuesDTO a2 = new SysLookupValuesDTO();
        a2.setLookupMeaning("1");
        a2.setDescriptionChin("2");
        a2.setLookupType(new BigDecimal("1004097"));
        sys.add(a2);
        SysLookupValuesDTO a3 = new SysLookupValuesDTO();
        a3.setLookupMeaning("1");
        a3.setDescriptionChin("2");
        a3.setLookupType(new BigDecimal("1114"));
        sys.add(a3);

        List<WarehouseEntryExportDTO> warehouseEntryExportDTOS = new LinkedList<>();
        WarehouseEntryExportDTO b1 = new WarehouseEntryExportDTO();
        b1.setBillStatus("1");
        b1.setErpStatus("1");
        b1.setBillType("1");
        warehouseEntryExportDTOS.add(b1);
        WarehouseEntryExportDTO b2 = new WarehouseEntryExportDTO();
        b2.setBillStatus("1");
        b2.setErpStatus("1");
        b2.setBillType("1");
        b2.setZjSubcardFlag("Y");
        warehouseEntryExportDTOS.add(b2);
        PowerMockito.when(warehouseEntryInfoRepository.exportWarehouseInfoHead(any()))
                .thenReturn(warehouseEntryExportDTOS);

        PowerMockito.when(BasicsettingRemoteService.getBatchSysValueByCode(Mockito.anyList()))
                .thenReturn(sys)
        ;
        PowerMockito.when(EasyExcelFactory.writerSheet(0, Constant.WAREHOUSE_EXPORT_SHEET_NAME))
                .thenReturn(excelWriterSheetBuilder);

        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
        PowerMockito.when(EasyExcelFactory.write(response.getOutputStream()))
                .thenReturn(write);
        PowerMockito.when(write.head(anyList())).thenReturn(excelWriterBuilder);
        PowerMockito.when(excelWriterBuilder.excelType(ExcelTypeEnum.XLSX)).thenReturn(excelWriterBuilder);
        PowerMockito.when(excelWriterBuilder.build()).thenReturn(excelWriter);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
            Assert.assertEquals(MessageId.TYPE_IS_EMPTY, e.getMessage());
        }
        dtoForm.setBillType("2");
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_THAN_THREE_MONTH, e.getMessage());
        }

        dtoForm.setBillNo("2");
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_THAN_THREE_MONTH, e.getMessage());
        }

        dtoForm.setInforBillNo("2");
        dtoForm.setBillNo(null);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_THAN_THREE_MONTH, e.getMessage());
        }

        dtoForm.setTaskNo("2");
        dtoForm.setInforBillNo(null);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setProdplanId("2");
        dtoForm.setTaskNo(null);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setSn("2");
        dtoForm.setProdplanId("2");
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setProdplanId(null);
        dtoForm.setSn(null);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setLpn("2");
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setLpn(null);
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setCreateDateStart("2022-02-01 00:00:00");
        dtoForm.setCreateDateEnd("2023-03-23 23:59:59");
        try{
            warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
        }catch (Exception e){
        }

        dtoForm.setCreateDateStart("2023-02-01 00:00:00");
        warehouseEntryInfoServiceImpl.exportWarehouseInfoHead(response, dtoForm);
    }

    @Test
    public void setParentProdplanNo() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setProdplanId("1111");
        PowerMockito.when(planscheduleRemoteService.getPsTask(anyMap()))
                .thenReturn(null);
        try {
            Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setParentProdplanNo", warehouseEntryInfo, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_TASK_OF_PRODPLANNO_NOT_EXIST, e.getMessage());
        }

        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTasks.add(psTask);
        PowerMockito.when(planscheduleRemoteService.getPsTask(anyMap()))
                .thenReturn(psTasks);
        try {
            Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setParentProdplanNo", warehouseEntryInfo, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_TASK_OF_PRODPLANNO_NOT_EXIST, e.getMessage());
        }

        psTask.setZbjprodplanNo("222");
        PowerMockito.when(planscheduleRemoteService.getPsTask(anyMap()))
                .thenReturn(psTasks);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setParentProdplanNo", warehouseEntryInfo, dto);
        Assert.assertEquals("222", warehouseEntryInfo.getParentProdplanNo());
    }

    @Test
    public void extracted() {
        SysLookupValuesDTO valueByTypeCode=new SysLookupValuesDTO();
        valueByTypeCode.setLookupType(new BigDecimal(5981));
        Map<String, String> carryAccountMap=new HashMap<>();
        Map<String, String> outboundTypeMap=new HashMap<>();
        Map<String, String> boardPackErpSucceNodeMap=new HashMap<>();
        warehouseEntryInfoServiceImpl.extracted(valueByTypeCode, carryAccountMap,outboundTypeMap,boardPackErpSucceNodeMap);
        Assert.assertTrue(true);

        valueByTypeCode.setLookupType(new BigDecimal(5980));
        valueByTypeCode.setLookupMeaning("0");
        valueByTypeCode.setDescriptionChin("测试");
        warehouseEntryInfoServiceImpl.extracted(valueByTypeCode, carryAccountMap,outboundTypeMap,boardPackErpSucceNodeMap);
        Assert.assertTrue(true);

        valueByTypeCode.setLookupType(new BigDecimal(5974));
        valueByTypeCode.setLookupMeaning("0");
        valueByTypeCode.setDescriptionChin("测试");
        warehouseEntryInfoServiceImpl.extracted(valueByTypeCode, carryAccountMap,outboundTypeMap,boardPackErpSucceNodeMap);
        Assert.assertTrue(true);

        valueByTypeCode.setLookupType(new BigDecimal(5979));
        valueByTypeCode.setLookupMeaning("0");
        valueByTypeCode.setDescriptionChin("测试");
        warehouseEntryInfoServiceImpl.extracted(valueByTypeCode, carryAccountMap,outboundTypeMap,boardPackErpSucceNodeMap);
        Assert.assertTrue(true);
    }

    @Test
    public void getlookupDesc() throws Exception{
        List<SysLookupValuesDTO> list = new ArrayList<>();
        String lookupType = "SomeType";
        String attribute1 = "SomeCode";
        try {
            warehouseEntryInfoServiceImpl.getlookupDesc(list, lookupType, attribute1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void getlookupDesc_noMatch() throws Exception{
        List<SysLookupValuesDTO> list = new ArrayList<>();
        SysLookupValuesDTO dto1 = new SysLookupValuesDTO();
        BigDecimal type1=new BigDecimal(5973);
        dto1.setLookupType(type1);
        dto1.setAttribute1("10");
        dto1.setDescriptionChin("Meaning1");
        list.add(dto1);

        String lookupType = "Type2";
        String attribute1 = "Code2";
        try {
            warehouseEntryInfoServiceImpl.getlookupDesc(list, lookupType, attribute1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void getlookupDesc_match() throws Exception{
        List<SysLookupValuesDTO> list = new ArrayList<>();
        SysLookupValuesDTO dto1 = new SysLookupValuesDTO();
        BigDecimal type1=new BigDecimal(5973);
        dto1.setLookupType(type1);
        dto1.setAttribute1("12");
        dto1.setDescriptionChin("Meaning1");
        list.add(dto1);

        String lookupType = "5973";
        String attribute1 = "12";
        assertEquals("Meaning1", warehouseEntryInfoServiceImpl.getlookupDesc(list, lookupType, attribute1));
    }

    @Test
    public void printWarehouseDetail() throws Exception {
        try {
            warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRINT_INSTOCK_DATA_ERROR, e.getMessage());
        }

        List<WarehouseEntryInfoDTO> list=new ArrayList<>();
        WarehouseEntryInfoDTO dto=new WarehouseEntryInfoDTO();
        dto.setBillNo("3244");
        dto.setProdplanId("3244");
        dto.setIsLead("10");
        dto.setCreateBy("10346719");
        dto.setItemNo("234");
        list.add(dto);
        PowerMockito.when(warehouseEntryInfoRepository.printWarehouseDetail(anyString())).thenReturn(list);
        try {
            warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_IS_NOT_EXITS, e.getMessage());
        }

        List<PsTask> psTaskList=new ArrayList<>();
        PsTask psTask=new PsTask();
        psTask.setStock("2343");
        psTask.setProdAddress("1");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList);
        List<SysLookupValuesDTO> sysLookupValuesDTOS= new ArrayList<>();
        SysLookupValuesDTO valuesDTO=new SysLookupValuesDTO();
        valuesDTO.setAttribute1("10");
        valuesDTO.setLookupType(new BigDecimal("1036"));
        valuesDTO.setDescriptionChin("测试");
        sysLookupValuesDTOS.add(valuesDTO);
        SysLookupValuesDTO valuesDTO2=new SysLookupValuesDTO();
        valuesDTO2.setAttribute1("1");
        valuesDTO2.setLookupType(new BigDecimal("5973"));
        valuesDTO2.setDescriptionChin("测试");
        sysLookupValuesDTOS.add(valuesDTO2);
        try {
            warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        PowerMockito.when(BasicsettingRemoteService.getLookupTypesByValue(any())).thenReturn(sysLookupValuesDTOS);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfoByUserId(any())).thenReturn(null);
        warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        Assert.assertNotNull(list);

        List<WarehouseEntryInfoDTO> list1=new ArrayList<>();
        dto.setIsLead("10");
        list1.add(dto);
        PowerMockito.when(warehouseEntryInfoRepository.printWarehouseDetail(anyString())).thenReturn(list1);
        List<PsTask> psTaskList1=new ArrayList<>();
        psTask.setStock("电源模块转材料");
        psTaskList1.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList1);
        PowerMockito.when(BasicsettingRemoteService.getLookupTypesByValue(any())).thenReturn(sysLookupValuesDTOS);
        PowerMockito.when(datawbRemoteService.getItemTransfer(any())).thenReturn("234");
        BsPubHrvOrgId bsPubHrvOrgId=new BsPubHrvOrgId();
        bsPubHrvOrgId.setUserName("李志标");
        bsPubHrvOrgId.setUserId("10346719");
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfoByUserId(any())).thenReturn(bsPubHrvOrgId);
        warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        Assert.assertNotNull(list);

        List<WarehouseEntryInfoDTO> list2=new ArrayList<>();
        dto.setIsLead("10");
        list2.add(dto);
        PowerMockito.when(warehouseEntryInfoRepository.printWarehouseDetail(anyString())).thenReturn(list2);
        List<PsTask> psTaskList2=new ArrayList<>();
        psTask.setStock("23434电源模块转材料324");
        psTaskList2.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdplanIdList(anyList())).thenReturn(psTaskList2);
        PowerMockito.when(BasicsettingRemoteService.getLookupTypesByValue(any())).thenReturn(sysLookupValuesDTOS);
        PowerMockito.when(datawbRemoteService.getItemTransfer(any())).thenReturn("234");
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfoByUserId(any())).thenReturn(bsPubHrvOrgId);
        warehouseEntryInfoServiceImpl.printWarehouseDetail("3244");
        Assert.assertNotNull(list);
    }
    /* Ended by AICoder, pid:e465e628e50e4d08a3cc290d796aa1cc */

    @Test
    public void checkCondition() throws Exception {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setBillType("11");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setBillType(BILL_TYPE_SON_CARD);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setBillType(BILL_TYPE_OUT_CARD);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setTaskNo("123");
        dto.setItemNo("321");
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setCommitedQty(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setCommitedQty(BigDecimal.ONE);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
        dto.setPsWipInfoList(new ArrayList() {{
            add(new PsWipInfo() {{
                setSn("123321");
            }});
        }});
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"checkCondition", dto);
    }

    @Test
    public void extractedSub() throws Exception {
        List<PsTask> treeList =  new ArrayList() {{
            add(new PsTask() {{
                setSubChildList(new ArrayList<>());
            }});
        }};
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setPsWipInfoList(new ArrayList() {{
            add(new PsWipInfo() {{
                setSn("123");
            }});
        }});
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"extractedSub", dto, treeList, new ArrayList<>());
        dto.setPsWipInfoList(null);
        dto.setCommitedQty(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl,"extractedSub", dto, treeList, new ArrayList<>());
    }

    @Test
    public void setMbomTest() throws Exception {
        WarehouseEntryInfo dto1 = new WarehouseEntryInfo();
        dto1.setProdplanId("7654321");
        List<WarehouseEntryInfo> list1 = new ArrayList<>();
        WarehouseEntryExportDTO dto = new WarehouseEntryExportDTO();
        dto.setProdplanId("7654321");
        List<WarehouseEntryExportDTO> list = new ArrayList<>();
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234567");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        list.add(dto);
        list1.add(dto1);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setMBomProductCodeExport", list);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setMBomProductCode", list1);
        dto1.setProdplanId("76543211");
        dto.setProdplanId("76543211");
        Assert.assertEquals("7654321", list.get(0).getMBomProductCode());
        Assert.assertEquals("7654321", list1.get(0).getMBomProductCode());
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setMBomProductCodeExport", list);
        Whitebox.invokeMethod(warehouseEntryInfoServiceImpl, "setMBomProductCode", list1);
    }
}