package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

@PrepareForTest({CommonUtils.class, CrafttechRemoteService.class,ObtainRemoteServiceDataUtil.class,
        RedisHelper.class, MicroServiceRestUtil.class,DatawbRemoteService.class,
        BasicsettingRemoteService.class,ProductionDeliveryRemoteService.class})
public class PsWipInfoServiceImplTwoTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl psWipInfoServiceImpl;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Mock
    private RedisLock redisLock;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private WipRepairSnService wipRepairSnService;

    @Mock
    private PsScanHistoryService psScanHistoryService;

    @Mock
    BsWorkTimeSectionService bsWorkTimeSectionService;

    @Mock
    PsOutputInfoService psOutputInfoService;

    @Mock
    PsBarcodeControlInfoService psBarcodeControlInfoService;
    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private PsCommonScanService psCommonScanService;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Test
    public void getWipInfoByProdPlanIdAndLastUpdateDate() throws Exception {
        List<String> list= new ArrayList<>();
        psWipInfoServiceImpl.getWipInfoByProdPlanIdAndLastUpdateDate(list,new Date(),new Date(),1);
        list.add("2");
        Assert.assertNotNull(psWipInfoServiceImpl.getWipInfoByProdPlanIdAndLastUpdateDate(list,new Date(),new Date(),1));
    }
    @Test
    public void getMaxSnDate() throws Exception {
        List<String> list= new ArrayList<>();
        psWipInfoServiceImpl.getMaxSnDate(new Date(),list);
        list.add("2");
        Assert.assertNotNull(psWipInfoServiceImpl.getMaxSnDate(new Date(),list));
    }

    @Test
    public void pmScanNew() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(MpConstant.LOOKUP_TYPE_1890002));
        sysLookupValuesDTO.setLookupMeaning(Constant.FLAG_N);
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(sysLookupValuesDTOList);
        PmScanConditionDTO dto = new PmScanConditionDTO();
        dto.setWorkOrderNo("7778889-DIP5503");
        dto.setSn("777888800001");
        PowerMockito.spy(CommonUtils.class);
        PowerMockito.doReturn("双语返回固定值").when(CommonUtils.class);
        CommonUtils.getLmbMessage(Mockito.anyObject());
        Assert.assertNull(psWipInfoServiceImpl.pmScanNew(dto));
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        redisOpsValue.set("123", "123");
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn("123");
        try {
            psWipInfoServiceImpl.pmScanNew(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_BATCH_IS_BEING_CHANGED, e.getMessage());
        }

    }
    @Test
    public void getStringFromMap() throws Exception {
        Assert.assertNotNull(psWipInfoServiceImpl.getStringFromMap(new HashMap<String,Object>()));
    }

    @Test
    public void checkFirstProcessGroupInRoute() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{
                    setBo(Lists.newArrayList(new CtRouteInfoDTO(){{
                        setListDetail(Lists.newArrayList(new CtRouteDetailDTO(){{
                            setCraftSection("1");
                            setNextProcess("1");
                        }}));
                    }}));
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        psWipInfoServiceImpl.checkFirstProcessGroupInRoute(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}});
        psWipInfoServiceImpl.checkFirstProcessGroupInRoute(new FlowControlInfoDTO(){{
            setEntityPlanBasic(new PsEntityPlanBasicDTO(){{
                setProcessGroup("1");
                setRouteId("1");
            }});
        }});
        psWipInfoServiceImpl.checkFirstProcessGroupInRoute(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO(){{setProcessGroup("2");}});}});

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{
                    setBo(Lists.newArrayList());
                }}));
        psWipInfoServiceImpl.checkFirstProcessGroupInRoute(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}});

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{
                    setBo(Lists.newArrayList(new CtRouteInfoDTO(){{
                        setListDetail(Lists.newArrayList());
                    }}));
                }}));
        Assert.assertNull(psWipInfoServiceImpl.checkFirstProcessGroupInRoute(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}}));
    }

    @Test
    public void checkFirstProcess() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(),any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
                    setCraftSection("1");
                    setNextProcess("1");
                    setMappingProcess("1");
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkFirstStation() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(),any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
                    setCraftSection("1");
                    setNextProcess("1");
                    setMappingProcess("1");
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkWipProcessNew() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(),any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
                    setCraftSection("1");
                    setNextProcess("1");
                    setMappingProcess("1");
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkWipStationNew() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        List<CtRouteInfoDTO> ctList = new LinkedList<>();
        CtRouteInfoDTO c1 = new CtRouteInfoDTO();
        ctList.add(c1);
        List<CtRouteDetailDTO> listDetail = new LinkedList<>();
        CtRouteDetailDTO ct1 = new CtRouteDetailDTO();
        ct1.setNextProcess("3");
        listDetail.add(ct1);
        c1.setListDetail(listDetail);
        PowerMockito.when(flowControlCommonService.getScanProcessNew(Mockito.any(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(ctList);

        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(),any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
                    setCraftSection("1");
                    setNextProcess("1");
                    setMappingProcess("1");
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertPsWipInfoByScanBatch() {
        psWipInfoServiceImpl.insertPsWipInfoByScanBatch(
                Lists.newArrayList(new FlowControlInfoDTO(){{setWipInfo(new PsWipInfo());setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                        new FlowControlInfoDTO(){{setaSourceSysName("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}}), 1);
        Assert.assertEquals(0, psWipInfoServiceImpl.insertPsWipInfoByScanBatch(Lists.newArrayList(), 1));
    }

    @Test
    public void setProcessName() throws Exception {
        psWipInfoServiceImpl.setProcessName(Lists.newArrayList(new PsWipInfoDTO()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updatePsWipInfoByScanBatch() {
        psWipInfoServiceImpl.updatePsWipInfoByScanBatch(
                Lists.newArrayList(new FlowControlInfoDTO(){{setWipInfo(new PsWipInfo());setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                        new FlowControlInfoDTO(){{setWipInfo(new PsWipInfo());setaSourceSysName("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}}), 1);
        Assert.assertEquals(0,psWipInfoServiceImpl.updatePsWipInfoByScanBatch(
                Lists.newArrayList(), 1));
    }

    @Test
    public void setCreateByName() throws IOException, RouteException {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(anyString()))
                .thenReturn(Lists.newArrayList(new BsPubHrvOrgId(){{setUserFullId("1");}},new BsPubHrvOrgId(){{setUserFullId("2");}}));
        psWipInfoServiceImpl.setCreateByName(
                Lists.newArrayList(new PsWipInfoSnQueryDTO(){{setCreateBy("1");}}));
        psWipInfoServiceImpl.setCreateByName(
                Lists.newArrayList());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void setContainerCodeEx() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList());}}));
        psWipInfoServiceImpl.setContainerCodeEx(
                Lists.newArrayList(),
                Lists.newArrayList());
        psWipInfoServiceImpl.setContainerCodeEx(
                Lists.newArrayList(new ContainerContentInfoDTO(){{setCreateBy("1");}}), Lists.partition(Lists.newArrayList(new PsWipInfoSnQueryDTO(){{setCreateBy("1");}}), 1));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void getWoEx() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList());}}));
        psWipInfoServiceImpl.getWoEx(new PmSubmitConditionDTO(),
                new FlowControlInfoDTO());
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList(new PsEntityPlanBasicDTO()));}}));
        Assert.assertTrue(psWipInfoServiceImpl.getWoEx(new PmSubmitConditionDTO(){{setWorkOrderNo("1");}},
                new FlowControlInfoDTO()));
    }

    @Test
    public void getBarcodeEx() {
        psWipInfoServiceImpl.getBarcodeEx(
                Lists.newArrayList(new BarcodeDTO()),
                Lists.newArrayList(new BarcodeDTO()),
                Lists.newArrayList(new BarcodeDTO()));
        PowerMockito.when(wipRepairSnService.batchGetRepairSn(any(), any(), any()))
                .thenReturn(Lists.newArrayList(new WipRepairSn(){{setSn("1");}}));
        Assert.assertNotNull(psWipInfoServiceImpl.getBarcodeEx(
                Lists.newArrayList(new BarcodeDTO(){{setBarcode("1");}},new BarcodeDTO(){{setBarcode("2");}}),
                Lists.newArrayList(new BarcodeDTO()),
                Lists.newArrayList(new BarcodeDTO())));
    }

    @Test
    public void checkBatchFlowControledEx() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        psWipInfoServiceImpl.checkBatchFlowControledEx(
                new FlowControlInfoDTO(){{setCurrProcessCode("1");}},
                Lists.newArrayList(new CtRouteInfoDTO(){{setListDetail(Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}},
                        new CtRouteDetailDTO(){{setNextProcess("2");}}));}})
        );

        psWipInfoServiceImpl.checkBatchFlowControledEx(
                new FlowControlInfoDTO(){{setCurrProcessCode("3");}},
                Lists.newArrayList(new CtRouteInfoDTO(){{setListDetail(Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}},
                        new CtRouteDetailDTO(){{setNextProcess("2");}}));}})
        );

        psWipInfoServiceImpl.checkBatchFlowControledEx(
                new FlowControlInfoDTO(){{setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList()
        );
        Assert.assertNotNull(psWipInfoServiceImpl.checkBatchFlowControledEx(
                new FlowControlInfoDTO(){{setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteInfoDTO(){{setListDetail(Lists.newArrayList());}})
        ));
    }

    @Test
    public void checkTestControl() throws IOException, RouteException {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        psWipInfoServiceImpl.checkTestControl(
                new FlowControlInfoDTO(){{
                    setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setWipInfo(new PsWipInfo(){{setCurrProcessCode("1");}});
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        psWipInfoServiceImpl.checkTestControl(
                new FlowControlInfoDTO(){{
                    setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setWipInfo(new PsWipInfo(){{setCurrProcessCode("2");}});
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        Assert.assertNotNull(psWipInfoServiceImpl.checkTestControl(
                new FlowControlInfoDTO(){{
                    setWipInfo(new PsWipInfo());
                    setCurrProcessCode("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}})));
    }

    @Test
    public void checkTestStationControl() throws IOException, RouteException {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        psWipInfoServiceImpl.checkTestStationControl(
                new FlowControlInfoDTO(){{
                    setWorkStationType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setEntityPlanTest(new PsEntityPlanTestDTO(){{setMaxTestCount(new BigDecimal(1));}});
                    setWipInfo(new PsWipInfo(){{setWorkStation("2");setOpeTimes(new BigDecimal(1));}});
                    setWorkStation("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        psWipInfoServiceImpl.checkTestStationControl(
                new FlowControlInfoDTO(){{
                    setWorkStationType(MpConstant.PROCESS_TYPE_AUTOTEST);
                    setEntityPlanTest(new PsEntityPlanTestDTO(){{setMaxTestCount(new BigDecimal(1));}});
                    setWipInfo(new PsWipInfo(){{setStatus("N");setWorkStation("1");setOpeTimes(new BigDecimal(1));}});
                    setWorkStation("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}}));
        Assert.assertNotNull(psWipInfoServiceImpl.checkTestStationControl(
                new FlowControlInfoDTO(){{
                    setEntityPlanTest(new PsEntityPlanTestDTO(){{setMaxTestCount(new BigDecimal(1));}});
                    setWipInfo(new PsWipInfo(){{setWorkStation("2");setOpeTimes(new BigDecimal(1));}});
                    setWorkStation("1");setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Lists.newArrayList(new CtRouteDetailDTO(){{setNextProcess("1");}})));
    }

    @Test
    public void batchSaveWipInfoForProcessTransfer() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class,BasicsettingRemoteService.class,MicroServiceRestUtil.class);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString()))
                .thenReturn(Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("1");setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_BATCH_SIZE_TWO));}}));
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList(
                        new CtRouteDetailDTO(){{setNextProcess("1");}}));}}));

        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE);

        try {
            psWipInfoServiceImpl.batchSaveWipInfoForProcessTransfer(
                    Lists.newArrayList(new FlowControlConditionDTO()), new FlowControlInfoDTO(), "1", "1");
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.INSERT_WIP_SCAN_HISTORY_FAILURE, returnMessage.getMessage());
        }

    }

    @Test
    public void setOutPutInfoEx() {
        psWipInfoServiceImpl.setOutPutInfoEx(
                new FlowControlInfoDTO(){{setErrorCode("1");}},
                Constant.SN, new PsOutputInfo());
        psWipInfoServiceImpl.setOutPutInfoEx(
                new FlowControlInfoDTO(),
                Constant.SN, new PsOutputInfo());
        psWipInfoServiceImpl.setOutPutInfoEx(
                new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}},
                Constant.WORKORDER, new PsOutputInfo());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void writeOutPutBatch() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        PowerMockito.when(psOutputInfoService.insertPsOutputInfoSelective(any())).thenReturn(1);
        PowerMockito.when(bsWorkTimeSectionService.selectBsWorkTimeSectionByLineWorkShopAndFactory(any(), any(), any())).thenReturn(new BsWorkTimeSectionDTO());
        psWipInfoServiceImpl.writeOutPutBatch(
                Lists.newArrayList(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}}),
                "1", 1, 1);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.FAILED_TO_UPDATE_PERIOD_OUTPUT_INFORMATION);
        PowerMockito.when(psOutputInfoService.getList(any())).thenReturn(Lists.newArrayList(new PsOutputInfo()));
        try {
            psWipInfoServiceImpl.writeOutPutBatch(
                    Lists.newArrayList(new FlowControlInfoDTO(){{setEntityPlanBasic(new PsEntityPlanBasicDTO());}}),
                    "1", 1, 1);
        }catch (Exception returnMessage){
            Assert.assertEquals(MessageId.FAILED_TO_UPDATE_PERIOD_OUTPUT_INFORMATION, returnMessage.getMessage());
        }

    }

    @Test
    public void getWarehouseEntryFlowInfoEx() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("");
        psWipInfoServiceImpl.getWarehouseEntryFlowInfoEx(new WarehouseEntryFlowInfoDTO(),
                new FlowControlInfoDTO());
        psWipInfoServiceImpl.getWarehouseEntryFlowInfoEx(new WarehouseEntryFlowInfoDTO(),
                new FlowControlInfoDTO(){{
                    setResultType(Constant.FAIL);}});
        psWipInfoServiceImpl.getWarehouseEntryFlowInfoEx(new WarehouseEntryFlowInfoDTO(),
                new FlowControlInfoDTO(){{
                    setWipInfo(new PsWipInfo());
                    setResultType(Constant.FAIL);}});
        psWipInfoServiceImpl.getWarehouseEntryFlowInfoEx(new WarehouseEntryFlowInfoDTO(),
                new FlowControlInfoDTO(){{
                    setWipInfo(new PsWipInfo(){{setErrorCode("1");}});
                    setResultType(Constant.FAIL);}});
        Assert.assertNotNull(psWipInfoServiceImpl.getWarehouseEntryFlowInfoEx(new WarehouseEntryFlowInfoDTO(),
                new FlowControlInfoDTO(){{
                    setEntityPlanTest(new PsEntityPlanTestDTO(){{setIsOneStageProd(Constant.FLAG_N);}});
                    setWipInfo(new PsWipInfo(){{setErrorCode("1");}});
                    setResultType(Constant.FAIL);}}));
    }

    @Test
    public void checkWipProcessNew2() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        PowerMockito.when(CrafttechRemoteService.selectCtProcessMapping(any(), any(), any()))
                .thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
                    setCraftSection("1");
                    setNextProcess("1");
                    setMappingProcess("1");
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void checkWipStationNew2() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getScanProcessNew(any()))
                .thenReturn(Lists.newArrayList(new CtRouteInfoDTO(){{
                    setListDetail(Lists.newArrayList(
                            new CtRouteDetailDTO(){{
                                setCraftSection("1");
                                setNextProcess("1");
                            }},
                            new CtRouteDetailDTO(){{
                                setCraftSection("2");
                                setNextProcess("2");
                                setIsUnnecessary("Y");
                            }}));
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(CommonUtils.getLmbMessage(any(), anyString())).thenReturn("1");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void deleteWipInfoBysn() throws Exception {
        List<String> snList = new ArrayList<>();
        PowerMockito.when(psWipInfoRepository.deleteWipInfoBysn(any())).thenReturn(1);
        psWipInfoServiceImpl.deleteWipInfoBysn(snList);
        snList.add("111");
        Assert.assertEquals(1,psWipInfoServiceImpl.deleteWipInfoBysn(snList));
    }

    @Test
    public void snQueryPageByLpn () throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class,CrafttechRemoteService.class,BasicsettingRemoteService.class,ObtainRemoteServiceDataUtil.class);
        PsWipInfoSnQueryDTO dtoForm = new PsWipInfoSnQueryDTO();
        dtoForm.setLpn("123");
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContentPage(Mockito.any())).thenReturn(new Page<>());
        psWipInfoServiceImpl.snQueryPage(dtoForm);

        Page<ContainerContentInfoSnQueryDTO> contentInfoPage = new Page<>();
        List<ContainerContentInfoSnQueryDTO> contentInfoList = new ArrayList<>();
        ContainerContentInfoSnQueryDTO dto = new ContainerContentInfoSnQueryDTO();
        dto.setLpn("123");
        dto.setEntityIdentification("123123");
        contentInfoList.add(dto);
        contentInfoPage.setRows(contentInfoList);
        PowerMockito.when(ProductionDeliveryRemoteService.getPageContainerContentPage(Mockito.any())).thenReturn(contentInfoPage);
        PowerMockito.when(psWipInfoRepository.getWipInfoBatch(Mockito.any())).thenReturn(new ArrayList<>());
        psWipInfoServiceImpl.snQueryPage(dtoForm);

        List<PsWipInfo> list = new ArrayList<>();
        PsWipInfo info = new PsWipInfo();
        info.setSn("123123");
        list.add(info);
        PsWipInfo info1 = new PsWipInfo();
        info1.setLpn("123123");
        list.add(info1);
        PowerMockito.when(psWipInfoRepository.getWipInfoBatch(Mockito.any())).thenReturn(list);
        psWipInfoServiceImpl.snQueryPage(dtoForm);
        info.setCurrProcessCode("p1");
        info.setWorkStation("w1");
        info.setCurrProcessCode("p2");
        info.setWorkStation("w2");
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.any())).thenReturn(new ArrayList<>());
        psWipInfoServiceImpl.snQueryPage(dtoForm);

        List<BSProcess> process = new ArrayList<>();
        BSProcess bs = new BSProcess();
        bs.setProcessCode("p1");
        bs.setProcessName("p1Name");
        BSProcess bs1 = new BSProcess();
        bs.setProcessCode("w1");
        bs.setProcessName("w1Name");
        process.add(bs);
        process.add(bs1);
        PowerMockito.when(CrafttechRemoteService.getProcessByProList(Mockito.any())).thenReturn(process);
        psWipInfoServiceImpl.snQueryPage(dtoForm);

        info.setLineCode("line1");
        info1.setLineCode("line1");
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(Mockito.any())).thenReturn(new HashMap<>());
        psWipInfoServiceImpl.snQueryPage(dtoForm);

        Map<String, String> lineMap = new HashMap<>();
        lineMap.put("line1", "name1");
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(Mockito.any())).thenReturn(lineMap);
        Assert.assertNotNull(psWipInfoServiceImpl.snQueryPage(dtoForm));

        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyString())).thenReturn(new ArrayList<>());



    }


    @Test
    public void snQueryPageNoLpn () throws Exception {
        PsWipInfoSnQueryDTO dtoForm = new PsWipInfoSnQueryDTO();
        psWipInfoServiceImpl.snQueryPage(dtoForm);
        StringBuilder sb = new StringBuilder();
        sb.append("sn1");
        for (int i = 0; i < 101; i++) {
            sb.append(",").append(i);
        }
        dtoForm.setSn(sb.toString());
        try {
            psWipInfoServiceImpl.snQueryPage(dtoForm);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SN_MORE_THAN_100.equals(e.getMessage()));
        }
        dtoForm.setSn("123321");
        dtoForm.setSort("currProcessName");
        dtoForm.setPage(0);
        dtoForm.setRows(0);
        Assert.assertNotNull(psWipInfoServiceImpl.snQueryPage(dtoForm));
        PowerMockito.when(psWipInfoRepository.getPage(Mockito.any())).thenReturn(new ArrayList<>());
    }
    /* Started by AICoder, pid:s17f1a87397ddba140d10b7170d02b434c55724f */

    @Test
    public void setMBomOfPsWipInfoSnQueryDTOTest() throws Exception {
        Whitebox.invokeMethod(psWipInfoServiceImpl, "setMBomOfPsWipInfoSnQueryDTO", null);
        Assert.assertTrue(1==1);
        List<PsWipInfoSnQueryDTO> list = new ArrayList<>();
        PsWipInfoSnQueryDTO entity = new PsWipInfoSnQueryDTO();
        entity.setAttribute1("1234567");
        list.add(entity);
        PsWipInfoSnQueryDTO entity1 = new PsWipInfoSnQueryDTO();
        entity1.setAttribute1("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(psWipInfoServiceImpl, "setMBomOfPsWipInfoSnQueryDTO", list);
        Assert.assertTrue(list.get(0).getMBom().equals("test"));
        Assert.assertTrue(list.get(1).getMBom().equals("itemNo"));
    }
    @Test
    public void setMBomOfPsWipInfoDTOTest() throws Exception {
        Whitebox.invokeMethod(psWipInfoServiceImpl, "setMBomOfPsWipInfoDTO", null);
        Assert.assertTrue(1==1);
        List<PsWipInfoDTO> list = new ArrayList<>();
        PsWipInfoDTO entity = new PsWipInfoDTO();
        entity.setAttribute1("1234567");
        list.add(entity);
        PsWipInfoDTO entity1 = new PsWipInfoDTO();
        entity1.setAttribute1("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(psWipInfoServiceImpl, "setMBomOfPsWipInfoDTO", list);
        Assert.assertTrue(list.get(0).getMBom().equals("test"));
        Assert.assertTrue(list.get(1).getMBom().equals("itemNo"));
    }

    /* Ended by AICoder, pid:s17f1a87397ddba140d10b7170d02b434c55724f */
}
