package com.zte.test;

import com.zte.application.impl.WipDailyStatisticServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.WipDailyStatisticReportRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashMap;

@PrepareForTest({RedisHelper.class})
public class WipDailyStatisticServiceImpl1Test extends PowerBaseTestCase {
    @InjectMocks
    private WipDailyStatisticServiceImpl service;

    @Mock
    private RedisLock redisLock;

    @Mock
    WipDailyStatisticReportRepository repository;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Test
    public void dailyReportToMes() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        Assert.assertEquals(0,service.dailyReportToMes());
    }

    @Test
    public void dailyReportToMes1() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        Assert.assertEquals(0,service.dailyReportToMes());
    }

    @Test
    public void syncOuterQryToMES() throws Exception {
        service.syncOuterQryToMES(null);
        service.syncOuterQryToMES(1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setDataSource() {
        service.setDataSource(new HashMap<>());
        PowerMockito.when(factoryConfig.getFactoryDbById("51")).thenReturn(null);
        service.setDataSource(new HashMap(){{put(SysConst.HTTP_HEADER_X_FACTORY_ID, "51");}});
        PowerMockito.when(factoryConfig.getFactoryDbById("51")).thenReturn("51");
        service.setDataSource(new HashMap(){{put(SysConst.HTTP_HEADER_X_FACTORY_ID, "51");}});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


}
