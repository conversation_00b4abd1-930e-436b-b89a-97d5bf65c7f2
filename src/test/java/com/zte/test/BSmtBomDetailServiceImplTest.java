package com.zte.test;


import com.zte.application.BSmtBomHeaderService;
import com.zte.application.impl.BSmtBomDetailServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.BSmtBomHeader;
import com.zte.domain.model.BSmtBomHeaderRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BSmtBomDetailDTO;
import com.zte.interfaces.dto.PdaFeederBindDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.doReturn;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, StringHelper.class, BSmtBomHeaderService.class, PlanscheduleRemoteService.class,
        ProductionDeliveryRemoteService.class, ConstantInterface.class, CenterfactoryRemoteService.class})
public class BSmtBomDetailServiceImplTest extends PowerBaseTestCase {

    private RetCode retCode;

    private ServiceData serviceData;

    @InjectMocks
    private BSmtBomDetailServiceImpl bSmtBomDetailServiceImpl;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    private BSmtBomHeaderRepository bSmtBomHeaderRepository;

    @Mock
    private BSmtBomHeaderService bSmtBomHeaderService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;


    @Test
    public void getBSmtBomDetailList() throws Exception {
        BSmtBomHeader bSmtBomHeader = new BSmtBomHeader();
        bSmtBomHeader.setCfgHeaderId("132a0981-9881-4322-86dc-b186f55cc359");
        bSmtBomHeader.setProductCode("129692851028zhq");
        bSmtBomHeader.setItemName("ZXR10 9916 ISFSD");
        bSmtBomHeader.setCreateUser("10200835");
        bSmtBomHeader.setLastUpdatedBy("10200835");
        bSmtBomHeader.setEnabledFlag("Y");
        bSmtBomHeader.setCraftSection("DIP");
        bSmtBomHeader.setLineCode("DIP4");
        bSmtBomHeader.setLocationNo("dip4上线工位");

        List<BSmtBomHeader> headerList = new ArrayList<>();
        headerList.add(bSmtBomHeader);

        PowerMockito.when(bSmtBomHeaderRepository.getListLinkBomDetail(anyMap())).thenReturn(headerList);

        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setCfgLineId("7a36300d-6908-47d9-9673-36a3037cfaef");
        bSmtBomDetail.setCfgHeaderId("132a0981-9881-4322-86dc-b186f55cc359");
        bSmtBomDetail.setItemCode("007020100305");
        bSmtBomDetail.setLocationNo("dip4上线工位");
        bSmtBomDetail.setCreateUser("10200835");
        bSmtBomDetail.setLastUpdatedBy("10200835");
        bSmtBomDetail.setEnabledFlag("Y");
        bSmtBomDetail.setItemName("16nm Kintex UltraScale+ FPGA KU15P | XCKU15P-L2FFVE1517E");
        bSmtBomDetail.setProcessCode("P0238");
        bSmtBomDetail.setPreManue("N");

        List<BSmtBomDetail> detailList = new ArrayList<>();
        detailList.add(bSmtBomDetail);

        PowerMockito.when(bSmtBomDetailRepository.getList(anyMap())).thenReturn(detailList);

        AgeingInfoFencePointToPointQueryItemInfoDTO dto = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        dto.setItemId("19884936");
        dto.setItemNo("007020100305");
        dto.setSourceSystem("WMES");
        dto.setUnit("只");
        dto.setItemName("16nm Kintex UltraScale+ FPGA KU15P | XCKU15P-L2FFVE1517E");
        dto.setItemEnName("16nm Kintex UltraScale+ FPGA KU15P | XCKU15P-L2FFVE1517E");
        dto.setCreateBy("80043");
        dto.setEnabledFlag("Y");
        dto.setOrgId("635");
        dto.setItemType("2");
        dto.setFactoryId(new BigDecimal((55)));
        dto.setEntityId(new BigDecimal((2)));
        dto.setIsSmt(new BigDecimal((0)));

        List<AgeingInfoFencePointToPointQueryItemInfoDTO> ageList = new ArrayList<>();
        ageList.add(dto);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyList())).thenReturn(ageList);

        BSmtBomDetailDTO entity = new BSmtBomDetailDTO();
        entity.setLocationNo("dip4上线工位");
        entity.setLineCode("DIP4");
        entity.setProductCode("129692851028zhq");
        entity.setPreManue("N");
        entity.setProcessCode("P2038");
        entity.setCraftSection("DIP");

        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBSmtBomDetailList(entity));
    }


    @Test
    public void getVirtualLocationNoData() throws Exception {
        BSmtBomDetailDTO dto = new BSmtBomDetailDTO();
        dto.setVirtualFlag("Y");
        dto.setCfgHeaderId("cf3e1369-ac2d-4f02-ae16-e2520d5672f3");
        dto.setWorkOrder("7777888-SMT-B5501");
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        bSmtBomDetailRepository.getVirtualLocationNoData(dto);
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("itemCode1");
        bSmtBomDetailList.add(bSmtBomDetail);
        PowerMockito.when(bSmtBomDetailRepository.getVirtualLocationNoData(Mockito.anyObject())).thenReturn(bSmtBomDetailList);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setItemNo("itemNo1");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);

        PageRows<BPcbLocationDetail> bPcbLocationDetailListPage = new PageRows<>();
        List<BPcbLocationDetail> list = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetail.setItemCode("itemCode1");
        list.add(bPcbLocationDetail);
        bPcbLocationDetailListPage.setRows(list);
        bPcbLocationDetailListPage.setTotal(10000L);
        PowerMockito.when(centerfactoryRemoteService.getBPcbLocPage(Mockito.anyObject())).thenReturn(bPcbLocationDetailListPage);
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getVirtualLocationNoData(dto));
    }

    @Test
    public void getDetailList() throws Exception {
        BSmtBomDetailServiceImpl service = PowerMockito.spy(new BSmtBomDetailServiceImpl());
        service.setBSmtBomHeaderService(bSmtBomHeaderService);
        service.setbSmtBomDetailRepository(bSmtBomDetailRepository);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(StringHelper.class);
        PowerMockito.mock(BSmtBomHeaderService.class);
        BSmtBomDetailDTO entity = new BSmtBomDetailDTO();
        String orderField = "locationNo";
        String order = "desc";
        Long page = (long) 1;
        Long rows = (long) 10;
        Map<String, Object> mapConquery = new HashMap<String, Object>();
        //设置查询条件
        List<BSmtBomDetail> detailList = new ArrayList<BSmtBomDetail>();
        BSmtBomDetail detail = new BSmtBomDetail();
        detail.setCfgHeaderId("1616e9c2-b57b-4a4f-8bf0-a8652b96c6a4");
        detail.setItemCode("132010700031");
        detail.setFactoryId(new BigDecimal(55));
        detailList.add(detail);
        PowerMockito.when(bSmtBomDetailRepository.getPage(anyObject())).thenReturn(detailList);
        BSmtBomHeader productList = new BSmtBomHeader();
        productList.setProductCode("130000012326AIB");
        productList.setCraftSection("SMT-B");
        productList.setCfgHeaderId("1616e9c2-b57b-4a4f-8bf0-a8652b96c6a4");
        productList.setFactoryId(new BigDecimal(55));
        doReturn(productList).when(bSmtBomHeaderService).selectBSmtBomHeaderById(anyObject());
        Map<String, Object> pointMap = new HashMap<String, Object>();
        pointMap.put("productCode", productList.getProductCode());
        pointMap.put("craftSection", productList.getCraftSection());
        BPcbLocationDetail pointDetail = new BPcbLocationDetail();
        List<BPcbLocationDetail> point = new ArrayList<BPcbLocationDetail>();
        pointDetail.setPointLoc("GDSGR");
        pointDetail.setItemCode("132010700031");
        pointDetail.setFactoryId(55);
        point.add(pointDetail);
        BsItemInfo styleNew = new BsItemInfo();
        styleNew.setItemNo("132010700031");
        styleNew.setStyle("set");
        styleNew.setFactoryId(new BigDecimal(55));
        List<BsItemInfo> style = new ArrayList<BsItemInfo>();
        style.add(styleNew);
        Map<String, Object> styleMap = new HashMap<String, Object>();
        styleMap.put("inItemNo", "132010700031");
        PowerMockito.when(BasicsettingRemoteService.getStyleInfo(styleMap)).thenReturn(style);
        PowerMockito.when(StringHelper.isNotEmpty(anyObject())).thenReturn(true);
        mapConquery.put("orderField", entity.getSort());
        mapConquery.put("order", entity.getOrder());
        mapConquery.put("startRow", (page - 1) * rows + 1);
        mapConquery.put("endRow", page * rows);
        service.getDetailList(mapConquery, "1616e9c2-b57b-4a4f-8bf0-a8652b96c6a4");
        Assert.assertEquals("132010700031",styleNew.getItemNo());
    }


    @Test
    public void queryQtyAndWorkOrderByCfgHeaderId() throws Exception {
        BSmtBomDetailDTO dto = new BSmtBomDetailDTO();
        dto.setCfgHeaderId("17a0770a-28a1-467b-b772-b5c895c0b0c3");
        dto.setItemCode("046050200294");
        dto.setCraftSection("SMT-B");
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasicDTO workOrderNoList = new PsWorkOrderBasicDTO();
        workOrderNoList.setWorkOrderNo("7792420-SMT-B5201");
        PowerMockito.when(PlanscheduleRemoteService.queryWorkOrderNo(Mockito.any(), Mockito.any())).thenReturn(workOrderNoList);

        PsWorkOrderSmt smtOrderList = new PsWorkOrderSmt();
        smtOrderList.setPcbQty(new BigDecimal(9));
        PowerMockito.when(PlanscheduleRemoteService.getPcbQty(Mockito.any())).thenReturn(smtOrderList);

        Assert.assertNotNull(bSmtBomDetailServiceImpl.queryQtyAndWorkOrderByCfgHeaderId(dto));
    }


    @Test
    public void updateQtyByItemCode() throws Exception {
        BSmtBomDetailDTO record = new BSmtBomDetailDTO();
        record.setCfgHeaderId("17a0770a-28a1-467b-b772-b5c895c0b0c3");
        record.setItemCode("046110400006");
        record.setQty(new BigDecimal(20));
        record.setLastUpdatedBy("00286569");
        record.setCraftSection("SMT-B");
        record.setProdplanId("7792420");
        record.setLineCode("SMT-CS3");
        record.setProductCode("129571751003AOB");

        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CenterfactoryRemoteService.class,
                ProductionDeliveryRemoteService.class, ConstantInterface.class);

        PsWorkOrderBasicDTO workOrderNoList = new PsWorkOrderBasicDTO();
        workOrderNoList.setWorkOrderNo("7792420-SMT-B5201");
        PowerMockito.when(PlanscheduleRemoteService.queryWorkOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(workOrderNoList);

        PsWorkOrderSmt smtOrderList = new PsWorkOrderSmt();
        smtOrderList.setPcbQty(new BigDecimal(9));
        PowerMockito.when(PlanscheduleRemoteService.getPcbQty(Mockito.anyString())).thenReturn(smtOrderList);

        List<PsWorkOrderBasicDTO> orderListAll = new ArrayList<>();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO1 = new PsWorkOrderBasicDTO();
        psWorkOrderBasicDTO.setCraftSection("SMT-B");
        psWorkOrderBasicDTO.setItemNo("129206751425AKB");
        psWorkOrderBasicDTO.setLineCode("SMT-CS3");
        psWorkOrderBasicDTO.setChildCardFlag(true);
        psWorkOrderBasicDTO.setProdPlanNo("126510150593AHB");
        psWorkOrderBasicDTO.setProdPlanId("7792423");
        psWorkOrderBasicDTO1.setCraftSection("SMT-B");
        psWorkOrderBasicDTO1.setItemNo("129571751003AOB");
        psWorkOrderBasicDTO1.setLineCode("SMT-CS1");
        psWorkOrderBasicDTO1.setChildCardFlag(false);
        psWorkOrderBasicDTO1.setProdPlanNo("129571751003AOB");
        psWorkOrderBasicDTO1.setProdPlanId("7792420");
        orderListAll.add(psWorkOrderBasicDTO);
        orderListAll.add(psWorkOrderBasicDTO1);
        PowerMockito.when(ProductionDeliveryRemoteService.getChildPsTask(Mockito.anyString())).thenReturn(orderListAll);

        List<BBomDetailDTO> allBomQtyList = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
        bBomDetailDTO.setbBomDetailId("15451831236463");
        bBomDetailDTO.setBomHeaderId("1545183");
        bBomDetailDTO.setItemCode("046110400006");
        bBomDetailDTO.setUsageCount(new BigDecimal(6));
        bBomDetailDTO.setProductCode("129571751003AOB");
//		bBomDetailDTO1.setbBomDetailId("1783470394");
//		bBomDetailDTO1.setBomHeaderId("1634097");
//		bBomDetailDTO1.setItemCode("129571751003AOB");
//		bBomDetailDTO1.setUsageCount(new BigDecimal(4));
//		bBomDetailDTO1.setProductCode("129206751425AKB");
        allBomQtyList.add(bBomDetailDTO);
//		allBomQtyList.add(bBomDetailDTO1);
        PowerMockito.when(CenterfactoryRemoteService.getNumByProductCodeAndItemCode(Mockito.anyObject())).thenReturn(allBomQtyList);


        PowerMockito.when(bSmtBomDetailRepository.updateQtyByItemCode(Mockito.anyObject())).thenReturn(1);
        Assert.assertEquals(1,bSmtBomDetailServiceImpl.updateQtyByItemCode(record));
    }

    @Test
    public void insertItemCode() throws Exception {
        BSmtBomDetailDTO record = new BSmtBomDetailDTO();
        record.setCfgHeaderId("17a0770a-28a1-467b-b772-b5c895c0b0c3");
        record.setCfgLineId("");
        record.setCreateUser("00286569");
        record.setItemCode("046110400006");
        record.setQty(new BigDecimal(2));
        record.setLastUpdatedBy("00286569");
        record.setCraftSection("SMT-B");
        record.setProdplanId("7792420");

        PowerMockito.mockStatic(PlanscheduleRemoteService.class, CenterfactoryRemoteService.class,
                ProductionDeliveryRemoteService.class, ConstantInterface.class);

        PsWorkOrderBasicDTO workOrderNoList = new PsWorkOrderBasicDTO();
        workOrderNoList.setWorkOrderNo("7792420-SMT-B5201");
        PowerMockito.when(PlanscheduleRemoteService.queryWorkOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(workOrderNoList);

        PsWorkOrderSmt smtOrderList = new PsWorkOrderSmt();
        smtOrderList.setPcbQty(new BigDecimal(9));
        PowerMockito.when(PlanscheduleRemoteService.getPcbQty(Mockito.anyString())).thenReturn(smtOrderList);

        List<BSmtBomDetail> tempList = new ArrayList<>();
        PowerMockito.when(bSmtBomDetailRepository.checkByItemCode(Mockito.anyObject())).thenReturn(tempList);

        List<BBomDetailDTO> allBomQtyList = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
        bBomDetailDTO.setbBomDetailId("15451831236463");
        bBomDetailDTO.setBomHeaderId("1545183");
        bBomDetailDTO.setItemCode("046110400006");
        bBomDetailDTO.setUsageCount(new BigDecimal(6));
        bBomDetailDTO.setProductCode("129571751003AOB");
        allBomQtyList.add(bBomDetailDTO);
        PowerMockito.when(CenterfactoryRemoteService.getNumByProductCodeAndItemCode(Mockito.anyObject())).thenReturn(allBomQtyList);

        List<PsWorkOrderBasicDTO> orderListAll = new ArrayList<>();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO = new PsWorkOrderBasicDTO();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO1 = new PsWorkOrderBasicDTO();
        psWorkOrderBasicDTO.setCraftSection("SMT-B");
        psWorkOrderBasicDTO.setItemNo("129206751425AKB");
        psWorkOrderBasicDTO.setLineCode("SMT-CS3");
        psWorkOrderBasicDTO.setChildCardFlag(true);
        psWorkOrderBasicDTO.setProdPlanNo("126510150593AHB");
        psWorkOrderBasicDTO.setProdPlanId("7792423");
        psWorkOrderBasicDTO1.setCraftSection("SMT-B");
        psWorkOrderBasicDTO1.setItemNo("129571751003AOB");
        psWorkOrderBasicDTO1.setLineCode("SMT-CS1");
        psWorkOrderBasicDTO1.setChildCardFlag(false);
        psWorkOrderBasicDTO1.setProdPlanNo("129571751003AOB");
        psWorkOrderBasicDTO1.setProdPlanId("7792420");
        orderListAll.add(psWorkOrderBasicDTO);
        orderListAll.add(psWorkOrderBasicDTO1);
        PowerMockito.when(ProductionDeliveryRemoteService.getChildPsTask(Mockito.anyString())).thenReturn(orderListAll);

        PowerMockito.when(bSmtBomDetailRepository.insertMaterialRecord(Mockito.anyObject())).thenReturn(1);
        Assert.assertEquals(1,bSmtBomDetailServiceImpl.insertItemCode(record));
    }

    @Test
    public void deleteVirtualBomDetailByItemCode() throws Exception {
        BSmtBomDetailDTO record = new BSmtBomDetailDTO();
        record.setCfgHeaderId("17a0770a-28a1-467b-b772-b5c895c0b0c3");
        record.setItemCode("046050200298");
        PowerMockito.when(bSmtBomDetailRepository.deleteVirtualBomDetailByItemCode(Mockito.anyObject())).thenReturn(1);
        Assert.assertEquals(1,bSmtBomDetailServiceImpl.deleteVirtualBomDetailByItemCode(record));
    }

    @Test
    public void getListForOneSide() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(Constant.LINE_CODE, "SMT-CS1");
        map.put("attr1", "7792420");
        List<BSmtBomDetail> oneSideList = new ArrayList<>();
        BSmtBomDetail dto = new BSmtBomDetail();
        dto.setItemCode("046050200298");
        dto.setLocationNo("test");
        dto.setModuleNo("test");
        oneSideList.add(dto);
        PowerMockito.when(bSmtBomDetailRepository.getListForOneSide(Mockito.anyMap())).thenReturn(oneSideList);
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getListForOneSide(map));
    }

	@Test
	public void getBomDetailByMouting() throws Exception {
		BSmtBomDetailDTO dto = new BSmtBomDetailDTO();
		PowerMockito.when(bSmtBomDetailRepository.getBomDetailByMouting(Mockito.any())).thenReturn("12X MM");
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBomDetailByMouting(dto));
	}

    @Test
    public void getEndMachineNo() {
        PowerMockito.when(bSmtBomDetailRepository.getEndMachineNo(Mockito.any())).thenReturn("(X4S-3)");
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getEndMachineNo("96d74e1d9f504ef9be7a2d637edef863"));
    }

    @Test
    public void chechCommonItemByReelIdTest() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);

        BSmtBomDetailDTO dto = new BSmtBomDetailDTO();
        PowerMockito.when(bSmtBomDetailRepository.countBomDetailByReelId(Mockito.any())).thenReturn(1);
        String result = bSmtBomDetailServiceImpl.checkCommonItemByReelId(dto);
        Assert.assertEquals(Constant.EMPTY_STRING, result);

        dto.setLineCode("SMT-TZ001");
        dto.setProdplanId("8899855");
        PowerMockito.when(bSmtBomDetailRepository.countBomDetailByReelId(Mockito.any())).thenReturn(2);
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoCount(Mockito.anyMap())).thenReturn(0);
        result = bSmtBomDetailServiceImpl.checkCommonItemByReelId(dto);
        Assert.assertEquals(Constant.EMPTY_STRING, result);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderInfoCount(Mockito.anyMap())).thenReturn(1);
        result = bSmtBomDetailServiceImpl.checkCommonItemByReelId(dto);
        Assert.assertEquals(Constant.STR_Y, result);
    }

    @Test
    public void getBSmtBomDetailBindFeederListNew() {
        PdaFeederBindDTO dto = new PdaFeederBindDTO();
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBSmtBomDetailBindFeederListNew(dto));
        dto.setWorkOrder("2401293-SMT-B5501");
        dto.setCfgHeaderId("1e82d417-4de5-4b2a-8b8c-fd4cf483fc35");
        PowerMockito.when(bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(Mockito.any())).thenReturn(null);
        try{
            bSmtBomDetailServiceImpl.getBSmtBomDetailBindFeederListNew(dto);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.CURRENT_NO_ITEMS, e.getExMsgId());
        }
        List<BSmtBomDetail> bSmtBomDetailBindFeederList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail1 = new BSmtBomDetail();
        bSmtBomDetail1.setModuleNo("(X4S-2)2");
        bSmtBomDetail1.setLocationNo("2-33-1");
        bSmtBomDetailBindFeederList.add(bSmtBomDetail1);
        BSmtBomDetail bSmtBomDetail2 = new BSmtBomDetail();
        bSmtBomDetail2.setModuleNo("(X4S-2)2");
        bSmtBomDetail2.setLocationNo("2-31-1");
        bSmtBomDetailBindFeederList.add(bSmtBomDetail2);
        PowerMockito.when(bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(Mockito.any())).thenReturn(bSmtBomDetailBindFeederList);
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBSmtBomDetailBindFeederListNew(dto));

        dto.setmIsBothSides(true);
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBSmtBomDetailBindFeederListNew(dto));
        dto.setmIsBothSides(true);
        dto.setmOtherSideCfgHeaderId("131a5b2c-96fa-480c-818c-1ddd01bfde06");

        List<BSmtBomDetail> otherBomDetails = new ArrayList<>();
        BSmtBomDetail otherDetail1 = new BSmtBomDetail();
        otherDetail1.setModuleNo("(X4S-2)2");
        otherDetail1.setLocationNo("2-33-1");
        otherBomDetails.add(otherDetail1);
        BSmtBomDetail otherDetail2 = new BSmtBomDetail();
        otherDetail2.setModuleNo("(X4S-2)2");
        otherDetail2.setLocationNo("2-31-3");
        otherBomDetails.add(otherDetail2);
        PowerMockito.when(bSmtBomDetailRepository.getBSmtBomDetailBindFeederList(Mockito.any())).thenReturn(otherBomDetails);
        Assert.assertNotNull(bSmtBomDetailServiceImpl.getBSmtBomDetailBindFeederListNew(dto));
    }
}
