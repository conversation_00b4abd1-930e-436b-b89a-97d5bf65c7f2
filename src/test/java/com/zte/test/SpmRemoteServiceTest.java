package com.zte.test;

import com.zte.infrastructure.remote.SpmRemoteService;
import com.zte.interfaces.dto.scan.PmBillRcvVO;
import com.zte.interfaces.dto.scan.SpmBillDetailDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-07 19:03
 */

@PrepareForTest({HttpRemoteUtil.class})
public class SpmRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private SpmRemoteService spmRemoteService;

    @Before
    public void init(){
        PowerMockito.mockStatic(HttpRemoteUtil.class);
    }

    @Test
    public void queryBillDetailByBillNo() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"操作成功\"\n" +
                        "    },\n" +
                        "    \"bo\": [\n" +
                        "        {\n" +
                        "            \"ref45\": \"10\",\n" +
                        "            \"sku\": \"006021300055\",\n" +
                        "            \"descr\": \"DDR3 RAM\",\n" +
                        "            \"lottable02\": \"220019247795\",\n" +
                        "            \"lottable08\": null,\n" +
                        "            \"shippedqty\": 100,\n" +
                        "            \"externalorderkey2\": \"IMES20210722000020\",\n" +
                        "            \"barCode\": null,\n" +
                        "            \"qty\": null,\n" +
                        "            \"status\": 0,\n" +
                        "            \"whseid\": null\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"other\": null,\n" +
                        "    \"requestId\": \"083ad00cfdee45da\"\n" +
                        "}");
        Assert.assertNotNull(spmRemoteService.queryBillDetailByBillNo("123", "billno"));
    }

    @Test
    public void queryBarcodeInfo() throws Exception {
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyMap(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn("{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"操作成功\"\n" +
                        "    },\n" +
                        "    \"bo\": \n" +
                        "        {\n" +
                        "            \"ref45\": \"10\",\n" +
                        "            \"sku\": \"006021300055\",\n" +
                        "            \"descr\": \"DDR3 RAM\",\n" +
                        "            \"lottable02\": \"220019247795\",\n" +
                        "            \"lottable08\": null,\n" +
                        "            \"shippedqty\": 100,\n" +
                        "            \"externalorderkey2\": \"IMES20210722000020\",\n" +
                        "            \"barCode\": null,\n" +
                        "            \"qty\": 5,\n" +
                        "            \"status\": 0,\n" +
                        "            \"whseid\": null\n" +
                        "        }\n" +
                        "    ,\n" +
                        "    \"other\": null,\n" +
                        "    \"requestId\": \"083ad00cfdee45da\"\n" +
                        "}");
        Assert.assertNotNull(spmRemoteService.queryBarcodeInfo(new PmBillRcvVO(), "123"));
    }
}
