package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.impl.TestInfoBatchServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsWorkTimeSectionRepository;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsOutputInfoRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.domain.model.WipTestRecodeRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TestInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@PrepareForTest({MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, PlanscheduleRemoteService.class,
		MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class,CommonUtils.class,AopContext.class})
public class TestInfoBatchServiceImplTest extends PowerBaseTestCase {

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @InjectMocks
    private TestInfoBatchServiceImpl testInfoBatchService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    private WipTestRecodeRepository wipTestRecodeRepository;

    @Mock
    private BsWorkTimeSectionRepository bsWorkTimeSectionRepository;
	@Mock
	private IMESLogService imesLogService;
    @Mock
    private PsOutputInfoRepository psOutputInfoRepository;
	@Value("${testInfo.upload.maxNum:100}")
	private Integer testInfoUploadMaxNum;

    @Test
    public void saveInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
		PowerMockito.field(TestInfoBatchServiceImpl.class, "testInfoUploadMaxNum").set(testInfoBatchService, 100);
        List<TestInfoDTO> testInfoList = JSON.parseArray("[{\"factoryId\":\"53\",\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
                "{\"factoryId\":\"53\",\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
                ",{\"factoryId\":\"53\",\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"0\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]", TestInfoDTO.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"processId\":\"9e93813f-56f4-4b94-b757-184a3cea29a3\",\"processCode\":\"P0004\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"检焊\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-01-16 15:41:18\",\"lastUpdatedBy\":\"10207212\",\"lastUpdatedDate\":\"2017-11-02 19:21:22\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"维修\",\"processControlGroupName\":null,\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null},{\"processId\":\"1128762d-9dbe-4003-a752-bbacf3c2807b\",\"processCode\":\"PB_INPUT\",\"processType\":\"手工测试\",\"xType\":\"工站\",\"processName\":\"SMT投入\",\"toolType\":\"标贴\",\"processControlGroup\":\"Common_Error_Scan\",\"isPrintTempLabel\":\"N\",\"isDictionaryConfigProcess\":\"N\",\"isDeliverProcess\":\"N\",\"isFailScan\":\"N\",\"remark\":\"wsj\",\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":null,\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2017-12-01 09:19:16\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"单板装配\",\"processControlGroupName\":\"通用扫描（包含不良代码）\",\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"182ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Mon Jul 04 15:40:47 CST 2022\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10270446\"}}");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
		Assert.assertEquals(Constant.STR_Y, sysLookupTypesDTO.getLookupMeaning());
        testInfoBatchService.saveInfo(testInfoList);
    }

	@Test
	public void saveInfoTwo() throws Exception {
		PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class);
		PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
		PowerMockito.field(TestInfoBatchServiceImpl.class, "testInfoUploadMaxNum").set(testInfoBatchService, 1);
		List<TestInfoDTO> testInfoList = JSON.parseArray("[{\"factoryId\":\"53\",\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
				"{\"factoryId\":\"53\",\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
				",{\"factoryId\":\"53\",\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"0\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]", TestInfoDTO.class);
		try{
			testInfoBatchService.saveInfo(testInfoList);
		} catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.UPLOAD_TEST_DATA_TOO_MUCH);
		}
	}

	@Test
	public void saveInfoThree() throws Exception {
		PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class);
		PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
		PowerMockito.field(TestInfoBatchServiceImpl.class, "testInfoUploadMaxNum").set(testInfoBatchService, 100);
		List<TestInfoDTO> testInfoList = JSON.parseArray("[{\"lineName\":\"53\",\"factoryId\":\"53\",\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
				"{\"lineName\":\"53\",\"factoryId\":\"53\",\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
				",{\"lineName\":\"53\",\"factoryId\":\"53\",\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"0\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]", TestInfoDTO.class);
		try{
			testInfoBatchService.saveInfo(testInfoList);
		} catch (Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}
	}

    @Test
    public void dealWithMesBusinessException() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        Exception e = new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_IS_DOING_OTHER_OPT);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any(),(String[])Mockito.any())).thenReturn("error");
        Assert.assertNull(Whitebox.invokeMethod(testInfoBatchService, "dealWithMesBusinessException", e));
    }

    @Test
    public void dealWithMesBusinessExceptionTwo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        Exception e = new Exception();
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any(),(String[])Mockito.any())).thenReturn("error");
        Assert.assertNull(Whitebox.invokeMethod(testInfoBatchService, "dealWithMesBusinessException", e));
    }

	@Test
	public void assembleDataAndBatchPass() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class);
		List<TestInfoDTO> testInfoList = new ArrayList<>();
		TestInfoDTO testInfoDTO = new TestInfoDTO();
		testInfoDTO.setWorkStation("198");
		testInfoDTO.setLineCode("ICT-HY2");
		testInfoDTO.setProcessCode("C");
		testInfoDTO.setTestType("6");
		testInfoDTO.setTestResult("1");
		testInfoDTO.setAutoPassFlag("N");
		testInfoDTO.setWorkOrderNo("7011153-ICT5504");
		testInfoList.add(testInfoDTO);

		try {
			List<PsWorkOrderDTO> workList = new ArrayList<>();
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList);
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		testInfoDTO.setTestResult("0");
		testInfoDTO.setAutoPassFlag("Y");
		try {
			List<PsWorkOrderDTO> workList = new ArrayList<>();
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList);
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		try {
			List<PsWorkOrderDTO> workList1 = new ArrayList<>();
			PsWorkOrderDTO ps = new PsWorkOrderDTO();
			ps.setWorkOrderNo("7011154-ICT5504");
			ps.setLineCode("ICT-HY2");
			workList1.add(ps);
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList1);
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		try {
			List<PsWorkOrderDTO> workList2 = new ArrayList<>();
			PsWorkOrderDTO ps1 = new PsWorkOrderDTO();
			ps1.setWorkOrderNo("7011153-ICT5504");
			workList2.add(ps1);
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList2);
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}

		List<PsWorkOrderDTO> workList3 = new ArrayList<>();
		PsWorkOrderDTO ps2 = new PsWorkOrderDTO();
		ps2.setWorkOrderNo("7011153-ICT5504");
		ps2.setLineCode("ICT-HY2");
		workList3.add(ps2);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList3);

		try {
			List<CtRouteDetailDTO> ctRouteDetailList1 = new LinkedList<>();
			PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
					.thenReturn(ctRouteDetailList1);
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}

		List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
		CtRouteDetailDTO b1 = new CtRouteDetailDTO();
		b1.setProcessCode("C");
		b1.setCraftSection(Constant.CRAFTSECTION_SMT_B);
		b1.setNextProcess("198");
		b1.setSourceImu(new BigDecimal(1));
		b1.setSourceBimu(new BigDecimal(2));
		b1.setSysColName("E5");
		b1.setSourceSysName("SMT-B-B面");
		b1.setItemNo("ICT-HY2");
		ctRouteDetailList.add(b1);
		PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
				.thenReturn(ctRouteDetailList);

		try {
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void assembleDataAndBatchPassTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class);
		List<TestInfoDTO> testInfoList = new ArrayList<>();
		TestInfoDTO testInfoDTO = new TestInfoDTO();
		testInfoDTO.setWorkStation("198");
		testInfoDTO.setLineCode("ICT-HY2");
		testInfoDTO.setProcessCode("C");
		testInfoDTO.setTestType("4");
		testInfoDTO.setTestResult("0");
		testInfoDTO.setAutoPassFlag("Y");
		testInfoDTO.setWorkOrderNo("7011153-ICT5504");
		testInfoList.add(testInfoDTO);

		List<PsWorkOrderDTO> workList3 = new ArrayList<>();
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList3);
		try {
			Whitebox.invokeMethod(testInfoBatchService, "assembleDataAndBatchPass", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}


	@Test
	public void queryWorkOrderAndLineInfo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class);
		List<TestInfoDTO> testInfoList = new ArrayList<>();
		TestInfoDTO testInfoDTO = new TestInfoDTO();
		testInfoDTO.setWorkStation("198");
		testInfoDTO.setLineCode("ICT-HY2");
		testInfoDTO.setProcessCode("C");
		testInfoDTO.setTestType("4");
		testInfoDTO.setTestResult("0");
		testInfoDTO.setAutoPassFlag("Y");
		testInfoList.add(testInfoDTO);

		try {
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}

		try {
			testInfoDTO.setWorkOrderNo("7011153-ICT5504");
			List<PsWorkOrderDTO> workList1 = new ArrayList<>();
			PsWorkOrderDTO ps = new PsWorkOrderDTO();
			ps.setWorkOrderNo("7011154-ICT5504");
			ps.setLineCode("ICT-HY2");
			workList1.add(ps);
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList1);
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		try {
			testInfoDTO.setWorkOrderNo("7011153-ICT5504");
			List<PsWorkOrderDTO> workList1 = new ArrayList<>();
			PsWorkOrderDTO ps = new PsWorkOrderDTO();
			ps.setWorkOrderNo("7011153-ICT5504");
			workList1.add(ps);
			PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList1);
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
		try {
			List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
			PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
					.thenReturn(ctRouteDetailList);
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}

		List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
		CtRouteDetailDTO b1 = new CtRouteDetailDTO();
		b1.setProcessCode("C");
		b1.setCraftSection(Constant.CRAFTSECTION_SMT_B);
		b1.setNextProcess("198");
		b1.setSourceImu(new BigDecimal(1));
		b1.setSourceBimu(new BigDecimal(2));
		b1.setSysColName("E5");
		b1.setSourceSysName("SMT-B-B面");
		b1.setItemNo("ICT-HY3");
		ctRouteDetailList.add(b1);
		PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
				.thenReturn(ctRouteDetailList);
		try {
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void queryWorkOrderAndLineInfoTwo() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class, MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class);
		List<TestInfoDTO> testInfoList = new ArrayList<>();
		TestInfoDTO testInfoDTO = new TestInfoDTO();
		testInfoDTO.setWorkStation("198");
		testInfoDTO.setLineCode("ICT-HY2");
		testInfoDTO.setProcessCode("C");
		testInfoDTO.setTestType("4");
		testInfoDTO.setTestResult("0");
		testInfoDTO.setAutoPassFlag("Y");
		testInfoList.add(testInfoDTO);
		testInfoDTO.setWorkOrderNo("7011153-ICT5504");
		List<PsWorkOrderDTO> workList1 = new ArrayList<>();
		PsWorkOrderDTO ps = new PsWorkOrderDTO();
		ps.setWorkOrderNo("7011153-ICT5504");
		ps.setLineCode("ICT-HY2");
		workList1.add(ps);
		PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrder(Mockito.anyMap())).thenReturn(workList1);
		List<CtRouteDetailDTO> ctRouteDetailList = new LinkedList<>();
		CtRouteDetailDTO b1 = new CtRouteDetailDTO();
		b1.setProcessCode("C");
		b1.setCraftSection(Constant.CRAFTSECTION_SMT_B);
		b1.setNextProcess("198");
		b1.setSourceImu(new BigDecimal(1));
		b1.setSourceBimu(new BigDecimal(2));
		b1.setSysColName("E5");
		b1.setSourceSysName("SMT-B-B面");
		b1.setItemNo("ICT-HY3");
		ctRouteDetailList.add(b1);
		PowerMockito.when(CrafttechRemoteService.getLineBodyListBatch(Mockito.anyString()))
				.thenReturn(ctRouteDetailList);
		try {
			Whitebox.invokeMethod(testInfoBatchService, "queryWorkOrderAndLineInfo", testInfoList);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

    @Test
    public void saveInfoException() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
		PowerMockito.field(TestInfoBatchServiceImpl.class, "testInfoUploadMaxNum").set(testInfoBatchService, 100);
        List<TestInfoDTO> testInfoList = JSON.parseArray("[{\"factoryId\":\"53\",\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
                "{\"factoryId\":\"53\",\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
                ",{\"factoryId\":\"53\",\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"0\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]", TestInfoDTO.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"processId\":\"9e93813f-56f4-4b94-b757-184a3cea29a3\",\"processCode\":\"P0004\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"检焊\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-01-16 15:41:18\",\"lastUpdatedBy\":\"10207212\",\"lastUpdatedDate\":\"2017-11-02 19:21:22\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"维修\",\"processControlGroupName\":null,\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null},{\"processId\":\"1128762d-9dbe-4003-a752-bbacf3c2807b\",\"processCode\":\"PB_INPUT\",\"processType\":\"手工测试\",\"xType\":\"工站\",\"processName\":\"SMT投入\",\"toolType\":\"标贴\",\"processControlGroup\":\"Common_Error_Scan\",\"isPrintTempLabel\":\"N\",\"isDictionaryConfigProcess\":\"N\",\"isDeliverProcess\":\"N\",\"isFailScan\":\"N\",\"remark\":\"wsj\",\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":null,\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2017-12-01 09:19:16\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"单板装配\",\"processControlGroupName\":\"通用扫描（包含不良代码）\",\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"182ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Mon Jul 04 15:40:47 CST 2022\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10270446\"}}");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.doNothing().when(wipTestRecodeRepository).updateBatchAttribute4ByIds(any(),any());
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
        testInfoBatchService.saveInfo(testInfoList);

    }

    @Test
    public void setWipForUpdate() {
        TestInfoBatchServiceImpl service = PowerMockito.spy(new TestInfoBatchServiceImpl());
        CFLine line = new CFLine();
        BSProcessDTO process = new BSProcessDTO();
        PsWipInfo wipInfo = new PsWipInfo();
        TestInfoDTO testInfo = new TestInfoDTO();
		PsEntityPlanBasic workOrder = new PsEntityPlanBasic();
        List<BSProcessDTO> processList = new ArrayList<BSProcessDTO>();
        List<CFLine> lineList = new ArrayList<CFLine>();
        List<PsWipInfo> wipInfoList = new ArrayList<PsWipInfo>();
        lineList.add(line);
        wipInfoList.add(wipInfo);
        processList.add(process);
		Assert.assertNotNull(service.setWipForUpdate(lineList,processList,wipInfoList,testInfo,workOrder));
    }

    @Test
    public void setScanHistoryForAdd() {
        TestInfoBatchServiceImpl service = PowerMockito.spy(new TestInfoBatchServiceImpl());
        CFLine line = new CFLine();
        BSProcessDTO process = new BSProcessDTO();
        PsWipInfo wipInfo = new PsWipInfo();
        TestInfoDTO testInfo = new TestInfoDTO();
        List<BSProcessDTO> processList = new ArrayList<BSProcessDTO>();
        processList.add(process);
        List<CFLine> lineList = new ArrayList<CFLine>();
        PsScanHistory history = new PsScanHistory();
        List<PsScanHistory> historyList = new ArrayList<PsScanHistory>();
        historyList.add(history);
        lineList.add(line);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
        service.setScanHistoryForAdd(lineList, processList, historyList, testInfo, "52");
    }

    @Test
    public void saveTestInfo() throws Exception {
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, HttpRemoteUtil.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(testInfoBatchService);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        PowerMockito.field(TestInfoBatchServiceImpl.class, "testInfoUploadMaxNum").set(testInfoBatchService, 100);
        List<TestInfoDTO> testInfoList = JSON.parseArray("[{\"factoryId\":\"53\",\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
                "{\"factoryId\":\"53\",\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
                ",{\"factoryId\":\"53\",\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"iMES\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"0\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]", TestInfoDTO.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"processId\":\"9e93813f-56f4-4b94-b757-184a3cea29a3\",\"processCode\":\"P0004\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"检焊\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-01-16 15:41:18\",\"lastUpdatedBy\":\"10207212\",\"lastUpdatedDate\":\"2017-11-02 19:21:22\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"维修\",\"processControlGroupName\":null,\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null},{\"processId\":\"1128762d-9dbe-4003-a752-bbacf3c2807b\",\"processCode\":\"PB_INPUT\",\"processType\":\"手工测试\",\"xType\":\"工站\",\"processName\":\"SMT投入\",\"toolType\":\"标贴\",\"processControlGroup\":\"Common_Error_Scan\",\"isPrintTempLabel\":\"N\",\"isDictionaryConfigProcess\":\"N\",\"isDeliverProcess\":\"N\",\"isFailScan\":\"N\",\"remark\":\"wsj\",\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":null,\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2017-12-01 09:19:16\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"单板装配\",\"processControlGroupName\":\"通用扫描（包含不良代码）\",\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"182ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Mon Jul 04 15:40:47 CST 2022\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10270446\"}}");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        Assert.assertEquals(Constant.STR_Y, sysLookupTypesDTO.getLookupMeaning());
        Assert.assertNull(Whitebox.invokeMethod(testInfoBatchService, "saveTestInfo", testInfoList));
    }

    @Test
    public void getWipInfo() {
        TestInfoBatchServiceImpl service = PowerMockito.spy(new TestInfoBatchServiceImpl());
        service.setPsWipInfoRepository(psWipInfoRepository);
        List<String> snList = new ArrayList<String>();
        snList.add("12345678");
        List<PsWipInfo> wipList = new ArrayList<PsWipInfo>();
        PsWipInfo wipInfo = new PsWipInfo();
        wipList.add(wipInfo);
        when(psWipInfoRepository.getListByBatchSn(anyObject())).thenReturn(wipList);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
        service.getWipInfo(snList, wipList);
    }

    @Test
    public void setOtherInfoForHistory() {
        List<PsWipInfo> wipList = new ArrayList<PsWipInfo>();
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setSn("123456");
        wipList.add(wipInfo);
        PsScanHistory history = new PsScanHistory();
        history.setSn("123456");
        history.setParentSn("123456");
        List<PsScanHistory> historyList = new ArrayList<PsScanHistory>();
        historyList.add(history);
        TestInfoBatchServiceImpl service = PowerMockito.spy(new TestInfoBatchServiceImpl());
        PsEntityPlanBasic workOrder = new PsEntityPlanBasic();
        workOrder.setWorkOrderNo("123456");
        service.setOtherInfoForHistory(wipList, historyList, workOrder);
		Assert.assertEquals("123456", wipInfo.getSn());
    }

    @Test
    public void saveInfoForZj() throws Exception {
        TestInfoBatchServiceImpl service = PowerMockito.spy(new TestInfoBatchServiceImpl());
        service.setPsWipInfoRepository(psWipInfoRepository);
        service.setPsScanHistoryRepository(psScanHistoryRepository);
        PsEntityPlanBasic workOrder = new PsEntityPlanBasic();
        workOrder.setWorkOrderNo("123456");
        List<PsEntityPlanBasic> workOrderList = new ArrayList<PsEntityPlanBasic>();
        workOrderList.add(workOrder);
        TestInfoDTO testInfo = new TestInfoDTO();
        testInfo.setSn("12345678");
        testInfo.setFactoryId("123456");
        CFLine line = new CFLine();
        List<CFLine> lineList = new ArrayList<CFLine>();
        lineList.add(line);
        List<TestInfoDTO> testInfoList = new ArrayList<TestInfoDTO>();
        testInfoList.add(testInfo);
        BSProcessDTO process = new BSProcessDTO();
        List<BSProcessDTO> processList = new ArrayList<BSProcessDTO>();
        processList.add(process);
        PsWipInfo wipInfo = new PsWipInfo();
        List<PsWipInfo> wipInfoList = new ArrayList<PsWipInfo>();
        doReturn(lineList).when(service).getLineInfo(anyObject(), anyObject());
        when(service.getLineInfo(anyObject(), anyObject())).thenReturn(lineList);
        doReturn(processList).when(service).getProcessInfo(anyObject(), anyObject());
        when(service.getProcessInfo(anyObject(), anyObject())).thenReturn(processList);
        doReturn(workOrderList).when(service).getWorkOrderInfo("43242", "343", "3434");
        //when(service.getWorkOrderInfo("43242","343","3434")).thenReturn(workOrderList);
        //doReturn(wipInfo).when(service).setWipForUpdate(anyObject(),anyObject(),anyObject(),anyObject(),anyObject());
        when(service.setWipForUpdate(lineList, processList, wipInfoList, testInfo, workOrder)).thenReturn(wipInfo);
        doNothing().when(service).setScanHistoryForAdd(anyObject(), anyObject(), anyObject(), anyObject(), anyObject());
        doNothing().when(service).getWipInfo(anyObject(), anyObject());
        doNothing().when(service).setOtherInfoForHistory(anyObject(), anyObject(), anyObject());
		Assert.assertEquals("12345678", testInfo.getSn());
        when(psScanHistoryRepository.updatePsScanHistoryByScanBatch(anyObject())).thenReturn(1);
        when(psWipInfoRepository.updatePsWipInfoByScanBatch(anyObject())).thenReturn(1);
    }


	@Test
	public void updateWorkOrderTest() throws Exception {
		List<TestInfoDTO> testInfoList = new ArrayList<>();
		PsEntityPlanBasic workOrderInfo = new PsEntityPlanBasic();
		workOrderInfo.setOutputQty(new BigDecimal("1"));
		workOrderInfo.setInputQty(new BigDecimal("2"));
		String taskNo = "";
		int outPutQtyForAdd = 1;
		try {
			Whitebox.invokeMethod(testInfoBatchService, "updateWorkOrder", testInfoList, workOrderInfo, taskNo, outPutQtyForAdd);
		} catch (MesBusinessException e) {
			Assert.assertTrue(e.getExMsgId().equals(MessageId.OUT_PUT_QTY_LARGER_THAN_WORK_ORDER_QTY));
		}
		workOrderInfo.setWorkOrderQty(new BigDecimal("10"));
		Assert.assertTrue(1==1);

	}
}
