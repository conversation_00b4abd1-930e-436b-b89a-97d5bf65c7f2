package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.AssemblExecLogService;
import com.zte.application.AssemblyOptRecordService;
import com.zte.application.AssemblyResultService;
import com.zte.application.impl.ProdBindingSettingServiceImpl;
import com.zte.application.impl.WipEntityScanInfoServiceImpl;
import com.zte.application.impl.WipExtendIdentificationServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.AssemUnbindHistoryInfoRepository;
import com.zte.domain.model.AssemblyOptRecordRepository;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.ProdUnbindingSetting;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AssemblExecLogEntityDTO;
import com.zte.interfaces.dto.AssemblyRelationshipQueryDTO;
import com.zte.interfaces.dto.AssemblyResultEntityDTO;
import com.zte.interfaces.dto.AssemblyResultHisEntityDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PDMProductMaterialResultDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.StItemMessage;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({SpringContextUtil.class,ConstantInterface.class, MicroServiceRestUtil.class,
        JacksonJsonConverUtil.class, ConstantInterface.class,
        HttpRemoteService.class, CommonUtils.class,CenterfactoryRemoteService.class,
        BasicsettingRemoteService.class, RedisHelper.class,
        DatawbRemoteService.class,ConstantInterface.class,HttpRemoteUtil.class,
        PlanscheduleRemoteService.class,CrafttechRemoteService.class,MESHttpHelper.class})
public class WipExtendIdentificationServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ProdBindingSettingServiceImpl service;
    @Mock
    private ProdBindingSettingRepository repository;
    @InjectMocks
    private WipExtendIdentificationServiceImpl extendIdentificationService;
    @Mock
    private ProdBindingSettingServiceImpl prodBindingSettingService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private AssemblyResultService assemblyResultService;

    @Mock
    private AssemblExecLogService assemblExecLogService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private AssemUnbindHistoryInfoRepository assemUnbindHistoryInfoRepository;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private FlowControlCommonService flowControlCommonService;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private AssemblyOptRecordService assemblyOptRecordService;
    @Mock
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;
    @Mock
    private AssemblyOptRecordRepository assemblyOptRecordRepository;
    @Test
    public void assemblyRelationshipPage() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("printer");
        sysLookupTypesDTO.setLookupMeaning("printer");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);

        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setNeedHasSubSn(true);
        assemblyRelationshipQueryDTO.setTaskNo("2");
        assemblyRelationshipQueryDTO.setFormType("2");
        assemblyRelationshipQueryDTO.setQueryType("0");
        extendIdentificationService.assemblyRelationshipQueryAll(assemblyRelationshipQueryDTO);
        extendIdentificationService.assemblyRelationshipQuery(assemblyRelationshipQueryDTO);

        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentification.setSn("sn");
        wipExtendIdentification.setFormSn("sn");
        wipExtendIdentificationList.add(wipExtendIdentification);
        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipQuery(any())).thenReturn(wipExtendIdentificationList);
        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipPage(any())).thenReturn(wipExtendIdentificationList);
        Assert.assertNotNull(extendIdentificationService.assemblyRelationshipQueryAll(assemblyRelationshipQueryDTO));
        extendIdentificationService.assemblyRelationshipQuery(assemblyRelationshipQueryDTO);
        assemblyRelationshipQueryDTO.setFormType("4");
        extendIdentificationService.assemblyRelationshipQuery(assemblyRelationshipQueryDTO);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("1");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoList(any())).thenReturn(psTaskList);
        extendIdentificationService.assemblyRelationshipQuery(assemblyRelationshipQueryDTO);
        psTask.setTaskNo("taskNo");
        extendIdentificationService.assemblyRelationshipQuery(assemblyRelationshipQueryDTO);
    }

    @Test
    public void pushWipExtSemiToFactory() throws Exception {
        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentification.setSn("sn");
        wipExtendIdentification.setFormSn("sn");
        wipExtendIdentificationList.add(wipExtendIdentification);
        List<List<WipExtendIdentification>> lists = new ArrayList<>();
        lists.add(wipExtendIdentificationList);
        Assert.assertEquals(0,extendIdentificationService.pushWipExtSemiToFactory(wipExtendIdentificationList));

    }
    @Test
    public void setHasSubSn() throws Exception {
        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentification.setSn("sn");
        wipExtendIdentification.setFormSn("sn");
        wipExtendIdentificationList.add(wipExtendIdentification);

        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setNeedHasSubSn(true);
        extendIdentificationService.setHasSubSn(assemblyRelationshipQueryDTO,wipExtendIdentificationList);

        PowerMockito.when(wipExtendIdentificationRepository.queryBindingQtyByFormSnList(any(),any())).thenReturn(wipExtendIdentificationList);
        Assert.assertEquals("taskNo", wipExtendIdentification.getTaskNo());
        extendIdentificationService.setHasSubSn(assemblyRelationshipQueryDTO,wipExtendIdentificationList);
    }
    @Test
    public void getEmpNo() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String,String> mesHttpHelper=new HashMap<>();
        mesHttpHelper.put(Constant.X_EMP_NO_SMALL,"55");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(mesHttpHelper);
        Assert.assertNotNull(extendIdentificationService.getEmpNo());
    }
    @Test
    public void unBindAssemblyRelationship() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentificationList.add(wipExtendIdentification);
        List<AssemblyRelationshipQueryDTO> list = new ArrayList<>();
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        try {
            extendIdentificationService.unBindAssemblyRelationship(list,"00286523");
        }catch (Exception e){
            Assert.assertEquals(MessageId.TASK_BATCH_BARCODES_CANNOT_ALL_BE_EMPTY, e.getMessage());
        }
        assemblyRelationshipQueryDTO.setFormType("2");
        assemblyRelationshipQueryDTO.setTaskNo("taskNo");
        assemblyRelationshipQueryDTO.setIdentiId("taskNo");
        assemblyRelationshipQueryDTO.setQueryType("0");
        list.add(assemblyRelationshipQueryDTO);
        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndProcess(any(),any())).thenReturn(wipExtendIdentificationList);

        extendIdentificationService.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("2");setCreateBy("10270056");}});
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("3");setWorkStation("3");}});
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("3");setWorkStation("2");}});
        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipQuery(any())).thenReturn(wipExtendIdentificationList);
        extendIdentificationService.unBindAssemblyRelationship(list,"00286523");
    }
    @Test
    public void saveOrUpdateBatch() throws Exception {
        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentificationList.add(wipExtendIdentification);
        extendIdentificationService.saveOrUpdateBatch(wipExtendIdentificationList);

        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendBySnAndProcess(any(),any())).thenReturn(wipExtendIdentificationList);
        Assert.assertEquals(0,extendIdentificationService.saveOrUpdateBatch(wipExtendIdentificationList));
    }
    @Test
    public void insertOptRecordBySn() throws Exception {
        extendIdentificationService.insertOptRecordBySn("2");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void deleteWipExtendIdentificationBySn() throws Exception {
        Assert.assertEquals(0,extendIdentificationService.deleteWipExtendIdentificationBySn(new WipExtendIdentification()));
    }
    @Test
    public void updateWipExtendIdentificationById() throws Exception {
        extendIdentificationService.updateWipExtendIdentificationById(new WipExtendIdentification());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void deleteWipExtendIdentificationById() throws Exception {
        extendIdentificationService.deleteWipExtendIdentificationById(new WipExtendIdentification());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void queryExportData() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("printer");
        sysLookupTypesDTO.setLookupMeaning("printer");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        List<String> snList = new ArrayList<>();
        snList.add("3");
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setFormType("2");
        assemblyRelationshipQueryDTO.setQueryType("0");
        assemblyRelationshipQueryDTO.setFormSnList(snList);
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();

        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipQuery(any())).thenReturn(wipExtendIdentificationList);
        extendIdentificationService.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        List<PsTask> psTaskList = new LinkedList<>();
        psTaskList.add(new PsTask());
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNoList(any())).thenReturn(psTaskList);
        assemblyRelationshipQueryDTO.setTaskNo("2");
        extendIdentificationService.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("2");setCreateBy("10270056");}});
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("3");setWorkStation("3");}});
        wipExtendIdentificationList.add(new WipExtendIdentification(){{setProcessCode("3");setWorkStation("2");}});
        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipQuery(any())).thenReturn(wipExtendIdentificationList);
        assemblyRelationshipQueryDTO.setFormType("4");
        extendIdentificationService.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        assemblyRelationshipQueryDTO.setFormType("2");
        List<BSProcessDTO> bsProcessDTOS = new LinkedList<>();
        BSProcessDTO c1 = new BSProcessDTO();
        c1.setProcessCode("1");
        bsProcessDTOS.add(c1);
        bsProcessDTOS.add(new BSProcessDTO(){{setProcessCode("2");}});
        bsProcessDTOS.add(new BSProcessDTO(){{setProcessCode("3");}});
        PowerMockito.when(wipEntityScanInfoService.getProcessInfo(any(),any(),any(),any(),any())).thenReturn(bsProcessDTOS);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("name");
        hrmPersonInfoDTOMap.put("10270056", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("10270047", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);
        Assert.assertNotNull(extendIdentificationService.queryExportData(assemblyRelationshipQueryDTO, 1, 10));


    }
    @Test
    public void countExportTotal() throws Exception {
        try {
            extendIdentificationService.countExportTotal(new AssemblyRelationshipQueryDTO());
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARMS_ERR, e.getMessage());
        }
        try {
            extendIdentificationService.countExportTotal(new AssemblyRelationshipQueryDTO(){{setFormType("3");setQueryType("0");}});
        }catch (Exception e){
            Assert.assertEquals(MessageId.TASK_BATCH_BARCODES_CANNOT_ALL_BE_EMPTY_SEARCH, e.getMessage());
        }
        List<String> snList = new ArrayList<>();
        for (int i = 0; i < 102; i++) {
            snList.add("3"+i);
        }

        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        assemblyRelationshipQueryDTO.setQueryType("0");
        assemblyRelationshipQueryDTO.setFormType("2");
        assemblyRelationshipQueryDTO.setFormSnList(snList);

        try {
            extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_NUMBER_OF_PRIMARY_BARCODES_CANNOT_EXCEED_100, e.getMessage());
        }
        assemblyRelationshipQueryDTO.setFormSnList(new ArrayList<>());
        assemblyRelationshipQueryDTO.setSnList(snList);
        try {
            extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_NUMBER_OF_SUB_BARCODES_CANNOT_EXCEED_100, e.getMessage());
        }
        assemblyRelationshipQueryDTO.setSnList(new ArrayList<>());
        assemblyRelationshipQueryDTO.setStartTime(new Date());
        extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);
        assemblyRelationshipQueryDTO.setEndTime(new Date());
        extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);

        assemblyRelationshipQueryDTO.setEndTime(DateUtils.addDays(new Date(),-1));
        try {
            extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.START_TIME_IS_LATER, e.getMessage());
        }
        assemblyRelationshipQueryDTO.setEndTime(DateUtils.addDays(new Date(),100));
        try {
            extendIdentificationService.countExportTotal(assemblyRelationshipQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.DURATION_IS_MORE_THAN_MONTH, e.getMessage());
        }
    }

    @Test
    public void insertWipExtendIdentification() throws Exception {
        extendIdentificationService.insertWipExtendIdentification(new WipExtendIdentification());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void dealWhenNotExistWipinfo() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        WipExtendIdentificationDTO wipExtendIdentificationDTO = new WipExtendIdentificationDTO();
        wipExtendIdentificationDTO.setSn("728262200001");
        wipExtendIdentificationDTO.setFormSn("mainSn");
        wipExtendIdentificationDTO.setProdPlanId("7778889");
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("126510150593ACB");
        list.add(prodBindingSettingDTO);

        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("ZTE200603023362");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTO.setIsLead("HFS");
        barcodeExpandDTO.setItemCode("126510150593ACB");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.when(CenterfactoryRemoteService.getPsTaskListFromCenterFactory(anyString())).thenReturn(Lists.newArrayList(new PsTask()));
        Whitebox.invokeMethod(extendIdentificationService,"dealWhenNotExistWipinfo",wipExtendIdentificationDTO,list);
        WipExtendIdentificationDTO wipExtendIdentificationDTOTwo = new WipExtendIdentificationDTO();
        wipExtendIdentificationDTOTwo.setSn("828262200001");
        wipExtendIdentificationDTOTwo.setFormSn("mainSn");
        wipExtendIdentificationDTOTwo.setProdPlanId("7778889");
        Whitebox.invokeMethod(extendIdentificationService,"dealWhenNotExistWipinfo",wipExtendIdentificationDTOTwo,list);
        Assert.assertEquals("728262200001",  wipExtendIdentificationDTO.getSn());
    }
    @Test
    public void getFormSnBindingList() throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7778889");
        wipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoRepository.getList(anyMap())).thenReturn(wipInfoList);
        ServiceData<Object> data = new ServiceData<>();
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("itemCode");
        list.add(prodBindingSettingDTO);
        data.setBo(list);
        PowerMockito.when(prodBindingSettingService.getProdBindingSettingList(anyObject())).thenReturn(data);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(any())).thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
            setNextProcess("λ");
            setRouteId("1");
        }}));
        WipExtendIdentificationDTO wipExtendIdentificationDTO = new WipExtendIdentificationDTO();
        wipExtendIdentificationDTO.setFormSn("mainSn");
        wipExtendIdentificationDTO.setProdPlanId("7778889");
        Assert.assertNotNull(extendIdentificationService.getFormSnBindingList(wipExtendIdentificationDTO));

    }
    @Test
    public void checkBomversionByPatter() throws Exception {
        Set<String>  set=new HashSet<>();
        set.add("[0]{3}");
        extendIdentificationService.checkBomversionByPatter(set,"000");
        Assert.assertFalse(extendIdentificationService.checkBomversionByPatter(set,"AA"));

    }
    @Test
    public void addHisInfo() {
        List<AssemblyResultHisEntityDTO> needInsertHisList =new ArrayList<>();
        AssemblyResultEntityDTO assemblyResultEntityDTO=new AssemblyResultEntityDTO();
        extendIdentificationService.addHisInfo(needInsertHisList,assemblyResultEntityDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void sendWipExtendIdentificationToPdm() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,RedisHelper.class,ConstantInterface.class,HttpRemoteUtil.class,PlanscheduleRemoteService.class);

        AssemblExecLogEntityDTO assemblExecLogEntityDTO=new AssemblExecLogEntityDTO();
        assemblExecLogEntityDTO.setLastUpdatedDate(new Date());
        assemblExecLogEntityDTO.setExecTime(new Date());
        PowerMockito.when(assemblExecLogService.getLastSuccessInfo()).thenReturn(assemblExecLogEntityDTO);
        List<SysLookupTypesDTO> sysLookupTypesDTOS=new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("11111111");
        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688001));
        sysLookupTypesDTOS.add(sysLookupTypesDTO);

        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("http://");
        sysLookupTypesDTO1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688002));
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);

        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        sysLookupTypesDTO2.setLookupMeaning("^[a-z0-9A-Z]{2,}");
        sysLookupTypesDTO2.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688003));
        sysLookupTypesDTOS.add(sysLookupTypesDTO2);

        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO3.setLookupMeaning("10270446");
        sysLookupTypesDTO3.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688004));
        sysLookupTypesDTOS.add(sysLookupTypesDTO3);

        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupMeaning("5");
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688005));
        sysLookupTypesDTOS.add(sysLookupTypesDTO4);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOS);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        List<WipExtendIdentification> wipExtendIdentificationList =new ArrayList<>();
        WipExtendIdentification wipExtendIdentification=new WipExtendIdentification();
        wipExtendIdentification.setTaskNo("taskNo");
        wipExtendIdentificationList.add(wipExtendIdentification);

        List<AssemblyResultEntityDTO> assemblyResultEntityDTOList=new ArrayList<>();
        AssemblyResultEntityDTO assemblyResultEntityDTO=new AssemblyResultEntityDTO();
        assemblyResultEntityDTO.setItemVersion("AA");
        assemblyResultEntityDTO.setItemCode("itemNo1");
        assemblyResultEntityDTOList.add(assemblyResultEntityDTO);
        PowerMockito.when(assemblyResultService.getListByStatus(anyObject())).thenReturn(assemblyResultEntityDTOList);

        PowerMockito.when(wipExtendIdentificationRepository.getWipExtendIdentificationToPdm(anyObject())).thenReturn(wipExtendIdentificationList);

        //PowerMockito.when(DatawbRemoteService.getBomVerByTaskNo(anyObject())).thenReturn(wipExtendIdentificationList);

        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<PDMProductMaterialResultDTO> pdmProductMaterialResultDTOList=new ArrayList<>();
        PDMProductMaterialResultDTO pdmProductMaterialResultDTO=new PDMProductMaterialResultDTO();
        pdmProductMaterialResultDTO.setEntityName("taskNo");
        pdmProductMaterialResultDTO.setBomRevision("AA");
        pdmProductMaterialResultDTOList.add(pdmProductMaterialResultDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(pdmProductMaterialResultDTOList);
                }})
        );
        List<PsTask> tempPaskList =new ArrayList<>();
        PsTask psTask=new PsTask();
        psTask.setTaskNo("taskNo");
        psTask.setItemNo("itemNo");
        tempPaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getTaskListByTaskNos(any())).thenReturn(tempPaskList);
        PowerMockito.when( factoryConfig.getCommonEntityId()).thenReturn("222");
        extendIdentificationService.sendWipExtendIdentificationToPdm("","55");

        sysLookupTypesDTOS.remove(sysLookupTypesDTO2);
        extendIdentificationService.sendWipExtendIdentificationToPdm("","55");
        Assert.assertEquals("taskNo", pdmProductMaterialResultDTO.getEntityName());
    }

    @Test
    public void sendWipExtToPdmAlarm() throws Exception {
        AssemblExecLogEntityDTO assemblExecLogEntityDTO=new AssemblExecLogEntityDTO();
        assemblExecLogEntityDTO.setLastUpdatedDate(new Date());
        PowerMockito.when(assemblExecLogService.getLastSuccessInfoWithin24Hours()).thenReturn(assemblExecLogEntityDTO);
        extendIdentificationService.sendWipExtToPdmAlarm();
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void setProdBindingSettingRepository() throws Exception {
        service.setProdBindingSettingRepository(repository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getProdBindingSettingDTOList() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        Assert.assertNotNull(service.getProdBindingSettingDTOList(dto));
        verify(repository, times(1)).getProdBindingSettingDTOList(dto);
    }

    @Test
    public void isFinish() throws Exception {
        List<ProdBindingSettingDTO> toBindingList = new ArrayList<>();
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setItemCode("11");
        dto.setUsageCount(new BigDecimal(1));
        Whitebox.invokeMethod(extendIdentificationService, "isFinish", toBindingList, "11");
        Assert.assertEquals("11", dto.getItemCode());
    }

    @Test
    public void getBindList() throws Exception {
        PowerMockito.mock(ConstantInterface.class);
        PowerMockito.mock(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setMainSn("11");
        PowerMockito.when(HttpRemoteService.remoteExe((InterfaceEnum) Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn("");
        PowerMockito.when(centerfactoryRemoteService.getNeedBindList(any(), any(), any())).thenReturn(any());
        ServiceData<Object> data = new ServiceData<>();
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("itemCode");
        list.add(prodBindingSettingDTO);
        data.setBo(list);
        PowerMockito.when(prodBindingSettingService.getProdBindingSettingList(dto)).thenReturn(data);
        Whitebox.invokeMethod(extendIdentificationService, "getBindList", "11", "11", "11", "11");
        Assert.assertEquals("11", dto.getMainSn());
    }

    @Test
    public void inBindList() throws Exception {
        List<ProdBindingSettingDTO> bindList = new ArrayList<>();
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setItemCode("11");
        bindList.add(dto);
        Whitebox.invokeMethod(extendIdentificationService, "inBindList", bindList, "11",new WipExtendIdentificationDTO());
        Assert.assertEquals("11", dto.getItemCode());
    }

    @Test
    public void getWpExtend() throws Exception {
        List<ProdBindingSettingDTO> bindList = new ArrayList<>();
        Map<String, Object> snMap = new HashMap<>();
        snMap.put("sn", "11");
        snMap.put("formType", "1");
        PowerMockito.when(wipExtendIdentificationRepository.getList(snMap)).thenReturn(new ArrayList<WipExtendIdentification>());
        Whitebox.invokeMethod(extendIdentificationService, "getWpExtend", "1", "11");
        Assert.assertEquals(new ArrayList<>(), bindList);
    }

    @Test
    public void itemCode2MaterialCode() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        List<ProdBindingSettingDTO> bindList = new ArrayList<>();
        StItemMessage stItemMessage = new StItemMessage();
        PowerMockito.when(datawbRemoteService.queryItemNoAndName("11")).thenReturn(stItemMessage);
        Whitebox.invokeMethod(extendIdentificationService, "itemCode2MaterialCode", bindList, "1", new WipExtendIdentificationDTO());
        Assert.assertEquals(new ArrayList<>(), bindList);
    }

    @Test
    public void sn2MaterialCode() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        List<ProdBindingSettingDTO> bindList = new ArrayList<>();
        StItemMessage stItemMessage = new StItemMessage();
        PowerMockito.when(datawbRemoteService.queryItemNoAndName("11")).thenReturn(stItemMessage);
        Whitebox.invokeMethod(extendIdentificationService, "sn2MaterialCode", bindList, new WipExtendIdentificationDTO());
        Assert.assertEquals(new ArrayList<>(), bindList);
    }

    @Test
    public void dealCardAfter() throws Exception {
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        wipInfoList.add(psWipInfo);
        List<PsWipInfo> formSnWipInfoList = new ArrayList<>();
        PsWipInfo ps = new PsWipInfo();
        formSnWipInfoList.add(ps);

        List<ProdBindingSettingDTO> toBindingList = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("itemCode1");
        prodBindingSettingDTO.setBindedCount(BigDecimal.ONE);
        prodBindingSettingDTO.setUsageCount(BigDecimal.ONE);
        toBindingList.add(prodBindingSettingDTO);
        dto.setSubItemNo("itemCode1");
        Whitebox.invokeMethod(extendIdentificationService, "dealCardAfter", dto, wipInfoList, formSnWipInfoList, toBindingList);
        Assert.assertEquals("itemCode1", dto.getSubItemNo());
    }


    @Test
    public void dealWhenExistWipInfo() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,PlanscheduleRemoteService.class);
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        dto.setSn("777611800026");
        dto.setFormSn("777611900002");
        List<PsWipInfo> wipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7778889");
        psWipInfo.setSn("777888900001");
        psWipInfo.setSecAssembleFlag("Y");
        psWipInfo.setItemNo("126510150593ACB");
        wipInfos.add(psWipInfo);
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO a1 = new ProdBindingSettingDTO();
        a1.setBoundNo(BigDecimal.ONE);
        a1.setMainProductCode("main");
        a1.setUsageCount(BigDecimal.ONE);
        a1.setProductCode("126510150593AHB");
        a1.setItemCode("126510150593ACB");
        list.add(a1);
        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO4 = new SysLookupTypesDTO();
        sysLookupTypesDTO4.setLookupMeaning("Y");
        sysLookupTypesDTO4.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6688005));
        sysLookupTypesDTOS.add(sysLookupTypesDTO4);

        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(sysLookupTypesDTOS);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo");
        psTask.setTaskQty(new BigDecimal(NumConstant.NUM_100));
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("itemNo");
        psTask2.setTaskQty(new BigDecimal(NumConstant.NUM_100));
        psTaskList.add(psTask2);
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTask(anyMap())).thenReturn(psTaskList);

        ServiceData<Object> data = new ServiceData<>();
        List<ProdBindingSettingDTO> prodBindingSettingDTOList = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("itemCode");
        prodBindingSettingDTO.setMainProductCode("main");
        prodBindingSettingDTOList.add(prodBindingSettingDTO);
        data.setBo(list);
        PowerMockito.when(prodBindingSettingService.getProdBindingSettingList(anyObject())).thenReturn(data);
        Whitebox.invokeMethod(extendIdentificationService,"dealWhenExistWipInfo",dto,wipInfos,wipInfos,list);
        Assert.assertEquals("itemCode", prodBindingSettingDTO.getItemCode());
    }
    @Test
    public void insertSubSnBinding() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class,CommonUtils.class,PlanscheduleRemoteService.class);
        Map<String, Object> snMap = new HashMap<>();
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        dto.setSn("777611800026");
        dto.setFormSn("777611900002");
        snMap.put("sn", dto.getSn());
        List<PsWipInfo> wipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setAttribute1("7778889");
        psWipInfo.setSn("777888900001");
        psWipInfo.setItemNo("777888900001");
        wipInfos.add(psWipInfo);
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setMainSn("777611900002");
        ServiceData<Object> data = new ServiceData<>();
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        ProdBindingSettingDTO a1 = new ProdBindingSettingDTO();
        a1.setBoundNo(BigDecimal.ONE);
        a1.setUsageCount(BigDecimal.ONE);
        a1.setProductCode("126510150593AHB");
        a1.setItemCode("126510150593ACB");
        list.add(a1);
        data.setBo(list);
        PowerMockito.when(prodBindingSettingService.getProdBindingSettingList(Mockito.any())).thenReturn(data);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        psWorkOrderBasic.setRouteId("test123");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(psWorkOrderBasic);
        //PowerMockito.when(prodBindingSettingService.getPartialProdBindingSettingList(Mockito.any())).thenReturn(data);
        PowerMockito.when(psWipInfoRepository.getList(anyMap())).thenReturn(wipInfos);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");

        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailByRouteIds(any())).thenReturn(Lists.newArrayList(new CtRouteDetailDTO(){{
            setNextProcess("λ");
            setRouteId("1");
        }}));

        List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("ZTE200603023362");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTO.setIsLead("HFS");
        barcodeExpandDTO.setItemCode("126510150593ACB");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);

        // 流水线报错，临时改动
        try {
            extendIdentificationService.insertSubSnBinding(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

        List<ProdUnbindingSetting> unBindingList = new LinkedList<>();
        ProdUnbindingSetting c1 = new ProdUnbindingSetting();
        unBindingList.add(c1);
        PowerMockito.when(centerfactoryRemoteService.postUnBindingList(Mockito.any())).thenReturn(unBindingList);
        PsWipInfo ps = new PsWipInfo();
        ps.setItemNo("126510150593ACB");
        wipInfos.add(ps);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any()))
                .thenReturn(wipInfos);
        try {
            extendIdentificationService.insertSubSnBinding(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

    }

    @Test
    public void insertBindInfoTest() throws Exception {
        extendIdentificationService.insertBindInfo(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }


    @Test
    public void controlByProdPlanId() throws Exception {
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        List<PsWipInfo> formSnWipInfoLis = new ArrayList<>();
        PsWipInfo dto1 = new PsWipInfo();
        PsWipInfo dto2 = new PsWipInfo();
        dto1.setAttribute1("7776119");
        dto1.setItemNo("129206751186ACB");
        dto2.setAttribute1("7776119");
        dto2.setItemNo("126510150593AHB");
        wipInfoList.add(dto1);
        formSnWipInfoLis.add(dto2);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> list =new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTOUrl=new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        list.add(sysLookupTypesDTOUrl);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(list);
        extendIdentificationService.verifyControlByProdPlanId(dto1,dto2);
        Assert.assertEquals("7776119", dto1.getAttribute1());
    }

    @Test
    public void getSubSnBinding() throws Exception {
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        Assert.assertNotNull(extendIdentificationService.getSubSnBinding(dto));
    }

    @Test
    public void getFormsnBindingList() throws Exception {
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        Assert.assertNotNull(extendIdentificationService.getFormSnBindingList(dto));
    }

    @Test
    public void unBind() {
        extendIdentificationService.assemblyUnbind("1",
                Lists.newArrayList(new WipExtendIdentification() {{
                    setMainProductCode("1");
                }}, new WipExtendIdentification() {{}})
        );
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handlerQueryBoundSnAndCheckCanPassStationTest() {
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        dto.setSubItemNo("itemNo");
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        extendIdentificationService.handlerQueryBoundSnAndCheckCanPassStation(dto, bindingList);

        dto.setProcessCode("process");
        List<WipExtendIdentification> boundList = new ArrayList<>();
        PowerMockito.when(wipExtendIdentificationRepository.listProdBindingInfoBySnAndProcessCode(Mockito.anyString(), Mockito.anyString())).thenReturn(boundList);

        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setItemCode("itemNo");
        prodBindingSettingDTO.setBoundNo(new BigDecimal("1"));
        prodBindingSettingDTO.setUsageCount(new BigDecimal("3"));
        bindingList.add(prodBindingSettingDTO);
        extendIdentificationService.handlerQueryBoundSnAndCheckCanPassStation(dto, bindingList);

        Assert.assertNotNull(extendIdentificationService.handlerQueryBoundSnAndCheckCanPassStation(dto, bindingList));

    }

    @Test
    public void getProdBindingListTest() throws Exception {
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        String formSn = "formSn";
        String prodPlanId = "prodPlanId";
        extendIdentificationService.getProdBindingList(dto, formSn, prodPlanId);
        dto.setProcessCode("processCode");
        extendIdentificationService.getProdBindingList(dto, formSn, prodPlanId);
        dto.setProdPlanId("prod");
        Assert.assertNull(extendIdentificationService.getProdBindingList(dto, formSn, prodPlanId));

    }
}
