package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.PsWipInfoService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.impl.AssemblyRelaScanServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.SpringUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.domain.model.WarehouseEntryStatics;
import com.zte.domain.model.WarehouseRequirementDetail;
import com.zte.domain.model.WarehouseRequirementDetailRepository;
import com.zte.domain.model.WarehouseRequirementInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ConstantInterface.class, HttpRemoteUtil.class, MicroServiceRestUtil.class, CommonUtils.class, BasicsettingRemoteService.class, RedisCacheUtils.class, JacksonJsonConverUtil.class,
        ProductionDeliveryRemoteService.class, SpringUtil.class, RedisHelper.class,DatawbRemoteService.class,CenterfactoryRemoteService.class,HttpRemoteService.class,WarehouseEntryInfoServiceImpl.class,PlanscheduleRemoteService.class})
public class WarehouseEntryInfoServiceImpl3Test extends PowerBaseTestCase {

    @InjectMocks
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Mock
    private ErpRemoteService erpRemoteService;

    @Mock
    private WarehouseRequirementInfoRepository warehouseRequirementInfoRepository;

    @Mock
    private WarehouseRequirementDetailRepository warehouseRequirementDetailRepository;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private RedisLock redisLock;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;
	@Mock
	private IMESLogService imesLogService;
    @Mock
    private AssemblyRelaScanServiceImpl assemblyRelaScanService;
    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(SpringUtil.class);
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.mockStatic(DatawbRemoteService.class, PlanscheduleRemoteService.class);

    }

    @Test
    public void warehouseDataStasticsTest() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CenterfactoryRemoteService.class);
        String startTime = "2022-05-01 12:26:08";
        String endTime = "2022-07-02 12:26:08";
        SysLookupValues values = new SysLookupValues();
        PowerMockito.when(CenterfactoryRemoteService.getSysLookupValuesCenter(anyString())).thenReturn(values);
        warehouseEntryInfoServiceImpl.sync(startTime, endTime);
        values.setLookupMeaning("2023-01-01 00:00:00");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2022-05-05 20:03:31");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);
        try {
            warehouseEntryInfoServiceImpl.sync(startTime, endTime);
        } catch (Exception e) {
            Assert.assertEquals("2022-05-01 12:26:08", startTime);
        }
        try {
            warehouseEntryInfoServiceImpl.sync(startTime, endTime);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SYS_LOOK_PARAM_NOT_CONFIG.equals(e.getExMsgId()));
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTO);

        List<WarehouseEntryStatics> statDataList = new ArrayList<WarehouseEntryStatics>();
        WarehouseEntryStatics warehouseEntryStatics = new WarehouseEntryStatics();
        warehouseEntryStatics.setWarehouseEntryId("10292893");
        warehouseEntryStatics.setFactoryId("10292893");
        statDataList.add(warehouseEntryStatics);
        PowerMockito.when(warehouseEntryInfoRepository.warehouseEntryStatics(anyString(), anyString(), anyInt(), anyInt())).thenReturn(statDataList);

        List<String> existWarehouseEntryIdList = new ArrayList<>();
        existWarehouseEntryIdList.add("10309567");
        PowerMockito.when(CenterfactoryRemoteService.queryWarehouseEntryIdIsExist(anyList())).thenReturn(existWarehouseEntryIdList);

        warehouseEntryInfoServiceImpl.sync(startTime, endTime);
    }

    @Test
    public void updateImuForSubCardTo613()throws Exception{
        String json = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\":{\"subCategory\":\"111\",\"sysLotCode\":null,\"lot\":null,\"prodDate\":null,\"barcode\":\"ZJ19103100021\"},"
                + "\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsBarcodeInfoController@generate\","
                + "\"code\":\"0000\",\"costTime\":\"65ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Oct 31 10:35:25 CST 2019\","
                + "\"tag\":\"1111\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10240988\"}}";
        JsonNode jsonNode =  JacksonJsonConverUtil.getMapperInstance().readTree(json);
        PowerMockito.when(DatawbRemoteService.updateImuForSubCardTo613(Mockito.any()))
                .thenReturn(jsonNode);
        List<Map<String, String>> boards=new ArrayList<>();
        warehouseEntryInfoServiceImpl.updateImuForSubCardTo613(boards);
        Assert.assertEquals("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\":{\"subCategory\":\"111\",\"sysLotCode\":null,\"lot\":null,\"prodDate\":null,\"barcode\":\"ZJ19103100021\"},"
                + "\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsBarcodeInfoController@generate\","
                + "\"code\":\"0000\",\"costTime\":\"65ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Oct 31 10:35:25 CST 2019\","
                + "\"tag\":\"1111\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10240988\"}}", json);
    }

    @Test
    public void writeErpTransactionNew()throws Exception{
        String json = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\":{\"subCategory\":\"111\",\"sysLotCode\":null,\"lot\":null,\"prodDate\":null,\"barcode\":\"ZJ19103100021\"},"
                + "\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsBarcodeInfoController@generate\","
                + "\"code\":\"0000\",\"costTime\":\"65ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Oct 31 10:35:25 CST 2019\","
                + "\"tag\":\"111\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10240988\"}}";
        JsonNode jsonNode =  JacksonJsonConverUtil.getMapperInstance().readTree(json);
        PowerMockito.when(DatawbRemoteService.writeErpTransactionNew(Mockito.any()))
                .thenReturn(jsonNode);
        WarehouseEntryInfo warehouseEntryInfo=new WarehouseEntryInfo();
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.writeErpTransactionNew(warehouseEntryInfo));
    }



    @Test
    public void dealTridimensionalWarehousEntry() throws Exception {

        warehouseEntryInfoServiceImpl = PowerMockito.spy(new WarehouseEntryInfoServiceImpl());

        StandardMouldStoreHouseDTO dto = new StandardMouldStoreHouseDTO();
        List<StandardMouldContainerDTO> list = new ArrayList<>();
        StandardMouldContainerDTO sdto = new StandardMouldContainerDTO();
        sdto.setContainerID("CT102020042400000001");
        List<String> snList = new ArrayList<>();
        snList.add("706257400044");
        sdto.setSnList(snList);
        list.add(sdto);
        dto.setPltList(list);
        dto.setTransNum("RK522018102600017");
        ServiceData  serviceData = new ServiceData();

        doReturn(serviceData).when(warehouseEntryInfoServiceImpl).dealTridimensionalRk(dto, list);
        doReturn(serviceData).when(warehouseEntryInfoServiceImpl).dealTridimensionalZpk(dto, list);

        Assert.assertNotNull(warehouseEntryInfoServiceImpl.dealTridimensionalWarehousEntry(dto));

    }


    @Test
    public void dealTridimensionalRk() throws Exception {
        StandardMouldStoreHouseDTO dto = new StandardMouldStoreHouseDTO();
        dto.setTransNum("RK552020071802641");
        List<StandardMouldContainerDTO> list = new ArrayList<>();
        StandardMouldContainerDTO standardMouldContainerDTO = new StandardMouldContainerDTO();
        standardMouldContainerDTO.setContainerID("lpn123");
        standardMouldContainerDTO.setSnList(new ArrayList<>());
        list.add(standardMouldContainerDTO);
        dto.setPltList(list);

        List<WarehouseEntryInfoDetailDTO> weidlist = new ArrayList<>();
        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setApplicationNo("2242135235");
        warehouseEntryInfoDetailDTO.setApplicationType("42352325");
        warehouseEntryInfoDetailDTO.setStatus("2");
        warehouseEntryInfoDetailDTO.setLpn("lpn123");
        warehouseEntryInfoDetailDTO.setTaskType(MpConstant.TASK_TYPE_RETURN);
        weidlist.add(warehouseEntryInfoDetailDTO);

        Map<String, Object> record = new HashMap<>();
        record.put("billNo", dto.getTransNum());
        warehouseEntryInfoServiceImpl.setWarehouseEntryInfoRepository(warehouseEntryInfoRepository);
        when(warehouseEntryInfoRepository.selectWarehouseEntryInfoDetailByBillNo(record)).thenReturn(weidlist);
        warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);

        warehouseEntryInfoDetailDTO.setDetailStatus(Constant.WARHOUSE_DETAIL_STATUS_ZERO);
        when(erpRemoteService.invokeErpImport(Mockito.any(), Mockito.any())).thenReturn(true);
        ServiceData serviceData = warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);
        assert RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode());

        when(erpRemoteService.invokeErpImport(Mockito.any(), Mockito.any())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);
        } catch (MesBusinessException e) {
            assert MessageId.TRANSFER_WARHOUSE_FAILED.equals(e.getExMsgId());
        }

        warehouseEntryInfoDetailDTO.setTaskType("other");
        when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenReturn(true);
        when(erpRemoteService.invokeErpDone(Mockito.any(), Mockito.any())).thenReturn(true);
        serviceData = warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);
        assert RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode());

        when(erpRemoteService.invokeErpDone(Mockito.any(), Mockito.any())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);
        } catch (MesBusinessException e) {
            assert MessageId.ERP_MACHINE_MOVE_COMPLETE_FAILED.equals(e.getExMsgId());
        }

        when(erpRemoteService.invokeErpMove(Mockito.any(), Mockito.any())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.dealTridimensionalRk(dto, list);
        } catch (MesBusinessException e) {
            assert MessageId.ERP_MACHINE_MOVE_COMPLETE_FAILED.equals(e.getExMsgId());
        }
    }

    @Test
    public void submitWareEntryInfo2() throws Exception {
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        String updateWorkStation = "";
        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_SINGLE);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
        } catch (MesBusinessException e) {
            assert MessageId.ERP_WAREHOUSING_FAILED.equals(e.getExMsgId());
        }

        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_HOME_BOARD);
        warehouseEntryInfo.setSnList(new ArrayList<>());
        PowerMockito.when(erpRemoteService.invokeErpMove(anyObject(), anyObject())).thenReturn(true);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
        } catch (MesBusinessException e) {
            assert MessageId.ERP_WAREHOUSING_FAILED.equals(e.getExMsgId());
        }

        PowerMockito.when(erpRemoteService.invokeErpMove(anyObject(), anyObject())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
        } catch (MesBusinessException e) {
            assert MessageId.ERP_WAREHOUSING_FAILED.equals(e.getExMsgId());
        }

        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_WHOLE);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        PowerMockito.when(erpRemoteService.invokeErpMove(anyObject(), anyObject())).thenReturn(true);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        warehouseEntryInfo.setBillType(MpConstant.BILL_TYPE_FIVE);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        warehouseEntryInfo.setStockType(Constant.STOCK_TYPE_HOME_BOARD);
        warehouseEntryInfo.setTaskType(MpConstant.TASK_TYPE_RETURN);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        StringBuffer errMsg = new StringBuffer();
        errMsg.append("222");
        PowerMockito.whenNew(StringBuffer.class).withNoArguments().thenReturn(errMsg);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);

        warehouseEntryInfo.setTaskType(null);
        warehouseEntryInfoServiceImpl.submitWareEntryInfo(warehouseEntryInfo, sysLookupTypesDTO, updateWorkStation);
    }

    @Test
    public void dealTridimensionalZpk() throws Exception {

        StandardMouldStoreHouseDTO dto = new StandardMouldStoreHouseDTO();
        dto.setTransNum("ZPK552020071802641");
        List<StandardMouldContainerDTO> list = new ArrayList<>();
        StandardMouldContainerDTO standardMouldContainerDTO = new StandardMouldContainerDTO();
        standardMouldContainerDTO.setContainerID("21425262342");
        list.add(standardMouldContainerDTO);
        dto.setPltList(list);

        List<WarehouseEntryInfoDetailDTO> weidlist = new ArrayList<>();
        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setApplicationNo("2242135235");
        warehouseEntryInfoDetailDTO.setApplicationType("42352325");
        warehouseEntryInfoDetailDTO.setStatus("2");
        weidlist.add(warehouseEntryInfoDetailDTO);

        PowerMockito.when(ProductionDeliveryRemoteService.queryApplyOrderDetails(Mockito.anyObject())).thenReturn(weidlist);

        Assert.assertNotNull(warehouseEntryInfoServiceImpl.dealTridimensionalZpk(dto, list));

    }

    @Test
    public void permissionControlForStock() throws Exception {
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.permissionControlForStock("10270446"));
    }

    @Test
    public void getRKBillNo() throws Exception {

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.when(RedisCacheUtils.get(anyObject(), anyObject())).thenReturn(null);
        Assert.assertNull(warehouseEntryInfoServiceImpl.getRKBillNo("58"));
    }

    @Test
    public void getItemListEntityListByTaskNo() throws Exception {
            PowerMockito.mockStatic(MicroServiceRestUtil.class);
            PowerMockito.mockStatic(ConstantInterface.class);
            PowerMockito.mockStatic(HttpRemoteUtil.class);
            String json = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                    + "\"bo\":{\"current\":1,\"total\":7,\"rows\":[{\"factoryId\":2,\"factoryCode\":\"0000\","
                    + "\"factoryName\":\"通用\",\"address\":\"0000\",\"remark\":\"test000\",\"erpCode\":\"0000\","
                    + "\"lastUpdatedDate\":\"2017-09-2817:21:27\",\"createDate\":\"2017-09-2514:28:32\","
                    + "\"lastUpdatedBy\":\"10207212\",\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,"
                    + "\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"createDate\":\"2017-09-2514:28:32\","
                    + "\"createdBy\":\"10207212\",\"organizationId\":31}]},\"other\":null}";
            PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyMap())).thenReturn(json);
            PowerMockito.mockStatic(JacksonJsonConverUtil.class);
            PowerMockito.mockStatic(MicroServiceRestUtil.class);
            PowerMockito.mockStatic(ConstantInterface.class);
            PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/databack/flow/add");
            PowerMockito.mockStatic(HttpRemoteUtil.class);
            Page<ItemListEntityDTO> pageList = new Page<ItemListEntityDTO>();
            PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                    JSON.toJSONString(new ServiceData() {{
                        setBo(pageList);
                    }})
            );
            Assert.assertThrows(NullPointerException.class, () -> warehouseEntryInfoServiceImpl.getItemListEntityListByTaskNo("58",new BigDecimal(8)));
    }

    @Test
    public void checkQty() throws Exception {
        //try {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        dto.setTaskQty(new BigDecimal("10"));
        dto.setCommitedQty(new BigDecimal("5"));
        dto.setSubmitQty(new BigDecimal("5"));
        List<ItemListEntityDTO> itemListEntityDTOList = new ArrayList<>();
        ItemListEntityDTO itemListEntityDTO = new ItemListEntityDTO();
        itemListEntityDTO.setItemNo("056472000025");
        itemListEntityDTO.setWipSupplyType("1");
        itemListEntityDTO.setSupplySubinventory(null);
        itemListEntityDTO.setQuantityIssued(".06");
        itemListEntityDTO.setRequiredQuantity(".06");
        itemListEntityDTOList.add(itemListEntityDTO);
        ItemListEntityDTO itemListEntityDTO1 = new ItemListEntityDTO();
        itemListEntityDTO1.setItemNo("056472000025");
        itemListEntityDTO1.setWipSupplyType("1");
        itemListEntityDTO1.setSupplySubinventory(null);
        itemListEntityDTO1.setQuantityIssued(".06");
        itemListEntityDTO1.setRequiredQuantity(".06");
        itemListEntityDTOList.add(itemListEntityDTO1);
        Assert.assertEquals("", warehouseEntryInfoServiceImpl.checkQty(dto, itemListEntityDTOList));
        itemListEntityDTO1.setRequiredQuantity(".1");
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.ITEM_CODE_NOT_ENOUGH_SENT, new String[]{null,"056472000025"})).thenReturn("1");
        Assert.assertEquals("1", warehouseEntryInfoServiceImpl.checkQty(dto, itemListEntityDTOList));
        List<MtlRelatedItemsEntityDTO> list = new ArrayList<>();
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        list.add(mtlRelatedItemsEntityDTO);
        mtlRelatedItemsEntityDTO.setItemCode("056472000025");
        mtlRelatedItemsEntityDTO.setReplaceItemCode("056472000026");
        PowerMockito.when(assemblyRelaScanService.getReplaceItemByErp(anyList(), anyBoolean())).thenReturn(list);
        Assert.assertEquals("1", warehouseEntryInfoServiceImpl.checkQty(dto, itemListEntityDTOList));
        itemListEntityDTO.setItemNo("056472000026");
        Assert.assertEquals("", warehouseEntryInfoServiceImpl.checkQty(dto, itemListEntityDTOList));
        //}catch (Exception e){}
    }

    @Test
    public void receiveWarehouseEntryInfoBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        list.add(warehouseEntryInfo);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyObject(), anyObject())).thenReturn(sysLookupTypesDTO);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyObject(), anyObject(), anyObject())).thenReturn(Boolean.TRUE);

        PowerMockito.when(SpringUtil.getBean(WarehouseEntryInfoServiceImpl.class))
                .thenReturn(warehouseEntryInfoServiceImpl);
        warehouseEntryInfoServiceImpl.receiveWarehouseEntryInfoBatch(list, "test");

        try {
            StringBuffer errMsg = new StringBuffer();
            errMsg.append("23");
            PowerMockito.whenNew(StringBuffer.class).withNoArguments().thenReturn(errMsg);
            warehouseEntryInfoServiceImpl.receiveWarehouseEntryInfoBatch(list, "test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

    }

    @Test
    public void closeWarehouseEntryInfoBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        warehouseEntryInfo.setProdplanId("8888421");
        list.add(warehouseEntryInfo);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyObject(), anyObject())).thenReturn(sysLookupTypesDTO);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyObject(), anyObject(), anyObject())).thenReturn(Boolean.TRUE);

        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryInfoBatch(anyObject())).thenReturn(1);

        PowerMockito.when(warehouseRequirementDetailRepository.selectByPrimaryKey(anyObject())).thenReturn(new WarehouseRequirementDetail());
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("12349999999");
        psWipInfoList.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getWipInfoByBillNo(anyObject(), anyObject())).thenReturn(psWipInfoList);


        warehouseEntryInfoServiceImpl.closeWarehouseEntryInfoBatch(list);

        warehouseEntryInfo.setBillType("2");
        List<PsScanHistory> psScanHistories = new LinkedList<>();
        PsScanHistory a1 = new PsScanHistory();
        a1.setSourceImu(new BigDecimal("23"));
        a1.setSn("888842100001");
        psScanHistories.add(a1);
        PowerMockito.when(psScanHistoryRepository.getList(Mockito.any())).thenReturn(psScanHistories);

        Assert.assertEquals(0,warehouseEntryInfoServiceImpl.closeWarehouseEntryInfoBatch(list));

    }

    @Test
    public void rejectWarehouseEntryInfoBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552020071802641");
        warehouseEntryInfo.setBillType("test");
        warehouseEntryInfo.setLastUpdatedBy("10275524");
        WarehouseEntryInfo warehouseEntryInfo1 = new WarehouseEntryInfo();
        warehouseEntryInfo1.setBillNo("RK552020071802641");
        warehouseEntryInfo1.setStockType(Constant.STOCK_TYPE_K2);
        warehouseEntryInfo1.setLastUpdatedBy("10275524");
        list.add(warehouseEntryInfo1);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupType(new BigDecimal("1218"));
        sysLookupTypesDTO.setLookupCode(new BigDecimal("1218004"));
        sysLookupTypesDTO.setLookupMeaning("N");

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyObject(), anyObject())).thenReturn(sysLookupTypesDTO);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);

        PowerMockito.when(warehouseEntryInfoRepository.validateWarehouseEntryInfoStatus(anyObject(), anyObject(), anyObject())).thenReturn(Boolean.TRUE);

        PowerMockito.when(warehouseEntryInfoRepository.updateWarehouseEntryInfoBatch(anyObject())).thenReturn(1);

        PowerMockito.when(warehouseRequirementDetailRepository.selectByPrimaryKey(anyObject())).thenReturn(new WarehouseRequirementDetail());

        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("1234");
        psWipInfoList.add(psWipInfo);
        warehouseEntryInfo.setPsWipInfoList(psWipInfoList);
        warehouseEntryInfo1.setPsWipInfoList(psWipInfoList);
        PowerMockito.when(psWipInfoService.getWipInfoByBillNo(anyObject(), anyObject())).thenReturn(psWipInfoList);

        PowerMockito.when(psScanHistoryRepository.getList(anyObject())).thenReturn(null);

        List<BarSubmitDTO> barSubmitDTOS = new LinkedList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setBillNo("123");
        barSubmitDTO.setStatus(0L);
        barSubmitDTOS.add(barSubmitDTO);
        PowerMockito.when(datawbRemoteService.queryBarSubmitInfo(Mockito.anyList())).thenReturn(barSubmitDTOS);
		try {
			warehouseEntryInfoServiceImpl.rejectWarehouseEntryInfoBatch(list);
		} catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
		}
    }

    @Test
    public void updateImuBySn() throws Exception {
        List<Map<String, String>> boards = new ArrayList<>();
        Map<String, String> dto = new HashMap<>();
        String url = "http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBySn";
        dto.put("sn", "111");
        boards.add(dto);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBySn");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(Lists.newArrayList(
                                    new BsItemInfo() {{
                                        setItemNo("1");
                                    }}
                            ));
                        }}));

        warehouseEntryInfoServiceImpl.updateImuBySn(boards);
        Assert.assertEquals("http://************/zte-mes-manufactureshare-datawbsys/WB/updateImuBySn", url);
    }

    @Test
    public void warehouseEntryInfoWritebackStep() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/barSubmit/add");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(Lists.newArrayList(
                                    new BsItemInfo() {{
                                        setItemNo("1");
                                    }}
                            ));
                        }}));
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setProdplanId("7778889");
        warehouseEntryInfoDTO.setStatus("22");
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.warehouseEntryInfoWritebackStep(warehouseEntryInfoDTO));
    }

    @Test
    public void warehouseEntryInfoWritebackStepRollBack() throws Exception {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/WB/barSubmit/add");

        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(Lists.newArrayList(
                                    new BsItemInfo() {{
                                        setItemNo("1");
                                    }}
                            ));
                        }}));
        warehouseEntryInfoServiceImpl.warehouseEntryInfoWritebackStepRollBack("77484");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    /* Started by AICoder, pid:e329c297b6y349914b79092fc0455867e994ef10 */
    @Test
    public void testGetWarehouseEntryInfoDTOListByTaskNos_whenValidInput_returnsExpectedResult() {
        // Arrange
        Set<String> taskNos = Collections.singleton("task1");
        List<WarehouseEntryInfoDTO> expected = Collections.singletonList(new WarehouseEntryInfoDTO());

        PowerMockito.when(warehouseEntryInfoRepository.getWarehouseEntryInfoDTOListByTaskNos(taskNos)).thenReturn(expected);

        // Act
        List<WarehouseEntryInfoDTO> result = warehouseEntryInfoServiceImpl.getWarehouseEntryInfoDTOListByTaskNos(taskNos);

        // Assert
        assertNotNull(result);
        assertEquals(expected, result);
        verify(warehouseEntryInfoRepository).getWarehouseEntryInfoDTOListByTaskNos(taskNos);
    }

    @Test
    public void testGetWarehouseEntryInfoDTOListByTaskNos_whenEmptySet_returnsEmptyList() {
        // Arrange
        Set<String> taskNos = Collections.emptySet();
        List<WarehouseEntryInfoDTO> expected = Collections.emptyList();

        when(warehouseEntryInfoRepository.getWarehouseEntryInfoDTOListByTaskNos(taskNos)).thenReturn(expected);

        // Act
        List<WarehouseEntryInfoDTO> result = warehouseEntryInfoServiceImpl.getWarehouseEntryInfoDTOListByTaskNos(taskNos);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(warehouseEntryInfoRepository).getWarehouseEntryInfoDTOListByTaskNos(taskNos);
    }

    @Test
    public void testTaskStockQuery_whenValidInput_returnsExpectedResult() {
        // Arrange
        WarehouseBatchQueryDTO warehouseBatchQueryDTO = new WarehouseBatchQueryDTO();
        List<WarehouseEntryInfoDTO> expected = Arrays.asList(new WarehouseEntryInfoDTO());

        when(warehouseEntryInfoRepository.taskStockQuery(warehouseBatchQueryDTO)).thenReturn(expected);

        // Act
        List<WarehouseEntryInfoDTO> result = warehouseEntryInfoServiceImpl.taskStockQuery(warehouseBatchQueryDTO);

        // Assert
        assertNotNull(result);
        assertEquals(expected, result);
        verify(warehouseEntryInfoRepository).taskStockQuery(warehouseBatchQueryDTO);
    }
    /* Ended by AICoder, pid:e329c297b6y349914b79092fc0455867e994ef10 */
}
