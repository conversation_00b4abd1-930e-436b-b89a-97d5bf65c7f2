package com.zte.test;

import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.WipDailyStatisticServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.PsScanHistoryDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.WipDailyStatisticQueryDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.PowerBaseTestCase;
import com.zte.common.model.MessageId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

/**
 * @Author:
 * @Date: 2020/9/18 11:10
 */
@PrepareForTest({PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class, RedisHelper.class,
        MicroServiceDiscoveryInvoker.class})
public class WipDailyStatisticServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WipDailyStatisticServiceImpl service;
    @Mock
    private WipDailyStatisticReportRepository repository;
    @Mock
    private WarehousehmEntryDetailRepository warehouseDetailRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Before
    public void init(){
        PowerMockito.mockStatic(BasicsettingRemoteService.class,MicroServiceDiscoveryInvoker.class);
    }
    @Test
    public void calculateComponentMaintenanceAndCompleteMachineMaintenance() throws Exception {
        List<WipDailyStatisticReport> wipDailyStatisticReportList = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReportEntityDTO = new WipDailyStatisticReport();
        wipDailyStatisticReportEntityDTO.setProdplanId("7");
        wipDailyStatisticReportList.add(wipDailyStatisticReportEntityDTO);
        List<String> prodplanIdList = new ArrayList<>();
        prodplanIdList.add("1");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(6001040));
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);
        Map<String, Integer> completeMachineMap = new HashMap<>();
        completeMachineMap.put("7",1);
        completeMachineMap.put("8",1);
        PowerMockito.when(psWipInfoService.calculateMaintenanceQty(any(),any(),anyString(),anyString())).thenReturn(completeMachineMap);
        Whitebox.invokeMethod(service,"calculateComponentMaintenanceAndCompleteMachineMaintenance",prodplanIdList,wipDailyStatisticReportList,"Y");
        Assert.assertEquals("7", wipDailyStatisticReportEntityDTO.getProdplanId());
    }
    @Test
    public void multipleMailExport() throws Exception {
        List<WipDailyStatisticReport> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReportEntityDTO = new WipDailyStatisticReport();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        WipDailyStatisticQueryDTO wipDailyStatisticQueryDTO = new WipDailyStatisticQueryDTO();
        wipDailyStatisticQueryDTO.setIncludeRealtime(false);
        List<String> selectedFields = new ArrayList<>();
        selectedFields.add("taskNo");
        wipDailyStatisticQueryDTO.setSelectedFields(selectedFields);
        wipDailyStatisticQueryDTO.setTitle(selectedFields.toArray(new String[selectedFields.size()]));
        wipDailyStatisticQueryDTO.setProps(selectedFields.toArray(new String[selectedFields.size()]));

        Page<WipDailyStatisticReport> firstPage = new Page<>();
        firstPage.setTotalPage(NumConstant.NUM_24);
        firstPage.setRows(wipDailyStatisticReportEntityDTOList);
        firstPage.setTotalPage(NumConstant.NUM_24);
        firstPage.setTotal(NumConstant.NUM_60);
        PowerMockito.when(repository.selectPage(anyObject())).thenReturn(wipDailyStatisticReportEntityDTOList);
        try {
            Whitebox.invokeMethod(service,"multipleMailExport",wipDailyStatisticQueryDTO,firstPage,new StringBuilder());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
    @Test
    public void mailExport2()throws Exception {
        WipDailyStatisticQueryDTO dto = new WipDailyStatisticQueryDTO();
        Page<WipDailyStatisticReport> page = new Page<>();
        page.setTotalPage(5);

        List<WipDailyStatisticReport> list = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReport = new WipDailyStatisticReport();
        list.add(wipDailyStatisticReport);
        page.setRows(list);
        PowerMockito.when(repository.selectPage(Mockito.anyObject())).thenReturn(list);

        Map<String, String> header = new HashMap<>();
        Whitebox.invokeMethod(service,"mailExport",dto,page,header);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void mailExport()throws Exception {
        WipDailyStatisticQueryDTO dto = new WipDailyStatisticQueryDTO();
        Page<WipDailyStatisticReport> page = new Page<>();
        page.setRows(null);
        Map<String, String> header = new HashMap<>();
        Whitebox.invokeMethod(service,"mailExport",dto,page,header);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void writeBackToTheCentralFactory()throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        service.writeBackToTheCentralFactory("10","10");
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        service.writeBackToTheCentralFactory("10","10");

        Page page = new Page();
        page.setTotalPage(5);
        Whitebox.invokeMethod(service,"synchronizeData","",new WipDailyStatisticQueryDTO(),page);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
    @Test
    public void testCopyProperties() {
        Map<String, Integer> snCountMap = new HashMap<>();
        snCountMap.put("123", 1);
        service.copyProperties(new WipDailyStatisticReport(), new PsTask() {{
            setTaskQty(new BigDecimal(1));
            setProdplanId("123");
        }});
        service.copyProperties(new WipDailyStatisticReport(), new PsTask() {{
            setFirstExpectedDeliveryDate(new Date());
            setLastDeliveryDate(new Date(System.currentTimeMillis() + 1));
            setTaskQty(new BigDecimal(1));
            setProdplanId("123");
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void cleanFinishedTask() {
        service.cleanFinishedTask(Lists.newArrayList(new WipDailyStatisticReport() {{
            setAttr1(1);
            setTaskQty(1);
        }}), new HashMap<String, String>());
        service.cleanFinishedTask(Lists.newArrayList(new WipDailyStatisticReport() {{
            setAttr1(0);
            setTaskQty(1);
        }}), new HashMap<String, String>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getPage() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO dto2 = new SysLookupTypesDTO();
        SysLookupTypesDTO dto3 = new SysLookupTypesDTO();
        dto2.setLookupMeaning("Test");
        dto3.setLookupMeaning("Test1");
        types.add(dto2);
        types.add(dto3);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);

        PowerMockito.when(repository.selectCount(Mockito.anyObject())).thenReturn(10);

        List<WipDailyStatisticReport> list = new ArrayList<>();
        PowerMockito.when(repository.selectPage(Mockito.anyObject())).thenReturn(list);

        WipDailyStatisticQueryDTO dto = new WipDailyStatisticQueryDTO();
        dto.setTaskNo("202002231022ACE");
        dto.setIncludeRealtime(false);
        Assert.assertNotNull(service.getPage(dto));
    }

    @Test
    public void selectRealTimePage() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupValuesDTO> lookupValueList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode(new BigDecimal(6001040));
        sysLookupValuesDTO.setAttribute3("CUST_POWER_TEST_QTY");
        lookupValueList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(Mockito.anyString())).thenReturn(lookupValueList);

        List<WipDailyStatisticReport> list = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReport = new WipDailyStatisticReport();
        wipDailyStatisticReport.setProdplanId("7777803");
        wipDailyStatisticReport.setPartsInboundQty(1);
        list.add(wipDailyStatisticReport);
        PowerMockito.when(repository.selectRealTimePage(Mockito.anyList(), Mockito.anyList(), Mockito.anyList())).thenReturn(list);
        List<ProdPlanStock> list2 = new ArrayList<>();
        ProdPlanStock wipDailyStatisticReport2 = new ProdPlanStock();
        wipDailyStatisticReport2.setProdPlanId("7777803");
        wipDailyStatisticReport2.setInboundQty(1);
        list2.add(wipDailyStatisticReport2);
        PowerMockito.when(repository.selectCraftTransOutQty(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(list2);

        List<String> planIds = new ArrayList<>();
        planIds.add("7777803");
        Assert.assertNotNull(service.selectRealTimePage(planIds, true));
    }

    @Test
    public void getPageRealtime() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(1L);
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(
                Lists.newArrayList(new SysLookupValuesDTO() {{
                    setAttribute3("1");
                }}));
        PowerMockito.when(repository.getSockQtyByPlanId(any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setStockQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.queryInboundQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.querySubmitQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
        try {
            service.getPageRealtime(new WipDailyStatisticQueryDTO(), 0, 1, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            service.getPageRealtime(new WipDailyStatisticQueryDTO(), 0, 1, false);
            PowerMockito.when(CrafttechRemoteService.getRouteByItems(any())).thenReturn(Lists.newArrayList(new CtBasicRouteDTO() {{
                    setItemOrTask("1");
                    setSourceSys("P0240N");
                }}));
            PowerMockito.when(warehouseDetailRepository.getOuterQtyByPlanIds((any()))).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
                setProdPlanId("1");
                setInboundQty(1);
                setSubmitQty(1);
            }}));
            service.getPageRealtime(new WipDailyStatisticQueryDTO(), 0, 1, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void getPageRealtimeTwo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(1L);
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(
                Lists.newArrayList(new SysLookupValuesDTO() {{
                    setAttribute3("1");
                }}));
        PowerMockito.when(repository.getSockQtyByPlanId(any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setStockQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.queryInboundQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
        }}));
        PowerMockito.when(warehouseDetailRepository.querySubmitQtyByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
        }}));
        try {
            service.getPageRealtime(new WipDailyStatisticQueryDTO(), 0, 1, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void statisticJob() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class,CrafttechRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
            }}));
            setTotal(1L);
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(
                Lists.newArrayList(new SysLookupValuesDTO() {{
                    setAttribute3("1");
                }}));
        PowerMockito.when(repository.getSockQtyByPlanId(any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setStockQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.getByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setInboundQty(1);
            setSubmitQty(1);
        }}));
        PowerMockito.doAnswer(invocationOnMock -> null).when(repository).insertBatch(any(), any());
        List<SysLookupValuesDTO> leadList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(leadList);
        PowerMockito.when(CrafttechRemoteService.getRouteByItems(any())).thenReturn(Lists.newArrayList(new CtBasicRouteDTO() {{
            setItemOrTask("1");
            setSourceSys("P0240N");
        }}));
        try {
            service.statisticJob(1, 2, "1","2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupMeaning("1");
        dto.setDescriptionChin("无铅");
        leadList.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(leadList);
        try {
            service.statisticJob(1, 2, "1","2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
            }}));
            setTotal(1L);
        }});
        try {
            service.statisticJob(1, 2, "1","2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
            }}));
            setTotal(1L);
        }});
        List<BoardOnline> repairList = new ArrayList<>();
        BoardOnline online = new BoardOnline();
        online.setProdplanId(new BigDecimal(1));
        online.setRepairCount(1);
        repairList.add(online);
        PowerMockito.when(datawbRemoteService.getRepairCountFromBoardOnline(Mockito.anyList())).thenReturn(repairList);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(leadList);
        try {
            service.statisticJob(1, 2, "1","2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void transformCode() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<WipDailyStatisticReport> list = new ArrayList<>();
        WipDailyStatisticReport dto = new WipDailyStatisticReport();
        dto.setIsComplete("Y");
        dto.setIsScheduled("Y");
        dto.setOrgId(2347);
        list.add(dto);
        dto = new WipDailyStatisticReport();
        dto.setIsComplete("N");
        dto.setIsScheduled("N");
        dto.setOrgId(2347);
        list.add(dto);
        List<SysLookupTypesDTO> sysLookupTypeList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("2347");
        sysLookupTypesDTO.setDescriptionChinV("test");
        sysLookupTypesDTO.setDescriptionEngV("test");
        sysLookupTypeList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypeList);
        service.transformCode(list);
        Assert.assertEquals("2347", sysLookupTypesDTO.getLookupMeaning());
    }

    @Test
    public void setOuterQty() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        service.setOuterQty(Lists.newArrayList(new WipDailyStatisticReport(){{setProdplanId("1"); setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}), false);
        service.setOuterQty(Lists.newArrayList(new WipDailyStatisticReport(){{setProdplanId("1"); setSourceSysNo("P0240N"); setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}), true);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(
                Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("insertionQty");}},
                        new SysLookupValuesDTO(){{setLookupMeaning("1");}})
        );
        PowerMockito.when(warehouseDetailRepository.getOuterQtyByPlanIds((any()))).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
        service.setOuterQty(Lists.newArrayList(new WipDailyStatisticReport(){{
            setProdplanId("2");
            setSourceSysNo("P0240N");
            setRouteDetail("P0240,N");
            setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}), false);
        service.setOuterQty(Lists.newArrayList(new WipDailyStatisticReport(){{
            setProdplanId("1");
            setSourceSysNo("P02401N");
            setRouteDetail("P0240,1,N");
            setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}, new WipDailyStatisticReport(){{
            setProdplanId("2");
            setSourceSysNo("P0240N");
            setRouteDetail("P0240,N");
            setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}), false);
        PowerMockito.when(repository.selectCraftTransOutQty(anyList(), anyBoolean())).thenReturn(
                Lists.newArrayList(new ProdPlanStock(){{setProdPlanId("1"); setInboundQty(1);}})
        );
        service.setOuterQty(Lists.newArrayList(new WipDailyStatisticReport(){{
            setProdplanId("1");
            setSourceSysNo("P02401N");
            setRouteDetail("P0240,1,N");
            setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}, new WipDailyStatisticReport(){{
            setProdplanId("2");
            setSourceSysNo("P0240N");
            setRouteDetail("P0240,N");
            setTaskQty(1); setOnlineTotalQty(1); setUncommittedQuantity(1);}}), false);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setPartQty() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        try {
            service.setPartQty(Lists.newArrayList(new WipDailyStatisticReport(){{setProdplanId("1"); setTaskQty(1);}}), false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.LOOKUP_221103_EMPTY, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(
                Lists.newArrayList(new SysLookupValuesDTO(){{setLookupMeaning("1");}})
        );
        try {
            service.setPartQty(Lists.newArrayList(new WipDailyStatisticReport(){{setProdplanId("1"); setSourceSysNo("P0240N"); setTaskQty(1);}}), true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.LOOKUP_221103_EMPTY, e.getMessage());
        }
        PowerMockito.when(warehouseDetailRepository.getOuterQtyByPlanIds((any()))).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("1");
            setSubmitQty(1);
        }}));
        try {
            service.setPartQty(Lists.newArrayList(new WipDailyStatisticReport(){{
                setProdplanId("2");
                setSourceSysNo("P0240N");
                setRouteDetail("P0240,N");
                setTaskQty(1);}}), false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            service.setPartQty(Lists.newArrayList(new WipDailyStatisticReport(){{
                setProdplanId("1");
                setSourceSysNo("P02401N");
                setRouteDetail("P0240,1,N");
                setTaskQty(1);}}, new WipDailyStatisticReport(){{
                setProdplanId("2");
                setSourceSysNo("P0240N");
                setRouteDetail("P0240,N");
                setTaskQty(1);}}), false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        try {
            PowerMockito.when(repository.selectCraftTransOutQty(anyList(), anyBoolean())).thenReturn(
                    Lists.newArrayList(new ProdPlanStock(){{setProdPlanId("1"); setInboundQty(1);}})
            );
            service.setPartQty(Lists.newArrayList(new WipDailyStatisticReport(){{
                setProdplanId("1");
                setSourceSysNo("P02401N");
                setRouteDetail("P0240,1,N");
                setTaskQty(1);}}, new WipDailyStatisticReport(){{
                setProdplanId("2");
                setSourceSysNo("P0240N");
                setRouteDetail("P0240,N");
                setTaskQty(1);}}), false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void transferDo()  throws Exception{
        PowerMockito.mockStatic(PlanscheduleRemoteService.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        try{
            service.transferDo();
        }catch (Exception e){
            Assert.assertEquals(MessageId.LOOKUP_6001_EMPTY, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(Lists.newArrayList(
                new SysLookupValuesDTO(){{
                    setDescriptionChin("部件装配结存");
                    setAttribute4("partsAssemblyQty");
                }},
                new SysLookupValuesDTO(){{
                    setDescriptionChin("部件调试结存");
                    setAttribute4("partsDebugQty");
                }},
                new SysLookupValuesDTO(){{
                    setDescriptionChin("高温标模结存");
                    setAttribute4("highTempStandardQty");
                }},
                new SysLookupValuesDTO(){{
                    setDescriptionChin("功放结存");
                    setAttribute4("amplifierQty");
                }},
                new SysLookupValuesDTO(){{
                    setDescriptionChin("高温配送结存");
                    setAttribute4("highTempDeliveryQty");
                }}
        ));
        PowerMockito.when(repository.selectCount(Mockito.anyObject())).thenReturn(0);
        List<WipDailyStatisticReport> list = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.insertWmesMachineOnlineInfo(any(), anyBoolean())).thenReturn(1);
        service.transferDo();
        PowerMockito.when(repository.selectCount(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(repository.selectPage(Mockito.anyObject())).thenReturn(list);
        PowerMockito.when(datawbRemoteService.insertWmesMachineOnlineInfo(any(), anyBoolean())).thenReturn(1);
        service.transferDo();

        WipDailyStatisticReport wipDailyStatisticReport = new WipDailyStatisticReport();
        wipDailyStatisticReport.setItemNo("111");
        list.add(wipDailyStatisticReport);
        service.transferDo();

        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("7777803");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(1L);
        }});
        PowerMockito.when(repository.getSockQtyByPlanId(any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("7777803");
            setStockQty(1);
        }}));
        PowerMockito.when(warehouseDetailRepository.getByPlanIds(any(), any())).thenReturn(Lists.newArrayList(new ProdPlanStock() {{
            setProdPlanId("333333");
            setInboundQty(1);
            setSubmitQty(1);
        }}));
        List<WipDailyStatisticReport> list1 = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReport1 = new WipDailyStatisticReport();
        wipDailyStatisticReport1.setProdplanId("7777803");
        wipDailyStatisticReport1.setAmplifierQty(1234);
        list1.add(wipDailyStatisticReport1);
        wipDailyStatisticReport1.setProdplanId("333333");
        list1.add(wipDailyStatisticReport1);
        PowerMockito.when(repository.selectRealTimePage(Mockito.anyList(), Mockito.anyList(), Mockito.anyList())).thenReturn(list1);
        try {
            service.transferDo();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

    }

    @Test
    public void setMbomTest() throws Exception {
        WipDailyStatisticReport dto = new WipDailyStatisticReport();
        dto.setProdplanId("7654321");
        List<WipDailyStatisticReport> list = new ArrayList<>();
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234567");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        list.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
        Assert.assertEquals("7654321", list.get(0).getMBomProductCode());
        dto.setProdplanId("76543211");
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
    }
}
