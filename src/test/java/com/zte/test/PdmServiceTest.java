package com.zte.test;

import com.zte.application.impl.PdmServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.km.udm.api.DiskFileDownloadApi;
import com.zte.km.udm.model.ServiceData;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2021/9/14
 */
@PrepareForTest({BasicsettingRemoteService.class, FileUtils.class, DiskFileDownloadApi.class})
public class PdmServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private PdmServiceImpl service;
    @Mock
    private PdmRemoteService pdmRemoteService;
    @Mock
    CloudDiskHelper cloudDiskHelper;
    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Test
    public void queryOptionalMaterial() throws Exception {
        PdmBomItemQueryDTO pdmBomItemQueryDTO=new PdmBomItemQueryDTO();
        pdmBomItemQueryDTO.setItemNo("123456789012");
        List<PdmBomItemResultDTO> pdmBomItemResultDTOList=new ArrayList<>();
        PdmBomItemResultDTO pdmBomItemResultDTO=new PdmBomItemResultDTO();
        pdmBomItemResultDTO.setBomNo("123456789012ABB");
        pdmBomItemResultDTOList.add(pdmBomItemResultDTO);
        PowerMockito.when(pdmRemoteService.query(anyObject())).thenReturn(pdmBomItemResultDTOList);
        PdmBomItemPageDTO<PdmBomItemResultDTO> pdmBomItemPageDTO=new PdmBomItemPageDTO<>();
        pdmBomItemPageDTO.setCurrentPageNo(1);
        pdmBomItemPageDTO.setItemsTotal(1);
        pdmBomItemPageDTO.setPageTotal(1);
        pdmBomItemPageDTO.setBomItems(pdmBomItemResultDTOList);
        PowerMockito.when(pdmRemoteService.bomItemQuery(anyObject())).thenReturn(pdmBomItemPageDTO);
        Assert.assertNotNull(service.queryOptionalMaterial(pdmBomItemQueryDTO));
    }

    @Test
    public void queryOptionalMaterialNew() throws Exception {
        PdmBomItemQueryDTO pdmBomItemQueryDTO=new PdmBomItemQueryDTO();
        pdmBomItemQueryDTO.setItemNo("123456789012");
        List<PdmBomItemResultDTO> pdmBomItemResultDTOList=new ArrayList<>();
        PowerMockito.when(pdmRemoteService.query(anyObject())).thenReturn(pdmBomItemResultDTOList);
        PdmBomItemPageDTO<PdmBomItemResultDTO> pdmBomItemPageDTO=new PdmBomItemPageDTO<>();
        pdmBomItemPageDTO.setCurrentPageNo(1);
        pdmBomItemPageDTO.setItemsTotal(1);
        pdmBomItemPageDTO.setPageTotal(1);
        pdmBomItemPageDTO.setBomItems(pdmBomItemResultDTOList);
        PowerMockito.when(pdmRemoteService.bomItemQuery(anyObject())).thenReturn(pdmBomItemPageDTO);
        Assert.assertNotNull(service.queryOptionalMaterial(pdmBomItemQueryDTO));
    }

    /* Started by AICoder, pid:7643fxfb55tb390140910afd10aa92146466ec70 */
    @Test
    public void TestgetPdmTemplateInfoList() throws Exception {
        PdmTemplateInfoQueryDTO pdmTemplateInfoQueryDTO = new PdmTemplateInfoQueryDTO();
        List<String> partNoList = new ArrayList<>();
        partNoList.add("130000164282");
        partNoList.add("180000395543");
        pdmTemplateInfoQueryDTO.setPartNoList(partNoList);
        List<TemplateInfoDTO> TemplateInfoDTOList = new ArrayList<>();
        TemplateInfoDTO templateInfoDTO = new TemplateInfoDTO();
        templateInfoDTO.setPartNo("180000395543");
        TemplateInfoDTOList.add(templateInfoDTO);
        PowerMockito.when(pdmRemoteService.getPdmTemplateInfoList(anyObject())).thenReturn(TemplateInfoDTOList);
        Assert.assertNotNull(service.getPdmTemplateInfoList(pdmTemplateInfoQueryDTO));
    }
    /* Ended by AICoder, pid:7643fxfb55tb390140910afd10aa92146466ec70 */

    /* Started by AICoder, pid:91253acb5ch68d214ed8082f1028d662fad4263c */
    @Test
    public void TestuploadFileByUrl() throws Exception {
        String fileUrl = "http://";
        String fileName = "PPC33 A018A(V2.0).btw";
        String empNo = "10307329";
        String docId = "d5ee5088-a03a-4f3d-805d-4c9dd1032d64";
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString())).thenReturn(docId);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(BigDecimal.valueOf(123));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(null);
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOList);
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }
        sysLookupTypesDTO.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678005)));
        sysLookupTypesDTO.setLookupMeaning("");
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }
        sysLookupTypesDTO.setLookupMeaning("100000455581");
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678004)));
        sysLookupTypesDTO1.setLookupMeaning("123");
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.mockStatic(DiskFileDownloadApi.class);
        ServiceData<String> serviceData = new ServiceData<>();
        serviceData.setBo("http://test.idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/ISS_BARCODE/3bc8ad7f-9e8c-47ee-8fe8-fb9c4c516b8c");
        PowerMockito.when(DiskFileDownloadApi.getFileDownloadUrl(Mockito.anyString() ,Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(serviceData);
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }

        sysLookupTypesDTO1.setLookupMeaning("");
        serviceData.setCode(new com.zte.km.udm.model.RetCode(com.zte.springbootframe.common.model.RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
        }
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("");
        service.uploadFileByUrl(fileUrl, fileName, empNo);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("/user/temp");
        String fileUrl1 = "";
        service.uploadFileByUrl(fileUrl1, fileName, empNo);
    }
    /* Ended by AICoder, pid:91253acb5ch68d214ed8082f1028d662fad4263c */


    /* Started by AICoder, pid:b054ckb698wd2b2149790b72c0ba423a3ee66c82 */
    @Test
    public void TestuploadFileByUrl1() throws Exception {
        String fileUrl = "http://";
        String fileName = "PPC33 A018A(V2.0).btw";
        String empNo = "10307329";
        String docId = "d5ee5088-a03a-4f3d-805d-4c9dd1032d64";
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString())).thenReturn(docId);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(BigDecimal.valueOf(123));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOList);
        sysLookupTypesDTO.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678005)));
        sysLookupTypesDTO.setLookupMeaning("100000455581");
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678004)));
        sysLookupTypesDTO1.setLookupMeaning("123");
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.mockStatic(DiskFileDownloadApi.class);
        PowerMockito.when(DiskFileDownloadApi.getFileDownloadUrl(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        try {
            service.uploadFileByUrl(fileUrl, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.FAIL_TO_UPLOAD_FILE);
        }
    }
    /* Ended by AICoder, pid:b054ckb698wd2b2149790b72c0ba423a3ee66c82 */


    @Test
    public void TestdownloadPdmTemplate() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        String docDownloadNo = "6604851";
        String fileName = "PPC33 A018A(V2.0).btw";
        String empNo = "10307329";
        try {
            service.downloadPdmTemplate(docDownloadNo, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookupTypesDTO);
        try {
            service.downloadPdmTemplate(docDownloadNo, fileName, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.FAILED_TO_GET_BARCODE_CENTER_URL);
        }
        sysLookupTypesDTO.setLookupMeaning("http://test.idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/ISS_BARCODE/3bc8ad7f-9e8c-47ee-8fe8-fb9c4c516b8c");
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("/user/temp");
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        sysLookupTypesDTO.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678005)));
        sysLookupTypesDTOList.add(sysLookupTypesDTO);
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(BigDecimal.valueOf(Long.parseLong(MpConstant.LOOKUP_6678004)));
        sysLookupTypesDTO1.setLookupMeaning("123");
        sysLookupTypesDTOList.add(sysLookupTypesDTO1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesDTOList);
        PowerMockito.mockStatic(DiskFileDownloadApi.class);
        ServiceData<String> serviceData = new ServiceData<>();
        serviceData.setBo("http://test.idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/ISS_BARCODE/3bc8ad7f-9e8c-47ee-8fe8-fb9c4c516b8c");
        PowerMockito.when(DiskFileDownloadApi.getFileDownloadUrl(Mockito.anyString() ,Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(serviceData);
        String docId = "d5ee5088-a03a-4f3d-805d-4c9dd1032d64";
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString())).thenReturn(docId);
        service.downloadPdmTemplate(docDownloadNo, fileName, empNo);
    }

    @Test
    public void verifyCategory() throws Exception {
        List<BarcodeExpandDTO> expandDTOList = new ArrayList<>();

        try {
            PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(expandDTOList);
            service.verifyCategory("test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LOST_BOARD_CENTER, e.getMessage());
        }

        BarcodeExpandDTO barcodeExpandDTO = new BarcodeExpandDTO();
        barcodeExpandDTO.setCategoryName("test");
        expandDTOList.add(barcodeExpandDTO);

        try {
            PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(expandDTOList);
            service.verifyCategory("test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NON_MACHINE_219_BARCODE_CANNOT_BE_BOUND_TO_NETWORK_ACCESS_CERTIFICATE, e.getMessage());
        }
        List<BarcodeExpandDTO> expandDTOList1 = new ArrayList<>();
        barcodeExpandDTO.setCategoryName("整机条码（219）");
        expandDTOList1.add(barcodeExpandDTO);
        PowerMockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any())).thenReturn(expandDTOList1);
        Assert.assertNull(service.verifyCategory("test"));
    }

    @Test
    public void bindResourceNumToSn() throws Exception {
        NetworkLicenseBindingDTO bindingDTO = new NetworkLicenseBindingDTO();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);

        PowerMockito.when(centerfactoryRemoteService.getProductNameCn(Mockito.any())).thenReturn("test");
        List<PdmBomItemResultDTO> resultList = new ArrayList<>();
        PdmBomItemResultDTO pdmBomItemResultDTO = new PdmBomItemResultDTO();
        pdmBomItemResultDTO.setEnCode("test");
        resultList.add(pdmBomItemResultDTO);
        PowerMockito.when(pdmRemoteService.queryEnCode(Mockito.any())).thenReturn(resultList);
        PowerMockito.when(centerfactoryRemoteService.bindingNetworkLicense(Mockito.any())).thenReturn("test");
        Assert.assertNotNull(service.bindResourceNumToSn(bindingDTO));
    }

    @Test
    public void verifyProductCn() throws Exception {
        PdmBomItemResultDTO dto = new PdmBomItemResultDTO();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(null);
        Assert.assertNull(Whitebox.invokeMethod(service, "verifyProductCn", dto));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        sysLookupTypesDTO.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        sysLookupTypesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypesDTO);
        try{
            PowerMockito.when(centerfactoryRemoteService.getProductNameCn(Mockito.any())).thenReturn("");
            Assert.assertNull(Whitebox.invokeMethod(service, "verifyProductCn", dto));
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_CHINESE_NAME_FIELD_VALUE_WAS_OBTAINED_FOR_THE_PRODUCT, e.getMessage());
        }
        PowerMockito.when(centerfactoryRemoteService.getProductNameCn(Mockito.any())).thenReturn("test");
        try{
            PowerMockito.when(pdmRemoteService.queryEnCode(Mockito.any())).thenReturn(new ArrayList<>());
            Assert.assertNull(Whitebox.invokeMethod(service, "verifyProductCn", dto));
        }catch (Exception e){
            Assert.assertEquals(MessageId.THE_ENGLISH_CODE_OF_PDM_HAS_NOT_BEEN_OBTAINED, e.getMessage());
        }
        List<PdmBomItemResultDTO> resultList = new ArrayList<>();
        PdmBomItemResultDTO pdmBomItemResultDTO = new PdmBomItemResultDTO();
        pdmBomItemResultDTO.setEnCode("test1");
        resultList.add(pdmBomItemResultDTO);
        try{
            PowerMockito.when(pdmRemoteService.queryEnCode(Mockito.any())).thenReturn(resultList);
            Assert.assertNull(Whitebox.invokeMethod(service, "verifyProductCn", dto));
        }catch (Exception e){
            Assert.assertEquals(MessageId.CN_OF_THE_PRODUCT_DOES_NOT_MATCH_THE_ENGLISH_CODE_OF_PDM, e.getMessage());
        }
        List<PdmBomItemResultDTO> resultList1 = new ArrayList<>();
        pdmBomItemResultDTO.setEnCode("test");
        resultList1.add(pdmBomItemResultDTO);
        PowerMockito.when(pdmRemoteService.queryEnCode(Mockito.any())).thenReturn(resultList1);
        Assert.assertNull(Whitebox.invokeMethod(service, "verifyProductCn", dto));
    }

    @Test
    public void bomItemQuery() throws Exception {
        Assert.assertNull(service.bomItemQuery(any()));
    }
}
