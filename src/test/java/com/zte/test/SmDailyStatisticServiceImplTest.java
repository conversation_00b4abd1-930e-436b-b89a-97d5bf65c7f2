package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.impl.SmDailyStatisticServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.SmDailyStatisticReport;
import com.zte.domain.model.SmDailyStatisticReportRepository;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.SmDailyStatisticQueryDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

/**
 *
 * @Author:
 * @Date: 2020/8/27 9:36
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,ConstantInterface.class,HttpRemoteUtil.class,MicroServiceDiscoveryInvoker.class,
		CrafttechRemoteService.class,MockitoAnnotations.class})
public class SmDailyStatisticServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private SmDailyStatisticServiceImpl service;

	@Mock
	private SmDailyStatisticReportRepository repository;

	@Before
	public void init() {
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void getPage() throws MesBusinessException {
		PowerMockito.mockStatic(CommonUtils.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		dto.setIncludeRealtime(true);
		dto.setStartDate(new Date());
		dto.setEndDate(new Date());
		service.getPage(dto);

		dto.setIncludeRealtime(false);
		service.getPage(dto);
		Assert.assertEquals(false, dto.getIncludeRealtime());
	}

	@Test
	public void selectRealTimePage() throws MesBusinessException {
		PowerMockito.mockStatic(CommonUtils.class);
		List<String> taskNos = Lists.newArrayList("123");
		service.selectRealTimePage(taskNos, false);
		Assert.assertNull(service.selectRealTimePage(taskNos, true));
	}

	@Test
	@PrepareForTest({CommonUtils.class})
	public void statisticJob() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		service.statisticJob(Constant.WIP_DAILY_MAX_BATCH_CNT, 635);
		service.statisticJob(Constant.WIP_DAILY_MAX_BATCH_CNT+1, 635);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}

	@Test
	public void getPageRealtime() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		dto.setIncludeRealtime(true);
		dto.setStartDate(new Date());
		dto.setEndDate(new Date());
		List<PsTask> taskList = Lists.newArrayList();
		service.getPageRealtime(dto, 1, 10, false, taskList);
		PsTask task = new PsTask();
		task.setTaskNo("123");
		taskList.add(task);
		PowerMockito.mockStatic(ConstantInterface.class);
		PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://************/zte-mes-manufactureshare-datawbsys/boardFirstwarehouse/selectGetDateByProdplanNo");
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
				JSON.toJSONString(new ServiceData() {{
					setBo(Lists.newArrayList(""));
				}})
		);
		Assert.assertEquals("123", task.getTaskNo());
		service.getPageRealtime(dto, 1, 10, false, taskList);
	}

	@Test
	public void export() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		dto.setIncludeRealtime(true);
		dto.setStartDate(new Date());
		dto.setEndDate(new Date());
		service.export(null, dto);
		Assert.assertEquals(true, dto.getIncludeRealtime());
	}

	@Test
	public void getRouteHead() throws Exception {
		PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class,CrafttechRemoteService.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		Assert.assertNull(service.getRouteHead(Lists.newArrayList("213")));
	}

	@Test
	public void getInboundInfo() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		Assert.assertNull(service.getInboundInfo(Lists.newArrayList("17101510")));
	}

	@Test
	public void getReworkInfo() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		SmDailyStatisticQueryDTO dto = new SmDailyStatisticQueryDTO();
		Assert.assertNull(service.getReworkInfo(Lists.newArrayList("7101510")));
	}

	@Test
	public void copyProperties() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
		service.copyProperties(new SmDailyStatisticReport(), new PsTask());
	}

	@Test
	public void appendExcel() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		List<SysLookupValuesDTO> lookupValueList = Lists.newArrayList();
		SysLookupValuesDTO lookUp = new SysLookupValuesDTO();
		lookUp.setAttribute2("Y");
		lookUp.setAttribute4("Y");
		lookUp.setDescriptionChin("Y");
		lookUp.setSortSeq(new BigDecimal(1));
		lookupValueList.add(lookUp);
		service.appendExcelField(lookupValueList);
		Assert.assertNotNull(service.appendExcelTitle(lookupValueList));
	}

	@Test
	public void aggERPInfo() {
		PowerMockito.mockStatic(CommonUtils.class);
		List<SmDailyStatisticReport> list = Lists.newArrayList();
		SmDailyStatisticReport report = new SmDailyStatisticReport();
		report.setTaskNo("123");
		list.add(report);
		Assert.assertEquals("123", report.getTaskNo());
		service.aggERPInfo(list, list);
	}

}
