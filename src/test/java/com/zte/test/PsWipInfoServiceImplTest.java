package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.HighTempSamplingService;
import com.zte.application.PsCommonScanService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.QcRegulationService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.CtRouteDetail;
import com.zte.domain.model.HighTempSamplingRepository;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.QcSamplingHeadRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WipScanHisExtraRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.interfaces.dto.BsWorkTimeSectionDTO;
import com.zte.interfaces.dto.CreateWipInfoBatchDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.FlowControlConditionDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PcProcessTransferDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.QcSamplingHeadDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Future;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

@PrepareForTest({BasicsettingRemoteService.class, PlanscheduleRemoteService.class,
        CommonUtils.class, SpringContextUtil.class, MicroServiceRestUtil.class,
        CrafttechRemoteService.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class,
        PsWipInfoServiceImpl.class, BarcodeCenterRemoteService.class,
        HttpRemoteService.class, RedisHelper.class, MESHttpHelper.class})
public class PsWipInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;

    @InjectMocks
    private PsWipInfoServiceImpl psService;

    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private WipScanHisExtraRepository wipScanHisExtraRepository;
    @Mock
    private HighTempSamplingService highTempSamplingService;
    @Mock
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;
    @Mock
    private HighTempSamplingRepository highTempSamplingRepository;

    @Mock
    private RedisLock redisLock;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private PsCommonScanService psCommonScanService;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private PsScanHistoryService psScanHistoryService;
    @Mock
    private BsWorkTimeSectionService bsWorkTimeSectionService;
    @Mock
    private PsOutputInfoService psOutputInfoService;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private QcRegulationService qcRegulationService;
    @Mock
    private QcSamplingHeadRepository qcSamplingHeadRepository;
    @Value("${high.temp.switch:Y}")
    private String highTempSwitch;
    @Value("${high.process.code:P2065}")
    private String highProcessCode;
    @Mock
    private ServiceData serviceData;


    @Before
    public void init() {

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void submitTransferAndCheckResult()throws Exception{
        PcProcessTransferDTO dto=new PcProcessTransferDTO();
        List<PcProcessTransferDTO> readySubmitList=new ArrayList<>();
        Whitebox.invokeMethod(psWipInfoServiceImpl,"submitTransferAndCheckResult",
                dto,readySubmitList);
        Assert.assertEquals(new ArrayList<>(), readySubmitList);
    }

    @Test
    public void testDoTheList() throws Exception {
        WarehouseEntryInfo record = new WarehouseEntryInfo();
        record.setWarehouseEntryId(UUID.randomUUID().toString());

        List<WarehouseEntryInfo> nowHeadList = new ArrayList();
        WarehouseEntryInfo nowhead = new WarehouseEntryInfo();
        nowhead.setWarehouseEntryId("1");
        nowHeadList.add(nowhead);
        when(warehouseEntryInfoService.getWarehouseEntryInfoList(anyObject())).thenReturn(nowHeadList);
        Assert.assertEquals("1", nowhead.getWarehouseEntryId());

    }

    @Test
    public void getWipInfoByParentSnTest() throws Exception {
        PsWipInfoServiceImpl service = PowerMockito.spy(new PsWipInfoServiceImpl());
        service.setPsWipInfoRepository(psWipInfoRepository);
        PowerMockito.mock(CrafttechRemoteService.class);

        PsWipInfo returnDto = new PsWipInfo();
        returnDto.setParentSn("1");
        returnDto.setCurrProcessCode("2");
        returnDto.setWorkStation("3");
        returnDto.setLastUpdatedBy("4");
        returnDto.setCreateDate(new Date());
        returnDto.setItemNo("6");
        PowerMockito.when(psWipInfoRepository.getWipInfoByParentSn(anyObject())).thenReturn(returnDto);
        String err = "7";
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getWorkCodeStation(anyObject())).thenReturn(err);

        String sn = "3214";
        Assert.assertEquals(service.getWipInfoByParentSn(sn).getCurrProcessCode(), returnDto.getCurrProcessCode());
    }

    @Test
    public void toPcProcessTransferSimpleDTO() throws Exception {
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        dto.setWorkOrderNo("7777889-SMT-B5501");
        Assert.assertNotNull(psWipInfoServiceImpl.toPcProcessTransferSimpleDTO(dto));
    }

    @Test
    public void getPCProcessTransferPage() throws Exception {
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        Assert.assertNotNull(psWipInfoServiceImpl.getPCProcessTransferPage(dto));
    }


    @Test
    public void getStock85Date() throws Exception {
        List<BoardInstructionCycleDataCreateDTO> dtoList = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO dto = new BoardInstructionCycleDataCreateDTO();
        dto.setProdplanId("7777666");
        dto.setStock85Index(85);
        dtoList.add(dto);

        List<BoardInstructionCycleDataCreateDTO> list = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO rtdto = new BoardInstructionCycleDataCreateDTO();
        rtdto.setProdplanId("7777666");
        rtdto.setStock85Date(new Date());
        list.add(rtdto);
        Assert.assertEquals("7777666", rtdto.getProdplanId());
    }

    @Test
    public void createWipInfoBatch() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(BarcodeCenterRemoteService.class);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("itemNo");
        psTask.setTaskNo("test");
        psTask.setTaskQty(new BigDecimal(NumConstant.NUM_100));
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);

        List<SysLookupTypesDTO> list = new ArrayList<>();
        // 查询忽略不注册物料代码
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_UP_1290)).thenReturn(list);
        // 查询条码是否已存在
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);
        // 查询已注册条码数
        PowerMockito.when(psWipInfoRepository.getCount(anyObject())).thenReturn((long) 0);

        // 校验批次对应指令是否存在
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderNo("7777666-SMT-B5501");
        psWorkOrderDTO.setRemark("0");
        psWorkOrderDTO.setWorkOrderStatus(Constant.IS_SUBMITTED);
        workOrderList.add(psWorkOrderDTO);

        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(anyString(), any())).thenReturn(workOrderList);

        List<CtRouteDetail> ctRouteDetailList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setProcessSeq(0);
        ctRouteDetail.setRouteId("123456");
        ctRouteDetail.setNextProcess("SMT-B");
        ctRouteDetail.setCraftSection("SMT-B");

        ctRouteDetailList.add(ctRouteDetail);

        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(any())).thenReturn(ctRouteDetailList);
        PowerMockito.when(psWipInfoRepository.insertPsWipInfoBatch(anyObject())).thenReturn(1);
        PowerMockito.when(wipScanHisExtraRepository.batchInsert(anyObject())).thenReturn(1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.SYS_LOOK_2344, Constant.SYS_LOOK_2344001)).thenReturn(null);

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");

        CreateWipInfoBatchDTO dto = new CreateWipInfoBatchDTO();
        dto.setProdPlanId("7777666");
        dto.setPrintQty(NumConstant.NUM_TEN);
        dto.setStartNum(NumConstant.NUM_ONE);
        dto.setFactoryId("52");
        // 查询条码是否已存在
        List<PsWipInfo> wipList = new ArrayList<>();
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setSn("777766600001");
        wipList.add(wipInfo);
        PowerMockito.when(psWipInfoRepository.getList(anyObject())).thenReturn(wipList);
        psWipInfoServiceImpl.createWipInfoBatch(dto);
        Assert.assertEquals("777766600001", wipInfo.getSn());
    }

    @Test
    public void batchSave4PackScan() throws Exception {
        List<FlowControlConditionDTO> list = new LinkedList<>();
        FlowControlConditionDTO a1 = new FlowControlConditionDTO();
        a1.setFactoryId(new BigDecimal("42"));
        list.add(a1);

        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("Y");
        PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class);

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("52");
        PowerMockito.when(
                BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookUpValue);

        try {
            psWipInfoServiceImpl.batchSave4PackScan(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCAN_WORK_ORDER_NO_IS_NULL,e.getMessage());
        }
        a1.setWorkOrderNo("7011706-SMTA");
        psWipInfoServiceImpl.batchSave4PackScan(list);
        sysLookUpValue.setAttribute1("7011706");
        psWipInfoServiceImpl.batchSave4PackScan(list);
    }

    @Test
    public void checkHighTempSwitch() throws Exception {
        SysLookupTypesDTO sysLookUpValue = new SysLookupTypesDTO();
        sysLookUpValue.setLookupMeaning("N");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Assert.assertFalse(Whitebox.invokeMethod(psWipInfoServiceImpl,"checkHighTempSwitch", "7011706"));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookUpValue);
        Whitebox.invokeMethod(psWipInfoServiceImpl,"checkHighTempSwitch", "7011706");
        sysLookUpValue.setLookupMeaning("Y");
        sysLookUpValue.setAttribute1("111");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(), Mockito.anyString())).thenReturn(sysLookUpValue);
        Whitebox.invokeMethod(psWipInfoServiceImpl,"checkHighTempSwitch", "7011706");
    }

    @Test
    public void checkTestWorkOrderNo() throws Exception {
        FlowControlConditionDTO dto = new FlowControlConditionDTO();
        dto.setManualFlag(false);
        List<PsWorkOrderDTO> assemblyWorkList = new ArrayList<>();
        PsWorkOrderDTO p1 = new PsWorkOrderDTO();
        p1.setWorkOrderNo("12");
        Whitebox.invokeMethod(psWipInfoServiceImpl,"checkTestWorkOrderNo",dto, "7011706",assemblyWorkList);
        dto.setManualFlag(true);
        dto.setStockInFlag(true);
        Whitebox.invokeMethod(psWipInfoServiceImpl,"checkTestWorkOrderNo",dto, "7011706",assemblyWorkList);
        dto.setStockInFlag(false);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        List<PsWorkOrderDTO> workOrderDTOS = new ArrayList<>();
        PsWorkOrderDTO dto1 = new PsWorkOrderDTO();
        dto1.setWorkOrderNo("123");
        dto1.setProcessGroup("P123$P321");
        workOrderDTOS.add(dto1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.NO_TEST_INSTRUCTIONS_OR_INSTRUCTIONS_NOT_SCHEDULED);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl,"checkTestWorkOrderNo",dto, "7011706",assemblyWorkList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_TEST_INSTRUCTIONS_OR_INSTRUCTIONS_NOT_SCHEDULED,e.getMessage());
        }
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(workOrderDTOS);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl,"checkTestWorkOrderNo",dto, "7011706",assemblyWorkList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_TEST_INSTRUCTIONS_OR_INSTRUCTIONS_NOT_SCHEDULED,e.getMessage());
        }
        dto1.setProcessType(MpConstant.PROCESS_TYPE_AUTOTEST);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.NOT_ALLOWED_TO_OFFLINE);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl,"checkTestWorkOrderNo",dto, "7011706",assemblyWorkList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_ALLOWED_TO_OFFLINE,e.getMessage());
        }
    }

    @Test
    public void submitPcProcessTransfer() throws Exception {
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", false);
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        List<PsWipInfo> wipInfoList = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setSn("1234567800001");
        a1.setCurrProcessCode("2");
        wipInfoList.add(a1);
        dto.setWipList(wipInfoList);
        dto.setToProcessCode("234");
        dto.setToWorkOrderNo("123");

        ServiceData serviceData = new ServiceData();
        serviceData.setBo(new LinkedList<>());

        BsWorkTimeSectionDTO currWorkTimeDTO = new BsWorkTimeSectionDTO();
        PowerMockito.when(
                bsWorkTimeSectionService.selectBsWorkTimeSectionByLineWorkShopAndFactory(
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any())).thenReturn(currWorkTimeDTO);
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new LinkedList<>();
        PsEntityPlanBasicDTO a2 = new PsEntityPlanBasicDTO();
        a2.setWorkOrderNo("123");
        a2.setCraftSection("SMT-A");
        a2.setProcessGroup("2");
        psEntityPlanInfo.add(a2);
        serviceData.setBo(psEntityPlanInfo);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(psEntityPlanInfo);

        List<SysLookupValuesDTO> valuesDTOList = new LinkedList<>();
        SysLookupValuesDTO b1 = new SysLookupValuesDTO();
        b1.setLookupCode(new BigDecimal("10250001"));
        b1.setLookupMeaning("Y");
        valuesDTOList.add(b1);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(valuesDTOList);
        ;

        PowerMockito.when(psScanHistoryService.insertPsScanHistoryByScanBatch(Mockito.anyList(), Mockito.anyInt())).thenReturn(1);

        PowerMockito.when(
                bsWorkTimeSectionService.selectBsWorkTimeSectionByLineWorkShopAndFactory(
                        Mockito.any(),
                        Mockito.any(),
                        Mockito.any())).thenReturn(currWorkTimeDTO);

        PowerMockito.when(psOutputInfoService.insertPsOutputInfoSelective(Mockito.anyObject())).thenReturn(1);
        Assert.assertNotNull(psWipInfoServiceImpl.submitPcProcessTransfer(dto));

    }

    @Test
    public void getPartStock90Date() throws Exception {
        List<BoardInstructionCycleDataCreateDTO> dtoList = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO dto2 = new BoardInstructionCycleDataCreateDTO();
        dto2.setProdplanId("222");
        dtoList.add(dto2);
        PowerMockito.when(psWipInfoRepository.getPartStock90Date(Mockito.any(),Mockito.any())).thenReturn(dtoList);
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        psWipInfoServiceImpl.getPartStock90Date(dtoList);
        types = null;
        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
            psWipInfoServiceImpl.getPartStock90Date(dtoList);
        } catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_UP_TYPE_NOT_CONFIGURE, e.getMessage());
        }

    }

    @Test
    public void checkWorkOrRemain() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CrafttechRemoteService.class,CommonUtils.class);
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "checkWorkOrRemain", "N");

        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        dto.setWorkOrderNo("test123");
        FlowControlInfoDTO flow = new FlowControlInfoDTO();
        flow.setCurrProcessCode("test123");
        flow.setLineCode("test123");
        flow.setSourceSysName("test123");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        dto.setWipList(psWipInfos);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("test123");
        psWipInfo.setLastProcess("Y");
        psWipInfo.setCurrProcessCode("test123");
        psWipInfo.setSn("test123");
        psWipInfo.setSourceSysName("test123");

        PsWipInfo psWipInfo1 = new PsWipInfo();
        psWipInfo1.setWorkOrderNo("test123");
        psWipInfo1.setLastProcess("N");
        psWipInfo1.setCurrProcessCode("test123");
        psWipInfo1.setSn("test222");
        psWipInfo1.setSourceSysName("test123");

        List<PsEntityPlanBasicDTO> psList = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setWorkOrderNo("test123");
        psEntityPlanBasicDTO.setRouteId("test123");
        psList.add(psEntityPlanBasicDTO);

        List<CtRouteDetail> currList = new ArrayList<>();
        CtRouteDetail ctRouteDetail = new CtRouteDetail();
        ctRouteDetail.setRemainTime(new BigDecimal("1"));
        currList.add(ctRouteDetail);

        List<CtRouteDetailDTO> workStationList = new ArrayList<>();
        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        CtRouteDetailDTO ctRouteDetailDTO2 = new CtRouteDetailDTO();
        workStationList.add(ctRouteDetailDTO);


        List<WipScanHistory> snDateList = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("test123");
        wipScanHistory.setCreateDate(new Date());
        snDateList.add(wipScanHistory);
        List<List<String>> snSplit = new ArrayList<>();
        List<String> sns = new ArrayList<>();
        sns.add("test123");
        sns.add("test222");
        snSplit.add(sns);

        PowerMockito.when(psCommonScanService.queryWorkOrderInfo(Mockito.anyString())).thenReturn(psEntityPlanBasicDTO);
        PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(currList);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any(),(String[])Mockito.any())).thenReturn("error");
        PowerMockito.when(CommonUtils.splitList((List<String>)Mockito.any(),Mockito.anyInt())).thenReturn(snSplit);
        PowerMockito.when(wipScanHistoryRepository.getBatchTimeIntervalBySnList(Mockito.any(),Mockito.anyString())).thenReturn(snDateList);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "checkWorkOrRemain", "Y");
        flow.setEntityPlanBasic(psEntityPlanBasicDTO);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        flow.setEntityPlanBasic(null);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        psWipInfos.add(psWipInfo);
        psWipInfos.add(psWipInfo1);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        long oneHour = 3 * 60 * 60 * 1000;
        Date oneHourAgo = new Date(new Date().getTime() - oneHour);
        wipScanHistory.setCreateDate(oneHourAgo);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        ctRouteDetail.setRemainTime(null);
        try{
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

        try{
            PowerMockito.when(wipScanHistoryRepository.getBatchTimeIntervalBySnList(Mockito.any(),Mockito.anyString())).thenReturn(new ArrayList<>());
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            PowerMockito.when(CrafttechRemoteService.getCtRouteDetailInfo(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
            psWipInfoServiceImpl.checkWorkOrRemain(dto,flow);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void checkQcRegulation() throws Exception {
        List<PsWipInfo> wipList = new ArrayList<>();
        wipList.add(new PsWipInfo(){{setSn("111");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        wipList.remove(0);
        wipList.add(new PsWipInfo(){{setSn("111");setLineCode("lineCode");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        wipList.remove(0);
        wipList.add(new PsWipInfo(){{setSn("111");setLineCode("lineCode");setAttribute1("attr1");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        wipList.remove(0);
        wipList.add(new PsWipInfo(){{setSn("111");setLineCode("lineCode");setAttribute1("attr1");setItemCode("itemCode");setProcessCode("1");setWorkOrderNo("workOrderNo");}});
        PowerMockito.when(qcRegulationService.whetherNeedQc(any())).thenReturn(Constant.FLAG_N);
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        PowerMockito.when(qcRegulationService.whetherNeedQc(any())).thenReturn(Constant.FLAG_Y);
        List<QcSamplingHeadDTO> samplingHeadInfo = new ArrayList<>();
        PowerMockito.when(qcSamplingHeadRepository.getSamplingHeadInfo(any(),any())).thenReturn(samplingHeadInfo);
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_VALID_SAMPLING_RECORD, e.getMessage());
        }

        samplingHeadInfo.add(new QcSamplingHeadDTO(){{setHeadId("id");setStatus("1");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_VALID_SAMPLING_RECORD, e.getMessage());
        }

        samplingHeadInfo.remove(0);
        samplingHeadInfo.add(new QcSamplingHeadDTO(){{setHeadId("id");setStatus("1");setSn("111");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PASS_SAMPLING_RECORD, e.getMessage());
        }

        samplingHeadInfo.remove(0);
        samplingHeadInfo.add(new QcSamplingHeadDTO(){{setHeadId("id");setStatus("3");setSn("111");}});
        try {
            psWipInfoServiceImpl.checkQcRegulation(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkQcRegulationByPcProcessTransfer() throws Exception {
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", false);
        List<PsWipInfo> wipList = new ArrayList<>();
        wipList.add(new PsWipInfo(){{setSn("111");}});

        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        dto.setWipList(wipList);
        dto.setWorkOrderNo("1234567-SMT-B5502");
        List<Future<String>> futureList = new LinkedList<>();
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new ArrayList<>();
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any())).thenReturn(psEntityPlanInfo);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl, "checkQcRegulationByPcProcessTransfer", dto,futureList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
        }
        psEntityPlanInfo.add(new PsEntityPlanBasicDTO(){{setSourceTask("1234567");}});
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl, "checkQcRegulationByPcProcessTransfer", dto,futureList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", true);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl, "checkQcRegulationByPcProcessTransfer", dto,futureList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        List<PsWipInfo> list = new ArrayList<>();
        list.add(new PsWipInfo(){{setSn("1");}});
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(list);
        try {
            Whitebox.invokeMethod(psWipInfoServiceImpl, "checkQcRegulationByPcProcessTransfer", dto,futureList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkQcRegulationByBatchPassScan() throws Exception {
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", false);
        List<PsWipInfo> wipList = new ArrayList<>();
        try {
            psWipInfoServiceImpl.checkQcRegulationByBatchPassScan(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", true);
        try {
            psWipInfoServiceImpl.checkQcRegulationByBatchPassScan(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        wipList.add(new PsWipInfo(){{setSn("111");setLastProcess("Y");}});
        try {
            psWipInfoServiceImpl.checkQcRegulationByBatchPassScan(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void checkQcRegulationBySaveWarehouse() throws Exception {
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", false);
        List<PsWipInfo> wipList = new ArrayList<>();
        try {
            psWipInfoServiceImpl.checkQcRegulationBySaveWarehouse(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        wipList.add(new PsWipInfo(){{setSn("111");}});
        try {
            psWipInfoServiceImpl.checkQcRegulationBySaveWarehouse(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        ReflectionTestUtils.setField(psWipInfoServiceImpl, "qcSamplingFlag", true);
        try {
            psWipInfoServiceImpl.checkQcRegulationBySaveWarehouse(wipList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void checkIsExistNecessaryProcessAfterCurrProcess() throws Exception {
        PsEntityPlanBasicDTO planBasicDTO = new PsEntityPlanBasicDTO();
        FlowControlConditionDTO entity = new FlowControlConditionDTO();
        entity.setCurrProcessCode("111");
        entity.setSn("701170600001");
        PowerMockito.when(psCommonScanService.queryWorkOrderInfo(Mockito.any())).thenReturn(null);
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "highTempSwitch", "N");
        psWipInfoServiceImpl.checkIsExistNecessaryProcessAfterCurrProcess(entity);
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "highTempSwitch", "Y");
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "highProcessCode", "112");
        try {
            psWipInfoServiceImpl.checkIsExistNecessaryProcessAfterCurrProcess(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ROUTEID_IS_EMPTY,e.getMessage());
        }
        PowerMockito.when(psCommonScanService.queryWorkOrderInfo(Mockito.any())).thenReturn(planBasicDTO);
        List<CtRouteDetail> routeDetailList = new ArrayList<>();
        CtRouteDetail detail = new CtRouteDetail();
        detail.setNextProcess("1");
        detail.setProcessSeq(0);
        routeDetailList.add(detail);
        CtRouteDetail detail1 = new CtRouteDetail();
        detail1.setNextProcess("111");
        detail1.setProcessSeq(1);
        detail1.setDoOrNotFlag("Y");
        detail1.setSkipRule(Constant.HIGH_TEMP_RULE);
        routeDetailList.add(detail1);
        CtRouteDetail detail2 = new CtRouteDetail();
        detail2.setNextProcess("112");
        detail2.setDoOrNotFlag("Y");
        detail2.setSkipRule(Constant.HIGH_TEMP_RULE);
        detail2.setProcessSeq(2);
        routeDetailList.add(detail2);
        CtRouteDetail detail3 = new CtRouteDetail();
        detail3.setNextProcess("112");
        detail3.setSkipRule(Constant.HIGH_TEMP_RULE);
        detail3.setProcessSeq(3);
        routeDetailList.add(detail3);
        PowerMockito.when(warehouseEntryInfoServiceImpl.getCtRouteDetailInfo(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        try {
            psWipInfoServiceImpl.checkIsExistNecessaryProcessAfterCurrProcess(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_CARFT_NULL,e.getMessage());
        }
        PowerMockito.when(warehouseEntryInfoServiceImpl.getCtRouteDetailInfo(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(routeDetailList);
        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(null);
        psWipInfoServiceImpl.checkIsExistNecessaryProcessAfterCurrProcess(entity);
        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(1);
        psWipInfoServiceImpl.checkIsExistNecessaryProcessAfterCurrProcess(entity);
    }

    /* Started by AICoder, pid:ed30d4b449nd69c14dc80872c004e3756f9299d1 */
    @Test
    public void mergeSnToPushData () {
        List<String> customerNameList = new ArrayList<>();
        int preDays = -1;
        try {
            psWipInfoServiceImpl.mergeSnToPushData(customerNameList, preDays);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }

        customerNameList.add("alibaba");
        try {
            psWipInfoServiceImpl.mergeSnToPushData(customerNameList, preDays);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }

        preDays = 1;
        List<String> firstPage = new ArrayList<>();
        List<String> secondPage = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getNotPushDoneProdplanId(Mockito.anyList(), Mockito.anyInt(), Mockito.eq(1))).thenReturn(firstPage);
        PowerMockito.when(centerfactoryRemoteService.getNotPushDoneProdplanId(Mockito.anyList(), Mockito.anyInt(), Mockito.eq(2))).thenReturn(secondPage);
        int result = psWipInfoServiceImpl.mergeSnToPushData(customerNameList, preDays);
        Assert.assertEquals(0, result);

        for (int i = 0; i < 101; i++) {
            firstPage.add(i + "");
        }
        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PowerMockito.when(psWipInfoRepository.getNeedPushSnByProdplanId(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.eq(""), Mockito.anyInt())).thenReturn(wipInfoList);
        PowerMockito.when(psWipInfoRepository.getNeedPushSnByProdplanId(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.eq("500"), Mockito.anyInt())).thenReturn(new ArrayList<>());
        result = psWipInfoServiceImpl.mergeSnToPushData(customerNameList, preDays);
        Assert.assertEquals(0, result);

        for (int i = 0; i < 501; i++) {
            PsWipInfo psWipInfo = new PsWipInfo();
            psWipInfo.setSn(i + "");
            psWipInfo.setAttribute1("7777666");
            psWipInfo.setLastUpdatedDate(new Date());
            wipInfoList.add(psWipInfo);
        }
        PowerMockito.when(centerfactoryRemoteService.mergeSnToPushDataDetail(Mockito.anyList())).thenReturn(1);
        result = psWipInfoServiceImpl.mergeSnToPushData(customerNameList, preDays);
        Assert.assertEquals(1, result);
    }

    @Test
    public void getTaskNoBySns () throws Exception {
        try {
            psWipInfoServiceImpl.getTaskNoBySns(new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LIST_IS_NULL,e.getMessage());
        }
        List<String> snList = new ArrayList<>();
        snList.add("11");
        ReflectionTestUtils.setField(psWipInfoServiceImpl, "snListMax", 1);
        psWipInfoServiceImpl.getTaskNoBySns(snList);
        snList.add("22");
        try {
            psWipInfoServiceImpl.getTaskNoBySns(snList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LIST_EXCEED_MAX,e.getMessage());
        }
    }
    /* Ended by AICoder, pid:ed30d4b449nd69c14dc80872c004e3756f9299d1 */
}
