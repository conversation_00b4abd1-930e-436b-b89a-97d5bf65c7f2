package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.AssemblyRelaScanRecordInfoService;
import com.zte.application.WipExtendIdentificationService;
import com.zte.application.impl.AssemblyRelaScanServiceImpl;
import com.zte.application.impl.StandardModeCommonScanServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.model.MessageId.*;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

@PrepareForTest({DatawbRemoteService.class,BasicsettingRemoteService.class, HttpRemoteUtil.class, CrafttechRemoteService.class,
		RedisHelper.class, PlanscheduleRemoteService.class, CommonUtils.class})
public class AssemblyRelaScanServiceImplNewTest extends PowerBaseTestCase {
	
	private RetCode retCode;

	@InjectMocks
	private AssemblyRelaScanServiceImpl assemblyRelaScanService;
	
	@Mock
	private ProdBindingSettingRepository prodBindingSettingRepository;
	
	@Mock
	private PsWipInfoRepository psWipInfoRepository;
	
	@Mock
	private WipExtendIdentificationRepository wipExtendIdentificationRepository;

	@Mock
	private BarcodeCenterRemoteService barcodeCenterRemoteService;

	@Mock
	private DatawbRemoteService datawbRemoteService;

	@Mock
	private AssemblyRelaScanRecordInfoService assemblyRelaScanRecordInfoService;
	@Mock
	private BarcodeLockDetailRepository barcodeLockDetailRepository;

	@Mock
	private StandardModeCommonScanServiceImpl standardModeCommonScanService;
	@Mock
	private WipExtendIdentificationService wipExtendIdentificationService;

	@Test
	public void isCheckHbAttr() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		List<SysLookupTypesDTO> lookUpList=new ArrayList<>();
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("P1015");
		sysLookupTypesDTO.setDescriptionChin("HFS");
		sysLookupTypesDTO.setAttribute1("30");
		lookUpList.add(sysLookupTypesDTO);
		when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(lookUpList);
		Assert.assertTrue(assemblyRelaScanService.isCheckHbAttr(dto));
	}
	@Test
	public void isCheckHbAttr2() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(new ArrayList<>());
		Assert.assertTrue(assemblyRelaScanService.isCheckHbAttr(dto));
	}
	@Test
	public void assemblyRelaScanNew() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class,DatawbRemoteService.class,BasicsettingRemoteService.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		List<String> snList = new ArrayList<String>();
		snList.add("123");
		dto.setSubSnList(snList);
		BaItem itemInfo = new BaItem();
		itemInfoList.add(itemInfo);
		ProdBindingSettingDTO bindDto = new ProdBindingSettingDTO();
		bindList.add(bindDto);
		checkFlowRsult.setResultType("OK");
		dto.setToPassWorkStaion(true);
		retCode = PowerMockito.mock(RetCode.class);
		PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
		PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
		workOrder.setTaskNo("taskNo1");
		workOrderList.add(workOrder);
		dto.setMainWorkOrder(workOrder);

		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setAttribute2("ZZ河源VCS190619036-V11-包装5502");
		wipInfo.setItemNo("125000360031");
		wipInfo.setAttribute3("HFS");
		List<WipExtendIdentification> tempList = new ArrayList<>();
		when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
		when(prodBindingSettingRepository.getBindingInfoByItem(anyObject())).thenReturn(bindList);
		when(wipExtendIdentificationRepository.getList(anyObject())).thenReturn(tempList);
		List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
		BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
		barcodeExpandDTO.setBarcode("ZTE200603023362");
		barcodeExpandDTO.setParentCategoryName("序列码");
		barcodeExpandDTO.setIsLead("HFS");
		barcodeExpandDTOList.add(barcodeExpandDTO);
		when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);
		List<WipEntitiesDTO> wipEntitiesDTOList=new ArrayList<>();
		WipEntitiesDTO wipEntitiesDTO=new WipEntitiesDTO();
		wipEntitiesDTO.setStartQuantity(BigDecimal.ONE);
		wipEntitiesDTOList.add(wipEntitiesDTO);
		when(DatawbRemoteService.selectMpsNetQty(anyString())).thenReturn(wipEntitiesDTOList);
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList=new ArrayList<>();
		MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO=new MtlRelatedItemsEntityDTO();
		mtlRelatedItemsEntityDTO.setItemCode("125000360032");
		mtlRelatedItemsEntityDTO.setReplaceItemCode("125000360031");
		mtlRelatedItemsEntityDTOList.add(mtlRelatedItemsEntityDTO);
		when(datawbRemoteService.getItemInfoList(anyString())).thenReturn(mtlRelatedItemsEntityDTOList);
		List<ItemListEntityDTO> itemListEntityDTOList = new ArrayList<>();
		ItemListEntityDTO itemListEntityDTO=new ItemListEntityDTO();
		itemListEntityDTO.setItemNo("125000360031");
		itemListEntityDTO.setRequiredQuantity("20");
		itemListEntityDTOList.add(itemListEntityDTO);
		when(datawbRemoteService.getErpItemListByTaskNo(anyString())).thenReturn(itemListEntityDTOList);
		dto = JSON.parseObject("{\"processCode\":\"P1035\",\"workStationCode\":\"S1069\",\"itemCode\":\"125000360031\",\"workOrderNo\":\"ZZ河源VCS190619036-V11-包装5502\",\"mainSn\":\"219999321946\",\"lineCode\":\"0005\",\"isPassWorkStaion\":\"0\",\"subSnScan\":true,\"notAutoPassWorkStaion\":false,\"toPassWorkStaion\":false,\"subSnList\":[\"ZTE200603023362\"],\"subSnQty\":\"\",\"itemInfoList\":[{\"itemId\":null,\"itemNo\":\"042060100005\",\"itemName\":\"****直式2.0间距扁平电缆IDC压接插头（孔）\",\"sn\":\"ZTE200603023362\",\"errMsg\":null,\"itemVersion\":null,\"pcbVersion\":null,\"scanType\":null,\"fromBarCodeCenter\":false,\"barCodeType\":\"批次码\",\"qty\":1,\"replaceItemNo\":null,\"hbCode\":\"\"}],\"hbCode\":\"\",\"craftSection\":\"包装\"}",AssemblyRelaScanDTO.class);
		ServiceData serviceData=new ServiceData();
		serviceData.setBo(workOrderList);
		when(PlanscheduleRemoteService.getBasicWorkOrderInfo(anyObject())).thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JacksonJsonConverUtil.beanToJson(serviceData)));
		dto.setSubSnScan(true);
		dto.setSkipTotalQty(BigDecimal.TEN);
		when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);
		List<SysLookupValuesDTO> lookUpList = new ArrayList<SysLookupValuesDTO>();
		SysLookupValuesDTO sysLookupTypesDTO = new SysLookupValuesDTO();
		sysLookupTypesDTO.setLookupMeaning("P1015");
		sysLookupTypesDTO.setDescriptionChin("HFS");
		sysLookupTypesDTO.setAttribute1("30");
		lookUpList.add(sysLookupTypesDTO);
		when(BasicsettingRemoteService.getSysLookupValuesList(anyObject())).thenReturn(lookUpList);
		try {
			assemblyRelaScanService.assemblyRelaScanNew(dto);
		}catch (Exception e){
			Assert.assertEquals(com.zte.common.model.MessageId.FAILED_TO_GET_MATERIAL_STANDARD_USAGE, e.getMessage());
		}
	}

	@Test
	public void getItemInfo() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		dto.setItemCode("itemCode");
		dto.setNewAssemblyRelaScan(true);
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		List<String> snList = new ArrayList<String>();
		snList.add("123");
		dto.setSubSnList(snList);
		BaItem itemInfo = new BaItem();
		itemInfoList.add(itemInfo);
		ProdBindingSettingDTO bindDto = new ProdBindingSettingDTO();
		bindList.add(bindDto);
		checkFlowRsult.setResultType("OK");
		dto.setToPassWorkStaion(true);
		retCode = PowerMockito.mock(RetCode.class);
		PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
		PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
		workOrderList.add(workOrder);
		dto.setMainWorkOrder(workOrder);
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setAttribute3("HFS");
		List<WipExtendIdentification> tempList = new ArrayList<>();
		when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
		when(prodBindingSettingRepository.getBindingInfoByItemNew(anyObject())).thenReturn(bindList);
		when(wipExtendIdentificationRepository.getList(anyObject())).thenReturn(tempList);
		List<SysLookupValuesDTO> lookUpList = new ArrayList<SysLookupValuesDTO>();
		SysLookupValuesDTO sysLookupTypesDTO = new SysLookupValuesDTO();
		sysLookupTypesDTO.setLookupMeaning("P1015");
		sysLookupTypesDTO.setDescriptionChin("HFS");
		sysLookupTypesDTO.setAttribute1("30");
		lookUpList.add(sysLookupTypesDTO);
		when(BasicsettingRemoteService.getSysLookupValuesList(anyObject())).thenReturn(lookUpList);
		List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
		BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
		barcodeExpandDTO.setBarcode("ZTE200603023362");
		barcodeExpandDTO.setIsLead("HFS");
		barcodeExpandDTO.setParentCategoryName("序列码");
		barcodeExpandDTO.setRelatedSnBarcode("ZS1225");
		barcodeExpandDTO.setQuantity(BigDecimal.ZERO);
		barcodeExpandDTOList.add(barcodeExpandDTO);
		when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);
		Assert.assertNotNull(assemblyRelaScanService.getItemInfo(dto).getBo());
	}

	@Test
	public void setTaskNoAndProdplanId() throws Exception {
		AssemblyRelaScanDTO dto =new AssemblyRelaScanDTO();
		dto.setWipInfo(new PsWipInfo());
		PsWorkOrderDTO workOrder =new PsWorkOrderDTO();
		WipExtendIdentification extendInfo = new WipExtendIdentification();
		assemblyRelaScanService.setTaskNoAndProdplanId(dto,workOrder,extendInfo);
		Assert.assertNull(extendInfo.getProdPlanId());
	}

	@Test
	public void getRetCode() throws Exception {
		Map<String, String> replaceMap=new HashMap<>();
		AssemblyRelaScanDTO dto=new AssemblyRelaScanDTO();
		FlowControlInfoDTO checkFlowRsult=new FlowControlInfoDTO();
		RetCode ret=new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID);
		List<BaItem> itemInfoList=new ArrayList<>();
		BaItem baItem=new BaItem();
		itemInfoList.add(baItem);
		List<ProdBindingSettingDTO> bindList=new ArrayList<>();
		ProdBindingSettingDTO prodBindingSettingDTO=new ProdBindingSettingDTO();
		prodBindingSettingDTO.setUsageCount(BigDecimal.TEN);
		prodBindingSettingDTO.setBindedCount(BigDecimal.ONE);
		bindList.add(prodBindingSettingDTO);

		dto.setNewAssemblyRelaScan(true);
		dto.setSkipTotalQty(BigDecimal.TEN);
		List<String>  subList=new ArrayList<>();
		subList.add("sn1");
		dto.setSubSnList(subList);
		PowerMockito.when(wipExtendIdentificationRepository.getUsageCount(any())).thenReturn(Constant.INT_12);
		dto.setItemInfoParamList(itemInfoList);
		dto.setProdBindingSettingDTOS(bindList);
		Assert.assertEquals("0000", assemblyRelaScanService.getRetCode(replaceMap,dto,checkFlowRsult,ret).getCode());
	}


	@Test
	public void isAllBinded() throws Exception {
		Assert.assertTrue(assemblyRelaScanService.isAllBinded(new ArrayList<>(),new AssemblyRelaScanDTO(){{setNewAssemblyRelaScan(true);}}));
	}
	@Test
	public void eachMainMaterial() throws Exception {
		Map<String,String> replaceMap=new HashMap<>();
		replaceMap.put("itemNo","125000360031");
		BaItem baItem=new BaItem();
		baItem.setItemNo("125000360031");
		Map<String, List<MtlRelatedItemsEntityDTO>> mtlRelatedMap=new HashMap<>();
		List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOList=new ArrayList<>();
		MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO=new MtlRelatedItemsEntityDTO();
		mtlRelatedItemsEntityDTO.setItemCode("125000360032");
		mtlRelatedItemsEntityDTO.setReplaceItemCode("12500036003211");
		mtlRelatedItemsEntityDTO.setInventoryItemCode("12500036003211");
		mtlRelatedItemsEntityDTOList.add(mtlRelatedItemsEntityDTO);

		mtlRelatedMap.put("125000360032",mtlRelatedItemsEntityDTOList);

		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		ProdBindingSettingDTO bindDto = new ProdBindingSettingDTO();
		bindDto.setItemCode("12500036003211");
		bindDto.setBindedCount(BigDecimal.ONE);
		bindDto.setUsageCount(BigDecimal.ONE);
		bindList.add(bindDto);
		ProdBindingSettingDTO bindDto1 = new ProdBindingSettingDTO();
		bindDto1.setItemCode("125000360032");
		bindDto1.setBindedCount(BigDecimal.ONE);
		bindDto1.setUsageCount(BigDecimal.ONE);
		bindList.add(bindDto1);
		Map<String,Long> map=new HashMap<>();
		map.put("125000360032",1L);
        for(Map.Entry<String, Long> entry:map.entrySet()){
			Assert.assertTrue(assemblyRelaScanService.eachMainMaterial(replaceMap,mtlRelatedMap,bindList,Pair.of("12500036003211",1L),Pair.of(new StringBuilder(), false)));
		}
		for(Map.Entry<String, Long> entry:map.entrySet()){
			Assert.assertTrue(assemblyRelaScanService.eachMainMaterial(replaceMap,mtlRelatedMap,bindList,Pair.of("125000360032",1L) , Pair.of(new StringBuilder(), false)));
		}


	}

	@Test
	public void setReplaceItemCode() throws Exception {
		Map<String,String> replaceMap=new HashMap<>();
		replaceMap.put("itemNo","itemNo11");
		BaItem baItem=new BaItem();
		baItem.setItemNo("itemNo");
		baItem.setNotByReplaceItem(true);
		assemblyRelaScanService.setReplaceItemCode(replaceMap,baItem);
		Assert.assertNull(baItem.getReplaceItemNo());
		baItem.setNotByReplaceItem(false);
		assemblyRelaScanService.setReplaceItemCode(replaceMap,baItem);
		Assert.assertNotNull(baItem.getReplaceItemNo());
	}

	@Test
	public void insertAssemblyRelaScanRecordInfo() throws Exception {
		AssemblyRelaScanDTO dto =new AssemblyRelaScanDTO();
		PsWorkOrderDTO psWorkOrderDTO=new PsWorkOrderDTO();
		psWorkOrderDTO.setTaskNo("taskNo");
		dto.setMainWorkOrder(psWorkOrderDTO);
		dto.setTaskQty(BigDecimal.ONE);
		Map<String, ItemListEntityDTO> itemListEntityDTOHashMap=new HashMap<>();
		ItemListEntityDTO itemListEntityDTO=new ItemListEntityDTO();
		itemListEntityDTO.setRequiredQuantity("20");
		itemListEntityDTOHashMap.put("itemNo",itemListEntityDTO);
		List<BaItem> baItemList =new ArrayList<>();
		BaItem baItem=new BaItem();
		baItemList.add(baItem);
		assemblyRelaScanService.insertAssemblyRelaScanRecordInfo(dto,baItemList);
		Assert.assertNotNull(dto);
	}

	@Test
	public void RetCode() throws Exception {
		AssemblyRelaScanDTO dto =new AssemblyRelaScanDTO();
		PsWorkOrderDTO psWorkOrderDTO=new PsWorkOrderDTO();
		dto.setMainWorkOrder(psWorkOrderDTO);
		dto.setTaskQty(BigDecimal.ONE);
		BaItem baItem=new BaItem();
		Assert.assertEquals("0000", assemblyRelaScanService.getRetCode(dto, baItem, Constant.TYPE_SEQUENCE_CODE).getCode());
	}

	@Test
	public void getUsageCount() throws Exception {
		AssemblyRelaScanDTO dto =new AssemblyRelaScanDTO();
		PsWorkOrderDTO psWorkOrderDTO=new PsWorkOrderDTO();
		psWorkOrderDTO.setTaskNo("taskNo");
		dto.setMainWorkOrder(psWorkOrderDTO);
		dto.setTaskQty(BigDecimal.ONE);
		Map<String, ItemListEntityDTO> itemListEntityDTOHashMap=new HashMap<>();
		ItemListEntityDTO itemListEntityDTO=new ItemListEntityDTO();
		itemListEntityDTO.setRequiredQuantity("20");
		itemListEntityDTOHashMap.put("itemNo",itemListEntityDTO);
		Assert.assertNotNull(assemblyRelaScanService.getUsageCount("itemNo",dto,itemListEntityDTOHashMap));
		dto.setTaskQty(null);
		Assert.assertNotNull(assemblyRelaScanService.getTaskQty(dto));
		dto.setTaskQty(BigDecimal.ONE);
		Assert.assertNotNull(assemblyRelaScanService.getTaskQty(dto));
	}

	@Test
	public void assemblyRelaScan() throws Exception {
		PowerMockito.mockStatic(PlanscheduleRemoteService.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		List<String> snList = new ArrayList<String>();
		snList.add("123");
		dto.setSubSnList(snList);
		BaItem itemInfo = new BaItem();
		itemInfoList.add(itemInfo);
		ProdBindingSettingDTO bindDto = new ProdBindingSettingDTO();
		bindList.add(bindDto);
		checkFlowRsult.setResultType("OK");
		dto.setToPassWorkStaion(true);
		retCode = PowerMockito.mock(RetCode.class);
		PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);
		List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
		PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
		workOrder.setTaskNo("taskNo1");
		workOrderList.add(workOrder);
		dto.setMainWorkOrder(workOrder);

		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setItemNo("125000360031");
		List<WipExtendIdentification> tempList = new ArrayList<>();
		when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
		when(prodBindingSettingRepository.getBindingInfoByItem(anyObject())).thenReturn(bindList);
		when(wipExtendIdentificationRepository.getList(anyObject())).thenReturn(tempList);
		dto = JSON.parseObject("{\"itemCode\":\"125000360031\",\"processCode\":\"P1035\",\"workStationCode\":\"S1069\",\"workOrderNo\":\"ZZ河源VCS190619036-V11-包装5502\",\"mainSn\":\"219999321946\",\"lineCode\":\"0005\",\"isPassWorkStaion\":\"0\",\"mainSnScan\":true,\"notAutoPassWorkStaion\":false,\"craftSection\":\"包装\"}",AssemblyRelaScanDTO.class);
		dto.setMainSnScan(true);
		ServiceData serviceData=new ServiceData();
		serviceData.setBo(workOrderList);
		when(PlanscheduleRemoteService.getBasicWorkOrderInfo(anyObject())).thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JacksonJsonConverUtil.beanToJson(serviceData)));
		dto.setSkipTotalQty(BigDecimal.TEN);
		List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
		BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
		barcodeExpandDTO.setBarcode("ZTE200603023362");
		barcodeExpandDTO.setParentCategoryName("序列码");
		barcodeExpandDTOList.add(barcodeExpandDTO);
		when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);
		Assert.assertEquals("0000", assemblyRelaScanService.assemblyRelaScanNew(dto).getCode());
	}

	@Test
	public void relaScanBatch() throws Exception {
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		PowerMockito.mockStatic(BasicsettingRemoteService.class,DatawbRemoteService.class, HttpRemoteUtil.class, CrafttechRemoteService.class, RedisHelper.class, PlanscheduleRemoteService.class);
		PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(false);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(MAIN_SN_IS_NOT_NULL, e.getMessage());
		}
		dto.setMainSn("123123");
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(MAIN_SN_WIP_EXT_BIND_LOCK, e.getMessage());
		}
		PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(PROCESS_IS_NOT_NULL, e.getMessage());
		}
		dto.setProcessName("非白盒装配");
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(WORK_STATION_IS_NOT_NULL, e.getMessage());
		}
		dto.setWorkStationName("装配上线扫描");
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(SUB_SN_IS_NOT_NULL, e.getMessage());
		}
		List<AssemblyRelaScanSubSnDTO> subSnDTOList = new ArrayList<>();
		AssemblyRelaScanSubSnDTO subSnDTO = new AssemblyRelaScanSubSnDTO();
		subSnDTO.setSubSn("888824500009");
		subSnDTO.setItemNo("122393751064");
		subSnDTO.setQty(1);
		subSnDTOList.add(subSnDTO);
		dto.setSubSnDTOList(subSnDTOList);
		PowerMockito.when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(null);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(CURRPROCESSCODE_OR_WORKSTATION_OF_WIPINFO_IS_NULL, e.getMessage());
		}
		ObjectMapper mapper = new ObjectMapper();
		JsonNode json = mapper.readTree("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"processId\":\"7fad8049-bd13-4cd6-b40c-423680cc27d7\",\"processCode\":\"P1341\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"非白盒装配\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":\"10240988\",\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2019-11-28 15:36:44\",\"lastUpdatedBy\":\"10240988\",\"lastUpdatedDate\":\"2019-11-28 15:36:44\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"包装\",\"processControlGroupName\":null,\"jigBinding\":0,\"jigDownline\":0,\"wareHousing\":0,\"sourceSys\":null,\"bimuId\":null,\"processControl\":\"0\",\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null,\"testControl\":\"0\"},{\"processId\":\"3ecdce8d-a6d7-4855-93d6-89c4a31f3604\",\"processCode\":\"S1332\",\"processType\":\"手工测试\",\"xType\":\"工站\",\"processName\":\"装配上线扫描\",\"toolType\":null,\"processControlGroup\":\"smCommonScan\",\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":null,\"lastUpdatedBy\":\"00236785\",\"lastUpdatedDate\":\"2020-07-28 16:35:49\",\"orgId\":null,\"entityId\":2.0,\"factoryId\":52.0,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":null,\"processControlGroupName\":\"标模通用扫描\",\"jigBinding\":0,\"jigDownline\":0,\"wareHousing\":0,\"sourceSys\":null,\"bimuId\":null,\"processControl\":\"0\",\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null,\"testControl\":null}],\"other\":{\"msg\":\"Success\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"52ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue May 30 16:30:02 CST 2023\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10313234\"}}");
		PowerMockito.when(CrafttechRemoteService.getProcessInfo(anyMap())).thenReturn(json);
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setSn("123123");
		wipInfo.setCraftSection("入库");
		wipInfo.setAttribute2("123123");
		PowerMockito.when(psWipInfoRepository.selectPsWipInfoBySn(anyString())).thenReturn(wipInfo);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(MAIN_SN_IS_IN_STORE, e.getMessage());
		}
		wipInfo.setCraftSection("SMT-B");
		List<ItemListEntityDTO> itemListEntityDTOList = new ArrayList<>();
		ItemListEntityDTO itemListEntityDTO=new ItemListEntityDTO();
		itemListEntityDTO.setItemNo("122393751115");
		itemListEntityDTO.setRequiredQuantity("1");
		itemListEntityDTOList.add(itemListEntityDTO);
		PowerMockito.when(datawbRemoteService.getErpItemListByTaskNo(anyString())).thenReturn(itemListEntityDTOList);
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("P1015");
		sysLookupTypesDTO.setDescriptionChin("HFS");
		sysLookupTypesDTO.setAttribute1("30");
		when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
		ServiceData serviceData=new ServiceData();
		serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG));
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(GET_CURRENT_WORKORDER_ERROR, e.getMessage());
		}
		List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
		PsWorkOrderDTO workOrderDTO = new PsWorkOrderDTO();
		workOrderDTO.setLineCode("123");
		PsWorkOrderDTO workOrderDTO1 = new PsWorkOrderDTO();
		workOrderDTO1.setLineCode("123");
		workOrderList.add(workOrderDTO);
		workOrderList.add(workOrderDTO1);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(any(), any())).thenReturn(workOrderList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(GET_CARFT_NULL, e.getMessage());
		}
		workOrderDTO.setProcessGroup("P1341");
		workOrderDTO1.setProcessGroup("P1341");
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(any(), any())).thenReturn(workOrderList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(TASK_NO_HAVE_MORE_WORK_ORDER_NO, e.getMessage());
		}
		workOrderList.remove(workOrderDTO1);
		PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(any(), any())).thenReturn(workOrderList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(GET_ITEM_INFO_FAIL, e.getMessage());
		}
		serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		serviceData.setBo(null);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(ITEM_NO_NO_BIND_LIST, e.getMessage());
		}
		List<MtlRelatedItemsEntityDTO> entityDTOS = new ArrayList<>();
		MtlRelatedItemsEntityDTO dto1 = new MtlRelatedItemsEntityDTO();
		dto1.setInventoryItemCode("122393751064");
		dto1.setRelatedItemCode("122393751115");
		entityDTOS.add(dto1);
		serviceData.setBo(entityDTOS);
		serviceData.setOther(null);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(ITEM_NO_NO_BIND_LIST, e.getMessage());
		}
		Map<String, Object> map = new HashMap<>();
		map.put("totalCount", 1);
		serviceData.setOther(map);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		ProdBindingSettingDTO dto2 = new ProdBindingSettingDTO();
		dto2.setItemCode("122393751115");
		bindList.add(dto2);
		PowerMockito.when(prodBindingSettingRepository.getBindingInfoByItemNew(any())).thenReturn(bindList);
		PowerMockito.when(wipExtendIdentificationRepository.checkWipExtExist(any())).thenReturn(0);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(SUB_SN_NOT_EXIST, e.getMessage());
		}
		List<BarcodeExpandDTO> dtoList = new ArrayList<>();
		BarcodeExpandDTO dto3 = new BarcodeExpandDTO();
		dto3.setBarcode("8888245000091");
		dtoList.add(dto3);
		PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(dtoList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(NOT_NEED_BIND_SUB_SN, e.getMessage());
		}
		dto3.setItemCode("1223937510641");
		dto3.setBarcode("888824500009");
		PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(dtoList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(SN_ITEMNO_NOT_SAME, e.getMessage());
		}
		dto3.setItemCode("122393751064");
		PowerMockito.when(barcodeCenterRemoteService.expandQuery(any())).thenReturn(dtoList);
		try {
			assemblyRelaScanService.relaScanBatch(dto);
		} catch (Exception e) {
			Assert.assertEquals(SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
	}

	@Test
	public void subSnScanNew() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		Map<String, List<MtlRelatedItemsEntityDTO>> replaceItem = new HashMap<>();
		List<ProdBindingSettingDTO> bindList = new ArrayList<>();
		ProdBindingSettingDTO settingDTO = new ProdBindingSettingDTO();
		settingDTO.setItemCode("122393751064");
		settingDTO.setUsageCount(new BigDecimal(1));
		settingDTO.setBindedCount(new BigDecimal(1));
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		List<String> snList = new ArrayList<>();
		snList.add("888824500009");
		dto.setSubSnList(snList);

		List<AssemblyRelaScanSubSnDTO> subSnDTOList = new ArrayList<>();
		AssemblyRelaScanSubSnDTO subSnDTO = new AssemblyRelaScanSubSnDTO();
		subSnDTO.setSubSn("888824500009");
		subSnDTO.setItemNo("122393751064");
		subSnDTO.setQty(1);
		subSnDTOList.add(subSnDTO);
		dto.setSubSnDTOList(subSnDTOList);
		FlowControlInfoDTO checkFlowResult = new FlowControlInfoDTO();
		Map<String, Long> qtyMap = new HashMap<>();
		qtyMap.put("122393751064", 1L);
		PowerMockito.when(wipExtendIdentificationRepository.getList(any())).thenReturn(new ArrayList<>());
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"subSnScanNew",replaceItem, bindList, dto, checkFlowResult, new HashSet<>());
		} catch (Exception e) {}
		PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(new ArrayList<>());
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"checkSubSn",snList, new AssemblyRelaScanDTO(), new ArrayList<>(), new HashSet<>(), new HashSet<>());
		} catch (Exception e) {
			Assert.assertEquals(SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		List<PsWipInfo> list = new ArrayList<>();
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setSn("123321");
		wipInfo.setAttribute3("321");
		list.add(wipInfo);
		dto.setMainSnWipInfo(wipInfo);
		PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(list);
		List<SysLookupValuesDTO> listSys = new ArrayList<>();
		SysLookupValuesDTO dto1 = new SysLookupValuesDTO();
		dto1.setLookupMeaning("123");
		dto1.setDescriptionChin("123");
		listSys.add(dto1);
		PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(any())).thenReturn(listSys);
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"checkSubSn",snList, dto, new ArrayList<>(), new HashSet<>(), new HashSet<>());
		} catch (Exception e) {
			Assert.assertEquals(SN_STATUS_NOT_WAREHOUSE_IN, e.getMessage());
		}
		wipInfo.setCraftSection("入库");
		wipInfo.setAttribute3("123");
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"checkSubSn",snList, dto, new ArrayList<>(), new HashSet<>(), new HashSet<>());
		} catch (Exception e) {
			Assert.assertEquals(ISLEAD_DATA_DICTIONARY_DOES_NOT_EXIST, e.getMessage());
		}

	}

	@Test
	public void checkSnLock() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class, CommonUtils.class);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		List<String> snList = new ArrayList<>();
		snList.add("888824500009");
		dto.setSubSnList(snList);
		PsWipInfo wipInfo = new PsWipInfo();
		wipInfo.setCraftSection("SMT-A");
		dto.setWipInfo(wipInfo);
		List<WipExtendIdentification> bindingList = new ArrayList<>();
		WipExtendIdentification wipExt = new WipExtendIdentification();
		wipExt.setSn("888824500010");
		bindingList.add(wipExt);
		List<SysLookupTypesDTO> sysLookUpList = new ArrayList<>();
		SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
		sysLookUpList.add(typesDTO);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(sysLookUpList);
		PowerMockito.when(wipExtendIdentificationRepository.getAllChildProdBinding(any())).thenReturn(bindingList);
		List<BarcodeLockDetail> lockDetailList = new ArrayList<>();
		BarcodeLockDetail detail = new BarcodeLockDetail();
		detail.setBatchSn("888824500010");
		detail.setType("条码锁定");
		lockDetailList.add(detail);
		PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(any(), any())).thenReturn(new ArrayList<>());
		PowerMockito.when(standardModeCommonScanService.getTechnicalChangeBarcode(any(), any())).thenReturn("123");
		PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sn.technical.change");
		Set<String> prodSet = new HashSet<>();
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"checkSnLock", dto, prodSet);
		} catch (Exception e) {
			Assert.assertEquals(SN_TECHNICAL_CHANGE, e.getMessage());
		}
		PowerMockito.when(standardModeCommonScanService.getTechnicalChangeBarcode(any(), any())).thenReturn(null);
		PowerMockito.when(barcodeLockDetailRepository.queryLockDetail(any(), any())).thenReturn(lockDetailList);
		try {
			Whitebox.invokeMethod(assemblyRelaScanService,"checkSnLock", dto, prodSet);
		} catch (Exception e) {
			Assert.assertEquals(SN_IS_LOCK_CAN_NOT_BIND, e.getMessage());
		}
	}
}
