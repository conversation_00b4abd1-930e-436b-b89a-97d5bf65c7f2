package com.zte.test;

import java.util.Date;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import com.zte.application.impl.ImesSnDataBackServiceImpl;
import com.zte.domain.model.ImesSnDataBackRepository;

@RunWith(PowerMockRunner.class)
public class ImesSnDataBackServiceImplTest {
	
	@InjectMocks
	private ImesSnDataBackServiceImpl imesSnDataBackServiceImpl;
	
	@Mock
	private ImesSnDataBackRepository backRepository;
	

	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
    }
	
	@Test
	public void updateWriteBackStatusRangeTime() throws Exception {
		PowerMockito.when(backRepository.updateWriteBackStatusRangeTime(Mockito.any(),Mockito.any())).thenReturn(1);
		Date createDateStart = new Date(); 
		Date createDateEnd = new Date();
		Assert.assertEquals(1,imesSnDataBackServiceImpl.updateWriteBackStatusRangeTime(createDateStart, createDateEnd));
	}
	
}
