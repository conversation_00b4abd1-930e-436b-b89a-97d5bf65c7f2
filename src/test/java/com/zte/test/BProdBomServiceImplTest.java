package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.BProdBomServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.aop.framework.AopContext;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2024-12-30 11:29
 */
@PrepareForTest({SpringContextUtil.class,JacksonJsonConverUtil.class, RetCode.class,AopContext.class,
        MicroServiceRestUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class,
        ConstantInterface.class,CrafttechRemoteService.class, MicroServiceDiscoveryInvoker.class, HttpRemoteUtil.class})
public class BProdBomServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private BProdBomServiceImpl bProdBomService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class, MicroServiceDiscoveryInvoker.class, HttpRemoteUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void getBProdBomHeader() throws Exception {
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("bProdBomHeader/getBProdBomHeader");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("");
                }})
        );
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(null);
        Assert.assertNotNull(bProdBomService.getBProdBomHeader(new BProdBomHeaderDTO()));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        List<BProdBomHeaderDTO> list = new ArrayList<>();
        list.add(new BProdBomHeaderDTO(){{
            setProdplanId("1234567");
        }});
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(null);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(list);
        Assert.assertNotNull(bProdBomService.getBProdBomHeader(new BProdBomHeaderDTO()));
    }

    @Test
    public void getBProdBomListBatch() throws Exception {
        List<String> bomList = new ArrayList(){{add("123");}};
        List<BProdBomHeaderDTO> list = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.any()))
                .thenReturn(list);
        Assert.assertNotNull(bProdBomService.getBProdBomListBatch(bomList));
        list.add(new BProdBomHeaderDTO(){{
            setProdplanId("1234567");
        }});
        list.add(new BProdBomHeaderDTO(){{
            setProdplanId("1234333");
            setProductCode("32112");
        }});
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.any()))
                .thenReturn(list);
        Assert.assertNotNull(bProdBomService.getBProdBomListBatch(bomList));
    }
}
