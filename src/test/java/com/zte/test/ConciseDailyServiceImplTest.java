package com.zte.test;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.PsWipInfoService;
import com.zte.application.TaskDailyStatDetailService;
import com.zte.application.TaskDailyStatHeadService;
import com.zte.application.WarehousehmEntryDetailService;
import com.zte.application.WipScanHistoryService;
import com.zte.application.impl.ConciseDailyServiceImpl;
import com.zte.application.impl.TaskDailyStatHeadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ProdPlanStock;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.TaskDailyStatDetail;
import com.zte.domain.model.TaskDailyStatHead;
import com.zte.domain.model.TaskDailyStatHeadRepository;
import com.zte.domain.model.WorkOrderOperateHis;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.ConciseDailyDTO;
import com.zte.interfaces.dto.CtBasicRouteDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.springframework.data.util.Pair;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyBoolean;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anySet;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doThrow;

/**
 * ClassName: ConciseDailyServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2022/12/16 上午10:25
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,CrafttechRemoteService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class, JSONObject.class})
public class ConciseDailyServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ConciseDailyServiceImpl service;

    @Mock
    TaskDailyStatDetailService taskDailyStatDetailService;

    @Mock
    TaskDailyStatHeadService taskDailyStatHeadService;

    @Mock
    TaskDailyStatHeadServiceImpl taskDailyStatHeadServiceImpl;

    @Mock
    TaskDailyStatHeadRepository taskDailyStatHeadRepository;

    @Mock
    WipScanHistoryService wipScanHistoryService;

    @Mock
    PsWipInfoService psWipInfoService;

    @Mock
    WarehousehmEntryDetailService warehousehmEntryDetailService;

    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    Logger LOG;

    @Mock
    EmailUtils emailUtils;

    @Before
    public void init(){
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,BasicsettingRemoteService.class);
    }

    @Test
    public void transferToMapList()throws Exception{
        List<TaskDailyStatHead> headList=new ArrayList<>();
        List<TaskDailyStatDetail> detailList=new ArrayList<>();
        List<SysLookupTypesDTO> sysLookupTypesList=new ArrayList<>();
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        //headList不为空
        TaskDailyStatHead taskDailyStatHead=new TaskDailyStatHead();
        taskDailyStatHead.setStatisticDate(new Date());
        headList.add(taskDailyStatHead);
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        //detailList不为空
        TaskDailyStatDetail taskDailyStatDetail1=new TaskDailyStatDetail();
        taskDailyStatDetail1.setHeadId("1");
        taskDailyStatDetail1.setYesterdayOnhandQty(1L);
        taskDailyStatDetail1.setTurnIntoQty(1L);
        taskDailyStatDetail1.setTurnOutQty(1L);
        taskDailyStatDetail1.setOnhandQty(1L);
        TaskDailyStatDetail taskDailyStatDetail2=new TaskDailyStatDetail();
        taskDailyStatDetail2.setHeadId("2");
        taskDailyStatDetail2.setYesterdayOnhandQty(1L);
        taskDailyStatDetail2.setTurnIntoQty(1L);
        taskDailyStatDetail2.setTurnOutQty(1L);
        taskDailyStatDetail2.setOnhandQty(1L);
        detailList.add(taskDailyStatDetail1);
        detailList.add(taskDailyStatDetail2);
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        //sysLookupTypesList不为空
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesList.add(sysLookupTypesDTO);
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        //attribute3不为空
        sysLookupTypesDTO.setAttribute3("3");
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        //headid不为空
        taskDailyStatHead.setHeadId("1");
        taskDailyStatHead.setStatisticDate(null);
        Whitebox.invokeMethod(service,"transferToMapList",
                headList,detailList,sysLookupTypesList);
        Assert.assertEquals("1", taskDailyStatHead.getHeadId());
    }

    @Test
    public void findFirstStartTime()throws Exception{
        List<WorkOrderOperateHis> workOrderOperateHisList =new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getWorkFirstStartTimeByWorkNoList(Mockito.anyList()))
                .thenReturn(workOrderOperateHisList);

        List<PsWorkOrderBasic> workOrderList=new ArrayList<>();
        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        //workOrderList不为空
        PsWorkOrderBasic psWorkOrderBasic=new PsWorkOrderBasic();
        workOrderList.add(psWorkOrderBasic);

        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        //entity不为空
        psWorkOrderBasic.setRemark("1");
        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        //workOrderNo不为空
        psWorkOrderBasic.setRemark(null);
        psWorkOrderBasic.setWorkOrderNo("order1");
        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        //entity不为空 workOrderNo不为空
        psWorkOrderBasic.setRemark("1");
        psWorkOrderBasic.setWorkOrderNo("order1");
        psWorkOrderBasic.setSourceTask("task1");
        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        //workOrderList两个
        PsWorkOrderBasic psWorkOrderBasic1=new PsWorkOrderBasic();
        psWorkOrderBasic1.setRemark("2");
        psWorkOrderBasic1.setWorkOrderNo("order2");
        psWorkOrderBasic1.setSourceTask("task1");
        workOrderList.add(psWorkOrderBasic1);
        Whitebox.invokeMethod(service,"findFirstStartTime",
                workOrderList);
        Assert.assertEquals("task1", psWorkOrderBasic1.getSourceTask());
    }

    @Test
    public void findFirstAndLastProcess()throws Exception{
        String[] processArr=null;
        Map<String, Integer> processToSeqMap=new HashMap<>();
        SysLookupTypesDTO sysEntity=new SysLookupTypesDTO();
        Whitebox.invokeMethod(service,"findFirstAndLastProcess",
                processArr,processToSeqMap,sysEntity);
        //processArr不为空
        processArr=new String[]{"123"};
        Whitebox.invokeMethod(service,"findFirstAndLastProcess",
                processArr,processToSeqMap,sysEntity);
        //seq 不为空
        processToSeqMap.put("123",1);
        Whitebox.invokeMethod(service,"findFirstAndLastProcess",
                processArr,processToSeqMap,sysEntity);
        //seq 小于MAX
        processToSeqMap.put("123",-2);
        Whitebox.invokeMethod(service,"findFirstAndLastProcess",
                processArr,processToSeqMap,sysEntity);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleScheduledTaskTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        service.handleScheduledTask(conciseDailyDTO, null);
        service.handleScheduledTask(conciseDailyDTO, -1);
        service.handleScheduledTask(conciseDailyDTO, 2);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysDto1 = new SysLookupTypesDTO();
        sysDto1.setLookupMeaning("1$S$3");
        sysDto1.setAttribute3("SMTA");
        sysDto1.setRemark("贴片A面+刮胶A面+点胶A面");
        sysDto1.setAttribute1("Y");
        SysLookupTypesDTO sysDto2 = new SysLookupTypesDTO();
        sysDto2.setLookupMeaning("2$T$4");
        sysDto2.setAttribute3("SMTB");
        sysDto2.setRemark("贴片B面+刮胶B面+点胶B面");
        sysDto2.setAttribute1("Y");
        sysLookupTypesDTOList.add(sysDto1);
        sysLookupTypesDTOList.add(sysDto2);
        SysLookupTypesDTO sysDto3 = new SysLookupTypesDTO();
        sysDto3.setAttribute1("email");
        sysDto3.setLookupMeaning("dasdasd");
        sysLookupTypesDTOList.add(sysDto3);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("prod1");
        psTask1.setItemNo("itemNo1");
        psTask1.setTaskQty(new BigDecimal(1000));
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("prod2");
        psTask2.setItemNo("itemNo2");
        psTask2.setTaskQty(new BigDecimal(2000));
        psTaskList.add(psTask2);
        psTaskList.add(psTask1);
        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDaily(null, 1, 100)).thenReturn(psTaskList);
        List<TaskDailyStatDetail> dailyDetailList = new ArrayList<>();
        TaskDailyStatDetail taskDailyDetail1 = new TaskDailyStatDetail();
        taskDailyDetail1.setProdplanId("prod1");
        taskDailyDetail1.setOnhandQty(10L);
        taskDailyDetail1.setStatItem("SMTA");
        TaskDailyStatDetail taskDailyDetail2 = new TaskDailyStatDetail();
        taskDailyDetail2.setProdplanId("prod1");
        taskDailyDetail2.setOnhandQty(11L);
        taskDailyDetail2.setStatItem("SMTB");
        TaskDailyStatDetail taskDailyDetail3 = new TaskDailyStatDetail();
        taskDailyDetail3.setProdplanId("prod2");
        taskDailyDetail3.setOnhandQty(20L);
        taskDailyDetail3.setStatItem("SMTA");
        TaskDailyStatDetail taskDailyDetail4 = new TaskDailyStatDetail();
        taskDailyDetail4.setProdplanId("prod2");
        taskDailyDetail4.setOnhandQty(21L);
        taskDailyDetail4.setStatItem("SMTB");
        dailyDetailList.add(taskDailyDetail1);
        dailyDetailList.add(taskDailyDetail2);
        dailyDetailList.add(taskDailyDetail3);
        dailyDetailList.add(taskDailyDetail4);
        PowerMockito.when(taskDailyStatDetailService.selectByTimeAndProds(any(), anyList())).thenReturn(dailyDetailList);

        // 准备过站和转交数据
        List<WipScanHistoryDTO> wipScanInfoList = new ArrayList<>();
        WipScanHistoryDTO wipScanHistoryDTO1 = new WipScanHistoryDTO();
        wipScanHistoryDTO1.setAttribute1("prod1");
        wipScanHistoryDTO1.setCurrProcessCode("1");
        wipScanHistoryDTO1.setTransmitQty(31L);
        wipScanHistoryDTO1.setCrossStationQty(41L);
        WipScanHistoryDTO wipScanHistoryDTO2 = new WipScanHistoryDTO();
        wipScanHistoryDTO2.setAttribute1("prod1");
        wipScanHistoryDTO2.setCurrProcessCode("S");
        wipScanHistoryDTO2.setTransmitQty(32L);
        wipScanHistoryDTO2.setCrossStationQty(42L);
        WipScanHistoryDTO wipScanHistoryDTO3 = new WipScanHistoryDTO();
        wipScanHistoryDTO3.setAttribute1("prod1");
        wipScanHistoryDTO3.setCurrProcessCode("3");
        wipScanHistoryDTO3.setTransmitQty(33L);
        wipScanHistoryDTO3.setCrossStationQty(43L);
        WipScanHistoryDTO wipScanHistoryDTO4 = new WipScanHistoryDTO();
        wipScanHistoryDTO4.setAttribute1("prod1");
        wipScanHistoryDTO4.setCurrProcessCode("2");
        wipScanHistoryDTO4.setTransmitQty(34L);
        wipScanHistoryDTO4.setCrossStationQty(44L);
        WipScanHistoryDTO wipScanHistoryDTO5 = new WipScanHistoryDTO();
        wipScanHistoryDTO5.setAttribute1("prod1");
        wipScanHistoryDTO5.setCurrProcessCode("T");
        wipScanHistoryDTO5.setTransmitQty(35L);
        wipScanHistoryDTO5.setCrossStationQty(45L);


        WipScanHistoryDTO wipScanHistoryDTO6 = new WipScanHistoryDTO();
        wipScanHistoryDTO6.setAttribute1("prod2");
        wipScanHistoryDTO6.setCurrProcessCode("S");
        wipScanHistoryDTO6.setTransmitQty(31L);
        wipScanHistoryDTO6.setCrossStationQty(41L);
        WipScanHistoryDTO wipScanHistoryDTO7 = new WipScanHistoryDTO();
        wipScanHistoryDTO7.setAttribute1("prod2");
        wipScanHistoryDTO7.setCurrProcessCode("2");
        wipScanHistoryDTO7.setTransmitQty(34L);
        wipScanHistoryDTO7.setCrossStationQty(44L);
        WipScanHistoryDTO wipScanHistoryDTO8 = new WipScanHistoryDTO();
        wipScanHistoryDTO8.setAttribute1("prod2");
        wipScanHistoryDTO8.setCurrProcessCode("4");
        wipScanHistoryDTO8.setTransmitQty(35L);
        wipScanHistoryDTO8.setCrossStationQty(45L);
        WipScanHistoryDTO wipScanHistoryDTO9 = new WipScanHistoryDTO();
        wipScanHistoryDTO9.setAttribute1("prod2");
        wipScanHistoryDTO9.setCurrProcessCode("5");
        wipScanHistoryDTO9.setTransmitQty(36L);
        wipScanHistoryDTO9.setCrossStationQty(46L);
        wipScanInfoList.add(wipScanHistoryDTO1);
        wipScanInfoList.add(wipScanHistoryDTO2);
        wipScanInfoList.add(wipScanHistoryDTO3);
        wipScanInfoList.add(wipScanHistoryDTO4);
        wipScanInfoList.add(wipScanHistoryDTO5);
        wipScanInfoList.add(wipScanHistoryDTO6);
        wipScanInfoList.add(wipScanHistoryDTO7);
        wipScanInfoList.add(wipScanHistoryDTO8);
        wipScanInfoList.add(wipScanHistoryDTO9);
        PowerMockito.when(wipScanHistoryService.countQtyGroupByProdAndProcess(any(), anyList(), anyBoolean())).thenReturn(wipScanInfoList);
        PowerMockito.when(wipScanHistoryService.countQtyForScrapOrMaintence(any(), anyList(), anyBoolean())).thenReturn(wipScanInfoList);

        List<WipScanHistoryDTO> wipScanInfoList1 = new ArrayList<>();
        WipScanHistoryDTO wipScanHistory1 = new WipScanHistoryDTO();
        wipScanHistory1.setAttribute1("prod1");
        wipScanHistory1.setCraftSection("SMT-A");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date1 = "2022-11-11 11:11:11";
        wipScanHistory1.setOnLineTime(sdf.parse(date1));
        WipScanHistoryDTO wipScanHistory2 = new WipScanHistoryDTO();
        wipScanHistory2.setAttribute1("prod1");
        wipScanHistory2.setCraftSection("SMT-B");
        String date2 = "2022-11-12 11:11:11";
        wipScanHistory2.setOnLineTime(sdf.parse(date2));
        WipScanHistoryDTO wipScanHistory3 = new WipScanHistoryDTO();
        wipScanHistory3.setAttribute1("prod1");
        wipScanHistory3.setCraftSection("TEST");
        String date3 = "2022-10-12 11:11:11";
        wipScanHistory3.setOnLineTime(sdf.parse(date3));
        WipScanHistoryDTO wipScanHistory4 = new WipScanHistoryDTO();
        wipScanHistory4.setAttribute1("prod1");
        wipScanHistory4.setCraftSection("DIP");
        String date4 = "2022-09-12 11:11:11";
        wipScanHistory4.setOnLineTime(sdf.parse(date4));

        WipScanHistoryDTO wipScanHistory5 = new WipScanHistoryDTO();
        wipScanHistory5.setAttribute1("prod2");
        wipScanHistory5.setCraftSection("SMT-B");
        String date5 = "2022-11-12 11:11:12";
        wipScanHistory5.setOnLineTime(sdf.parse(date5));
        WipScanHistoryDTO wipScanHistory6 = new WipScanHistoryDTO();
        wipScanHistory6.setAttribute1("prod2");
        wipScanHistory6.setCraftSection("TEST");
        String date6 = "2022-10-12 11:11:12";
        wipScanHistory6.setOnLineTime(sdf.parse(date6));
        WipScanHistoryDTO wipScanHistory7 = new WipScanHistoryDTO();
        wipScanHistory7.setAttribute1("prod2");
        wipScanHistory7.setCraftSection("DIP");
        String date7 = "2022-09-12 11:11:12";
        wipScanHistory7.setOnLineTime(sdf.parse(date7));
        wipScanInfoList1.add(wipScanHistory1);
        wipScanInfoList1.add(wipScanHistory2);
        wipScanInfoList1.add(wipScanHistory3);
        wipScanInfoList1.add(wipScanHistory4);
        wipScanInfoList1.add(wipScanHistory5);
        wipScanInfoList1.add(wipScanHistory6);
        wipScanInfoList1.add(wipScanHistory7);
        PowerMockito.when(wipScanHistoryService.findOnLineTimeOfProcess(anyList())).thenReturn(wipScanInfoList1);

        PowerMockito.mockStatic(CrafttechRemoteService.class);
        List<CtBasicRouteDTO> routeInfoList = new ArrayList<>();
        List<CtBasicRouteDTO> routeInfoList1 = new ArrayList<>();
        CtBasicRouteDTO ctBasicRouteDTO1 = new CtBasicRouteDTO();
        ctBasicRouteDTO1.setItemNo("itemNo1");
        ctBasicRouteDTO1.setRoutePathCode("1$S$3$2$T$N");
        ctBasicRouteDTO1.setRouteDetail("贴片A面->刮胶A面->点胶A面->贴片B面->刮胶B面->入库");

        CtBasicRouteDTO ctBasicRouteDTO2 = new CtBasicRouteDTO();
        ctBasicRouteDTO2.setRouteId("routeId2");
        ctBasicRouteDTO2.setRoutePathCode("9$S$2$4$5$N");
        ctBasicRouteDTO2.setRouteDetail("刮胶A面->贴片B面->点胶B面->DIP->入库");

        routeInfoList.add(ctBasicRouteDTO1);
        routeInfoList1.add(ctBasicRouteDTO2);

        // 一个测试用物料代码查，一个测试用routeID查。
        PowerMockito.when(CrafttechRemoteService.getRouteAndSeqByItemNos(anyList())).thenReturn(routeInfoList);
        PowerMockito.when(CrafttechRemoteService.getRouteAndSeqByRouteIds(anyList())).thenReturn(routeInfoList1);

        List<PsWorkOrderBasic> workOrderList = new ArrayList<>();
        PsWorkOrderBasic workOrder1 = new PsWorkOrderBasic();
        // 验证计算转入时子工序是工艺路径首工序
        workOrder1.setWorkOrderNo("work1");
        workOrder1.setRemark("0");
        workOrder1.setProcessGroup("1$2");
        workOrder1.setSourceTask("prod1");
        workOrder1.setRouteId("routeId1");
        // 验证计算转出时子工序是指令的最后子工序
        PsWorkOrderBasic workOrder2 = new PsWorkOrderBasic();
        workOrder2.setWorkOrderNo("work2");
        workOrder2.setRemark("2");
        workOrder2.setProcessGroup("3");
        workOrder2.setSourceTask("prod1");
        workOrder2.setRouteId("routeId1");
        // 验证计算转入时子工序是指令的首子工序，计算转出时时子工序是入库前子工序
        PsWorkOrderBasic workOrder3 = new PsWorkOrderBasic();
        workOrder3.setWorkOrderNo("work3");
        workOrder3.setRemark("3");
        workOrder3.setProcessGroup("2$T");
        workOrder3.setSourceTask("prod1");
        workOrder3.setRouteId("routeId1");
        // 最后子工序 入库
        PsWorkOrderBasic workOrder4 = new PsWorkOrderBasic();
        workOrder4.setWorkOrderNo("work4");
        workOrder4.setRemark("5");
        workOrder4.setProcessGroup("N");
        workOrder4.setSourceTask("prod1");
        workOrder4.setRouteId("routeId1");
        //  验证计算转入时子工序S不是工艺路径首工序，也不是指令首工序， 转出时是指令的最后子工序
        PsWorkOrderBasic workOrder5 = new PsWorkOrderBasic();
        workOrder5.setWorkOrderNo("work5");
        workOrder5.setRemark("0");
        workOrder5.setProcessGroup("1$S");
        workOrder5.setSourceTask("prod2");
        workOrder5.setRouteId("routeId2");
        // 验证转入时，子工序2不是工艺路径首工序，是指令首工序 ，转出时4既不是入库前，也不是指令最后子工序
        PsWorkOrderBasic workOrder6 = new PsWorkOrderBasic();
        workOrder6.setWorkOrderNo("work6");
        workOrder6.setRemark("2");
        workOrder6.setProcessGroup("2$4$5");
        workOrder6.setSourceTask("prod2");
        workOrder6.setRouteId("routeId2");
        PsWorkOrderBasic workOrder8 = new PsWorkOrderBasic();
        workOrder8.setWorkOrderNo("work8");
        workOrder8.setRemark("5");
        workOrder8.setProcessGroup("N");
        workOrder8.setSourceTask("prod2");
        workOrder8.setRouteId("routeId2");
        workOrderList.add(workOrder1);
        workOrderList.add(workOrder2);
        workOrderList.add(workOrder3);
        workOrderList.add(workOrder4);
        workOrderList.add(workOrder5);
        workOrderList.add(workOrder6);
        workOrderList.add(workOrder8);
        PowerMockito.when(PlanscheduleRemoteService.queryRouteIdByProdIdList(anyList())).thenReturn(workOrderList);

        // MOCK首指令开工时间
        List<WorkOrderOperateHis> workOrderOperateHisList = new ArrayList<>();
        WorkOrderOperateHis workHis1 = new WorkOrderOperateHis();
        String workHisDate1 = "2022-12-11 11:11:12";
        workHis1.setWorkOrderNo("work1");
        workHis1.setCreateDate(sdf.parse(workHisDate1));
        WorkOrderOperateHis workHis2 = new WorkOrderOperateHis();
        String workHisDate2 = "2022-12-12 11:11:12";
        workHis2.setWorkOrderNo("work5");
        workHis2.setCreateDate(sdf.parse(workHisDate2));
        workOrderOperateHisList.add(workHis1);
        workOrderOperateHisList.add(workHis2);
        PowerMockito.when(PlanscheduleRemoteService.getWorkFirstStartTimeByWorkNoList(anyList())).thenReturn(workOrderOperateHisList);

        // Mock过站信息  prod1: 1$S$3$2$T$N,  prod2 :S$2$4$5$N
        List<PsWipInfoDTO> psWipInfoDTOList = new ArrayList<>();
        PsWipInfoDTO psWipInfoDTO1 = new PsWipInfoDTO();
        psWipInfoDTO1.setAttribute1("prod1");
        psWipInfoDTO1.setCurrProcessCode("1");
        psWipInfoDTO1.setOnhandQty(10L);
        PsWipInfoDTO psWipInfoDTO2 = new PsWipInfoDTO();
        psWipInfoDTO2.setAttribute1("prod1");
        psWipInfoDTO2.setCurrProcessCode("S");
        psWipInfoDTO2.setOnhandQty(20L);
        PsWipInfoDTO psWipInfoDTO3 = new PsWipInfoDTO();
        psWipInfoDTO3.setAttribute1("prod1");
        psWipInfoDTO3.setCurrProcessCode("3");
        psWipInfoDTO3.setOnhandQty(30L);
        PsWipInfoDTO psWipInfoDTO4 = new PsWipInfoDTO();
        psWipInfoDTO4.setAttribute1("prod1");
        psWipInfoDTO4.setCurrProcessCode("2");
        psWipInfoDTO4.setOnhandQty(40L);
        PsWipInfoDTO psWipInfoDTO5 = new PsWipInfoDTO();
        psWipInfoDTO5.setAttribute1("prod1");
        psWipInfoDTO5.setCurrProcessCode("T");
        psWipInfoDTO5.setOnhandQty(50L);
        PsWipInfoDTO psWipInfoDTO6 = new PsWipInfoDTO();
        psWipInfoDTO6.setAttribute1("prod2");
        psWipInfoDTO6.setCurrProcessCode("S");
        psWipInfoDTO6.setOnhandQty(60L);
        PsWipInfoDTO psWipInfoDTO7 = new PsWipInfoDTO();
        psWipInfoDTO7.setAttribute1("prod2");
        psWipInfoDTO7.setCurrProcessCode("2");
        psWipInfoDTO7.setOnhandQty(70L);
        PsWipInfoDTO psWipInfoDTO8 = new PsWipInfoDTO();
        psWipInfoDTO8.setAttribute1("prod2");
        psWipInfoDTO8.setCurrProcessCode("4");
        psWipInfoDTO8.setOnhandQty(80L);
        PsWipInfoDTO psWipInfoDTO9 = new PsWipInfoDTO();
        psWipInfoDTO9.setAttribute1("prod2");
        psWipInfoDTO9.setCurrProcessCode("5");
        psWipInfoDTO9.setOnhandQty(90L);
        psWipInfoDTOList.add(psWipInfoDTO1);
        psWipInfoDTOList.add(psWipInfoDTO2);
        psWipInfoDTOList.add(psWipInfoDTO3);
        psWipInfoDTOList.add(psWipInfoDTO4);
        psWipInfoDTOList.add(psWipInfoDTO5);
        psWipInfoDTOList.add(psWipInfoDTO6);
        psWipInfoDTOList.add(psWipInfoDTO7);
        psWipInfoDTOList.add(psWipInfoDTO8);
        psWipInfoDTOList.add(psWipInfoDTO9);
        PowerMockito.when(psWipInfoService.countSnQtyGroupByProdAndProcess(anyList())).thenReturn(psWipInfoDTOList);

        List<BBomHeaderDTO> bBomHeaderDTOList = new ArrayList<>();
        BBomHeaderDTO bBomHeaderDTO1 = new BBomHeaderDTO();
        bBomHeaderDTO1.setProductCode("itemNo1");
        bBomHeaderDTO1.setVerNo("verNo1");
        BBomHeaderDTO bBomHeaderDTO2 = new BBomHeaderDTO();
        bBomHeaderDTO2.setProductCode("itemNo2");
        bBomHeaderDTO2.setVerNo("verNo2");
        BBomHeaderDTO bBomHeaderDTO3 = new BBomHeaderDTO();
        bBomHeaderDTO3.setProductCode("itemNo3");
        bBomHeaderDTO3.setVerNo("verNo3");
        bBomHeaderDTOList.add(bBomHeaderDTO1);
        bBomHeaderDTOList.add(bBomHeaderDTO2);
        bBomHeaderDTOList.add(bBomHeaderDTO3);
        PowerMockito.when(BasicsettingRemoteService.getBBomHeaderByProductCodeSet(anySet())).thenReturn(bBomHeaderDTOList);

        List<ProdPlanStock> entryList = new ArrayList<>();
        ProdPlanStock prodPlanStock1 = new ProdPlanStock();
        prodPlanStock1.setProdPlanId("prod1");
        prodPlanStock1.setInboundQty(22);
        prodPlanStock1.setSubmitQty(33);
        entryList.add(prodPlanStock1);
        ProdPlanStock prodPlanStock2 = new ProdPlanStock();
        prodPlanStock2.setProdPlanId("prod2");
        prodPlanStock2.setInboundQty(44);
        prodPlanStock2.setSubmitQty(55);
        entryList.add(prodPlanStock2);
        PowerMockito.when(warehousehmEntryDetailService.getByPlanIds(anyList(), any())).thenReturn(entryList);
        doThrow(new RuntimeException("异常")).when(emailUtils).sendMail(anyString(), anyString(), anyString(), anyString(),anyString());
        doThrow(new RuntimeException("异常")).when(LOG).error(anyString(), (Throwable) any());
        service.handleScheduledTask(conciseDailyDTO, 1);

        String[] processArr = null;
        Map<String, Integer> processToSeqMap = new HashMap<>();
        SysLookupTypesDTO sysEntity = null;
        Whitebox.invokeMethod(service, "findFirstAndLastProcess", processArr,processToSeqMap,
                sysEntity);

        List<TaskDailyStatHead> headList = new LinkedList<>();
        TaskDailyStatHead taskDailyStatHead1 = new TaskDailyStatHead();
        taskDailyStatHead1.setStatisticDate(new Date());
        headList.add(taskDailyStatHead1);
        List<TaskDailyStatDetail> detailList = new LinkedList<>();
        List<SysLookupTypesDTO> sysLookupTypesList = new LinkedList<>();
        Whitebox.invokeMethod(service, "transferToMapList", headList,detailList,
                sysLookupTypesList);
        Assert.assertEquals("prod1",  prodPlanStock1.getProdPlanId());

    }

    @Test
    public void conciseDailyQueryTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        try {
            service.conciseDailyQuery(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PARAM_IS_NULL.equals(e.getExMsgId()));
        }
        try {
            service.conciseDailyQuery(conciseDailyDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CONCISE_DAILY_QUERY_PAGE_OR_ROW_ILLEGAL.equals(e.getExMsgId()));
        }
        conciseDailyDTO.setPage(1);
        conciseDailyDTO.setRow(100);
        try {
            service.conciseDailyQuery(conciseDailyDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.DATE_RANGE_IS_EMPTY.equals(e.getExMsgId()));
        }
        conciseDailyDTO.setStartDate(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        conciseDailyDTO.setEndDate(sdf.parse("2023-11-11 11:11:11"));
        try {
            service.conciseDailyQuery(conciseDailyDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.DATE_RANGE_TOO_LONG.equals(e.getExMsgId()));
        }
        conciseDailyDTO.setEndDate(sdf.parse("2023-01-11 11:11:11"));

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysDto1 = new SysLookupTypesDTO();
        sysDto1.setLookupMeaning("1$S$3");
        sysDto1.setAttribute3("SMTA");
        sysDto1.setRemark("贴片A面+刮胶A面+点胶A面");
        sysDto1.setAttribute1("Y");
        sysDto1.setDescriptionChinV("dasdasd");
        SysLookupTypesDTO sysDto2 = new SysLookupTypesDTO();
        sysDto2.setLookupMeaning("2$T$4");
        sysDto2.setAttribute3("SMTB");
        sysDto2.setRemark("贴片B面+刮胶B面+点胶B面");
        sysDto2.setAttribute1("Y");
        sysDto2.setDescriptionChinV("dasdasd");
        sysLookupTypesDTOList.add(sysDto1);
        sysLookupTypesDTOList.add(sysDto2);
        SysLookupTypesDTO sysDto3 = new SysLookupTypesDTO();
        sysDto3.setAttribute1("email");
        sysDto3.setLookupMeaning("dasdasd");
        sysDto3.setDescriptionChinV("dasdasd");
        sysLookupTypesDTOList.add(sysDto3);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);
        PowerMockito.when( BasicsettingRemoteService.getLineNameByCodeList(Mockito.anyList())).thenReturn(new HashMap<>());


        // 实时查询
        conciseDailyDTO.setRealTimeQueryFlag(true);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        Page<PsTask> pageTask = new Page<>();
        pageTask.setRows(psTaskList);
        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(any())).thenReturn(pageTask);
        service.conciseDailyQuery(conciseDailyDTO);

        // 不实时查
        conciseDailyDTO.setRealTimeQueryFlag(false);
        List<TaskDailyStatHead> headList = new ArrayList<>();
        TaskDailyStatHead taskDailyStatHead = new TaskDailyStatHead();
        taskDailyStatHead.setHeadId("head1");
        taskDailyStatHead.setStatisticDate(new Date());
        headList.add(taskDailyStatHead);
        TaskDailyStatHead taskDailyStatHead1 = new TaskDailyStatHead();
        taskDailyStatHead1.setHeadId("head2");
        taskDailyStatHead1.setStatisticDate(new Date());
        headList.add(taskDailyStatHead1);
        TaskDailyStatHead taskDailyStatHead2 = new TaskDailyStatHead();
        taskDailyStatHead2.setHeadId("head3");
        taskDailyStatHead2.setStatisticDate(new Date());
        headList.add(taskDailyStatHead2);
        PowerMockito.when(taskDailyStatHeadService.selectByConciseDailyDTO(any())).thenReturn(headList);
        List<TaskDailyStatDetail> detailList = new ArrayList<>();
        TaskDailyStatDetail taskDailyStatDetail1 = new TaskDailyStatDetail();
        taskDailyStatDetail1.setHeadId("head1");
        taskDailyStatDetail1.setStatItem("SMTA");
        taskDailyStatDetail1.setYesterdayOnhandQty(1L);
        taskDailyStatDetail1.setTurnIntoQty(2L);
        taskDailyStatDetail1.setTurnOutQty(3L);
        taskDailyStatDetail1.setOnhandQty(4L);
        TaskDailyStatDetail taskDailyStatDetail2 = new TaskDailyStatDetail();
        taskDailyStatDetail2.setHeadId("head1");
        taskDailyStatDetail2.setStatItem("SMTB");
        taskDailyStatDetail2.setYesterdayOnhandQty(5L);
        taskDailyStatDetail2.setTurnIntoQty(6L);
        taskDailyStatDetail2.setTurnOutQty(7L);
        taskDailyStatDetail2.setOnhandQty(8L);
        TaskDailyStatDetail taskDailyStatDetail3 = new TaskDailyStatDetail();
        taskDailyStatDetail3.setHeadId("head2");
        taskDailyStatDetail3.setStatItem("SMTB");
        taskDailyStatDetail3.setYesterdayOnhandQty(11L);
        taskDailyStatDetail3.setTurnIntoQty(22L);
        taskDailyStatDetail3.setTurnOutQty(33L);
        taskDailyStatDetail3.setOnhandQty(44L);
        detailList.add(taskDailyStatDetail1);
        detailList.add(taskDailyStatDetail2);
        detailList.add(taskDailyStatDetail3);
        PowerMockito.when(taskDailyStatDetailService.selectByHeadIds(anyList())).thenReturn(detailList);
        service.conciseDailyQuery(conciseDailyDTO);

        Map<String, Object> map = new HashMap<>();
        map.put("prodplanId", "7654321");
        List<Map<String, Object>> list = new ArrayList<>();
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        BProdBomHeaderDTO headerDTO1 = new BProdBomHeaderDTO();
        BProdBomHeaderDTO headerDTO2 = new BProdBomHeaderDTO();
        headerDTO2.setProdplanId("1234567");
        headerDTO1.setProdplanId("7654321");
        headerDTO1.setProductCode("7654321");
        headerDTO2.setProductCode("7654321");
        mBomList.add(headerDTO1);
        mBomList.add(headerDTO2);
        list.add(map);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(anyList())).thenReturn(mBomList);
        Whitebox.invokeMethod(service, "setMBomProductCode", list);
    }

    @Test
    public void conciseDailyExportTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        HttpServletResponse response = null;
        try {
            service.conciseDailyExport(response, conciseDailyDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }
        List<String> propList = new ArrayList<>();
        propList.add("SMTA");
        conciseDailyDTO.setExportPropList(propList);
        conciseDailyDTO.setStartDate(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        conciseDailyDTO.setEndDate(sdf.parse("2023-01-11 11:11:11"));

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> sysLookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO sysDto1 = new SysLookupTypesDTO();
        sysDto1.setLookupMeaning("1$S$3");
        sysDto1.setAttribute3("SMTA");
        sysDto1.setRemark("贴片A面+刮胶A面+点胶A面");
        sysDto1.setDescriptionChinV("SMTA");
        sysDto1.setAttribute1("Y");
        SysLookupTypesDTO sysDto2 = new SysLookupTypesDTO();
        sysDto2.setLookupMeaning("2$T$4");
        sysDto2.setAttribute3("SMTB");
        sysDto2.setDescriptionChinV("SMTB");
        sysDto2.setRemark("贴片B面+刮胶B面+点胶B面");
        sysDto2.setAttribute1("Y");
        sysDto2.setAttribute4("Y");
        sysLookupTypesDTOList.add(sysDto1);
        sysLookupTypesDTOList.add(sysDto2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesDTOList);



        // 实时查询
        conciseDailyDTO.setRealTimeQueryFlag(true);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        List<PsTask> psTaskList = new ArrayList<>();
        Page<PsTask> pageTask = new Page<>();
        pageTask.setRows(psTaskList);
        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(any())).thenReturn(pageTask);
        try {
            service.conciseDailyExport(response, conciseDailyDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_QTY_LESS, e.getMessage());
        }


        // 不实时查
        conciseDailyDTO.setRealTimeQueryFlag(false);
        List<TaskDailyStatHead> headList = new ArrayList<>();
        TaskDailyStatHead taskDailyStatHead = new TaskDailyStatHead();
        taskDailyStatHead.setHeadId("head1");
        headList.add(taskDailyStatHead);
        TaskDailyStatHead taskDailyStatHead1 = new TaskDailyStatHead();
        taskDailyStatHead1.setHeadId("head2");
        headList.add(taskDailyStatHead1);
        TaskDailyStatHead taskDailyStatHead2 = new TaskDailyStatHead();
        taskDailyStatHead2.setHeadId("head3");
        headList.add(taskDailyStatHead2);
        PowerMockito.when(taskDailyStatHeadService.selectByConciseDailyDTO(any())).thenReturn(null);
        List<TaskDailyStatDetail> detailList = new ArrayList<>();
        TaskDailyStatDetail taskDailyStatDetail1 = new TaskDailyStatDetail();
        taskDailyStatDetail1.setHeadId("head1");
        taskDailyStatDetail1.setStatItem("SMTA");
        taskDailyStatDetail1.setYesterdayOnhandQty(1L);
        taskDailyStatDetail1.setTurnIntoQty(2L);
        taskDailyStatDetail1.setTurnOutQty(3L);
        taskDailyStatDetail1.setOnhandQty(4L);
        TaskDailyStatDetail taskDailyStatDetail2 = new TaskDailyStatDetail();
        taskDailyStatDetail2.setHeadId("head1");
        taskDailyStatDetail2.setStatItem("SMTB");
        taskDailyStatDetail2.setYesterdayOnhandQty(5L);
        taskDailyStatDetail2.setTurnIntoQty(6L);
        taskDailyStatDetail2.setTurnOutQty(7L);
        taskDailyStatDetail2.setOnhandQty(8L);
        TaskDailyStatDetail taskDailyStatDetail3 = new TaskDailyStatDetail();
        taskDailyStatDetail3.setHeadId("head2");
        taskDailyStatDetail3.setStatItem("SMTB");
        taskDailyStatDetail3.setYesterdayOnhandQty(11L);
        taskDailyStatDetail3.setTurnIntoQty(22L);
        taskDailyStatDetail3.setTurnOutQty(33L);
        taskDailyStatDetail3.setOnhandQty(44L);
        detailList.add(taskDailyStatDetail1);
        detailList.add(taskDailyStatDetail2);
        detailList.add(taskDailyStatDetail3);
        PowerMockito.when(taskDailyStatDetailService.selectByHeadIds(anyList())).thenReturn(detailList);
        try {
            service.conciseDailyExport(response, conciseDailyDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_QTY_LESS, e.getMessage());
        }
    }


    @Test
    public void saveToDataBase() {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        List<TaskDailyStatHead> headList = new LinkedList<>();
        TaskDailyStatHead a1 = new TaskDailyStatHead();
        headList.add(a1);
        conciseDailyDTO.setStatHeadList(headList);

        List<TaskDailyStatDetail> statDetailList = new LinkedList<>();
        TaskDailyStatDetail b1 = new TaskDailyStatDetail();
        statDetailList.add(b1);
        conciseDailyDTO.setStatDetailList(statDetailList);
        service.saveToDataBase(conciseDailyDTO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getOuterFlag() {
        service.getOuterFlag(null);
        service.getOuterFlag(Pair.of("1", ""));
        service.getOuterFlag(Pair.of("1", "外协 -> 入库"));
        service.getOuterFlag(Pair.of("1", "DIP -> 入库"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void countByConciseDailyDTO() throws Exception {
        PowerMockito.when(taskDailyStatHeadRepository.countByConciseDailyDTO(new ConciseDailyDTO()))
                .thenReturn(1);
        taskDailyStatHeadServiceImpl.countByConciseDailyDTO(new ConciseDailyDTO());
        PowerMockito.when(taskDailyStatHeadService.countByConciseDailyDTO(new ConciseDailyDTO()))
                .thenReturn(1);
        Assert.assertNotNull(service.countByConciseDailyDTO(new ConciseDailyDTO()));
    }

    /* Started by AICoder, pid:l5b41t7687pb92614d7d0b55f0b0d04e1b736144 */
    @Test
    public void reelTimeExportTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        String[] headArr = new String[]{"test"};
        List<SysLookupTypesDTO> sysLookupList = new ArrayList<>();
        String[] propArr = new String[]{"test"};
        BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        List<PsTask> psTaskList = new ArrayList<>();
        Page<PsTask> pageTask = new Page<>();
        pageTask.setRows(psTaskList);
        psTaskList.add(new PsTask());
        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(any())).thenReturn(pageTask);
        List<PsTask> psTaskList1 = new ArrayList<>();
        pageTask.setRows(psTaskList1);
        PowerMockito.when(PlanscheduleRemoteService.pageSelectForConciseDailyReelTime(any())).thenReturn(pageTask);
        Whitebox.invokeMethod(service, "reelTimeExport", conciseDailyDTO, headArr, sysLookupList, propArr, bigExcelProcesser);
        Assert.assertTrue(conciseDailyDTO != null);
    }

    @Test
    public void multiThreadExportCoreTest() throws Exception {
        ConciseDailyDTO conciseDailyDTO = new ConciseDailyDTO();
        List<SysLookupTypesDTO> sysLookupList = new ArrayList<>();
        Map<String, String> httpRequestHeader = new HashMap<>();
        List<PsTask> psTaskList = new ArrayList<>();
        List<Future<List<Map<String, Object>>>> futureList = new ArrayList<>();
        Whitebox.invokeMethod(service, "multiThreadExportCore", conciseDailyDTO, sysLookupList, httpRequestHeader, psTaskList, futureList);
        Assert.assertTrue(conciseDailyDTO != null);
    }
    /* Ended by AICoder, pid:l5b41t7687pb92614d7d0b55f0b0d04e1b736144 */
}
