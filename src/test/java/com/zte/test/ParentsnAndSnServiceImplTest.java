package com.zte.test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.lang.Snowflake;
import com.zte.domain.model.ParentsnAndSn;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.zte.application.WipScanHistoryService;
import com.zte.application.impl.ParentsnAndSnServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ParentsnAndSnRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
public class ParentsnAndSnServiceImplTest {
	
	@InjectMocks
	private ParentsnAndSnServiceImpl parentsnAndSnServiceImpl;
	
	@Mock
	private ParentsnAndSnRepository parentsnAndSnRepository;
	
	@Mock
	private WipScanHistoryService wipScanHistoryService;

	@Mock
	private Snowflake snowflake;
	
	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
    }
	
	
	@Test
	@PrepareForTest({BasicsettingRemoteService.class})
    public void syncParentSnAndSn() throws Exception {
		PowerMockito.when(snowflake.nextId()).thenReturn(1L);

		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
		sysLookupTypesDTO.setLookupMeaning("2018-11-02 06:40:32");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.eq(Constant.SYS_LOOK_7000), Mockito.eq(Constant.SYS_LOOK_7000001))).thenReturn(sysLookupTypesDTO);
		Date endDate = new Date();
		PowerMockito.when(wipScanHistoryService.getMaxCreateDate()).thenReturn(endDate);
		PowerMockito.when(parentsnAndSnRepository.getMaxId()).thenReturn(null);
		List<WipScanHistory> statDataList = new ArrayList<WipScanHistory>();
		WipScanHistory wipScanHistory = new WipScanHistory();
		wipScanHistory.setAttribute1("7777666");
		wipScanHistory.setParentSn("777766600001");
		wipScanHistory.setSn("777766600001");
		statDataList.add(wipScanHistory);
		
		PowerMockito.when(wipScanHistoryService.statPsnAndSn(Mockito.any(),Mockito.any(),Mockito.anyInt(),Mockito.anyInt())).thenReturn(statDataList);
		
		PowerMockito.when(parentsnAndSnRepository.getExistMix(Mockito.any())).thenReturn(new ArrayList<String>());
		
		PowerMockito.when(parentsnAndSnRepository.batchInsert(Mockito.any())).thenReturn(1);
		
		int count = parentsnAndSnServiceImpl.sync("", "");

		Assert.assertEquals(1, count);

		count = parentsnAndSnServiceImpl.sync("2022-02-09 00:40:00", "2022-02-09 01:40:00");
		
		Assert.assertEquals(1, count);
	}
	@Test
	public void getParentsn() throws Exception {
		String res1 = parentsnAndSnServiceImpl.getParentsn("111");
		Assert.assertNull( res1);
		ParentsnAndSn parentsnAndSn = new ParentsnAndSn();
		parentsnAndSn.setParentSn("222");
		PowerMockito.when(parentsnAndSnRepository.getBySn(Mockito.any())).thenReturn(parentsnAndSn);
		String res2 = parentsnAndSnServiceImpl.getParentsn("111");
		Assert.assertEquals("222", res2);
	}

}
