package com.zte.test;


import com.zte.application.PmRepairDetailService;
import com.zte.application.PmRepairInfoService;
import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PmRepairRcvService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.BProdBomServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.MaintenanceMaterialLineRepository;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.vo.PmRepairRcvVo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.interfaces.dto.PmRepairRcvDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.PmRepairRcvDetailParamDTO;
import com.zte.interfaces.dto.PmRepairVirtualSnDTO;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.any;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class, RetCode.class, BasicsettingRemoteService.class,
        MicroServiceDiscoveryInvoker.class})
public class PmRepairRcvServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PmRepairRcvServiceImpl pmRepairRcvServiceImpl;
    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;
    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;
    @Mock
    private ServiceData serviceData;
    @Mock
    private MicroServiceRestUtil microServiceRestUtil;
    @Mock
    private PmRepairInfoService pmRepairInfoService;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PmRepairRcvService pmRepairRcvService;

    @Mock
    private PmRepairDetailService pmRepairDetailService;

    @Mock
    private MaintenanceMaterialLineRepository maintenanceMaterialLineRepository;

    @Mock
    private BProdBomServiceImpl bProdBomService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);

    }

    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Test
    public void validAndInitReturnInfo() throws Exception {
        PmRepairRcvDetailParamDTO pmRepairRcvDetailParamDTO = new PmRepairRcvDetailParamDTO();
        pmRepairRcvDetailParamDTO.setProcessMap(new HashMap<>());
        pmRepairRcvDetailParamDTO.setSns(new StringBuilder());
        List<PmRepairRcvDetailDTO> record = new ArrayList<>();
        List<PsScanHistory> psScanHistorys = new ArrayList<>();
        psScanHistorys.add(new PsScanHistory());
        pmRepairRcvDetailParamDTO.setPsScanHistorys(psScanHistorys);
        record.add(new PmRepairRcvDetailDTO(){{setSn("2");setReceptionDetailId("detailId");}});
        record.add(new PmRepairRcvDetailDTO(){{setSn("2");setFromStation( Constant.DEVICE_PRODUCTION_MAINTENANCE);setReceptionDetailId("detailId");}});
        pmRepairRcvDetailParamDTO.setRecord(record);
        List<PsWipInfo> returnPsWipInfos = new ArrayList<>();
        returnPsWipInfos.add(new PsWipInfo());
        pmRepairRcvDetailParamDTO.setPsWipInfos(returnPsWipInfos);
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfos.add(psWipInfo);
        psWipInfo.setSn("2");
        psWipInfo.setParentSn("777888900001");
        PowerMockito.when(psWipInfoService.getListByBatchSn(Mockito.any())).thenReturn(psWipInfos);
        List<PmRepairRcvDetail> pmRepairRcvDetails = new ArrayList<>();
        pmRepairRcvDetails.add(new PmRepairRcvDetail(){{setSn("2");setReceptionDetailId("detailId");}});
        PowerMockito.when(pmRepairRcvDetailService.getRelOneList(Mockito.any())).thenReturn(pmRepairRcvDetails);
        PowerMockito.when(pmRepairInfoService.creatPsScanHistory(any(),any())).thenReturn(new PsScanHistory());
        List<PmRepairRcvVo> pmRepairRcvVoTempList = new ArrayList<>();

        PmRepairRcvVo pm1 = new PmRepairRcvVo();
        pm1.setSn("2");
        pm1.setReceptionDetailId("detailId");
        pmRepairRcvVoTempList.add(pm1);
        PowerMockito.when(pmRepairRcvRepository.getRepairPcvAndDeatilByDeatilIdList(any())).thenReturn(pmRepairRcvVoTempList);
        pmRepairRcvServiceImpl.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);
        List<PmRepairRcvDetail> tempList = new ArrayList<>();
        tempList.add(new PmRepairRcvDetail(){{setSn("2");}});
        PowerMockito.when(pmRepairRcvDetailService.getRelOneList(any())).thenReturn(tempList);
        pmRepairRcvServiceImpl.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);

        PmRepairRcvDetailDTO a1 = new PmRepairRcvDetailDTO();
        a1.setSn("234");
        record.add(a1);
        try {
            pmRepairRcvServiceImpl.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL, e.getMessage());
        }

        PsWipInfo b1 = new PsWipInfo();
        b1.setSn("234");
        psWipInfos.add(b1);
        pmRepairRcvDetailParamDTO.setValieTip(MpConstant.REPAIR_VALID_TIP_SC);
        pmRepairRcvServiceImpl.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);

        pm1.setReceptionDetailId("2");
        try {
            pmRepairRcvServiceImpl.validAndInitReturnInfo(pmRepairRcvDetailParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REPAIR_RECORD_NOT_FOUND, e.getMessage());
        }
    }
    @Test
    public void postList() throws Exception {
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo ps = new PsWipInfo();
        ps.setAttribute1("7774013");
        list.add(ps);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.any())).thenReturn(list);
        PowerMockito.when(bProdBomService.getBProdBomHeader(Mockito.any())).thenReturn(null);
        Assert.assertNotNull(pmRepairRcvServiceImpl.postList(new PmRepairRcvDTO(){{
            setChildCard(true);
        }}));
        List<BProdBomHeaderDTO> bomHeaderDTOS = new ArrayList<>();
        bomHeaderDTOS.add(new BProdBomHeaderDTO(){{
            setProdplanId("12345678");
        }});
        PowerMockito.when(bProdBomService.getBProdBomHeader(Mockito.any())).thenReturn(bomHeaderDTOS);
        Assert.assertNotNull(pmRepairRcvServiceImpl.postList(new PmRepairRcvDTO(){{
            setChildCard(true);
        }}));
    }

    @Test
    public void exportPmRepairRcv() throws Exception {
        List<PmRepairRcvVo> list = new LinkedList<>();
        PmRepairRcvVo pmRepairRcvVo = new PmRepairRcvVo();
        pmRepairRcvVo.setStatus("11");
        list.add(pmRepairRcvVo);
        PmRepairRcvDTO dto = new PmRepairRcvDTO();
        PowerMockito.when(pmRepairRcvRepository.exportPmRepairRcv(any())).thenReturn(list);
        Assert.assertNotNull(pmRepairRcvServiceImpl.exportPmRepairRcv(dto, "52"));
    }

    @Test
    public void getRepairSnList1() throws Exception {
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(0);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[\"730638800003\",\"730638800004\"],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result);
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setProdplanId("7306388");
        dto.setQty(2);
        List<String> repairSnList = pmRepairRcvServiceImpl.getRepairSnList(dto);
        Assert.assertTrue(repairSnList.size() == 2);
    }

    @Test
    public void getRepairSnList2() throws Exception{
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(0);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result1 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"生成的条码序列号超过99999\"},\"bo\":[],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result1);
        String errMsg = "";
        try {
            PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
            dto.setProdplanId("7306388");
            dto.setQty(2);
            Assert.assertNotNull(pmRepairRcvServiceImpl.getRepairSnList(dto));
        } catch (Exception e) {
            errMsg = e.getMessage();
        }
        Assert.assertTrue("生成的条码序列号超过99999".equals(errMsg));
    }

    @Test
    public void getRepairSnList3() throws Exception{
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(1);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"生成的条码序列号超过99999\"},\"bo\":[],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result);
        String errMsg = "";
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setProdplanId("7306388");
        dto.setQty(2);
        try {
            List<String> repairSnList = pmRepairRcvServiceImpl.getRepairSnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_EXIST_SN_RECORD, e.getMessage());
        }
    }

    @Test
    public void getRepairSnList4() throws Exception{
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(0);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result1 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"生成的条码序列号超过99999\"},\"bo\":[],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result1);
        String errMsg = "";
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setProdplanId("7306388");

        try {
            List<String> repairSnList = pmRepairRcvServiceImpl.getRepairSnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_QTY_LESS, e.getMessage());
        }
    }

    @Test
    public void getRepairSnList5() throws Exception{
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(0);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result1 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"生成的条码序列号超过99999\"},\"bo\":[],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result1);
        String errMsg = "";PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setQty(2);
        try {
            List<String> repairSnList = pmRepairRcvServiceImpl.getRepairSnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_IS_NULL, e.getMessage());
        }
    }

    @Test
    public void getRepairSnList6() throws Exception{
        PowerMockito.when(pmRepairRcvDetailRepository.getProdplanSnRecord("7306388", "1")).thenReturn(0);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        String result1 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"生成的条码序列号超过99999\"},\"bo\":[],\"other\":{}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(MicroServiceNameEum.PLANSCHEDULE),
                Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject())).thenReturn(result1);
        String errMsg = "";
        PmRepairVirtualSnDTO dto = new PmRepairVirtualSnDTO();
        dto.setProdplanId("7306388");
        dto.setQty(1500);
        try {
            List<String> repairSnList = pmRepairRcvServiceImpl.getRepairSnList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_QTY_MORE, e.getMessage());
        }
    }

    @Test
    public void snScrap() throws Exception {
        List<PmRepairDetailDTO> repairDetailDTOList = new ArrayList<>();
        PmRepairDetailDTO dto = new PmRepairDetailDTO();
        dto.setRepairProductMstype("qq");
        repairDetailDTOList.add(dto);
        List<String> list = new ArrayList<>();
        list.add("12313");
        PowerMockito.when(pmRepairDetailService.getRepairDetailBySnList(Mockito.any())).thenReturn(repairDetailDTOList);
        pmRepairRcvServiceImpl.snScrap(list);
        Assert.assertEquals("qq",dto.getRepairProductMstype());

    }

}
