package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.mds.MdsAccessTokenDTO;
import com.zte.interfaces.dto.mds.MdsControlQueryDTO;
import com.zte.interfaces.dto.mds.MdsControlResponseDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2022-12-07 17:00
 */
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, JSON.class,
        BasicsettingRemoteService.class, MESHttpHelper.class, HttpHeaderUtil.class})
public class MdsRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private MdsRemoteService mdsRemoteService;
    @Mock
    private RedisTemplate<String,Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;

    @Test
    public void queryFirmwareVersionBatch() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class,BasicsettingRemoteService.class);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("123");
        List<String> batchNoList = new ArrayList<>();
        mdsRemoteService.queryFirmwareVersionBatch(batchNoList);
        batchNoList.add("2");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732001)).thenReturn(null);
        try {
            mdsRemoteService.queryFirmwareVersionBatch(batchNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("");
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732001)).thenReturn(sysLookupTypesDTO);
        try {
            mdsRemoteService.queryFirmwareVersionBatch(batchNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("http://10.5.208.215/zte-scm-wms-sstock/stockLock/queryStockOccupyInfo");
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732,Constant.LOOKUP_TYPE_6732001)).thenReturn(sysLookupTypesDTO);

        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        when(valueOps.get(Mockito.anyString()))
                .thenReturn("");

        List<SysLookupTypesDTO> lookupValues = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        dto1.setLookupMeaning("1");
        dto1.setLookupCode(new BigDecimal("6732001"));
        dto1.setDescriptionChinV("在库");
        lookupValues.add(dto1);

        SysLookupTypesDTO dto2 = new SysLookupTypesDTO();
        dto2.setLookupMeaning("1");
        dto2.setLookupCode(new BigDecimal("6732002"));
        dto2.setDescriptionChinV("在库");
        lookupValues.add(dto2);

        SysLookupTypesDTO dto3 = new SysLookupTypesDTO();
        dto3.setLookupMeaning("1");
        dto3.setLookupCode(new BigDecimal("6732003"));
        dto3.setDescriptionChinV("在库");
        lookupValues.add(dto3);

        SysLookupTypesDTO dto4 = new SysLookupTypesDTO();
        dto4.setLookupMeaning("1");
        dto4.setLookupCode(new BigDecimal("6732004"));
        dto4.setDescriptionChinV("在库");
        lookupValues.add(dto4);

        SysLookupTypesDTO dto5 = new SysLookupTypesDTO();
        dto5.setLookupMeaning("1");
        dto5.setLookupCode(new BigDecimal("6732005"));
        dto5.setDescriptionChinV("在库");
        lookupValues.add(dto5);
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732)).thenReturn(lookupValues);
        try {
            mdsRemoteService.queryFirmwareVersionBatch(batchNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        MdsAccessTokenDTO mdsAccessTokenDTO = new MdsAccessTokenDTO();
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  anyMap(),anyString(),anyString())).thenReturn(JSON.toJSONString(mdsAccessTokenDTO));
        try {
            mdsRemoteService.queryFirmwareVersionBatch(batchNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  anyMap(),anyString(),anyString())).thenReturn("{\"access_token\":\"1\"}");
        mdsRemoteService.queryFirmwareVersionBatch(batchNoList);

    }
    @Test
    public void getMdsEqpMaintain() throws Exception {
        PowerMockito.mockStatic(JSON.class, HttpRemoteUtil.class, BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(new SysLookupTypesDTO());
        Assert.assertThrows(MesBusinessException.class, () -> mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        when(valueOps.get(any())).thenReturn("1");
        when(HttpHeaderUtil.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("1");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        Assert.assertNotNull(mdsRemoteService.getTokenTimeOut(100000));
        Assert.assertNotNull(mdsRemoteService.getTokenTimeOut(1));
    }

    @Test
    public void moduleSMTOpReceiveOrPassAutoMoveForIMES() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(JSON.class,BasicsettingRemoteService.class);
        String url = "11";
        MdsControlQueryDTO queryDTO = new MdsControlQueryDTO();
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("123");

        MdsControlResponseDTO mdsControlResponseDTO = new MdsControlResponseDTO();
        mdsControlResponseDTO.setTotalResult(true);
        when(JacksonJsonConverUtil.jsonToBean("123", MdsControlResponseDTO.class))
                .thenReturn(mdsControlResponseDTO)
        ;
        Assert.assertNull(mdsRemoteService.moduleSMTOpReceiveOrPassAutoMoveForIMES(url, queryDTO));
    }
}
