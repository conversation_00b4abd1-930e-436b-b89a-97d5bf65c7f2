package com.zte.test;

import com.zte.application.InvTransInfoService;
import com.zte.application.impl.InvTransInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.InvTransInfo;
import com.zte.domain.model.InvTransInfoRepository;
import com.zte.domain.model.TransNumInfoRepository;
import com.zte.domain.model.WarehouseEntryDetailRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

public class InvTransInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private InvTransInfoServiceImpl invTransInfoServiceImpl;
    @Mock
    private TransNumInfoRepository transNumInfoRepository;
    @Mock
    private InvTransInfoRepository invTransInfoRepository;
    @Mock
    private InvTransInfoService invTransInfoService;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @PrepareForTest({CommonUtils.class})
    public void getList() throws Exception {
        InvTransInfo invTransInfo = new InvTransInfo();
        invTransInfo.setContainerID("CT552019061100000021");
        invTransInfo.setItemNum("122096830268XYZ");
        invTransInfo.setEnvPro(null);
        invTransInfo.setTaskNum(null);
        invTransInfo.setSnList(null);

        List<InvTransInfo> invTransList = new ArrayList<>();
        invTransList.add(invTransInfo);

        PowerMockito.when(invTransInfoRepository.getList(Mockito.anyString(),Mockito.anyList())).thenReturn(invTransList);

        List<String> snList = new ArrayList<>();
        snList.add("777776800027");

        PowerMockito.when(warehouseEntryDetailRepository.getSnList(Mockito.anyString(),Mockito.anyString())).thenReturn(snList);

        String billNo = "B551910140002";
        List<String> containerIds =new ArrayList<>();
        containerIds.add("CT552019061100000021");

        Assert.assertNotNull(invTransInfoServiceImpl.getList(billNo,containerIds));
    }

}
