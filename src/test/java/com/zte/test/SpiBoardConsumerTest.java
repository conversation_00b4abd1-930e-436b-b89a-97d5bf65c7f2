package com.zte.test;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.PsScanHistoryService;
import com.zte.application.kafka.consumer.SpiBoardConsumer;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.EmEqpSpiBoardDTO;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.ArrayList;
import java.util.List;

public class SpiBoardConsumerTest extends PowerBaseTestCase {
	@InjectMocks
	private SpiBoardConsumer spiBoardConsumer;
	@Mock
	private FactoryConfig factoryConfig;
	@Mock
	private PsScanHistoryService psScanHistoryService;

	@Test
	public void consume() {
		spiBoardConsumer.consume(null);


		ReflectionTestUtils.setField(spiBoardConsumer, "factoryId", "53");
		MsgData<List<EmEqpSpiBoardDTO>> msgData = new MsgData<>();
		spiBoardConsumer.consume(JSONObject.toJSONString(msgData));

		msgData.setFactoryId("53");
		spiBoardConsumer.consume(JSONObject.toJSONString(msgData));

		List<EmEqpSpiBoardDTO> list = new ArrayList<>();
		EmEqpSpiBoardDTO spiBoardDTO = new EmEqpSpiBoardDTO();
		list.add(spiBoardDTO);
		msgData.setData(list);
		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn(null);
		spiBoardConsumer.consume(JSONObject.toJSONString(msgData));

		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("DB_SZ");
		spiBoardConsumer.consume(JSONObject.toJSONString(msgData));String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);

	}
}
