package com.zte.test;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.mds.MdsEqpMaintainDTO;
import com.zte.interfaces.dto.mds.MdsNameBoardDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.COMPLETED;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2022-12-07 17:00
 */
@PrepareForTest({ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class,
        BasicsettingRemoteService.class, HttpHeaderUtil.class, HttpRemoteUtil.class, MESHttpHelper.class})
public class MdsRemoteService1Test extends PowerBaseTestCase {
    @InjectMocks
    private MdsRemoteService mdsRemoteService;
    @Mock
    private RedisTemplate<String,Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;

    @Test
    public void getMdsEqpMaintain1() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class, ServiceDataBuilderUtil.class);
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(new SysLookupTypesDTO());
        Assert.assertThrows(MesBusinessException.class, () -> mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_2020, Constant.LOOKUP_TYPE_2020010))
                .thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");}});
        when(redisTemplate.opsForValue()).thenReturn(valueOps);
        when(valueOps.get(any())).thenReturn("1");
        when(HttpHeaderUtil.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID)).thenReturn("1");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(null)).thenReturn(JSON.toJSONString(new ArrayList()));
        Assert.assertNotNull(mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(null)).thenReturn(JSON.toJSONString(Lists.newArrayList(
                        new MdsEqpMaintainDTO(){{setLineName("1");}},
                        new MdsEqpMaintainDTO(){{setLineName("2"); setResult(COMPLETED);}}
                )));
        Assert.assertNotNull(mdsRemoteService.getMdsEqpMaintain(Lists.newArrayList("1")));
    }

    @Test
    public void getNameBoard() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class, ServiceDataBuilderUtil.class);
        List<MdsNameBoardDTO> res = mdsRemoteService.getNameBoard(null);
        Assert.assertEquals(0, res.size());

        List<String> snList = new ArrayList<>();
        snList.add("sn1");
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732, Constant.LOOKUP_TYPE_6732012))
                .thenReturn(null);
        try {
            res = mdsRemoteService.getNameBoard(snList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732, Constant.LOOKUP_TYPE_6732012))
                .thenReturn(sysLookupTypesDTO);
        try {
            res = mdsRemoteService.getNameBoard(snList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        sysLookupTypesDTO.setLookupMeaning("url");
        when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_6732, Constant.LOOKUP_TYPE_6732012))
                .thenReturn(sysLookupTypesDTO);
        MdsRemoteService spy = PowerMockito.spy(mdsRemoteService);
        PowerMockito.doReturn("Token").when(spy, "getAccessToken");
        ServiceData serviceData = new ServiceData();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  anyMap(),anyString(),anyString()))
                .thenReturn(com.alibaba.fastjson.JSON.toJSONString(serviceData));
        Map<String, String> header = new HashMap<>();
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        try {
            res = spy.getNameBoard(snList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }

        List<MdsNameBoardDTO> tempList = new ArrayList<>();
        serviceData.setBo(tempList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  anyMap(),anyString(),anyString()))
                .thenReturn(com.alibaba.fastjson.JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSON.toJSONString(new ArrayList()));
        res = spy.getNameBoard(snList);
        Assert.assertEquals(0, res.size());

        MdsNameBoardDTO mdsNameBoardDTO = new MdsNameBoardDTO();
        tempList.add(mdsNameBoardDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),  anyMap(),anyString(),anyString()))
                .thenReturn(com.alibaba.fastjson.JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn(JSON.toJSONString(tempList));
        res = spy.getNameBoard(snList);
        Assert.assertEquals(1, res.size());
    }
}
