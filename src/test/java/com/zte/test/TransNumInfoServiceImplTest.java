package com.zte.test;

import com.zte.application.InvTransInfoService;
import com.zte.application.impl.InvTransInfoServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.application.impl.TransNumInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.doReturn;

@PrepareForTest({CommonUtils.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class})
public class TransNumInfoServiceImplTest  extends PowerBaseTestCase {
    @InjectMocks
    private TransNumInfoServiceImpl service;
    @Mock
    private TransNumInfoRepository transNumInfoRepository;
    @Mock
    private InvTransInfoRepository invTransInfoRepository;
    @Mock
    private InvTransInfoService invTransInfoService;
    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;
    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;
    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(CommonUtils.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class);

    }

    @Test
    public void getList() throws Exception {

        TransNumInfoServiceImpl service =  PowerMockito.spy(new TransNumInfoServiceImpl());

        List<String> snList = new ArrayList<>();
        snList.add("21312414");

        List<InvTransInfo> invTransList = new ArrayList<>();
        InvTransInfo invTransInfo = new InvTransInfo();
        invTransInfo.setTaskNum("241251");
        invTransInfo.setItemNum("211251");
        invTransInfo.setEnvPro("3141");
        invTransInfo.setContainerID("21125151");
        invTransInfo.setSnList(snList);
        invTransList.add(invTransInfo);


        TransNumInfo transNumInfo = new TransNumInfo();
        transNumInfo.setTransNum("B551910140002");
        transNumInfo.setInvType("1");
        transNumInfo.setInvTransList(invTransList);

        List<TransNumInfo> transNumList = new ArrayList();
        transNumList.add(transNumInfo);

        doReturn(transNumList).when(service).getWarehouseDetailEntry(Mockito.anyList());
        doReturn(transNumList).when(service).getTransferApplyEntry(Mockito.anyList());

        List<String> containerIds = new ArrayList<>();
        containerIds.add("CT552019061100000021");

        PhysicalInfo physicalInfo =new PhysicalInfo();
        physicalInfo.setInfCode("BM001");
        physicalInfo.setInfDesc("标模入库接口");
        physicalInfo.setMsgID("TIMMS_202010241456083642007");
        physicalInfo.setSenderSys("TIMMS");
        physicalInfo.setTargetSys("IMES");
        physicalInfo.setContainerIds(containerIds);

        Assert.assertNotNull(service.getList(physicalInfo));
    }

    @Test
    public void getTransferApplyEntry() throws Exception {
        List<String> lpnList = new ArrayList<>();
        lpnList.add("13124242");

        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType(new BigDecimal("1116"));
        sysLookupValuesDTO.setLookupCode(new BigDecimal("11160001"));
        sysLookupValuesDTO.setLookupMeaning("XCB_J");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookupValuesList(Mockito.anyObject())).thenReturn(sysLookupValuesDTOList);

        List<WarehouseEntryInfoDetailDTO> list = new ArrayList<>();
        WarehouseEntryInfoDetailDTO warehouseEntryInfoDetailDTO = new WarehouseEntryInfoDetailDTO();
        warehouseEntryInfoDetailDTO.setApplicationNo("12312414");
        warehouseEntryInfoDetailDTO.setLpn("1124124");
        warehouseEntryInfoDetailDTO.setSn("1231241");
        warehouseEntryInfoDetailDTO.setItemCode("1212512");
        list.add(warehouseEntryInfoDetailDTO);

        PowerMockito.when(ProductionDeliveryRemoteService.queryApplyOrderDetails(Mockito.anyObject())).thenReturn(list);

        Assert.assertNotNull(service.getTransferApplyEntry(lpnList));
    }

    @Test
    public void getWarehouseDetailEntry() throws Exception {
        List<String> lpnList = new ArrayList<>();
        lpnList.add("13124242");

        List<WarehouseEntryInfoDTO> list = new ArrayList<>();
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setBillNo("23124151");
        warehouseEntryInfoDTO.setLpn("1231241");
        warehouseEntryInfoDTO.setSn("1232151514");
        warehouseEntryInfoDTO.setTaskNo("12312151");
        warehouseEntryInfoDTO.setIsLead("124215141");
        warehouseEntryInfoDTO.setItemNo("4151");
        list.add(warehouseEntryInfoDTO);

        PowerMockito.when(warehouseEntryInfoRepository.selectWarehouseEntryDetailInfo(Mockito.anyObject())).thenReturn(list);

        Assert.assertNotNull(service.getWarehouseDetailEntry(lpnList));
    }

}
