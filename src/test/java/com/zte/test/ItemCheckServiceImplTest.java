package com.zte.test;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.impl.ItemCheckServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.model.CFLine;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

/**
 * @Author: 10307315
 * @Date: 2022/6/14 上午10:43
 */
@PrepareForTest({RedisHelper.class, PlanscheduleRemoteService.class, BasicsettingRemoteService.class,AsyncExportFileCommonService.class,
        MESHttpHelper.class, HttpRemoteService.class, HttpRemoteUtil.class, RedisLock.class,EasyExcelFactory.class,
        MicroServiceDiscoveryInvoker.class, CommonUtils.class, MicroServiceRestUtil.class,
        JacksonJsonConverUtil.class,ConstantInterface.class})
public class ItemCheckServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ItemCheckServiceImpl service;

    @Mock
    BSmtBomHeaderService bSmtBomHeaderService;

    @Mock
    ItemCheckinfoHeadService itemCheckinfoHeadService;

    @Mock
    StItemBarcodeService stItemBarcodeService;

    @Mock
    PkCodeInfoService pkCodeInfoService;

    @Mock
    BSmtBomDetailService bSmtBomDetailService;

    @Mock
    TaskMaterialIssueSeqService taskMaterialIssueSeqService;

    @Mock
    ItemCheckinfoDetailService itemCheckinfoDetailService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    IMESLogService imesLogService;

    @Mock
    AsyncExportFileCommonService asyncExportFileCommonService;

    @Mock
    DatawbRemoteService datawbRemoteService;

    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(EasyExcelFactory.class,RedisHelper.class, RedisLock.class,
                MicroServiceDiscoveryInvoker.class, CommonUtils.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class);
    }

    @Test
    public void handleAfterInputProdIdTest() throws Exception {
        ItemCheckDTO dto = new ItemCheckDTO();
        try {
            // 分支批次为空
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PLAN_NO_IS_NULL.equals(e.getExMsgId()));
    }
        // 设置批次后
        dto.setProdPlanId("7111222");
        PowerMockito.mockStatic(RedisHelper.class);
        // 获取锁mock为失败
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(false);
        try {
            //分支 获取锁资源失败
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GET_REDIS_LOCK.equals(e.getExMsgId()));
        }
        // 设置锁资源mock为成功。使得逻辑继续
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        List<PsWorkOrderSmt> psWorkOrderSmtList = new ArrayList<>();
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        // 从psWorkSmt表获取为空列表，
        PowerMockito.when(PlanscheduleRemoteService.getPsWorkSmtListByProdPlanId(anyString())).thenReturn(psWorkOrderSmtList);
        try {
            // 分支 没有对应的smt指令信息
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.NOT_HAVE_SMT_WORK_ORDER.equals(e.getExMsgId()));
        }
        // mock  对应的smt指令信息
        PsWorkOrderSmt smtA = new PsWorkOrderSmt();
        PsWorkOrderSmt smtB = new PsWorkOrderSmt();
        PsWorkOrderSmt dipA = new PsWorkOrderSmt();
        PsWorkOrderSmt dipB = new PsWorkOrderSmt();
        smtA.setWorkOrderNo("7111222-SMT-A5801");
        smtA.setCfgHeaderId("");
        smtB.setWorkOrderNo("7111222-SMT-B5801");
        smtB.setCfgHeaderId("");
        dipA.setWorkOrderNo("7111222-DIP-A5801");
        dipA.setCfgHeaderId("test03");
        dipB.setWorkOrderNo("");
        dipB.setCfgHeaderId("test04");
        psWorkOrderSmtList.add(smtA);
        psWorkOrderSmtList.add(smtB);
        psWorkOrderSmtList.add(dipA);
        psWorkOrderSmtList.add(dipB);
        try {
            // 分支 上料表没导入A面
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SMT_A_BOM_NOT_ACTIVE.equals(e.getExMsgId()));
        }
        // mock设置A面
        smtA.setCfgHeaderId("test01");
        try {
            // 分支 上料表没导入B面
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SMT_B_BOM_NOT_ACTIVE.equals(e.getExMsgId()));
        }
        // mock设置B面
        smtB.setCfgHeaderId("test02");
        // mock根据SMT指令表中头表id获取上料头表信息。
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader bomHeadA = new BSmtBomHeader();
        BSmtBomHeader bomHeadB = new BSmtBomHeader();
        bomHeadA.setCraftSection("SMT-A1");
        bomHeadA.setCfgHeaderId("test01");
        bomHeadB.setCraftSection("SMT-B");
        bomHeadB.setCfgHeaderId("test02");
        bSmtBomHeaderList.add(bomHeadA);
        bSmtBomHeaderList.add(bomHeadB);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomHeaderByIdList(anyList())).thenReturn(bSmtBomHeaderList);
        PowerMockito.when(bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(anyList())).thenReturn(bSmtBomHeaderList);
        try {
            //分支  计划划服务psWorkOrderSmt表记录的A面头表ID在生产管理bSmtBomHead表中不是有效数据。
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SMT_A_BOM_NOT_ACTIVE.equals(e.getExMsgId()));
        }
        // 设置A面为有效
        bomHeadA.setCraftSection("SMT-A");
        bomHeadB.setCraftSection("SMT-B1");
        try {
            //分支  计划划服务psWorkOrderSmt表记录的B面头表ID在生产管理bSmtBomHead表中不是有效数据。
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SMT_B_BOM_NOT_ACTIVE.equals(e.getExMsgId()));
        }
        // 设置B面为有效
        bomHeadB.setCraftSection("SMT-B");



        // mock无清点记录
        PowerMockito.when(itemCheckinfoHeadService.getEntityByProdPlanId(anyString())).thenReturn(null);
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtABomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail3 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail3 = new BSmtBomDetail();
        // 准备上料详细表数据，两个A面，两个B面，A和B有一个同站位(2结尾的同站位2-22-2)
        // 设置头id
        bSmtABomDetail1.setCfgHeaderId("test01");
        bSmtABomDetail2.setCfgHeaderId("test01");
        bSmtABomDetail3.setCfgHeaderId("test01");
        bSmtBBomDetail1.setCfgHeaderId("test02");
        bSmtBBomDetail2.setCfgHeaderId("test02");
        bSmtBBomDetail3.setCfgHeaderId("test02");
        // 设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");
        // 设置物料代码。 itemNo3有3个站位使用, itemNo2有2个不同面但同站位使用，itemNo1无序， itemNo2无序(在seq表不存在)， itemNo3有序
        bSmtABomDetail1.setItemCode("itemNo1");
        bSmtABomDetail2.setItemCode("itemNo2");
        bSmtABomDetail3.setItemCode("itemNo3");
        bSmtBBomDetail1.setItemCode("itemNo3");
        bSmtBBomDetail2.setItemCode("itemNo2");
        bSmtBBomDetail3.setItemCode("itemNo3");

        bSmtBomDetailList.add(bSmtABomDetail1);
        bSmtBomDetailList.add(bSmtABomDetail2);
        bSmtBomDetailList.add(bSmtABomDetail3);
        bSmtBomDetailList.add(bSmtBBomDetail1);
        bSmtBomDetailList.add(bSmtBBomDetail2);
        bSmtBomDetailList.add(bSmtBBomDetail3);
        // mock 上料表详细数据
        PowerMockito.when(bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo(anySet(), anyObject())).thenReturn(bSmtBomDetailList);
        // 不同线体情况数据，站位2-22-2不合并
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line2");


        // mock pkCode注册信息
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo3 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo4 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo5 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo6 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo7 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo8 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo9 = new PkCodeInfo();
        // reel1和reel2同物料代码itemNo1，但是数量不同，验证无序情况下，是否会取大于阈值
        pkCodeInfo1.setPkCode("pkCode1");
        pkCodeInfo1.setItemCode("itemNo1");
        pkCodeInfo1.setItemQty(new BigDecimal("600"));
        pkCodeInfo1.setSupplerCode("sup");
        pkCodeInfo1.setSysLotCode("uuid");

        pkCodeInfo2.setPkCode("pkCode2");
        pkCodeInfo2.setItemCode("itemNo1");
        pkCodeInfo2.setItemQty(new BigDecimal("400"));
        pkCodeInfo2.setSupplerCode("sup1");
        pkCodeInfo2.setSysLotCode("uuid1");
        //itemNo2只放一块，为了验证，同线体是否会合并站位
        pkCodeInfo3.setPkCode("pkCode3");
        pkCodeInfo3.setItemCode("itemNo2");
        pkCodeInfo3.setItemQty(new BigDecimal("560"));
        pkCodeInfo3.setSupplerCode("sup");
        pkCodeInfo3.setSysLotCode("uuid");
        // itemNo3放6块， 其中和最高优先级物料，不同供应商一块，不同uuid一块，剩余4块
        pkCodeInfo4.setPkCode("pkCode4");
        pkCodeInfo4.setItemCode("itemNo3");
        pkCodeInfo4.setItemQty(new BigDecimal("560"));
        pkCodeInfo4.setSupplerCode("sup1");
        pkCodeInfo4.setSysLotCode("uuid");

        pkCodeInfo5.setPkCode("pkCode5");
        pkCodeInfo5.setItemCode("itemNo3");
        pkCodeInfo5.setItemQty(new BigDecimal("550"));
        pkCodeInfo5.setSupplerCode("sup");
        pkCodeInfo5.setSysLotCode("uuid1");

        pkCodeInfo6.setPkCode("pkCode6");
        pkCodeInfo6.setItemCode("itemNo3");
        pkCodeInfo6.setItemQty(new BigDecimal("600"));
        pkCodeInfo6.setSupplerCode("sup");
        pkCodeInfo6.setSysLotCode("uuid");

        pkCodeInfo7.setPkCode("pkCode7");
        pkCodeInfo7.setItemCode("itemNo3");
        pkCodeInfo7.setItemQty(new BigDecimal("510"));
        pkCodeInfo7.setSupplerCode("sup");
        pkCodeInfo7.setSysLotCode("uuid");

        pkCodeInfo8.setPkCode("pkCode8");
        pkCodeInfo8.setItemCode("itemNo3");
        pkCodeInfo8.setItemQty(new BigDecimal("450"));
        pkCodeInfo8.setSupplerCode("sup");
        pkCodeInfo8.setSysLotCode("uuid");

        pkCodeInfo9.setPkCode("pkCode9");
        pkCodeInfo9.setItemCode("itemNo3");
        pkCodeInfo9.setItemQty(new BigDecimal("480"));
        pkCodeInfo9.setSupplerCode("sup");
        pkCodeInfo9.setSysLotCode("uuid");

        pkCodeInfoList.add(pkCodeInfo1);
        pkCodeInfoList.add(pkCodeInfo2);
        pkCodeInfoList.add(pkCodeInfo3);
        pkCodeInfoList.add(pkCodeInfo4);
        pkCodeInfoList.add(pkCodeInfo5);
        pkCodeInfoList.add(pkCodeInfo6);
        pkCodeInfoList.add(pkCodeInfo7);
        pkCodeInfoList.add(pkCodeInfo8);
        pkCodeInfoList.add(pkCodeInfo9);
        PowerMockito.when(pkCodeInfoService.getPkCodeInfoListByProdPlanId(anyString(), anyList())).thenReturn(pkCodeInfoList);

        // mock 有序，
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList  = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto1 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto2 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto3 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto4 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto5 = new TaskMaterialIssueSeqEntityDTO();
        // itemNo1 有记录，但是无序
        dto1.setItemNo("itemNo1");
        dto1.setSupplerCode("sup");
        dto1.setSysLotCode("uuid");
        dto1.setSeq(null);
        dto2.setItemNo("itemNo1");
        dto2.setSupplerCode("sup1");
        dto2.setSysLotCode("uuid1");
        dto2.setSeq(null);

        // itemNo2无记录，itemNo3有序
        dto3.setItemNo("itemNo3");
        dto3.setSupplerCode("sup");
        dto3.setSysLotCode("uuid");
        dto3.setSeq(0);
        dto4.setItemNo("itemNo3");
        dto4.setSupplerCode("sup1");
        dto4.setSysLotCode("uuid");
        dto4.setSeq(1);
        dto5.setItemNo("itemNo3");
        dto5.setSupplerCode("sup");
        dto5.setSysLotCode("uuid1");
        dto5.setSeq(2);
        seqEntityList.add(dto1);
        seqEntityList.add(dto2);
        seqEntityList.add(dto3);
        seqEntityList.add(dto4);
        seqEntityList.add(dto5);
        PowerMockito.when(taskMaterialIssueSeqService.getEntityListByItemSet(any(),anySet())).thenReturn(seqEntityList);
        // mock 数据字典
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1003002001"));
        // 预分配开启
        sysLookupTypesDTO1.setLookupMeaning("Y");
        sysLookupTypesDTO2.setLookupCode(new BigDecimal("1003002002"));
        sysLookupTypesDTO2.setLookupMeaning("501");
        sysLookupTypesDTO3.setLookupCode(new BigDecimal("1003002003"));
        sysLookupTypesDTO3.setLookupMeaning("Y");
        sysLookupTypesList.add(sysLookupTypesDTO1);
        sysLookupTypesList.add(sysLookupTypesDTO2);
        sysLookupTypesList.add(sysLookupTypesDTO3);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesList);

        // 未做齐套 将上料信息详表的物料改为 pkCodeList没有的，就会报错。
        bSmtABomDetail1.setItemCode("itemNo4");
        //设置回itemNo1
        bSmtABomDetail1.setItemCode("itemNo1");
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        List<PsTask> psTaskList = new ArrayList<>();
        // psTaskList 为空
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        try {
            // 分支 如果写数据时，任务号查询无结果，报错
            service.handleAfterInputProdId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PS_TASK_NULL.equals(e.getExMsgId()));
        }
        // psTaskList设置
        PsTask psTask = new PsTask();
        psTask.setTaskNo("testNo");
        psTaskList.add(psTask);
        // 分支不同线体，且预分配的完整分支。
        try {
            service.handleAfterInputProdId(dto);
        }catch (Exception e){

        }

        // 不是预分配
        sysLookupTypesDTO1.setLookupMeaning("N");
        // 分支不同线体，不是预分配
        service.handleAfterInputProdId(dto);

        // 同线体情况数据，站位2-22-2合并
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line1");
        sysLookupTypesDTO1.setLookupMeaning("Y");
        // 分支同线体，预分配开启
        try {
            service.handleAfterInputProdId(dto);
        }catch (Exception e){

        }
        sysLookupTypesDTO1.setLookupMeaning("N");
        // 分支同线体，预分配关闭
        service.handleAfterInputProdId(dto);
    }

    /**
    * 有清点记录的测试
    *@Author: 10307315陈俊熙
    *@date 2022/6/15 下午3:38
    *@param
    *@return void
    */
    @Test
    public void handleAfterInputProdIdTest1 () throws Exception {
        ItemCheckDTO dto = new ItemCheckDTO();
        dto.setProdPlanId("7111222");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(),anyString(),anyInt())).thenReturn(true);
        List<PsWorkOrderSmt> psWorkOrderSmtList = new ArrayList<>();
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsWorkSmtListByProdPlanId(anyString())).thenReturn(psWorkOrderSmtList);
        PsWorkOrderSmt smtA = new PsWorkOrderSmt();
        PsWorkOrderSmt smtB = new PsWorkOrderSmt();
        PsWorkOrderSmt dipA = new PsWorkOrderSmt();
        PsWorkOrderSmt dipB = new PsWorkOrderSmt();
        smtA.setWorkOrderNo("7111222-SMT-A5801");
        smtA.setCfgHeaderId("test01");
        smtB.setWorkOrderNo("7111222-SMT-B5801");
        smtB.setCfgHeaderId("test02");
        dipA.setWorkOrderNo("7111222-DIP-A5801");
        dipA.setCfgHeaderId("test03");
        dipB.setWorkOrderNo("");
        dipB.setCfgHeaderId("test04");
        psWorkOrderSmtList.add(smtA);
        psWorkOrderSmtList.add(smtB);
        psWorkOrderSmtList.add(dipA);
        psWorkOrderSmtList.add(dipB);
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader bomHeadA = new BSmtBomHeader();
        BSmtBomHeader bomHeadB = new BSmtBomHeader();
        bomHeadA.setCraftSection("SMT-A");
        bomHeadA.setCfgHeaderId("test01");
        bomHeadB.setCraftSection("SMT-B");
        bomHeadB.setCfgHeaderId("test02");
        bSmtBomHeaderList.add(bomHeadA);
        bSmtBomHeaderList.add(bomHeadB);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomHeaderByIdList(anyList())).thenReturn(bSmtBomHeaderList);
        PowerMockito.when(bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(anyList())).thenReturn(bSmtBomHeaderList);
        // 有清点记录
        ItemCheckInfoHead itemCheckInfoHead = new ItemCheckInfoHead();
        itemCheckInfoHead.setCfgHeaderIdA("testOld01");
        itemCheckInfoHead.setCfgHeaderIdB("testOld02");
        PowerMockito.when(itemCheckinfoHeadService.getEntityByProdPlanId(anyString())).thenReturn(itemCheckInfoHead);
        // 清点详情表 mock
        List<ItemCheckInfoDetail> itemCheckDetList = new ArrayList<>();
        ItemCheckInfoDetail detail1 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail2 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail3 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail4 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail5 = new ItemCheckInfoDetail();
        // 1清点完成状态，2 不可清点，其他
        detail1.setPkCode("pkCode1");
        detail1.setItemNo("itemNo1");
        detail1.setCheckStatus("0");
        detail2.setPkCode("pkCode2");
        detail2.setItemNo("itemNo2");
        detail2.setCheckStatus("-1");
        detail3.setPkCode("pkCode3");
        detail3.setItemNo("itemNo2");
        detail3.setCheckStatus("-1");
        detail4.setPkCode("pkCode4");
        detail4.setItemNo("itemNo7");
        detail4.setCheckStatus("-1");
        detail5.setPkCode("pkCode5");
        detail5.setItemNo("itemNo8");
        detail5.setCheckStatus("1");
        itemCheckDetList.add(detail1);
        itemCheckDetList.add(detail2);
        itemCheckDetList.add(detail3);
        itemCheckDetList.add(detail4);
        itemCheckDetList.add(detail5);
        PowerMockito.when(itemCheckinfoDetailService.getListByProdPlanId(anyString())).thenReturn(itemCheckDetList);

        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtABomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail3 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail3 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail4 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail4 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail5 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail6 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail5 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail6 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail7 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail8 = new BSmtBomDetail();
        // 准备上料详细表数据，两个A面，两个B面，A和B有一个同站位(2结尾的同站位2-22-2)
        // 设置头id
        bSmtABomDetail1.setCfgHeaderId("test01");
        bSmtABomDetail2.setCfgHeaderId("test01");
        bSmtABomDetail3.setCfgHeaderId("test01");
        bSmtBBomDetail1.setCfgHeaderId("test02");
        bSmtBBomDetail2.setCfgHeaderId("test02");
        bSmtBBomDetail3.setCfgHeaderId("test02");
        bSmtBBomDetail4.setCfgHeaderId("test02");
        // 设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");
        bSmtBBomDetail4.setLocationNo("4-44-4");
        // 设置物料代码。 itemNo3有3个站位使用, itemNo2有2个不同面但同站位使用，itemNo1无序， itemNo2无序(在seq表不存在)， itemNo3有序
        bSmtABomDetail1.setItemCode("itemNo1");
        bSmtABomDetail2.setItemCode("itemNo2");
        bSmtABomDetail3.setItemCode("itemNo3");
        bSmtBBomDetail1.setItemCode("itemNo3");
        bSmtBBomDetail2.setItemCode("itemNo2");
        bSmtBBomDetail3.setItemCode("itemNo3");
        bSmtBBomDetail4.setItemCode("itemNo6");

        // 设置旧的
        // 设置头id
        bSmtABomDetail4.setCfgHeaderId("testOld01");
        bSmtABomDetail5.setCfgHeaderId("testOld01");
        bSmtABomDetail6.setCfgHeaderId("testOld01");
        bSmtBBomDetail5.setCfgHeaderId("testOld02");
        bSmtBBomDetail6.setCfgHeaderId("testOld02");
        bSmtBBomDetail7.setCfgHeaderId("testOld02");
        bSmtBBomDetail8.setCfgHeaderId("testOld02");
        // 设置站位    新增站位5-55-5，对应物料itemNo5，删除站位4-44-4，物料代码itemNo6
        bSmtABomDetail4.setLocationNo("1-11-1A");
        bSmtABomDetail5.setLocationNo("2-22-2");
        bSmtABomDetail6.setLocationNo("3-33-3A");
        bSmtBBomDetail5.setLocationNo("1-11-1B");
        bSmtBBomDetail6.setLocationNo("2-22-2");
        bSmtBBomDetail7.setLocationNo("3-33-3B");
        bSmtBBomDetail8.setLocationNo("5-55-5");
        // 设置物料代码。 bSmtABomDetail6的 3-33-3A站位更换物料物料从itemNo3到itemNo4，
        bSmtABomDetail4.setItemCode("itemNo1");
        bSmtABomDetail5.setItemCode("itemNo2");
        bSmtABomDetail6.setItemCode("itemNo4");
        bSmtBBomDetail5.setItemCode("itemNo3");
        bSmtBBomDetail6.setItemCode("itemNo2");
        bSmtBBomDetail7.setItemCode("itemNo3");
        bSmtBBomDetail8.setItemCode("itemNo5");
        bSmtBomDetailList.add(bSmtABomDetail1);
        bSmtBomDetailList.add(bSmtABomDetail2);
        bSmtBomDetailList.add(bSmtABomDetail3);
        bSmtBomDetailList.add(bSmtBBomDetail1);
        bSmtBomDetailList.add(bSmtBBomDetail2);
        bSmtBomDetailList.add(bSmtBBomDetail3);
        bSmtBomDetailList.add(bSmtBBomDetail4);
        bSmtBomDetailList.add(bSmtABomDetail4);
        bSmtBomDetailList.add(bSmtABomDetail5);
        bSmtBomDetailList.add(bSmtABomDetail6);
        bSmtBomDetailList.add(bSmtBBomDetail5);
        bSmtBomDetailList.add(bSmtBBomDetail6);
        bSmtBomDetailList.add(bSmtBBomDetail7);
        bSmtBomDetailList.add(bSmtBBomDetail8);
        // mock 上料表详细数据， 上料表发生变更的逻辑。
        PowerMockito.when(bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo(anySet(), anyObject())).thenReturn(bSmtBomDetailList);

        // mock 有序，
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList  = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto1 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto2 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto3 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto4 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto5 = new TaskMaterialIssueSeqEntityDTO();
        // itemNo1 有记录，但是无序
        dto1.setItemNo("itemNo1");
        dto1.setSupplerCode("sup");
        dto1.setSysLotCode("uuid");
        dto1.setSeq(null);
        dto2.setItemNo("itemNo1");
        dto2.setSupplerCode("sup1");
        dto2.setSysLotCode("uuid1");
        dto2.setSeq(null);

        // itemNo2无记录，itemNo3有序
        dto3.setItemNo("itemNo3");
        dto3.setSupplerCode("sup");
        dto3.setSysLotCode("uuid");
        dto3.setSeq(0);
        dto4.setItemNo("itemNo3");
        dto4.setSupplerCode("sup1");
        dto4.setSysLotCode("uuid");
        dto4.setSeq(1);
        dto5.setItemNo("itemNo3");
        dto5.setSupplerCode("sup");
        dto5.setSysLotCode("uuid1");
        dto5.setSeq(2);
        seqEntityList.add(dto1);
        seqEntityList.add(dto2);
        seqEntityList.add(dto3);
        seqEntityList.add(dto4);
        seqEntityList.add(dto5);
        PowerMockito.when(taskMaterialIssueSeqService.getEntityListByItemSet(any(), anySet())).thenReturn(seqEntityList);

        // mock pkCode注册信息
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo3 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo4 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo5 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo6 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo7 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo8 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo9 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo10 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo11 = new PkCodeInfo();
        PkCodeInfo pkCodeInfo12= new PkCodeInfo();
        PkCodeInfo pkCodeInfo13 = new PkCodeInfo();

        // reel1和reel2同物料代码itemNo1，但是数量不同，验证无序情况下，是否会取大于阈值
        pkCodeInfo1.setPkCode("pkCode1");
        pkCodeInfo1.setItemCode("itemNo1");
        pkCodeInfo1.setItemQty(new BigDecimal("600"));
        pkCodeInfo1.setSupplerCode("sup");
        pkCodeInfo1.setSysLotCode("uuid");

        pkCodeInfo2.setPkCode("pkCode2");
        pkCodeInfo2.setItemCode("itemNo1");
        pkCodeInfo2.setItemQty(new BigDecimal("400"));
        pkCodeInfo2.setSupplerCode("sup1");
        pkCodeInfo2.setSysLotCode("uuid1");
        //itemNo2只放一块，为了验证，同线体是否会合并站位
        pkCodeInfo3.setPkCode("pkCode3");
        pkCodeInfo3.setItemCode("itemNo2");
        pkCodeInfo3.setItemQty(new BigDecimal("560"));
        pkCodeInfo3.setSupplerCode("sup");
        pkCodeInfo3.setSysLotCode("uuid");
        // itemNo3放6块， 其中和最高优先级物料，不同供应商一块，不同uuid一块，剩余4块
        pkCodeInfo4.setPkCode("pkCode4");
        pkCodeInfo4.setItemCode("itemNo3");
        pkCodeInfo4.setItemQty(new BigDecimal("560"));
        pkCodeInfo4.setSupplerCode("sup1");
        pkCodeInfo4.setSysLotCode("uuid");

        pkCodeInfo5.setPkCode("pkCode5");
        pkCodeInfo5.setItemCode("itemNo3");
        pkCodeInfo5.setItemQty(new BigDecimal("550"));
        pkCodeInfo5.setSupplerCode("sup");
        pkCodeInfo5.setSysLotCode("uuid1");

        pkCodeInfo6.setPkCode("pkCode6");
        pkCodeInfo6.setItemCode("itemNo3");
        pkCodeInfo6.setItemQty(new BigDecimal("600"));
        pkCodeInfo6.setSupplerCode("sup");
        pkCodeInfo6.setSysLotCode("uuid");

        pkCodeInfo7.setPkCode("pkCode7");
        pkCodeInfo7.setItemCode("itemNo3");
        pkCodeInfo7.setItemQty(new BigDecimal("510"));
        pkCodeInfo7.setSupplerCode("sup");
        pkCodeInfo7.setSysLotCode("uuid");

        pkCodeInfo8.setPkCode("pkCode8");
        pkCodeInfo8.setItemCode("itemNo3");
        pkCodeInfo8.setItemQty(new BigDecimal("450"));
        pkCodeInfo8.setSupplerCode("sup");
        pkCodeInfo8.setSysLotCode("uuid");

        pkCodeInfo9.setPkCode("pkCode9");
        pkCodeInfo9.setItemCode("itemNo3");
        pkCodeInfo9.setItemQty(new BigDecimal("480"));
        pkCodeInfo9.setSupplerCode("sup");
        pkCodeInfo9.setSysLotCode("uuid");

        pkCodeInfo10.setPkCode("pkCode10");
        pkCodeInfo10.setItemCode("itemNo4");
        pkCodeInfo10.setItemQty(new BigDecimal("600"));
        pkCodeInfo10.setSupplerCode("sup");
        pkCodeInfo10.setSysLotCode("uuid");

        pkCodeInfo11.setPkCode("pkCode11");
        pkCodeInfo11.setItemCode("itemNo5");
        pkCodeInfo11.setItemQty(new BigDecimal("600"));
        pkCodeInfo11.setSupplerCode("sup");
        pkCodeInfo11.setSysLotCode("uuid");

        pkCodeInfo12.setPkCode("pkCode12");
        pkCodeInfo12.setItemCode("itemNo6");
        pkCodeInfo12.setItemQty(new BigDecimal("600"));
        pkCodeInfo12.setSupplerCode("sup");
        pkCodeInfo12.setSysLotCode("uuid");

        pkCodeInfo13.setPkCode("pkCode13");
        pkCodeInfo13.setItemCode("itemNo7");
        pkCodeInfo13.setItemQty(new BigDecimal("600"));
        pkCodeInfo13.setSupplerCode("sup");
        pkCodeInfo13.setSysLotCode("uuid");

        pkCodeInfoList.add(pkCodeInfo1);
        pkCodeInfoList.add(pkCodeInfo2);
        pkCodeInfoList.add(pkCodeInfo3);
        pkCodeInfoList.add(pkCodeInfo4);
        pkCodeInfoList.add(pkCodeInfo5);
        pkCodeInfoList.add(pkCodeInfo6);
        pkCodeInfoList.add(pkCodeInfo7);
        pkCodeInfoList.add(pkCodeInfo8);
        pkCodeInfoList.add(pkCodeInfo9);
        pkCodeInfoList.add(pkCodeInfo10);
        pkCodeInfoList.add(pkCodeInfo11);
        pkCodeInfoList.add(pkCodeInfo12);
        pkCodeInfoList.add(pkCodeInfo13);
        PowerMockito.when(pkCodeInfoService.getListByItemNoSetAndProd(anySet(), anyString(), anyList())).thenReturn(pkCodeInfoList);

        // mock 数据字典
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
        SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupCode(new BigDecimal("1003002001"));
        // 预分配开启
        sysLookupTypesDTO1.setLookupMeaning("Y");
        sysLookupTypesDTO2.setLookupCode(new BigDecimal("1003002002"));
        sysLookupTypesDTO2.setLookupMeaning("501");
        sysLookupTypesDTO3.setLookupCode(new BigDecimal("1003002003"));
        sysLookupTypesDTO3.setLookupMeaning("Y");
        sysLookupTypesList.add(sysLookupTypesDTO1);
        sysLookupTypesList.add(sysLookupTypesDTO2);
        sysLookupTypesList.add(sysLookupTypesDTO3);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesList);
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);


        // 不同线体
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line2");
        try {
            service.handleAfterInputProdId(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }



        detail2.setPkCode("pkCode2");
        detail2.setItemNo("itemNo2");
        detail2.setCheckStatus("-1");
        detail3.setPkCode("pkCode3");
        detail3.setItemNo("itemNo2");
        detail3.setCheckStatus("-1");
        detail4.setPkCode("pkCode4");
        detail4.setItemNo("itemNo7");
        detail4.setCheckStatus("-1");
        // 同线体
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line1");
        try {
            service.handleAfterInputProdId(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }


        detail2.setPkCode("pkCode2");
        detail2.setItemNo("itemNo2");
        detail2.setCheckStatus("-1");
        detail3.setPkCode("pkCode3");
        detail3.setItemNo("itemNo2");
        detail3.setCheckStatus("-1");
        detail4.setPkCode("pkCode4");
        detail4.setItemNo("itemNo7");
        detail4.setCheckStatus("-1");
        // mock 上料表详细数据， 上料表没有发生改变的逻辑。
        // 设置站位
        bSmtABomDetail4.setLocationNo("1-11-1A");
        bSmtABomDetail5.setLocationNo("2-22-2");
        bSmtABomDetail6.setLocationNo("3-33-3A");
        bSmtBBomDetail5.setLocationNo("1-11-1B");
        bSmtBBomDetail6.setLocationNo("2-22-2");
        bSmtBBomDetail7.setLocationNo("3-33-3B");
        bSmtBBomDetail8.setLocationNo("4-44-4");
        // 设置物料代码。 itemNo3有3个站位使用, itemNo2有2个不同面但同站位使用，itemNo1无序， itemNo2无序(在seq表不存在)， itemNo3有序
        bSmtABomDetail4.setItemCode("itemNo1");
        bSmtABomDetail5.setItemCode("itemNo2");
        bSmtABomDetail6.setItemCode("itemNo3");
        bSmtBBomDetail5.setItemCode("itemNo3");
        bSmtBBomDetail6.setItemCode("itemNo2");
        bSmtBBomDetail7.setItemCode("itemNo3");
        bSmtBBomDetail8.setItemCode("itemNo6");
        try {
            service.handleAfterInputProdId(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.DATE_RANGE_IS_EMPTY, e.getMessage());
        }

    }

    @Test
    public void handleAfterInputReelIdTest() throws Exception {
        ItemCheckDTO dto = new ItemCheckDTO();
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PLAN_NO_IS_NULL.equals(e.getExMsgId()));
        }
        dto.setProdPlanId("7111333");
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_IS_NULL.equals(e.getExMsgId()));
        }
        dto.setReelId("pkCode1");
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PowerMockito.when(pkCodeInfoService.getList(anyObject())).thenReturn(pkCodeInfoList);
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_NOT_REGISTER_ITEM_CHECK.equals(e.getExMsgId()));
        }
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfoList.add(pkCodeInfo);
        dto.setLfId("1");
        pkCodeInfo.setLfid("2");
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.LFID_NOT_MATCH_REGISTER_INFO.equals(e.getExMsgId()));
        }
        pkCodeInfo.setLfid("1");

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(),any(),anyInt())).thenReturn(false);
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GET_REDIS_LOCK.equals(e.getExMsgId()));
        }
        PowerMockito.when(RedisHelper.setnx(any(),any(),anyInt())).thenReturn(true);

        PowerMockito.when(itemCheckinfoDetailService.getEntityByReelIdAndProdId(any(), any())).thenReturn(null);
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_NOT_PRE_CHECK.equals(e.getExMsgId()));
        }
        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        PowerMockito.when(itemCheckinfoDetailService.getEntityByReelIdAndProdId(any(), any())).thenReturn(itemCheckInfoDetail);
        itemCheckInfoDetail.setCheckStatus("-1");
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_FORBIDDEN_CHECK.equals(e.getExMsgId()));
        }
        itemCheckInfoDetail.setCheckStatus("1");
        try {
            service.handleAfterInputReelId(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_FINISH_CHECK.equals(e.getExMsgId()));
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLocationNo("A1-11-1");
        // 首盘
        itemCheckInfoDetail.setFirstFlag("Y");
        itemCheckInfoDetail.setPkCode("pkCode6");
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        ItemCheckInfoHead headEntity =  new ItemCheckInfoHead();
        headEntity.setCfgHeaderIdA("test01");
        headEntity.setCfgHeaderIdB("test02");
        PowerMockito.when(itemCheckinfoHeadService.getEntityById(any())).thenReturn(headEntity);


        List<CFLine> cfLines = new ArrayList<>();
        CFLine cfLine1 = new CFLine();
        cfLine1.setLineCode("line1");
        cfLine1.setLineName("线体1");
        CFLine cfLine2 = new CFLine();
        cfLine2.setLineCode("line2");
        cfLine2.setLineName("线体2");
        cfLines.add(cfLine1);
        cfLines.add(cfLine2);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(cfLines);


        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(rtJson);

        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(rtJson);
        // mock 上料头表信息
        List<BSmtBomHeader> bSmtBomHeaderList = new ArrayList<>();
        BSmtBomHeader bomHeadA = new BSmtBomHeader();
        BSmtBomHeader bomHeadB = new BSmtBomHeader();
        // 不同线体
        bomHeadA.setCraftSection("SMT-A");
        bomHeadA.setCfgHeaderId("test01");
        bomHeadB.setCraftSection("SMT-B");
        bomHeadB.setCfgHeaderId("test02");
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line2");
        bSmtBomHeaderList.add(bomHeadA);
        bSmtBomHeaderList.add(bomHeadB);
        PowerMockito.when(bSmtBomHeaderService.selectBSmtBomHeaderByIdList(anyList())).thenReturn(bSmtBomHeaderList);
        PowerMockito.when(bSmtBomHeaderService.selectInfosByIdListIgnoreEnabeld(anyList())).thenReturn(bSmtBomHeaderList);
        // mock 打印。
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );

        try {
            ItemCheckDTO resultDto7 = service.handleAfterInputReelId(dto);
            Assert.assertTrue(resultDto7.getUpdateDetailInfo().getPkCode().equals("pkCode6"));
            Assert.assertTrue(resultDto7.getUpdateDetailInfo().getLocationNo().equals("A1-11-1"));
            Assert.assertTrue(resultDto7.getUpdateDetailInfo().getFirstFlag().equals("Y"));
            Assert.assertTrue(resultDto7.getUpdateDetailInfo().getCheckStatus().equals("1"));
            Assert.assertTrue(resultDto7.getUpdateDetailInfo().getLineName().equals("线体1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);
        // 非首盘或未分配
        // 还没弹窗
        dto.setIgnorePriorityFlag(false);
        // mock 无序
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList1  = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto1 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto2 = new TaskMaterialIssueSeqEntityDTO();
        // itemNo1 有记录，但是无序
        dto1.setItemNo("itemNo1");
        dto1.setSupplerCode("sup");
        dto1.setSysLotCode("uuid");
        dto1.setSeq(null);
        dto2.setItemNo("itemNo1");
        dto2.setSupplerCode("sup1");
        dto2.setSysLotCode("uuid1");
        dto2.setSeq(null);
        seqEntityList1.add(dto1);
        seqEntityList1.add(dto2);
        PowerMockito.when(taskMaterialIssueSeqService.getListByItemNo(anyString(), anyString())).thenReturn(seqEntityList1);
        // 非首盘，且分配了，
        itemCheckInfoDetail.setFirstFlag("N");
        itemCheckInfoDetail.setLocationNo("A1-11-11,B2-22-2");



        try {
            ItemCheckDTO resultDto6 = service.handleAfterInputReelId(dto);
            Assert.assertTrue(resultDto6.getUpdateDetailInfo().getPkCode().equals("pkCode6"));
            Assert.assertTrue(resultDto6.getUpdateDetailInfo().getLocationNo().equals("A1-11-11,B2-22-2"));
            Assert.assertTrue(resultDto6.getUpdateDetailInfo().getFirstFlag().equals("N"));
            Assert.assertTrue(resultDto6.getUpdateDetailInfo().getCheckStatus().equals("1"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);
        // 非首盘，且未分配。
        // MOCK 数据字典
        SysLookupTypesDTO lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypesDTO.setLookupMeaning("501");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(lookupTypesDTO);

        // 清点详情表 mock
        List<ItemCheckInfoDetail> itemCheckDetList = new ArrayList<>();
        ItemCheckInfoDetail detail1 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail2 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail3 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail4 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail5 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail6 = new ItemCheckInfoDetail();
        ItemCheckInfoDetail detail7 = new ItemCheckInfoDetail();
        //
        detail1.setPkCode("pkCode1");
        detail1.setItemNo("itemNo1");
        detail1.setCheckStatus("1");
        detail1.setLocationNo("A:1-11-1A");

        detail2.setPkCode("pkCode2");
        detail2.setItemNo("itemNo1");
        detail2.setCheckStatus("1");
        detail2.setLocationNo("A:2-22-2");

        detail3.setPkCode("pkCode3");
        detail3.setItemNo("itemNo1");
        detail3.setCheckStatus("1");
        detail3.setLocationNo("B:1-11-1B");

        //
        detail4.setPkCode("pkCode4");
        detail4.setItemNo("itemNo1");
        detail4.setCheckStatus("1");
        detail4.setLocationNo("B:3-33-3B");
        // 多站位
        detail5.setPkCode("pkCode5");
        detail5.setItemNo("itemNo1");
        detail5.setCheckStatus("1");
        detail5.setLocationNo("[A:1-11-1A,A:2-22-2,A:3-33-3A,B:1-11-1B,B:2-22-2,B:3-33-3B]");

        detail6.setPkCode("pkCode6");
        detail6.setItemNo("itemNo1");
        detail6.setCheckStatus("0");
        detail6.setItemQty(new BigDecimal("600"));
        detail7.setPkCode("pkCode7");
        detail7.setItemNo("itemNo1");
        detail7.setCheckStatus("0");
        detail7.setItemQty(new BigDecimal("550"));
        itemCheckDetList.add(detail1);
        itemCheckDetList.add(detail2);
        itemCheckDetList.add(detail3);
        itemCheckDetList.add(detail4);
        itemCheckDetList.add(detail5);
        itemCheckDetList.add(detail6);
        itemCheckDetList.add(detail7);
        // 5个已清点，待清点2个，pkCode6和pkCode7;
        // 设置本次清点的为pkCode6，数量是600，优先级低于7，且未分配的站位是A:3-33-3A和B:2-22-2，会分配到A:3-33-3A
        itemCheckInfoDetail.setPkCode("pkCode6");
        PowerMockito.when(itemCheckinfoDetailService.getListByItemNoAndProdId(anyString(), anyString())).thenReturn(itemCheckDetList);
        ItemCheckInfoHead itemCheckHead = new ItemCheckInfoHead();
        itemCheckHead.setCfgHeaderIdA("test01");
        itemCheckHead.setCfgHeaderIdB("test02");
        PowerMockito.when(itemCheckinfoHeadService.getEntityByProdPlanId(anyString())).thenReturn(itemCheckHead);
        // 设置reelid注册时的物料代码
        pkCodeInfo.setItemCode("itemNo1");
        // mock 上料表详细数据
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtABomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtABomDetail3 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail1 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail2 = new BSmtBomDetail();
        BSmtBomDetail bSmtBBomDetail3 = new BSmtBomDetail();
        // 准备上料详细表数据，两个A面，两个B面，A和B有一个同站位(2结尾的同站位2-22-2)
        // 设置头id
        bSmtABomDetail1.setCfgHeaderId("test01");
        bSmtABomDetail2.setCfgHeaderId("test01");
        bSmtABomDetail3.setCfgHeaderId("test01");
        bSmtBBomDetail1.setCfgHeaderId("test02");
        bSmtBBomDetail2.setCfgHeaderId("test02");
        bSmtBBomDetail3.setCfgHeaderId("test02");
        // 设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");
        // 设置物料代码。 itemNo3有3个站位使用, itemNo2有2个不同面但同站位使用，itemNo1无序， itemNo2无序(在seq表不存在)， itemNo3有序
        bSmtABomDetail1.setItemCode("itemNo1");
        bSmtABomDetail2.setItemCode("itemNo1");
        bSmtABomDetail3.setItemCode("itemNo1");
        bSmtBBomDetail1.setItemCode("itemNo1");
        bSmtBBomDetail2.setItemCode("itemNo1");
        bSmtBBomDetail3.setItemCode("itemNo1");

        bSmtBomDetailList.add(bSmtABomDetail1);
        bSmtBomDetailList.add(bSmtABomDetail2);
        bSmtBomDetailList.add(bSmtABomDetail3);
        bSmtBomDetailList.add(bSmtBBomDetail1);
        bSmtBomDetailList.add(bSmtBBomDetail2);
        bSmtBomDetailList.add(bSmtBBomDetail3);
        PowerMockito.when(bSmtBomDetailService.selectBSmtBomDetailByIdSetAndItemNo(anySet(), anyObject())).thenReturn(bSmtBomDetailList);

        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);

        } catch (Exception e) {
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);

        // 重新设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");

        // 同线体
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line1");
        detail2.setLocationNo("AB:2-22-2");
        //  本次清点的为pkCode6，数量是600，优先级低于7，且未分配的站位是A:3-33-3A只有一个(因为同线体时2-22-2会合并)，会分配到多站位

        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);

        } catch (Exception e) {
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);
        // 重新设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");

        // 未弹窗 ，且 有序，
        List<TaskMaterialIssueSeqEntityDTO> seqEntityList  = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto3 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto4 = new TaskMaterialIssueSeqEntityDTO();
        TaskMaterialIssueSeqEntityDTO dto5 = new TaskMaterialIssueSeqEntityDTO();
        // itemNo1有序
        dto3.setItemNo("itemNo1");
        dto3.setSupplerCode("sup");
        dto3.setSysLotCode("uuid");
        dto3.setSeq(0);
        dto4.setItemNo("itemNo1");
        dto4.setSupplerCode("sup1");
        dto4.setSysLotCode("uuid");
        dto4.setSeq(1);
        dto5.setItemNo("itemNo1");
        dto5.setSupplerCode("sup");
        dto5.setSysLotCode("uuid1");
        dto5.setSeq(2);
        seqEntityList.add(dto3);
        seqEntityList.add(dto4);
        seqEntityList.add(dto5);
        PowerMockito.when(taskMaterialIssueSeqService.getListByItemNo(anyString(), anyString())).thenReturn(seqEntityList);
        // 让输入的reelId优先级是2。
        pkCodeInfo.setSupplerCode("sup");
        pkCodeInfo.setSysLotCode("uuid1");
        detail6.setSupplerCode("sup");
        detail6.setSysLotCode("uuid1");
        itemCheckInfoDetail.setSupplerCode("sup");
        itemCheckInfoDetail.setSysLotCode("uuid1");
        // 将为清点的pkCode7设置为优先级高的。
        detail7.setSupplerCode("sup1");
        detail7.setSysLotCode("uuid");
        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);

        } catch (Exception e) {
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);

        // 让输入的reelId优先级是0。
        pkCodeInfo.setSupplerCode("sup");
        pkCodeInfo.setSysLotCode("uuid");
        detail6.setSupplerCode("sup");
        detail6.setSysLotCode("uuid");
        itemCheckInfoDetail.setSupplerCode("sup");
        itemCheckInfoDetail.setSysLotCode("uuid");
        // 将为清点的pkCode7设置为优先级0的。
        detail7.setSupplerCode("sup");
        detail7.setSysLotCode("uuid");
        // 同线体
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line1");
        detail2.setLocationNo("AB:2-22-2");

        Map<String, String> mapCodeToName = new HashMap<>();
        mapCodeToName.put("line1", "线体1");
        mapCodeToName.put("line2", "线体2");
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(anyList())).thenReturn(mapCodeToName);
        //  本次清点的为pkCode6，数量是600，优先级低于7，且未分配的站位是A:3-33-3A只有一个(因为同线体时2-22-2会合并)，会分配到多站位
        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);

        } catch (Exception e) {
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);
        // 重新设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");

        // 不同线体
        bomHeadA.setLineCode("line1");
        bomHeadB.setLineCode("line2");
        detail2.setLocationNo("A:2-22-2");
        //  本次清点的为pkCode6，数量是600，优先级低于7，且未分配的站位是A:3-33-3A 和B:2-22-2，会分配到A:3-33-3A
        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);

        } catch (Exception e) {
        }
        itemCheckInfoDetail.setCheckStatus("0");
        itemCheckInfoDetail.setLineName(null);
        itemCheckInfoDetail.setLocationNo(null);
        itemCheckInfoDetail.setFirstFlag(null);
        // 重新设置站位
        bSmtABomDetail1.setLocationNo("1-11-1A");
        bSmtABomDetail2.setLocationNo("2-22-2");
        bSmtABomDetail3.setLocationNo("3-33-3A");
        bSmtBBomDetail1.setLocationNo("1-11-1B");
        bSmtBBomDetail2.setLocationNo("2-22-2");
        bSmtBBomDetail3.setLocationNo("3-33-3B");


        // 设置已弹窗
        dto.setIgnorePriorityFlag(true);
        // 让输入的reelId优先级是2。
        pkCodeInfo.setSupplerCode("sup");
        pkCodeInfo.setSysLotCode("uuid1");
        detail6.setSupplerCode("sup");
        detail6.setSysLotCode("uuid1");
        itemCheckInfoDetail.setSupplerCode("sup");
        itemCheckInfoDetail.setSysLotCode("uuid1");
        // 将为清点的pkCode7设置为优先级高的。
        detail7.setSupplerCode("sup1");
        detail7.setSysLotCode("uuid");

        try {
            ItemCheckDTO resultDto5 = service.handleAfterInputReelId(dto);
        } catch (Exception e) {
        }
    }

    @Test
    public void itemCheckPrintReelIdLabelTest() throws Exception {
        ItemCheckDTO itemCheckDTO = new ItemCheckDTO();
        PowerMockito.when(itemCheckinfoDetailService.getEntityByReelIdAndProdId(any(), any())).thenReturn(null);
        try {
            service.itemCheckPrintReelIdLabel(itemCheckDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_ITEM_CHECK_INFO_NOT_EXIST.equals(e.getExMsgId()));
        }
        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        PowerMockito.when(itemCheckinfoDetailService.getEntityByReelIdAndProdId(any(), any())).thenReturn(itemCheckInfoDetail);
        try {
            service.itemCheckPrintReelIdLabel(itemCheckDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.REEL_ID_ITEM_CHECK_NOT_FINISH.equals(e.getExMsgId()));
        }
    }

    @Test
    public void queryDetailByItemNoAndTaskInfoTest() throws Exception {
        ItemCheckDifferenceDTO dto = new ItemCheckDifferenceDTO();
        try {
            service.itemCheckDifferenceQuery(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PROD_PLAN_ID_AND_TASK_NO_ALL_EMPTY.equals(e.getExMsgId()));
        }
        dto.setTaskNo("test");
        PsTask psTask = new PsTask();
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByTaskNo(anyString())).thenReturn(psTask);
        List<ItemCheckDifferenceDTO> totalRecordList = new ArrayList<>();
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(anyObject(), any(), any())).thenReturn(totalRecordList);
        try {
            service.itemCheckDifferenceQuery(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.RESULT_LIST_EMPTY.equals(e.getExMsgId()));
        }
        dto.setProdPlanId("123");
        dto.setTaskNo(null);
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        totalRecordList.add(itemCheckDifferenceDTO);
        dto.setPage(1);
        dto.setRows(10);
        List<ItemCheckDifferenceDTO> checkFinishList = new ArrayList<>();
        ItemCheckDifferenceDTO itemCheckDifferenceDTO1 = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO1.setItemTotalQty(new BigDecimal("1"));
        itemCheckDifferenceDTO1.setItemTrayQty(1);
        itemCheckDifferenceDTO1.setNeedCount(new BigDecimal("1"));;
        checkFinishList.add(itemCheckDifferenceDTO1);
        ItemCheckDifferenceDTO itemCheckDifferenceDTO2 = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO2.setItemTotalQty(new BigDecimal("2"));
        itemCheckDifferenceDTO2.setItemTrayQty(1);
        checkFinishList.add(itemCheckDifferenceDTO2);
        String result = "";
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(result);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("[]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(itemCheckinfoDetailService.getCheckQtyGroupByItemNo(anyObject())).thenReturn(checkFinishList);
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(anyObject(), any(), any())).thenReturn(checkFinishList);
        service.itemCheckDifferenceQuery(dto);
    }


    @Test
    public void exportExcel() throws Exception{
        PowerMockito.mockStatic(AsyncExportFileCommonService.class);
        HttpServletResponse response = null;
        String prodPlanId = "7111333";
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        List<ItemCheckDifferenceDTO> totalRecordList = new ArrayList<>();
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(anyObject(), any(), any())).thenReturn(totalRecordList);
        totalRecordList.add(itemCheckDifferenceDTO);
        ItemCheckDifferenceDTO dto = new ItemCheckDifferenceDTO();
        dto.setTaskNo("test");
        dto.setPage(1);
        dto.setRows(10);
        List<ItemCheckDifferenceDTO> checkFinishList = new ArrayList<>();
        ItemCheckDifferenceDTO itemCheckDifferenceDTO1 = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO1.setItemTotalQty(new BigDecimal("1"));
        itemCheckDifferenceDTO1.setItemTrayQty(1);
        checkFinishList.add(itemCheckDifferenceDTO1);
        PowerMockito.when(itemCheckinfoDetailService.getCheckQtyGroupByItemNo(anyObject())).thenReturn(checkFinishList);
        PowerMockito.when(itemCheckinfoDetailService.getTotalQtyGroupByItemNo(anyObject(), any(), any())).thenReturn(checkFinishList);

        List<ItemCheckInfoDetail> itemCheckInfoDetails = new ArrayList<>();
        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        itemCheckInfoDetail.setCheckStatus("1");
        PowerMockito.when(itemCheckinfoDetailService.getPageByProdPlanId(any(), any(), any())).thenReturn(itemCheckInfoDetails);
        Assert.assertEquals("7111333", prodPlanId);
//        service.exportExcel(response, prodPlanId);
    }

    @Test
    public void printReelIdLabelTest() throws Exception {
        ItemCheckDTO itemCheckDTO = new ItemCheckDTO();
        itemCheckDTO.setPrintReelId("ZTE20220601000995");
        itemCheckDTO.setProdPlanId("7111333");

        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode(itemCheckDTO.getPrintReelId());
        pkCodeInfo.setItemCode("itemCode1");
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        pkCodeInfoList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);

        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        headerMap.put("x-emp-no","10308742");
        headerMap.put("x-factory-id","56");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(new ArrayList<>());
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(rtJson);

        try {
            service.printReelIdLabel(itemCheckDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
    }

    @Test
    public void itemCheckLfIdExist() throws Exception{
    	//触发单元测试
        ItemCheckDTO itemCheckDTO = new ItemCheckDTO();
        itemCheckDTO.setProdPlanId("8889434");
        itemCheckDTO.setLfId("KL202307260005");
        VReelidInfoDTO dto = new VReelidInfoDTO();
        List<VReelidInfo> listResult = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getReelidInfoList(dto)).thenReturn(listResult);
        try{
            service.itemCheckLfIdExist(itemCheckDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.LFID_NOT_FOUND_IN_PROD, e.getMessage());
        }
        VReelidInfo vReelidInfo = new VReelidInfo();
        vReelidInfo.setSusr1("8889434");
        vReelidInfo.setDropid("KL202307260001");
        listResult.add(vReelidInfo);
        PowerMockito.when(datawbRemoteService.getReelidInfoList(any())).thenReturn(listResult);
        int i = service.itemCheckLfIdExist(itemCheckDTO);
        Assert.assertEquals(1, i);
    }

    @Test
    public void checkReelIdRegistered() throws Exception{
        String pkCode = "ZTE202309260006";
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode(pkCode);
        PowerMockito.when(centerfactoryRemoteService.getPkCodeInfo(any())).thenReturn(pkCodeInfo);
        try{
            service.checkReelIdRegistered(pkCode);
        }catch (Exception e){
            Assert.assertEquals(MessageId.REEL_ID_REGISTERED, e.getMessage());
        }
        pkCodeInfo = null;
        PowerMockito.when(centerfactoryRemoteService.getPkCodeInfo(any())).thenReturn(pkCodeInfo);
        int i = service.checkReelIdRegistered(pkCode);
        Assert.assertEquals(1, i);
    }

    @Test
    public void batchReelIdDataProcess() throws Exception{
        VReelidInfoDTO dto = new VReelidInfoDTO();
        try{
            service.batchReelIdDataProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RECORD_NOT_FOUND_WITH_BARCODE, e.getMessage());
        }
        BatchReelIdDataDTO batchReelIdDataDTO = new BatchReelIdDataDTO();
        PowerMockito.when(centerfactoryRemoteService.batchReelIdDataProcess(any())).thenReturn(batchReelIdDataDTO);
        try{
            service.batchReelIdDataProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RECORD_NOT_FOUND_WITH_BARCODE, e.getMessage());
        }
        batchReelIdDataDTO.setRegisterTotalQtyNum(new BigDecimal("10"));
        batchReelIdDataDTO.setTotalNum(new BigDecimal("10"));
        PowerMockito.when(centerfactoryRemoteService.batchReelIdDataProcess(any())).thenReturn(batchReelIdDataDTO);
        try{
            service.batchReelIdDataProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_HAS_REGISTERED, e.getMessage());
        }

        batchReelIdDataDTO.setTotalNum(new BigDecimal("20"));
        PowerMockito.when(centerfactoryRemoteService.batchReelIdDataProcess(any())).thenReturn(batchReelIdDataDTO);
        service.batchReelIdDataProcess(dto);
    }

    @Test
    public void reelIdRegister() throws Exception{
		//触发单元测试
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        PowerMockito.when(centerfactoryRemoteService.createReelIds(any(), any(), any())).thenReturn(null);
        try {
            service.reelIdRegister(pkCodeInfoDTO, "11");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GENERATE_REELID_IN_THE_FACTORY, e.getMessage());
        }

        List<String> reelidList = new ArrayList<>();
        reelidList.add("ZTE0223A11000001");
        PowerMockito.when(centerfactoryRemoteService.createReelIds(any(), any(), any())).thenReturn(reelidList);
        pkCodeInfoDTO.setSourceBatchCode("220016740561");
        Ztebarcode ztebarcode = new Ztebarcode();
        PowerMockito.when(datawbRemoteService.getZteBarcodeInfo(any())).thenReturn(ztebarcode);
        pkCodeInfoDTO.setItemCode("050502500135");
        String itemName = "打线刀";
        PowerMockito.when(datawbRemoteService.getItemNameByItemNo(anyObject())).thenReturn(itemName);
        PowerMockito.when(centerfactoryRemoteService.centerFactoryInsertPkCodeInfo(anyObject())).thenReturn(null);
        PowerMockito.when(pkCodeInfoService.lateReelIdBindToLocal(anyObject())).thenReturn(1);

        try {
            service.reelIdRegister(pkCodeInfoDTO, "11");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK, e.getMessage());
        }

        PowerMockito.when(datawbRemoteService.getZteBarcodeInfo(any())).thenReturn(null);
        pkCodeInfoDTO.setItemName("ItemName");
        try {
            service.reelIdRegister(pkCodeInfoDTO, "11");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK, e.getMessage());
        }

        pkCodeInfoDTO.setItemCode("");
        try {
            service.reelIdRegister(pkCodeInfoDTO, "11");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK, e.getMessage());
        }
    }

    @Test
    public void printLocationLabel() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
        headerMap.put("x-emp-no","10308742");
        headerMap.put("x-factory-id","56");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        ItemCheckInfoDetail detailInfo = new ItemCheckInfoDetail();
        Whitebox.invokeMethod(service, "printLocationLabel", detailInfo, "");

        try {
            Whitebox.invokeMethod(service, "printLocationLabel", detailInfo, "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void handleAfterReelidReg() throws Exception {
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        try {
            Whitebox.invokeMethod(service, "handleAfterReelidReg", pkCodeInfo, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLAN_NO_IS_NULL, e.getMessage());
        }
        pkCodeInfo.setProductTask("8889434");
        try {
            Whitebox.invokeMethod(service, "handleAfterReelidReg", pkCodeInfo, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REEL_ID_IS_NULL, e.getMessage());
        }

        pkCodeInfo.setPkCode("ZTE202309221008");

        PowerMockito.when(itemCheckinfoHeadService.getEntityByProdPlanId(anyObject())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "handleAfterReelidReg", pkCodeInfo, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INVENTORY_INFORMATION_NOT_FOUND, e.getMessage());
        }

        ItemCheckInfoHead itemCheckInfoHead = new ItemCheckInfoHead();
        itemCheckInfoHead.setId("63fd5e2b-a94f-472c-a79a-8a19e993e5c8");
        PowerMockito.when(itemCheckinfoHeadService.getEntityByProdPlanId(anyObject())).thenReturn(itemCheckInfoHead);
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        try {
            Whitebox.invokeMethod(service, "handleAfterReelidReg", pkCodeInfo, "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
    }
    @Test
    public void checkFirstReelId() throws Exception {
        List<ItemCheckInfoDetail> suitableReelIds = new ArrayList<>();
        int k = 2;
        String reelId = "reelId";
        BigDecimal qty = BigDecimal.TEN;
        boolean strictThreshold = true;

        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTO1.setLookupMeaning("500");
        sysLookupTypesList.add(sysLookupTypesDTO1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(sysLookupTypesList);

        Object checkFirstReelId = Whitebox.invokeMethod(service, "checkFirstReelId", suitableReelIds, k, reelId, qty, strictThreshold);
        Assert.assertEquals(false, checkFirstReelId);


        ItemCheckInfoDetail itemCheckInfoDetail = new ItemCheckInfoDetail();
        suitableReelIds.add(itemCheckInfoDetail);
        itemCheckInfoDetail.setPkCode("reelId");
        Object checkFirstReelId1 = Whitebox.invokeMethod(service, "checkFirstReelId", suitableReelIds, k, reelId, qty, strictThreshold);
        Assert.assertEquals(false, checkFirstReelId1);

        itemCheckInfoDetail.setItemQty(BigDecimal.TEN);
        Object checkFirstReelId2 = Whitebox.invokeMethod(service, "checkFirstReelId", suitableReelIds, k, reelId, qty, strictThreshold);
        Assert.assertEquals(false, checkFirstReelId2);

        itemCheckInfoDetail.setItemQty(BigDecimal.valueOf(501));
        Object checkFirstReelId3 = Whitebox.invokeMethod(service, "checkFirstReelId", suitableReelIds, k, reelId, qty, strictThreshold);
        Assert.assertEquals(true, checkFirstReelId3);
    }


    @Test
    public void validateProdAndTaskNo() throws Exception {
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO.setPkCode("123");
        Assert.assertNull(Whitebox.invokeMethod(service, "validateProdAndTaskNo", itemCheckDifferenceDTO));
    }

    @Test
    public void countExportTotal() throws Exception {
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        try {
            service.countExportTotal(itemCheckDifferenceDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROD_PLAN_ID_AND_TASK_NO_ALL_EMPTY, e.getMessage());
        }
        itemCheckDifferenceDTO.setPkCode("ZTE0001");
        service.countExportTotal(itemCheckDifferenceDTO);
    }

    @Test
    public void queryExportData() throws Exception {
        List<ItemCheckInfoDetailDTO> detailDTOList = new ArrayList () {{
            add(new ItemCheckInfoDetailDTO() {{
                setCheckStatus(Constant.MINUS_ONE);
                setContainerType(Constant.STR_0);
            }});
            add(new ItemCheckInfoDetailDTO() {{
                setCheckStatus(Constant.STR_0);
                setContainerType(Constant.STR_1);
            }});
            add(new ItemCheckInfoDetailDTO() {{
                setCheckStatus(Constant.STR_1);
            }});
            add(new ItemCheckInfoDetailDTO() {{
                setCheckStatus(Constant.STR_2);
            }});
        }};

        PowerMockito.when(itemCheckinfoDetailService.getPageByItemNoAndProdPlanId(any(),any(),any())).thenReturn(detailDTOList);
        Assert.assertNotNull(service.queryExportData(new ItemCheckDifferenceDTO(),1,1));
    }

    @Test
    public void getTaskQty() throws Exception {
        List<PsTask> psTaskList = Whitebox.invokeMethod(service, "getTaskQty", null);
        Assert.assertEquals(0, psTaskList.size());
        try {
            String result = "";
            PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                    .thenReturn(result);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
            PowerMockito.when(json.toString()).thenReturn(null);
            PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(Collections.EMPTY_LIST);
            psTaskList = Whitebox.invokeMethod(service, "getTaskQty", "123");
            Assert.assertEquals(0, psTaskList.size());
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
        try {
            String result = "";
            PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                    .thenReturn(result);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
            String bo = "[{\"taskId\":\"AAAcsbAAFAAAAS6221\",\"empNo\":null,\"taskNo\":\"127201150968ABB\"," +
                    "\"taskNoList\":null,\"contractNo\":null,\"itemNo\":\"127201150968ABB\",\"reworkTaskNo\":null,\"erpModel\":null,\"reworkSource\":null,\"isFinishGood\":null,\"productMode\":null,\"itemName\":\"ZXV10 B860AV2D1PR STBAB\",\"isLead\":\"\",\"taskQty\":20000,\"completeQty\":null,\"operateType\":null,\"taskStatus\":null,\"internalType\":\"ONT\",\"externalType\":\"DHOME\",\"moNo\":null,\"type\":null,\"softwareVersion\":null,\"releaseDate\":\"2022-06-20 15:52:29\",\"plannedFinishDate\":null,\"finalFinishDate\":null,\"sourceSys\":\"STEP\",\"productType\":null,\"remark\":null,\"createBy\":\"system\",\"createDate\":\"2022-06-22 15:14:29\",\"lastUpdatedBy\":\"system\",\"lastUpdatedDate\":\"2023-09-01 14:37:28\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":52,\"entityId\":2,\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"waitInvestyNo\":null,\"onlineNo\":null,\"warehouseNo\":null,\"outhouseNo\":null,\"assemblyRemark\":null,\"attribute5\":null,\"attribute6\":null,\"attribute7\":null,\"attribute8\":null,\"attribute9\":null,\"attribute10\":null,\"prodplanId\":\"8886221\",\"prodplanIdList\":null,\"prodplanNo\":\"127201150968ABB\",\"bomId\":null,\"planId\":null,\"planUuid\":null,\"isParts\":null,\"partsPlanno\":null,\"planSequence\":null,\"isFinishen\":null,\"priceDate\":null,\"softwareVer\":null,\"leadFlag\":\"3\",\"outFlag\":null,\"planner\":null,\"bjBomId\":null,\"demanEndDate\":null,\"generateFlag\":null,\"workOrderType\":null,\"inItemNo\":null,\"splitFlag\":null,\"erpStatus\":null,\"notStart\":null,\"envAttrName\":null,\"envAttr1\":null,\"barcodeTotal\":null,\"erpQty\":null,\"inProcessGroup\":null,\"inCraftSection\":null,\"originalTask\":null,\"workOrderNo\":null,\"firstExpectedDeliveryDate\":\"2022-06-30 15:52:29\",\"updateFirstWarehouseDate\":null,\"firstWarehouseDate\":null,\"lastDeliveryDate\":\"2023-09-09 14:37:27\",\"receivieSendMaterialsFlag\":null,\"receiveSendMaterialsFlag\":null,\"castleSubCard\":null,\"prodAddress\":null,\"hasGenerateSn\":null,\"attribute11\":null,\"attribute12\":null,\"attribute13\":null,\"attribute14\":null,\"toGrantDate\":null,\"grantTime\":null,\"subProdplanId\":null,\"isTopProdplanMap\":null,\"notTopProdplanMap\":null,\"subChildList\":null,\"getDate\":null,\"inforExe\":null,\"consignplanNo\":null,\"isConsign\":null,\"consignBomId\":null,\"consignBomNo\":null,\"orgBomId\":null,\"outSourceFactoryCode\":null,\"stock\":null,\"zbjprodplanNo\":null,\"sourceType\":null,\"productClass\":null,\"productSmlClass\":null,\"delSns\":null,\"entpNo\":\"D\",\"isCompleted\":null}]";
            PowerMockito.when(json.toString()).thenReturn(bo);
            List<PsTask> resultList = new ArrayList<>();
            resultList.add(new PsTask() {{
                setTaskNo("127201150968ABB");
            }});
            PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(resultList);
            psTaskList = Whitebox.invokeMethod(service, "getTaskQty", "123");
            Assert.assertEquals(1, psTaskList.size());
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void getNumByProductCodeAndItemCode() throws Exception {
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask() {{
            setTaskId("TaskId");
        }};
        psTaskList.add(psTask);
        List<ItemCheckDifferenceDTO> differenceDTOS = new ArrayList<>();
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO() {{
            setUsageCount(new BigDecimal("10"));
        }};

        List<BBomDetailDTO> bomDetailDTOS;
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(0, bomDetailDTOS.size());
        psTaskList.clear();
        differenceDTOS.add(itemCheckDifferenceDTO);
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(0, bomDetailDTOS.size());

        psTaskList.add(psTask);
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(0, bomDetailDTOS.size());
        itemCheckDifferenceDTO.setItemNo("ItemNo");
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(0, bomDetailDTOS.size());
        psTask.setItemNo("ItemNo");
        itemCheckDifferenceDTO.setItemNo(null);
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(0, bomDetailDTOS.size());

        itemCheckDifferenceDTO.setItemNo("ItemNo");
        List<BBomDetailDTO> allBomQtyList = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        bBomDetailDTO.setbBomDetailId("15451831236463");
        bBomDetailDTO.setBomHeaderId("1545183");
        bBomDetailDTO.setItemCode("046110400006");
        bBomDetailDTO.setUsageCount(new BigDecimal("6"));
        bBomDetailDTO.setProductCode("129571751003AOB");
        allBomQtyList.add(bBomDetailDTO);
        PowerMockito.mockStatic(HttpRemoteUtil.class, ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("url");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(allBomQtyList);
        bomDetailDTOS = Whitebox.invokeMethod(service, "getNumByProductCodeAndItemCode", psTaskList, differenceDTOS);
        Assert.assertEquals(1, bomDetailDTOS.size());
    }

    @Test
    public void calculateDifferenceCount() throws Exception {
        List<ItemCheckDifferenceDTO> checkDifferenceDTOList = new ArrayList<>();
        List<PsTask> psTaskList = new ArrayList<>();
        List<BBomDetailDTO> bomDetailDTOList = new ArrayList<>();
        Whitebox.invokeMethod(service, "calculateDifferenceCount", checkDifferenceDTOList, psTaskList,
                bomDetailDTOList);
        Assert.assertEquals(0, checkDifferenceDTOList.size());
        ItemCheckDifferenceDTO itemCheckDifferenceDTO = new ItemCheckDifferenceDTO();
        itemCheckDifferenceDTO.setItemNo("itemCode1");
        itemCheckDifferenceDTO.setItemTotalQty(new BigDecimal("100"));
        checkDifferenceDTOList.add(itemCheckDifferenceDTO);
        Whitebox.invokeMethod(service, "calculateDifferenceCount", checkDifferenceDTOList, psTaskList,
                bomDetailDTOList);
        Assert.assertEquals(new BigDecimal("0"), checkDifferenceDTOList.get(0).getTaskQty());

        PsTask psTask = new PsTask();
        psTask.setTaskQty(new BigDecimal("10"));
        psTaskList.add(psTask);
        Whitebox.invokeMethod(service, "calculateDifferenceCount", checkDifferenceDTOList, psTaskList,
                bomDetailDTOList);
        Assert.assertEquals(new BigDecimal("10"), checkDifferenceDTOList.get(0).getTaskQty());

        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        bBomDetailDTO.setItemCode("itemCode");
        bBomDetailDTO.setUsageCount(new BigDecimal("10"));
        bomDetailDTOList.add(bBomDetailDTO);
        Whitebox.invokeMethod(service, "calculateDifferenceCount", checkDifferenceDTOList, psTaskList,
                bomDetailDTOList);
        Assert.assertEquals(new BigDecimal("0"), checkDifferenceDTOList.get(0).getUsageCount());
        itemCheckDifferenceDTO.setItemNo("itemCode");
        bomDetailDTOList.add(new BBomDetailDTO(){{setItemCode("2");setUsageCount(BigDecimal.ZERO);}});
        bomDetailDTOList.add(new BBomDetailDTO(){{setItemCode("2");setUsageCount(BigDecimal.ZERO);}});
        bomDetailDTOList.add(new BBomDetailDTO(){{setItemCode("3");setUsageCount(BigDecimal.ZERO);}});
        Whitebox.invokeMethod(service, "calculateDifferenceCount", checkDifferenceDTOList, psTaskList,
                bomDetailDTOList);
        Assert.assertEquals(new BigDecimal("10"), checkDifferenceDTOList.get(0).getUsageCount());
    }

    @Test
    public void convertToCfgHeaderIdMap() throws Exception {
        List<PsWorkOrderSmt> psWorkOrderSmtList = new ArrayList<>();
        String prodPlanId = "8887778";
        psWorkOrderSmtList.add(new PsWorkOrderSmt(){{setWorkOrderNo("8887778-SMT-A5801");setCfgHeaderId("");setCfgStatus("已导入");}});
        psWorkOrderSmtList.add(new PsWorkOrderSmt(){{setWorkOrderNo("8887778-SMT-B5801");setCfgHeaderId("");setCfgStatus("已导入");}});
        Assert.assertNotNull(Whitebox.invokeMethod(service, "convertToCfgHeaderIdMap", psWorkOrderSmtList, prodPlanId));
    }
}