package com.zte.test;

import com.zte.application.impl.OutOrderAbnormalInfoServiceImpl;
import com.zte.domain.model.OutAbnormalDetails;
import com.zte.domain.model.OutAbnormalDetailsRepository;
import com.zte.interfaces.dto.OutAbnormalInfoDTO;
import com.zte.interfaces.dto.OutLnpDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @Title: 测试
 * @Description:
 * @date 2020/9/2 11:32
 */
public class OutOrderAbnormalInfoServiceImplTest extends PowerBaseTestCase {

    @Mock
    private OutAbnormalDetailsRepository detailsRepository;
    private OutOrderAbnormalInfoServiceImpl infoService = PowerMockito.spy(new OutOrderAbnormalInfoServiceImpl());

    @Test
    public void setSnList() throws ParseException {
        OutAbnormalDetails details = new OutAbnormalDetails();
        details.setOutOrderNo("1234567890");
        OutLnpDTO dto=new OutLnpDTO();
        List<String> snList=new ArrayList<>();
        snList.add("22");
        dto.setSnList(snList);
        Assert.assertEquals("1234567890", details.getOutOrderNo());
        infoService.setSnList(dto,details);
    }



}
