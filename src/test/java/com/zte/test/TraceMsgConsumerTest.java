package com.zte.test;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.SmtTraceService;
import com.zte.application.kafka.consumer.TraceMsgConsumer;
import com.zte.interfaces.dto.TraceCalDTO;
import com.zte.interfaces.dto.TraceFixDTO;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

@PrepareForTest({RedisHelper.class})
public class TraceMsgConsumerTest extends PowerBaseTestCase {
	@InjectMocks
	private TraceMsgConsumer traceMsgConsumer;
	@Mock
	private FactoryConfig factoryConfig;
	@Mock
	private RedisTemplate<String, Object> redisTemplate;
	@Mock
	private SmtTraceService smtTraceService;
	@Mock
	private RedisLock redisLock;

	@Test
	public void consumeTraceCalMsg() throws Exception {
		PowerMockito.mockStatic(RedisHelper.class);
		traceMsgConsumer.consumeTraceCalMsg(null);

		ReflectionTestUtils.setField(traceMsgConsumer, "factoryId", "53");
		MsgData<TraceCalDTO> msgData = new MsgData<>();
		traceMsgConsumer.consumeTraceCalMsg(JSONObject.toJSONString(msgData));

		msgData.setFactoryId("53");
		traceMsgConsumer.consumeTraceCalMsg(JSONObject.toJSONString(msgData));

		TraceCalDTO dto = new TraceCalDTO();
		msgData.setData(dto);
		PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
		PowerMockito.when(redisLock.lock(Mockito.anyLong())).thenReturn(true);
		PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);

		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn(null);
		traceMsgConsumer.consumeTraceCalMsg(JSONObject.toJSONString(msgData));

		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("DB_SZ");
		traceMsgConsumer.consumeTraceCalMsg(JSONObject.toJSONString(msgData));
		Assert.assertEquals("53", msgData.getFactoryId());
	}

	@Test
	public void consumeTraceFixdMsg() throws Exception {
		PowerMockito.mockStatic(RedisHelper.class);
		traceMsgConsumer.consumeTraceFixdMsg(null);

		ReflectionTestUtils.setField(traceMsgConsumer, "factoryId", "53");
		MsgData<TraceFixDTO> msgData = new MsgData<>();
		traceMsgConsumer.consumeTraceFixdMsg(JSONObject.toJSONString(msgData));

		msgData.setFactoryId("53");
		traceMsgConsumer.consumeTraceFixdMsg(JSONObject.toJSONString(msgData));

		TraceFixDTO dto = new TraceFixDTO();
		msgData.setData(dto);
		PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
		PowerMockito.when(redisLock.lock(Mockito.anyLong())).thenReturn(true);
		PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn(null);
		traceMsgConsumer.consumeTraceFixdMsg(JSONObject.toJSONString(msgData));

		PowerMockito.when(factoryConfig.getFactoryDbById(Mockito.anyString())).thenReturn("DB_SZ");
		traceMsgConsumer.consumeTraceFixdMsg(JSONObject.toJSONString(msgData));
		Assert.assertEquals("53", msgData.getFactoryId());
	}
}
