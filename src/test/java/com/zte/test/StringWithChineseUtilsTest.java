package com.zte.test;

import java.io.UnsupportedEncodingException;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;

import com.zte.common.utils.StringWithChineseUtils;

public class StringWithChineseUtilsTest {
	
	@Test
    public void substring() throws UnsupportedEncodingException {
		String ret = StringWithChineseUtils.substring("ascv4561", 4);
		Assert.assertEquals(ret, "ascv");
	}
	
	@Test
	public void substring2() throws UnsupportedEncodingException {
		String ret = StringWithChineseUtils.substring("ascv4561", 1,3);
		Assert.assertEquals(ret, "sc");
	}
	
	@Test
	public void gsubstring() throws UnsupportedEncodingException {
		String ret = StringWithChineseUtils.gsubstring("ascv4561", 4);
		Assert.assertEquals(ret, "ascv");
	}
	
	@Test
	public void idgui() throws UnsupportedEncodingException {
		String ret = StringWithChineseUtils.idgui("ascv4561", 4);
		Assert.assertEquals(ret, "ascv");
	}
	
	@Test
	public void getStrList() throws UnsupportedEncodingException {
		List<String> ret = StringWithChineseUtils.getStrList("ascv4561", 2);
		Assert.assertEquals(ret.get(1), "cv");
	}
}
