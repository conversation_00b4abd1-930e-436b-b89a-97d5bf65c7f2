package com.zte.test;

import com.zte.application.impl.WipPrintConfigServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipPrintConfigDTO;
import com.zte.domain.model.WipPrintConfigRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.WipPrintConfigExcelDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({CommonUtils.class, MESHttpHelper.class, HttpClientUtil.class, BasicsettingRemoteService.class, PlanscheduleRemoteService.class})
public class WipIPrintConfigTest extends PowerBaseTestCase {

    @InjectMocks
    private WipPrintConfigServiceImpl wipPrintConfigService;
    @Mock
    private WipPrintConfigRepository wipPrintConfigRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Test
    public void query() throws Exception {
        Assert.assertNotNull(wipPrintConfigService.query(new WipPrintConfigDTO()));
    }

    @Test
    public void importResult() throws Exception {
        Assert.assertThrows(MesBusinessException.class, () -> wipPrintConfigService.importResult(null));
    }

    @Test
    public void save() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");

        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class, BasicsettingRemoteService.class);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-factory-id", "2");
        headerMap.put("x-emp-no", "123");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        List<WipPrintConfigExcelDTO> result = new ArrayList<>();
        result.add(new WipPrintConfigExcelDTO() {{
            setSn("1");
        }});
        result.add(new WipPrintConfigExcelDTO() {{
            setSn("2");
        }});
        result.add(new WipPrintConfigExcelDTO() {{
            setSn("3");
        }});

        List<PsWipInfo> list = new ArrayList<>();
        list.add(new PsWipInfo() {{
            setSn("1");
            setAttribute1("1");
        }});
        list.add(new PsWipInfo() {{
            setSn("2");
            setAttribute1("2");
        }});
        list.add(new PsWipInfo() {{
            setSn("3");
        }});
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(any())).thenReturn(list);

        List<PsTask> psTaskList = new ArrayList<>();
        psTaskList.add(new PsTask(){{setProdplanId("1");}});
        PowerMockito.when(PlanscheduleRemoteService.getPartsPlannoInfoByProdPlanIdList(any())).thenReturn(psTaskList);

        List<WipPrintConfigDTO> configList = new ArrayList<>();
        configList.add(new WipPrintConfigDTO(){{setSn("2");}});
        PowerMockito.when(wipPrintConfigRepository.selectEntityListPage(any())).thenReturn(configList);
        wipPrintConfigService.save(result);
        Assert.assertEquals("123", MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
    }

    @Test
    public void saveException() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");

        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, BasicsettingRemoteService.class);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-factory-id", "2");
        headerMap.put("x-emp-no", "123");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        List<WipPrintConfigExcelDTO> result = new ArrayList<>();
        Assert.assertThrows(MesBusinessException.class, () -> wipPrintConfigService.save(result));
        for (int i = 0; i < 501; i++) {
            WipPrintConfigExcelDTO wipPrintConfigExcelDTO = new WipPrintConfigExcelDTO();
            wipPrintConfigExcelDTO.setSn(String.valueOf(i));
            result.add(wipPrintConfigExcelDTO);
        }
        Assert.assertThrows(MesBusinessException.class, () -> wipPrintConfigService.save(result));
        result.remove(0);
        result.remove(1);
        result.get(50).setSn(null);
        result.get(0).setSn(null);
        result.get(1).setSn("1");
        result.get(2).setSn("1");

        Assert.assertThrows(MesBusinessException.class, () -> wipPrintConfigService.save(result));
        result.remove(0);
        Assert.assertThrows(MesBusinessException.class, () -> wipPrintConfigService.save(result));
    }

    @Test
    public void delete() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, BasicsettingRemoteService.class);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-factory-id", "2");
        headerMap.put("x-emp-no", "123");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        wipPrintConfigService.delete("1");
        Assert.assertEquals("123", MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
    }

    @Test
    public void exportTemplate() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        MockHttpServletResponse mockHttpServletResponse = new MockHttpServletResponse();
        wipPrintConfigService.exportTemplate(mockHttpServletResponse);
        Assert.assertNotNull(mockHttpServletResponse);
    }

}
