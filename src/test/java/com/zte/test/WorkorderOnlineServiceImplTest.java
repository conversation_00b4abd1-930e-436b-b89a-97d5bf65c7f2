package com.zte.test;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.WorkorderOnline;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zte.application.impl.WorkorderOnlineServiceImpl;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.WorkorderOnlineRepository;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import com.zte.util.PowerBaseTestCase;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class,MicroServiceDiscoveryInvoker.class, CommonUtils.class})
public class WorkorderOnlineServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WorkorderOnlineServiceImpl service;
    @Mock
    private WorkorderOnlineRepository workorderOnlineRepository;
    @Mock
    private SmtMachineMaterialPrepareRepository SmtMachineMaterialPrepareService;
    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceDiscoveryInvoker.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        service = spy(new WorkorderOnlineServiceImpl());
        service.setWorkorderOnlineRepository(workorderOnlineRepository);
        service.setSmtMachineMaterialPrepareService(SmtMachineMaterialPrepareService);
        service.setSmtMachineMaterialMoutingRepository(smtMachineMaterialMoutingRepository);

    }

    @Test
    public void beginwork4workorder() throws JsonProcessingException, RouteException, IOException {
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void updateObject() throws Exception {
        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderId("1");
        doReturn("").when(service).updateWorkOrder(anyObject());
        doReturn(1).when(service).insertWorkorderOnline(anyObject());
        when(SmtMachineMaterialPrepareService.selectByWorkOrderCode(anyObject())).thenReturn(null);
        when(smtMachineMaterialMoutingRepository.batchInsertSmtMachineMaterialMouting(anyObject())).thenReturn(1);
        when(SmtMachineMaterialPrepareService.batchUpdateByid(anyObject())).thenReturn(1);
        Assert.assertEquals("1", psWorkOrderDTO.getWorkOrderId());



    }

    @Test
    public void selectWorkorderOnline() throws Exception {
        List<WorkorderOnline> workorderOnlineList = new LinkedList<>();
        WorkorderOnline a1 = new WorkorderOnline();
        workorderOnlineList.add(a1);
        PowerMockito.when(
                workorderOnlineRepository.selectWorkorderOnlineSelective(Mockito.any())).thenReturn(workorderOnlineList);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "\t{\n" +
                "\t\t\"workOrderId\":\"ggg\"\n" +
                "\t}\n" +
                "  ]\n" +
                "  }\n" +
                "}");
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        Assert.assertThrows(NullPointerException.class, () -> service.selectWorkorderOnline(new WorkorderOnline()));
    }
}
