package com.zte.test;

import com.zte.application.BarcodeLockDetailService;
import com.zte.application.impl.WipScanHistoryServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipInfoDelLogRepository;
import com.zte.domain.model.WipScanHistory;
import com.zte.domain.model.WipScanHistoryRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.interfaces.dto.BoardInstructionCycleInfoDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipInfoDelLogDTO;
import com.zte.interfaces.dto.WipScanHistoryDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class, BasicsettingRemoteService.class})
public class WipScanHistoryServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private WipScanHistoryServiceImpl service;
    @Mock
    private WipScanHistoryRepository wipScanHistoryRepository;
    @Mock
    private WipInfoDelLogRepository wipInfoDelLogRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    BarcodeLockDetailService barcodeLockDetailService;

    @Test
    public void queryWipScanHistoryListPage() throws Exception {
        WipScanHistoryDTO dto = new WipScanHistoryDTO();
        dto.setAttribute1("7310021");
        dto.setParentSn("717211408608");
        dto.setSn("221155125511");
        dto.setPageNo(1L);
        dto.setPageSize(10L);
//        dto.setStartRow(0L);
//        dto.setEndRow(1L);

        // dto.setStartTime("2000-01-01");
        // dto.setEndTime("2020-12-30");

        List<WipScanHistoryDTO> list = new ArrayList<WipScanHistoryDTO>();
        list.add(dto);

        Long count = 10L;
        PowerMockito.when(wipScanHistoryRepository.queryWipScanHistoryListCount(any())).thenReturn(count);


        PageRows<WipScanHistoryDTO> page = new PageRows<>();
        page.setTotal(10);
        page.setRows(list);

        Assert.assertNotNull(service.queryWipScanHistoryListPage(dto));

    }

    @Test
    public void getWipScanHistorySelective() throws Exception {
        List<WipScanHistory> list = new ArrayList<>();
        PowerMockito.when(wipScanHistoryRepository.getWipScanHistorySelective(Mockito.anyObject())).thenReturn(list);

        WipScanHistoryDTO dto = new WipScanHistoryDTO();
        dto.setWorkOrderNo("7000153-SMT-B5302");
        dto.setSourceSysName("SMT-62-B面");
        Assert.assertNotNull(service.getWipScanHistorySelective(dto));

    }

    @Test
    public void getCumulativeScans() {
        PowerMockito.when(wipScanHistoryRepository.getCumulativeScans(any(), any(), any())).thenReturn(2L);

        List<WipScanHistory> wipScanHistories = new ArrayList<>();
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("111");
        Date date = new Date();
        wipScanHistory.setCreateDate(date);
        wipScanHistories.add(wipScanHistory);
        PowerMockito.when(wipScanHistoryRepository.getLastScanBySn(any())).thenReturn(wipScanHistories);
        PowerMockito.when(wipScanHistoryRepository.getLastScanBySnWorkOrder(any(), any(), any())).thenReturn(wipScanHistories);

        List<WipInfoDelLogDTO> wipInfoDelLogDTOS = new ArrayList<>();
        WipInfoDelLogDTO wipInfoDelLogDTO = new WipInfoDelLogDTO();
        wipInfoDelLogDTO.setSn("111");
        wipInfoDelLogDTO.setCreateDate(new Date(date.getTime() + 10000));
        wipInfoDelLogDTOS.add(wipInfoDelLogDTO);
        PowerMockito.when(wipInfoDelLogRepository.getLastScan(any(), any(), any())).thenReturn(wipInfoDelLogDTOS);

        Assert.assertNotNull(service.getCumulativeScans("7000153-SMT-B5302", "a2", "a1"));
    }

    @Test
    public void updateIsWriteScanBack() {
        List<String> prodList = new LinkedList<>();
        prodList.add("8899512");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service.updateIsWriteScanBack(prodList);
    }

    @Test
    public void getDateOfProdplanId() throws Exception {
        List<String> prodplanIdList = new ArrayList<>();
        List<BoardInstructionCycleDataCreateDTO> tempList = new ArrayList<>();
        List<BoardInstructionCycleInfoDTO> infoDTOList = new ArrayList<>();
        List<BoardInstructionCycleDataCreateDTO> dtoList = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO dto2 = new BoardInstructionCycleDataCreateDTO();
        dto2.setProdplanId("222");
        dtoList.add(dto2);
        BoardInstructionCycleInfoDTO dto1 = new BoardInstructionCycleInfoDTO();
        dto1.setProdplanId("2222");
        infoDTOList.add(dto1);
        BoardInstructionCycleDataCreateDTO dto = new BoardInstructionCycleDataCreateDTO();
        dto.setProdplanId("666");
        tempList.add(dto);
        String s = "11";
        prodplanIdList.add(s);
        prodplanIdList.add("7778889");
        prodplanIdList.add("7778899");
        PowerMockito.when(wipScanHistoryRepository.getDateOfProdplanId(any())).thenReturn(tempList);
        PowerMockito.when(wipScanHistoryRepository.getPartData(any(), any())).thenReturn(infoDTOList);
        PowerMockito.when(psWipInfoRepository.getPartStock90Date(any(), any())).thenReturn(dtoList);
        service.getDateOfProdplanId(prodplanIdList);
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        service.getPartData(null);
        Assert.assertNotNull(d1);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);
        Map<String, List<CtRouteDetailDTO>> routeMap = new HashMap<>();
        List<CtRouteDetailDTO> detailDTOS = new LinkedList<>();
        CtRouteDetailDTO f1 = new CtRouteDetailDTO();
        f1.setNextProcess("6");
        f1.setCraftSection("smta");
        f1.setProcessSeq(new BigDecimal(2));
        f1.setItemNo("1234");
        detailDTOS.add(f1);
        routeMap.put("11",detailDTOS);
        List<CtRouteDetailDTO> detailDTOS2 = new LinkedList<>();
        CtRouteDetailDTO f2 = new CtRouteDetailDTO();
        f2.setCraftSection("smta");
        f2.setProcessSeq(new BigDecimal(2));
        f2.setItemNo("1234");
        detailDTOS2.add(f2);
        routeMap.put("7778889",detailDTOS2);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(null);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);
        detailDTOS2 = new LinkedList<>();
        f2 = new CtRouteDetailDTO();
        f2.setCraftSection("smta");
        f2.setNextProcess("#");
        f2.setProcessSeq(new BigDecimal(2));
        f2.setItemNo("1234");
        detailDTOS2.add(f2);
        CtRouteDetailDTO f3 = new CtRouteDetailDTO();
        f3.setCraftSection("smta");
        f3.setNextProcess("#");
        f3.setProcessSeq(new BigDecimal(2));
        f3.setItemNo("1234");
        detailDTOS2.add(f3);
        routeMap.put("7778889",detailDTOS2);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);

        detailDTOS2 = new LinkedList<>();
        f2 = new CtRouteDetailDTO();
        f2.setCraftSection("smta");
        f2.setNextProcess("smta");
        f2.setProcessSeq(new BigDecimal(2));
        f2.setItemNo("1234");
        detailDTOS2.add(f2);
        f3 = new CtRouteDetailDTO();
        f3.setCraftSection("smta");
        f3.setNextProcess("9");
        f3.setProcessSeq(new BigDecimal(2));
        f3.setItemNo("1234");
        detailDTOS2.add(f3);
        routeMap.put("7778889",detailDTOS2);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);

        detailDTOS2 = new LinkedList<>();
        f2 = new CtRouteDetailDTO();
        f2.setCraftSection("smta");
        f2.setNextProcess("smta");
        f2.setProcessSeq(new BigDecimal(2));
        f2.setItemNo("1234");
        detailDTOS2.add(f2);
        f3 = new CtRouteDetailDTO();
        f3.setCraftSection("smta");
        f3.setNextProcess("#");
        f3.setProcessSeq(new BigDecimal(2));
        f3.setItemNo("1234");
        detailDTOS2.add(f3);
        routeMap.put("7778889",detailDTOS2);
        PowerMockito.when(barcodeLockDetailService.getRouteInfoByProdPlanIdList(any())).thenReturn(routeMap);
        service.getPartData(prodplanIdList);
        Assert.assertNotNull(d1);
        types = null;
        try {
            PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
            service.getPartData(prodplanIdList);
        } catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_UP_TYPE_NOT_CONFIGURE, e.getMessage());
        }

    }


    @Test
    public void getLastDateOfProdplanId() throws Exception {
        service.getLastDateOfProdplanId(null);
        List<String> prodplanIdList = new ArrayList<>();
        service.getLastDateOfProdplanId(prodplanIdList);
        List<BoardInstructionCycleDataCreateDTO> boardInstructionCycleDataCreateDTOS = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO boardInstructionCycleDataCreateDTO = new BoardInstructionCycleDataCreateDTO();
        boardInstructionCycleDataCreateDTO.setRank(55);
        boardInstructionCycleDataCreateDTOS.add(boardInstructionCycleDataCreateDTO);
        PowerMockito.when(wipScanHistoryRepository.getLastDateOfProdplanId(any())).thenReturn(boardInstructionCycleDataCreateDTOS);
        Assert.assertNotNull(service.getLastDateOfProdplanId(prodplanIdList));
    }

    @Test
    public void getWipScanHistoryForTestCraftSection() throws Exception {
        List<WipScanHistory> list = new ArrayList<>();
        service.getWipScanHistoryForTestCraftSection(list);
        WipScanHistory wipScanHistory = new WipScanHistory();
        wipScanHistory.setSn("12345");
        list.add(wipScanHistory);
        service.getWipScanHistoryForTestCraftSection(list);
        List<WipScanHistory> tempList = new ArrayList<>();
        tempList.add(wipScanHistory);
        PowerMockito.when(wipScanHistoryRepository.getWipScanHistoryForTestCraftSection(any())).thenReturn(tempList);
        Assert.assertNotNull(service.getWipScanHistoryForTestCraftSection(list));
    }
    @Test
    public void getTimeRange() throws Exception {

        PowerMockito.when(wipScanHistoryRepository.queryMinCreateDate(Mockito.any())).thenReturn(null);
        WipScanHistoryDTO wipScanHistoryDTO = new WipScanHistoryDTO();
        Assert.assertNull(service.getTimeRange(wipScanHistoryDTO));

        WipScanHistory createDateBo = new WipScanHistory();
        PowerMockito.when(wipScanHistoryRepository.queryMinCreateDate(Mockito.any())).thenReturn(createDateBo);

        Assert.assertNull(service.getTimeRange(wipScanHistoryDTO));
        createDateBo.setCreateDate(new Date());
        PowerMockito.when(wipScanHistoryRepository.queryMaxCreateDate(Mockito.any())).thenReturn(null);
        Assert.assertNull(service.getTimeRange(wipScanHistoryDTO));
        WipScanHistory createDateBo2 = new WipScanHistory();
        PowerMockito.when(wipScanHistoryRepository.queryMaxCreateDate(Mockito.any())).thenReturn(createDateBo2);
        Assert.assertNull(service.getTimeRange(wipScanHistoryDTO));
        createDateBo2.setLastUpdatedDate(new Date());
        Assert.assertNotNull(service.getTimeRange(wipScanHistoryDTO));
    }
}
