package com.zte.test;

import com.zte.application.PmRepairRcvDetailService;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.PmRepairRcvDetailServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceImpl;
import com.zte.application.impl.PmRepairRcvServiceRecodeImpl;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.PsScanHistoryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PmRepairRcv;
import com.zte.domain.model.PmRepairRcvRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.SemiManufactureDealInfo;
import com.zte.interfaces.dto.PmRepairRcvDetailDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.doReturn;


@PrepareForTest({PmRepairRcvServiceRecodeImpl.class, CommonUtils.class})
public class PmRepairRcvServiceRecodeImplTest extends PowerBaseTestCase {

    @Mock
    private RedisLock redisLock;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private PmRepairRcvRepository pmRepairRcvRepository;

    @Mock
    private PmRepairRcvDetailService pmRepairRcvDetailService;

    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    private PmRepairRcvServiceImpl pmRepairRcvService;

    @Mock
    private PsScanHistoryServiceImpl scanHistoryServiceImpl;

    @InjectMocks
    private PmRepairRcvServiceRecodeImpl pmRepairRcvServiceRecodeImpl;

    @Test
    public void submitRepairOrder() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.mockStatic(CommonUtils.class);
        PmRepairRcv record = new PmRepairRcv();
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new ArrayList<>();
        PmRepairRcvDetailDTO pmRepairRcvDetailDTO = new PmRepairRcvDetailDTO();
        pmRepairRcvDetailDTO.setSn("7310035000081");
        pmRepairRcvDetailDTO.setStatus(Constant.REPAIR_STATUS_FICTION);
        pmRepairRcvDetailDTOs.add(pmRepairRcvDetailDTO);
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn(pmRepairRcvDetailDTO.getSn());
        psWipInfo.setWipId("1");
        doReturn(psWipInfo).when(psWipInfoService).getWipInfoBySn(Mockito.any());
        try{
            pmRepairRcvServiceRecodeImpl.submitRepairOrder(record, pmRepairRcvDetailDTOs);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException || e.getMessage().length() > 0);
        }
    }

    @Test
    public void submitRepairOrderEx() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.mockStatic(CommonUtils.class);
        PmRepairRcvDetailDTO a1 = new PmRepairRcvDetailDTO();
        a1.setStatus(Constant.REPAIR_STATUS_FICTION);
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setWipId("123");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.any())).thenReturn(wipInfo);
        pmRepairRcvServiceRecodeImpl.submitRepairOrderEx(new PmRepairRcv(), new LinkedList<>(), new LinkedList<>(),
                new LinkedList<SysLookupTypesDTO>(), a1);
        Assert.assertEquals("123",wipInfo.getWipId());
    }

    @Test
    public void sendRepair() throws Exception {
        PmRepairRcv record = new PmRepairRcv();
        record.setFromStation("123");
        List<PmRepairRcvDetailDTO> pmRepairRcvDetailDTOs = new LinkedList<>();
        PmRepairRcvDetailDTO a1 = new PmRepairRcvDetailDTO();
        a1.setSn("123");
        pmRepairRcvDetailDTOs.add(a1);

        List<PsWipInfo> wipInfo = new LinkedList<>();
        PsWipInfo b1 = new PsWipInfo();
        wipInfo.add(b1);
        PowerMockito.when(psWipInfoService.getWipInfoJoinTestBySn(Mockito.any())).thenReturn(wipInfo)
        ;

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PsWipInfo wipIno = new PsWipInfo();
        wipIno.setWipId("123");
        PowerMockito.when(psWipInfoService.getWipInfoBySn(Mockito.any())).thenReturn(wipIno);
        try{
            pmRepairRcvServiceRecodeImpl.sendRepair(record, pmRepairRcvDetailDTOs);
        }catch (Exception e){
            Assert.assertTrue(e instanceof NullPointerException || e.getMessage().length()>0);
        }
    }

}
