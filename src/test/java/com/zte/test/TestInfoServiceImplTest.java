package com.zte.test;

import com.zte.application.WipExtendIdentificationService;
import com.zte.application.impl.TestInfoBatchServiceImpl;
import com.zte.application.impl.TestInfoServiceImpl;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.TestInfoDTO;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;

public class TestInfoServiceImplTest extends PowerBaseTestCase {


    @InjectMocks
    private TestInfoServiceImpl testInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    private WipExtendIdentificationService wipExtendIdentificationService;

    @Mock
    private WipTestRecodeRepository wipTestRecodeRepository;

    @Mock
    private BsWorkTimeSectionRepository bsWorkTimeSectionRepository;

    @Mock
    private PsOutputInfoRepository psOutputInfoRepository;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private FactoryConfig factoryConfig;

    @Test
    public void saveWipExtendInfo() {
        PowerMockito.when(factoryConfig.getCommonSiteCsFactoryId()).thenReturn("55");
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("55");
        Assert.assertEquals(0,testInfoService.saveWipExtendInfo(new TestInfoDTO(),new BSProcessDTO()));
    }

    @Test
    public void updateExtendInfo() {
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWipId("123");
        psWipInfo.setWorkOrderNo("8888422-SMT-A5501");
        psWipInfo.setSn("888842200001");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any()))
                .thenReturn(psWipInfo);
        PowerMockito.when(factoryConfig.getCommonSiteCsFactoryId()).thenReturn("55");
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("55");
        Assert.assertEquals(0,testInfoService.updateExtendInfo(new TestInfoDTO(),new BSProcessDTO()));
    }

	@Test
	public void writeOutPut() throws IllegalAccessException {
		TestInfoDTO testInfoDto = new TestInfoDTO();
		BSProcessDTO processDto = new BSProcessDTO();
		PowerMockito.field(TestInfoServiceImpl.class, "writeOutputSwitch").set(testInfoService, "Y");
		try{
			Whitebox.invokeMethod(testInfoService, "writeOutPut", testInfoDto,processDto);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void writeOutPutTwo() throws IllegalAccessException {
		TestInfoDTO testInfoDto = new TestInfoDTO();
		BSProcessDTO processDto = new BSProcessDTO();
		PowerMockito.field(TestInfoServiceImpl.class, "writeOutputSwitch").set(testInfoService, "N");
		try{
			Whitebox.invokeMethod(testInfoService, "writeOutPut", testInfoDto,processDto);
		} catch (Exception e) {
			Assert.assertNull(e.getMessage());
		}
	}

}
