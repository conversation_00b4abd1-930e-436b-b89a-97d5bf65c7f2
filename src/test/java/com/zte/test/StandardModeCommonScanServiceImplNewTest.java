package com.zte.test;


import com.zte.application.WipInfoDelLogService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.StandardModeCommonScanServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.SnAttrInfoRepository;
import com.zte.domain.model.WhiteListInfoRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.ReturnProductionOutWarehouseDTO;
import com.zte.interfaces.dto.SnAttrInfoEntityDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WhiteListInfoDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({BasicsettingRemoteService.class,CrafttechRemoteService.class,PlanscheduleRemoteService.class,CommonUtils.class})
public class StandardModeCommonScanServiceImplNewTest extends PowerBaseTestCase {
    @InjectMocks
    private StandardModeCommonScanServiceImpl standardModeCommonScanService;

    @Mock
    private WhiteListInfoRepository whiteListInfoRepository;

    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;

    @Mock
    private SnAttrInfoRepository snAttrInfoRepository;

    @Mock
    private WipInfoDelLogService wipInfoDelLogService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    FlowControlCommonService flowControlCommonService;
    

    @Before
    public void init(){
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void getReturnMsg() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class,CommonUtils.class);
        List<SysLookupTypesDTO> lookupTypes = new ArrayList<>();
        SysLookupTypesDTO lookupTypesDTO = new SysLookupTypesDTO();
        lookupTypesDTO.setLookupCode(new BigDecimal(55));
        lookupTypesDTO.setLookupMeaning("2g");
        lookupTypes.add(lookupTypesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap()))
                .thenReturn(null);
        standardModeCommonScanService.getReturnMsg(new FlowControlInfoDTO(){{setWipInfo(new PsWipInfo());}},"2");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap()))
                .thenReturn(lookupTypes);
        Assert.assertNotNull(standardModeCommonScanService.getReturnMsg(new FlowControlInfoDTO(){{setWipInfo(new PsWipInfo());}},"2"));
    }

    @Test
    public void dealWipInfoAndScanHistoryAndUpdateWoInOutQty()throws Exception{
        List<BarcodeExpandDTO> barcodeExpandDTOList=new ArrayList<>();
        BarcodeExpandDTO barcodeExpandDTO=new BarcodeExpandDTO();
        barcodeExpandDTO.setBarcode("ZTE200603023362");
        barcodeExpandDTO.setParentCategoryName("序列码");
        barcodeExpandDTO.setIsLead("HFS");
        barcodeExpandDTO.setItemCode("126510150593ACB");
        barcodeExpandDTOList.add(barcodeExpandDTO);
        when(barcodeCenterRemoteService.expandQuery(anyObject())).thenReturn(barcodeExpandDTOList);

        List<SnAttrInfoEntityDTO> snAttrInfoEntityDTOList = new ArrayList<>();
        SnAttrInfoEntityDTO snAttrInfoEntityDTO = new SnAttrInfoEntityDTO();
        snAttrInfoEntityDTO.setEnCode("enCode");
        snAttrInfoEntityDTO.setSn("sn");
        snAttrInfoEntityDTO.setMac("mac");
        snAttrInfoEntityDTOList.add(snAttrInfoEntityDTO);
        when(snAttrInfoRepository.getList(anyObject())).thenReturn(snAttrInfoEntityDTOList);

        ReturnProductionOutWarehouseDTO returnProductionOutWarehouseDTO = new ReturnProductionOutWarehouseDTO();
        List<String> enCodeList = new ArrayList<>();
        enCodeList.add("enCode");
        returnProductionOutWarehouseDTO.setEnList(enCodeList);
        try {
            standardModeCommonScanService.dealWipInfoAndScanHistoryAndUpdateWoInOutQty(returnProductionOutWarehouseDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getExMsgId());
        }

        List<PsEntityPlanBasicDTO> psEntityPlanBasicDTOS = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setTaskNo("TaskNo");
        psEntityPlanBasicDTOS.add(psEntityPlanBasicDTO);
        PowerMockito.when(psWipInfoServiceImpl.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanBasicDTOS);

        standardModeCommonScanService.dealWipInfoAndScanHistoryAndUpdateWoInOutQty(returnProductionOutWarehouseDTO);

        ReflectionTestUtils.setField(standardModeCommonScanService, "checkInputQty", true);
        try {
            standardModeCommonScanService.dealWipInfoAndScanHistoryAndUpdateWoInOutQty(returnProductionOutWarehouseDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.IN_QTY_BIG_THAN_WO_QTY, e.getExMsgId());
        }

        psEntityPlanBasicDTO.setWorkOrderQty(BigDecimal.TEN);
        psEntityPlanBasicDTO.setInputQty(BigDecimal.ONE);
        standardModeCommonScanService.dealWipInfoAndScanHistoryAndUpdateWoInOutQty(returnProductionOutWarehouseDTO);
        Assert.assertEquals("sn", snAttrInfoEntityDTO.getSn());
    }
    @Test
    public void setProcessList(){
        List<BSProcess> processNameList = new ArrayList<>();
        List<BSProcess> workStationNameList = new ArrayList<>();
        List<BSProcess> processList=new ArrayList<>();
        BSProcess processDto=new BSProcess();
        processDto.setCurrProcess("123");
        processDto.setProcessCode("124");
        FlowControlInfoDTO entity=new FlowControlInfoDTO();
        entity.setCurrProcessCode("123");
        Assert.assertEquals("123", processDto.getCurrProcess());
        standardModeCommonScanService.setProcessList(processNameList,workStationNameList,processList,entity);

    }

    @Test
    public void updateWoInOutQty() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO entityPlanBasic = new PsEntityPlanBasicDTO();
        entityPlanBasic.setWorkOrderNo("123");
        entityPlanBasic.setWorkOrderQty(new BigDecimal("23"));
        entityPlanBasic.setInputQty(new BigDecimal("2"));
        entityPlanBasic.setOutputQty(new BigDecimal("2"));
        entityPlanBasic.setIsFirstProcess("Y");
        entityPlanBasic.setIsLastProcess("Y");
        entity.setEntityPlanBasic(entityPlanBasic);

        entity.setWorkStation("2");

        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.anyMap()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"Success\"\n" +
                        "  },\n" +
                        "  \"bo\": [{\"qtyControl\":\"1\"}],\n" +
                        "  \"other\": null\n" +
                        "}"));
        standardModeCommonScanService.updateWoInOutQty(entity, 2);
        Assert.assertEquals("123", entityPlanBasic.getWorkOrderNo());
        standardModeCommonScanService.doWoStart(entity, 2);
        standardModeCommonScanService.doWoEnd(entity, 2);
        standardModeCommonScanService.doWoEnd(entity, 20);

        try {
            PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("指令投入数量大于指令数量");
            entity.setIsFirstProcess(Constant.FLAG_Y);
            standardModeCommonScanService.updateWoInOutQty(entity, 30);
        } catch (Exception e) {
            Assert.assertEquals("指令投入数量大于指令数量", e.getMessage());
        }
    }


    @Test
    public void dealWhiteList() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.REWORK_TASK_DIFFERENCE);
        FlowControlInfoDTO checkflow = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO entityPlanBasic = new PsEntityPlanBasicDTO();
        entityPlanBasic.setWorkOrderNo("123");
        entityPlanBasic.setWorkOrderQty(new BigDecimal("23"));
        entityPlanBasic.setInputQty(new BigDecimal("2"));
        entityPlanBasic.setOutputQty(new BigDecimal("2"));
        checkflow.setEntityPlanBasic(entityPlanBasic);
        checkflow.setWorkStation("2");
        checkflow.setWorkOrderNo("WorkOrderNo");

        List<WhiteListInfoDTO> whiteList = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTO.setTaskNo("TaskNo");
        whiteList.add(whiteListInfoDTO);
        PowerMockito.when(whiteListInfoRepository.getWhiteListInfoDetailBySN(any())).thenReturn(whiteList);
        PowerMockito.when(psWipInfoServiceImpl.getList(any())).thenReturn(null);

        List<PsEntityPlanBasicDTO> psEntityPlanBasicDTOS = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setTaskNo("TaskNo");
        psEntityPlanBasicDTOS.add(psEntityPlanBasicDTO);
        PowerMockito.when(psWipInfoServiceImpl.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanBasicDTOS);
        try {
            standardModeCommonScanService.dealWhiteList(checkflow, "1111");
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.REWORK_TASK_DIFFERENCE, e.getMessage());
        }
    }


    @Test
    public void checkProcessForBoard() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        FlowControlInfoDTO checkflow = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO entityPlanBasic = new PsEntityPlanBasicDTO();
        entityPlanBasic.setWorkOrderNo("123");
        entityPlanBasic.setWorkOrderQty(new BigDecimal("23"));
        entityPlanBasic.setInputQty(new BigDecimal("2"));
        entityPlanBasic.setOutputQty(new BigDecimal("2"));
        checkflow.setEntityPlanBasic(entityPlanBasic);
        checkflow.setWorkStation("2");
        checkflow.setWorkOrderNo("WorkOrderNo");

        List<WhiteListInfoDTO> whiteList = new ArrayList<>();
        WhiteListInfoDTO whiteListInfoDTO = new WhiteListInfoDTO();
        whiteListInfoDTO.setTaskNo("TaskNo");
        whiteList.add(whiteListInfoDTO);
        PowerMockito.when(whiteListInfoRepository.getWhiteListInfoDetailBySN(any())).thenReturn(whiteList);
        PowerMockito.when(psWipInfoServiceImpl.getList(any())).thenReturn(null);

        List<PsEntityPlanBasicDTO> psEntityPlanBasicDTOS = new ArrayList<>();
        PsEntityPlanBasicDTO psEntityPlanBasicDTO = new PsEntityPlanBasicDTO();
        psEntityPlanBasicDTO.setTaskNo("TaskNo");
        psEntityPlanBasicDTOS.add(psEntityPlanBasicDTO);
        PowerMockito.when(psWipInfoServiceImpl.getPsEntityPlanInfo(any())).thenReturn(psEntityPlanBasicDTOS);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProductType("111");
        psTaskList.add(psTask);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskByProdPlanId(anyString())).thenReturn(psTaskList);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.TASK_NOT_HAVE_DETAILS);
        try {
            standardModeCommonScanService.checkProcessForBoard(checkflow, "1111");
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.TASK_NOT_HAVE_DETAILS, e.getMessage());
        }

        psTask.setProductType(MpConstant.TASK_TYPE_RETURN);
        try {
            standardModeCommonScanService.checkProcessForBoard(checkflow, "1111");
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.TASK_NOT_HAVE_DETAILS, e.getMessage());
        }

        whiteList.clear();
        try {
            standardModeCommonScanService.checkProcessForBoard(checkflow, "1111");
        } catch (Exception e) {
            Assert.assertEquals(com.zte.common.model.MessageId.TASK_NOT_HAVE_DETAILS, e.getMessage());
        }
    }
    

}
