package com.zte.test;

import com.google.common.collect.Lists;
import com.zte.application.impl.CmDailyReportServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2023/8/9 9:11
 */
@PrepareForTest({BasicsettingRemoteService.class,PlanscheduleRemoteService.class,DatawbRemoteService.class,CrafttechRemoteService.class})
public class CmDailyReportServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private CmDailyReportServiceImpl service;
    @Mock
    private CmDailyReportStatDetailRepository cmDailyReportStatDetailRepository;
    @Mock
    private CmDailyReportStatHeadRepository cmDailyReportStatHeadRepository;
    @Mock
    private PsWipScanHistoryRepository psWipScanHistoryRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Before
    public void init() {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class,DatawbRemoteService.class,BasicsettingRemoteService.class,CrafttechRemoteService.class);
    }

    @Test
    public void statisticJob() throws Exception {
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(any())).thenReturn(null);
        service.statisticJob(null);
        service.statisticJob(600);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(0);
        }});
        service.statisticJob(500);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setTotal(10);
        }});
        service.statisticJob(500);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(1);
        }});
        PowerMockito.when(DatawbRemoteService.getCmReportErpInfo(any(),any(),any())).thenReturn(new ArrayList());
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(new ArrayList() {{
            add(new SysLookupValuesDTO());
        }});
        List<CmDailyStatisticReport> list1 = new ArrayList<>();
        list1.add(new CmDailyStatisticReport());
        PowerMockito.when(psWipScanHistoryRepository.selectAccumulatePage(Mockito.anyBoolean(), Mockito.anyList(),Mockito.anyList())).thenReturn(list1);
        PowerMockito.when(psWipInfoRepository.selectRemainPage(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(list1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service.statisticJob(500);
    }

    @Test
    public void getPageRealtimeData() throws Exception {
        PowerMockito.when(DatawbRemoteService.getCmReportErpInfo(any(),any(),any())).thenReturn(new ArrayList());
        List<PsTask> taskList = new ArrayList<>();
        taskList.add(new PsTask());
        Whitebox.invokeMethod(service, "getPageRealtimeData", new CmDailyStatisticReportQueryDTO(), 1,1,true,taskList);
        PowerMockito.when(DatawbRemoteService.getCmReportErpInfo(any(),any(),any())).thenReturn(new ArrayList() {{
            add(new CmDailyReportErpInfoDTO());
        }});
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(new ArrayList() {{
            add(new SysLookupValuesDTO());
            add(new SysLookupValuesDTO() {{
                setAttribute2("Y");
            }});
        }});
        List<CmDailyStatisticReport> list1 = new ArrayList<>();
        list1.add(new CmDailyStatisticReport());
        PowerMockito.when(psWipScanHistoryRepository.selectAccumulatePage(Mockito.anyBoolean(), Mockito.anyList(),Mockito.anyList())).thenReturn(list1);
        PowerMockito.when(psWipInfoRepository.selectRemainPage(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(list1);
        CmDailyStatisticReportQueryDTO dto = new CmDailyStatisticReportQueryDTO(){{
            setPage(1);
            setRows(10);
        }};
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        Whitebox.invokeMethod(service, "getPageRealtimeData", dto, 1,1,true,taskList);
    }


    @Test
    public void getCmDailyStatisticReports() throws Exception {
        List<String> taskNos = new ArrayList<>();
        taskNos.add("111");
        List<SysLookupValuesDTO> lookupValueList = new ArrayList(){{
            add(new SysLookupValuesDTO(){{
                setAttribute4("box");
            }});
        }};
        Whitebox.invokeMethod(service, "getCmDailyStatisticReports", taskNos ,true,lookupValueList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void selectRealTimePage() throws Exception {
        Map<String, List<PsTask>> groupByTaskNoMap = new HashMap<>();
        List<PsTask> tempList = new ArrayList<>();
        tempList.add(new PsTask(){{
            setProdplanId("123321");
            setTaskNo("1111");
        }});
        groupByTaskNoMap.put("1",tempList);
        groupByTaskNoMap.put("2",tempList);
        groupByTaskNoMap.put("3",tempList);
        groupByTaskNoMap.put("4",tempList);
        groupByTaskNoMap.put("5",tempList);
        groupByTaskNoMap.put("6",tempList);
        groupByTaskNoMap.put("7",tempList);
        groupByTaskNoMap.put("8",tempList);
        groupByTaskNoMap.put("9",tempList);
        groupByTaskNoMap.put("10",tempList);
        groupByTaskNoMap.put("11",tempList);
        groupByTaskNoMap.put("12",tempList);
        groupByTaskNoMap.put("13",tempList);
        groupByTaskNoMap.put("14",tempList);
        groupByTaskNoMap.put("15",tempList);
        groupByTaskNoMap.put("16",tempList);
        groupByTaskNoMap.put("17",tempList);
        groupByTaskNoMap.put("18",tempList);
        groupByTaskNoMap.put("19",tempList);
        groupByTaskNoMap.put("20",tempList);
        groupByTaskNoMap.put("21",tempList);
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(new ArrayList());
        try {
            Whitebox.invokeMethod(service, "selectRealTimePage", new ArrayList<>(),groupByTaskNoMap,true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOOKUP_6008_EMPTY, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(new ArrayList() {{
            add(new SysLookupValuesDTO());
            add(new SysLookupValuesDTO() {{
                setAttribute2("Y");
            }});
        }});
        PowerMockito.when(psWipScanHistoryRepository.selectAccumulatePage(Mockito.anyBoolean(), Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        PowerMockito.when(psWipInfoRepository.selectRemainPage(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "selectRealTimePage", new ArrayList<>(),groupByTaskNoMap,true);
        groupByTaskNoMap.clear();
        Whitebox.invokeMethod(service, "selectRealTimePage", new ArrayList<>(),groupByTaskNoMap,true);
    }

    @Test
    public void setLineCodes() throws Exception {
        List<PsTask> taskList = new ArrayList(){{
            add(new PsTask(){{
                setProdplanId("1111111");
            }});
        }};
        List<PsWorkOrderBasicDTO> lineList = new ArrayList(){{
            add(new PsWorkOrderBasicDTO(){{
                setSourceTask("1111111");
                setLineCode("1");
            }});
            add(new PsWorkOrderBasicDTO(){{
                setSourceTask("1111111");
                setLineCode("2");
            }});
        }};
        Whitebox.invokeMethod(service, "setLineCodes", taskList,lineList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getRouteByItemNo() throws Exception {
        List<CtBasicRouteDTO> list = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getRouteByItems(Mockito.anyList())).thenReturn(list);
        Whitebox.invokeMethod(service, "getRouteByItemNo", new ArrayList<>());
        list.add(new CtBasicRouteDTO(){{
            setRouteDetail("321");
        }});
        Whitebox.invokeMethod(service, "getRouteByItemNo", new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getRouteByPlanId() throws Exception {
        List<PsWorkOrderBasic> list = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getWorkBasicByTask(Mockito.anyList(),Mockito.isNull(),Mockito.isNull())).thenReturn(list);
        Whitebox.invokeMethod(service, "getRouteByPlanId", new ArrayList<>());
        list.add(new PsWorkOrderBasic());
        Whitebox.invokeMethod(service, "getRouteByPlanId", new ArrayList<>());
        list.add(new PsWorkOrderBasic() {{
            setRouteId("1");
        }});
        Whitebox.invokeMethod(service, "getRouteByPlanId", new ArrayList<>());
        List<CtRouteHead> list1 = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getRouteHeadByRouteIds(Mockito.anyList())).thenReturn(list1);
        Whitebox.invokeMethod(service, "getRouteByPlanId", new ArrayList<>());
        list1.add(new CtRouteHead() {{
            setRouteId("1");
            setRouteDetail("321");
        }});
        Whitebox.invokeMethod(service, "getRouteByPlanId", new ArrayList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setRouteInfo() throws Exception {
        Map<String, List<CmDailyStatisticReport>> statisticInfo = null;
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,null,null,null,null);
        statisticInfo = new HashMap<>();
        statisticInfo.put("111",new ArrayList<>());
        PsTask e = new PsTask();
        e.setTaskNo("112");
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,null,null,new ArrayList<>(),e);
        e.setTaskNo("111");
        List<CmDailyStatisticReport> reportList = new ArrayList<>();
        reportList.add(new CmDailyStatisticReport() {{
            setTaskQty(1);
        }});
        statisticInfo.put("111",reportList);
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,null,null,new ArrayList<>(),e);
        Map<String, String> routeMap = new HashMap<>();
        routeMap.put("111","1111");
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,null,routeMap,new ArrayList<>(),e);
        e.setTaskStatus("1");
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,null,routeMap,new ArrayList<>(),e);
        Map<String, String> itemRouteMap = new HashMap<>();
        itemRouteMap.put("222","2222");
        e.setTaskStatus(null);
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,itemRouteMap,routeMap,new ArrayList<>(),e);
        e.setTaskStatus("1");
        Whitebox.invokeMethod(service, "setRouteInfo", statisticInfo,itemRouteMap,routeMap,new ArrayList<>(),e);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void addERPInfo() throws Exception {
        Whitebox.invokeMethod(service, "addERPInfo",new ArrayList<>(),new ArrayList<>());
        List<CmDailyReportErpInfoDTO> erpList = new ArrayList<>();
        erpList.add(new CmDailyReportErpInfoDTO(){{
            setTaskNo("111");
        }});
        Whitebox.invokeMethod(service, "addERPInfo",new ArrayList<>(),erpList);
        List<CmDailyStatisticReport> list = new ArrayList<>();
        list.add(new CmDailyStatisticReport());
        Whitebox.invokeMethod(service, "addERPInfo",list,erpList);
        list.add(new CmDailyStatisticReport() {{
            setTaskNo("111");
        }});
        Whitebox.invokeMethod(service, "addERPInfo",list,erpList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    @Test
    public void cmDailyReportQuery() throws Exception {
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(sysLookupTypesList);
        CmDailyStatisticReportQueryDTO queryDTO = new CmDailyStatisticReportQueryDTO();
        queryDTO.setPage(1);
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(1);
        List<CmDailyReportStatHead> headList = new ArrayList<>();
        PowerMockito.when(cmDailyReportStatHeadRepository.selectByQueryDTO(queryDTO)).thenReturn(headList);
        List<CmDailyReportStatDetail> detailList = new ArrayList<>();
        PowerMockito.when(cmDailyReportStatDetailRepository.selectByHeadIds(Mockito.anyList())).thenReturn(detailList);
        service.cmDailyReportQuery(queryDTO);
        queryDTO.setRealTimeQueryFlag(true);
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(0);
        }});
        Assert.assertNotNull(service.cmDailyReportQuery(queryDTO));
    }

    @Test
    public void getPageRealTimeQueryInfo() throws Exception {
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(1);
        }});
        CmDailyStatisticReportQueryDTO queryDTO = new CmDailyStatisticReportQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setRows(10);
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(0);
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        sysLookupTypesList.add(new SysLookupTypesDTO() {{
            setLookupMeaning("Y");
        }});
        Whitebox.invokeMethod(service, "getPageRealTimeQueryInfo",queryDTO,sysLookupTypesList);
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(10);
        Whitebox.invokeMethod(service, "getPageRealTimeQueryInfo",queryDTO,sysLookupTypesList);
        queryDTO.setPage(2);
        queryDTO.setRows(10);
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(15);
        Whitebox.invokeMethod(service, "getPageRealTimeQueryInfo",queryDTO,sysLookupTypesList);
        queryDTO.setPage(3);
        queryDTO.setRows(10);
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(20);
        Whitebox.invokeMethod(service, "getPageRealTimeQueryInfo",queryDTO,sysLookupTypesList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void transferLeadFlag() throws Exception {
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        resultMapList.add(new HashMap(){{
            put("leadFlag","65");
        }});
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(new ArrayList());
        Whitebox.invokeMethod(service, "transferLeadFlag",resultMapList);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString())).thenReturn(new ArrayList() {{
            add(new SysLookupTypesDTO(){{
                setLookupMeaning("1");
                setDescriptionChinV("啊哈哈");
            }});
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        Whitebox.invokeMethod(service, "transferLeadFlag",resultMapList);
    }

    @Test
    public void transferLineName() throws Exception {
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        resultMapList.add(new HashMap(){{
            put("leadFlag","65");
            put("productLineCode","69,665");
        }});
        resultMapList.add(new HashMap(){{
            put("leadFlag","65");
            put("productLineCode","77");
        }});
        PowerMockito.when(BasicsettingRemoteService.getLineNameByCodeList(anyList())).thenReturn(new HashMap() {{
            put("69","aa");
            put("77","bb");
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        Whitebox.invokeMethod(service, "transferLineName",resultMapList);
    }

    @Test
    public void export() throws Exception {
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(new ArrayList(){{
            add(new SysLookupTypesDTO(){{
                setDescriptionChinV("白盒结存");
                setAttribute3("Y");
                setAttribute4("baiHe");
            }});
        }});
        CmDailyStatisticReportQueryDTO dto = new CmDailyStatisticReportQueryDTO();
        dto.setRealTimeQueryFlag(true);
        HttpServletResponse response = null;
        PowerMockito.when(PlanscheduleRemoteService.getPsTaskPage(anyMap())).thenReturn(new PageRows() {{
            setRows(Lists.newArrayList(new PsTask() {{
                setProdplanId("1");
                setTaskQty(BigDecimal.ONE);
                setTaskStatus(Constant.TASK_FINISHED);
                setItemNo("1");
            }}));
            setTotal(0);
        }});
        try {
            service.export(response,dto);
        } catch (Exception e){
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void notReelTimeExport() throws Exception {
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(Mockito.any())).thenReturn(60000);
        PowerMockito.when(cmDailyReportStatHeadRepository.selectByQueryDTO(Mockito.any())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "notReelTimeExport",new CmDailyStatisticReportQueryDTO(),
                new ArrayList<>(),new BigExcelProcesser(),null);
        PowerMockito.when(cmDailyReportStatHeadRepository.selectByQueryDTO(Mockito.any())).thenReturn(new ArrayList(){{
            add(new CmDailyReportStatHead(){{
                setHeadId("123");
            }});
        }});
        Whitebox.invokeMethod(service, "notReelTimeExport",new CmDailyStatisticReportQueryDTO(),
                new ArrayList<>(),new BigExcelProcesser(),null);
        PowerMockito.when(cmDailyReportStatHeadRepository.selectByQueryDTO(Mockito.any())).thenReturn(new ArrayList(){{
            add(new CmDailyReportStatHead(){{
                setHeadId("123");
                setStatisticDate(new Date());
            }});
        }});
        CmDailyStatisticReportQueryDTO queryDTO = new CmDailyStatisticReportQueryDTO();
        PowerMockito.when(cmDailyReportStatHeadRepository.countByQueryDTO(queryDTO)).thenReturn(12);
        PowerMockito.when(cmDailyReportStatDetailRepository.selectByHeadIds(Mockito.anyList())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(service, "notReelTimeExport",new CmDailyStatisticReportQueryDTO(),
                new ArrayList<>(),new BigExcelProcesser(),null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setIntNullToZero() throws Exception {
        CmDailyStatisticReport report = new CmDailyStatisticReport();
        report.setErpFQty(null);
        report.setTaskQty(52);
        Whitebox.invokeMethod(service, "setIntNullToZero",report);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void taskNoSetInfo() throws Exception {
        List<String> taskNos = new ArrayList<>();
        taskNos.add("111");
        List<SysLookupValuesDTO> lookupValueList = new ArrayList(){{
            add(new SysLookupValuesDTO(){{
                setAttribute4("box");
            }});
        }};
        List<CmDailyStatisticReport> list = new ArrayList(){{
            add(new CmDailyStatisticReport(){{
                setTaskNo("111");
            }});
        }};
        Whitebox.invokeMethod(service, "taskNoSetInfo",taskNos,lookupValueList,list);
        taskNos.add("112");
        Whitebox.invokeMethod(service, "taskNoSetInfo",taskNos,lookupValueList,list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
