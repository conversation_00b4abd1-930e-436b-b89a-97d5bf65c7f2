package com.zte.test;

import com.zte.application.impl.WhiteListInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WhiteListInfoRepository;
import com.zte.interfaces.dto.WhiteListInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * 标模白名单扫描单元测试
 * <AUTHOR> 徐娟
 */

public class WhiteListInfoServiceImplTest extends PowerBaseTestCase {


	@InjectMocks
	private WhiteListInfoServiceImpl whiteListInfoServiceImpl;
	@Mock
	private WhiteListInfoRepository whiteListInfoRepository;
	@Mock
	private PsWipInfoRepository psWipInfoRepository;
	@Mock
	private ServiceData serviceData;

	@Test
	public void setWhitelistInfoRepository() throws Exception {
		whiteListInfoServiceImpl.setWhitelistInfoRepository(whiteListInfoRepository);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);
	}
	

	 @Test
	public void getPageWhiteListDetailbySN() throws Exception{
		 WhiteListInfoDTO record = new WhiteListInfoDTO();
		 record.setPage(2L);
		 record.setRows(4L);
		 Long startRow = (record.getPage() - 1) * record.getRows() + 1;
		 Long endRow = record.getPage() * record.getRows();
		 record.setStartRow(startRow);
		 record.setEndRow(endRow);
		 List<WhiteListInfoDTO> list = new ArrayList<WhiteListInfoDTO>();
		 list.add(record);
		 String runNormal = "Y";
		 Assert.assertEquals(Constant.STR_Y, runNormal);

		 when(whiteListInfoRepository.getPageWhiteListDetailDetailbySN(record)).thenReturn(list);
	}

//	@Test
//	public void insertWhiteListInfo() throws Exception{
//		serviceData = PowerMockito.mock(ServiceData.class);
//
//		WhiteListInfoDTO dto = new WhiteListInfoDTO();
//		dto.setSn("111112222233");
//
//
//		WhiteListInfoServiceImpl service = PowerMockito.spy(new WhiteListInfoServiceImpl());
//		service.setWhitelistInfoRepository(whiteListInfoRepository);
//		service.setPsWipInfoRepository(psWipInfoRepository);
//
//		when(psWipInfoRepository.selectSNfromWipInfo(dto.getSn())).thenReturn(new ArrayList<>());
//		ServiceData ret1 = new ServiceData();
////		ret1.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WIP_INFO_EXIS));
////		Assert.assertSame(whiteListInfoRepository.insertWhiteListInfo(dto),ret1);
//
//		when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(dto.getSn())).thenReturn(new ArrayList<>());
//		ServiceData ret2 = new ServiceData();
////		ret2.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.WHITE_LIST_EXIS));
////		Assert.assertSame(whiteListInfoRepository.insertWhiteListInfo(dto),ret2);
//		ArrayList list1 = new ArrayList<>();
//		list1.add("123");
//		when(psWipInfoRepository.selectSNfromWipInfo(dto.getSn())).thenReturn(list1);
//		when(whiteListInfoRepository.selectSNfromWhiteLIstInfo(dto.getSn())).thenReturn(new ArrayList<>());
//		ServiceData ret3 = new ServiceData();
////		ret3.setCode(new RetCode(RetCode.SUCCESS_CODE, MessageId.SUCCESS));
////		Assert.assertSame(whiteListInfoRepository.insertWhiteListInfo(dto),ret3);
//	}

}
