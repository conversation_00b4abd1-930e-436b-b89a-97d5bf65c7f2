package com.zte.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.impl.ZteLmsMtlTransactionsInterServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.enums.InterfaceEnum;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.ZteLmsMtlTransactionsInterRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.ZteLmsMtlTransactionsInterDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.*;

/**
 * 指令基础信息测试类
 *
 * <AUTHOR> 袁海洋
 */
@PrepareForTest({HttpRemoteService.class, BasicsettingRemoteService.class, JacksonJsonConverUtil.class, MESHttpHelper.class})
public class ZteLmsMtlTransactionsInterServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ZteLmsMtlTransactionsInterServiceImpl zteLmsMtlTransactionsInterServiceImpl;
    @Mock
    private ZteLmsMtlTransactionsInterRepository zteLmsMtlTransactionsInterRepository;
    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private ICenterRemoteService iCenterRemoteService;

    private ServiceData serviceData;

    @Test
    public void setZteLmsMtlTransactionsInterRepository() throws Exception {
        zteLmsMtlTransactionsInterServiceImpl.setZteLmsMtlTransactionsInterRepository(zteLmsMtlTransactionsInterRepository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void transferZteLmsMtlTransactionsInterList() throws Exception {
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        String empno = "10255258";
        List<ZteLmsMtlTransactionsInterDTO> list = new ArrayList<>();
        serviceData = PowerMockito.mock(ServiceData.class);
        zteLmsMtlTransactionsInterServiceImpl = PowerMockito.mock(ZteLmsMtlTransactionsInterServiceImpl.class);

        doReturn(list).when(zteLmsMtlTransactionsInterRepository).selectZteLmsMtlTransactionsInterList();

        int transferNum = 0;
        int transferNumSplit = 0;
        int updateNum = 0;
        Map<String, Object> map = new HashMap<>(16);
        doReturn(transferNumSplit).when(zteLmsMtlTransactionsInterServiceImpl).insertZteLmsOrgTransInterBatch(list);
        doReturn(updateNum).when(zteLmsMtlTransactionsInterRepository).updateZteLmsMtlTransactionsInterBatch(map);
        serviceData.setBo(transferNum);
        doReturn(serviceData).when(zteLmsMtlTransactionsInterServiceImpl).transferZteLmsMtlTransactionsInterList(empno);
        Assert.assertEquals("10255258", empno);
    }

    @Test
    public void checkIsNotEmpty() {
        zteLmsMtlTransactionsInterServiceImpl = PowerMockito.mock(ZteLmsMtlTransactionsInterServiceImpl.class);
        ZteLmsMtlTransactionsInterDTO dto = new ZteLmsMtlTransactionsInterDTO();
        String msg = "11";
        doReturn(msg).when(zteLmsMtlTransactionsInterServiceImpl).checkIsNotEmpty(dto);
        Assert.assertEquals("11", msg);
    }

    @Test
    public void insertZteLmsOrgTransInterBatch() throws Exception {
        zteLmsMtlTransactionsInterServiceImpl = PowerMockito.mock(ZteLmsMtlTransactionsInterServiceImpl.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mock(JacksonJsonConverUtil.class);
        PowerMockito.mock(MESHttpHelper.class);
        Map<String, String> map = new HashMap<>(16);
        List<ZteLmsMtlTransactionsInterDTO> list = new ArrayList<>();
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.insertZteLmsOrgTransInterBatch);
//        String msg = HttpRemoteService.remoteExe(InterfaceEnum.insertZteLmsOrgTransInterBatch, map, headParams, url);
        String msg = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\": 1}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        // 点对点，返回JsonNode
        Assert.assertEquals(0,zteLmsMtlTransactionsInterServiceImpl.insertZteLmsOrgTransInterBatch(list));
    }

    @Test
    public void transferZteLmsMtlTransactionsInterList2() throws Exception {
        List<ZteLmsMtlTransactionsInterDTO> list = new LinkedList<>();
        ZteLmsMtlTransactionsInterDTO a1 = new ZteLmsMtlTransactionsInterDTO();
        a1.setCreateBy("123");
        a1.setLastUpdateDate(new Date());
        a1.setLastUpdatedDate(new Date());
        a1.setLastUpdatedBy("2");
        a1.setCreateDate(new Date());
        a1.setTransferTypeId(new BigDecimal("2"));
        a1.setTransactionQuantity(new BigDecimal("2"));
        a1.setTransactionDate(new Date());
        a1.setOrganizationId(new BigDecimal("1"));
        a1.setSubinventoryCode("2");
        a1.setTransferOrganization(new BigDecimal("2"));
        a1.setTransferSubinventory("2");
        list.add(a1);
        PowerMockito.when(zteLmsMtlTransactionsInterRepository.selectZteLmsMtlTransactionsInterList()).thenReturn(list);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.any(), Mockito.anyMap(),
                Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                        "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                        "  },\n" +
                        "  \"bo\": \"1\"\n" +
                        " \n" +
                        " \n" +
                        "}");

        Assert.assertNotNull(zteLmsMtlTransactionsInterServiceImpl.transferZteLmsMtlTransactionsInterList("123"));

        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.any(), Mockito.anyMap(),
                        Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                        "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                        "  },\n" +
                        "  \"bo\": \"0\"\n" +
                        " \n" +
                        " \n" +
                        "}");

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);
        Assert.assertNotNull(zteLmsMtlTransactionsInterServiceImpl.transferZteLmsMtlTransactionsInterList("123"));

        SysLookupTypesDTO sysLookupValuesDTO = new SysLookupTypesDTO();
        sysLookupValuesDTO.setLookupMeaning("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupValuesDTO);
        Assert.assertNotNull(zteLmsMtlTransactionsInterServiceImpl.transferZteLmsMtlTransactionsInterList("123"));
    }
}
