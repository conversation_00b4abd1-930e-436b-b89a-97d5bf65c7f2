package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.BsWorkTimeSectionService;
import com.zte.application.PsBarcodeControlInfoService;
import com.zte.application.PsCommonScanService;
import com.zte.application.PsOutputInfoService;
import com.zte.application.PsScanHistoryService;
import com.zte.application.WarehouseEntryInfoService;
import com.zte.application.WipInfoDelLogService;
import com.zte.application.WipRepairSnService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.PsWorkOrderSnAssignRepository;
import com.zte.domain.model.WipDailyStatisticReport;
import com.zte.domain.model.WorkOrderInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BSProcessInfoDTO;
import com.zte.interfaces.dto.BarcodeDTO;
import com.zte.interfaces.dto.BasicScanExDTO;
import com.zte.interfaces.dto.BsPubHrvOrgIdDTO;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.DealPsWipInfoParamDTO;
import com.zte.interfaces.dto.FlowControlConditionDTO;
import com.zte.interfaces.dto.PackScanBussDTO;
import com.zte.interfaces.dto.PcProcessTransferDTO;
import com.zte.interfaces.dto.PcProcessTransferSimpleDTO;
import com.zte.interfaces.dto.PmKeyValueDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PmSubmitConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.PsWipInfoDTO;
import com.zte.interfaces.dto.PsWipInfoSnQueryDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.LOOKUP_VALUE_6691;
import static com.zte.common.utils.Constant.LOOKUP_VALUE_6691001;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

@PrepareForTest({MicroServiceRestUtil.class, CommonUtils.class, BasicsettingRemoteService.class, CrafttechRemoteService.class,
        ConstantInterface.class, HttpRemoteUtil.class, BSmtBomHeaderService.class,PlanscheduleRemoteService.class,JacksonJsonConverUtil.class})
public class PsWipInfoServiceImplThrTest extends PowerBaseTestCase {

    @InjectMocks
    private PsWipInfoServiceImpl service;

    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;
    @Mock
    private WarehouseEntryInfoService warehouseEntryInfoService;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private RedisLock redisLock;

    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private WipRepairSnService wipRepairSnService;

    @Mock
    private PsScanHistoryService psScanHistoryService;

    @Mock
    BsWorkTimeSectionService bsWorkTimeSectionService;

    @Mock
    PsOutputInfoService psOutputInfoService;

    @Mock
    PsBarcodeControlInfoService psBarcodeControlInfoService;

    @Mock
    JsonMapper mapper;

    @Mock
    JsonNode jsonNode;

    @Mock
    PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private PsCommonScanService psCommonScanService;

    @Mock
    private WipInfoDelLogService wipInfoDelLogService;
    @Mock
    PsWorkOrderSnAssignRepository psWorkOrderSnAssignRepository;
    @Test
    public void delWipInfoForTaskQtyChange() throws Exception {
        DealPsWipInfoParamDTO dealPsWipInfoParamDTO = new DealPsWipInfoParamDTO();
        try {
            service.delWipInfoForTaskQtyChange(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        try {
            service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        try {
            dealPsWipInfoParamDTO.setTaskNo("taskNo");
            service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        dealPsWipInfoParamDTO.setTaskQty(1);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        Assert.assertNotNull(dealPsWipInfoParamDTO);
        dealPsWipInfoParamDTO.setProducedQty(1);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        Assert.assertNotNull(dealPsWipInfoParamDTO);
        PowerMockito.when(psWipInfoRepository.getTaskPrintingQuantity(any())).thenReturn(1);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        dealPsWipInfoParamDTO.setDelSns("123,123,123,124");
        PowerMockito.when(psWipInfoRepository.getTaskPrintingQuantity(any())).thenReturn(1);
        try {
            service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_SNS_MORE_THAN_NEED_DEL, e.getMessage());

        }
        dealPsWipInfoParamDTO.setProducedQty(5);
        List<PsWipInfo> psWipInfoList = new ArrayList<>();
        psWipInfoList.add(new PsWipInfo());
        PowerMockito.when(psWipInfoRepository.getCouldDelSns(any(), any())).thenReturn(psWipInfoList);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        PowerMockito.when( wipInfoDelLogService.batchInsertByWipInfoList(any(), any())).thenReturn(5);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        Assert.assertNotNull(dealPsWipInfoParamDTO);
        dealPsWipInfoParamDTO.setTaskQty(2000);
        PowerMockito.when( wipInfoDelLogService.batchInsertByWipInfoList(any(), any())).thenReturn(3);
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);
        Assert.assertNotNull(dealPsWipInfoParamDTO);
        dealPsWipInfoParamDTO.setDelSns("123");
        service.delWipInfoForTaskQtyChange(dealPsWipInfoParamDTO);

    }

    @Test
    public void pmBatchPassScanNew() throws Exception {
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PmSubmitConditionDTO pmSubmitConditionDTO = new PmSubmitConditionDTO() {{
            setBatchPassType("0");
            setSnStart("77777771");
            setSnEnd("77777772");
        }};
        try {
            service.pmBatchPassScanNew(pmSubmitConditionDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
        }

        pmSubmitConditionDTO.setWorkOrderNo("7777777");
        List<PsEntityPlanBasicDTO> psEntityPlanInfo = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setAttribute1("122");
        a1.setSourceTask("7777777");
        a1.setWorkOrderNo("7777777-SMT-B5202");
        a1.setCraftSection("SMT-B");
        a1.setProcessGroup("2");
        a1.setWorkOrderStatus("已提交");
        a1.setWorkOrderQty(new BigDecimal(20));
        psEntityPlanInfo.add(a1);
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(psEntityPlanInfo);
        PowerMockito.when(psWorkOrderSnAssignRepository.getPrintSnRecordCount(Mockito.any())).thenReturn(0L);
        try {
            service.pmBatchPassScanNew(pmSubmitConditionDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("已开工");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(LOOKUP_VALUE_6691, LOOKUP_VALUE_6691001))
                .thenReturn(sysLookupTypesDTO);
        try {
            service.pmBatchPassScanNew(pmSubmitConditionDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_STATUS_MUST_BE_FOLLOWS, e.getMessage());
        }

        sysLookupTypesDTO.setLookupMeaning("已提交");
        List<PsWipInfo> list = new LinkedList<>();
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(list);
        try {
            service.pmBatchPassScanNew(pmSubmitConditionDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLANID_IS_NOT_EXITS, e.getMessage());
        }

        List<PsWorkOrderDTO> basicWorkList = new LinkedList<>();
        PsWorkOrderDTO b1 = new PsWorkOrderDTO();
        b1.setRemark("1");
        b1.setWorkOrderNo("7777777-SMT-B5202");
        basicWorkList.add(b1);
        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfo(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(basicWorkList);
        service.pmBatchPassScanNew(pmSubmitConditionDTO);

        a1.setWorkOrderQty(new BigDecimal(1));
        service.pmBatchPassScanNew(pmSubmitConditionDTO);

        PsWorkOrderDTO b2 = new PsWorkOrderDTO();
        b2.setRemark("0");
        b2.setWorkOrderNo("7777777-SMT-A5201");
        basicWorkList.add(b2);
        service.pmBatchPassScanNew(pmSubmitConditionDTO);

        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(null);
        service.pmBatchPassScanNew(pmSubmitConditionDTO);

        pmSubmitConditionDTO.setBatchPassType("3");
        try{
            service.pmBatchPassScanNew(pmSubmitConditionDTO);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void calculateMaintenanceQty() throws Exception {
        List<String> craftSectionList = new ArrayList<>();
        for (int i = 0; i < 1002; i++) {
            craftSectionList.add(i + "");
        }
        try {
            service.calculateMaintenanceQty(craftSectionList,
                    Lists.newArrayList(""), "", "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.OPERATION_CANNOT_EXCEED_1000, e.getExMsgId());
        }
        try {
            service.calculateMaintenanceQty(new ArrayList<>(),
                    Lists.newArrayList(""), "", "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.OPERATION_CANNOT_EXCEED_1000, e.getExMsgId());
        }
        service.calculateMaintenanceQty(Lists.newArrayList(""), new ArrayList<>(), "", "");
        List<WipDailyStatisticReport> wipDailyStatisticReportList = new ArrayList<>();
        WipDailyStatisticReport wipDailyStatisticReportEntityDTO = new WipDailyStatisticReport();
        wipDailyStatisticReportEntityDTO.setProdplanId("7");
        wipDailyStatisticReportList.add(wipDailyStatisticReportEntityDTO);
        PowerMockito.when(psWipInfoRepository.batchMaintenanceDataStatistics(anyList(), anyList(), anyString(), anyString())).thenReturn(wipDailyStatisticReportList);
        service.calculateMaintenanceQty(Lists.newArrayList(""), Lists.newArrayList(""), "", "");
        try {
            service.calculateMaintenanceQty(Lists.newArrayList(""),
                    Lists.newArrayList(""), "", "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.OPERATION_CANNOT_EXCEED_1000, e.getExMsgId());
        }
    }

    @Test
    public void getSnCountByWorkOrder() {
        try {
            service.getSnCountByWorkOrder(new HashMap<>(), Lists.newArrayList(""));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BARCODE_AVAILABLE_IN_LPN,e.getMessage());
        }
    }

    @Test
    public void getScanSnList() {
        Assert.assertNotNull(service.getScanSnList("1"));
    }

    @Test
    public void setContainerCode() throws Exception{
        PowerMockito.mockStatic(MicroServiceRestUtil.class,JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap())).thenReturn(null);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(new ArrayList<ContainerContentInfoDTO>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        service.setContainerCode(
                Lists.newArrayList(new PsWipInfoSnQueryDTO()),
                "123");
    }

    @Test
    public void getWipInfo() {
        Assert.assertNotNull(service.getWipInfo(new HashMap<>()));
    }

    @Test
    public void getWipInfoBatch() {
        Assert.assertNotNull(service.getWipInfoBatch(Lists.newArrayList(new BarcodeDTO())));
    }

    @Test
    public void updateWipInfoBySns() throws Exception {
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setSn("1");
        a1.setParentSn("1");
        list.add(a1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(list);
        service.updateWipInfoBySns(new PsWipInfoDTO());
        service.updateWipInfoBySns(new PsWipInfoDTO() {{
            setInSns("1");
            setUpDateCount(2);
        }});
        service.updateWipInfoBySns(new PsWipInfoDTO() {{
            setInSns("1");
            setInParentSns("1");
        }});
        Assert.assertEquals(0,service.updateWipInfoBySns(new PsWipInfoDTO() {{
            setInParentSns("1");
            setUpDateCount(2);
        }}));
    }

    @Test
    public void addPsWorkOrder() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1");
                    }}));
                }}));

        service.addPsWorkOrder(null,
                Lists.newArrayList(new ContainerContentInfoDTO()));
        service.addPsWorkOrder(Lists.newArrayList(new PsWorkOrderDTO()),
                null);
        service.addPsWorkOrder(null,
                null);
        service.addPsWorkOrder(Lists.newArrayList(),
                Lists.newArrayList(new ContainerContentInfoDTO(),
                        new ContainerContentInfoDTO() {{
                            setTaskBelongsTo("1");
                        }},
                        new ContainerContentInfoDTO() {{
                            setTaskBelongsTo("1");
                        }},
                        new ContainerContentInfoDTO() {{
                            setTaskBelongsTo("2");
                        }}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getWorkOrderNoInfo() throws Exception {
        service.getWorkOrderNoInfo(new PsWipInfoDTO());
        service.getWorkOrderNoInfo(new PsWipInfoDTO() {{
            setLpn("1");
            setSn("1");
            setWorkOrderNo("1");
            setAttribute1("1");
        }});
        PowerMockito.mockStatic(MicroServiceRestUtil.class,JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);

//        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any())).thenReturn(new ArrayList<PsWorkOrderDTO>());
        PowerMockito.when(psWipInfoRepository.getList(any())).thenReturn(Lists.newArrayList((
                new PsWipInfo() {{
                    setWorkOrderNo("1234567");
                }}
        )));
        PowerMockito.when(psWipInfoRepository.getWorkOrderNoByCond(any())).thenReturn(Lists.newArrayList((
                "1234567"
        )));
            service.getWorkOrderNoInfo(new PsWipInfoDTO() {{
                setCurrProcessCode("q");
                setWorkStation("1");
                setLpn("1");
                setSn("1");
                setWorkOrderNo("1234567");
                setAttribute1("1234567");
            }});


            service.getWorkOrderNoInfo(new PsWipInfoDTO() {{
                setCurrProcessCode("q");
                setWorkStation("1");
                setLpn("1");
                setAttribute1("1234567");
            }});
        try {
            service.getWorkOrderNoInfo(new PsWipInfoDTO() {{
                setCurrProcessCode("q");
                setWorkStation("1");
                setLpn("1");
            }});
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void pushModel() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new HashMap() {{
                        put("listDetail", Lists.newArrayList(new CtRouteDetailDTO() {{
                            setNextProcess("1");
                        }}));
                        put("nextProcess", "1");
                        put("currProcess", "1");
                        put("processName", "n1");
                        put("processGroup", "1");
                        put("workOrderNo", "w1");
                        put("lineCode", "l1");
                    }}));
                }}));
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString()))
                .thenReturn(Lists.newArrayList(
                        new SysLookupValuesDTO() {{
                            setLookupMeaning("Y");
                            setLookupCode(new BigDecimal(MpConstant.LOOKUP_VALUE_DIP_FINISH));
                        }}));
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.PROCESS_CODE_IS_NOT_EXISTED);
            service.getPtModel("1");
            service.getCheckDipFinish(Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                setCraftSection(MpConstant.PROCESS_NAME_DIP);
            }}));
            service.pushModel(
                    new PcProcessTransferDTO(),
                    new PcProcessTransferSimpleDTO(),
                    Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setProcessGroup("3$(*)2");
                    }}),
                    ""
            );
            service.pushModel(new PcProcessTransferDTO(),
                    new PcProcessTransferDTO(),
                    Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setProcessGroup("3$(*)2");
                    }}),
                    ""
            );
            service.pullModel(
                    new PcProcessTransferDTO() {{
                        setIsBoxupFlag("");
                    }},
                    "",
                    new PcProcessTransferSimpleDTO(),
                    Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setProcessGroup("1$(*)2");
                    }}),
                    ""
            );
            service.pullModel(new PcProcessTransferDTO(),
                    new PcProcessTransferDTO(),
                    Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setProcessGroup("1$(*)2");
                    }}),
                    ""
            );
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void pushModel1() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn(MessageId.WORDER_ORDER_NOT_FOUND);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new HashMap() {{
                        put("listDetail", Lists.newArrayList(new CtRouteDetailDTO() {{
                            setNextProcess("1q");
                        }}));
                        put("nextProcess", "1q");
                        put("currProcess", "1q");
                        put("processName", "n1q");
                        put("processGroup", "1q");
                        put("workOrderNo", "w1q");
                        put("lineCode", "l1q");
                    }}));
                }}));
        PcProcessTransferDTO dto = new PcProcessTransferDTO();
        dto.setTransferBySn("Y");

        List<PsEntityPlanBasicDTO> psEntityPlanInfo = Lists.newArrayList(new PsEntityPlanBasicDTO() {{
            setProcessGroup("3$(*)2");
        }});
        service.pushModel(dto, new PcProcessTransferSimpleDTO(), psEntityPlanInfo, "");
        Assert.assertEquals("Y", dto.getTransferBySn());

        dto.setNotInQcFlag("Y");
        service.pushModel(dto, new PcProcessTransferSimpleDTO(), psEntityPlanInfo, "");
        Assert.assertEquals("Y", dto.getTransferBySn());

        dto.setNotInQcFlag("N");
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new HashMap() {{
                        put("listDetail", Lists.newArrayList(new CtRouteDetailDTO() {{
                            setNextProcess("1q");
                        }}));
                        put("nextProcess", "1q");
                        put("currProcess", "1q");
                        put("processName", "n1q");
                        put("processGroup", "1q");
                        put("workOrderNo", "w1q");
                        put("lineCode", "l1q");
                    }}));
                }}))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }}));
        PcProcessTransferSimpleDTO pcProcessTransferSimpleDTO = service.pushModel(dto, new PcProcessTransferSimpleDTO(), psEntityPlanInfo, "");
        Assert.assertEquals("work.order.not.found", pcProcessTransferSimpleDTO.getReturnMessage());
    }

    @Test
    public void remote() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, ConstantInterface.class, HttpRemoteUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }}));
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new Object());
                }}));

        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1");
        service.getPsEntityPlanBasic("", "", "", "");
        service.getRouteHeadInfo("");
        service.getListPsTask("");
        service.getListPsTask("1");
        service.getProcessInfo("1", 1);
        service.getPsEntityPlanInfo("");
        service.getPsEntityPlanInfo("1");
        service.getCtRouteDetailInfo("", "", "", "");
        service.getProcessInfo("1", "", "");
        service.getProcessInfo("", "2", "");
        service.getProcessInfo("", "", "3");
        service.updateTaskQtyOrStatus("", 1);
        service.updateTaskQtyOrStatus("1", 1);
        service.getbarcodeType("1");
        service.getPsEntityPlanBasic("");
        service.updatePsWorkorderQty(new PsEntityPlanBasicDTO());
        service.updateWorkOrder(new PmSubmitConditionDTO(), "Y");
        service.updateWorkOrder(new PmSubmitConditionDTO(), "N");
        service.updateWorkOrderOutPut(Lists.newArrayList(new PsWipInfoDTO()));
        service.queryContentInfoBySn("");
        service.getProcessCodeBylineAndStationInfo("", "");
        service.getEqpInfoByMac("");
        service.getAutoTestProcess("", "");
        service.getAutoTestProcess("1", "");
        service.getAutoTestProcess("1", "2");
        service.getPreProcess("", "1");
        service.getPreProcess("1", "");
        try {
            service.plcExcuteRelease(new PmScanConditionDTO(), "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new Object());
                }}));
        service.getContainerContentInfo(new ContainerContentInfoDTO());
        service.checkConContent(new PackScanBussDTO());
        service.insertEventLogForPackingScan("", new PackScanBussDTO());

        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new PageRows() {{
                        setRows(Lists.newArrayList());
                    }});
                }}));
        service.getFactory("");
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(1);
                }}));
        service.deleteContentInfoBySn("");
    }

    @Test
    public void basicScan() throws JsonProcessingException {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class,JacksonJsonConverUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsWorkOrderDTO() {{
                        setWorkOrderNo("1234567");
                    }}));
                }}));
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        try {
            service.basicScan(new BSProcessInfoDTO() {{
                setRegularExpression("1");
                setInputParameterValue("2");
            }});
            service.basicScan(new BSProcessInfoDTO() {{
                setProgramName("1$1$2$1");
            }});
            BasicScanExDTO basicScanExDTO = new BasicScanExDTO();
            basicScanExDTO.setInputParameter(Constant.WORK_ORDER_NO);
            basicScanExDTO.setLowCase(true);
            service.setResult(basicScanExDTO, new BSProcessInfoDTO(), new PsWorkOrderDTO(),
                    new BsPubHrvOrgIdDTO(), "");
            basicScanExDTO.setInputParameter(Constant.USER_ID);
            basicScanExDTO.setLowCase(true);
            service.setResult(basicScanExDTO, new BSProcessInfoDTO(), new PsWorkOrderDTO(),
                    new BsPubHrvOrgIdDTO(), "");
            basicScanExDTO.setInputParameter(Constant.LPN);
            basicScanExDTO.setLowCase(true);
            service.setResult(basicScanExDTO, new BSProcessInfoDTO(), new PsWorkOrderDTO(),
                    new BsPubHrvOrgIdDTO(), "");
            basicScanExDTO.setInputParameter("");
            basicScanExDTO.setLowCase(false);
            service.setResult(basicScanExDTO, new BSProcessInfoDTO(), new PsWorkOrderDTO(),
                    new BsPubHrvOrgIdDTO(), "");

            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO, Lists.newArrayList());
            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO, Lists.newArrayList(new PsWorkOrderDTO()));
            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO, Lists.newArrayList(new PsWorkOrderDTO() {{
                setWorkOrderStatus(Constant.IS_START);
                setCraftSection(Constant.BOARD_TEST);
                setProcessCode(Constant.BOARD_TEST_INPUT);
                setInputQty(new BigDecimal(1));
                setWorkOrderQty(new BigDecimal(1));
            }}));
            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO, Lists.newArrayList(new PsWorkOrderDTO() {{
                setWorkOrderStatus(Constant.IS_START);
                setCraftSection(Constant.BOARD_TEST);
                setProcessCode(Constant.BOARD_TEST_INPUT);
                setInputQty(new BigDecimal(1));
                setWorkOrderQty(new BigDecimal(10));
            }}));
            basicScanExDTO.setSkipSequence(0);
            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO,
                    Lists.newArrayList(new PmKeyValueDTO()), json);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_ERROR, e.getMessage());
        }
        try {
            BasicScanExDTO basicScanExDTO = new BasicScanExDTO();
            basicScanExDTO.setSkipSequence(1);
            service.getBsProcessInfoDTO(new BSProcessInfoDTO(), basicScanExDTO,
                    Lists.newArrayList(new PmKeyValueDTO()), json);

            BasicScanExDTO basicScanExDTO1 = new BasicScanExDTO();
            basicScanExDTO1.setSkipSequence(1);
            service.getBsProcessInfoDTO(new BSProcessInfoDTO() {{
                                            setaProcessCode("1");
                                        }}, basicScanExDTO1,
                    Lists.newArrayList(new PmKeyValueDTO()), json);
            service.getReturnInfo(new BSProcessInfoDTO() {{
                                      setIsTheLast("Y");
                                  }}, 1, "", "",
                    Lists.newArrayList(new PmKeyValueDTO()));
            service.getReturnInfo(new BSProcessInfoDTO() {{
                                      setaProcessCode("1");
                                  }}, 1, "", "",
                    Lists.newArrayList(new PmKeyValueDTO()));
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), any(), any(), any(), any(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
                }}));
            service.basicScan(new BSProcessInfoDTO() {{
                setProgramName("1$1$2$1");
                setUserId("1");
                setSourceBimu(new BigDecimal("1"));
                setSourceImu(new BigDecimal("1"));
                setaSourceBimu(new BigDecimal("1"));
                setaSourceImu(new BigDecimal("1"));
                setInputParameter(Constant.WORK_ORDER_NO);
            }});
            service.basicScan(new BSProcessInfoDTO() {{
                setProgramName("1$1$2$1");
                setUserId("1");
                setSourceBimu(new BigDecimal("1"));
                setSourceImu(new BigDecimal("1"));
                setaSourceBimu(new BigDecimal("1"));
                setaSourceImu(new BigDecimal("1"));
                setInputParameter(Constant.USER_ID);
            }});
            service.basicScan(new BSProcessInfoDTO() {{
                setProgramName("1$1$2$1");
                setUserId("1");
                setSourceBimu(new BigDecimal("1"));
                setSourceImu(new BigDecimal("1"));
                setaSourceBimu(new BigDecimal("1"));
                setaSourceImu(new BigDecimal("1"));
                setInputParameter(Constant.LPN);
            }});
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void checkFlowControled() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, BasicsettingRemoteService.class, CrafttechRemoteService.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setSourceTask("7777777");
                        setWorkOrderQty(new BigDecimal(1));
                    }}));
                }}));
        PowerMockito.when(BasicsettingRemoteService.getLookupValueByTypeCodes(anyString())).thenReturn(Lists.newArrayList(new SysLookupValuesDTO() {{
            setLookupCode(new BigDecimal(MpConstant.LOOKUP_CODE_BATCH_SIZE_ONE));
            setLookupMeaning("10");
        }}));
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("1");
        FlowControlConditionDTO params =
        new FlowControlConditionDTO() {{
            setLineCode("1");
            setCurrProcessCode("1");
            setCraftSection("1");
        }};
        service.checkFlowControled(params);

        params.setSnList(new LinkedList<>());
        Assert.assertNotNull(service.checkFlowControled(params));
    }

    @Test
    public void updateWipInfoBySnsBatch() throws Exception {
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setSn("1");
        a1.setParentSn("1");
        list.add(a1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(list);
        List<PsWipInfoDTO> dtos = new ArrayList<>();
        dtos.add(new PsWipInfoDTO() {{
            setInSns("1");
            setUpDateCount(2);
        }});
        Assert.assertEquals(0,service.updateWipInfoBySnsBatch(dtos));
    }


    @Test
    public void returningUpdate() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, BasicsettingRemoteService.class, BSmtBomHeaderService.class);

        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PsEntityPlanBasicDTO() {{
                        setRouteId("1");
                        setInputQty(BigDecimal.TEN);
                    }}));
                }}));
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<PsWipInfo> list = new LinkedList<>();
        PsWipInfo a1 = new PsWipInfo();
        a1.setSn("1");
        a1.setParentSn("1");
        list.add(a1);
        PowerMockito.when(psWipInfoRepository.getList(Mockito.anyMap())).thenReturn(list);
        List<PsWipInfoDTO> dtos = new ArrayList<>();
        dtos.add(new PsWipInfoDTO() {{
            setInSns("1");
            setUpDateCount(2);
            setAttribute1("Attribute1");
        }});

        List<SysLookupValuesDTO> settings = new ArrayList<>();
        PowerMockito.when(BasicsettingRemoteService.getSettings(anyObject())).thenReturn(settings);
        PowerMockito.when(BasicsettingRemoteService.getSetting(settings, MpConstant.LOOKUP_LAST_PROCESS_ZP)).thenReturn("P0227");

        List<PsScanHistory> psScanHistories = new ArrayList<>();
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistories.add(psScanHistory);
        psScanHistory.setWorkOrderNo("111");
        psScanHistory.setLastProcess("Y");
        psScanHistory.setCurrProcessCode("P0227");
        psScanHistory.setAttribute1("Attribute1");
        PowerMockito.when(psScanHistoryRepository.getList(anyMap())).thenReturn(psScanHistories);

        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setCraftSection(MpConstant.PACKAGING);
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn(any())).thenReturn(psWipInfo);

        try {
            service.returningUpdate(dtos);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_WORK_ORDER_FAILURE, e.getMessage());
        }
    }

    public void inOutQtyThenUpdate() throws Exception {
        List<PsEntityPlanBasicDTO> entityPlanBasicDTOS = new LinkedList<>();
        PsEntityPlanBasicDTO a1 = new PsEntityPlanBasicDTO();
        a1.setScheduleEndDate(new Date());
        a1.setScheduleStartDate(new Date());
        a1.setInputQtyForAdd(new BigDecimal(1));
        a1.setOutputQtyForAdd(new BigDecimal(2));
        a1.setActualStartDate(new Date());
        a1.setWorkOrderStatus("2");
        entityPlanBasicDTOS.add(a1);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(
                "{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"操作成功\"\n" +
                        "    },\n" +
                        "    \"bo\": []\n" +
                        "}")
        ;

        service.inOutQtyThenUpdate("123");
        Assert.assertEquals("2", a1.getWorkOrderStatus());
    }

    @Test
    public void batchPassScanForMDS() throws Exception {
        PmSubmitConditionDTO entity = new PmSubmitConditionDTO();
        List<String> snList = new ArrayList<>();
        snList.add("7011705000011");
        snList.add("701170500002");
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LIST_IS_NULL, e.getMessage());
        }

        ReflectionTestUtils.setField(service, "batchPassSnListMax", 1);
        entity.setSnList(snList);
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_LIST_EXCEED_MAX, e.getMessage());
        }

        ReflectionTestUtils.setField(service, "batchPassSnListMax", 3);
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_12_NUMBERS, e.getMessage());
        }

        snList.clear();
        snList.add("A01170500001");
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_12_NUMBERS, e.getMessage());
        }

        snList.clear();
        snList.add("701170500001");
        snList.add("7011705000021");
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_12_NUMBERS, e.getMessage());
        }

        snList.clear();
        snList.add("701170500001");
        snList.add("701170600002");
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_ID_IS_DIFFERENT, e.getMessage());
        }

        snList.clear();
        snList.add("701170500001");
        snList.add("701170500002");
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(new ArrayList<>());
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }

        List<PsEntityPlanBasicDTO> workOrderInfoList = new ArrayList() {{
            add(new PsEntityPlanBasicDTO() {{
                setWorkOrderQty(new BigDecimal("10"));
            }});
        }};
        PowerMockito.when(planscheduleRemoteService.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(workOrderInfoList);
        snList.clear();
        snList.add("701170500001");
        snList.add("701170500012");
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXCEED_WORK_QTY, e.getMessage());
        }

        snList.clear();
        snList.add("701170500001");
        snList.add("701170500002");
        PowerMockito.mockStatic(CrafttechRemoteService.class,BasicsettingRemoteService.class);
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(null);
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_NOT_EXIST, e.getMessage());
        }

        BSProcess bSProcess = new BSProcess(){{
            setProcessName("DIP");
        }};
        PowerMockito.when(CrafttechRemoteService.getBSProcessInfo(any())).thenReturn(bSProcess);
        Map<String,Object> lookupType = new HashMap<>();
        lookupType.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_VALUE_2038);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupType))
                .thenReturn(new ArrayList<>());
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("SMT");
        types.add(typesDTO);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupType))
                .thenReturn(types);
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_MUST_EXIST_IN_2038, e.getMessage());
        }
        typesDTO.setLookupMeaning("DIP");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(lookupType))
                .thenReturn(types);
        try {
            service.batchPassScanForMDS(entity);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_INFO_IS_NULL, e.getMessage());
        }
    }

    /* Started by AICoder, pid:88f75q89a7790bd144c0084ad0e55f2c9a52f119 */
    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(service, "setMBom", null);
        Assert.assertTrue(1==1);
        List<WorkOrderInfo> list = new ArrayList<>();
        WorkOrderInfo entity = new WorkOrderInfo();
        entity.setProdplanId("1234567");
        list.add(entity);
        WorkOrderInfo entity1 = new WorkOrderInfo();
        entity1.setProdplanId("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(centerfactoryRemoteService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(service, "setMBom", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertTrue(list.get(1).getMbom().equals("itemNo"));
    }
    /* Ended by AICoder, pid:88f75q89a7790bd144c0084ad0e55f2c9a52f119 */
}