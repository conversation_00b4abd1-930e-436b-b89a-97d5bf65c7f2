package com.zte.test;

import com.zte.application.BSmtBomDetailService;
import com.zte.application.SmtMachineMTLHistoryHService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.impl.SmtMachineMTLHistoryLServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.SmtMachineMTLHistoryLDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({ProductionDeliveryRemoteService.class, PlanscheduleRemoteService.class})
public class SmtMachineMTLHistoryLServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private SmtMachineMTLHistoryLServiceImpl smtMachineMTLHistoryLServiceImpl;

    @Mock
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;

    private RetCode retCode;
    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;
    private ServiceData serviceData;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingServicec;
    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private SmtMachineMTLHistoryHService smtMachineMTLHistoryHService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    ValueOperations<String, Object> valueOperationsImpl;

    @Before
    public void init(){
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }

    @Test
    public void checkIsScanFinishWhenPreTogether()throws Exception{
        List<BSmtBomDetail> dList=new ArrayList<>();
        SmtMachineMTLHistoryL record=new SmtMachineMTLHistoryL();
        List<String> trayList=new ArrayList<>();
        String headerId="";
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        record.setMountType("8");
        record.setWorkOrder("7000945-SMT-A5301");
        List<PsWorkOrderBasicDTO> orderList=new ArrayList<>();
        PsWorkOrderBasicDTO orderBasicDTO1=new PsWorkOrderBasicDTO();
        orderBasicDTO1.setCraftSection("SMT-A");
        orderBasicDTO1.setLineCode("SMT-61");
        orderBasicDTO1.setWorkOrderNo("7000945-SMT-A5301");
        orderList.add(orderBasicDTO1);
        PowerMockito.when(PlanscheduleRemoteService.queryWorkOrderList(Mockito.any())).thenReturn(orderList);
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        record.setmIsBothSides(true);
        record.setWorkOrder("");
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        record.setWorkOrder("7000945-SMT-A5301");
        PsWorkOrderBasicDTO orderBasicDTO2=new PsWorkOrderBasicDTO();
        orderBasicDTO2.setCraftSection("SMT-A");
        orderBasicDTO2.setLineCode("SMT-62");
        orderBasicDTO2.setWorkOrderNo("7000945-SMT-A5301");
        orderList.add(orderBasicDTO2);
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        orderBasicDTO1.setLineCode("SMT-62");
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        orderBasicDTO2.setWorkOrderNo("7000945-SMT-B5302");
        orderBasicDTO2.setCfgHeaderId("1232");
        List<BSmtBomDetail> otherBomList=new ArrayList<>();
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(Mockito.any())).thenReturn(otherBomList);
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        BSmtBomDetail smtBomDetail=new BSmtBomDetail();
        otherBomList.add(smtBomDetail);
        smtBomDetail.setMachineNo("machineNo");
        smtBomDetail.setModuleNo("moduleNo");
        smtBomDetail.setLocationNo("locationNo");
        smtBomDetail.setItemCode("itemcode");
        BSmtBomDetail smtBomDetail2=new BSmtBomDetail();
        otherBomList.add(smtBomDetail2);
        smtBomDetail2.setMachineNo("machineNo2");
        smtBomDetail2.setModuleNo("moduleNo2");
        smtBomDetail2.setLocationNo("locationNo2");
        smtBomDetail2.setItemCode("itemcode2");
        smtBomDetail2.setMaterialTray("reelid2");
        PowerMockito.when(bSmtBomDetailService.getModuleBSmtBomDetailList(Mockito.any())).thenReturn(otherBomList);
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        //dList不为空
        BSmtBomDetail dDto1=new BSmtBomDetail();
        dDto1.setItemCode("itemcode");
        dDto1.setMachineNo("machineNo");
        dDto1.setModuleNo("moduleNo");
        dDto1.setLocationNo("locationNo");
        dDto1.setMaterialTray("reelid");
        dList.add(dDto1);
        BSmtBomDetail dDto2=new BSmtBomDetail();
        dDto2.setItemCode("itemcode3");
        dDto2.setMachineNo("machineNo2");
        dDto2.setModuleNo("moduleNo2");
        dDto2.setLocationNo("locationNo2");
        dList.add(dDto2);
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        dDto2.setItemCode("itemcode2");
        smtMachineMTLHistoryLServiceImpl.checkIsScanFinishWhenPreTogether(dList,record,trayList,headerId);
        Assert.assertEquals("itemcode",dDto1.getItemCode());
    }

    @Test
    public void checkReelId() throws Exception {
        SmtMachineMTLHistoryLServiceImpl serivce = new SmtMachineMTLHistoryLServiceImpl();
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        record.setLineId("1");
        List<SmtMachineMTLHistoryL> reslutList = new ArrayList<SmtMachineMTLHistoryL>();
        reslutList.add(record);
        retCode = PowerMockito.mock(RetCode.class);
        serviceData = PowerMockito.mock(ServiceData.class);
        PowerMockito.whenNew(RetCode.class).withArguments(anyString(), anyString()).thenReturn(retCode);
        PowerMockito.whenNew(ServiceData.class).withAnyArguments().thenReturn(serviceData);
        when(smtMachineMTLHistoryLRepository.getReelIdHistory(anyObject())).thenReturn(reslutList);
        Assert.assertEquals("1", record.getLineId());
    }


    @Test
    public void insertSmtMachineMTLHistoryHeadAndDetail() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class);
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        record.setCombination("Y");
        Assert.assertEquals(0,smtMachineMTLHistoryLServiceImpl.insertSmtMachineMTLHistoryHeadAndDetail(record));
    }

    @Test
    public void insertSmtMachineMTLHistoryAll() throws MesBusinessException {
        SmtMachineMTLHistoryL record = new SmtMachineMTLHistoryL();
        record.setObjectId("123");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperationsImpl);
        List<PkCodeInfo> pkCodeList = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        a1.setPkCode("123");
        pkCodeList.add(a1);
        PowerMockito.when(pkCodeInfoRepository.getList(Mockito.any())).thenReturn(pkCodeList);
        PowerMockito.when(valueOperationsImpl.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.any())).thenReturn(true);
        Assert.assertNotNull(smtMachineMTLHistoryLServiceImpl.insertSmtMachineMTLHistoryAll(record));

    }

    @Test
    public void getTimeRangeTest() throws Exception {
        SmtMachineMTLHistoryLDTO dto = new SmtMachineMTLHistoryLDTO();
        SmtMachineMTLHistoryL entity = new SmtMachineMTLHistoryL();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        entity.setCreateDate(sf.parse("2019-06-19 19:59:22"));
        entity.setLastUpdatedDate(sf.parse("2019-06-28 11:07:06"));
        PowerMockito.when(smtMachineMTLHistoryLRepository.getMinAndMaxCreateDate(Mockito.anyObject())).thenReturn(entity);
        Assert.assertNotNull(smtMachineMTLHistoryLServiceImpl.getTimeRange(dto));
    }
}
