package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.BSmtBomHeaderService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.impl.BSmtBomHeaderServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)

@PrepareForTest({CollectionUtils.class, MicroServiceRestUtil.class, JsonConvertUtil.class, ExcelUtil.class, Constant.class,
        ExcelName.class, BasicsettingRemoteService.class, RedisHelper.class,Workbook.class,
        SpringContextUtil.class, PlanscheduleRemoteService.class, CommonUtils.class,
        HttpRemoteService.class, RedisLock.class})
public class BSmtBomHeaderServiceImplTest extends PowerBaseTestCase {

    private RetCode retCode;

    private ServiceData serviceData;


    @InjectMocks
    private BSmtBomHeaderServiceImpl bSmtBomHeaderServiceImpl;
    @Mock
    private BSmtBomHeaderService service;

    @Mock
    private Workbook wb;
    @Mock
    private BSmtBomHeaderRepository bSmtBomHeaderRepository;

    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;

    @Mock
    private RedisLock redisLock;
    @Mock
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, String> redisOpsValue;
    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;
    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Mock
    private Workbook workbook;
    @Mock
    private Sheet sheet;
    @Mock
    private Row row;
    @Mock
    private Cell cell;
    @Mock
    private DataFormatter dataFormatter;
    @Mock
    BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private PkCodeHistoryService pkCodeHistoryService;
    @Mock
    CloudDiskHelper cloudDiskHelper;

    @Test
    public void deleteOrupdateOrInsertBsmtInfo() throws Exception {
        BSmtBomHeaderDTO dto = new BSmtBomHeaderDTO();
        dto.setWorkOrderNo("777888512-DIP444");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.deleteOrupdateOrInsertBsmtInfo(dto));
    }

    @Test
    public void downExcel() throws Exception {
        BSmtBomHeaderServiceImpl service = PowerMockito.spy(new BSmtBomHeaderServiceImpl());
        service.setbSmtBomHeaderRepository(bSmtBomHeaderRepository);
        service.setBSmtBomDetailRepository(bSmtBomDetailRepository);
        List<BSmtBomHeader> dataList = new ArrayList<BSmtBomHeader>();
        BSmtBomHeader dataListDetail = new BSmtBomHeader();
        dataListDetail.setFactoryId(new BigDecimal(55));
        dataListDetail.setCfgHeaderId("1616e9c2-b57b-4a4f-8bf0-a8652b96c6a4");
        dataListDetail.setCraftSection("SMT-B");
        dataListDetail.setCreateUser("00263453");
        dataList.add(dataListDetail);
        PowerMockito.when(bSmtBomHeaderRepository.getList(anyObject())).thenReturn(dataList);
        PowerMockito.mockStatic(CollectionUtils.class);
        PowerMockito.mockStatic(Constant.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(ExcelName.class);
        PowerMockito.mockStatic(ExcelUtil.class);
        PowerMockito.when(CollectionUtils.isEmpty(dataList)).thenReturn(false);
        List<BSmtBomDetail> itemList = new ArrayList<>();
        BSmtBomDetail itemListDetail = new BSmtBomDetail();
        itemListDetail.setItemCode("132010700031");
        itemList.add(itemListDetail);
        PowerMockito.when(bSmtBomDetailRepository.getList(anyObject())).thenReturn(itemList);
        List<BsItemInfo> style = new ArrayList<>();
        BsItemInfo styleDetail = new BsItemInfo();
        styleDetail.setStyle("FSDG");
        styleDetail.setItemNo("132010700031");
        style.add(styleDetail);
        Map<String, String> headerParamsMap = new HashMap<>();

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JsonConvertUtil.class);
        //PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.spy(JsonConvertUtil.class);

        ObjectMapper mapper = new ObjectMapper();
        String jsonNew = mapper.writeValueAsString("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"shrehe\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsItemInfoController@getInfoList\",\"code\":\"0000\",\"costTime\":\"1710ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Oct 29 21:31:09 CST 2019\",\"tag\":\"查询物料信息通用方法\",\"serviceName\":\"zte-mes-manufactureshare-basicsettingsysczjj\",\"userId\":null}}");
        JsonNode json = mapper.readTree(jsonNew);
        String bo = "[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"shrehe\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}]";
        String getresult = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"itemId\":\"uluo7o\",\"itemNo\":\"106010200020\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"shrehe\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"fgjtykiy\",\"itemNo\":\"132010700031\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"set\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"kuily\",\"itemNo\":\"146050300063\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"dfed\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"rtutyk\",\"itemNo\":\"045020200257\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"gesdg\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null},{\"itemId\":\"tryr\",\"itemNo\":\"003070100027\",\"version\":null,\"sourceItemId\":0,\"sourceSystem\":null,\"itemDesc\":null,\"unit\":null,\"productClass\":null,\"productSmlclass\":null,\"style\":\"tewte\",\"itemName\":null,\"itemEnName\":null,\"remark\":null,\"createBy\":null,\"createDate\":\"2019-09-05 14:42:17\",\"lastUpdatedBy\":null,\"lastUpdatedDate\":\"2019-09-05 14:42:17\",\"enabledFlag\":\"Y\",\"orgId\":null,\"itemType\":null,\"itemStatus\":null,\"factoryId\":55,\"entityId\":null,\"innerType\":null,\"outterType\":null,\"abcType\":null,\"msLevel\":null,\"esdLevel\":null,\"chargeTimes\":null,\"itemWeight\":null,\"itemLength\":null,\"itemHeight\":null,\"itemWidth\":null,\"rohs\":null,\"isLead\":null,\"weightUnit\":null,\"lengthUnit\":null,\"craftSection\":null,\"isBind\":null,\"inItemNo\":null,\"productType\":null,\"isSmt\":null,\"likeStyle\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsItemInfoController@getInfoList\",\"code\":\"0000\",\"costTime\":\"1710ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Oct 29 21:31:09 CST 2019\",\"tag\":\"查询物料信息通用方法\",\"serviceName\":\"zte-mes-manufactureshare-basicsettingsysczjj\",\"userId\":null}}";
        PowerMockito.when(MicroServiceRestUtil.invokeService(anyString(), anyString(), anyString(),
                anyString(), anyString(), anyMap())).thenReturn(getresult);


        PowerMockito.when(JsonConvertUtil.jsonToBean(bo, List.class, BsItemInfo.class)).thenReturn(style);

        List<List<BSmtBomDetail>> detailList = new ArrayList<>();
        String[] sheetName = {"1", "2", "3"};
        PowerMockito.when(ExcelUtil.getXSSFWorkbook(sheetName, ExcelName.BSMTBOMHEADER_EXCEL_TITLE, detailList, null, ExcelName.BSMTBOMHEADER_EXECL_PROPS)).thenReturn(new XSSFWorkbook());
        Map<String, Object> mapConquery = new HashMap<String, Object>();
        mapConquery.put("factoryId", 55);
        headerParamsMap.put("X-Factory-Id", "53");
        service.downExcel(mapConquery, "", "", headerParamsMap);
        Assert.assertEquals("132010700031", itemListDetail.getItemCode());
    }


    @Test
    public void getLastModule() {
        List<BSmtBomIDTO> recordList = new ArrayList<>();
        BSmtBomIDTO bSmtBomIDTO = new BSmtBomIDTO();
        bSmtBomIDTO.setCraftSection("SMT-A");
        bSmtBomIDTO.setProductCode("123456789ABC");
        recordList.add(bSmtBomIDTO);
        PowerMockito.when(bSmtBomDetailRepository.getLastMachineNo(any())).thenReturn(bSmtBomIDTO);
        Assert.assertNotNull(bSmtBomHeaderServiceImpl.getLastMachineNo(recordList));
    }

    @Test
    public void checkNoRegisterItemNoThenReturn() throws Exception {
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        List<BSmtBomDetail> list = new LinkedList<>();
        record.setTransferList(list);
        bSmtBomHeaderServiceImpl.checkNoRegisterItemNoThenReturn(record);
        record.setWorkOrderNo("123");
        bSmtBomHeaderServiceImpl.checkNoRegisterItemNoThenReturn(record);

        BSmtBomDetail a1 = new BSmtBomDetail();
        a1.setItemCode("123");
        list.add(a1);
        List<BBomDetailDTO> bomDetailList = new LinkedList<>();
        BBomDetailDTO b1 = new BBomDetailDTO();
        b1.setItemCode("1234");
        bomDetailList.add(b1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getBomDetailByProductCode(Mockito.any())).thenReturn(bomDetailList);

        List<BBomTechnicalChangeInfo> bBomTechnicalChangeInfos = new LinkedList<>();
        BBomTechnicalChangeInfo c1 = new BBomTechnicalChangeInfo();
        c1.setItemCode("567");
        bBomTechnicalChangeInfos.add(c1);
        PowerMockito.when(BasicsettingRemoteService.queryTechnicalListByWorkOrder(record.getWorkOrderNo())).thenReturn(bBomTechnicalChangeInfos);

        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        workOrder.setItemNo("123");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.anyString())).thenReturn(workOrder);
        try {
            bSmtBomHeaderServiceImpl.checkNoRegisterItemNoThenReturn(record);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

    }

    @Test
    public void importBomLocked() throws Exception {
        List<BSmtBomIDTO> bomIDTOList = new LinkedList<>();
        BSmtBomIDTO bSmtBomA = new BSmtBomIDTO();
        bSmtBomA.setAttr1("123");
        bSmtBomA.setLineCode("123");
        bSmtBomA.setCraftSection("SMT-A");
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailList = new LinkedList<>();
        BSmtBomInfoDetailDTO a1 = new BSmtBomInfoDetailDTO();
        a1.setMachineNo("123");
        a1.setModuleNo("123");
        a1.setLocationNo("123");
        a1.setQty(new BigDecimal("1"));
        bSmtBomInfoDetailList.add(a1);
        bSmtBomA.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailList);
        bomIDTOList.add(bSmtBomA);

        BSmtBomIDTO bSmtBomB = new BSmtBomIDTO();
        bSmtBomB.setAttr1("123");
        bSmtBomB.setLineCode("123");
        bSmtBomB.setCraftSection("SMT-B");
        bSmtBomB.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailList);
        bomIDTOList.add(bSmtBomB);

        // 2.
        List<PsWorkOrderBasicDTO> orderBasicList = new LinkedList<>();
        PsWorkOrderBasicDTO b1 = new PsWorkOrderBasicDTO();
        b1.setCraftSection("SMT-A");
        b1.setWorkOrderNo("123");
        b1.setLineCode("123");
        orderBasicList.add(b1);
        PsWorkOrderBasicDTO b2 = new PsWorkOrderBasicDTO();
        b2.setCraftSection("SMT-B");
        b2.setWorkOrderNo("1234");
        b2.setLineCode("123");
        orderBasicList.add(b2);
        PowerMockito.when(planscheduleRemoteService.querySourceTaskInfo(Mockito.anyObject()))
                .thenReturn(orderBasicList);

        // 站位信息
        List<SmtLocationInfoDTO> locationInfoDTOList = new LinkedList<>();
        SmtLocationInfoDTO smtLocationInfoDTO = new SmtLocationInfoDTO();
        smtLocationInfoDTO.setMachineNo("123");
        smtLocationInfoDTO.setModuleNo("123");
        smtLocationInfoDTO.setLocationNo("123");
        locationInfoDTOList.add(smtLocationInfoDTO);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getListSmtLocation(Mockito.anyString(),any()))
                .thenReturn(locationInfoDTOList);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(true);

        PowerMockito.mockStatic(SpringContextUtil.class);


        CFLine cfLine = new CFLine();
        PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyString()))
                .thenReturn(cfLine);

        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        String params = "{\"lineCode\":\"123\",\"sourceTask\":\"123\",\"craftSection\":\"SMT-A\"}";
        Map<String, String> param1 = new HashMap<>();
        param1.put("lineCode", "123");
        param1.put("sourceTask", "123");
        param1.put("craftSection", "SMT-A");
        PowerMockito.when(MicroServiceRestUtil.invokeService("zte-mes-manufactureshare-planschedulesys",
                "v1", "GET", "/PS/psEntityPlanBasic", JSON.toJSONString(param1),
                new HashMap<>()))
                .thenReturn("{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"操作成功\"\n" +
                        "    },\n" +
                        "    \"bo\":[\n" +
                        "    \t{\n" +
                        "    \t\t\"lineCode\": \"123\",\n" +
                        "        \t\"sourceTask\": \"123\",\n" +
                        "        \t\"craftSection\": \"SMT-A\"\n" +
                        "    \t},\n" +
                        "    \t{\n" +
                        "    \t\t\"lineCode\": \"123\",\n" +
                        "        \t\"sourceTask\": \"123\",\n" +
                        "        \t\"craftSection\": \"SMT-B\"\n" +
                        "    \t}\n" +
                        "    ]\n" +
                        "}")
        ;

        PowerMockito.when(bSmtBomHeaderRepository.insertBSmtBomHeaderSelective(Mockito.any())).
                thenReturn(1);
        PowerMockito.when(SpringContextUtil.getBean("bSmtBomHeaderServiceImpl",
                BSmtBomHeaderService.class))
                .thenReturn(service);
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            bSmtBomHeaderServiceImpl.importBomLocked(bomIDTOList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATION_SN_ERROR, e.getMessage());
        }

        try {
            PowerMockito.when(SpringContextUtil.getBean("bSmtBomHeaderServiceImpl",
                    BSmtBomHeaderService.class))
                    .thenReturn(bSmtBomHeaderServiceImpl);
            bSmtBomHeaderServiceImpl.importBomLocked(bomIDTOList);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void dealMaterialPrepare() throws Exception {
        List<SmtMachineMaterialMouting> matrialMoutingList = new LinkedList<>();
        SmtMachineMaterialMouting a1 = new SmtMachineMaterialMouting();
        matrialMoutingList.add(a1);

        List<String> keyList = new LinkedList<>();
        boolean isRelatedLine = true;

        bSmtBomHeaderServiceImpl.dealMaterialPrepare(matrialMoutingList);
        Assert.assertTrue(isRelatedLine);
    }

    @Test
    public void uploadBom() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class,Workbook.class);
        BSmtBomIDTO dto = new BSmtBomIDTO();
        dto.setProductCode("");
        PowerMockito.when(wb.getSheetAt(Mockito.anyInt())).thenReturn(sheet);
        PowerMockito.when(sheet.getRow(Mockito.anyInt())).thenReturn(row);
        PowerMockito.when(row.getCell(Mockito.anyInt())).thenReturn(cell);
        PowerMockito.when(cell.getCellType()).thenReturn(CellType.NUMERIC);
        PowerMockito.when(dataFormatter.formatCellValue(cell)).thenReturn("1");
        PowerMockito.when(row.getLastCellNum()).thenReturn(Short.valueOf("4"));
        try {
            BomUploadTempDTO bomUploadTempDTO = new BomUploadTempDTO();
            MultipartFile multipartFile2 = new MockMultipartFile("test", "tes", "aaaa", "".getBytes());
            bomUploadTempDTO.setFile(multipartFile2);
            bSmtBomHeaderServiceImpl.uploadBom(bomUploadTempDTO, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
            BomUploadTempDTO bomUploadTempDTO = new BomUploadTempDTO();
            MultipartFile multipartFile2 = new MockMultipartFile("test", "tes.xlsx", "aaaa", "".getBytes());
            bomUploadTempDTO.setFile(multipartFile2);
//            PowerMockito.when(cloudDiskHelper.fileUpload(multipartFile2,Mockito.any())).thenReturn("");
//            PowerMockito.when(ExcelUtil.initWorkbook(Mockito.any(),Mockito.any())).thenReturn(workbook);
            bSmtBomHeaderServiceImpl.uploadBom(bomUploadTempDTO, dto);
    }

    @Test
    public void updateOrInsertBsmtInfoByTwoOrder() throws Exception {
        BSmtBomHeaderDTO record = new BSmtBomHeaderDTO();
        List<BSmtBomDetail> detailList = new LinkedList<>();
        BSmtBomDetail a1 = new BSmtBomDetail();
        a1.setItemCode("123");
        a1.setQty(new BigDecimal("3"));
        a1.setOperateType("");
        detailList.add(a1);
        record.setTransferList(detailList);

        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.when(PlanscheduleRemoteService.getPsWorkOrderSmtList(record.getWorkOrderNo()))
                .thenReturn(workOrder);

        Page<BBomDetail> bomPage = new Page<>();
        List<BBomDetail> list = new LinkedList<>();
        BBomDetail b1 = new BBomDetail();
        a1.setItemCode("234");
        a1.setQty(new BigDecimal("1"));
        list.add(b1);
        bomPage.setRows(list);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getBomListofDIP(Mockito.anyMap()))
                .thenReturn(bomPage);

        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyObject()))
                .thenReturn(jsonNode);

        Assert.assertNotNull(bSmtBomHeaderServiceImpl.updateOrInsertBsmtInfoByTwoOrder(record));
    }

    @Test
    public void importBomLockedTest1() throws Exception {
        List<BSmtBomIDTO> bomIDTOList = new LinkedList<>();
        BSmtBomIDTO bSmtBomA = new BSmtBomIDTO();
        bSmtBomA.setAttr1("123");
        bSmtBomA.setLineCode("123");
        bSmtBomA.setCraftSection("SMT-A");
        List<BSmtBomInfoDetailDTO> bSmtBomInfoDetailList = new LinkedList<>();
        BSmtBomInfoDetailDTO a1 = new BSmtBomInfoDetailDTO();
        a1.setMachineNo("123");
        a1.setModuleNo("123");
        a1.setLocationNo("123");
        a1.setQty(new BigDecimal("0"));
        bSmtBomInfoDetailList.add(a1);
        bSmtBomA.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailList);
        bomIDTOList.add(bSmtBomA);

        BSmtBomIDTO bSmtBomB = new BSmtBomIDTO();
        bSmtBomB.setAttr1("123");
        bSmtBomB.setLineCode("123");
        bSmtBomB.setCraftSection("SMT-B");
        bSmtBomB.setBSmtBomInfoDetailDTO(bSmtBomInfoDetailList);
        bomIDTOList.add(bSmtBomB);

        BSmtBomHeaderServiceImpl service1 = PowerMockito.spy(new BSmtBomHeaderServiceImpl());
        PowerMockito.doReturn(1).when(service1).updateWorkOrderSMT(Mockito.anyMap());
        String s = service1.importBomLocked(bomIDTOList);
        Assert.assertEquals(Constant.B_SMT_BOM_IMPORT_OK,s);
    }
    @Test
    public void insertHeadAndDetailLoseOldTest() throws Exception {
        BSmtBomIDTO bSmtBom = new BSmtBomIDTO();
        bSmtBom.setAttr1("123");
        bSmtBom.setLineCode("123");
        bSmtBom.setCraftSection("SMT-A");
        BSmtBomHeaderDTO headDTO = new BSmtBomHeaderDTO();
        headDTO.setCfgHeaderId("123");
        BSmtBomHeaderServiceImpl service1 = PowerMockito.spy(new BSmtBomHeaderServiceImpl());
        PowerMockito.doReturn("1").when(service1).insertBSmtBomHeaderSelective(Mockito.any());
        PowerMockito.doReturn(1).when(service1).updateWorkOrderSMT(Mockito.anyMap());
        List<BSmtBomDetailDTO> list = new ArrayList<>();
        list.add(new BSmtBomDetailDTO(){{setCfgHeaderId("123");}});
        PowerMockito.doReturn(list).when(service1).generateBomDetailDTOList(Mockito.any(),Mockito.any());
        PowerMockito.mockStatic(CommonUtils.class);
        Whitebox.invokeMethod(service1,"insertHeadAndDetailLoseOld",bSmtBom, headDTO);
        Assert.assertEquals("123",headDTO.getCfgHeaderId());
    }

    @Test
    public void uploadBom1() {
        PowerMockito.mockStatic(CommonUtils.class);
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row row = sheet.createRow(0);
        row.createCell(1).setCellValue("123");
        row.createCell(5).setCellValue("/");
        BSmtBomIDTO dto = new BSmtBomIDTO();
        dto.setProductCode("123");
        try {
            bSmtBomHeaderServiceImpl.uploadBom(workbook, dto);
        } catch (Exception e) {
            Assert.assertEquals(CommonUtils.getLmbMessage(MessageId.BSMTBOMDETAIL_TABLE_PCB_QTY_ERROR), e.getMessage());
        }
        row.createCell(1).setCellValue("456");
        try {
            bSmtBomHeaderServiceImpl.uploadBom(workbook, dto);
        } catch (Exception e) {
            Assert.assertEquals(CommonUtils.getLmbMessage(MessageId.BSMTBOMDETAIL_TABLE_ITEM_CODE_ERROR), e.getMessage());
        }
        row.createCell(1).setCellValue("/");
        try {
            bSmtBomHeaderServiceImpl.uploadBom(workbook, dto);
        } catch (Exception e) {
            Assert.assertEquals(CommonUtils.getLmbMessage(MessageId.BSMTBOMDETAIL_TABLE_PCB_QTY_ERROR), e.getMessage());
        }
    }
}
