package com.zte.test;

import com.zte.application.impl.SmDailyStatisticServiceImpl;
import com.zte.application.impl.SmtMachineMTLHistoryHServiceImpl;
import com.zte.application.impl.SmtMachineMTLHistoryLServiceImpl;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)

public class SmtMachineMTLHistoryHServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private SmtMachineMTLHistoryHServiceImpl service;

	@Mock
	private SmtMachineMTLHistoryHRepository repository;

	@Before
	public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
	}


	@Test
	public void updatePickStatusByWorkOrder() throws Exception {
		PowerMockito.when(repository.updatePickStatusByWorkOrder(Mockito.anyObject())).thenReturn(1);
		SmtMachineMTLHistoryH record = new SmtMachineMTLHistoryH();
		record.setWorkOrder("7000179-DIP5303");
		record.setPickStatus("2");
		Assert.assertEquals(1,service.updatePickStatusByWorkOrder(record));
	}

}
