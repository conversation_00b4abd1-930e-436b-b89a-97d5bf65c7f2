package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.utils.Constant;
import com.zte.dme.client.StringUtils;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.ContainerInfoDTO;
import com.zte.interfaces.dto.StandardModeStockInfoDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-06-29 19:40
 */
@PrepareForTest({MicroServiceRestUtil.class, JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class,HttpRemoteService.class})
public class ProductionDeliveryRemoteServiceTest extends PowerBaseTestCase {

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
    }

    @Test
    public void batchInsertContainerContentInfo() throws Exception {
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                "  },\n" +
                "  \"bo\": []\n" +
                "  }\n" +
                "}");
        ProductionDeliveryRemoteService.batchInsertContainerContentInfo(new LinkedList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void batchUpdateContainerContentInfo() throws Exception {
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                "  },\n" +
                "  \"bo\": []\n" +
                "  }\n" +
                "}");
        ProductionDeliveryRemoteService.batchUpdateContainerContentInfo(new LinkedList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getContainerContentInfoBySn() throws Exception {
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"调拨仓库资源执行成功\"\n" +
                "  },\n" +
                "  \"bo\": []\n" +
                "  }\n" +
                "}");
        ProductionDeliveryRemoteService.getContainerContentInfoBySn(new LinkedList<>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getStandardModeStockInfoPage() throws Exception {
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"aaa\"\n" +
                "  },\n" +
                "  \"bo\": 11\n" +
                "  }\n" +
                "}");
        ProductionDeliveryRemoteService.getStandardModeStockInfoPage(new StandardModeStockInfoDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkWmsHasReelId() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(false);

        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson);
        boolean result = ProductionDeliveryRemoteService.checkWmsHasReelId("ZTE001");
        Assert.assertEquals(false, result);
    }

    @Test
    public void replaceSnDeleteStock() throws Exception {
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn("{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"db.stock.resource.excute.success\",\n" +
                "    \"msg\": \"aaa\"\n" +
                "  },\n" +
                "  \"bo\": 11\n" +
                "  }\n" +
                "}");
        ProductionDeliveryRemoteService.replaceSnDeleteStock(new StandardModeStockInfoDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getContainerContentInfoBatch() throws Exception {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("{}");
        ProductionDeliveryRemoteService.getContainerContentInfoBatch(Arrays.asList("LPN00"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

	@Test
	public void getEnInfoByLpnOrOriginalLpn() throws Exception {
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
				.thenReturn("{}");

		ContainerInfoDTO record = new ContainerInfoDTO();
		record.setLpn("TEST");
		record.setPage(1);
		record.setRows(100);
		ProductionDeliveryRemoteService.getEnInfoByLpnOrOriginalLpn(record);
        Assert.assertEquals("TEST", record.getLpn());
	}

    @Test
    public void getContainerContentInfoCountByLpnList() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,MicroServiceRestUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("{}");

        ContainerInfoDTO record = new ContainerInfoDTO();
        record.setLpn("TEST");
        record.setPage(1);
        record.setRows(100);
        ProductionDeliveryRemoteService.getContainerContentInfoCountByLpnList(new ArrayList<>());
        Assert.assertEquals("TEST", record.getLpn());
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("11");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("5");
        ProductionDeliveryRemoteService.getContainerContentInfoCountByLpnList(new ArrayList<>());
        Assert.assertEquals("TEST", record.getLpn());
    }

}
