package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BSmtBomDetailService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.PsWipInfoService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.impl.PsScanHistoryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BSProcess;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PsScanHistory;
import com.zte.domain.model.PsScanHistoryRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class,
        MESHttpHelper.class, ObtainRemoteServiceDataUtil.class,CrafttechRemoteService.class,
        BasicsettingRemoteService.class, CommonUtils.class, PlanscheduleRemoteServiceTest.class,PlanscheduleRemoteService.class})
public class PsScanHistoryServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private PsScanHistoryServiceImpl service;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Mock
    private SmtSnMtlTracingTService smtSnMtlTracingTService;
    @Mock
    private JacksonJsonConverUtil jacksonJsonConverUtil;
    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private ValueOperations<String, String> redisOpsValue;
    @Mock
    private MESHttpHelper mesHttpHelper;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private PsWipInfoService psWipInfoService;

    @Before
    public void init() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(PlanscheduleRemoteServiceTest.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
    }


    @Test
    public void setSourceSys() throws Exception {
        service.setSourceSys(null, null);
        service.setSourceSys(new FlowControlInfoDTO(), new PsScanHistory());
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getOtherWorkOrder() throws Exception {
        List<EmEqpSpiBoardDTO> spiList = new ArrayList<>();
        EmEqpSpiBoardDTO emEqpSpiBoardDTO = new EmEqpSpiBoardDTO();
        spiList.add(emEqpSpiBoardDTO);
        Whitebox.invokeMethod(service, "getOtherWorkOrder", "777888990001", "2", spiList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getPsScanHistoryInfo() throws Exception {
        Map map = new HashMap();
        map.put("sns", "'55501212011'");
        map.put("currProcess", "P0243");
        when(service.insertPsScanHistoryByWipInfo(map)).thenReturn(1);
        List<PsScanHistory> listHis = new ArrayList<>();
        FlowControlInfoDTO record = new FlowControlInfoDTO();
        PsEntityPlanBasicDTO entityPlanBasicDTO = new PsEntityPlanBasicDTO();
        entityPlanBasicDTO.setWorkOrderNo("47415");
        record.setEntityPlanBasic(entityPlanBasicDTO);
        service.getPsScanHistoryInfo(listHis, record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void insertPsScanHistoryByWipInfo() throws Exception {
        Map map = new HashMap();
        map.put("sns", "'55501212011'");
        map.put("currProcess", "P0243");
        when(service.insertPsScanHistoryByWipInfo(map)).thenReturn(1);
        Assert.assertNotNull(service.insertPsScanHistoryByWipInfo(map));
        Assert.assertSame(service.insertPsScanHistoryByWipInfo(map), 1);
    }

    @Test
    public void getBindingBarcodeDataByProdplanId() throws Exception {
        Assert.assertNull(service.getBindingBarcodeDataByProdplanId("7310021"));
    }

    @Test
    public void selectPsScanHistoryBySn() throws Exception {
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistory.setSn("777766600001");
        Assert.assertNotNull(service.selectPsScanHistoryBySn(psScanHistory));
    }

    @Test
    public void getScanHistoryList() throws Exception {
        PsScanHistory psScanHistory = new PsScanHistory();
        psScanHistory.setAttribute1("7777808");
        psScanHistory.setCraftSection("SMT-A");
        psScanHistory.setCreateBy("00236517");
        psScanHistory.setCurrProcessCode("1");
        psScanHistory.setItemName("20200410102246X");
        psScanHistory.setItemNo("20200410102246X");
        psScanHistory.setLineCode("SMT-HY004");
        psScanHistory.setNextProcess("477");
        psScanHistory.setProcessName("贴片A面");
        psScanHistory.setRouteId("664d7e68-ee32-44f3-bcc3-4ce45ecd515b");
        psScanHistory.setSmtScanId("a5c325bb-dae7-4b07-ba68-a65f2824f4c3");
        psScanHistory.setSn("777780800100");
        psScanHistory.setSourceSysName("转交");
        psScanHistory.setWorkOrderNo("7777808-SMT-A5502");
        psScanHistory.setWorker("6dd49c7b-32ad-4c3c-9459-3caedce82223");

        List<PsScanHistory> list = new ArrayList();
        list.add(psScanHistory);

        PowerMockito.when(psScanHistoryRepository.getScanHistoryList(Mockito.anyObject())).thenReturn(list);

        BSProcess bSProcess = new BSProcess();
        bSProcess.setProcessName("贴片A面");

        List<BSProcess> listWorkStation = new ArrayList();
        listWorkStation.add(bSProcess);
        PsScanHistoryDTO dto = new PsScanHistoryDTO();
        dto.setAttribute1("7777808");
        dto.setCraftSection("SMT-A");
        dto.setCreateBy("00236517");
        dto.setCurrProcessCode("1");
        dto.setItemName("20200410102246X");
        dto.setItemNo("20200410102246X");
        dto.setLineCode("SMT-HY004");
        dto.setRouteId("664d7e68-ee32-44f3-bcc3-4ce45ecd515b");
        dto.setSmtScanId("a5c325bb-dae7-4b07-ba68-a65f2824f4c3");
        dto.setSn("777780800100");
        dto.setSourceSysName("转交");
        dto.setWorkOrderNo("7777808-SMT-A5502");
        dto.setWorker("6dd49c7b-32ad-4c3c-9459-3caedce82223");
        Assert.assertNotNull(service.getScanHistoryList(dto));
    }

    @Test
    public void getCountScanHistoryList() throws Exception {
        PowerMockito.when(psScanHistoryRepository.getCountScanHistoryList(Mockito.anyObject())).thenReturn(10L);

        PsScanHistoryDTO dto = new PsScanHistoryDTO();
        dto.setAttribute1("7777808");
        dto.setCraftSection("SMT-A");
        dto.setCreateBy("00236517");
        dto.setCurrProcessCode("1");
        dto.setItemName("20200410102246X");
        dto.setItemNo("20200410102246X");
        dto.setLineCode("SMT-HY004");
        dto.setRouteId("664d7e68-ee32-44f3-bcc3-4ce45ecd515b");
        dto.setSmtScanId("a5c325bb-dae7-4b07-ba68-a65f2824f4c3");
        dto.setSn("777780800100");
        dto.setSourceSysName("转交");
        dto.setWorkOrderNo("7777808-SMT-A5502");
        dto.setWorker("6dd49c7b-32ad-4c3c-9459-3caedce82223");

        Assert.assertNotNull(service.getCountScanHistoryList(dto));
    }

    @Test
    public void getEqpCount() throws Exception {
        EmEqpPdcountDTO pdcountDTO = new EmEqpPdcountDTO();
        pdcountDTO.setEqpModule("22");
        List<EmEqpPdcountDTO> emEqpPdcountDTOList = new ArrayList<>();
        emEqpPdcountDTOList.add(pdcountDTO);
        List<EmEqpPdcountDTO> updateEmList = new ArrayList<>();
        updateEmList.add(pdcountDTO);
        Whitebox.invokeMethod(service, "getEqpCount", "11", "22", "33", emEqpPdcountDTOList, updateEmList);
        Assert.assertEquals("22", pdcountDTO.getEqpModule());
    }

    @Test
    public void eqpInteractiveInfoProcess() throws Exception {
        List<EmEqpInteractiveDTO> updateEqpInteractiveList = new ArrayList<>();
        EmEqpInteractiveDTO eqpInteractiveDTO = new EmEqpInteractiveDTO();
        eqpInteractiveDTO.setCommander("11");
        updateEqpInteractiveList.add(eqpInteractiveDTO);
        SMTScanDTO dto = new SMTScanDTO();
        Date curDate = new Date();
        SmtMachineMaterialMouting mounting = new SmtMachineMaterialMouting();
        SMTScanParamDTO smtScanParamDTO = new SMTScanParamDTO();
        smtScanParamDTO.setNextReel("555");
        Assert.assertEquals("555", smtScanParamDTO.getNextReel());
        Whitebox.invokeMethod(service, "eqpInteractiveInfoProcess", smtScanParamDTO);
    }

    @Test
    public void insertMakeupRelationshipTest() throws Exception {
        List<PsWipInfo> listChildSnWipInfo = new ArrayList<>();
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        dto.setListChildSn(new ArrayList<>(Arrays.asList(new String[]{"23123231"})));
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setWorkOrderNo("12333");
        listChildSnWipInfo.add(psWipInfo);
        Map<String, String> map = new HashMap<>();
        map.put("X-Factory-Id", "58");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(map);

        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "insertMakeupRelationship",
                    listChildSnWipInfo, dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }
    }

    @Test
    public void checkChildSnTest() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        List<String> getListChildSn = new ArrayList<>();
        String a = "a";
        getListChildSn.add(a);
        dto.setListChildSn(getListChildSn);
        ArrayList<String> listChildSn = new ArrayList<>();
        List<PsWipInfo> listPsWipInfo = new ArrayList<>();
        PowerMockito.when(psWipInfoService.getListByBatchSn(anyList())).thenReturn(listPsWipInfo);
        try {
            Whitebox.invokeMethod(service, "checkChildSn",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CHILD_SN_IS_NOT_SCANNED, e.getMessage());
        }
    }

    @Test
    public void checkChildSnTest1() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        ArrayList<String> listChildSn = new ArrayList<>();
        dto.setListChildSn(listChildSn);
        List<PsWipInfo> listPsWipInfo = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("sdasda");
        listPsWipInfo.add(psWipInfo);
        PowerMockito.when(psWipInfoService.getListByBatchSn(anyList())).thenReturn(listPsWipInfo);
        try {
            Whitebox.invokeMethod(service, "checkChildSn",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CHILD_SN_IS_NOT_SCANNED, e.getMessage());
        }
        listChildSn.add("23213123");
        try {
            Whitebox.invokeMethod(service, "checkChildSn",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CHILD_SN_IS_NOT_SCANNED, e.getMessage());
        }
    }

    @Test
    public void checkParentSnTest() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        Long count = 1L;
        PowerMockito.when(psScanHistoryRepository.getCount(Mockito.anyObject())).thenReturn(count);
        Assert.assertNotNull(Whitebox.invokeMethod(service, "checkParentSn", dto));
    }

    @Test
    public void checkFormatOfParentSnTest() throws Exception {
        String parentSn = "2312312";
        try {
            Whitebox.invokeMethod(service, "checkFormatOfParentSn",
                    parentSn);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_SN_FORMAT_ERROR, e.getMessage());
        }
    }

    @Test
    public void checkParamTest() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        dto = null;
        try {
            Whitebox.invokeMethod(service, "checkParam",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }
    }

    @Test
    public void checkParamTest1() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        try {
            Whitebox.invokeMethod(service, "checkParam",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARENT_SN_CAN_NOT_BE_NULL, e.getMessage());
        }
    }

    @Test
    public void checkParamTest2() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        dto.setParentSn("sdsad");
        try {
            Whitebox.invokeMethod(service, "checkParam",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LIST_CHILD_SN_CAN_NOT_BE_NULL, e.getMessage());
        }
    }

    @Test
    public void checkParamTest3() throws Exception {
        MakeupRelationshipDTO dto = new MakeupRelationshipDTO();
        dto.setParentSn("sdsad");
        dto.setListChildSn(new ArrayList<>(Arrays.asList(new String[]{"23123"})));
        try {
            Whitebox.invokeMethod(service, "checkParam",
                    dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATOR_CAN_NOT_BE_NULL, e.getMessage());
        }
    }
}
