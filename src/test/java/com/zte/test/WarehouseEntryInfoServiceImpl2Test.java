package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.zte.application.PsWipInfoService;
import com.zte.application.ScrapBillDetailService;
import com.zte.application.WarehouseEntryDetailService;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Matchers.*;

@PrepareForTest({RedisHelper.class, MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteUtil.class, CommonUtils.class,
        CrafttechRemoteService.class, ProductionDeliveryRemoteService.class, BasicsettingRemoteService.class,PlanscheduleRemoteService.class})
public class WarehouseEntryInfoServiceImpl2Test extends PowerBaseTestCase {

    @InjectMocks
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Mock
    private WarehouseEntryDetailService warehouseEntryDetailService;

    @Mock
    private ApsRemoteService apsRemoteService;

    @Mock
    private PsWipInfoService psWipInfoService;

    @Mock
    private ScrapBillDetailService scrapBillDetailService;

    @Mock
    private WarehouseEntryDetailRepository warehouseEntryDetailRepository;

    @Mock
    private PsWipInfoRepository psWipInfoRepository;

    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private PsScanHistoryRepository psScanHistoryRepository;
    @Mock
    private ErpRemoteService erpRemoteService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void insertWarehouseEntryDetail() throws Exception {
        List<WarehouseEntryDetailDTO> warehouseEntryDetailList = new ArrayList<>();
        WarehouseEntryDetailDTO dto = new WarehouseEntryDetailDTO();
        warehouseEntryDetailList.add(dto);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning(Constant.FLAG_Y);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTO);
        PowerMockito.when(warehouseEntryInfoRepository.insertWarehouseEntryDetailBatchNotExist(any())).thenReturn(1);
        Assert.assertEquals(1,warehouseEntryInfoServiceImpl.insertWarehouseEntryDetail(warehouseEntryDetailList));
    }

    @Test
    public void writeBackAps() throws Exception {

        List<WarehouseEntryInfo> needWriteBackApsBillList = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552021021804920");
        warehouseEntryInfo.setProdplanNo("yf200704CF05-z");
        needWriteBackApsBillList.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.getNeedWriteBackApsBill(anyObject(), anyObject())).thenReturn(needWriteBackApsBillList);

        List<WarehouseEntryDetailDTO> statisticData = new ArrayList<>();
        PowerMockito.when(warehouseEntryDetailService.statisticReceiveDataOfBill(anyObject())).thenReturn(statisticData);

        PowerMockito.when(warehouseEntryDetailService.updateApsStatusByReceiveBatch(anyObject())).thenReturn(2);
        PowerMockito.when(warehouseEntryDetailService.updateApsWriteBackInfo(anyObject())).thenReturn(2);

        ServiceData<Object> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(apsRemoteService.updateProdplanInsmstockQty(anyObject(), anyObject(), anyInt(), anyObject(), anyObject())).thenReturn(ret);

        String startTime = "2021-02-13 00:00:00";
        String endTime = "2021-02-20 00:00:00";
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.writeBackAps(startTime, endTime));
    }

    @Test
    public void reWriteBackAps() throws Exception {

        List<WarehouseEntryInfo> needWriteBackApsBillList = new ArrayList<>();
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        warehouseEntryInfo.setBillNo("RK552021021804920");
        warehouseEntryInfo.setProdplanNo("yf200704CF05-z");
        needWriteBackApsBillList.add(warehouseEntryInfo);
        PowerMockito.when(warehouseEntryInfoRepository.getNeedWriteBackApsBill(anyObject(), anyObject())).thenReturn(needWriteBackApsBillList);

        List<WarehouseEntryDetailDTO> statisticData = new ArrayList<>();
        PowerMockito.when(warehouseEntryDetailService.statisticErrorDataOfBill(anyObject())).thenReturn(statisticData);

        PowerMockito.when(warehouseEntryDetailService.updateApsStatusByReceiveBatch(anyObject())).thenReturn(2);
        PowerMockito.when(warehouseEntryDetailService.updateApsWriteBackInfo(anyObject())).thenReturn(2);

        ServiceData<Object> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(apsRemoteService.updateProdplanInsmstockQty(anyObject(), anyObject(), anyInt(), anyObject(), anyObject())).thenReturn(ret);

        String startTime = "2021-02-13 00:00:00";
        String endTime = "2021-02-20 00:00:00";
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.reWriteBackAps(startTime, endTime));
    }

    @Test
    public void writeBackAps2() throws Exception {

        String prodplanNo = "yf200704CF05-z";
        String billNo = "RK552021021804920";
        WarehouseEntryDetailDTO warehouseEntryDetailDTO = new WarehouseEntryDetailDTO();
        warehouseEntryDetailDTO.setReceiveCount(2);
        warehouseEntryDetailDTO.setLastUpdatedBy("10266925");
        warehouseEntryDetailDTO.setLastUpdatedDate(new Date());

        PowerMockito.when(warehouseEntryDetailService.updateApsStatusByReceiveBatch(anyObject())).thenReturn(2);
        PowerMockito.when(warehouseEntryDetailService.updateApsWriteBackInfo(anyObject())).thenReturn(2);

        ServiceData<Object> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(apsRemoteService.updateProdplanInsmstockQty(anyObject(), anyObject(), anyInt(), anyObject(), anyObject())).thenReturn(ret);

        Assert.assertNotNull(warehouseEntryInfoServiceImpl.writeBackAps(prodplanNo, billNo, warehouseEntryDetailDTO));

    }

    @Test
    public void getWarehouseEntryInfoScrapTest() throws Exception {
        String prodplanId = "test";
        List<PsTask> psTaskList = new ArrayList<>();
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(psTaskList);
                }}));
        try {
            warehouseEntryInfoServiceImpl.getWarehouseEntryInfoScrap(prodplanId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCRAP_BILL_PROD_PLAN_ID_NOT_HAVE_TASK_INFO, e.getMessage());
        }
        PsTask psTask = new PsTask();
        psTask.setLeadFlag("test");
        psTask.setTaskQty(new BigDecimal("1"));
        psTaskList.add(psTask);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(psTaskList);
                }}));
        warehouseEntryInfoServiceImpl.getWarehouseEntryInfoScrap(prodplanId);
    }

    @Test
    public void saveWarehouseEntryInfoScrapTest() throws Exception {
        WarehouseEntryInfoQueryDTO dto = new WarehouseEntryInfoQueryDTO();
        String factoryId = "55";
        String entityId = "2";
        String empNo = "10300130";
        dto.setBillTypeCode(new BigDecimal(Constant.BILL_TYPE_LOOKUP_CODE_SCRAP));
        dto.setProdplanId("213123");
        List<PsWipInfo> psWipInfos = new ArrayList<>();
        PsWipInfo psWipInfo = new PsWipInfo();
        psWipInfo.setSn("777815645613");
        psWipInfos.add(psWipInfo);
        dto.setPsWipInfoList(psWipInfos);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GET_REDIS_LOCK.equals(e.getExMsgId()));
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.WAREHOUSEENTRYINFO_STOCK_TYPE_IS_NULL.equals(e.getExMsgId()));
        }
        dto.setStockType("半成品K2库");
        List<SysLookupTypesDTO> list = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupCode(new BigDecimal(Constant.SCRAP_BILL_WRITE_BACK_LOOKUP_CODE));
        sysLookupTypesDTO.setLookupMeaning("Y");
        list.add(sysLookupTypesDTO);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(list);
                }}));
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("2213123");
                }})
        );
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("1");
        List<List<WarehouseEntryDetailDTO>> list1 = new ArrayList<>();
        List<WarehouseEntryDetailDTO> list2 = new ArrayList<>();
        WarehouseEntryDetailDTO dto2 = new WarehouseEntryDetailDTO();
        list2.add(dto2);
        list1.add(list2);
        PowerMockito.when(warehouseEntryInfoRepository.insertWarehouseEntryInfo(any())).thenReturn(1);
        PowerMockito.when(warehouseEntryInfoRepository.insertWarehouseEntryDetailBatchNotExist(any())).thenReturn(1);
        PowerMockito.when(psScanHistoryRepository.insertPsScanHistoryBatch(Mockito.anyList()))
                .thenReturn(1);
		try {

			warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
		} catch (Exception e) {
			Assert.assertTrue(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY.equals(e.getMessage()));
		}
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupTypesDTO sysLookupTypes = new SysLookupTypesDTO();
        sysLookupTypes.setLookupMeaning("N");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypes);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypes1 = new SysLookupTypesDTO();
        sysLookupTypes1.setLookupMeaning("Y");
        sysLookupTypes1.setLookupType(new BigDecimal(5972));
        sysLookupTypes1.setLookupCode(new BigDecimal(5972004));
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.any(),Mockito.any())).thenReturn(sysLookupTypes1);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CARRY_ACCOUNT_NAME_NOT_NULL, e.getMessage());
        }
        dto.setCarryAccountName("aaaaa");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5972)).thenReturn(new ArrayList<>());
        dto.setItemNo("123321123321AAB");
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        sysLookupTypesList.add(sysLookupTypes1);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5972)).thenReturn(sysLookupTypesList);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY, e.getMessage());
        }
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5976)).thenReturn(new ArrayList<>());
        dto.setItemNo("123321123321");
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypes2 = new SysLookupTypesDTO();
        sysLookupTypes2.setLookupMeaning("Y");
        sysLookupTypes2.setLookupType(new BigDecimal(5976));
        sysLookupTypes2.setLookupCode(new BigDecimal(5976004));
        sysLookupTypes2.setAttribute1("635");
        sysLookupTypesList.add(sysLookupTypes2);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_5976)).thenReturn(sysLookupTypesList);
        dto.setOrgId(new BigDecimal(395));
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY, e.getMessage());
        }
        dto.setOrgId(new BigDecimal(635));
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto.setItemNo("");
        sysLookupTypes2.setLookupType(new BigDecimal(5976));
        sysLookupTypes2.setLookupCode(new BigDecimal(5976005));;
        sysLookupTypes1.setLookupCode(new BigDecimal(5972005));
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATOR_ID_NOT_EXIST, e.getMessage());
        }
        dto.setItemNo(null);
        PowerMockito.when(erpRemoteService.getLocatorInfo(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOCATOR_ID_NOT_EXIST, e.getMessage());
        }
        List<LocatorInfoDTO> locatorInfoList = new ArrayList<>();
        LocatorInfoDTO infoDTO = new LocatorInfoDTO();
        infoDTO.setInventoryLocationCode("qweqwe");
        infoDTO.setOrganizationId(new BigDecimal(635));
        infoDTO.setInventoryLocationId(new BigDecimal(123));
        locatorInfoList.add(infoDTO);
        PowerMockito.when(erpRemoteService.getLocatorInfo(Mockito.any(), Mockito.any())).thenReturn(locatorInfoList);
        try {
            warehouseEntryInfoServiceImpl.saveWarehouseEntryInfoScrap(dto, factoryId, entityId, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ACTUAL_STOCK_QTY_INCONSISTENT_SUBMITTED_QUANTITY, e.getMessage());
        }
    }

    @Test
    public void scanedBillNo() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        dto.setBillNo("123123");
        dto.setBillType("6");
        dto.setStatus("0");
        dto.setWorkOrderNo("0");
        PowerMockito.when(warehouseEntryInfoRepository.getTransNoMsg(Mockito.anyMap())).thenReturn(dto);
        PsWorkOrderBasic psWorkOrderBasic = new PsWorkOrderBasic();
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.any())).thenReturn(null);
        PowerMockito.when(datawbRemoteService.getApsOpProdplanStock(Mockito.any())).thenReturn("1232141");
        try{
            warehouseEntryInfoServiceImpl.scanedBillNo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(PlanscheduleRemoteService.findWorkOrder(Mockito.any())).thenReturn(psWorkOrderBasic);
        psWorkOrderBasic.setRouteId("wqwr");
        try{
            warehouseEntryInfoServiceImpl.scanedBillNo(dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void transPackingScanSn() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        dto.setBillNo("123123");
        dto.setBillType("6");
        dto.setStatus("0");
        dto.setScanType("1");
        dto.setStartSn("700001100001");
        dto.setEndSn("700001100003");

        List<WarehouseEntryDetail> detailList = new ArrayList<>();
        WarehouseEntryDetail detail = new WarehouseEntryDetail();
        detail.setBillNo("123123");
        detail.setSn("700001100001");
        WarehouseEntryDetail detail1 = new WarehouseEntryDetail();
        detail1.setBillNo("123123");
        detail1.setSn("700001100002");
        WarehouseEntryDetail detail2 = new WarehouseEntryDetail();
        detail2.setBillNo("123123");
        detail2.setSn("700001100003");
        detailList.add(detail);
        detailList.add(detail1);
        detailList.add(detail2);
        PowerMockito.when(warehouseEntryDetailRepository.getDetailBySnList(Mockito.any())).thenReturn(detailList);

        List<PsWipInfo> wipInfoList = new ArrayList<>();
        PsWipInfo wipInfo = new PsWipInfo();
        wipInfo.setSn("700001100001");
        wipInfo.setWorkStation("0");
        PsWipInfo wipInfo1 = new PsWipInfo();
        wipInfo1.setSn("700001100002");
        wipInfo1.setWorkStation("0");
        PsWipInfo wipInfo2 = new PsWipInfo();
        wipInfo2.setSn("700001100003");
        wipInfo2.setWorkStation("0");
        wipInfoList.add(wipInfo);
        wipInfoList.add(wipInfo1);
        wipInfoList.add(wipInfo2);
        PowerMockito.when(psWipInfoRepository.getListByBatchSn(Mockito.anyList())).thenReturn(wipInfoList);

        List<CtRouteDetailDTO> ctRouteDetailDTOList = new ArrayList<>();
        CtRouteDetailDTO detailDTO = new CtRouteDetailDTO();
        detailDTO.setNextProcess("525");
        ctRouteDetailDTOList.add(detailDTO);
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(Mockito.any(), Mockito.any())).thenReturn(ctRouteDetailDTOList);
        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessName("康訊半成品庫");
        bsProcess.setProcessCode("#");
        bsProcessList.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.getBsProcessList(Mockito.any())).thenReturn(bsProcessList);
        Assert.assertNotNull(warehouseEntryInfoServiceImpl.transPackingScanSn(dto));
    }

    @Test
    public void transPackingScanSubmit() throws Exception {
        PowerMockito.mockStatic(ProductionDeliveryRemoteService.class, RedisHelper.class);
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        List<PsWipInfoDTO> wipInfoList = new ArrayList<>();
        PsWipInfoDTO wipInfo = new PsWipInfoDTO();
        wipInfo.setSn("700001100001");
        wipInfo.setWorkStation("0");
        PsWipInfoDTO wipInfo1 = new PsWipInfoDTO();
        wipInfo1.setSn("700001100002");
        wipInfo1.setWorkStation("0");
        PsWipInfoDTO wipInfo2 = new PsWipInfoDTO();
        wipInfo2.setSn("700001100003");
        wipInfo2.setWorkStation("0");
        wipInfoList.add(wipInfo);
        wipInfoList.add(wipInfo1);
        wipInfoList.add(wipInfo2);
        dto.setSnList(wipInfoList);
        dto.setBillNo("123123");
        dto.setLpn("123");

        List<WarehouseEntryDetail> detailList = new ArrayList<>();
        WarehouseEntryDetail detail = new WarehouseEntryDetail();
        detail.setBillNo("123123");
        detail.setSn("700001100001");
        WarehouseEntryDetail detail1 = new WarehouseEntryDetail();
        detail1.setBillNo("123123");
        detail1.setSn("700001100002");
        WarehouseEntryDetail detail2 = new WarehouseEntryDetail();
        detail2.setBillNo("123123");
        detail2.setSn("700001100003");
        detailList.add(detail);
        detailList.add(detail1);
        detailList.add(detail2);
        PowerMockito.when(warehouseEntryDetailRepository.getDetailBySnList(Mockito.any())).thenReturn(detailList);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        Assert.assertEquals(0, warehouseEntryInfoServiceImpl.transPackingScanSubmit(dto, "10313234"));
    }
}
