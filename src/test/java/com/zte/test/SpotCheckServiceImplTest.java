package com.zte.test;

import com.zte.application.PsWipInfoService;
import com.zte.application.impl.SpotCheckServiceImpl;
import com.zte.domain.model.SpotCheckDetail;
import com.zte.domain.model.SpotCheckDetailRepository;
import com.zte.domain.model.SpotCheckHead;
import com.zte.domain.model.SpotCheckHeadRepository;
import com.zte.interfaces.dto.SpotCheckDTO;
import com.zte.interfaces.dto.SpotCheckHeadDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class SpotCheckServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private SpotCheckServiceImpl service;
    @Mock
    private SpotCheckHeadRepository spotCheckHeadRepository;
    @Mock
    private SpotCheckDetailRepository spotCheckDetailRepository;
    @Mock
    private PsWipInfoService psWipInfoService;

    @Test
    public void getHeadPage() throws Exception {
        SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try{
            service.getHeadPage(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setPage(2);
        try{
            service.getHeadPage(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setRows(2);
        try{
            service.getHeadPage(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setProdplanId("111");
        try{
            service.getHeadPage(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setSn("111");
        try{
            service.getHeadPage(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHeadCount(Mockito.any())).thenReturn(1L);
        List<SpotCheckHead> spotCheckHeads = new ArrayList<>();
        PowerMockito.when(spotCheckHeadRepository.getSpotCheckHead(Mockito.any())).thenReturn(spotCheckHeads);
        service.getHeadPage(dto);
    }


    @Test
    public void getSpotCheck() throws Exception {
        SpotCheckHeadDTO dto = new SpotCheckHeadDTO();
        try{
            service.getSpotCheck(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }

        dto.setHeadId("11111");
        SpotCheckHead spotCheckHead = new SpotCheckHead();
        PowerMockito.when(spotCheckHeadRepository.selectByPrimaryKey(Mockito.any())).thenReturn(spotCheckHead);
        List<SpotCheckDetail> spotCheckDetails = new ArrayList<>();
        PowerMockito.when(spotCheckDetailRepository.selectByHeadId(Mockito.any())).thenReturn(spotCheckDetails);
        service.getSpotCheck(dto);
    }

    @Test
    public void createSpotCheck() throws Exception {
        SpotCheckDTO dto = new SpotCheckDTO();
        try{
            service.createSpotCheck(dto);
        }catch (Exception e){
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getMessage());
        }
    }

}
