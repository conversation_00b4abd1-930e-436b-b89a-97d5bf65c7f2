package com.zte.test;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.impl.HighTempSamplingServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.HighTempSamplingRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.HighTempQueryDTO;
import com.zte.interfaces.dto.HighTempSamplingDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:40
 */
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class, HttpRemoteUtil.class, HttpRemoteService.class, RedisHelper.class, RedisLock.class,
        EasyExcelFactory.class, ImesExcelUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, ObtainRemoteServiceDataUtil.class,PlanscheduleRemoteService.class})
public class HighTempSamplingServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private HighTempSamplingServiceImpl service;
    @Mock
    private HighTempSamplingRepository highTempSamplingRepository;
    @Mock
    HttpServletRequest request;
    @Mock
    HttpServletResponse response;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    EmailUtils emailUtils;
    @Mock
    private RedisLock redisLock;

    @Before
    public void init(){
        PowerMockito.mockStatic(RedisHelper.class);
    }

    @Test
    public void insertBatch() throws Exception {
        List<HighTempSamplingDTO> list = new ArrayList() {{
            add(new HighTempSamplingDTO() {{
                setSn("111");
            }});
        }};
        PowerMockito.when(highTempSamplingRepository.checkSnsIsExist(Mockito.anyList())).thenReturn(new ArrayList() {{
            add("111");
        }});
        try {
            service.insertBatch(list,"10338918");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_EXIST, e.getMessage());
        }
        PowerMockito.when(highTempSamplingRepository.checkSnsIsExist(Mockito.anyList())).thenReturn(new ArrayList<>());
        service.insertBatch(list,"10338918");
    }

    @Test
    public void getHighTempSnInfoPage() throws Exception {
        HighTempQueryDTO queryDTO = new HighTempQueryDTO();
        queryDTO.setSn("111");
        queryDTO.setCreateDateStart(new Date());
        queryDTO.setCreateDateEnd(new Date());
        queryDTO.setLastUpdatedDateStart(new Date());
        queryDTO.setLastUpdatedDateEnd(new Date());
        queryDTO.setPage(1);
        queryDTO.setRows(10);
        PowerMockito.when(highTempSamplingRepository.getHighTempSnInfoPage(Mockito.any())).thenReturn(new ArrayList<>());
        service.getHighTempSnInfoPage(queryDTO);
        PowerMockito.when(highTempSamplingRepository.getHighTempSnInfoPage(Mockito.any())).thenReturn(new ArrayList() {{
            add(new HighTempQueryDTO() {{
                setEnabled("Y");
            }});
        }});
        service.getHighTempSnInfoPage(queryDTO);
        queryDTO.setPage(0);
        queryDTO.setRows(0);
        Page<HighTempQueryDTO> infoPage = service.getHighTempSnInfoPage(queryDTO);
        List<HighTempQueryDTO> infoList = infoPage.getRows();
        HighTempQueryDTO queryDTO1 = infoList.get(0);
        Assert.assertEquals(queryDTO1.getEnabled(), Constant.FLAG_Y);
    }

    @Test
    public void snExpire() throws Exception {
        try {
            service.snExpire(null,"10338918");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL_VALID, e.getMessage());
        }
        HighTempQueryDTO dto = new HighTempQueryDTO();
        try {
            service.snExpire(dto,"10338918");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NULL_VALID, e.getMessage());
        }
        dto.setSn("111");
        service.snExpire(dto,"10338918");
    }

    @Test
    public void checkParams() throws Exception {
        HighTempQueryDTO dto = new HighTempQueryDTO();
        try {
            Whitebox.invokeMethod(service, "checkParams", dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_PARAMS_ERROR, e.getMessage());
        }
        dto.setSn("1111");
        Whitebox.invokeMethod(service, "checkParams", dto);
        dto.setSn("");
        dto.setLastUpdatedDateStart(new Date());
        try {
            Whitebox.invokeMethod(service, "checkParams", dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_PARAMS_ERROR, e.getMessage());
        }
        dto.setLastUpdatedDateEnd(new Date());
        dto.setSn("1111");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        Date oneYearAgo = calendar.getTime();
        dto.setLastUpdatedDateStart(oneYearAgo);
        try {
            Whitebox.invokeMethod(service, "checkParams", dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LAST_UPDATE_DATE_RANGE_TOO_LONG, e.getMessage());
        }
        dto.setLastUpdatedDateStart(new Date());
        dto.setCreateDateEnd(new Date());
        dto.setCreateDateStart(oneYearAgo);
        try {
            Whitebox.invokeMethod(service, "checkParams", dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CREATE_DATE_RANGE_TOO_LONG, e.getMessage());
        }
        dto.setCreateDateStart(new Date());
        Whitebox.invokeMethod(service, "checkParams", dto);
    }

    @Test
    public void setStatus() throws Exception {
        List<HighTempQueryDTO> queryList = new ArrayList() {{
            add(new HighTempQueryDTO() {{
                setEnabled("Y");
            }});
            add(new HighTempQueryDTO() {{
                setEnabled("N");
            }});
        }};
        Whitebox.invokeMethod(service, "setStatus", queryList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void countExportTotal() throws Exception {
        HighTempQueryDTO queryDTO = new HighTempQueryDTO();
        queryDTO.setSn("111");
        queryDTO.setCreateDateStart(new Date());
        queryDTO.setCreateDateEnd(new Date());
        queryDTO.setLastUpdatedDateStart(new Date());
        queryDTO.setLastUpdatedDateEnd(new Date());
        PowerMockito.when(highTempSamplingRepository.getInfoCountForExport(Mockito.any())).thenReturn(5);
        Integer total = service.countExportTotal(queryDTO);
        Assert.assertEquals(5L, (long)total);
    }

    @Test
    public void queryExportData() throws Exception {
        HighTempQueryDTO queryDTO = new HighTempQueryDTO();
        queryDTO.setSn("111");
        queryDTO.setCreateDateStart(new Date());
        queryDTO.setCreateDateEnd(new Date());
        queryDTO.setLastUpdatedDateStart(new Date());
        queryDTO.setLastUpdatedDateEnd(new Date());
        PowerMockito.when(highTempSamplingRepository.getHighTempSnInfoPage(Mockito.any())).thenReturn(new ArrayList<>());
        service.queryExportData(queryDTO,1,10);
        PowerMockito.when(highTempSamplingRepository.getHighTempSnInfoPage(Mockito.any())).thenReturn(new ArrayList() {{
            add(new HighTempQueryDTO() {{
                setSn("111222333");
            }});
        }});
        List<HighTempQueryDTO> dtoList = service.queryExportData(queryDTO, 1, 10);
        HighTempQueryDTO tempQueryDTO = dtoList.get(0);
        Assert.assertEquals("111222333",tempQueryDTO.getSn());
    }

    @Test
    public void checkSnIsExist() throws Exception {

        try {
            service.checkSnIsExist("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }

        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(null);
        service.checkSnIsExist("123321");
        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(1);
        try {
            service.checkSnIsExist("123321");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_HIGH_TEMP_SN,e.getMessage());
        }
    }

	@Test
	public void getSnValidInfo() throws Exception {
		HighTempQueryDTO dto = new HighTempQueryDTO();
		PowerMockito.when(highTempSamplingRepository.getSnValidInfo(Mockito.any())).thenReturn(dto);
		Assert.assertNotNull(service.getSnValidInfo("123321"));
	}

	@Test
	public void batchGetSnValidInfo() throws Exception {
		List<String> sns = new ArrayList<>();
		sns.add("123321");
		HighTempQueryDTO dto = new HighTempQueryDTO();
		dto.setSn("133321");
		List<HighTempQueryDTO> list = new ArrayList<>();
		list.add(dto);
		PowerMockito.when(highTempSamplingRepository.batchGetSnValidInfo(Mockito.any())).thenReturn(list);
		Assert.assertNotNull(service.batchGetSnValidInfo(sns));
	}

    @Test
    public void checkSnIsValid() throws Exception {
        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(null);
        service.checkSnIsValid("123321");
        PowerMockito.when(highTempSamplingRepository.checkSnIsExist(Mockito.any())).thenReturn(1);
        Assert.assertTrue(service.checkSnIsValid("123321"));
    }
}
