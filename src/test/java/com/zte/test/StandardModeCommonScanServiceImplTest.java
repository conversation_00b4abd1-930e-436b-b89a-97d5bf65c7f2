package com.zte.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.impl.BarcodeCenterServiceImpl;
import com.zte.application.impl.PsWipInfoServiceImpl;
import com.zte.application.impl.StandardModeCommonScanServiceImpl;
import com.zte.application.scan.FlowControlCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BarcodeGenerateRule;
import com.zte.domain.model.PmRepairRcvDetail;
import com.zte.domain.model.PmRepairRcvDetailRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.BarcodeRetDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.GenBarcodeParamDTO;
import com.zte.interfaces.dto.PmScanConditionDTO;
import com.zte.interfaces.dto.PsEntityPlanBasicDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, CommonUtils.class,
        DatawbRemoteService.class, PlanscheduleRemoteService.class, CrafttechRemoteService.class,
        HttpRemoteUtil.class, HttpRemoteService.class, ConstantInterface.class, BasicsettingRemoteService.class,
        JacksonJsonConverUtil.class, JsonConvertUtil.class})
public class StandardModeCommonScanServiceImplTest extends PowerBaseTestCase {

    @Mock
    private PmRepairRcvDetailRepository pmRepairRcvDetailRepository;

    @Mock
    private PsWipInfoServiceImpl psWipInfoServiceImpl;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private FlowControlCommonService flowControlCommonService;

    @InjectMocks
    private StandardModeCommonScanServiceImpl standardModeCommonScanService;

    @Mock
    private BarcodeCenterServiceImpl barcodeCenterService;

    @Test
    public void checkRepair() {
        StandardModeCommonScanServiceImpl service = new StandardModeCommonScanServiceImpl();
        service.setPmRepairRcvDetailRepository(pmRepairRcvDetailRepository);
        List<PmRepairRcvDetail> pepariDetailList = new ArrayList<PmRepairRcvDetail>();
        when(pmRepairRcvDetailRepository.getList(anyObject())).thenReturn(pepariDetailList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void checkLastProcess() throws Exception {
        StandardModeCommonScanServiceImpl service = new StandardModeCommonScanServiceImpl();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<SysLookupTypesDTO> lookUpList = new ArrayList<SysLookupTypesDTO>();
        SysLookupTypesDTO dto = new SysLookupTypesDTO();
        dto.setLookupMeaning("P1015");
        lookUpList.add(dto);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyMap())).thenReturn(lookUpList);
        Assert.assertSame("P1015",dto.getLookupMeaning());
    }

    @Test
    public void checkFirstPrcessAndWorkStation() throws Exception {
        StandardModeCommonScanServiceImpl service = PowerMockito.spy(new StandardModeCommonScanServiceImpl());
        service.setPsWipInfoServiceImpl(psWipInfoServiceImpl);
        List<PsEntityPlanBasicDTO> psEntityPlanInfoList = new ArrayList<PsEntityPlanBasicDTO>();
        PsEntityPlanBasicDTO workOrderDto = new PsEntityPlanBasicDTO();
        workOrderDto.setRouteId("sdfsdf");
        psEntityPlanInfoList.add(workOrderDto);
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setWorkOrderNo("sdsdf");
        entity.setEntityPlanBasic(workOrderDto);
        when(psWipInfoServiceImpl.getPsEntityPlanInfo(anyObject())).thenReturn(psEntityPlanInfoList);
        doReturn("").when(service).checkIsFirstProcess(anyObject(), anyObject());
        doReturn("").when(service).checkIsFirstStation(anyObject(), anyObject());
        Assert.assertNotNull(service.checkFirstPrcessAndWorkStation(entity));
    }

    @Test
    public void generateBarcode() throws Exception {
        StandardModeCommonScanServiceImpl service = PowerMockito.spy(new StandardModeCommonScanServiceImpl());
        PsEntityPlanBasicDTO workOrder = new PsEntityPlanBasicDTO();
        workOrder.setTaskNo("1234567832657899578966545566214");
        service.setConstantInterface(constantInterface);
        BarcodeGenerateRule rule = new BarcodeGenerateRule();
        BarcodeRetDTO barcodeDto = new BarcodeRetDTO();
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        when(constantInterface.getUrl(anyObject())).thenReturn("barcode/generate");
        String json = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\":{\"subCategory\":\"整机条码生成\",\"sysLotCode\":null,\"lot\":null,\"prodDate\":null,\"barcode\":\"ZJ19103100021\"},"
                + "\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsBarcodeInfoController@generate\","
                + "\"code\":\"0000\",\"costTime\":\"65ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Oct 31 10:35:25 CST 2019\","
                + "\"tag\":\"条码生成接口\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10240988\"}}";
        PowerMockito.when(HttpRemoteService.remoteExePost(anyObject(), anyMap(), anyMap(), anyString())).thenReturn(json);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(json);
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapper);
        when(JacksonJsonConverUtil.jsonToListBean(anyString(), anyObject())).thenReturn(barcodeDto);
        service.generateBarcode(rule, workOrder, "");
        Assert.assertSame("1234567832657899578966545566214",workOrder.getTaskNo());
    }

    @Test
    public void getBarcodeGenerateRule() throws Exception {
        StandardModeCommonScanServiceImpl service = PowerMockito.spy(new StandardModeCommonScanServiceImpl());
        service.setConstantInterface(constantInterface);
        BarcodeRetDTO barcodeDto = new BarcodeRetDTO();
        List<BarcodeRetDTO> barCodeList = new ArrayList<BarcodeRetDTO>();
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(JsonConvertUtil.class);
        when(constantInterface.getUrl(anyObject())).thenReturn("barcode/generate");
        String json = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},"
                + "\"bo\":{\"subCategory\":\"整机条码生成\",\"sysLotCode\":null,\"lot\":null,\"prodDate\":null,\"barcode\":\"ZJ19103100021\"},"
                + "\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BsBarcodeInfoController@generate\","
                + "\"code\":\"0000\",\"costTime\":\"65ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Oct 31 10:35:25 CST 2019\","
                + "\"tag\":\"条码生成接口\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10240988\"}}";
        PowerMockito.when(HttpRemoteService.remoteExe(anyObject(), anyMap(), anyMap(), anyString())).thenReturn(json);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(json);
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapper);
        when(JsonConvertUtil.jsonToBean(anyString(), anyObject(), anyObject())).thenReturn(barCodeList);
        Assert.assertNotNull(service.getBarcodeGenerateRule("123"));
    }

    /* Started by AICoder, pid:00561w37b473e3e147d108c2808d006b24514ea3 */
    @Test
    public void dealAsZjSnTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        FlowControlInfoDTO entity = new FlowControlInfoDTO();
        entity.setSn("123456789012345");
        String msg = "test";
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.any())).thenReturn(MessageId.SN_NOT_IN_WIP);
        try {
            standardModeCommonScanService.dealAsZjSn(entity, msg);
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.SN_NOT_IN_WIP, returnMessage.getMessage());
        }
        List<PsWipInfo> listWip = new ArrayList<>();
        PsWipInfo a1 = new PsWipInfo();
        listWip.add(a1);
        PowerMockito.when(psWipInfoServiceImpl.getList(Mockito.any())).thenReturn(listWip);
        try {
            standardModeCommonScanService.dealAsZjSn(entity, msg);
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.WORDER_ORDER_NOT_FOUND, returnMessage.getMessage());
        }
        List<PsEntityPlanBasicDTO> psEntityPlanInfoList = new ArrayList<>();
        PsEntityPlanBasicDTO b1 = new PsEntityPlanBasicDTO();
        psEntityPlanInfoList.add(b1);
        PowerMockito.when(psWipInfoServiceImpl.getPsEntityPlanInfo(Mockito.any()))
                .thenReturn(psEntityPlanInfoList);
        try {
            standardModeCommonScanService.dealAsZjSn(entity, msg);
        }catch (Exception returnMessage){
            Assert.assertEquals( MessageId.WORDER_ORDER_NOT_FOUND, returnMessage.getMessage());
        }
    }
    /* Ended by AICoder, pid:00561w37b473e3e147d108c2808d006b24514ea3 */


    @Test
    public void setNum() throws Exception {
        GenBarcodeParamDTO genBarcodeParamDTO = new GenBarcodeParamDTO();
        standardModeCommonScanService.setNum(genBarcodeParamDTO, "无铅");
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void setProcessInfoList() throws Exception {
        FlowControlInfoDTO controlResult = new FlowControlInfoDTO();
        PmScanConditionDTO entity = new PmScanConditionDTO();
        Whitebox.invokeMethod(standardModeCommonScanService, "setProcessInfoList", controlResult, entity);

        List<PmScanConditionDTO> pmScanConditionDTOS = new ArrayList<>();
        entity.setProcessInfoList(pmScanConditionDTOS);
        PmScanConditionDTO pmScanConditionDTO = new PmScanConditionDTO();
        pmScanConditionDTOS.add(pmScanConditionDTO);
        Whitebox.invokeMethod(standardModeCommonScanService, "setProcessInfoList", controlResult, entity);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }
}
