package com.zte.test;


import com.zte.application.BSmtBomDetailService;
import com.zte.application.IMESLogService;
import com.zte.application.ImesPDACommonService;
import com.zte.application.PkCodeHistoryService;
import com.zte.application.SmtMachineMTLHistoryLService;
import com.zte.application.SmtMachineMaterialMoutingService;
import com.zte.application.SmtMachineMaterialPrepareService;
import com.zte.application.SmtMachineMaterialReturnService;
import com.zte.application.SmtMachineMtlOnlineStandbyService;
import com.zte.application.SmtSnMtlTracingTService;
import com.zte.application.TransferStrategyInfoService;
import com.zte.application.impl.PkCodeInfoServiceImpl;
import com.zte.application.impl.SmtMachineMaterialMoutingServiceImpl;
import com.zte.application.impl.SmtMachineMaterialPrepareServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BSmtBomDetail;
import com.zte.domain.model.BSmtBomDetailRepository;
import com.zte.domain.model.CFLine;
import com.zte.domain.model.PkCodeHistory;
import com.zte.domain.model.PkCodeHistoryRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.domain.model.SmtMachineMTLHistoryH;
import com.zte.domain.model.SmtMachineMTLHistoryHRepository;
import com.zte.domain.model.SmtMachineMTLHistoryLRepository;
import com.zte.domain.model.SmtMachineMaterialMouting;
import com.zte.domain.model.SmtMachineMaterialMoutingRepository;
import com.zte.domain.model.SmtMachineMaterialPrepare;
import com.zte.domain.model.SmtMachineMaterialPrepareRepository;
import com.zte.domain.model.SmtMachineMaterialReturn;
import com.zte.domain.model.SmtMachineMtlOnlineStandby;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.AgeingInfoFencePointToPointQueryItemInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SmtMachineMaterialPrepareDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.ReflectUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, PlanscheduleRemoteService.class})
public class SmtMachineMaterialPrepareServiceImplTest {
    @InjectMocks
    private SmtMachineMaterialPrepareServiceImpl smtMachineMaterialPrepareService;
    @Mock
    private SmtMachineMaterialPrepareRepository smtMachineMaterialPrepareRepository;
    @Mock
    private TransferStrategyInfoService transferStrategyInfoService;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;
    @Mock
    private SmtMachineMTLHistoryLService smtMachineMTLHistoryLService;
    @Mock
    private PkCodeHistoryService pkCodeHistoryService;
    @Mock
    private SmtMachineMaterialPrepareService smtMachineMaterialPrepareService1;
    @Mock
    private SmtMachineMaterialReturnService smtMachineMaterialReturnService;
    @Mock
    private BSmtBomDetailService bSmtBomDetailService;
    @Mock
    private PkCodeInfoServiceImpl pkCodeInfoService;
    @Mock
    private BSmtBomDetailRepository bSmtBomDetailRepository;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private SmtMachineMaterialMoutingRepository smtMachineMaterialMoutingRepository;
    @Mock
    private SmtMachineMaterialMoutingService smtMachineMaterialMoutingService;
    @Mock
    private SmtMachineMtlOnlineStandbyService smtMachineMtlOnlineStandbyService;
    @Mock
    private com.zte.springbootframe.common.model.RetCode retCode;
    @Mock
    private SmtMachineMTLHistoryHRepository smtMachineMTLHistoryHRepository;
    @Mock
    private SmtMachineMTLHistoryLRepository smtMachineMTLHistoryLRepository;

    @Mock
    private SmtSnMtlTracingTService smtSnMtlTracingTService;

    @Mock
    private ImesPDACommonService imesPdaCommonService;

    @InjectMocks
    private SmtMachineMaterialMoutingServiceImpl smtMachineMaterialMoutingServiceimpl;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void setLineName() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<com.zte.domain.model.CFLine> cfLineList = new ArrayList<>();
        com.zte.domain.model.CFLine cfLine = new com.zte.domain.model.CFLine();
        cfLine.setLineName("name");
        cfLine.setLineCode("code");
        cfLineList.add(cfLine);
        PowerMockito.when(BasicsettingRemoteService.getLine(anyMap())).thenReturn(cfLineList);
        List<SmtMachineMaterialMouting> retList = new ArrayList<>();
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        retList.add(smtMachineMaterialMouting);
        smtMachineMaterialMoutingServiceimpl.setLineName(retList);
        Assert.assertEquals("name",cfLine.getLineName());
    }

    @Test
    public void pickkingReelid() throws Exception {

        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setMtlPrepareId("c8c26326-4d06-4fa2-9e3b-68684ee7a6e1");
        smtMachineMaterialPrepare.setWorkOrder("7777770-SMT-B5501");
        smtMachineMaterialPrepare.setItemCode("045020100180");
        smtMachineMaterialPrepare.setLineCode("SMT-HY004");
        smtMachineMaterialPrepare.setMachineNo("(X4S-2)");
        smtMachineMaterialPrepare.setLocationNo("4-1-1");
        smtMachineMaterialPrepare.setObjectId("7777777010115000000018");
        smtMachineMaterialPrepare.setQty(new BigDecimal(100));
        smtMachineMaterialPrepare.setRemark("->7777770-SMT-B5501");
        smtMachineMaterialPrepare.setEnabledFlag("Y");
        smtMachineMaterialPrepare.setCfgHeaderId("09ee251c-fb74-47c2-92ae-d3f3bfc2f4f8");
        Assert.assertEquals("Y",smtMachineMaterialPrepare.getEnabledFlag());
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new ArrayList();
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);

        PowerMockito.when(smtMachineMaterialPrepareRepository.getSmtMachineMaterialPrepareList(Mockito.anyObject())).thenReturn(smtMachineMaterialPrepareList);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        workOrder.setWorkOrderId("04cf3fe8-29cd-4eb6-a916-57e7b134869e");
        workOrder.setTaskNo("psa200601CF01-z");
        workOrder.setItemNo("129704651013AGB");
        workOrder.setItemName("ZXSDR R8139 T3500 R77TCA35A");
        workOrder.setTaskQty(new BigDecimal(560));
        workOrder.setWorkOrderNo("7305105-SMT-A5201");
        workOrder.setWorkshopCode("30");
        workOrder.setProductType("量产");
        workOrder.setCraftSection("DIP");
        workOrder.setLineCode("SMT-CS1");
        workOrder.setWorkOrderStatus("已提交");
        workOrder.setEnabledFlag("Y");
        workOrder.setSourceTask("7305105");

        PowerMockito.when(PlanscheduleRemoteService.findPsWorkOrderSmtList(Mockito.any())).thenReturn(workOrder);

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderId("04cf3fe8-29cd-4eb6-a916-57e7b134869e");
        psWorkOrderDTO.setTaskNo("psa200601CF01-z");
        psWorkOrderDTO.setItemNo("129704651013AGB");
        psWorkOrderDTO.setItemName("ZXSDR R8139 T3500 R77TCA35A");
        psWorkOrderDTO.setTaskQty(new BigDecimal(560));
        psWorkOrderDTO.setWorkOrderNo("7305105-SMT-A5201");
        psWorkOrderDTO.setWorkshopCode("30");
        psWorkOrderDTO.setProductType("量产");
        psWorkOrderDTO.setCraftSection("SMT-A");
        psWorkOrderDTO.setLineCode("SMT-CS1");
        psWorkOrderDTO.setWorkOrderStatus("已提交");
        psWorkOrderDTO.setCraftNo("CF522007150023");
        psWorkOrderDTO.setEnabledFlag("Y");
        psWorkOrderDTO.setProdplanNo("psa200601CF01-z");
        psWorkOrderDTO.setPlanId("KPCSQI200601-000499");

        List<com.zte.interfaces.dto.PsWorkOrderDTO> list = new ArrayList<>();
        list.add(psWorkOrderDTO);

        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.any())).thenReturn(list);

        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoByWorkOrders(Mockito.anyList(), Mockito.anyString()
                , Mockito.anyString(), Mockito.anyString())).thenReturn(smtMachineMaterialPrepareList);

        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setWorkOrder("123");
        Mockito.when(smtMachineMaterialPrepareRepository.updateGivenStatus(dto)).thenReturn(new Long(1));
        List<PkCodeInfo> pkCodeInfos = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("7777777010115000000018");
        pkCodeInfo.setWetLevel("一级");
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getPickCodeList(Mockito.any())).thenReturn(pkCodeInfos);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfoCollect = new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO ageingInfoFencePointToPointQueryItemInfoDTO = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        ageingInfoFencePointToPointQueryItemInfoDTO.setItemNo("045020100180");
        ageingInfoFencePointToPointQueryItemInfoDTO.setAbcType("A");
        itemInfoCollect.add(ageingInfoFencePointToPointQueryItemInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyList())).thenReturn(itemInfoCollect);
        PkCodeHistory pkInfoDto = new PkCodeHistory();
        pkInfoDto.setHistoryId(UUID.randomUUID().toString());
        pkCodeHistoryRepository.insertPkCodeHistory(pkInfoDto);
    }

    @Test
    public void findSmtMachineMaterialPrepareList() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        workOrder.setWorkOrderId("04cf3fe8-29cd-4eb6-a916-57e7b134869e");
        workOrder.setTaskNo("psa200601CF01-z");
        workOrder.setItemNo("129704651013AGB");
        workOrder.setItemName("ZXSDR R8139 T3500 R77TCA35A");
        workOrder.setTaskQty(new BigDecimal(560));
        workOrder.setWorkOrderNo("7305105-SMT-A5201");
        workOrder.setWorkshopCode("30");
        workOrder.setProductType("量产");
        workOrder.setCraftSection("DIP");
        workOrder.setLineCode("SMT-CS1");
        workOrder.setWorkOrderStatus("已提交");
        workOrder.setEnabledFlag("Y");
        workOrder.setSourceTask("7305105");

        PowerMockito.when(PlanscheduleRemoteService.findPsWorkOrderSmtList(Mockito.any())).thenReturn(workOrder);

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        psWorkOrderDTO.setWorkOrderId("04cf3fe8-29cd-4eb6-a916-57e7b134869e");
        psWorkOrderDTO.setTaskNo("psa200601CF01-z");
        psWorkOrderDTO.setItemNo("129704651013AGB");
        psWorkOrderDTO.setItemName("ZXSDR R8139 T3500 R77TCA35A");
        psWorkOrderDTO.setTaskQty(new BigDecimal(560));
        psWorkOrderDTO.setWorkOrderNo("7305105-SMT-A5201");
        psWorkOrderDTO.setWorkshopCode("30");
        psWorkOrderDTO.setProductType("量产");
        psWorkOrderDTO.setCraftSection("SMT-A");
        psWorkOrderDTO.setLineCode("SMT-CS1");
        psWorkOrderDTO.setWorkOrderStatus("已提交");
        psWorkOrderDTO.setCraftNo("CF522007150023");
        psWorkOrderDTO.setEnabledFlag("Y");
        psWorkOrderDTO.setProdplanNo("psa200601CF01-z");
        psWorkOrderDTO.setPlanId("KPCSQI200601-000499");

        List<com.zte.interfaces.dto.PsWorkOrderDTO> list = new ArrayList<>();
        list.add(psWorkOrderDTO);

        PowerMockito.when(PlanscheduleRemoteService.getBasicWorkOrderInfoByProdplanId(Mockito.any())).thenReturn(list);

        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setMtlPrepareId("c8c26326-4d06-4fa2-9e3b-68684ee7a6e1");
        smtMachineMaterialPrepare.setWorkOrder("7777770-SMT-B5501");
        smtMachineMaterialPrepare.setItemCode("045020100180");
        smtMachineMaterialPrepare.setLineCode("SMT-HY004");
        smtMachineMaterialPrepare.setMachineNo("(X4S-2)");
        smtMachineMaterialPrepare.setLocationNo("4-1-1");
        smtMachineMaterialPrepare.setObjectId("7777777010115000000018");
        smtMachineMaterialPrepare.setQty(new BigDecimal(100));
        smtMachineMaterialPrepare.setRemark("->7777770-SMT-B5501");
        smtMachineMaterialPrepare.setEnabledFlag("Y");
        smtMachineMaterialPrepare.setCfgHeaderId("09ee251c-fb74-47c2-92ae-d3f3bfc2f4f8");

        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new ArrayList();
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);

        PowerMockito.when(smtMachineMaterialPrepareRepository.getPrepareInfoByWorkOrders(Mockito.anyList(), Mockito.anyString()
                , Mockito.anyString(), Mockito.anyString())).thenReturn(smtMachineMaterialPrepareList);

        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setWorkOrder("7305105-SMT-A5201");
        dto.setFactoryId(new BigDecimal(52));
        dto.setIsGiven("N");
        List<PkCodeInfo> pkCodeInfos = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setPkCode("7777777010115000000018");
        pkCodeInfo.setWetLevel("一级");
        pkCodeInfos.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoService.getPickCodeList(Mockito.any())).thenReturn(pkCodeInfos);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        List<AgeingInfoFencePointToPointQueryItemInfoDTO> itemInfoCollect = new ArrayList<>();
        AgeingInfoFencePointToPointQueryItemInfoDTO ageingInfoFencePointToPointQueryItemInfoDTO = new AgeingInfoFencePointToPointQueryItemInfoDTO();
        ageingInfoFencePointToPointQueryItemInfoDTO.setItemNo("045020100180");
        ageingInfoFencePointToPointQueryItemInfoDTO.setAbcType("A");
        itemInfoCollect.add(ageingInfoFencePointToPointQueryItemInfoDTO);
        PowerMockito.when(BasicsettingRemoteService.getItemInfo(Mockito.anyList())).thenReturn(itemInfoCollect);

        Assert.assertNotNull(smtMachineMaterialPrepareService.findSmtMachineMaterialPrepareList(dto));
    }

    @Test
    public void insertSmtMachineMaterialPrepareBatchTest() throws Exception {
        List<SmtMachineMaterialPrepareDTO> lists = new LinkedList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.insertSmtMachineMaterialPrepareBatch(Mockito.anyList())).thenReturn(0);
        Assert.assertEquals(0, smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(lists));

        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        for (int i = 0; i < 200; i++) {
            lists.add(dto);
        }
        int count = Constant.INT_400;
        PowerMockito.when(smtMachineMaterialPrepareRepository.insertSmtMachineMaterialPrepareBatch(Mockito.anyList())).thenReturn(count);
        Assert.assertEquals(800,smtMachineMaterialPrepareService.insertSmtMachineMaterialPrepareBatch(lists));
    }

    @Test
    public void pdaBatchUpdateSmtMachineMaterialPrepareTest() throws Exception {
        List<SmtMachineMaterialPrepareDTO> lists = new LinkedList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.pdaBatchUpdateSmtMachineMaterialPrepare(Mockito.anyList())).thenReturn(0);
        Assert.assertEquals(0, smtMachineMaterialPrepareService.pdaBatchUpdateSmtMachineMaterialPrepare(lists));

        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        for (int i = 0; i < 200; i++) {
            lists.add(dto);
        }
        int count = Constant.INT_400;
        PowerMockito.when(smtMachineMaterialPrepareRepository.pdaBatchUpdateSmtMachineMaterialPrepare(Mockito.anyList())).thenReturn(count);
        Assert.assertEquals(800,smtMachineMaterialPrepareService.pdaBatchUpdateSmtMachineMaterialPrepare(lists));
    }

    @Test
    public void batchUpdateSmtMachineMaterialPrepareByIdTest() throws Exception {
        List<SmtMachineMaterialPrepare> lists = new LinkedList<>();
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "batchUpdateSmtMachineMaterialPrepareById",
                lists);

        SmtMachineMaterialPrepare dto = new SmtMachineMaterialPrepare();
        for (int i = 0; i < 200; i++) {
            lists.add(dto);
        }
        int count = Constant.INT_400;
        smtMachineMaterialPrepareService.batchUpdateSmtMachineMaterialPrepareById(lists);
        Assert.assertEquals(Constant.INT_400, count);
    }

    @Test
    public void saveOrUpdateSmtMachineMaterialPrepareByFeederRefTest() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setOperateMsg("123213");
        dto.setWorkOrder("22222");
        dto.setLineCode("2221");
        dto.setRelatedWorkOrder("123123");
        dto.setRelatedLine("231312");
        dto.setRelatedCfgHeaderId("213213");
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setDirection("0");
        bSmtBomDetailList.add(bSmtBomDetail);
        PowerMockito.when(BasicsettingRemoteService.getLookupMeaningByCode(Mockito.any())).thenReturn("Y");
        PowerMockito.when(bSmtBomDetailRepository.getList(any())).thenReturn(bSmtBomDetailList);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setItemAngle("0度");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(pkCodeInfo);
        PowerMockito.when(bSmtBomDetailRepository.getList(any())).thenReturn(new ArrayList<>());
        PkCodeInfo pkCodeResult = new PkCodeInfo();
        pkCodeResult.setItemAngle("0度");
        PowerMockito.when(pkCodeInfoService.getItemDirection(any())).thenReturn(pkCodeResult);
        Assert.assertNull(smtMachineMaterialPrepareService.saveOrUpdateSmtMachineMaterialPrepareByFeederRef(dto));
    }

    @Test
    public void savePkCodeHisotyTest() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        smtMachineMaterialPrepareService.savePkCodeHisoty(dto, Constant.COMPOSITE_PREPARE);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void saveOrUpdatePrepareTest() throws Exception {
        String workOrder = "21313";
        String lineCode = "213123";
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setLocationNo("213213");
        dto.setModuleNo("213213");
        dto.setMachineNo("123123213");
        List<SmtMachineMaterialPrepare> list = new LinkedList<>();
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareListByFeederRef(Mockito.anyObject())).thenReturn(list);
        smtMachineMaterialPrepareService.saveOrUpdatePrepare(dto, workOrder, lineCode, "1");
        Assert.assertEquals("213213", dto.getModuleNo());
    }

    @Test
    public void unbundlingFeederTest() throws Exception {
        SmtMachineMaterialPrepare dto = new SmtMachineMaterialPrepare();
        dto.setMtlPrepareId("23213");
        dto.setObjectId(null);
        Assert.assertEquals(0, smtMachineMaterialPrepareService.unbundlingFeeder(dto));

        PowerMockito.when(smtMachineMaterialPrepareRepository.unbindFeederByObjectId(any())).thenReturn(1);
        Assert.assertEquals(0,smtMachineMaterialPrepareService.unbundlingFeeder(dto));

        dto.setObjectId("XXXX");
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setEntityId(new BigDecimal("2"));
        PowerMockito.when(smtMachineMaterialPrepareRepository.unbindFeederByObjectId(prepare)).thenReturn(1);
        Assert.assertEquals(1, smtMachineMaterialPrepareService.unbundlingFeeder(dto));
    }

    @Test
    public void refreshbyMaterialTest() throws Exception {
        PsEntityPlanBasic entity = new PsEntityPlanBasic();
        entity.setWorkOrderNo("123213");
        entity.setLineCode("2313");
        entity.setAttribute1("2");
        List<PsEntityPlanBasic> list = new LinkedList<>();
        list.add(entity);
        list.add(entity);
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new LinkedList<>();
        SmtMachineMaterialPrepare a1 = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepareList.add(a1);
        a1.setItemCode("test");
        a1.setObjectId("test11");
        a1.setQty(new BigDecimal("2"));
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectByWorkOrderAndLineCode(Mockito.any()))
                .thenReturn(smtMachineMaterialPrepareList);
        CFLine cfLine = new CFLine();
        cfLine.setSupChgLineOnModule("Y");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getLine(Mockito.anyString()))
                .thenReturn(cfLine);
        smtMachineMaterialPrepareService.refreshbyMaterial(list);

        list.clear();
        list.add(entity);
        PsEntityPlanBasic entity1 = new PsEntityPlanBasic();
        entity1.setWorkOrderNo("8899512-SMT-B5202");
        entity1.setAttribute1("213");
        list.add(entity1);
        List<BSmtBomDetail> bSmtBomDetailList = new ArrayList<>();
        BSmtBomDetail bSmtBomDetail = new BSmtBomDetail();
        bSmtBomDetail.setItemCode("test");
        bSmtBomDetailList.add(bSmtBomDetail);
        PowerMockito.when(bSmtBomDetailService.getList(Mockito.anyMap(), any(), any()))
                .thenReturn(bSmtBomDetailList);
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutingList = new LinkedList<>();
        SmtMachineMaterialMouting c1 = new SmtMachineMaterialMouting();
        c1.setSwitchedQuicklyFlag("N");
        smtMachineMaterialMoutingList.add(c1);
        PowerMockito.when(smtMachineMaterialMoutingRepository
                .selectMoutingWithPkCodeInfo(Mockito.anyMap())).thenReturn(smtMachineMaterialMoutingList)
        ;
        smtMachineMaterialPrepareService.refreshbyMaterial(list);

        entity.setAttribute1("3");

        smtMachineMaterialPrepareService.refreshbyMaterial(list);
        c1.setItemCode("test");
        c1.setNextReelRowid("pkcode1");
        c1.setQty(new BigDecimal("2"));
        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepareList.add(smtMachineMaterialPrepare);
        smtMachineMaterialPrepare.setItemCode("test");
        smtMachineMaterialPrepare.setObjectId("pkcode1");
        smtMachineMaterialPrepare.setQty(new BigDecimal("2"));
        smtMachineMaterialPrepareService.refreshbyMaterial(list);
        c1.setSwitchedQuicklyFlag("Y");
        smtMachineMaterialPrepareService.refreshbyMaterial(list);
        Assert.assertEquals("pkcode1", c1.getNextReelRowid());
    }

    @Test
    public void processReturnTest() throws Exception {
        List<SmtMachineMaterialReturn> list = new LinkedList<>();
        SmtMachineMaterialReturn dto = new SmtMachineMaterialReturn();
        list.add(dto);
        list.add(dto);
        list.add(dto);
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "processReturn", list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void refreshByWorkorder() throws Exception {
        PsEntityPlanBasic entity = new PsEntityPlanBasic();

        SmtMachineMaterialMouting dto = new SmtMachineMaterialMouting();
        dto.setFactoryId(new BigDecimal(52));
        dto.setEntityId(new BigDecimal(21));
        List<SmtMachineMaterialMouting> list = new LinkedList<>();
        list.add(dto);
        List<SmtMachineMtlOnlineStandby> smtMachineMtlOnlineStandbyList = new LinkedList<>();
        SmtMachineMtlOnlineStandby smtMachineMtlOnlineStandby = new SmtMachineMtlOnlineStandby();
        smtMachineMtlOnlineStandbyList.add(smtMachineMtlOnlineStandby);
        smtMachineMtlOnlineStandbyList.add(smtMachineMtlOnlineStandby);
        PowerMockito.when(smtMachineMaterialMoutingService.selectSmtMachineMaterialMoutingSelective(Mockito.anyObject())).thenReturn(list);
        PowerMockito.when(smtMachineMtlOnlineStandbyService.getSmtMachineMtlOnlineStandby(Mockito.anyObject())).thenReturn(smtMachineMtlOnlineStandbyList);
        smtMachineMaterialPrepareService.refreshByWorkorder(entity);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
        dto.setNextReelRowid("123");
        smtMachineMaterialPrepareService.refreshByWorkorder(entity);
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkFeederTest() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        dto.setObjectId("2222");
        List<SmtMachineMaterialMouting> list = new LinkedList<>();
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectSmtMachineMaterialMoutingSelective(Mockito.anyObject())).thenReturn(list);
        List<SmtMachineMaterialPrepare> list1 = new LinkedList<>();
        SmtMachineMaterialPrepare smtPrepare = new SmtMachineMaterialPrepare();
        smtPrepare.setItemCode("2222");
        list1.add(smtPrepare);
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.anyObject()))
                .thenReturn(list1);
        Assert.assertNotNull(smtMachineMaterialPrepareService.checkFeeder(dto));
    }

    @Test
    public void unbindFeederTest() throws Exception {
        SmtMachineMaterialPrepare p = new SmtMachineMaterialPrepare();
        p.setLineCode("222");
        p.setWorkOrder("2222");
        p.setModuleNo("22222");
        Integer s = 2;
        try {
            smtMachineMaterialPrepareService.unbindFeeder(p);
        } catch (NullPointerException e) {
            Assert.assertEquals(MessageId.PARAM_MISSING, e.getMessage());
        }
    }

    @Test
    public void pickkingReelidTest() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setIsGiven("s");
        List<SmtMachineMaterialPrepare> reList = new LinkedList<>();
        reList.add(prepare);

        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PsWorkOrderBasic workOrder = new PsWorkOrderBasic();
        workOrder.setWorkOrderId("04cf3fe8-29cd-4eb6-a916-57e7b134869e");
        workOrder.setTaskNo("psa200601CF01-z");
        workOrder.setItemNo("129704651013AGB");
        workOrder.setItemName("ZXSDR R8139 T3500 R77TCA35A");
        workOrder.setTaskQty(new BigDecimal(560));
        workOrder.setWorkOrderNo("7305105-SMT-A5201");
        workOrder.setWorkshopCode("30");
        workOrder.setProductType("量产");
        workOrder.setCraftSection("DIP");
        workOrder.setLineCode("SMT-CS1");
        workOrder.setWorkOrderStatus("已提交");
        workOrder.setEnabledFlag("Y");
        workOrder.setSourceTask("7305105");
        PowerMockito.when(smtMachineMaterialPrepareService.getSmtMachineMaterialPrepareList(Mockito.anyObject())).thenReturn(reList);
        PowerMockito.when(PlanscheduleRemoteService.findPsWorkOrderSmtList(Mockito.any())).thenReturn(workOrder);

        smtMachineMaterialPrepareService.pickkingReelid(dto);
        Assert.assertEquals("7305105", workOrder.getSourceTask());
    }

    @Test
    public void selectSmtMachineMaterialPrepareByFeederRef() {
        SmtMachineMaterialPrepareDTO param = new SmtMachineMaterialPrepareDTO();
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(null);
        Assert.assertNull(smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederRef(param));

        List<SmtMachineMaterialPrepare> list = new LinkedList<>();
        SmtMachineMaterialPrepare a1 = new SmtMachineMaterialPrepare();
        list.add(a1);
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectSmtMachineMaterialPrepareByFeederRef(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(smtMachineMaterialPrepareService.selectSmtMachineMaterialPrepareByFeederRef(param));
    }
    /* Started by AICoder, pid:e442dn0a6a67b1714f2f081861a7681afbb5dd01 */
    @Test
    public void batchUpdateQtyTaskByPkCodes() throws Exception {

        List<PkCodeInfo> needUpdateQtyPkCodes = new ArrayList<>();
        needUpdateQtyPkCodes.add(new PkCodeInfo());
        needUpdateQtyPkCodes.add(new PkCodeInfo() {{
            setPkCode("reelid");
        }});
        Map<String, SmtMachineMaterialPrepare> smtMachineMaterialPrepareMap = new HashMap<>();
        smtMachineMaterialPrepareMap.put("3", new SmtMachineMaterialPrepare() {{
            setWorkOrder("7411879-SMT-5501");
            setObjectId("reelid");
        }});
        smtMachineMaterialPrepareMap.put("2", new SmtMachineMaterialPrepare() {{
            setWorkOrder("7411879-SMT-5501");
            setObjectId("reelid2");
        }});
        PsEntityPlanBasic nextProdPlan = new PsEntityPlanBasic();
        nextProdPlan.setSourceTask("7411879");
        ReflectUtil.setFieldValue(smtMachineMaterialPrepareService, "updatePreparationSwitch", "N");
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "batchUpdateQtyTaskByPkCodes", needUpdateQtyPkCodes, smtMachineMaterialPrepareMap, nextProdPlan);
        Assert.assertEquals(needUpdateQtyPkCodes.size(), 2);

        ReflectUtil.setFieldValue(smtMachineMaterialPrepareService, "updatePreparationSwitch", "Y");
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "batchUpdateQtyTaskByPkCodes", needUpdateQtyPkCodes, smtMachineMaterialPrepareMap, nextProdPlan);
        Assert.assertEquals(needUpdateQtyPkCodes.size(), 3);
    }
    /* Ended by AICoder, pid:e442dn0a6a67b1714f2f081861a7681afbb5dd01 */
    @Test
    public void transferMaterial() throws Exception {
        try {
            smtMachineMaterialPrepareService.transferMaterial(null);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_MISSING, e.getExMsgId());
        }
        List<PsEntityPlanBasic> planBasics = new ArrayList<>();
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_MISSING, e.getExMsgId());
        }
        // 当前完工指令
        PsEntityPlanBasic current = new PsEntityPlanBasic();
        current.setWorkOrderNo("70001-A");
        // 同批次下个指令
        PsEntityPlanBasic nextSide = new PsEntityPlanBasic();
        nextSide.setWorkOrderNo("70001-B");
        nextSide.setCfgHeaderId("CfgHeaderId70001-B");
        // 下个批次同面别指令
        PsEntityPlanBasic nextProdPlan = new PsEntityPlanBasic();
        nextProdPlan.setWorkOrderNo("70002-A");
        planBasics.add(current);
        planBasics.add(nextSide);
        planBasics.add(nextProdPlan);

        List<SmtMachineMaterialPrepare> nextProdPlanPrepare = new ArrayList<>();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare.setFeederNo("111");
        nextProdPlanPrepare.add(smtMachineMaterialPrepare);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.anyObject()))
                .thenReturn(nextProdPlanPrepare);
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_ALREADY_HAVE_PREPARE_DATA, e.getExMsgId());
        }

        nextProdPlanPrepare.clear();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare1 = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare1.setFeederNo("111");
        smtMachineMaterialPrepare1.setRemark("70001-A->70002-A");
        nextProdPlanPrepare.add(smtMachineMaterialPrepare1);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.anyObject()))
                .thenReturn(nextProdPlanPrepare);
        smtMachineMaterialPrepareService.transferMaterial(planBasics);

        SmtMachineMaterialPrepare smtMachineMaterialPrepare2 = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepare2.setFeederNo("111");
        nextProdPlanPrepare.add(smtMachineMaterialPrepare2);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.anyObject()))
                .thenReturn(nextProdPlanPrepare);
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_ALREADY_HAVE_PREPARE_DATA, e.getExMsgId());
        }


        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.anyObject()))
                .thenReturn(new ArrayList<>());
        List<SmtMachineMaterialMouting> smtMachineMaterialMoutings = new ArrayList<>();
        SmtMachineMaterialMouting m1 = new SmtMachineMaterialMouting();
        SmtMachineMaterialMouting m2 = new SmtMachineMaterialMouting();
        SmtMachineMaterialMouting m3 = new SmtMachineMaterialMouting();
        SmtMachineMaterialMouting m4 = new SmtMachineMaterialMouting();
        smtMachineMaterialMoutings.add(m1);
        smtMachineMaterialMoutings.add(m2);
        smtMachineMaterialMoutings.add(m3);
        smtMachineMaterialMoutings.add(m4);
        m1.setLocationNo("Loc1");
        m1.setQty(null);
        m1.setEnabledFlag("Y");
        m2.setLocationNo("Loc2");
        m2.setQty(BigDecimal.ZERO);
        m2.setEnabledFlag("Y");
        m3.setLocationNo("Loc3");
        m3.setQty(BigDecimal.TEN);
        m3.setEnabledFlag("Y");
//        m4.setLocationNo("Loc4");
//        m4.setQty(BigDecimal.TEN);
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(Mockito.anyObject()))
                .thenReturn(smtMachineMaterialMoutings);
        List<BSmtBomDetail> currentSmtBomDetails = new ArrayList<>();
        BSmtBomDetail b1 = new BSmtBomDetail();
        BSmtBomDetail b2 = new BSmtBomDetail();
        BSmtBomDetail b3 = new BSmtBomDetail();
        BSmtBomDetail b4 = new BSmtBomDetail();
        currentSmtBomDetails.add(b1);
        currentSmtBomDetails.add(b2);
        currentSmtBomDetails.add(b3);
        currentSmtBomDetails.add(b4);
        currentSmtBomDetails.add(b4);
        b1.setLocationNo("Loc1");
        b2.setLocationNo("Loc2");
        b2.setItemCode("ItemCode2");
        b3.setLocationNo("Loc3");
        b4.setLocationNo("Loc4");
        b4.setItemCode("ItemCode4");
        PowerMockito.when(bSmtBomDetailService.getList(Mockito.anyObject(), any(), any()))
                .thenReturn(currentSmtBomDetails);
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PLS_RENEW_TRAY_BEFORE_COMPLETING, e.getExMsgId());
        }

        m1.setLocationNo("Loc1");
        m1.setQty(BigDecimal.TEN);
        m1.setNextReelRowid("NextReel1");
        m1.setEnabledFlag("Y");
        m2.setLocationNo("Loc2");
        m2.setQty(BigDecimal.TEN);
        m2.setNextReelRowid("NextReel2");
        m2.setItemCode("ItemCode22");
        m2.setEnabledFlag("Y");
        m3.setLocationNo("Loc3");
        m3.setQty(BigDecimal.TEN);
        m3.setEnabledFlag("Y");
        m4.setLocationNo("Loc4");
        m4.setQty(BigDecimal.TEN);
        m4.setItemCode("ItemCode4");
        m4.setEnabledFlag("Y");
        PowerMockito.when(smtMachineMaterialMoutingRepository.selectMoutingWithPkCodeInfo(Mockito.anyObject()))
                .thenReturn(smtMachineMaterialMoutings);

        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new ArrayList<>();
        SmtMachineMaterialPrepare p1 = new SmtMachineMaterialPrepare();
        SmtMachineMaterialPrepare p2 = new SmtMachineMaterialPrepare();
        SmtMachineMaterialPrepare p3 = new SmtMachineMaterialPrepare();
        SmtMachineMaterialPrepare p4 = new SmtMachineMaterialPrepare();
        smtMachineMaterialPrepareList.add(p1);
        smtMachineMaterialPrepareList.add(p2);
        smtMachineMaterialPrepareList.add(p3);
        smtMachineMaterialPrepareList.add(p4);
        p1.setObjectId("NextReel1");
        p2.setObjectId("ObjectId2");
        p3.setObjectId("ObjectId3");
        p4.setObjectId("ObjectId4");
        PowerMockito.when(smtMachineMaterialPrepareRepository.selectByWorkOrderAndLineCode(Mockito.anyObject()))
                .thenReturn(smtMachineMaterialPrepareList);
        PowerMockito.when(transferStrategyInfoService.markUsed(Mockito.anyObject(),Mockito.anyObject()))
                .thenReturn(1);
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PLS_RENEW_TRAY_BEFORE_COMPLETING, e.getExMsgId());
        }

        smtMachineMaterialPrepare.setFeederNo("");
        nextProdPlanPrepare.clear();
        nextProdPlanPrepare.add(smtMachineMaterialPrepare);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getRelOneList(Mockito.anyObject()))
                .thenReturn(nextProdPlanPrepare);
        try {
            smtMachineMaterialPrepareService.transferMaterial(planBasics);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PLS_RENEW_TRAY_BEFORE_COMPLETING, e.getExMsgId());
        }
    }

    @Test
    public void insertReturn() throws Exception {
        List<SmtMachineMaterialReturn> returnsFromMounting = new ArrayList<>();
        List<SmtMachineMaterialReturn> returnsFromPrepare = new ArrayList<>();
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "insertReturn", returnsFromMounting, returnsFromPrepare);
        Assert.assertEquals(0, returnsFromMounting.size());

        SmtMachineMaterialReturn smtMachineMaterialReturn = new SmtMachineMaterialReturn();
        returnsFromPrepare.add(smtMachineMaterialReturn);
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "insertReturn", returnsFromMounting, returnsFromPrepare);
        Assert.assertEquals(1, returnsFromMounting.size());
    }

    @Test
    public void insertMTLHistory() throws Exception {
        PsEntityPlanBasic nextProdPlan = new PsEntityPlanBasic();
        nextProdPlan.setWorkOrderNo("WorkOrder");
        List<SmtMachineMaterialPrepareDTO> prepareInserts = new ArrayList<>();
        SmtMachineMaterialPrepareDTO s1 = new SmtMachineMaterialPrepareDTO();
        SmtMachineMaterialPrepareDTO s2 = new SmtMachineMaterialPrepareDTO();
        prepareInserts.add(s1);
        prepareInserts.add(s2);
        s1.setWorkOrder("WorkOrder1");

        Map<String, String> itemCodeToName = new HashMap<>();
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "insertMTLHistory",
                nextProdPlan, prepareInserts, itemCodeToName);
        Assert.assertEquals(2, prepareInserts.size());

        s1.setWorkOrder("WorkOrder");
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "insertMTLHistory",
                nextProdPlan, prepareInserts, itemCodeToName);
        Assert.assertEquals(2, prepareInserts.size());
    }


    @Test
    public void doPrepareUpdates() throws Exception {
        Collection<SmtMachineMaterialPrepare> prepareUpdates = new ArrayList<>();
        Set<String> nextSideItemSet = new HashSet<>();
        Object o = Whitebox.invokeMethod(smtMachineMaterialPrepareService, "doPrepareUpdates",
                prepareUpdates, nextSideItemSet, "WorkOrderNo1", "WorkOrderNo2");
        Assert.assertEquals(0, ((List<SmtMachineMaterialReturn>) o).size());

        SmtMachineMaterialPrepare smtMachineMaterialPrepare1 = new SmtMachineMaterialPrepare();
        SmtMachineMaterialPrepare smtMachineMaterialPrepare2 = new SmtMachineMaterialPrepare();
        prepareUpdates.add(smtMachineMaterialPrepare1);
        prepareUpdates.add(smtMachineMaterialPrepare2);
        smtMachineMaterialPrepare1.setItemCode("ItemCode1");
        smtMachineMaterialPrepare2.setItemCode("ItemCode2");

        nextSideItemSet.add("ItemCode1");
        Object o1 = Whitebox.invokeMethod(smtMachineMaterialPrepareService, "doPrepareUpdates",
                prepareUpdates, nextSideItemSet, "WorkOrderNo1", "WorkOrderNo2");
        Assert.assertEquals(1, ((List<SmtMachineMaterialReturn>) o1).size());

    }

    @Test
    public void materialMoutingToPrepare() throws Exception {
        SmtMachineMaterialMouting smtMachineMaterialMouting = new SmtMachineMaterialMouting();
        Object o1 = Whitebox.invokeMethod(smtMachineMaterialPrepareService, "materialMoutingToPrepare",
                smtMachineMaterialMouting, "WorkOrderNo1");
        Assert.assertEquals("WorkOrderNo1", ((SmtMachineMaterialPrepareDTO) o1).getWorkOrder());

    }

    @Test
    public void getMTLHistoryH() throws Exception {
        PsEntityPlanBasic nextProdPlan = new PsEntityPlanBasic();
        nextProdPlan.setWorkOrderNo("WorkOrderNo1");
        Object o1 = Whitebox.invokeMethod(smtMachineMaterialPrepareService, "getMTLHistoryH",
                nextProdPlan);
        Assert.assertEquals("WorkOrderNo1", ((SmtMachineMTLHistoryH) o1).getWorkOrder());
    }

    @Test
    public void bindFeederNew() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        try{
            smtMachineMaterialPrepareService.bindFeederNew(dto);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.REELID_EMPTY, e.getExMsgId());
        }
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(null);
        dto.setMaterialTray("ZTE202403080001");
        dto.setSourceTask("2401923");
        dto.setFeederNo("***********");
        try{
            smtMachineMaterialPrepareService.bindFeederNew(dto);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.NOT_FOUNT_DATA_OR_NOT_INSTRUCTION, e.getExMsgId());
        }
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        pkCodeInfo1.setProductTask("2401924");
        pkCodeInfoList.add(pkCodeInfo1);
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        PowerMockito.when(imesPdaCommonService.pkCodeSourceTaskCheck(any(),any())).thenReturn(false);

        try{
            smtMachineMaterialPrepareService.bindFeederNew(dto);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.PRODPLAN_ID_NOT_MATCH_WORKORDER, e.getExMsgId());
        }
        PowerMockito.when(imesPdaCommonService.pkCodeSourceTaskCheck(any(),any())).thenReturn(true);
        PowerMockito.when(bSmtBomDetailService.getBSmtBomDetailBindFeederListNew(any())).thenReturn(null);
        try{
            smtMachineMaterialPrepareService.bindFeederNew(dto);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.CURRENT_NO_ITEMS, e.getExMsgId());
        }
        PkCodeInfo pkCodeInfo2 = new PkCodeInfo();
        pkCodeInfo2.setProductTask("2401923");
        pkCodeInfoList.add(pkCodeInfo2);
        PowerMockito.when(pkCodeInfoService.getList(any())).thenReturn(pkCodeInfoList);
        List<BSmtBomDetail> bomDetails = new ArrayList<>();
        BSmtBomDetail detail = new BSmtBomDetail();
        detail.setMaterialTray("ZTE202403080001");
        detail.setCfgLineId("xa");
        bomDetails.add(detail);
        PowerMockito.when(bSmtBomDetailService.getBSmtBomDetailBindFeederListNew(any())).thenReturn(bomDetails);
        List<SmtMachineMaterialPrepare> bindings = new ArrayList<>();
        SmtMachineMaterialPrepare prepare = new SmtMachineMaterialPrepare();
        prepare.setFeederNo("***********");
        prepare.setEnabledFlag("E");
        bindings.add(prepare);
        PowerMockito.when(smtMachineMaterialPrepareRepository.getBindByFeederNoOrReelId(any())).thenReturn(bindings);
        Assert.assertNotNull(smtMachineMaterialPrepareService.bindFeederNew(dto));
        dto.setHasUnbind(true);
        Assert.assertNotNull(smtMachineMaterialPrepareService.bindFeederNew(dto));
    }

    @Test
    public void bindFeederAndSaveHis() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        List<BSmtBomDetail> bomDetails = new ArrayList<>();
        dto.setLocationNo("");
        dto.setMaterialTray("ZTE2024030910001");
        dto.setFeederNo("2024030910001");

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setCfgLineId("x1");
        detail1.setMaterialTray("ZTE2024030910001");
        detail1.setHisFeederNo("");
        BSmtBomDetail detail2 = new BSmtBomDetail();
        detail2.setCfgLineId("x2");
        detail2.setMaterialTray("");
        detail2.setHisFeederNo("ZTE2024030910001");
        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setCfgLineId("x3");
        detail3.setMaterialTray("ZTE2024030910001");
        detail3.setHisFeederNo("ZTE2024030910001");
        bomDetails.add(detail1);
        bomDetails.add(detail2);
        bomDetails.add(detail3);

        BSmtBomDetail detail4 = new BSmtBomDetail();
        detail4.setCfgLineId("x3");
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "bindFeederAndSaveHis",
                dto, bomDetails, detail4, BigDecimal.ZERO);

        bomDetails.clear();
        bomDetails.add(detail3);
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "bindFeederAndSaveHis",
                dto, bomDetails, detail4, BigDecimal.ZERO);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void checkItemCodeHasBinded() throws Exception {
        SmtMachineMaterialPrepareDTO dto = new SmtMachineMaterialPrepareDTO();
        List<BSmtBomDetail> bomDetails = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        dto.setLocationNo("3-10-1");
        dto.setMachineNo("(3-10-1)3");
        try{
            Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                    dto, bomDetails, pkCodeInfo);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.LOCATION_NOT_MATCH, e.getExMsgId());
        }

        BSmtBomDetail detail1 = new BSmtBomDetail();
        detail1.setMachineNo("(2-10-1)");
        BSmtBomDetail detail2 = new BSmtBomDetail();
        detail2.setMachineNo("(3-10-1)3");
        detail2.setLocationNo("3-11-1");
        BSmtBomDetail detail3 = new BSmtBomDetail();
        detail3.setMachineNo("(3-10-1)3");
        detail3.setLocationNo("3-10-1");
        detail3.setItemCode("002403090001");
        bomDetails.add(detail1);
        bomDetails.add(detail2);
        bomDetails.add(detail3);
        pkCodeInfo.setItemCode("002403090050");
        try{
            Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                    dto, bomDetails, pkCodeInfo);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.REEL_ID_NOT_MATCH, e.getExMsgId());
        }
        pkCodeInfo.setItemCode("002403090001");
        Assert.assertNotNull(Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                dto, bomDetails, pkCodeInfo));

        dto.setLocationNo("");
        dto.setMaterialTray("ZTE2024030910001");
        dto.setFeederNo("2024030910001");

        bomDetails.clear();
        try{
            Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                    dto, bomDetails, pkCodeInfo);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.NOT_FIND_FEEDER_BIND_LOC, e.getExMsgId());
        }
        detail1.setItemCode("002403090001");
        detail1.setHisFeederNo("F002403090001");
        bomDetails.add(detail1);
        try{
            Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                    dto, bomDetails, pkCodeInfo);
        }catch (MesBusinessException e){
            Assert.assertEquals(MessageId.ITEM_CODE_HAS_BINDED_FEEDER, e.getExMsgId());
        }
        detail1.setHisFeederNo("2024030910001");
        detail2.setMaterialTray("ZTE2024030910001");
        detail3.setNextReelId("ZTE2024030910001");
        BSmtBomDetail detail4 = new BSmtBomDetail();
        bomDetails.add(detail2);
        bomDetails.add(detail3);
        bomDetails.add(detail4);
        Assert.assertNotNull(Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                dto, bomDetails, pkCodeInfo));

        bomDetails.clear();
        detail1.setMaterialTray("");
        detail1.setHisFeederNo("");
        detail2.setItemCode("002403090001");
        detail2.setHisFeederNo("");
        detail2.setMaterialTray("ZTE2024030910001x");
        detail3.setItemCode("002403090050");
        detail3.setNextReelId("");
        bomDetails.add(detail1);
        bomDetails.add(detail2);
        bomDetails.add(detail3);
        Assert.assertNotNull(Whitebox.invokeMethod(smtMachineMaterialPrepareService, "checkItemCodeHasBinded",
                dto, bomDetails, pkCodeInfo));
    }


    @Test
    public void handleCfgIdNotBlank() throws Exception {
        List<SmtMachineMaterialPrepare> smtMachineMaterialPrepareList = new ArrayList() {{
            add(new SmtMachineMaterialPrepare() {{

            }});
            add(new SmtMachineMaterialPrepare() {{
                setItemCode("111");
            }});
            add(new SmtMachineMaterialPrepare() {{
                setItemCode("222");
            }});
            add(new SmtMachineMaterialPrepare() {{
                setItemCode("222");
                setObjectId("333");
            }});
        }};

        Set<String> itemSet = new HashSet<>();
        itemSet.add("222");

        Set<String> nextReelIdSet = new HashSet<>();
        nextReelIdSet.add("333");

        Assert.assertNotNull(smtMachineMaterialPrepareList);
        Whitebox.invokeMethod(smtMachineMaterialPrepareService, "handleCfgIdNotBlank", smtMachineMaterialPrepareList,
                itemSet, nextReelIdSet, new ArrayList<>(), new PsEntityPlanBasic());
    }
}
