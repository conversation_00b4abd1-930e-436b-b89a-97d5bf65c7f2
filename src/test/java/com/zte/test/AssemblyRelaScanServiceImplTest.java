package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.impl.AssemblyRelaScanServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BaItem;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.PsWipInfoRepository;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.AssemblyRelaScanDTO;
import com.zte.interfaces.dto.FlowControlInfoDTO;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@PrepareForTest({BasicsettingRemoteService.class,HttpRemoteUtil.class,MESHttpHelper.class, HttpClientUtil.class, AssemblyRelaScanServiceImpl.class})
public class AssemblyRelaScanServiceImplTest extends PowerBaseTestCase {
	
	private RetCode retCode;
	
	@Mock
	private ProdBindingSettingRepository prodBindingSettingRepository;
	
	@Mock
	private PsWipInfoRepository psWipInfoRepository;
	
	@Mock
	private WipExtendIdentificationRepository wipExtendIdentificationRepository;

	@InjectMocks
	private AssemblyRelaScanServiceImpl assemblyRelaScanService;

	public void init(){
		PowerMockito.mockStatic(HttpRemoteUtil.class);
	}
	@Test
	public void subSnScan() throws Exception {
		AssemblyRelaScanServiceImpl service = PowerMockito.spy(new AssemblyRelaScanServiceImpl());
		service.setProdBindingSettingRepository(prodBindingSettingRepository);
		service.setPsWipInfoRepository(psWipInfoRepository);
		service.setWipExtendIdentificationRepository(wipExtendIdentificationRepository);
		AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
		FlowControlInfoDTO checkFlowRsult = new FlowControlInfoDTO();
		List<ProdBindingSettingDTO> bindList = new ArrayList<ProdBindingSettingDTO>();
		List<BaItem> itemInfoList = new ArrayList<BaItem>();
		List<String> snList = new ArrayList<String>();
		snList.add("123");
		dto.setSubSnList(snList);
		BaItem itemInfo = new BaItem();
		itemInfoList.add(itemInfo);
		ProdBindingSettingDTO bindDto = new ProdBindingSettingDTO();
		bindList.add(bindDto);
		checkFlowRsult.setResultType("OK");
		dto.setToPassWorkStaion(true);
		retCode = PowerMockito.mock(RetCode.class);		
		PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);	
		List<PsWorkOrderDTO> workOrderList = new ArrayList<PsWorkOrderDTO>();
		PsWorkOrderDTO workOrder= new PsWorkOrderDTO();
		workOrderList.add(workOrder);
		dto.setMainWorkOrder(workOrder);
		doReturn(workOrderList).when(service).getWorkOrderInfo(dto);
		doReturn(checkFlowRsult).when(service).checkFlow(dto);
		doReturn(itemInfoList).when(service).getItemCode(dto);
		Map<String, String> replaceMap=new HashMap<>();
		doReturn(retCode).when(service).validateItemInfo(replaceMap,itemInfoList,bindList);
		PsWipInfo wipInfo = new PsWipInfo();
		List<WipExtendIdentification> tempList = new ArrayList<>();
		when(psWipInfoRepository.getWipInfoBySn(anyObject())).thenReturn(wipInfo);
		when(prodBindingSettingRepository.getBindingInfoByItemNew(anyObject())).thenReturn(bindList);
		when(wipExtendIdentificationRepository.getList(anyObject())).thenReturn(tempList);
		Assert.assertNotNull(service.subSnScan(dto, checkFlowRsult));
	}

	@Test
	public void getReplaceItemByErp() throws Exception {
		List<String> needReplaceItem = new ArrayList<>();
		String s1 = "0001023165";
		needReplaceItem.add(s1);
		SysLookupTypesDTO sysLookupTypesDTO = null;
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(sysLookupTypesDTO);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, true);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		sysLookupTypesDTO = new SysLookupTypesDTO();
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString(),Mockito.anyString())).thenReturn(sysLookupTypesDTO);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, false);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
		sysLookupTypesDTO.setLookupMeaning("123");

		PowerMockito.mockStatic(HttpRemoteUtil.class);
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("x-emp-no", "dasdas");
		headerMap.put("x-factory-id", "52");
		PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class);
		PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(null);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, true);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_MATERIAL_SUBSTITUTION_RELATIONSHIP, e.getMessage());
		}
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
				JSON.toJSONString(new ServiceData() {{
					setBo(Lists.newArrayList(""));
					setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
					setOther(new HashMap<>());
				}})
		);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, false);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.GET_ITEM_INFO_FAIL, e.getMessage());
		}
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
				JSON.toJSONString(new ServiceData() {{
					setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
					setOther(new HashMap<>());
				}})
		);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, true);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.GET_ITEM_INFO_FAIL, e.getMessage());
		}
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(
				JSON.toJSONString(new ServiceData() {{
					setBo(Lists.newArrayList(""));
					setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
				}})
		);
		try {
			assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, true);
		} catch (Exception e) {
			Assert.assertEquals(MessageId.GET_ITEM_INFO_COUNT_FAIL, e.getMessage());
		}
		ServiceData serviceData=new ServiceData();
		List<MtlRelatedItemsEntityDTO> entityDTOS = new ArrayList<>();
		MtlRelatedItemsEntityDTO dto1 = new MtlRelatedItemsEntityDTO();
		dto1.setInventoryItemCode("122393751064");
		dto1.setRelatedItemCode("122393751115");
		entityDTOS.add(dto1);
		serviceData.setBo(entityDTOS);
		Map<String, Object> map = new HashMap<>();
		map.put("totalCount", 1);
		serviceData.setOther(map);
		PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(serviceData));
		// PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
		assemblyRelaScanService.getReplaceItemByErp(needReplaceItem, true);
	}

	/* Started by AICoder, pid:a5f87i9e4bn19c914b7a09db0047cb7200c970b6 */

	@Test
	public void checkParamsTest() {
		AssemblyRelaScanDTO assemblyRelaScanDTO = new AssemblyRelaScanDTO();
		RetCode retCode1 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.ITEM_CODE_EMPTY.equals(retCode1.getMsgId()));

		assemblyRelaScanDTO.setItemCode("123456789123456");
		RetCode retCode2 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.WORKORDER_NO_IS_NOT_NULL.equals(retCode2.getMsgId()));

		assemblyRelaScanDTO.setWorkOrderNo("test");
		assemblyRelaScanDTO.setSubSnScan(true);
		RetCode retCode3 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.PROCESS_IS_NOT_NULL.equals(retCode3.getMsgId()));

		assemblyRelaScanDTO.setProcessCode("test");
		RetCode retCode4 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.PROCESS_IS_NOT_NULL.equals(retCode4.getMsgId()));

		assemblyRelaScanDTO.setSubSnScan(false);
		RetCode retCode5 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.PROCESS_IS_NOT_NULL.equals(retCode5.getMsgId()));

		assemblyRelaScanDTO.setMainSn("test");
		assemblyRelaScanDTO.setSubSnScan(true);
		RetCode retCode6 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.SUB_SN_IS_NOT_NULL.equals(retCode6.getMsgId()));

		assemblyRelaScanDTO.setMainSn("test");
		assemblyRelaScanDTO.setSubSnScan(false);
		assemblyRelaScanDTO.setExternalScan(true);
		RetCode retCode7 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.SUB_SN_IS_NOT_NULL.equals(retCode7.getMsgId()));


		assemblyRelaScanDTO.setMainSn("test");
		assemblyRelaScanDTO.setSubSnScan(true);
		assemblyRelaScanDTO.setExternalScan(true);
		assemblyRelaScanDTO.setSubSnList(new ArrayList<>());
		RetCode retCode11 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.SUB_SN_IS_NOT_NULL.equals(retCode11.getMsgId()));

		assemblyRelaScanDTO.setMainSn("test");
		assemblyRelaScanDTO.setSubSnScan(true);
		assemblyRelaScanDTO.setExternalScan(false);
		assemblyRelaScanDTO.setSubSnList(new ArrayList<String>(){{add("test");}});
		RetCode retCode12 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(RetCode.SUCCESS_MSGID.equals(retCode12.getMsgId()));

		assemblyRelaScanDTO.setSubSnScan(false);
		assemblyRelaScanDTO.setExternalScan(false);
		assemblyRelaScanDTO.setIsPassWorkStaion("2");
		RetCode retCode8 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(RetCode.SUCCESS_MSGID.equals(retCode8.getMsgId()));

		assemblyRelaScanDTO.setIsPassWorkStaion("1");
		RetCode retCode9 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(RetCode.SUCCESS_MSGID.equals(retCode9.getMsgId()));

		assemblyRelaScanDTO.setSubSnScan(true);
		assemblyRelaScanDTO.setWorkStationCode("");
		assemblyRelaScanDTO.setExternalScan(true);
		RetCode retCode10 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.WORK_STATION_IS_NOT_NULL.equals(retCode10.getMsgId()));


		assemblyRelaScanDTO.setWorkStationCode("test");
		assemblyRelaScanDTO.setExternalScan(true);
		RetCode retCode13 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.WORK_STATION_IS_NOT_NULL.equals(retCode13.getMsgId()));


		assemblyRelaScanDTO.setExternalScan(false);
		assemblyRelaScanDTO.setWorkStationCode("");
		RetCode retCode14 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(MessageId.WORK_STATION_IS_NOT_NULL.equals(retCode14.getMsgId()));

		assemblyRelaScanDTO.setExternalScan(false);
		assemblyRelaScanDTO.setWorkStationCode("test");
		RetCode retCode15 = assemblyRelaScanService.checkParams(assemblyRelaScanDTO);
		Assert.assertTrue(RetCode.SUCCESS_MSGID.equals(retCode15.getMsgId()));

	}
	/* Ended by AICoder, pid:a5f87i9e4bn19c914b7a09db0047cb7200c970b6 */
}
