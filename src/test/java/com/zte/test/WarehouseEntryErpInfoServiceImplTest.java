package com.zte.test;

import com.zte.application.WarehouseEntryErpInfoService;
import com.zte.application.impl.WarehouseEntryErpInfoServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BoardOnline;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.WarehouseEntryInfoRepository;
import com.zte.interfaces.assembler.WarehouseEntryInfoAssembler;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.domain.model.WarehouseEntryErpInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WarehouseEntryErpInfoEntityDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class, BasicsettingRemoteService.class, WarehouseEntryInfoAssembler.class})
public class WarehouseEntryErpInfoServiceImplTest {

    @InjectMocks
    private WarehouseEntryErpInfoServiceImpl warehouseEntryErpInfoServiceImpl;

    @Mock
    private WarehouseEntryInfoServiceImpl warehouseEntryInfoServiceImpl;

    @Mock
    private WarehouseEntryErpInfoRepository warehouseEntryErpInfoRepository;

    @Mock
    private WarehouseEntryInfoRepository warehouseEntryInfoRepository;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private EmailUtils emailUtils;

    @Before
    public void init() {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.mockStatic(WarehouseEntryInfoAssembler.class);
    }


    @Test
    public void pageList() throws Exception {
        WarehouseEntryErpInfoEntityDTO record = new WarehouseEntryErpInfoEntityDTO();
        record.setPage(1);
        record.setRows(1);
        Assert.assertNotNull(warehouseEntryErpInfoServiceImpl.pageList(record));
    }

    @Test
    public void update() throws Exception {
        WarehouseEntryErpInfoEntityDTO record = new WarehouseEntryErpInfoEntityDTO();
        record.setPage(1);
        record.setRows(1);
        Assert.assertNotNull(warehouseEntryErpInfoServiceImpl.update(record));
    }

    @Test
    public void excuteSubCardErpInfo() throws Exception {
        WarehouseEntryErpInfoEntityDTO record = new WarehouseEntryErpInfoEntityDTO();

        List<WarehouseEntryErpInfoEntityDTO> erpList = new ArrayList<>();
        WarehouseEntryErpInfoEntityDTO dto = new WarehouseEntryErpInfoEntityDTO();
        dto.setErpId("1");
        erpList.add(dto);

        PowerMockito.when(warehouseEntryErpInfoRepository.getList(Mockito.any()))
                .thenReturn(erpList);

        List<WarehouseEntryErpInfoEntityDTO> errorList = new ArrayList<>();
        WarehouseEntryErpInfoEntityDTO errorDto = new WarehouseEntryErpInfoEntityDTO();
        errorDto.setErpId("122");
        errorDto.setErpParam("{\"warehouseEntryId\":\"102500\"}");
        errorList.add(errorDto);
        PowerMockito.when(warehouseEntryErpInfoRepository.getList(Mockito.any()))
                .thenReturn(errorList);

        SysLookupTypesDTO sysDto = new SysLookupTypesDTO();
        sysDto.setLookupMeaning("123");
        PowerMockito.when(BasicsettingRemoteService
                        .getSysLookUpValue(Constant.LOOKUP_CODE_2044, Constant.LOOKUP_CODE_2044002))
                .thenReturn(sysDto);

        warehouseEntryErpInfoServiceImpl.excuteSubCardErpInfo(record);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);

    }

    /* Started by AICoder, pid:m7094a2d996fb8d14a5e080cd134725457902f53 */
    @Test
    public void automaticsSanReceiveWarehouseEntryInfoTest() throws Exception {
        PowerMockito.when(warehouseEntryInfoRepository.getWarehouseEntryInfoDetailBySn(Mockito.any())).thenReturn(null);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_NOT_IN_BILL, e.getMessage());
        }
        WarehouseEntryInfoDTO dto = new WarehouseEntryInfoDTO();
        dto.setSn("************");
        List<WarehouseEntryInfoDTO> snList = new ArrayList<>();
        snList.add(dto);
        PowerMockito.when(warehouseEntryInfoRepository.getWarehouseEntryInfoDetailBySn(Mockito.any())).thenReturn(snList);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(Mockito.any())).thenReturn(null);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_RIGHT_IMU, e.getMessage());
        }
        List<BoardOnline> boardOnlines = new ArrayList<>();
        BoardOnline boardOnline = new BoardOnline();
        boardOnline.setImuId(new BigDecimal("42"));
        boardOnline.setBoardSn(new BigDecimal("1"));
        boardOnline.setProdplanId(new BigDecimal("8888777"));
        boardOnline.setScanDate(new Date());
        boardOnlines.add(boardOnline);
        Page<BoardOnline> resultPage = new Page<BoardOnline>();
        resultPage.setRows(null);
        resultPage.setTotalPage(1);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(Mockito.any())).thenReturn(resultPage);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_RIGHT_IMU, e.getMessage());
        }
        resultPage.setRows(boardOnlines);
        List<SysLookupTypesDTO> sysLookupTypesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("N");
        sysLookupTypesList.add(sysLookupTypesDTO);
        dto.setBillType("5");
        dto.setZjSubcardFlag("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(sysLookupTypesList);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        dto.setStatus("0");
        dto.setBillType("4");
        WarehouseEntryInfo warehouseEntryInfo = new WarehouseEntryInfo();
        PowerMockito.when(WarehouseEntryInfoAssembler.toEntity(Mockito.any())).thenReturn(warehouseEntryInfo);
        warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        dto.setBillType("2");
        dto.setStockName("CS1078");
        dto.setStockType("整机车间库");
        sysLookupTypesDTO.setLookupMeaning("CS1077");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(sysLookupTypesList);
        PowerMockito.when(WarehouseEntryInfoAssembler.toEntity(Mockito.any())).thenReturn(warehouseEntryInfo);
        warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        dto.setStockType("半成品K1库");
        warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        dto.setStockType("机车库");
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        dto.setStockName("CS1077");
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        dto.setBillType("3");
        dto.setStockName("CS1078");
        dto.setStockType("整机车间库");
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        dto.setStatus("3");
        dto.setZjSubcardFlag("Y");
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        dto.setZjSubcardFlag("N");
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("Y");
        dto.setZjSubcardFlag("Y");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(sysLookupTypesList);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("************");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NOT_IN_SCANRECEIVE, e.getMessage());
        }
        WarehouseEntryInfoDTO dto1 = new WarehouseEntryInfoDTO();
        dto1.setSn("************");
        snList.add(dto1);
        PowerMockito.when(warehouseEntryInfoRepository.getWarehouseEntryInfoDetailBySn(Mockito.any())).thenReturn(snList);
        try {
            warehouseEntryErpInfoServiceImpl.automaticsSanReceiveWarehouseEntryInfo("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_MORE_THAN_ONE, e.getMessage());
        }
    }
    /* Ended by AICoder, pid:m7094a2d996fb8d14a5e080cd134725457902f53 */

}
