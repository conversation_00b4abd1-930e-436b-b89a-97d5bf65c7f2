package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.PsEntityPlanBasic;
import com.zte.domain.model.PsTask;
import com.zte.domain.model.PsWorkOrderSmt;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;

@PrepareForTest({HttpRemoteService.class, MicroServiceRestUtil.class, MESHttpHelper.class,JSON.class,
        ServiceDataBuilderUtil.class,ConstantInterface.class, HttpRemoteUtil.class,JacksonJsonConverUtil.class,MesBusinessException.class})
public class PlanscheduleRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private PlanscheduleRemoteService planscheduleRemoteService;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void selectPsWorkOrderSmtList() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Set<String> workOrderNoList = new HashSet<>();
        workOrderNoList.add("7368539-SMT-A5501");
        Assert.assertNotNull(PlanscheduleRemoteService.selectPsWorkOrderSmtList(workOrderNoList));
    }

    @Test
    public void getWorkOrderInfoByProdplanIdList() throws Exception {
        List<String> prodplanIdList = new LinkedList<>();
        prodplanIdList.add("123");
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO)).thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO).toString()).thenReturn("[]");
        PlanscheduleRemoteService.getWorkOrderInfoByProdplanIdList(prodplanIdList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void findBasicWorkOrderInfo() {
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO)).thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO).toString()).thenReturn("[]");
        PlanscheduleRemoteService.findBasicWorkOrderInfo("11111");

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(null);
        try {
            PlanscheduleRemoteService.findBasicWorkOrderInfo("11111");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getExMsgId());
        }

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(json);
        PowerMockito.when(json.get(MpConstant.JSON_BO)).thenReturn(null);
        try {
            PlanscheduleRemoteService.findBasicWorkOrderInfo("11111");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getExMsgId());
        }
    }

    @Test
    public void getList() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Assert.assertNotNull(PlanscheduleRemoteService.getList("workOrderNoList"));
    }

    @Test
    public void getEnvAttrBatchByIds() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Assert.assertNotNull(PlanscheduleRemoteService.getEnvAttrBatchByIds(new LinkedList<>()));
    }

    @Test
    public void getBsmtBomDetailListOfTwoDip() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Assert.assertNotNull(PlanscheduleRemoteService.getBsmtBomDetailListOfTwoDip(""));
    }

    @Test
    public void selectWorkOrderBasicByActualStartDateAndEndTime() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}]}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Assert.assertNotNull(PlanscheduleRemoteService.selectWorkOrderBasicByActualStartDateAndEndTime(null));
    }

    @Test
    public void getPsWorkOrderSmtList() throws Exception {
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"workOrderId\":\"3bd8dcc1-a7fc-4aeb-9bf5-2d5d5f243868\",\"cfgHeaderId\":\"beda21c1-93ef-42b3-bdfc-58e3410086fb\",\"pcbQty\":2,\"remark\":null,\"createBy\":\"10118128\",\"createDate\":\"2021-05-08 16:49:52\",\"lastUpdatedBy\":\"10210021\",\"lastUpdatedDate\":\"2021-05-11 09:17:19\",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":55,\"entityId\":2,\"cfgName\":\"ZXSM 2500C NCPI\",\"cfgVersion\":\"BEB\",\"workOrderNo\":\"7368539-SMT-A5501\",\"transferStrategy\":\"3\",\"stockStatus\":\"首备完成\",\"craftSection\":null,\"lineCode\":null,\"sourceTaskList\":null,\"itemNo\":null,\"workOrderStatus\":null,\"maintenance\":null,\"stationName\":null}}";
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(str);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(any(), any(), any(), any(), any()))
                .thenReturn(jsonNode);
        Assert.assertNotNull(PlanscheduleRemoteService.getPsWorkOrderSmtList("workOrderNoList"));
    }


    @Test
    public void querySourceTaskInfo() throws Exception {
        PsWorkOrderBasicDTO dto = new PsWorkOrderBasicDTO();
        List<PsWorkOrderBasicDTO> list = new LinkedList<>();
        PsWorkOrderBasicDTO a1 = new PsWorkOrderBasicDTO();
        list.add(a1);

        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(list));

        PowerMockito.when(HttpRemoteService.postHandler(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(json);
        Assert.assertNotNull(planscheduleRemoteService.querySourceTaskInfo(dto));
    }

    @Test
    public void getScanList() throws Exception {
        Map<String, Object> map = new HashMap<>();

        List<PsTaskScanlistDTO> list = new LinkedList<>();
        PsTaskScanlistDTO a1 = new PsTaskScanlistDTO();
        list.add(a1);

        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(json.asText()).thenReturn("0000");

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        Assert.assertNotNull(planscheduleRemoteService.getScanList(map));
    }

    @Test
    public void insertOrUpdatePsWorkOrderSmtInfo() throws Exception {
        PsWorkOrderSmtDTO dto = new PsWorkOrderSmtDTO();
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.anyObject()))
                .thenReturn(json);
        Assert.assertEquals(1,planscheduleRemoteService.insertOrUpdatePsWorkOrderSmtInfo(dto));

    }

    @Test
    public void getProcessCodeAndLineList() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class,MesBusinessException.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList());
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        try {
            planscheduleRemoteService.getProcessCodeAndLineList("628805421300-装配5201");
        }catch (Exception e){
            Assert.assertEquals(MessageId.WORKORDER_NOT_FIND, e.getMessage());
        }
    }

    @Test
    public void updateInputQtyByWorkOrderNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new Integer(2));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNull(planscheduleRemoteService.updateInputQtyByWorkOrderNo("", 1L));
    }

    @Test
    public void updateOutPutQtyByWorkOrderNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new Integer(2));
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNull(planscheduleRemoteService.updateOutPutQtyByWorkOrderNo("", 1L));
    }

    @Test
    public void getSnTaskRel() throws Exception {
        List<String> sns = new ArrayList<>();
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(new ArrayList<>()));
        PowerMockito.when(json.asText()).thenReturn("0000");

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        Assert.assertNull(planscheduleRemoteService.getSnTaskRel(sns));
    }

    @Test
    public void queryFirstWorkOrderByProdplanId() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PsWorkOrderBasicDTO psWorkOrderSmt = new PsWorkOrderBasicDTO();
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(psWorkOrderSmt));
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        Assert.assertNotNull(planscheduleRemoteService.queryFirstWorkOrderByProdplanId("7792420"));
    }

    @Test
    public void queryWorkOrderNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PsWorkOrderBasicDTO psWorkOrderSmt = new PsWorkOrderBasicDTO();
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(psWorkOrderSmt));
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        Assert.assertNotNull(planscheduleRemoteService.queryWorkOrderNo("7792420", "SMT-B"));
    }

    @Test
    public void getPcbQty() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);

        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(psWorkOrderSmt));
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        Assert.assertNull(planscheduleRemoteService.getPcbQty("17a0770a-28a1-467b-b772-b5c895c0b0c3"));
    }


    @Test
    public void getPsTaskListByWorkOrderNos() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        List<PsTask> list = new LinkedList<>();
        PsTask a1 = new PsTask();
        list.add(a1);

        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(json);
        List<String> workOrderNoList = new ArrayList<>();
        workOrderNoList.add("111");
        Assert.assertNull(planscheduleRemoteService.getPsTaskListByWorkOrderNos(workOrderNoList));
    }

    @Test
    public void getWorkOrderInfoByWorkOrderNo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class);
        PsEntityPlanBasic psEntityPlanBasic = new PsEntityPlanBasic();
        psEntityPlanBasic.setWorkOrderNo("test");
        List<PsEntityPlanBasic> psInfoList = new ArrayList<>();
        psInfoList.add(psEntityPlanBasic);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(psInfoList);

        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));

        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson);
        PsEntityPlanBasic result = planscheduleRemoteService.getWorkOrderInfoByWorkOrderNo("test");
        Assert.assertEquals("test", result.getWorkOrderNo());
    }

    @Test
    public void getPsWorkSmtListByProdPlanIdTest() throws Exception {
        String prodPlanId = "test";
        ArrayList<PsWorkOrderSmt> list = new ArrayList<>();
        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        list.add(psWorkOrderSmt);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson);
        try {
            planscheduleRemoteService.getPsWorkSmtListByProdPlanId(prodPlanId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_FAILED, e.getMessage());
        }
        ServiceData ret1 = new ServiceData();
        ret1.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret1.setBo(list);
        JsonNode rtJson1 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret1));
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson1);
        planscheduleRemoteService.getPsWorkSmtListByProdPlanId(prodPlanId);
    }

    @Test
    public void getPsTaskAndWorkOrder() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        List<PsTaskBasicDTO> dtoList = new ArrayList<>();
        PsTaskBasicDTO dto = new PsTaskBasicDTO();
        dto.setProdplanId("123123");
        dto.setWorkOrderNo("123-a123");
        dtoList.add(dto);
        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(dtoList);
                        }}))
                );
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        dto1.setLookupMeaning("123123");
        lookupTypesDTOList.add(dto1);
        Assert.assertNotNull(PlanscheduleRemoteService.getPsTaskAndWorkOrder(dto, lookupTypesDTOList));
    }


    @Test
    public void getPrintScene() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, ConstantInterface.class, HttpRemoteService.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");

        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(null);
                        }}))
                );
        PlanscheduleRemoteService.getPrintScene("prodplanId");
        List<PsTaskExtraDTO> dtoList = new ArrayList<>();
        PsTaskExtraDTO dto = new PsTaskExtraDTO();
        dto.setProdplanId("123123");
        dtoList.add(dto);
        PageRows<PsTaskExtraDTO> pageRows = new PageRows<>();
        pageRows.setRows(dtoList);
        PowerMockito.when(HttpRemoteService.pointToPointSelective(
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(
                        JSON.toJSONString(new ServiceData() {{
                            setBo(pageRows);
                        }}))
                );
        List<SysLookupTypesDTO> lookupTypesDTOList = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        dto1.setLookupMeaning("123123");
        lookupTypesDTOList.add(dto1);
        Assert.assertNotNull(PlanscheduleRemoteService.getPrintScene("prodplanId"));
    }

    @Test
    public void updateTaskCompleteQtyAndStatus() throws Exception {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PlanscheduleRemoteService.updateTaskCompleteQtyAndStatus("88997120", new BigDecimal("2"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.STR_Y, runNormal);
    }

    @Test
    public void getBasicWorkOrder() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        Assert.assertNotNull(planscheduleRemoteService.getBasicWorkOrder(new HashMap<>()));
    }

    @Test
    public void selectWorkNoOfComMachineTest() throws Exception {
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MicroServiceRestUtil.class,MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(new ArrayList<String>(){{add("test");}});
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNull(planscheduleRemoteService.selectWorkNoOfComMachine("test", "test", "test"));

    }

    @Test
    public void selectPsTaskOfWmesByTaskNosTest() throws Exception {
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MicroServiceRestUtil.class,MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        List<PsTaskDTO> psTaskDTOS = new ArrayList<>();
        psTaskDTOS.add(new PsTaskDTO());
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(psTaskDTOS);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        planscheduleRemoteService.selectPsTaskOfWmesByTaskNos(new HashSet<>());
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        Assert.assertNotNull( planscheduleRemoteService.selectPsTaskOfWmesByTaskNos(new HashSet<>()));
    }

    @Test
    public void getPsWorkSmtBySourceTaskAndLineCodeTest() throws Exception {
        String prodPlanId = "test";
        String workOrderNo = "123";
        String lineCode= "SMT-HY001";

        PsWorkOrderSmt psWorkOrderSmt = new PsWorkOrderSmt();
        psWorkOrderSmt.setCfgHeaderId("1111");
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(psWorkOrderSmt);
        JsonNode rtJson = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret));

        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(null);
        try {
            PsWorkOrderSmt result = planscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(prodPlanId, workOrderNo, lineCode);
            Assert.assertNull(result);
        } catch (Exception e) {
        }

        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson);
        try {
            planscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(prodPlanId, workOrderNo, lineCode);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CALL_SERVICE_FAILED,e.getMessage());
        }

        ServiceData ret1 = new ServiceData();
        ret1.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret1.setBo(psWorkOrderSmt);
        JsonNode rtJson1 = JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(ret1));
        PowerMockito.when(HttpRemoteService.pointToPointSelective(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyMap()))
                .thenReturn(rtJson1);
        try {
            PsWorkOrderSmt result1 = planscheduleRemoteService.getPsWorkSmtBySourceTaskAndLineCode(prodPlanId, workOrderNo, lineCode);
            Assert.assertEquals(result1.getCfgHeaderId(),psWorkOrderSmt.getCfgHeaderId());
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void getWorkOrderInfoCountTest(){
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo(null);
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        int workOrderInfoCount = planscheduleRemoteService.getWorkOrderInfoCount(new HashMap<>());
        Assert.assertEquals(workOrderInfoCount,0);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData() {{
                    setBo("1");
                    setCode(new RetCode() {{
                        setCode(RetCode.SUCCESS_CODE);
                    }});
                }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("1");
        Assert.assertNotNull(planscheduleRemoteService.getWorkOrderInfoCount(new HashMap<>()));

    }
}
