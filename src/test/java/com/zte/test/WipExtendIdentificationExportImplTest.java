package com.zte.test;

import com.zte.application.impl.WipExtendIdentificationExportImpl;
import com.zte.application.impl.WipExtendIdentificationServiceImpl;
import com.zte.domain.model.WipExtendIdentification;
import com.zte.domain.model.WipExtendIdentificationRepository;
import com.zte.interfaces.dto.AssemblyRelationshipQueryDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
public class WipExtendIdentificationExportImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WipExtendIdentificationExportImpl service;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;

    @Mock
    private WipExtendIdentificationServiceImpl wipExtendIdentificationService;

    @Test
    public void countExportTotal() {
        Assert.assertNotNull(service.countExportTotal(new AssemblyRelationshipQueryDTO()));
    }

    @Test
    public void queryExportData() {
        AssemblyRelationshipQueryDTO assemblyRelationshipQueryDTO = new AssemblyRelationshipQueryDTO();
        service.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        service.queryExportData(assemblyRelationshipQueryDTO, 1, 10);
        List<WipExtendIdentification> wipExtendIdentificationList = new ArrayList<>();
        wipExtendIdentificationList.add(new WipExtendIdentification());
        PowerMockito.when(wipExtendIdentificationRepository.assemblyRelationshipPage(any())).thenReturn(wipExtendIdentificationList);
        Assert.assertNotNull(service.queryExportData(assemblyRelationshipQueryDTO, 1, 10));
    }

}
