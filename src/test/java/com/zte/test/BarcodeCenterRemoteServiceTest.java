package com.zte.test;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.BarcodeRegisterDTO;
import com.zte.interfaces.dto.GenBarcodeParamDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyString;

/**
 * 条码中心接口调用单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, BasicsettingRemoteService.class, MESHttpHelper.class, JacksonJsonConverUtil.class})
public class BarcodeCenterRemoteServiceTest {
	@InjectMocks
	private BarcodeCenterRemoteService barcodeCenterRemoteService;
	@Mock
	private ObjectMapper mapperInstance;
	@Mock
	private JsonNode json;
	
	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
		PowerMockito.mockStatic(HttpRemoteUtil.class, BasicsettingRemoteService.class, MESHttpHelper.class);
		try {
			PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString()))
					.thenReturn(Lists.newArrayList(new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052001)); setLookupMeaning("http://dev.ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052002)); setLookupMeaning("10001");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052003)); setLookupMeaning("A0509777019518562304");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052004)); setLookupMeaning("9648B76C92DBA3BDAE028233271D80E0A6B10F416714E40C2FBCFF5FF48BF678");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052005)); setLookupMeaning("/barcode/update");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052006)); setLookupMeaning("/barcode/expandQuery");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052007)); setLookupMeaning("/barcode/barcodeQuery");
					}}, new SysLookupTypesDTO () {{
						setLookupCode(new BigDecimal(1004052008)); setLookupMeaning("/barcode/register");
					}}));
		} catch (Exception e) {
			Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
		}
	}

	@Test
    public void barcodeGenerate() throws Exception {
		ServiceData<List<String>> rt = new ServiceData<>();
		List<String> snList = new ArrayList<>();
		snList.add("777766600001");
		snList.add("777766600002");
		snList.add("777766600003");
		rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		rt.setBo(snList);
		String msg = JSONObject.toJSONString(rt);
		
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(),Mockito.anyMap(),Mockito.anyString(),Mockito.anyString())).thenReturn(msg);
		
		GenBarcodeParamDTO genBarcodeParamDTO = new GenBarcodeParamDTO();
    	genBarcodeParamDTO.setCategoryCode(BusinessConstant.ZJ_BARCODE_GENERATE_CATEGORY);
    	// 条码生成参数。分别对应规则内TASK_NO12和BOARD_SNID
    	Map<String, String> paramMap = new HashMap<>();
    	// 任务号截取12位
    	paramMap.put("TASK_NO12", "123456789ABC");
    	paramMap.put("BOARD_SNID", "777766612345");
    	genBarcodeParamDTO.setParamMap(paramMap);
    	genBarcodeParamDTO.setCount(NumConstant.NUM_ONE);
    	genBarcodeParamDTO.setSourceSystem(BusinessConstant.SOURCESYS_IMES);
		Assert.assertNotNull(barcodeCenterRemoteService.barcodeGenerate(genBarcodeParamDTO));
	}
	
	@Test
    public void barcodeRegister() throws Exception {
		ServiceData<List<String>> rt = new ServiceData<>();
		List<String> snList = new ArrayList<>();
		snList.add("777766611111");
		snList.add("777766611112");
		snList.add("777766611113");
		rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		rt.setBo(snList);
		String msg = JSONObject.toJSONString(rt);

		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(),Mockito.any(),Mockito.anyString(),Mockito.any())).thenReturn(msg);
		
		List<BarcodeRegisterDTO> list = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			BarcodeRegisterDTO registerDTO = new BarcodeRegisterDTO();
			registerDTO.setBarcode("7777666" + (11111 + i));
			registerDTO.setItemCode("125000860001ABC");
			registerDTO.setItemName("125000860001ABC");
			registerDTO.setQuantity(NumConstant.STR_ONE);
			registerDTO.setProdBatchNo("7777666");
			registerDTO.setParentCategoryCode(MpConstant.SN_CODE);
			registerDTO.setSourceSystem(BusinessConstant.SOURCESYS_IMES);
			list.add(registerDTO);
		}
    	barcodeCenterRemoteService.barcodeRegister(list);
		String runNormal = "Y";
		Assert.assertEquals(Constant.STR_Y, runNormal);

	}

	/* Started by AICoder, pid:h9d7ezf86ce802e14c50088cb0178b88ead95279 */
	@Test
	public void TestservernameplateLabelTemplatePrint() throws Exception {
		BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO = new BarcodeCenterTemplatePrintDTO();
		Map<String, String> headerParamsMap = new HashMap<>();
		SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
		sysLookupTypesDTOUrl.setLookupMeaning("http://");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(null);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.GET_LOOKUP_VALUE_ERROR);
		}
		sysLookupTypesDTOUrl.setLookupMeaning("");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(), anyString())).thenReturn(sysLookupTypesDTOUrl);
		PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerParamsMap);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.FAILED_TO_GET_BARCODE_CENTER_URL);
		}
		sysLookupTypesDTOUrl.setLookupMeaning("http://");
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(null);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
		}
		List<SysLookupTypesDTO> lookupValues = new ArrayList<>();
		SysLookupTypesDTO sysLookupTypesDTO = null;
		lookupValues.add(sysLookupTypesDTO);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
		}
		lookupValues.remove(sysLookupTypesDTO);
		SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
		sysLookupTypesDTO1.setLookupCode(BigDecimal.valueOf(123));
		sysLookupTypesDTO1.setLookupMeaning("");
		lookupValues.add(sysLookupTypesDTO1);
		PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyString())).thenReturn(lookupValues);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
		}
		lookupValues.remove(sysLookupTypesDTO1);
		SysLookupTypesDTO sysLookupTypesDTO2 = new SysLookupTypesDTO();
		sysLookupTypesDTO2.setLookupMeaning("test");
		lookupValues.add(sysLookupTypesDTO2);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.SYS_LOOK_NOT_CONFIG);
		}
		lookupValues.remove(sysLookupTypesDTO2);
		SysLookupTypesDTO sysLookupTypesDTO3 = new SysLookupTypesDTO();
		sysLookupTypesDTO3.setLookupCode(BigDecimal.valueOf(123));
		sysLookupTypesDTO3.setLookupMeaning("test");
		lookupValues.add(sysLookupTypesDTO3);
		ServiceData serviceData = new ServiceData<>();
		PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(JSONObject.toJSONString(serviceData));
		barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
		serviceData.getCode().setMsg("error");
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(JSONObject.toJSONString(serviceData));
		PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerParamsMap);
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.CALL_BARCODE_CENTER_TO_PRINT_FALIED);
		}
		ObjectMapper mockObjectMapper = Mockito.mock(ObjectMapper.class);
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mockObjectMapper);
		PowerMockito.when(mockObjectMapper.readTree(Mockito.anyString())).thenReturn(null);
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(JSONObject.toJSONString(serviceData));
		headerParamsMap.put("1", "123");
		try {
			barcodeCenterRemoteService.servernameplateLabelTemplatePrint(barcodeCenterTemplatePrintDTO);
		} catch (Exception e) {
			Assert.assertEquals(e.getMessage(), MessageId.CALL_BARCODE_CENTER_TO_PRINT_FALIED);
		}
	}
	/* Ended by AICoder, pid:h9d7ezf86ce802e14c50088cb0178b88ead95279 */
}
