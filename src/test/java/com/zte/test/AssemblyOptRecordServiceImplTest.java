package com.zte.test;

import com.zte.application.impl.AssemblyOptRecordServiceImpl;
import com.zte.application.impl.WipEntityScanInfoServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.AssemblyOptRecordRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.AssemblyOptRecordEntityDTO;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@PrepareForTest(BasicsettingRemoteService.class)
public class AssemblyOptRecordServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private AssemblyOptRecordServiceImpl service;
    @Mock
    private AssemblyOptRecordRepository assemblyOptRecordrepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WipEntityScanInfoServiceImpl wipEntityScanInfoService;

    @Test
    public void getListBySn() throws Exception {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        service.getListBySn("2");
        List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList = new ArrayList<>();
        PowerMockito.when(assemblyOptRecordrepository.getListBySn(anyString())).thenReturn(assemblyOptRecordEntityDTOList);
        assemblyOptRecordEntityDTOList.add(new AssemblyOptRecordEntityDTO(){{setOptType("1");setFormType("2");setWorkStation("1");}});
        assemblyOptRecordEntityDTOList.add(new AssemblyOptRecordEntityDTO(){{setOptType("2");setFormType("1");setProcessCode("2");setWorkStation("workStation");}});
        assemblyOptRecordEntityDTOList.add(new AssemblyOptRecordEntityDTO(){{setOptType("3");setCreateBy("10270056");setProcessCode("1");}});

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("name");
        hrmPersonInfoDTOMap.put("10270056", hrmPersonInfoDTO);
        hrmPersonInfoDTOMap.put("10270047", hrmPersonInfoDTO);
        PowerMockito.when(centerfactoryRemoteService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoDTOMap);

        List<BSProcessDTO> bsProcessDTOS = new LinkedList<>();
        BSProcessDTO c1 = new BSProcessDTO();
        c1.setProcessCode("1");
        bsProcessDTOS.add(c1);
        BSProcessDTO c2 = new BSProcessDTO();
        c2.setProcessCode("workStation");
        bsProcessDTOS.add(c1);
        PowerMockito.when(wipEntityScanInfoService.getProcessInfo(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(bsProcessDTOS);

        List<SysLookupTypesDTO> sysLookupTypesDTOS = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        sysLookupTypesDTOS.add(sysLookupTypesDTO1);
        sysLookupTypesDTO1.setLookupMeaning("2");
        sysLookupTypesDTO1.setAttribute1("1,2，3;4；5,");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Constant.LOOKUP_TYPE_BIND_TYPE)).thenReturn(sysLookupTypesDTOS);

        Assert.assertNotNull(service.getListBySn("2"));
    }

    @Test
    public void batchInsert() throws Exception {
        List<AssemblyOptRecordEntityDTO> assemblyOptRecordEntityDTOList = new ArrayList<>();
        PowerMockito.when(assemblyOptRecordrepository.getListBySn(anyString())).thenReturn(assemblyOptRecordEntityDTOList);
        assemblyOptRecordEntityDTOList.add(new AssemblyOptRecordEntityDTO());
        Assert.assertEquals(0,service.batchInsert(assemblyOptRecordEntityDTOList));
    }


}
