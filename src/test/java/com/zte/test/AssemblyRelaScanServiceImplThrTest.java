package com.zte.test;

import com.alibaba.fastjson.JSON;
import com.zte.application.PsWipInfoService;
import com.zte.application.impl.AssemblyRelaScanServiceImpl;
import com.zte.application.impl.StandardModeCommonScanServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanscheduleRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.PsWorkOrderDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, ExcelCommonUtils.class, CrafttechRemoteService.class, PlanscheduleRemoteService.class,
        BasicsettingRemoteService.class, RequestHeaderUtil.class, HttpRemoteUtil.class})
public class AssemblyRelaScanServiceImplThrTest extends PowerBaseTestCase {
    @InjectMocks
    private AssemblyRelaScanServiceImpl service;
    @Mock
    private PsWipInfoService psWipInfoService;
    @Mock
    private WipExtendIdentificationRepository wipExtendIdentificationRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private PsWipInfoRepository psWipInfoRepository;
    @Mock
    private StandardModeCommonScanServiceImpl standardModeCommonScanService;

    @Test
    public void assemblyPassStation() throws Exception {
        PowerMockito.mockStatic(PlanscheduleRemoteService.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);

        AssemblyRelaScanDTO dto = new AssemblyRelaScanDTO();
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getExMsgId());
        }

//        dto.setMainSn("1111");
//        try {
//            service.assemblyPassStation(dto);
//        } catch (MesBusinessException e) {
//            Assert.assertEquals(MessageId.SN_MUST_BE_12_NUMBER, e.getExMsgId());
//        }

        dto.setMainSn("725499800018");
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_IS_NOT_NULL, e.getExMsgId());
        }

        dto.setProcessName("波峰焊");
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WORK_STATION_IS_NOT_NULL, e.getExMsgId());
        }

        dto.setWorkStationName("焊接");
        List<PsWorkOrderDTO> workOrderList = new ArrayList<>();
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(Mockito.anyString(), Mockito.anyString())).thenReturn(workOrderList);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FORM_SN_NOT_IN_WIP_INFO, e.getExMsgId());
        }

        PsWipInfo wipInfoBySn = new PsWipInfo();
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn("725499800018")).thenReturn(wipInfoBySn);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PLAN_NO_IS_NULL, e.getExMsgId());
        }

        wipInfoBySn.setAttribute1("7254998");
        PowerMockito.when(psWipInfoRepository.getWipInfoBySn("725499800018")).thenReturn(wipInfoBySn);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.GET_CURRENT_WORKORDER_ERROR, e.getExMsgId());
        }

        PsWorkOrderDTO psWorkOrderDTO = new PsWorkOrderDTO();
        workOrderList.add(psWorkOrderDTO);
        psWorkOrderDTO.setProdplanId("7254998");
        psWorkOrderDTO.setLineCode("SMT1-1");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(Mockito.anyString(), any())).thenReturn(workOrderList);
        PowerMockito.when(CrafttechRemoteService.getProcess(any())).thenReturn(null);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_IS_NOT_EXISTED, e.getExMsgId());
        }

        List<BSProcess> processes = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        processes.add(bsProcess);
        PowerMockito.when(CrafttechRemoteService.getProcess(any())).thenReturn(processes);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_IS_NOT_EXISTED, e.getExMsgId());
        }

        bsProcess.setProcessCode("P1");
        PowerMockito.when(CrafttechRemoteService.getProcess(any())).thenReturn(processes);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.GET_CARFT_NULL, e.getExMsgId());
        }

        psWorkOrderDTO.setProcessGroup("P1$P2");
        PowerMockito.when(PlanscheduleRemoteService.getWorkOrderBasicByProdPlanId(any(), any())).thenReturn(workOrderList);
        List<CtRouteDetailDTO> routeDetailsByLine = new ArrayList<>();
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(null, "SMT1-1")).thenReturn(routeDetailsByLine);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.NOT_FIND_ROUTE, e.getExMsgId());
        }

        CtRouteDetailDTO ctRouteDetailDTO = new CtRouteDetailDTO();
        routeDetailsByLine.add(ctRouteDetailDTO);
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(null, "SMT1-1")).thenReturn(routeDetailsByLine);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CRAFT_NOT_CONTAIN_CURRENT, e.getExMsgId());
        }

        ctRouteDetailDTO.setProcessCode("P1");
        ctRouteDetailDTO.setNextProcess("S1");
        ctRouteDetailDTO.setNextProcessName("短插");
        ctRouteDetailDTO.setProcessSeq(BigDecimal.valueOf(3));
        CtRouteDetailDTO ctRouteDetailDTO1 = new CtRouteDetailDTO();
        ctRouteDetailDTO1.setProcessCode("P1");
        ctRouteDetailDTO.setNextProcess("S2");
        ctRouteDetailDTO1.setNextProcessName("焊接");
        ctRouteDetailDTO1.setProcessSeq(BigDecimal.valueOf(2));
        routeDetailsByLine.add(ctRouteDetailDTO1);
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(null, "SMT1-1")).thenReturn(routeDetailsByLine);
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PROCESS_LAST_WORKSTATION_ERROR, e.getExMsgId());
        }

        ctRouteDetailDTO.setProcessSeq(BigDecimal.valueOf(1));
        PowerMockito.when(CrafttechRemoteService.getCtRouteByRouteId(null, "SMT1-1")).thenReturn(routeDetailsByLine);
        PowerMockito.when(standardModeCommonScanService.smCommonScan(Mockito.any())).thenReturn("111");
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getExMsgId());
        }

        PowerMockito.when(standardModeCommonScanService.smCommonScan(Mockito.any())).thenReturn("");
        service.assemblyPassStation(dto);

        List<ProdBindingSettingDTO> bindingSettings = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        bindingSettings.add(prodBindingSettingDTO);
        prodBindingSettingDTO.setItemCode("itemCode1");
        prodBindingSettingDTO.setUsageCount(BigDecimal.valueOf(2));

        ProdBindingSettingDTO prodBindingSettingDTO1 = new ProdBindingSettingDTO();
        bindingSettings.add(prodBindingSettingDTO1);
        prodBindingSettingDTO1.setItemCode("itemCode2");
        prodBindingSettingDTO1.setUsageCount(BigDecimal.valueOf(-2));
        PowerMockito.when(prodBindingSettingRepository.getProdBindingSettingDTOList(Mockito.any())).thenReturn(bindingSettings);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("111");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(MpConstant.LOOKUP_TYPE_ERP_WS, MpConstant.LOOKUP_CODE_ERP_REPLACE_ITEM)).thenReturn(sysLookupTypesDTO);

        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO.setInventoryItemCode("2222");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(new ArrayList<MtlRelatedItemsEntityDTO>() {{
                        add(mtlRelatedItemsEntityDTO);
                    }});
                    setOther(new HashMap<String, Object>() {{
                        put("totalCount", 2);
                    }});
                }})
        );
        try {
            service.assemblyPassStation(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ITEM_NOT_BOUND, e.getExMsgId());
        }

        PowerMockito.when(prodBindingSettingRepository.getProdBindingSettingDTOList(Mockito.any())).thenReturn(bindingSettings);

    }


}