RetCode.Success=\u64CD\u4F5C\u6210\u529F
RetCode.ServerError=\u670D\u52A1\u5668\u9519\u8BEF
RetCode.AuthFailed=\u8BA4\u8BC1\u5931\u8D25
RetCode.PermissionDenied=\u6CA1\u6709\u6743\u9650
RetCode.ValidationError=\u9A8C\u8BC1\u5931\u8D25
RetCode.BusinessError=\u4E1A\u52A1\u5F02\u5E38
factoryId.or.empNo.is.null=\u5DE5\u5382ID\u6216\u8005\u5DE5\u53F7\u4E3A\u7A7A
sn_must_be_12_number=\u6761\u7801\u5FC5\u987B\u4E3A12\u4F4D\u6570\u5B57
sn_should_be_12_num_or_p_with_12_num=\u6761\u7801\u5FC5\u987B\u4E3A12\u4F4D\u6570\u5B57\u6216P+12\u4F4D\u6570\u5B57
not.rework.cannot.replace=\u975E\u8FD4\u5DE5\u4EFB\u52A1\u6761\u7801\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
params.error=\u53C2\u6570\u5F02\u5E38! {0}
factory.id.is.error=\u5DE5\u5382ID\u5F02\u5E38
reelid.not.register=reelId\u672A\u6CE8\u518C
reelId.not.in.table.material.return=reelId\uFF1A{0}\u5728\u9000\u6599\u8868\u91CC\u65E0\u9000\u6599\u4FE1\u606F
status.is.not.null=reelId:{0}\u9000\u6599\u72B6\u6001\u4E3A{1}\uFF0C\u8BF7\u786E\u8BA4
qty.cannot.be.zero.or.negative=\u6570\u91CF\u4E0D\u80FD\u4E3A0\u6216\u8005\u8D1F\u6570
adding.virtual.station.item.code.is.in.progress=\u5F53\u524D\u6B63\u5728\u65B0\u589E\u865A\u62DF\u7AD9\u4F4D\u7269\u6599\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
pcb.qty.is.null=\u672A\u83B7\u53D6\u5230{0}\u6307\u4EE4\u7684\u62FC\u677F\u6570\u4FE1\u606F\uFF0C\u4E0D\u80FD\u65B0\u589E\u865A\u62DF\u7AD9\u4F4D
item.code.is.exists.in.table={0}\u7269\u6599\u5DF2\u7ECF\u5728\u4E0A\u6599\u8868\u4E2D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
item.code.is.not.exists.in.table={0}\u7269\u6599\u4E0D\u5728BOM\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4
qty.exceeds.the.upper.limit=\u7528\u91CF\u5927\u4E8Ebom\u7528\u91CF*\u62FC\u677F\u6570\uFF1A{0}\uFF0C\u8BF7\u786E\u8BA4
standard.qty.is.null=\u672A\u83B7\u53D6\u5230\u5B50\u5361\uFF1A{0}\u5BF9\u5E94\u7684\u7269\u6599\u4EE3\u7801\uFF1A{1}\u7684\u6807\u51C6\u7528\u91CF
cfgheaderid.info.is.null=\u672A\u83B7\u53D6\u5230SMT\u6307\u4EE4\u4FE1\u606F
amount.exception=\u83B7\u53D6\u6570\u91CF\u5F02\u5E38
reelId.not.exist=reelId:{0} \u5728\u672C\u5730\u5DE5\u5382\u4E0D\u5B58\u5728\uFF01
prodplanid.not.belongs.to.workorder=\u7269\u6599\u6279\u6B21\u4E0D\u662F\u8BE5\u6307\u4EE4\u7684\uFF0C\u8BF7\u786E\u8BA4
new.pkcode.count.must.greater.than.zero=\u65B0\u6599\u76D8\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0\uFF0C\u8BF7\u786E\u8BA4
reel.has.been.used=\u8BE5\u6599\u76D8\u5DF2\u88AB\u5360\u7528\uFF0C\u6307\u4EE4\u53F7\u4E3A\uFF1A\u3010{0}\u3011
item.code.not.match=\u65B0\u6599\u76D8\u7269\u6599\u4EE3\u7801{0}\u4E0E\u65E7\u6599\u76D8\u7269\u6599\u4EE3\u7801{1}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.is.using.please.confirm=\u8BE5\u6599\u76D8\u5904\u4E8E\u5728\u7528\u4E2D\uFF0C\u4E0D\u80FD\u63A5\u6599\uFF0C\u8BF7\u786E\u8BA4
avl.server.failed=\u8C03\u7528AVL\u670D\u52A1\u5931\u8D25
avl.check.failed=AVL\u6821\u9A8C\u4E0D\u901A\u8FC7\uFF0C\u8BF7\u786E\u8BA4
task_cannot_negative=\u4EFB\u52A1\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0
sn_status_is_poor_and_no_stopover_is_allowed=\u6761\u7801\u72B6\u6001\u4E3A\u4E0D\u826F\uFF0C\u4E0D\u5141\u8BB8\u8FC7\u7AD9

mapcrafttocfgheadid_is_null=mapcrafttocfgheadid is null
map_is_null=map\u4E3A\u7A7A
update.error.smt.scan.id.null=\u8BE5\u6570\u636E\u6CA1\u6709id,\u4E0D\u80FD\u89E3\u9664
unbinding.please.wait=\u89E3\u7ED1\u4E2D\u8BF7\u5F85\u4F1A\u518D\u8BD5
get_workorder_condition_error=\u8C03\u7528\u67E5\u8BE2\u6307\u4EE4\u63A5\u53E3\u5F02\u5E38:{0}

scrap.updateTaskQty.failed = \u6309\u6279\u6B21\u66F4\u65B0pstask\u8868\u5931\u8D25
factory.server.is.move = \u672C\u5730\u90E8\u7F72\u5DE5\u5382\u6B63\u5728\u8FC1\u79FB\uFF0C\u76EE\u524D\u5E94\u7528\u4E0D\u53EF\u7528
feeder.has.binged = \u8BE5Feeder\u5DF2\u7ECF\u88AB{0}\u7ED1\u5B9A\u4E86
matrial.has.used = \u8FD9\u4E2A\u6599\u76D8\u5DF2\u7ECF\u88AB{0}\u4F7F\u7528\u4E86
has.binged.to.matrial = \u5DF2\u7ED1\u5B9A\u5230\u7269\u6599{0}
direction.error.forbidden.receive.items = \u4E0D\u540C\u5382\u5BB6\u7269\u6599\u5728\u7F16\u5E26\u5185\u65B9\u5411\u4E0D\u4E00\u81F4\uFF0C\u7981\u6B62\u63A5\u6599

dqas.error=\u4E2D\u8BD5\u63A5\u53E3\u8C03\u7528\u5F02\u5E38,\u63A5\u53E3\u4FE1\u606F:{0}
interface.error=\u63A5\u53E3\u8C03\u7528\u5F02\u5E38,\u63A5\u53E3\u4FE1\u606F:{0}
customize.msg={0}

entityplanbasic_is_null=EntityPlanBasic\u4E3A\u7A7A
sub_card_erp_compesation_info_null=\u6CA1\u6709\u9700\u8981\u8865\u507F\u7684\u5B50\u5361ERP\u8865\u507F\u4FE1\u606F
sub_card_erp_email_error=\u8BF7\u914D\u7F6E\u5B50\u5361\u5F02\u6B65\u63A8\u9001ERP\u5F02\u5E38\u6536\u4EF6\u4EBA
sub_card_erp_error=\u63D0\u4EA4\u5B50\u5361\u5165\u5E93\u5355\u5230ERP\u5931\u8D25{0}

call.basicsetting.failure=\u8C03\u7528\u57FA\u7840\u670D\u52A1\u5931\u8D25
static.workshop.output.failure=\u7EDF\u8BA1\u8F66\u95F4\u5404\u7EBF\u4F53\u4EA7\u51FA\u65F6\u8BA1\u7B97\u5931\u8D25
not.maintain.manpower=\u4EE5\u4E0B\u7EBF\u4F53\u672A\u7EF4\u62A4\u7EBF\u4F53\u4EBA\u529B\u6570\u636E:{0}
manual.statistic.info=\u624B\u52A8\u7EDF\u8BA1\u63D0\u4EA4\u6210\u529F\uFF0C\u9884\u8BA15\u5206\u949F\u53EF\u7EDF\u8BA1\u7ED3\u675F\uFF0C\u82E5\u7EDF\u8BA1\u5F02\u5E38\u5C06\u90AE\u4EF6\u544A\u77E5
current.locationNo.is.virtual=\u624B\u8865\u6599\u8BF7\u5728\u624B\u8865\u6599\u4E0A\u7EBF\u529F\u80FD\u8FDB\u884C\u63A5\u6599\u64CD\u4F5C
item.not.match_bom.item=\u7269\u6599\u4EE3\u7801\u4E0E\u4E0A\u6599\u8868\u4E0D\u4E00\u81F4\uFF0C\u7AD9\u4F4D\uFF1A{0}\uFF0C\u8BF7\u786E\u8BA4
order.item.empty= \u6307\u4EE4\u6599\u5355\u4EE3\u7801\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
transfer.scan.interactive.error=\u7AD9\u4F4D\uFF1A{0}\uFF0C\u7269\u6599\u4EE3\u7801\uFF1A{1}\uFF0C\u8F6C\u673A\u626B\u63CF\u62A5\u9519
location.not.feeder.bound=\u7AD9\u4F4D\uFF1A{0}\uFF0C\u6CA1\u6709feeder\u7ED1\u5B9A
feeder.not.found.eqp.status=feeder\u7F16\u53F7\uFF1A{0}\uFF0C\u6CA1\u6709\u627E\u5230feeder\u8BBE\u5907\u72B6\u6001\u4FE1\u606F
feeder.full.station.position.empty=feeder\u8BBE\u5907\u7AD9\u4F4D\u4E3A\u7A7A
feeder.location.not.match.bom=feeder\u8BBE\u5907\u7AD9\u4F4D{0}\u4E0E\u4E0A\u6599\u8868\u7AD9\u4F4D{}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4

line.code.is.null=\u7EBF\u4F53\u4EE3\u7801\u672A\u4F20\u5165
work.order.no.is.null=\u6307\u4EE4\u7F16\u53F7\u4E3A\u7A7A

workstation.can.not.be.null=\u5DE5\u4F4D\u4E0D\u80FD\u4E3A\u7A7A
sn.not.exits=\u5355\u677F\u6761\u7801\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
craftsection.of.sn.is.not.dip=\u5355\u677F\u6761\u7801\u4E3B\u5DE5\u5E8F\u4E0D\u662FDIP\uFF0C\u8BF7\u786E\u8BA4\uFF01
workorder.status.is.not.started=\u6307\u4EE4\u4E0D\u662F\u5F00\u5DE5\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u6263\u6570\uFF01
workorder.loading.scan.not.completed=\u6307\u4EE4\u4E0A\u6599\u626B\u63CF\u672A\u5B8C\u6210\uFF0C\u8BF7\u786E\u8BA4!
b.smt.bom.detail.of.workorder.is.null=\u5BF9\u5E94\u4E0A\u6599\u8868\u6570\u636E\u4E3A\u7A7A
dip.board.scan.get.redis.lock.failed=\u4E0A\u4E00\u6B21\u626B\u63CF\u5C1A\u672A\u7ED3\u675F\uFF0C\u8BF7\u7A0D\u5019\u91CD\u8BD5
smt.machine.material.of.workorder.is.null=\u5BF9\u5E94\u5728\u7528\u8868\u6570\u636E\u4E3A\u7A7A
item.of.reelid.is.not.enough=\u5DE5\u4F4D {0} \u6240\u9700\u7269\u6599 {1} \u6570\u91CF\u4E0D\u8DB3\uFF0C\u8BF7\u7EED\u6599
work.order.not.found=\u6307\u4EE4\u4E0D\u5B58\u5728
work.order.not.found.route=\u6307\u4EE4\uFF1A{0}\u672A\u7EF4\u62A4route\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5
task.difference=\u4EFB\u52A1\u4E0D\u540C
item.difference=\u7269\u6599\u4EE3\u7801\u4E0D\u540C
sn.bound.work.order=\u6761\u7801\u5DF2\u7ED1\u5B9A\u6307\u4EE4
sn.not.found=\u6CA1\u6709\u627E\u5230\u8BE5\u6761\u7801
task.not.have.details=\u672A\u627E\u5230\u4EFB\u52A1\u4FE1\u606F
task.not.have.item.no=\u4EFB\u52A1\u6599\u5355\u4EE3\u7801\u4E3A\u7A7A
task.not.have.route=\u672A\u627E\u5230\u4EFB\u52A1\u5B50\u5DE5\u5E8F\u8DEF\u5F84
line.is.not.null=\u8BF7\u8BBE\u7F6E\u7EBF\u4F53
process.is.not.null=\u8BF7\u8BBE\u7F6E\u5B50\u5DE5\u5E8F
work.station.is.not.null=\u8BF7\u8BBE\u7F6E\u5DE5\u7AD9
get.process.details.failure=\u70B9\u5BF9\u70B9\u83B7\u53D6\u5B50\u5DE5\u5E8F\u5217\u8868\u5931\u8D25
process.details.not.found=\u672A\u627E\u5230\u5F53\u524D\u5B50\u5DE5\u5E8F\u5177\u4F53\u4FE1\u606F
wip.current.process.not.completed=wip\u5F53\u524D\u5DE5\u5E8F{0}\u6CA1\u6709\u505A\u5B8C
first.bind.craft.section.work.order=\u8BF7\u5148\u7ED1\u5B9A{0}\u6BB5\u6307\u4EE4
task.item.not.have.route=\u4EFB\u52A1\u6599\u5355\u4EE3\u7801\u672A\u7EF4\u62A4\u5DE5\u827A
sn.not.found.first.process=\u672A\u627E\u5230\u6761\u7801{0}\u7684\u7B2C\u4E00\u9053\u5DE5\u5E8F\uFF0C\u8BF7\u68C0\u67E5
process.is.not.first=\u5B50\u5DE5\u5E8F\u4E0D\u662F\u9996\u5DE5\u5E8F
line.model.not.found=\u7EBF\u4F53\u5EFA\u6A21\u672A\u7EF4\u62A4
sn.not.found.first.work.station=\u672A\u627E\u5230\u6761\u7801{0}\u7684\u7B2C\u4E00\u4E2A\u5DE5\u7AD9\uFF0C\u8BF7\u68C0\u67E5
work.station.is.not.first=\u5DE5\u7AD9\u4E0D\u662F\u5B50\u5DE5\u5E8F\u9996\u5DE5\u7AD9
work.process.and.station.is.first.please.choose.workorder=\u5F53\u524D\u5B50\u5DE5\u5E8F\u3001\u5DE5\u7AD9\u662F\u9996\u5DE5\u5E8F\u3001\u9996\u5DE5\u7AD9\u3002\u8BF7\u4F7F\u7528\u6807\u6A21\u901A\u7528\u626B\u63CF\u5E76\u9009\u62E9\u6307\u4EE4!
workorder.prepare.not.complete=\u6307\u4EE4 {0} \u5907\u6599\u6216\u7EFC\u5408\u5907\u6599\u672A\u5B8C\u6210
task.of.sn.is.null=\u6761\u7801 {0} \u5BF9\u5E94\u4EFB\u52A1\u4E3A\u7A7A
task.of.sn.is.rework=\u6761\u7801 {0} \u6240\u5C5E\u4EFB\u52A1\u662F\u8FD4\u5DE5\u4EFB\u52A1\uFF0C\u4E0D\u5141\u8BB8\u8FC7\u7AD9!
get.task.info.error=\u83B7\u53D6\u4EFB\u52A1\u4FE1\u606F\u5F02\u5E38\u3002
not.found.sn.route=\u672A\u627E\u5230{0}\u6761\u7801\u7684\u5DE5\u827A\u8DEF\u5F84
not.found.sn.next.process=\u672A\u627E\u5230\u6761\u7801{0}\u5BF9\u5E94\u7684\u4E0B\u5DE5\u5E8F\uFF0C\u8BF7\u68C0\u67E5
not.found.sn.scan.route=\u672A\u627E\u5230{0}\u6761\u7801\u7684\u626B\u63CF\u6D41\u7A0B\u4FE1\u606F
not.found.wip.next.process=\u8BF7\u53BBWIP\u5F53\u524D\u5DE5\u7AD9{0}\u4E0B\u5DE5\u7AD9
update.wip.failure=\u66F4\u65B0\u5728\u5236\u8868\u4FE1\u606F\u5931\u8D25
insert.wip.scan.history.failure=\u63D2\u5165\u626B\u63CF\u5386\u53F2\u4FE1\u606F\u5931\u8D25
update.wip.scan.history.failure=\u66F4\u65B0\u626B\u63CF\u5386\u53F2\u4FE1\u606F\u5931\u8D25
insert.wip.test.record.failure=\u63D2\u5165\u6D4B\u8BD5\u8BB0\u5F55\u4FE1\u606F\u5931\u8D25
update.work.order.failure=\u66F4\u65B0\u6307\u4EE4\u4FE1\u606F\u5931\u8D25
in.qty.big.than.work.order.qty=\u6307\u4EE4\u6295\u5165\u6570\u91CF\u5927\u4E8E\u6307\u4EE4\u6570\u91CF{0}
out.qty.big.than.work.order.qty=\u6307\u4EE4\u4EA7\u51FA\u6570\u91CF\u5927\u4E8E\u6307\u4EE4\u6570\u91CF
craft.section.difference.with.wip=\u6307\u4EE4\u4E3B\u5DE5\u5E8F\u8DDFwip\u8868\u4E3B\u5DE5\u5E8F\u4E0D\u4E00\u81F4
wip.current.process.not.in.process.group=wip\u5F53\u524D\u5DE5\u5E8F\u4E0D\u5728\u6307\u4EE4\u5B50\u5DE5\u5E8F\u7EC4\u4E2D
select.error=\u67E5\u8BE2\u5931\u8D25
reelid.prodplanid.is.null=\u5F53\u524Dreelid\u6279\u6B21\u4E3A\u7A7A

worker.is.null=\u5DE5\u53F7\u4E3A\u7A7A
factory.id.is.null=\u5DE5\u5382ID\u4E0D\u80FD\u4E3A\u7A7A
emp.no.is.null=\u5458\u5DE5\u53F7\u4E0D\u80FD\u4E3A\u7A7A
prod.binding.setting.is.null=\u4F20\u5165\u7684\u7ED1\u5B9A\u6E05\u5355\u4E3A\u7A7A
item.type.is.null=\u4EA7\u54C1\u7C7B\u578B\u4E3A\u7A7A
product.code.is.null=\u4EA7\u54C1\u4EE3\u7801\u4E3A\u7A7A
product.name.is.null=\u4EA7\u54C1\u540D\u79F0\u4E3A\u7A7A
process.code.is.null=\u7ED1\u5B9A\u6E05\u5355\u5B58\u5728\u6761\u76EEprocessCode\u4E3A\u7A7A
route.info.is.null=\u4F20\u5165\u7684route\u4FE1\u606F\u4E3A\u7A7A
workorder.not.find=\u627E\u4E0D\u5230\u6307\u4EE4\u4FE1\u606F

start.time.is.later=\u67E5\u8BE2\u5F00\u59CB\u65F6\u95F4\u665A\u4E8E\u7ED3\u675F\u65F6\u95F4
duration.is.more.than.month=\u67E5\u8BE2\u65F6\u95F4\u4E0D\u80FD\u8D85\u8FC7\u4E00\u4E2A\u6708
RetCode.TimeIntervalError=\u8F93\u5165\u7684ReelID\u5173\u8054\u7684\u67E5\u8BE2\u65F6\u95F4\u95F4\u9694\u5927\u4E8E3\u4E2A\u6708
RetCode.ReelId.out.length=\u8F93\u5165\u7684ReelID\u957F\u5EA6\u8D85\u8FC7100
start.or.end.time.null=\u5FC5\u987B\u8F93\u5165\u67E5\u8BE2\u5F00\u59CB\u548C\u7ED3\u675F\u65F6\u95F4
process.need.bind.items.not.enough={0}\u5DE5\u5E8F\u9700\u8981\u7ED1\u5B9A{1}\u4E2A\u7269\u6599\uFF0C\u8FD8\u5DEE{2}\u4E2A\u6CA1\u6709\u7ED1\u5B9A
process.need.bind.items.not.finish=\u7269\u6599\u672A\u7ED1\u5B9A\u5B8C\u6210
workorder.info.is.null=\u6307\u4EE4\u4FE1\u606F\u4E0D\u5B58\u5728


productcode.is.null=\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A
workorderno.is.not.null=\u6307\u4EE4\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
currprocesscode.is.not.null=\u5B50\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u7A7A
the.corresponding.information.is.inconsistent=\u6761\u7801\u5217\u8868\u7684\u5BF9\u5E94\u6307\u4EE4\u3001\u5B50\u5DE5\u5E8F\u3001\u5DE5\u7AD9\u4E0D\u4E00\u81F4
mainsn.is.not.null=\u4E3B\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
subsn.is.not.null=\u5B50\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
miansn.binded.complete=\u4E3B\u6761\u7801\u7ED1\u5B9A\u5B8C\u6210
binded.and.passworkstation.complete=\u4E3B\u6761\u7801\u5DF2\u7ED1\u5B9A\u5B8C\u6210\uFF0C\u8FC7\u7AD9\u6210\u529F
main.sn.scan.success=\u4E3B\u6761\u7801\u626B\u63CF\u6210\u529F\uFF0C\u8BF7\u626B\u63CF\u5B50\u6761\u7801
sub.sn.scan.success=\u5B50\u6761\u7801\u626B\u63CF\u6210\u529F
bind.relation.not.found=\u8BF7\u5148\u8BBE\u7F6E\u7269\u6599\u7ED1\u5B9A\u5173\u7CFB
workstation.error=\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5DE5\u7AD9
item.info.not.found={0} \u672A\u627E\u5230\u5BF9\u5E94\u7684\u7269\u6599\u4FE1\u606F
item.info.find.error=\u7269\u6599\u4FE1\u606F\u67E5\u627E\u9519\u8BEF
bind.count.more.than.usage= {0} \u7ED1\u5B9A\u6570\u91CF\u5927\u4E8E\u9700\u6C42\u6570\u91CF
rela.count.err= {0} \u672A\u627E\u5230\u7ED1\u5B9A\u5173\u7CFB\uFF0C {1} \u7ED1\u5B9A\u6570\u91CF\u5927\u4E8E\u9700\u6C42\u6570\u91CF
rela.err = {0} \u672A\u627E\u5230\u7ED1\u5B9A\u5173\u7CFB
sn.has.binded = {0} \u5DF2\u7ED1\u5B9A\u5230\u7269\u6599  {1}
can.not.found.mainsn.item.info = \u672A\u627E\u5230\u4E3B\u6761\u7801\u7684\u7269\u6599\u4FE1\u606F
sn.not.match.task.no = \u6761\u7801\u548C\u4EFB\u52A1\u53F7\u4E0D\u5339\u914D
line.name.is.null = \u7EBF\u4F53\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
line.name.not.found = \u672A\u627E\u5230\u5BF9\u5E94\u7684\u7EBF\u4F53\u4EE3\u7801
line.info.is.null = \u67E5\u8BE2\u7EBF\u4F53\u4FE1\u606F\u4E3A\u7A7A
exter.type.not.found = \u4EA7\u54C1\u5927\u7C7B\u7684\u7BA1\u63A7\u7C7B\u578B\u672A\u627E\u5230
exter.type.err=\u7BA1\u63A7\u65B9\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u914D\u7F6E
page.is.null=\u9875\u7801\u4FE1\u606F\u4E3A\u7A7A

params.is.null=\u4F20\u5165\u53C2\u6570\u4E3A\u7A7A
factory.id.null=\u5DE5\u5382Id\u4E3A\u7A7A
head.line.not.equal=\u5F53\u524D\u5165\u5E93\u5355\u5934\u8868\u63D0\u4EA4\u6570\u91CF\u4E0E\u660E\u7EC6\u603B\u6570\u4E0D\u76F8\u7B49
update.commitqty.err=\u8C03\u6574\u63D0\u4EA4\u6570\u91CF\u5F02\u5E38
update.head.err=\u66F4\u65B0\u5165\u5E93\u5355\u5934\u8868\u5F02\u5E38
ope.type.is.null=\u64CD\u4F5C\u7C7B\u578B\u4E3A\u7A7A
ope.condition.is.null =\u8F93\u5165\u53C2\u6570\u4E3A\u7A7A
mounting.data.is.null=\u673A\u53F0\u5728\u7528\u7269\u6599\u4FE1\u606F\u4E3A\u7A7A
prepare.date.is.null=\u673A\u53F0\u5728\u7528\u548C\u5907\u6599\u4FE1\u606F\u4E3A\u7A7A

pk.id.is.null=\u4E3B\u952EID\u4E3A\u7A7A
param.is.missing=\u5FC5\u586B\u53C2\u6570\u7F3A\u5931
error.skip.has.maintenance=\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F\u5DF2\u7EF4\u62A4

main.sn.null=\u4E3B\u6761\u7801\u4E3A\u7A7A
no.main.sn=\u4E3B\u6761\u7801{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.bind.info_sub=\u5B50\u6761\u7801{0}\u65E0\u7ED1\u5B9A\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.bind.info_main=\u4E3B\u6761\u7801{0}\u65E0\u7ED1\u5B9A\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.bind.info.sn=\u6761\u7801{0}\u65E0\u7ED1\u5B9A\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01

condition.not.find=\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
form.type.not.find=\u7ED1\u5B9A\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
must.has.other.condition=\u9664\u7ED1\u5B9A\u7C7B\u578B\u5916\u5FC5\u987B\u8FD8\u6709\u5176\u4ED6\u6761\u4EF6
export.time.than.two.month=\u5BFC\u51FA\u65F6\u95F4\u4E0D\u80FD\u5927\u4E8E\u4E24\u4E2A\u6708
export.time.than.three.month=\u5BFC\u51FA\u65F6\u95F4\u4E0D\u80FD\u5927\u4E8E\u4E09\u4E2A\u6708


form.sn.no.binding.relation=\u4E3B\u6761\u7801{0}\u6CA1\u6709\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u8BF7\u786E\u8BA4
sn.no.binding.relation=\u5B50\u6761\u7801{0}\u6CA1\u6709\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u8BF7\u786E\u8BA4
form.sn.and.sn.no.binding.relation=\u4E3B\u6761\u7801{0}\u4E0E\u5B50\u6761\u7801{1}\u4E0D\u5B58\u5728\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u8BF7\u786E\u8BA4
form.sn.not.in.wip.extend=\u6761\u7801\u5728\u6761\u7801\u9644\u5C5E\u8868\u4E2D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
form.sn.not.in.wip.info=\u6761\u7801\u5728\u5728\u5236\u4FE1\u606F\u8868\u4E2D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
has.distribution=\u5DF2\u5206\u6599

feeder.bind.valid.failure=\u9A8C\u8BC1\u5931\u8D25 feedar\u7ED1\u5B9A\u5B8C\u6210\u624D\u80FD\u5907\u6599
item.code.empty= \u7269\u6599\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
work.order.empty= \u6307\u4EE4\u4E0D\u80FD\u4E3A\u7A7A
line.code.empty= \u7EBF\u4F53\u4E0D\u80FD\u4E3A\u7A7A
module.no.empty= \u6A21\u7EC4\u4E0D\u80FD\u4E3A\u7A7A
location.no.empty= \u5DE5\u7AD9\u4E0D\u80FD\u4E3A\u7A7A


qty.more.than.wait.register=\u6CE8\u518C\u6570\u91CF\u5927\u4E8E\u5F85\u6CE8\u518C\u6570\u91CF
get.generate.sn.lock.fail=\u8BE5\u4EFB\u52A1\u6B63\u5728\u751F\u6210\u6761\u7801\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5
source.item.id.is.null=\u7269\u6599\u5C5E\u6027sourceItemId\u4E3A\u7A7A
factory.info.is.null=\u5DE5\u5382\u4FE1\u606F\u7F3A\u5931
source.item.id.not.sync=itemId\u5C1A\u672A\u540C\u6B65,\u8BF7\u7A0D\u540E\u91CD\u8BD5\u3002\u540C\u6B65\u5468\u671F10\u5206\u949F/\u6B21
no.return.service.data=\u6570\u636E\u56DE\u5199\u8C03\u7528\u5931\u8D25\uFF0C\u65E0\u8FD4\u56DE\u4FE1\u606F
no.return.bo.data=\u6570\u636E\u56DE\u5199\u8C03\u7528\u5931\u8D25 {0}

sn.parent=\u5DF2\u7ED1\u5B9A\u5927\u677F\u6761\u7801
qty.error=\u6570\u91CF\u9519\u8BEF
source.task.erro=\u6279\u6B21\u683C\u5F0F\u4E0D\u6B63\u786E!
parent.error=\u5927\u677F\u6761\u7801\u6279\u6B21\u4E0D\u6B63\u786E!
sn.error=\u5C0F\u677F\u6761\u4E0D\u6B63\u786E!
no.sn.binded=\u6761\u7801\u91CD\u590D\u7ED1\u5B9A\uFF01

receive.must.after.qc.transform=QC\u8F6C\u673A\u626B\u63CF\u5B8C\u6BD5\u624D\u80FD\u63A5\u6599
receive.must.after.qc.recheck= QC\u63A5\u6599\u590D\u68C0\u4E4B\u540E\u624D\u80FD\u7EE7\u7EED\u5907\u6599
no.maintain.skip=\u53CC\u8F68\u7EBF\u4E2D\u7684\u53E6\u4E00\u6761\u7EBF\u4F53\u7269\u6599\u672A\u7EF4\u62A4\u7BA1\u63A7\u8DF3\u8FC7
has.not.rollover=\u7EF4\u4FEE\u672A\u8FD4\u8FD8
last.process.not.configured=\u6700\u540E\u5DE5\u5E8F\u5B57\u5178\u503C\uFF081076\uFF09\u672A\u914D\u7F6E 
not.found.workorder.route= \u672A\u627E\u5230\u6307\u4EE4 {0} \u7684\u5DE5\u827A\u8DEF\u5F84
can.not.found.workstation=\u672A\u627E\u5230\u5DE5\u7AD9\u4FE1\u606F

emp.no.empty=\u5DE5\u53F7\u4E3A\u7A7A
transfer.inter.empty=\u4E0D\u5B58\u5728\u672A\u8F6C\u79FB\u7684ERP\u4EA4\u6613\u6570\u636E

str.valid.error = \u53C2\u6570\u6821\u9A8C\u672A\u901A\u8FC7\uFF0C\u6821\u9A8C\u89C4\u5219\uFF1A\u4EFB\u52A1\u53F7\u4E0E\u5DE5\u5E8F\u6307\u4EE4\u53F7\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A

params.validate.error=\u4F20\u5165\u53C2\u6570\u6821\u9A8C\u4E0D\u901A\u8FC7:{0}
location.info.lacking=\u7AD9\u4F4D\u4FE1\u606F\u7F3A\u5931: {0}
line.info.lacking=\u7EBF\u4F53\u4FE1\u606F\u672A\u7EF4\u62A4: {0}
line.not.support.device.or.not.asm={0}: \u7EBF\u4F53\u672A\u542F\u7528\u8BBE\u5907\u5BF9\u63A5\u6216\u975EASM\u7EBF\u4F53
no.online.work.order={0}\uFF1A\u7EBF\u4F53\u65E0\u5F00\u5DE5\u6307\u4EE4
program.not.match.online.work.order=\u7A0B\u5E8F\u540D\u4E0E\u5F00\u5DE5\u6307\u4EE4\u4E0D\u5339\u914D
no.item.info={0} \u672A\u7EF4\u62A4\u6599\u5355\u4EE3\u7801
no.work.order.online={0} \u65E0\u5DF2\u63D0\u4EA4\u3001\u6302\u8D77\u3001\u5F00\u5DE5\u6307\u4EE4\u4FE1\u606F
program.name.analysis.error={0} \u7A0B\u5E8F\u540D\u89E3\u6790\u5F02\u5E38
work.order.no.cfg.id=\u672A\u5BFC\u5165\u4E0A\u6599\u8868
not.match.with.bom.detail=\u4E0E\u4E0A\u6599\u8868\u4E0D\u5339\u914D
location.has.transfer.scan=\u8BE5\u7AD9\u4F4D\u5DF2\u505A\u8F6C\u673A\u626B\u63CF reelId:{0}
previous.process.not.submitted.to.this=\u4E0A\u9053\u5DE5\u5E8F\u672A\u63D0\u4EA4\u5230\u8BE5\u5DE5\u5E8F
transfer.box.is.null = \u8F6C\u4EA4\u7BB1\u53F7\u4E3A\u7A7A
get.box.info.error = \u83B7\u53D6\u7BB1\u53F7\u5185\u5BB9\u5931\u8D25
transfer.error = \u8F6C\u4EA4\u6570\u636E\u5F02\u5E38
get.box.workOrder.error = \u83B7\u53D6\u7BB1\u53F7\u5185\u5BB9\u6307\u4EE4\u4FE1\u606F\u5931\u8D25
get.line.of.box.workorder.error = \u83B7\u53D6\u7BB1\u53F7\u5185\u5BB9\u6307\u4EE4\u5BF9\u5E94\u7EBF\u4F53\u4FE1\u606F\u5931\u8D25
sn.of.box.cannot.deliver = {0}\u4E2D\u4EE5\u4E0B\u6761\u7801\u4E0D\u80FD\u8F6C\u4EA4\uFF1A{1}

sn.technical.change=\u6761\u7801{0}\u5B58\u5728\u6280\u6539\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
sn.bar.attr.inconsistent.task.no=\u5B50\u6761\u7801\u73AF\u4FDD\u5C5E\u6027\u4E0E\u4EFB\u52A1{0}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
child.item.no.inconsistent.erp.list=\u5B50\u7269\u6599\u4EE3\u7801{0}\u6CA1\u6709\u5728\u7ED1\u5B9A\u5173\u7CFB\u6E05\u5355\u4E2D
query.result.is.null=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
dqas.result.is.not.pass=DQAS\u6D4B\u8BD5\u7ED3\u679C\u4E0D\u901A\u8FC7
wip.info.of.parent.sn.not.exist=\u7236\u6761\u7801 {0} \u5BF9\u5E94\u5728\u5236\u8868\u6570\u636E\u4E0D\u5B58\u5728
parent.task.of.prodplanno.not.exist=\u5B50\u5361\u6279\u6B21 {0} \u5BF9\u5E94\u7236\u4EFB\u52A1\u4E0D\u5B58\u5728
sn.of.parent.sn.is.empty=\u6574\u673A\u6761\u7801\u5BF9\u5E94\u5355\u677F\u6761\u7801\u4E3A\u7A7A
testtype.is.null=\u6D4B\u8BD5\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A

sn.is.null=\u8F93\u5165\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
snList.exceed.60=\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC760\u4E2A
sn.length.is.12=\u5355\u677F\u6761\u7801:{0}\u957F\u5EA6\u5E94\u4E3A12\u4F4D!
sn.length.is.30=\u6574\u673A\u6761\u7801:{0}\u957F\u5EA6\u5E94\u4E3A30\u4F4D!
wipinfo.of.sn.is.null=SN:{0}\u5BF9\u5E94\u5728\u5236\u4FE1\u606F\u8868\u6570\u636E\u4E3A\u7A7A!
currprocesscode.or.workstation.of.wipinfo.is.null=SN:{0}\u5BF9\u5E94\u5728\u5236\u4FE1\u606F\u7684\u5B50\u5DE5\u5E8F\u6216\u5DE5\u7AD9\u6570\u636E\u4E3A\u7A7A!
lookup.1087.is.empty=\u6570\u636E\u5B57\u5178\u914D\u7F6E\u7684\u5B50\u5DE5\u5E8F\u548C\u5DE5\u7AD9\u6570\u636E\u4E3A\u7A7A
processcode.and.workstation.of.sn.is.not.in.lookup=\u6761\u7801{0}\u72B6\u6001\u4E0E\u7CFB\u7EDF\u914D\u7F6E\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
prodplanId.is.null=\u6279\u6B21\u4E3A\u7A7A
success=\u6210\u529F
poilt.test.error=\u4E2D\u8BD5\u63A5\u53E3\u9519\u8BEF
insert.sn.err = \u63D2\u5165\u5355\u677F\u6761\u7801\u5931\u8D25
sn.exis.wipinfo.err = \u7CFB\u7EDF\u4E2D\u5DF2\u7ECF\u5B58\u5728\u8BE5\u6761\u7801 
sn.exis.whitelistinfo.err = \u8BE5\u6761\u7801\u5728\u767D\u540D\u5355\u4E2D\u5DF2\u5B58\u5728
sys.look.up.code.is.null = \u8BBE\u5907\u7C7B\u578B{0}\u6570\u636E\u5B57\u5178{1}\u5B50\u9879\u4EE3\u7801\u672A\u914D\u7F6E\u8BF7\u68C0\u67E5
eqp.subtype.info.is.null=\u83B7\u53D6\u8BBE\u5907\u7C7B\u578B{0}\u4E0B\u56DE\u8BBE\u5907\u5C0F\u7C7B\u4E3A\u7A7A
current.factory.not.line.info = \u5F53\u524D\u5DE5\u5382\u4E0B\u7684\u8F66\u95F4\u6682\u65F6\u6CA1\u6709\u751F\u4EA7\u7EBF\u4FE1\u606F
no.get.factory = \u672A\u83B7\u53D6\u5230\u5DE5\u5382ID\uFF0C\u8BF7\u91CD\u8BD5
no.get.workshop.code = \u672A\u83B7\u53D6\u5230\u8F66\u95F4\u7F16\u7801\uFF0C\u8BF7\u91CD\u8BD5
time.not.is.null = \u65F6\u95F4\u6BB5\u95F4\u9694\uFF0C\u5927\u4E8E0\u4E0D\u4E3A\u7A7A
ageing.length = \u8001\u5316\u65F6\u957F\uFF0C\u5927\u4E8E0\u4E0D\u4E3A\u7A7A
predict.out = \u6BCF\u5C0F\u65F6\u7406\u8BBA\u4EA7\u51FA\uFF0C \u5927\u4E8E0\u4E0D\u4E3A\u7A7A
start.time.not.null = \u5F00\u59CB\u65F6\u95F4\u5B57\u7B26\u4E32\u4E0D\u80FD\u4E3A\u7A7A,\u5B57\u7B26\u4E32\u683C\u5F0F\u5FC5\u987B\u4E3A(yyyy-MM-dd HH:mm:ss), \u4F8B: 1999-01-01 00:00:00
end.time.not.null = \u7ED3\u675F\u65F6\u95F4\u5B57\u7B26\u4E32\u4E0D\u80FD\u4E3A\u7A7A,\u5B57\u7B26\u4E32\u683C\u5F0F\u5FC5\u987B\u4E3A(yyyy-MM-dd HH:mm:ss), \u4F8B: 1999-01-01 00:00:00
search.for.interfaces.by.parent.sn=\u672A\u67E5\u8BE2\u5230\u76F8\u5173\u4FE1\u606F
sn.no.exis = \u8BE5\u5355\u677F\u6761\u7801\u6210\u529F\u626B\u63CF
sn.has.scanned = \u6761\u7801 {0} \u5DF2\u626B\u63CF
sys.look.not.config = \u6570\u636E\u5B57\u5178 {0} \u672A\u914D\u7F6E
no.configured.template.found = MDS\u6700\u65B0\u94ED\u724C\u5C5E\u6027\u662F{0}\uFF0C\u6CA1\u6709\u627E\u5230\u914D\u7F6E\u7684\u6A21\u677F\u3002
barcode.templates.batch.reprinting = \u6279\u91CF\u8865\u6253\u7684\u6761\u7801\u6A21\u677F\u4E0D\u4E00\u81F4\u3002
sys.look.param.not.config = 1081\u6570\u636E\u5B57\u5178\u503C\u672A\u914D\u7F6E
sys.look.param.setting.error.or.over.range=\u6570\u636E\u5B57\u5178{0}\u53C2\u6570\u914D\u7F6E\u683C\u5F0F\u6216\u8005\u8303\u56F4\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\u5907\u7528\u5B57\u6BB51\u30012\u30013\u90FD\u4E3A\u6574\u6570\u4E14\u5B57\u6BB52\u5927\u4E8E\u5B57\u6BB51\uFF0C\u95F4\u9694\u4E0D\u8D85\u8FC7\u5B57\u6BB53
sys.look.zj.param.not.config = 1081001 \u6574\u673A\u751F\u6210\u6807\u7B7E\u89C4\u5219\u672A\u914D\u7F6E
sys.look.wl.param.not.config = 1081004 \u7269\u6599\u6807\u8BC6\u5361\u751F\u6210\u6807\u7B7E\u89C4\u5219\u672A\u914D\u7F6E
wl.param.not.found= {0} \u89C4\u5219\u672A\u627E\u5230
zj.param.not.found = {0} \u89C4\u5219\u672A\u627E\u5230
zj.sn.generate.failed =  \u589E\u808C\u6761\u7801\u751F\u6210\u5931\u8D25
insert.wip.info.error=\u63D2\u5165\u5728\u5236\u8868\u5931\u8D25
poilttest.save.error = \u4E2D\u8BD5\u63A5\u53E3\u4FDD\u5B58\u5931\u8D25
task.no.is.null = \u5728\u5236\u8868\u7684\u4EFB\u52A1\u4E3A\u7A7A
line.not.found=\u672A\u627E\u5230\u7EBF\u4F53\u4FE1\u606F
zj.sn.has.scan= {0} \u5DF2\u626B\u63CF\uFF0C\u5BF9\u5E94\u7684\u6574\u673ASN\u4E3A {1}
min.time.length.not.zero = \u6700\u5C0F\u5206\u5E03\u65F6\u957F\uFF0C\u4E0D\u80FD\u5C0F\u4E8E0
max.time.length.not.zero = \u6700\u5927\u5206\u5E03\u65F6\u957F\uFF0C\u5FC5\u987B\u5927\u4E8E0
time.length.progressive = \u65F6\u957F\u9012\u8FDB\u503C\uFF0C\u5FC5\u987B\u5927\u4E8E0

not.get.workshop.code = \u672A\u83B7\u53D6\u5230\u8F66\u95F4\u7F16\u7801
date.format.error = \uFFFD\uFFFD\uFFFD\u06B8\uFFFD\u02BD\uFFFD\uFFFD\uFFFD\uFFFD
external.type.not.found= psTask\u8868\u672A\u627E\u5230externalType
sys.look.external.type.not.found= 1026\u6570\u636E\u5B57\u5178\u503C\u672A\u914D\u7F6E
product.type.not.found= \u672A\u627E\u5230\u4EA7\u54C1\u5927\u7C7B
noarr.and.time.is.empty=\u4EFB\u52A1\u53F7\u6216\u6279\u6B21\u53F7\u6216\u6599\u5355\u4EE3\u7801\u4EE5\u53CA\u65F6\u95F4\u53C2\u6570\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
type.is.empty=\u7C7B\u578B\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
need.bind.workorder= \u5F53\u524D\u5B50\u5DE5\u5E8F\u5DE5\u7AD9\u5FC5\u987B\u7ED1\u5B9A\u6307\u4EE4
cur.workstaion.is.not.outbound= \u6761\u7801\u5F53\u524D\u5DE5\u7AD9\u4E0D\u662F\u51FA\u5E93\uFF0C\u4E0D\u80FD\u4E0A\u7EBF
out.bound.workstation.not.found= \u672A\u627E\u5230\u51FA\u5E93\u5DE5\u7AD9
sn.has.out.bound= {0} \u5DF2\u7ECF\u51FA\u5E93\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
these.matrial.has.used = {0} \u6599\u76D8\u88AB\u4F7F\u7528
parent.pk.not.found = \u672A\u627E\u5230\u7269\u6599\u6807\u8BC6\u5361
get.pk.code.info.error = \u672A\u4F20\u6599\u76D8\u4FE1\u606F
Maintained.totalQty.exceed.stantard=\u7EF4\u62A4\u7684\u603B\u6570\u91CF\u8D85\u8FC7\u6807\u51C6\u7528\u91CF
dip.material.san.not.complete=DIP\u4E0A\u6599\u4FE1\u606F\u4E3A\u7EF4\u62A4\u5B8C\u6210\uFF0C\u8BF7\u8054\u7CFB\u76F8\u5173\u4EBA\u7EF4\u62A4\u5B8C\u6210\uFF01
work.order.is.null=\u672A\u4F20\u6307\u4EE4\u4FE1\u606F
item.not.in.task= \u6574\u673A\u4EFB\u52A1\u9700\u6C42\u65E0\u6B64\u7269\u6599\uFF0C\u8BF7\u786E\u8BA4\uFF01
pk.code.null="\u8BF7\u8F93\u5165\u67E5\u8BE2\u6761\u4EF6"
get.avl.error="AVL\u4FE1\u606F\u67E5\u8BE2\u9519\u8BEF"
item.no.null=\u7269\u6599\u4EE3\u7801\u4E3A\u7A7APK_CODE_NULL
pkCode.is.null=pKCode \u4E3A\u7A7A
get.itemlistno.error=\u6839\u636E\u6279\u6B21\u67E5\u8BE2\u6599\u5355\u4EE3\u7801\u51FA\u9519
product.task.is.null=\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
reelid.sn.is.null=reelid\u7684\u6761\u7801\u4E0D\u5B58\u5728
reelId.is.null=ReelId\u672A\u6CE8\u518C,\u8BF7\u786E\u8BA4
print.info.null=\u672A\u67E5\u8BE2\u5230\u6253\u5370\u4FE1\u606F
lpn.info.null=\u7BB1\u53F7\u4E0D\u5B58\u5728
parent.pk.null=\u8BF7\u8F93\u5165\u7269\u6599\u6807\u8BC6\u5361ID
get.bom.error=\u672A\u83B7\u53D6\u5230REELID\u7684BOM\u4FE1\u606F
ps.task.null=\u5F53\u524D\u5BF9\u5E94\u6279\u6B21\u672A\u67E5\u8BE2\u5230\u6599\u5355\u4EE3\u7801\u4FE1\u606F
exceed.stantard.count=\u8D85\u51FA\u6807\u51C6\u7528\u91CF:{0}\u5269\u4F59\u53EF\u7EF4\u62A4\u6570\u91CF\u4E3A{1}
no.message.need.to.deleted=\u6CA1\u6709\u4FE1\u606F\u53EF\u4EE5\u88AB\u5220\u9664
isolation.can.not.outbound=\u9694\u79BB\u72B6\u6001\u4E0D\u5141\u8BB8\u51FA\u5E93
sn.not.in.wip= \u6761\u7801{0}\u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u6761\u7801\u5728\u5728\u5236\u8868\u4E2D\u4E0D\u5B58\u5728
sn.not.exist.wip= \u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u6761\u7801\u5728\u5728\u5236\u8868\u4E2D\u4E0D\u5B58\u5728
data.repair.status.is.not.to.be.received = \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u5F85\u63A5\u6536
data.repair.status.is.not.fiction= \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D
data.repair.status.is.not.to.be.received.or.maintenance= \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u5F85\u63A5\u6536\u6216\u8005\u5F85\u7EF4\u4FEE
no.permisssion.to.reject.in.received.status= \u5F85\u63A5\u6536\u72B6\u6001\u4E0B\u8BE5\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650
no.permisssion.to.reject.in.maintenance.status= \u5F85\u7EF4\u4FEE\u72B6\u6001\u4E0B\u8BE5\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650
return.repair.sn.item.no.not.found=\u67E5\u4E0D\u5230\u8FD4\u8FD8\u6761\u7801\u7684\u7269\u6599\u4EE3\u7801
env.check.failed=\u73AF\u4FDD\u5C5E\u6027\u6821\u9A8C\u4E0D\u901A\u8FC7\uFF0C\u8BF7\u786E\u8BA4
return.repair.sn.item.code.not.match=\u9001\u4FEE\u6761\u7801\u548C\u8FD4\u8FD8\u6761\u7801\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.repair.order.found=\u627E\u4E0D\u5230\u9001\u4FEE\u5355
main.process.not.match=\u4E3B\u5DE5\u5E8F\u4E0E\u9001\u4FEE\u6761\u7801\u4E3B\u5DE5\u5E8F\u4E0D\u4E00\u81F4\u672A\u627E\u5230\u6A21\u7EC4\u4E0A\u6599\u8BE6\u60C5\u4FE1\u606F
main.process.is.not.warehouse.entry=\u6574\u673A\u751F\u4EA7\u7EF4\u4FEE\u65F6\u8FD4\u8FD8\u6761\u7801\u4E3B\u5DE5\u5E8F\u672A\u5728\u6570\u636E\u5B57\u5178\u914D\u7F6E
return.sn.repair.sn.main.process.not.match=\u7F6E\u6362\u5931\u8D25\uFF0C\u8FD4\u8FD8\u6761\u7801\u4E0E\u9001\u4FEE\u6761\u7801\u4E3B\u5DE5\u5E8F\u3001\u5B50\u5DE5\u5E8F\u3001\u5DE5\u7AD9\u4E0D\u4E00\u81F4
barcode.of.machine.not.allowed.replaced=\u6574\u673A\u6761\u7801\u4E0D\u5141\u8BB8\u7F6E\u6362
repair.record.not.found=\u65E0\u7EF4\u4FEE\u8BB0\u5F55
repair.sn.not.to.be.repaired=\u9001\u4FEE\u6761\u7801\u4E0D\u662F\u5F85\u7EF4\u4FEE
data.not.found=\u6570\u636E\u67E5\u8BE2\u5931\u8D25
wip.info.data.not.found=\u6761\u7801\u5728wip_info\u8868\u4E2D\u4E0D\u5B58\u5728
sn.sent.repair.not.allowed=\u6761\u7801{0}\u9001\u4FEE\u6B21\u6570\u5DF2\u8FBE\u5230\u9608\u503C\uFF0C\u4E0D\u5141\u8BB8\u9001\u4FEE!
wip.not.exist.wip.info=\u6761\u7801{0}\u5728wip_info\u8868\u4E2D\u4E0D\u5B58\u5728
sub.sn.not.found.in.wipinfo=\u5B50\u90E8\u4EF6\u6761\u7801\u5728wip_info\u8868\u4E2D\u4E0D\u5B58\u5728
item.sn.not.found.in.wipinfo=\u7269\u6599\u6761\u7801\u5728wip_info\u8868\u4E2D\u4E0D\u5B58\u5728
replace.sn.not.found.in.wipinfo=\u66F4\u6362\u6761\u7801\u5728wip_info\u8868\u4E2D\u4E0D\u5B58\u5728
return.sn.is.empty=\u8FD4\u8FD8\u6761\u7801\u4E3A\u7A7A
return.sn.is.scraped=\u8FD4\u8FD8\u6761\u7801\u5DF2\u62A5\u5E9F
sn.and.apply.time.and.receive.time.is.null = \u53EA\u6709\u6839\u636E\u6761\u7801\u3001\u9001\u4FEE\u5355\u53F7\u3001\u6279\u6B21/\u4EFB\u52A1\u53F7\u53EF\u4EE5\u5355\u72EC\u67E5\u8BE2\uFF0C\u5176\u4F59\u6761\u4EF6\u5FC5\u987B\u9009\u62E9\u7533\u8BF7\u65F6\u95F4\u6216\u8005\u63A5\u6536\u65F6\u95F4
work.order.has.complete=\u6307\u4EE4\u5DF2\u5B8C\u5DE5
rec.info.error=\u9001\u4FEE\u4FE1\u606F\u5F02\u5E38\uFF0C\u5B58\u5728\u591A\u6761\u72B6\u6001\u4E3A\u5F85\u7EF4\u4FEE\u6216\u7EF4\u4FEE\u7F6E\u6362\u7684\u5355\u636E
sn.get.item.code=\u6839\u636E\u6761\u7801\u67E5\u8BE2\u4EE3\u7801\u63A5\u53E3\u51FA\u9519
sn.get.item.code.null=\u672A\u67E5\u8BE2\u5230\u66F4\u6362\u6761\u7801\u7684\u7269\u6599\u4EE3\u7801
rec.info.null=\u8BE5\u6761\u7801\u672A\u67E5\u8BE2\u5230\u7B26\u5408\u6761\u4EF6\u7684\u9001\u4FEE\u4FE1\u606F
return.repair.sn.is.be.same=\u9001\u4FEE\u6761\u7801\u4E0D\u80FD\u4E0E\u8FD4\u56DE\u6761\u7801\u4E00\u6837
sup.select.error=\u672A\u67E5\u8BE2\u5230\u6761\u7801\u7684\u578B\u53F7\u3001\u4F9B\u5E94\u5546\u3001\u54C1\u724C
not.find.record.of.this.sn = \u6CA1\u6709\u627E\u5230\u8BE5\u6761\u7801\u7684\u751F\u4EA7\u8BB0\u5F55
not.find= \u6CA1\u6709\u627E\u5230
this.sn= \uFF0C\u8FD9\u4E2A\u6761\u7801\uFF01
never.get.this.sn=\u672A\u83B7\u53D6\u5230\u6761\u7801\uFF01
never.get.fromStation.of.sn= \u672A\u83B7\u53D6\u5230\u6765\u6E90
this.barcode.has.no.binding.records= \u8BE5\u6761\u7801\u6CA1\u6709\u7ED1\u5B9A\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4
the.system.barcode.corresponding.to.the.barcode.does.not.exist.in.the.wipInfo.table = \u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u6761\u7801\u5728wipInfo\u8868\u4E2D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
the.barcode.status.of.the.board.does.not.match.the.source= \u5355\u677F\u6761\u7801\u72B6\u6001\u4E0E\u6765\u6E90\u4E0D\u7B26\uFF0C\u8BF7\u786E\u8BA4\uFF01
the.entire.device.can.be.sent.for.repair.only.after.being.ex-warehoused = \u6574\u673A\u9700\u8981\u51FA\u5E93\u624D\u80FD\u9001\u4FEE\uFF01
insertPmRepairRBatch= insertPmRepairRcvBatch{}
submitPmRepairRcvBatch=  submitPmRepairRcvBatch{}
deliveryNo= \uFF0C\u8FD9\u4E2A\u5355\u636E\u53F7\uFF01
there.is.no.material.code.information= \u6CA1\u6709\u5BF9\u5E94\u7684\u7269\u6599\u4EE3\u7801\u4FE1\u606F
return.sn.craf.section.check.data.is.not.found=\u672A\u83B7\u53D6\u5230\u8FD4\u8FD8\u6761\u7801\u7684\u4E3B\u5DE5\u5E8F\u6821\u9A8C\u914D\u7F6E\u6570\u636E
change.item.no.failed=\u7F6E\u6362\u7269\u6599\u7ED1\u5B9A\u5173\u7CFB\u5931\u8D25
zj.sn.is.null=\u9001\u4FEE\u5355\u677F\u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u6761\u7801\u4E0D\u5B58\u5728
rcv.info.error=\u9001\u4FEE\u6761\u7801\u7684\u9001\u4FEE\u4FE1\u606F\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
parent.pk.exist=\u8BE5REELID\u6709\u7ED1\u5B9A\u7269\u6599\u6807\u8BC6\u5361:{0},\u8BF7\u8F93\u5165\u6216\u626B\u63CF\u7269\u6599\u6807\u8BC6\u5361

no.bom.detail.info=\u65E0\u4E0A\u6599\u8868\u4FE1\u606F
no.mounting.detail.info=\u65E0\u673A\u53F0\u5728\u7528\u7269\u6599\u8868\u4E2D\u5BF9\u5E94\u4FE1\u606F
no.reel.id.in.mounting=reelId\u4E3A\u7A7A
no.location.no.in.his=locationNo\u4E3A\u7A7A
bom.list.is.null=bom\u6E05\u5355\u4E3A\u7A7A
sub.level.query.error=\u5206\u9636\u67E5\u8BE2\u5931\u8D25
headid.of.workorder.is.null=\u6307\u4EE4\u7684headid\u4E3A\u7A7A
data_dictionary.over.stock.time.is.null=\u79EF\u538B\u5468\u671F\u8BA1\u7B97\u65B9\u5F0F\u6570\u636E\u5B57\u5178\u4E3A\u7A7A
data_dictionary.repair.tock.time.is.null=\u7EF4\u4FEE\u5468\u671F\u8BA1\u7B97\u65B9\u5F0F\u6570\u636E\u5B57\u5178\u4E3A\u7A7A
sys.error=\u6570\u636E\u5B57\u5178\u67E5\u8BE2\u5931\u8D25
no.query.result={0}\u63A5\u53E3\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
gx.interface.error=\u5DE5\u5E8F\u8F6C\u4EA4\u5F02\u5E38:{0}

process.is.null=\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u7A7A\uFF01
item.name.empty=\u7269\u6599\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A\uFF01
line.info.is.not.found.of.code=\u627E\u4E0D\u5230\u7EBF\u4F53{0}\u4FE1\u606F
work.order.no.is.not.correct=A\u3001B\u9762\u6307\u4EE4\u72B6\u6001\u90FD\u4E0D\u662F(\u5DF2\u63D0\u4EA4\u3001\u5DF2\u5F00\u5DE5\u3001\u6302\u8D77),\u4E0D\u5141\u8BB8\u5BFC\u5165\u4E0A\u6599\u8868
line.station.info.is.not.found.of.code=\u627E\u4E0D\u5230\u7EBF\u4F53{0}\u7AD9\u4F4D\u4FE1\u606F
no.bom.data.to.split=\u6CA1\u6709\u9700\u62C6\u5206\u7684BOM\u6570\u636E
tag.splitting.failed=\u4F4D\u53F7\u62C6\u5206\u5931\u8D25
basic.material.info.is.not.maintained=\u672A\u7EF4\u62A4\u7269\u6599\u57FA\u7840\u5C5E\u6027
no.bom.details_data=\u65E0BOM\u660E\u7EC6\u6570\u636E
tag.analysis.failed=\u4F4D\u53F7\u5206\u6790\u5931\u8D25
the.rule.of.independent.number.is.not.satisfied=\u4E0D\u6EE1\u8DB3\u72EC\u7ACB\u53F7\u89C4\u5219
there.are.multiple.connectors=\u5B58\u5728\u591A\u4E2A\u8FDE\u63A5\u7B26,
the.consecutive.number.rule.is.not.met=\u4E0D\u6EE1\u8DB3\u8FDE\u7EED\u53F7\u89C4\u5219,
not.consecutive.tag=\u4E0D\u662F\u8FDE\u7EED\u4F4D\u53F7\uFF0C
characters.before.two.tag.numbers.are.not.equal=\u4E24\u4E2A\u4F4D\u53F7\u6570\u5B57\u524D\u5B57\u7B26\u4E0D\u76F8\u7B49
start.tag.is.greater.than.or.equal.to.end.tag=\u5F00\u59CB\u4F4D\u53F7\u5927\u4E8E\u6216\u7B49\u4E8E\u7ED3\u675F\u4F4D\u53F7,
file.parsing.error=\u6587\u4EF6\u89E3\u6790\u9519\u8BEF
file.excelAnalysis.error=\u6587\u4EF6\u89E3\u6790\u9519\u8BEF,\u8BF7\u786E\u8BA4\u6587\u4EF6\u662F\u5426\u5DF2\u52A0\u5BC6
error=\u9519\u8BEF
task.no.can.not.null=\u4EFB\u52A1\u53F7\u4E3A\u7A7A
plan.no.is.null=\u6279\u6B21\u53F7\u4E3A\u7A7A
task.quantity.is.null=\u6CA1\u6709\u4EFB\u52A1\u6570\u91CF
task.quantity.more.than=\u4EFB\u52A1\u6570\u91CF\u8D85\u8FC799999
existed=\u5DF2\u5B58\u5728
fault.code.and.fault.phenomenon.are.required=\u6545\u969C\u4EE3\u7801\u3001\u6545\u969C\u73B0\u8C61\u5FC5\u586B
file.initialization.failed=\u6587\u4EF6\u521D\u59CB\u5316\u5931\u8D25
file.is.empty=\u6587\u4EF6\u4E3A\u7A7A
data.is.empty=\u6570\u636E\u4E3A\u7A7A
data.import.succeeded=\u6570\u636E\u5BFC\u5165\u6210\u529F
data.validation.fail=\u6570\u636E\u9A8C\u8BC1\u6709\u8BEF
the.table.does.not.contain.the.following.columns=\u8868\u683C\u4E0D\u542B\u4EE5\u4E0B\u5217
data.error.formula.cannot.be.used=\u6570\u636E\u6709\u8BEF\uFF0C\u4E0D\u80FD\u4F7F\u7528\u516C\u5F0F
data.type_error=\u6570\u636E\u7C7B\u578B\u9519\u8BEF
paging.query.parameters.is.empty=\u8BF7\u6307\u5B9A\u5206\u9875\u67E5\u8BE2\u53C2\u6570\uFF1A\u7B2C\u51E0\u9875\u3001\u6BCF\u9875\u663E\u793A\u8BB0\u5F55\u6761\u6570\uFF01
upn.exception.calculation=\u8BA1\u7B97UPH\u5DE5\u65F6\u53D1\u751F\u5F02\u5E38
uploaded.file.not.found=\u672A\u627E\u5230\u4E0A\u4F20\u7684\u6587\u4EF6
file.parsing.success=\u6587\u4EF6\u89E3\u6790\u6210\u529F

verification.passed=\u6821\u9A8C\u901A\u8FC7
error.code.is.null=\u8BF7\u8F93\u5165\u9519\u8BEF\u4EE3\u7801errorCode
error.code.not.found=\u672A\u627E\u5230\u4E0D\u826F\u4EE3\u7801\uFF01
excel.parsing.error=excel \u89E3\u6790\u9519\u8BEF


error.reason.code.is.null=\u8BF7\u8F93\u5165\u4E0D\u826F\u539F\u56E0\u4EE3\u7801
mt.small.type.not.found=\u8BF7\u8F93\u5165\u4E0D\u826F\u539F\u56E0\u4EE3\u7801
pre.processing.destination.code.already.exists=\u524D\u52A0\u5DE5\u53BB\u5411\u4EE3\u7801\u5DF2\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
department.code.is.empty=\u90E8\u95E8\u4EE3\u7801\u4E3A\u7A7A
department.name.is.empty=\u90E8\u95E8\u540D\u79F0\u4E3A\u7A7A
creator.is.empty=\u521B\u5EFA\u4EBA\u4E3A\u7A7A
department.code.is.existed=\u90E8\u95E8\u4EE3\u7801\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
department.name.is.existed=\u90E8\u95E8\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
modifier.is.empty=\u4FEE\u6539\u4EBA\u4E3A\u7A7A

employee.is.not.exist=\u5458\u5DE5{0}\u4E0D\u5B58\u5728
factory.code.is.exist=\u5DE5\u5382\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
workorder.craftsection.is.empty=\u6307\u4EE4\u4E3B\u5DE5\u5E8F\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
line.code.is.existed=\u7EBF\u4F53\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
workshop.code.is.existed=\u6B64\u8F66\u95F4\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
directory.code.is.existed=\u6B64\u76EE\u5F55\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
insert.data.is.zero=\u65B0\u589E\u6570\u636E0\u6761\uFF01
dictionary.code.is.existed=\u5B57\u5178\u9879\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
date.is.inconsistent.please.modify.Client.date=\u65E5\u671F\u4E0D\u4E00\u81F4\u8BF7\u4FEE\u6539\uFF0C\u5BA2\u6237\u7AEF\u65E5\u671F\uFF1A  
server.date=\uFF0C\u670D\u52A1\u7AEF\u65E5\u671F\uFF1A  
no.valid.date.information.transmit=\u6CA1\u6709\u4F20\u9012\u6709\u6548\u7684\u65E5\u671F\u4FE1\u606F
process.code.is.not.existed=\u672A\u627E\u5230\u5B50\u5DE5\u5E8F\u4EE3\u7801

packing.record.is.not.found=\u672A\u627E\u5230\u6B64\u5BB9\u5668{0}\u7684\u88C5\u7BB1\u8BB0\u5F55
not.found.sn.bar.code.record=\u672A\u627E\u5230\u6761\u7801   {0}\u7684\u8BB0\u5F55
failed.to.insert.period.output.information=\u63D2\u5165\u65F6\u6BB5\u4EA7\u51FA\u4FE1\u606F\u5931\u8D25\uFF01
failed.to.update.period.output.information=\u66F4\u65B0\u65F6\u6BB5\u4EA7\u51FA\u4FE1\u606F\u5931\u8D25\uFF01
workorder.soursys.error=\u6307\u4EE4\u7C7B\u578B\u9519\u8BEF
smt.workorder.update.fail=\u4FEE\u6539SMT\u6307\u4EE4\u5C5E\u6027\u8868\u5931\u8D25
header.table.insert.fail=\u5F80\u5934\u8868\u65B0\u589E\u5931\u8D25
detail.table.insert.fail==\u65B0\u589E\u660E\u7EC6\u8868\u5931\u8D25
bsmtbomdetail.table.item.code.error=\u4E0A\u6599\u8868\u4E2D\u6599\u5355\u4EE3\u7801\u6709\u8BEF
bsmtbomdetail.table.pcb.qty.error=\u4E0A\u6599\u8868\u4E2D\u62FC\u677F\u6570\u4E0D\u662F\u6B63\u6574\u6570
no.station.column=\u6CA1\u6709\u7AD9\u4F4D\u5217\uFF01
no.item.code.column=\u6CA1\u6709\u7269\u6599\u4EE3\u7801\u5217\uFF01
no.am.column=\u6CA1\u6709Am\u5217\uFF01
no.bm.column=\u6CA1\u6709Bm\u5217\uFF01
related.line.info.is.not.found=\u5173\u8054\u7EBF\u4F53\u4FE1\u606F\u7F3A\u5931
no.work.order.information.for.double.track.line=\u53CC\u8F68\u7EBF\u65E0\u6307\u4EE4\u4FE1\u606F
item.information.not.maintained={0}\u672A\u7EF4\u62A4\u7269\u6599\u4FE1\u606F!
start.work.order.is.empty=\u5F53\u524D\u65E0\u5F00\u5DE5\u6307\u4EE4
offline=\u79BB\u7EBF
ng.no.mounting.detail.info=NG,\u65E0\u5BF9\u5E94\u7684\u673A\u53F0\u5728\u7528\u7269\u6599
ng.mounting.detail.info.is.not.enough=NG,\u673A\u53F0\u5728\u7528\u7269\u6599\u4E0D\u8DB3
work.station.info.is.not.configured=\u672A\u914D\u7F6E\u5DE5\u4F4D\u4FE1\u606F
code.printing.succeeded=\u55B7\u7801\u6210\u529F!
update.mounting.info.failed=\u66F4\u65B0\u673A\u53F0\u5728\u7528\u7269\u6599\u8868\u5931\u8D25
submit.failed=\u63D0\u4EA4\u5931\u8D25
idention.or.entitytype.can.not.be.empty=\u5B9E\u7269\u6807\u8BC6\u548C\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
entitytype.must.be.tooling.or.fixture=\u5B9E\u7269\u7C7B\u578B\u987B\u4E3A\u5DE5\u88C5\u6216\u5939\u5177
idention.is.existed=\u5B9E\u7269\u6807\u8BC6\u5DF2\u5B58\u5728
device.interaction.information.insert.failed=\u65B0\u589E\u8BBE\u5907\u4EA4\u4E92\u4FE1\u606F\u5931\u8D25
file.format.error.please.use.excel=\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u4F7F\u7528excel\u5BFC\u5165\uFF01
create.excel.error=\u521B\u5EFAexcel\u5931\u8D25\uFF01

batch.query.contract.numbers.fifty.at.most=\u5408\u540C\u53F7\u6700\u591A\u540C\u65F6\u6279\u91CF\u67E5\u8BE250\u6761

batch.query.return.order.five.hundred.at.most=\u8FD4\u8D27\u6307\u4EE4\u53F7\u6700\u591A\u540C\u65F6\u6279\u91CF\u67E5\u8BE2500\u6761
batch.query.packing.box.order.five.hundred.at.most=\u88C5\u7BB1\u5355\u53F7\u6700\u591A\u540C\u65F6\u6279\u91CF\u67E5\u8BE2500\u6761
sn.is.existed.the.work.station=\u6761\u7801\u5DF2\u5728{0}\u7684{1}\u5DE5\u7AD9\u4E0B\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4
failed.to.insert.temporary.table=\u63D2\u5165\u4E34\u65F6\u8868\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
job.information.is.empty=\u67E5\u8BE2\u4F5C\u4E1A\u6307\u4EE4\u8868\uFF0C\u4F5C\u4E1A\u4FE1\u606F\u4E3A\u7A7A
exist.smt.material.mounting.info=\u8BE5reelId\u5B58\u5728\u673A\u53F0\u5728\u7528\u4FE1\u606F\uFF0C\u8BF7\u626B\u63CF\u9700\u8981\u63A5\u6599\u7684\u65B0\u6599\u76D8
pk.code.info.is.empty=PK_CODE_INFO\u67E5\u627E\u5931\u8D25\uFF01
pk.code.info.qty.is.null=PK_CODE_INFO\u6570\u91CF\u4E3A\u7A7A\u503C\uFF01
total.number.of.defective.products.is.zero=\u4E0D\u826F\u54C1\u603B\u6570\u4E3A0

repair.source.is.empty=\u9001\u4FEE\u6765\u6E90\u4E3A\u7A7A\uFF0C\u8BF7\u91CD\u65B0\u63D0\u4EA4
building.is.empty=\u697C\u680B\u4E3A\u7A7A
sn.has.not.been.repair.returned=\u8BE5\u6761\u7801\u8FD8\u6CA1\u6709\u505A\u7EF4\u4FEE\u8FD4\u8FD8
whole.machine.can.only.be.sent.out.for.repair=\u6574\u673A\u9700\u8981\u51FA\u5E93\u624D\u80FD\u9001\u4FEE
no.production.record.found.for.this.barcode=\u6CA1\u6709\u627E\u5230\u8BE5\u6761\u7801\u7684\u751F\u4EA7\u8BB0\u5F55
not.repair.two=\u6761\u7801{0}\u4E0D\u662F\u4E8C\u7EF4\uFF0C\u4E0D\u80FD\u8FD4\u8FD8
craft.section.inconsistent.can.not.be.returned=\u6761\u7801{0}\u539F\u5B50\u5DE5\u5E8F\u4E3A\uFF1A{1},\u4E0E\u539F\u5F55\u5165\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
line.code.inconsistent.can.not.be.returned=\u6761\u7801{0}\u539F\u7EBF\u4F53\u4E3A\uFF1A{1},\u4E0E\u5F55\u5165\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
from.station.inconsistent.can.not.be.returned=\u6761\u7801{0}\u539F\u6765\u6E90\u4E3A\uFF1A{1},\u4E0E\u5F55\u5165\u53BB\u5411\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
repair.status.not.complete.can.not.be.returned=\u6761\u7801{0}\u7EF4\u4FEE\u72B6\u6001\uFF1A{1}\uFF0C\u5E94\u4E3A\u7EF4\u4FEE\u5B8C\u6210\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
input.type.not.has.sn.can.not.be.returned=\u6761\u7801{0}\u7684\u8F93\u5165\u6765\u6E90\u4E0D\u662F\u6709\u6761\u7801\u8F93\u5165\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
input.type.not.has.no.sn.can.not.be.returned=\u6761\u7801{0}\u7684\u8F93\u5165\u6765\u6E90\u4E0D\u662F\u65E0\u6761\u7801\u8F93\u5165\uFF0C\u4E0D\u80FD\u8FD4\u8FD8\uFF01
prodplanid.has.no.complated.sn=\u6279\u6B21{0}\u672A\u67E5\u8BE2\u5230\u5F85\u7EF4\u4FEE\u5B8C\u6210\u7684\u6761\u7801
prodplanid.shall.be.seven.digits=\u6279\u6B21\u53F7\u5E94\u4E3A7\u4F4D\u6570\u5B57
repair.return.qty.error=\u8FD4\u8FD8\u6570\u91CF\u5E94\u57281\u5230100
sn.delete.fis.error=\u6761\u7801{0}\u5220\u9664fis\u6570\u636E\u51FA\u9519
mes.repair.restore.error=\u4FEE\u6539mes\u4E8C\u7EF4\u6570\u636E\u51FA\u9519
input.record.not.found=\u6CA1\u6709\u627E\u5230\u6761\u7801{0}\u5F55\u5165\u8BB0\u5F55\uFF01
it.needs.to.be.repaired.first=\u6761\u7801{0}\u9700\u8981\u5148\u8FDB\u884C\u9001\u4FEE\uFF01
no.wip.info.record.or.not.repairing=\u6761\u7801{0}\u5728\u5236\u8868\u4E0D\u5B58\u5728\u8BB0\u5F55\u6216\u4E0D\u662F\u7EF4\u4FEE\u4E2D\uFF01
not.be.repairing=\u6761\u7801{0}\u4E0D\u662F\u7EF4\u4FEE\u4E2D\uFF01
repair.zs.vaildate.fail=\u7EF4\u4FEE\u4E2D\u5F0F\u6821\u9A8C\u5931\u8D25,\u5177\u4F53\u4FE1\u606F:{0}
no.record.in.scan.history.table=\u626B\u63CF\u5386\u53F2\u8868\u65E0\u8BB0\u5F55
craft.section.not.loaded=\u4E3B\u5DE5\u5E8F\u672A\u52A0\u8F7D
failed.to.update.qty=\u66F4\u65B0QTY\u6570\u91CF\u5931\u8D25
batch.insert.transaction.table.failed=\u6279\u91CF\u63D2\u5165\u4EA4\u6613\u8868\u5931\u8D25
no.data.to.save=\u6CA1\u6709\u9700\u8981\u4FDD\u5B58\u7684\u6570\u636E
sn.is.scrapped=\u6761\u7801\u5DF2\u7ECF\u62A5\u5E9F:{0}
sn.of.the.device.is.scrapped=\u8BE5\u6574\u673A\u6761\u7801\u5BF9\u5E94\u7684\u5355\u677F\u5DF2\u7ECF\u62A5\u5E9F
status_no_restore_device=\u8BE5\u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u8FD8\u6CA1\u6709\u505A\u7EF4\u4FEE\u8FD4\u8FD8
device.to.sn.is.scrap=\u8BE5\u5355\u677F\u6761\u7801\u5BF9\u5E94\u7684\u6574\u673A\u5DF2\u7ECF\u62A5\u5E9F
no_wipinfo_of_sn_corresponding_device==\u6CA1\u6709\u627E\u5230\u8BE5\u6761\u7801\u5BF9\u5E94\u6574\u673A\u7684\u751F\u4EA7\u8BB0\u5F55
no.information.to.submit=\u6CA1\u6709\u9700\u8981\u63D0\u4EA4\u7684\u4FE1\u606F
sn_is_submited_by_other=\u6761\u7801\u5DF2\u88AB\u5176\u4ED6\u4EBA\u63D0\u4EA4
repair.input.is.required.before.scrap=\u5148\u8FDB\u884C\u7EF4\u4FEE\u5F55\u5165\u624D\u80FD\u62A5\u5E9F\uFF01
sn.has.no.planid=\u6761\u7801\u6CA1\u6709\u6279\u6B21\u53F7\uFF0C\u4E0D\u80FD\u5904\u7406\uFF01
search_reelid_error=Reelid\u5386\u53F2\u8F68\u8FF9\u8BB0\u5F55\u8868\u67E5\u8BE2\u5931\u8D25
item.no.is.not.exist=\u7269\u6599\u4EE3\u7801{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
multi.brand.not.support=\u9886\u6599\u5355{0}\u5B58\u5728\u591A\u4E2A\u54C1\u724C
not.the.same.planid=\u4E0D\u662F\u4E00\u4E2A\u6279\u6B21\u4E0D\u80FD\u7EDF\u4E00\u5904\u7406\uFF01
not.be.repair.summit=\u6CA1\u6709\u7EF4\u4FEE\u63D0\u4EA4\uFF0C\u4E0D\u80FD\u62A5\u5E9F!
cannot.scrap.repeatedly=\u4E0D\u80FD\u91CD\u590D\u62A5\u5E9F!
afferent.value.is.null=\u4F20\u5165\u503C\u4E3A\u7A7A
uncleaned.sn.in.box=\u7BB1\u4E2D\u8FD8\u6709\u672A\u6E05\u7406\u5B8C\u7684\u6761\u7801\uFF0C\u8BF7\u786E\u8BA4
process.inconsistencies.in.referral.process=\u8F6C\u4EA4\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u5DE5\u5E8F\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u91CD\u65B0\u8F6C\u4EA4
workoder.not.matched.prodplanid=\u6307\u4EE4:{0},\u4E0E\u6279\u6B21:{1},\u5BF9\u5E94\u4E0D\u4E0A\uFF0C\u8BF7\u786E\u8BA4
first.process.workStation.of.workOrder.not.found=\u672A\u627E\u5230\u6307\u4EE4Route\u9996\u5DE5\u5E8F/\u9996\u5DE5\u7AD9\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
workOrder_status_incorrect=\u6307\u4EE4\u72B6\u6001\u4E0D\u5BF9\uFF0C\u8BF7\u786E\u8BA4\uFF01
exception_in_get_next_process=\u83B7\u53D6\u4E0B\u4E00\u5DE5\u5E8F\u51FA\u73B0\u5F02\u5E38
sn_not_belong_prodPlanId=\u6761\u7801: {0} ,\u4E0D\u5C5E\u4E8E\u6279\u6B21: {1} ,\u8BF7\u786E\u8BA4
period.output.error=\u65F6\u6BB5\u4EA7\u51FAERROR\
xType.failed.warehouse=\u83B7\u53D6 {0} \u3010{1}\u3011 \u5165\u5E93\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4!
box.contents.are.empty=\u7BB1\u5185\u5BB9\u4E3A\u7A7A
barcode_is_controlled=\u6761\u7801\u88AB\u7BA1\u63A7\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
delete_successed=\u5220\u9664\u6210\u529F
spi_and_instruction_is_empty=SPI\u548C\u6307\u4EE4\u62FC\u677F\u6570\u5747\u4E3A\u7A7A
linecode.tracesource.error=\u7EBF\u4F53\uFF1A {0} \u8FFD\u6EAF\u6765\u6E90\uFF1A{1} error
sn_input_error=\u6761\u7801\u8F93\u5165\u9519\u8BEF

workorder_is_null_or_less_than_seven=\u6307\u4EE4\u4E3A\u7A7A\u6216\u4F4E\u4E8E\u4E03\u4F4D\uFF01
Barcode.prodPlanId.error=\u6761\u7801\u6279\u6B21\u9519\u8BEF\uFF01
line.info.not.maintained=SPI\u8C03\u7528\u8FC7\u677F\u6570\u5904\u7406,\u672A\u7EF4\u62A4\u7EBF\u4F53\u4FE1\u606F:lineCode-{0}
smt.investment.scan.success=SMT\u6295\u5165\u626B\u63CF\u6210\u529F
spi.deductions.and.retroactive.exception=\u5728SPI\u8FDB\u884C\u6263\u6570\u53CA\u8FFD\u6EAF\u8BA1\u7B97\u5F02\u5E38
workorder_id_is_null=\u6307\u4EE4ID\u4E3A\u7A7A
moduleno.is.null=\u8FFD\u6EAF\u5C5E\u6027\u4E3A\u6309\u6A21\u7EC4\u8BA1\u7B97\uFF0C\u4F46\u672A\u4F20\u5165moduleNo
last.module.data.is.passed.in=\u8FFD\u6EAF\u5C5E\u6027\u4E3A\u4E0D\u6309\u6A21\u7EC4\u8BA1\u7B97\uFF0C\u4F20\u5165\u975E\u6700\u540E\u6A21\u7EC4\u6570\u636E
pk_code_info_not_found=\u672A\u627E\u5230\u65B0\u6599\u76D8PK\u4FE1\u606F\uFF01pkCode:
itemqty_is_null=\u65B0\u6599\u76D8ItemQty\u4E3A\u7A7A\uFF01pkCode: {0}
qty_is_null_of_reelId=reelId\u6570\u91CF\u4E3A\u7A7A\u503C
bomqty_is_null=\u4E0A\u6599\u8868bomQty\u4E3A\u7A7A!cfgHeaderId: {0}   locationNo: {1}
smt_tracing_cfgheaderid_is_null=\u4E0A\u6599\u8868\u5934\u8868Id\u4E3A\u7A7A
workShop_is_null=\u8F66\u95F4\u4E3A\u7A7A
Incorrect.traceback.type.parameter=\u8FFD\u6EAF\u7C7B\u578B\u53C2\u6570\u6709\u8BEF

mac.address.not.obtained=\u672A\u83B7\u53D6\u5230\u672C\u673Amac\u5730\u5740\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
failed.to.insert.or.update.binding.information=\u63D2\u5165\u6216\u66F4\u65B0\u7ED1\u5B9A\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4
no.started.work.order=\u6CA1\u6709\u6B63\u5728\u5F00\u5DE5\u7684\u6307\u4EE4\uFF01
standard_dosage_cannot_be_zero=\u6807\u51C6\u7528\u91CF\u4E0D\u80FD\u4E3A0
spi_data_process_success=SPI\u6570\u636E\u5904\u7406\u6210\u529F
work_order_process_code_group_is_null=\u6307\u4EE4\u5B50\u5DE5\u5E8F\u7EC4\u4E3A\u7A7A
param_validation_error=\u53C2\u6570\u6821\u9A8C\u5931\u8D25
has.be.seted.spi.deductions.and.retroactive=\u5DF2\u8BBE\u7F6E\u5728SPI\u8FDB\u884C\u6263\u6570\u53CA\u8FFD\u6EAF\u8BA1\u7B97
smt.material.low.level.calculation.successful=SMT\u7269\u6599\u4F4E\u4F4D\u8BA1\u7B97\u6210\u529F
input_content_format_incorrect=\u3010NG\u3011,\u8F93\u5165\u5185\u5BB9\u683C\u5F0F\u4E0D\u6B63\u786E,\u8BF7\u68C0\u67E5!
work_order_not_summited=\u3010NG\u3011,\u6307\u4EE4\u672A\u63D0\u4EA4,\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
work_order_qty_is_full=\u3010NG\u3011,\u6307\u4EE4\u6295\u5165\u6570\u91CF\u5DF2\u6EE1\uFF01
work_order_not_bound_craft=\u3010NG\u3011,\u6307\u4EE4\u672A\u7ED1\u5B9A\u5DE5\u827A,\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
employee.info.is.not.found=\u3010NG\u3011,\u5458\u5DE5\u4FE1\u606F\u4E0D\u5B58\u5728,\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
scans_number_cannot_be_zero=\u626B\u63CF\u6570\u91CF\u4E0D\u80FD\u4E3A0,\u8BF7\u91CD\u65B0\u8F93\u5165
please_scan_the_slab=\u8BF7\u626B\u63CF\u5927\u677F
slab_is_splited=\u5927\u677F {0}\u5DF2\u7ECF\u62C6\u5206\u5B8C\u6BD5,\u8BF7\u6362\u522B\u7684\u5927\u677F\u626B\u63CF
failed.to.insert.staging.scan.table=\u63D2\u5165\u5206\u677F\u626B\u63CF\u6682\u5B58\u8868\u5931\u8D25
please.scan.the.small.board=\u5927\u677F: {0} ,\u8BF7\u626B\u63CF\u5C0F\u677F
bigSn_has_not_been_disassembled=\u5927\u677F: {0} \u8FD8\u6CA1\u6709\u62C6\u5B8C,\u8BF7\u626B\u63CF\u5C0F\u677F
bigSn_is_scaned=\u5927\u677F:  {0}\u5DF2\u626B\u63CF,\u8BF7\u626B\u63CF\u5C0F\u677F
failed.to.update.staging.scan.table=\u66F4\u65B0\u5206\u677F\u626B\u63CF\u6682\u5B58\u8868\u5931\u8D25
bigSn_is_scaned_qty=\u5927\u677F {0} \u7684\u5C0F\u677F\u5DF2\u626B {1} \u4E2A,\u8BF7\u7EE7\u7EED\u626B\u63CF\u5C0F\u677F
failed.to.delete.staging.scan.table=\u5220\u9664\u5206\u677F\u626B\u63CF\u6682\u5B58\u8868\u5931\u8D25
operation.configuration.is.incoreect=\u3010NG\u3011,\u5DE5\u5E8F\u64CD\u4F5C\u914D\u7F6E\u6709\u8BEF,\u8BF7\u68C0\u67E5
sn_is_empty_of_work_order=\u8BE5\u6307\u4EE4\u4E0B\u6CA1\u6709\u6761\u7801\uFF0C\u8BF7\u68C0\u67E5
write.back.failed=\u56DE\u5199\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
scan_in_workstation_of_next=\u8BF7\u53BB {0} \u5DE5\u4F4D\u626B\u63CF
sn_is_bad=\u6761\u7801\u662F\u4E0D\u826F\u54C1
work_order_not_done=\u6761\u7801 {0}\u6307\u4EE4 {1}\u672A\u8D70\u5B8C\u6240\u6709\u6D41\u7A0B
fail.too.many.need.repair=\u5931\u8D25\u6B21\u6570\u8FC7\u591A\uFF0C\u8BF7\u9001\u4FEE
is.scaned=\u5DF2\u626B\u63CF

please.go.to.next.process=\u8BF7\u53BB({0})\u5DE5\u5E8F

please.go.to.workstation.of.next.process=\u8BF7\u53BB({0})\u5DE5\u7AD9

scan.in.workstation.of.next.process=\u8BF7\u53BB {0} \u5DE5\u7AD9\u626B\u63CF
save.succeed=\u4FDD\u5B58\u6210\u529F

write.back.step.failed=\u56DE\u5199STEP\u5931\u8D25
write.back.step.succeed=\u56DE\u5199STEP\u6210\u529F
defective.products.cannot.be.stored=\u4E0D\u826F\u54C1\u4E0D\u80FD\u5165\u5E93
products.need.test=\u4EA7\u54C1\u9700\u8981\u505A\u5355\u677F\u6D4B\u8BD5
sn.process.is.changed=\u6761\u7801\u5F53\u524D\u5DE5\u5E8F\u5DF2\u53D8\uFF0C\u8BF7\u91CD\u65B0\u626B\u63CF
not.allowed.to.invest.across.prodPlanId=\u4E0D\u5141\u8BB8\u8DE8\u6279\u6B21\u6295\u5165!
sn.is.not.exits=\u6761\u7801\u4E0D\u5B58\u5728
sn.not.delong.item.code=\u8BE5\u5355\u677F\u6761\u7801\u4E0D\u5C5E\u4E8E {0}\u6599\u5355\uFF0C\u8BF7\u786E\u8BA4!
no.item.info.of.item.code=\u65E0\u5BF9\u5E94\u7684\u6599\u5355\u6307\u4EE4
sn.has.not.been.invested=\u6761\u7801\u6CA1\u6709\u6295\u5165\u8FC7\uFF01
sn.out.of.prodPlanId.range=\u6761\u7801\u8D85\u51FA\u6279\u6B21\u8303\u56F4\uFF0C\u6279\u6B21\u6570\u91CF\u4E3A
workOrder.is.full=\u6307\u4EE4\u5DF2\u6EE1\uFF0C\u4E0D\u80FD\u7EE7\u7EED\u6295\u5165
wip.workOrderNo.not.exist=WIP\u4E2D\u6307\u4EE4\uFF1A {0} \u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
route.not.completed=\u5DE5\u827A\u8DEF\u5F84\u6CA1\u6709\u5B8C\u6210\uFF0C\u4E0D\u80FD\u5207\u6362\u6599\u5355
wip.process.not.in.route=wip\u5F53\u524D\u5B50\u5DE5\u5E8F\u4E0D\u5728\u5DE5\u827A\u8DEF\u5F84\u4E2D
not.found.work.order.scan.route=\u672A\u67E5\u8BE2\u5230 {0}\u6307\u4EE4\u7684\u626B\u63CF\u6D41\u7A0B
to.wip.next.process=\u8BF7\u53BBWIP\u5F53\u524D\u5DE5\u5E8F({0})\u4E0B\u5DE5\u5E8F
param_is_empty=\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
form_sn_can_not_be_null=\u5DE5\u88C5\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
get_eqp_info_exception=\u83B7\u53D6\u5DE5\u88C5\u4FE1\u606F\u53D1\u751F\u5F02\u5E38
eqp_info_not_found=\u6CA1\u6709\u627E\u5230\u5DE5\u88C5\u4FE1\u606F
set.batch.bind.info=\u8BF7\u5728\u901A\u7528\u626B\u63CF\u4E2D\u8BBE\u7F6E\u6279\u6B21\u7ED1\u5B9A\u4FE1\u606F
prodPlanId.is.not.match=\u5F53\u524D\u6761\u7801\u6279\u6B21\u4E0E\u7ED1\u5B9A\u6279\u6B21\u4E0D\u5339\u914D\uFF0C\u4E0D\u80FD\u6DF7\u6279\u6B21\u626B\u63CF
status.not.warehouse.in.or.out=\u8BE5\u5355\u677F\u6761\u7801 {0}\u4E0D\u662F\u5165\u5E93\u6216\u8005\u51FA\u5E93\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u7ED1\u5B9A\uFF01
sn.error.during.unbind=\u5DE5\u88C5\u6761\u7801{0} \u89E3\u7ED1\u65F6\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
formSn.binding=\u5DE5\u88C5\u7ED1\u5B9A\u4E2D...
mac_address_can_not_be_null=mac\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
no.device.for.mac.address=\u6CA1\u6709\u627E\u5230mac\u5730\u5740\u5BF9\u5E94\u7684\u8BBE\u5907\u4FE1\u606F
mac.device.no.line.or.workstation=mac\u5730\u5740\u5BF9\u5E94\u7684\u8BBE\u5907\u4E2D\u672A\u7EF4\u62A4\u7EBF\u4F53\u4FE1\u606F
process.is.null.line.station=\u8BE5\u7EBF\u4F53\u5DE5\u7AD9\u5BF9\u5E94\u7684\u5B50\u5DE5\u5E8F\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
no.address.in.plc=plc\u653E\u884C\u65F6\u672A\u67E5\u8BE2\u5230\u5BF9\u5E94\u7684Ip\u4FE1\u606F
maintain.url.plc.data.dictionary=\u8BF7\u5728\u6570\u636E\u5B57\u5178\u4E2D\u7EF4\u62A4\u8054\u673A\u5E73\u53F0plc\u653E\u884C\u7684url\u5730\u5740
not.tested.or.test.failed=\u5355\u677F\u6761\u7801\u6CA1\u6709\u7ECF\u8FC7\u6D4B\u8BD5\u6216\u8005\u672A\u901A\u8FC7\uFF0C\u4E0D\u5141\u8BB8\u4E0B\u7EBF\u626B\u63CF!
sn_is_arehouse_entry=\u8BE5\u6761\u7801{0}\u5F53\u524D\u72B6\u6001\u4E0D\u662F\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u88C5\u7BB1\uFF01
sn.is.scrapped.not.allowed.box=\u8BE5\u6761\u7801{0}\u5DF2\u7ECF\u62A5\u5E9F\uFF0C\u4E0D\u5141\u8BB8\u88C5\u7BB1\uFF01
sn.not.been.returned.not.allowed.box=\u8BE5\u6761\u7801{0}\u8FD8\u6CA1\u6709\u505A\u7EF4\u4FEE\u8FD4\u8FD8\uFF0C\u4E0D\u5141\u8BB8\u88C5\u7BB1\uFF01
sn.is.existed.in.box=\u5355\u677F\u6761\u7801{0}\u5728\u7BB1{1}\u4E2D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.test.instructions.or.instructions.not.scheduled=\u65E0\u6D4B\u8BD5\u6307\u4EE4\u6216\u6D4B\u8BD5\u6307\u4EE4\u672A\u6392\u4EA7
not.allowed.to.offline=\u6D4B\u8BD5\u6307\u4EE4\u7684\u6700\u540E\u4E00\u9053\u5DE5\u5E8F\u7C7B\u578B\u4E3A\u81EA\u52A8\u6D4B\u8BD5\uFF0C\u4E0D\u5141\u8BB8\u4E0B\u7EBF
update_prodplanid_information_failed=\u66F4\u65B0\u6279\u6B21\u4FE1\u606F\u5931\u8D25
failed.to.get.redis.lock=\u83B7\u53D6redis\u9501\u8D44\u6E90\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5
sub_sn_has_bound=\u5B50\u6761\u7801\u5DF2\u7ED1\u5B9A\u81F3{0}\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u7ED1\u5B9A
shipment.data.is.empty=\u88C5\u8FD0\u6570\u636E\u4E3A\u7A7A
entity.is.is.empty=\u5B9E\u4F53ID\u4E3A\u7A7A
cannot.find.system.user=\u627E\u4E0D\u5230\u7CFB\u7EDF\u7528\u6237
demand.note.number.is.empty=\u9700\u6C42\u5355\u53F7\u4E3A\u7A7A
item.code.is.null=\u672A\u4F20\u5165\u6599\u5355\u4EE3\u7801
id.is.null=id\u4E3A\u7A7A
Create.the.SmtSnMtlTracingT.table=\u521B\u5EFASmtSnMtlTracingT\u5206\u8868\uFF1A
exception_info_fo_failure=\u5931\u8D25\uFF01\u5F02\u5E38\u4FE1\u606F\u4E3A\uFF1A
create_smtsnmtltracingt_table_by_quarter=\u6309\u5E74\u5B63\u5EA6\u521B\u5EFASmtSnMtlTracingT\u5206\u8868
must_specify_time=\u5FC5\u987B\u6307\u5B9A\u626B\u63CF\u5F00\u59CB\u65F6\u95F4\u548C\u7ED3\u675F\u65F6\u95F4
scan_start_time_cannot_later=\u626B\u63CF\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u665A\u4E8E\u626B\u63CF\u7ED3\u675F\u65F6\u95F4
primary.key.not.null=\u4E3B\u952E\u5FC5\u987B\u6709\u503C
factory_no_material_traceability=\u5DE5\u5382{0}\u8FD8\u6CA1\u6709\u914D\u7F6E\u7269\u6599\u8FFD\u6EAF\u529F\u80FD
search_result_is_null=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A

search_bom_detail_id_fail=\u67E5\u8BE2\u4E0A\u6599\u8868id\u5931\u8D25
query_upn_failed=\u67E5\u8BE2\u5DE5\u5355\u5BF9\u5E94uph\u5931\u8D25
available.quantity.query.failed=\u53EF\u7528\u6570\u91CF\u67E5\u8BE2\u5931\u8D25
querying.standard.usage.failed=\u67E5\u8BE2\u6807\u51C6\u7528\u91CF\u5931\u8D25
process.name.error=\u5DE5\u5E8F\u540D\u683C\u5F0F\u9519\u8BEF
corresponding.process.information.not.found=\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5DE5\u5E8F\u4FE1\u606F
wip.extendinfo.table.save.failed=\u5728\u5236\u6269\u5C55\u8BA4\u8BC1\u8868\u4FDD\u5B58\u5931\u8D25
wip.extendinfo.table.update.failed=\u5728\u5236\u6269\u5C55\u8BA4\u8BC1\u8868\u66F4\u65B0\u5931\u8D25
tracking.or.task.no.not.exist=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7/\u4EFB\u52A1\u53F7\u4E0D\u5B58\u5728\uFF01
routes.in.batch.not.stored.in.warehouse=\u8BE5\u6279\u6B21\u7684Route\u6CA1\u6709\u5165\u5E93\uFF0C\u4E0D\u9700\u8981\u5165\u5E93
wait_in_warehouse_not_exist=\u4E0D\u5B58\u5728\u5F85\u5165\u5E93\u6E05\u5355
warehousing.failed=\u5165\u5E93\u5931\u8D25\uFF01
organization.id.not.match=\u8BE5\u4EFB\u52A1\u7684\u7EC4\u7EC7ID\u4E0E\u5B50\u5E93\u5B58\u7684\u7EC4\u7EC7id\u4E0D\u4E00\u81F4\uFF1B\u8BF7\u68C0\u67E5\uFF0C\u8C22\u8C22\uFF01
task.org.id.not.match=\u4EFB\u52A1{0}\u7EC4\u7EC7ID\u4E0E\u5B50\u5E93\u5B58\u7EC4\u7EC7id {1}\u4E0D\u4E00\u81F4\u3002\u8BF7\u68C0\u67E5
no_sup_step_task=\u6DF7\u4EFB\u52A1\u63D0\u4EA4\u4EC5\u652F\u6301\u6807\u6A21\u4EFB\u52A1

task.id.not.exist=\u8BE5\u4EFB\u52A1\u7684\u539F\u59CBtaskId\u4E0D\u5B58\u5728

task.not.query.requirements=\u4EFB\u52A1\u6CA1\u6709\u67E5\u8BE2\u5230\u5BF9\u5E94\u7684\u9700\u6C42

task_org_id_is_null=\u4EFB\u52A1\u7EC4\u7EC7ID\u4E3A\u7A7A
task_id_is_null=\u4EFB\u52A1ID\u4E3A\u7A7A
tasks.qty.is_zero=\u4EFB\u52A1\u603B\u6570\u4E3A0
cannot.exceed.total.number=\u5DF2\u63D0\u4EA4\u6570\u91CF\u4E0E\u672C\u6B21\u63D0\u4EA4\u6570\u91CF\u4E4B\u548C\u4E0D\u80FD\u8D85\u8FC7\u4EFB\u52A1\u603B\u6570
item.code.not.enough.sent=\u4EFB\u52A1\u53F7{0}\u7269\u6599\u4EE3\u7801{1}\u5DF2\u53D1\u6570\u4E0D\u8DB3\uFF0C\u4E0D\u80FD\u5B8C\u5DE5\uFF0C\u672C\u6B21\u63D0\u4EA4\u5931\u8D25
imu_update_failed=imu\u66F4\u65B0\u5931\u8D25

in.warehousing.succeed=\u5165\u5E93\u6210\u529F\uFF01

not.warehouse.in.or.out=\u8BE5\u5355\u677F\u6761\u7801{0}\u4E0D\u662F\u51FA\u5E93\u72B6\u6001\uFF01
no_warehouse_in=\u672A\u5165\u5E93
sn.summit.succeed=\u63D0\u4EA4\u626B\u63CF\u6761\u7801\u6210\u529F
sn.must.start.seven=\u6761\u7801\u5FC5\u987B\u662F\u4EE57\u5F00\u5934\u768412\u4F4D\u6570\u5B57
scan_succeed=\u626B\u63CF\u6210\u529F!
sn.in.process.workstation=\u8BE5\u6761\u7801\u5728\u5B50\u5DE5\u5E8F:{0} ,\u5DE5\u7AD9:{1} ,\u8BF7\u786E\u8BA4!
routeId.is.empty=routeId\u4E3A\u7A7A\uFF01
routeDetail_info_not_found=\u6CA1\u6709\u67E5\u5230routeDetail\u4FE1\u606F\uFF01
no.need.to.bind.materials=\u4E0D\u9700\u7ED1\u5B9A\u7269\u6599
daughter.card.binding.verification.passed=\u5B50\u6BCD\u5361\u7ED1\u5B9A\u6821\u9A8C\u901A\u8FC7
daughter.mother.card.binding.verification.failed=\u5B50\u6BCD\u5361\u7ED1\u5B9A\u6821\u9A8C\u5931\u8D25\uFF01\u5DE5\u5E8F{0} \u9700\u8981\u7ED1\u5B9A\u7269\u6599{1}\u4E2A\uFF0C\u8FD8\u5DEE{2}\u4E2A
sn_not_returned=\u6761\u7801\u672A\u8FD4\u8FD8
sn_does_not_exist_repair=\u6761\u7801\u4E0D\u5B58\u5728\u9001\u4FEE\u8BB0\u5F55
sn_is_repair_two=\u6761\u7801\u5728\u4E8C\u7EF4\u9700\u8981\u5148\u9001\u4FEE
sn_is_repaira_succeed=\u6761\u7801\u5DF2\u7ECF\u7EF4\u4FEE\u5B8C\u6210\uFF01
sn.previous.process.not.started=\u4E0A\u4E00\u5DE5\u5E8F\u8FD8\u672A\u5F00\u5DE5\uFF0C\u8BF7\u5148\u6267\u884C\u4E0A\u4E00\u5DE5\u5E8F
wrok.order.status.is.not.pending.commited=\u5F53\u524D\u6307\u4EE4\u72B6\u6001\u4E0D\u662F\u6302\u8D77\u6216\u5DF2\u63D0\u4EA4
related.line.exist.started.work.order=\u5173\u8054\u7EBF\u4F53\uFF1A{0} \u5B58\u5728\u5DF2\u5F00\u5DE5\u6307\u4EE4\uFF0C\u8BF7\u5148\u5B8C\u5DE5
work_order_smt_info_is_null=\u6307\u4EE4SMT\u4FE1\u606F\u4E3A\u7A7A
mount.info.is.null=\u4E0A\u6599\u4FE1\u606F\u4E3A\u7A7A\uFF0C\u8BF7\u5148\u5BFC\u5165\u4E0A\u6599\u8868
previous.process.not.exist=\u4E0A\u4E00\u5DE5\u5E8F\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\u5DE5\u5E8F\u4FE1\u606F
remark.info.maintenance.exception=remark\u4FE1\u606F\u7EF4\u62A4\u5F02\u5E38
get.work.order.info.failed=\u901A\u8FC7\u6765\u6E90\u4EFB\u52A1\u83B7\u53D6\u6307\u4EE4\u4FE1\u606F\u5931\u8D25
call.service.failed=\u8C03\u7528\u670D\u52A1\u5931\u8D25
work.order.maintenance.info.not.found=\u627E\u4E0D\u5230\u6307\u4EE4\u4EA7\u80FD\u7EF4\u62A4\u4FE1\u606F
min.num.of.work.order.is.one=\u6307\u4EE4\u6570\u91CF\u6700\u5C0F\u4E3A1
minimum.output.is.zero=\u4EA7\u51FA\u91CF\u6700\u5C0F\u4E3A0
workOrder.qty.cannot.less.output.qty=\u6307\u4EE4\u6570\u91CF\u503C\u4E0D\u80FD\u5C0F\u4E8E\u4EA7\u51FA\u91CF
interface.returned.data.exception=\u63A5\u53E3\u8FD4\u56DE\u6570\u636E\u5F02\u5E38
mount_info_not_found=\u672A\u627E\u5230\u4E0A\u6599\u4FE1\u606F\uFF01
mount_save_failed=\u4FDD\u5B58\u4E0A\u6599\u4FE1\u606F\u5931\u8D25\uFF0C\u53D1\u751F\u5F02\u5E38!
material.already.exists.station=\u8BE5\u7AD9\u4F4D\u5DF2\u7ECF\u5B58\u5728\u7269\u6599\uFF0C\u8BF7\u66F4\u6362\uFF01
mount_update_failed=\u66F4\u65B0\u4E0A\u6599\u4FE1\u606F\u5931\u8D25\uFF0C\u53D1\u751F\u5F02\u5E38!
module.detail.not.found=\u672A\u627E\u5230\u6A21\u7EC4\u4E0A\u6599\u8BE6\u60C5\u4FE1\u606F\uFF01
material.feeder.info.not.found=\u672A\u627E\u5230\u4E0A\u6599Feeder\u7ED1\u5B9A\u4FE1\u606F\uFF01
source.cannot.be.empty=\u6765\u6E90\u4E0D\u80FD\u4E3A\u7A7A;
import.start.end.time.cannot.null=\u5BFC\u5165\u5F00\u59CB\u65F6\u95F4\u548C\u622A\u6B62\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A;
time.format.is.wrong=\u65F6\u95F4\u683C\u5F0F\u9519\u8BEF!
reel.id.is.wrong=Reel ID\u6709\u8BEF
reel.id.verify.succeed=Reel ID\u6821\u9A8C\u901A\u8FC7
operation.pk.code.exception=\u64CD\u4F5CPK\u7801\u4FE1\u606F\u53D1\u751F\u5F02\u5E38
export_data_exception=\u5BFC\u51FA\u6570\u636E\u5F02\u5E38

scheduled.task.executed.successfully=\u5B9A\u65F6\u63A8\u9001\u4EFB\u52A1\u6267\u884C\u6210\u529F
scheduled.task.executed.failed=\u5B9A\u65F6\u63A8\u9001\u4EFB\u52A1\u6267\u884C\u5931\u8D25
summit_successfully=\u63D0\u4EA4\u6210\u529F
batch.over.station.scan.successful=\u6279\u91CF\u8FC7\u7AD9\u626B\u63CF\u6210\u529F
data.is.precessed=\u6570\u636E\u5DF2\u5904\u7406\uFF0C\u5177\u4F53\u60C5\u51B5\u8BF7\u770B\u53F3\u4E0B\u89D2\u8868\u683C\uFF01
batch.over.station.scan.failed=\u6279\u91CF\u8FC7\u7AD9\u626B\u63CF\u5931\u8D25\uFF0C\u8BF7\u67E5\u770B\u6279\u91CF\u8FC7\u7AD9\u626B\u63CF\u8BB0\u5F55\uFF01
please.enter.the.correct.barcode=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u6761\u7801\u4FE1\u606F
formSn.bind.error=\u5DE5\u88C5\u7ED1\u5B9A\u53D1\u751F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
Sporadic.formSn.bind.error=\u96F6\u661F\u5DE5\u88C5\u7ED1\u5B9A\u53D1\u751F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
sn.contral.verify.success=\u6761\u7801\u7BA1\u63A7\u6821\u9A8C\u901A\u8FC7
call_successfully=\u8C03\u7528\u6210\u529F

box.is.used=\u53D1\u8FD0\u671F\u95F4\u7BB1\u5B50\u5DF2\u7ECF\u88AB\u5176\u5B83\u9700\u6C42\u5355\u4F7F\u7528
pda.boxed.unknown.error=PDA\u5DE5\u5E8F\u6309\u7BB1\u88C5\u8FD0\u53D1\u751F\u672A\u77E5\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D
email.maintenance.information.not.found=\u67E5\u8BE2\u4E0D\u5230\u90AE\u4EF6\u7EF4\u62A4\u4FE1\u606F\uFF01
Failed.get.email.maintenance.info=\u83B7\u53D6SMT\u626B\u63CF\u7387\u7684\u90AE\u4EF6\u7EF4\u62A4\u4FE1\u606F\u5931\u8D25\uFF01
deal.feeder.bind.info.error=\u5904\u7406\u5907\u6599Feeder\u7ED1\u5B9A\u4FE1\u606F\u53D1\u751F\u5F02\u5E38
update.data.failed.conversion.strategy.three=\u5728\u6267\u884C\u8F6C\u4EA7\u7B56\u75653\uFF0C\u4E0A\u6599\u8868id\u4E0D\u540C\u7684\u573A\u666F\uFF0C\u66F4\u65B0\u6570\u636E\u5931\u8D25
save_mount_history_data_error=\u4FDD\u5B58\u4E0A\u6599\u5386\u53F2\u660E\u7EC6\u4FE1\u606F\u6570\u636E\u53D1\u751F\u5F02\u5E38
generating.excel.attention.mail=\u6B63\u5728\u751F\u6210excel\uFF0C\u8BF7\u7559\u610F\u90AE\u4EF6
qty.to.stored.not.enough=\u5F85\u5165\u5E93\u6570\u91CF\u4E0D\u8DB3
document.no.is.existed=The document number already exists!
file_import_verify_failed=\u6587\u4EF6\u4E0A\u4F20\u6821\u9A8C\u5931\u8D25
file_import_failed=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
failed.to.save.file.data=\u4FDD\u5B58\u6587\u4EF6\u6570\u636E\u5931\u8D25
failed.to.get.file.token=\u83B7\u53D6\u6587\u4EF6token\u5931\u8D25

sn.too.bigger=\u626B\u63CF\u7684\u6761\u7801\u5E8F\u53F7\u5927\u4E8E\u6307\u4EE4\u8BBE\u7F6E\u7684\u4EA7\u51FA\u6570\u91CF\uFF0C\u8BF7\u66F4\u6362\u6761\u7801
get.lpn.error=\u6839\u636EREELID\u6216\u5BB9\u5668\u4EE3\u7801\u67E5\u8BE2\u5BB9\u5668\u4FE1\u606F\u5F02\u5E38

get.lpn.null=\u5BB9\u5668\u7801\u4E0D\u5B58\u5728
get.pkcode.null=\u672A\u67E5\u8BE2\u5230REELID\u4FE1\u606F

bom.contents.code.qty.error=\u83B7\u53D6\u88C5\u7BB1\u6570\u91CF\u5931\u8D25
bom.itemcode.less=\u8BF7\u626B\u63CF\u7269\u6599\u4EE3\u7801\u3010{0}\u3011\u7684\u6599\u76D8\uFF01
box.contents.code.qty.less=\u539F\u6750\u6599\u5BB9\u5668\u4E2D\u7269\u6599\u3010{0}\u3011\u6570\u91CF\u4E0D\u591F
bom.itemcode.then=\u7269\u6599\u4EE3\u7801\u3010{0}\u3011\u7684\u7269\u6599\u4E0D\u5728\u4E0A\u6599\u8868\u4E2D!
box.contents.update.error=\u7BB1\u5185\u5BB9\u66F4\u65B0\u5931\u8D25
get.sub.bom.info.error=\u83B7\u53D6\u6574\u673A\u5B50\u5361\u6570\u636E\u5F02\u5E38
material.qty.greater.bom.usage=\u7269\u6599\u4EE3\u7801\u7528\u91CF\u5927\u4E8Ebom\u7528\u91CF\uFF0C\u8BF7\u4FEE\u6539\u5B50\u5DE5\u5E8F\u5C5E\u6027
item_code_not_exist_bom_list=\u7269\u6599\u4EE3\u7801\u4E0D\u5728bom\u6E05\u5355
dip_scan_finished=\u8BE5\u6761\u7801DIP\u8FC7\u677F\u626B\u63CF\u5DF2\u5B8C\u6210\uFF0C\u8BF7\u786E\u8BA4
greater.than.bom.qty=\u4E0A\u6599\u8868\u6570\u91CF+\u5DF2\u6263\u6570\u6570\u91CF+\u63D2\u4EF6\u6570\u91CF \u5927\u4E8EBOM\u6E05\u5355\u7528\u91CF\uFF0C\u8BF7\u786E\u8BA4
worker_station_cannot_be_empty=\u5DE5\u7AD9\u4E0D\u80FD\u4E3A\u7A7A

item.sn.is.null=\u7269\u6599\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
is.control.is.null=\u662F\u5426\u7BA1\u63A7\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
get.item.code.null=\u7CFB\u7EDF\u65E0\u6CD5\u83B7\u5F97\u7269\u6599\u6761\u7801\u7684\u7269\u6599\u4FE1\u606F\uFF0C\u8BF7\u4ED4\u7EC6\u6838\u5B9E\u7269\u6599\u662F\u5426\u7B26\u5408\u8981\u6C42
get.ms.item.code.ptp.error=\u83B7\u53D6\u7BA1\u63A7\u7269\u6599\u6761\u7801\u7684\u7269\u6599\u4EE3\u7801\u5F02\u5E38
get.item.code.not.consistent=\u7269\u6599\u6761\u7801\u7684\u7269\u6599\u4EE3\u7801\u548C\u8F93\u5165\u7684\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u4ED4\u7EC6\u68C0\u67E5

replace.sn.is.null=\u66F4\u6362\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
get.time.range.null=\u672A\u67E5\u8BE2\u5230\u6761\u7801\u7684\u626B\u63CF\u5386\u53F2\u4FE1\u606F\uFF0C\u65E0\u6CD5\u83B7\u53D6\u8FFD\u6EAF\u7684\u67E5\u8BE2\u7684\u8D77\u59CB\u548C\u7EC8\u6B62\u65F6\u95F4
get.item.sn.null=\u672A\u67E5\u8BE2\u5230\u7269\u6599\u6761\u7801\u4FE1\u606F
item.code.null=\u7269\u6599\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
get.item.sn.pk.null=\u7269\u6599\u6761\u7801\u672A\u6CE8\u518C

get.replace.sn.pk.null=\u66F4\u6362\u6761\u7801\u672A\u6CE8\u518C
not.control.sn.env.fail=\u975E\u7BA1\u63A7\u7269\u6599\u73AF\u4FDD\u5C5E\u6027\u6821\u9A8C\u4E0D\u901A\u8FC7
get.contr.reelidsn.null=\u672A\u5728\u7BA1\u63A7\u8868\u67E5\u8BE2\u5230\u7269\u6599\u6761\u7801\u7684REELID\u6761\u7801\u4FE1\u606F
replace.sn.same=\u66F4\u6362\u6761\u7801\u4E0D\u80FD\u548C\u7269\u6599\u6761\u7801\u76F8\u540C
get.zj.env.error=\u83B7\u53D6\u7684\u73AF\u4FDD\u5C5E\u6027\u63A5\u53E3\u5F02\u5E38

get.itemsn.env.null=\u672A\u83B7\u53D6\u5230\u6761\u7801\u7684\u73AF\u4FDD\u5C5E\u6027
get.replacesn.env.null=\u672A\u83B7\u53D6\u5230\u66F4\u6362\u6761\u7801\u7684\u73AF\u4FDD\u5C5E\u6027
replacesn.env.fail=\u66F4\u6362\u6761\u7801\u7684\u73AF\u4FDD\u5C5E\u6027\u6821\u9A8C\u4E0D\u901A\u8FC7
get.style.null=\u672A\u83B7\u53D6\u5230REELID\u7684\u578B\u53F7\u4FE1\u606F
get.prodplan.null=\u672A\u83B7\u53D6\u5230REELID\u7684\u6599\u5355\u4EE3\u7801

sn.compare.error=\u6761\u7801{0}\u5F53\u524D\u5DE5\u5E8F\u4E3A{1}\uFF0C\u8BF7\u786E\u8BA4\u6761\u7801\u662F\u5426\u5DF2\u7ECF\u8F6C\u4EA4

sn.exceed.99999=\u6761\u7801\u7F16\u53F7\u4E0D\u80FD\u8D85\u8FC799999
task.of.prdoplanid.not.exist=\u6279\u6B21 {0} \u5BF9\u5E94\u4EFB\u52A1\u4E0D\u5B58\u5728
sn_no_last_process=\u6761\u7801\u672A\u7ED3\u5B58\u5728\u6700\u540E\u5DE5\u5E8F\uFF1A{0}
sn_submitted=\u6761\u7801\u5DF2\u63D0\u4EA4\u5165\u5E93\u5355:{0}
item.no.of.prdoplanid.not.exist=\u6761\u7801{0}\u6279\u6B21 {1} \u5BF9\u5E94\u7684\u6599\u5355\u4E0D\u5B58\u5728
workorder.of.prdoplanid.not.exist=\u6279\u6B21 {0} \u5BF9\u5E94\u6307\u4EE4\u4E0D\u5B58\u5728\u3002\u8BF7\u5148\u8FDB\u884C\u6307\u4EE4\u62C6\u5206
workorder.of.prdoplanid.data.error=\u6279\u6B21 {0} \u5BF9\u5E94\u6307\u4EE4\u6570\u636E\u9519\u8BEF
workorder.not.draw.up=\u6307\u4EE4 {0} \u672A\u6392\u4EA7
sn.total.count.exceed.taskqty=\u6761\u7801\u6CE8\u518C\u603B\u6570 [{0}] \u8D85\u8FC7\u4EFB\u52A1\u603B\u6570 [{1}]
ct.route.detail.of.itemno.not.exist=\u7269\u6599\u4EE3\u7801 {0} \u5BF9\u5E94\u5DE5\u827A\u8DEF\u5F84\u4FE1\u606F\u4E0D\u5B58\u5728
sn.between.range.exist={0} - {1} \u8303\u56F4\u5185\u5DF2\u5B58\u5728\u6761\u7801\u3002\u8BF7\u68C0\u67E5
printqty.can.not.bigger.than.taskqty=\u6761\u7801\u751F\u6210\u6570\u91CF\u5927\u4E8E\u4EFB\u52A1\u6570\u91CF\u3002\u8BF7\u68C0\u67E5

sn.is.returned=\u8BE5\u6761\u7801\u5DF2\u8FD4\u8FD8\uFF0C\u8BF7\u786E\u8BA4
get.zs.workstation.relationship.failed=\u83B7\u53D6\u4E2D\u8BD5\u5BF9\u63A5\u5DE5\u7AD9\u4E0Eimes\u5B50\u5DE5\u5E8F\u3001\u5DE5\u7AD9\u5BF9\u5E94\u5173\u7CFB\u5931\u8D25
sn.has.no.scan.record.in.workstation=\u8BE5\u6761\u7801\u5728{0}\u5DE5\u7AD9\u65E0\u626B\u63CF\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4
failed.to.get.scan.history={0}\u83B7\u53D6\u626B\u63CF\u5386\u53F2\u8BB0\u5F55\u5931\u8D25
failed.to.get.zs.processCode.list=\u83B7\u53D6\u4E2D\u8BD5\u5BF9\u63A5\u5B50\u5DE5\u5E8Flist\u5931\u8D25
call.zs.service.failed=\u8C03\u4E2D\u8BD5\u670D\u52A1\u5931\u8D25
failed.to.bind.for.zs=\u8C03\u4E2D\u8BD5\u7ED1\u5B9A\u5931\u8D25

failed.to.get.assemblyrelation=\u83B7\u53D6\u4E2D\u8BD5\u88C5\u914D\u5173\u7CFB\u5931\u8D25

failed.to.get.aps.stock=\u83B7\u53D6APS\u7CFB\u7EDF\u4E2D\u7684\u4EA4\u8D27\u4ED3\u5931\u8D25
sn.record.not.found=\u672A\u627E\u5230{0}\u6761\u7801\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4
get.re.work.sn.max.num.failed=\u83B7\u53D6\u8FD4\u5DE5\u6761\u7801\u6700\u5927\u9650\u5236\u6761\u6570\u5931\u8D25
exceeded.max.num.of.rework.sn=\u8D85\u8FC7\u6700\u5927\u9650\u5236\u4E2A\u6570{0}\uFF0C\u8BF7\u786E\u8BA4
barcode.material.code.inconsistent={0}\u6761\u7801\u7269\u6599\u4EE3\u7801\u548C{1}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
barcode.task.no.inconsistent={0}\u6761\u7801\u539F\u4EFB\u52A1\u548C{1}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
reelId.not.register=reelId\uFF1A{0}\u672A\u6CE8\u518C\uFF0C\u8BF7\u786E\u8BA4
date.range.is.null=\u63D0\u53D6\u65E5\u671F\u548C\u4EFB\u52A1\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
sn.work.station=\u6761\u7801 {0} \u5DE5\u7AD9\u4E0D\u662F\u5728\u5E93
sn.can.not.empty=\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
sn.all.not.found=\u6240\u6709\u6761\u90FD\u4E0D\u5728\u5728\u5236\u8868\u4E2D
sn.process.code=\u6761\u7801 {0} \u5B50\u5DE5\u5E8F\u4E0D\u662F\u5165\u5E93
sn.no.wip=\u6761\u7801 {0} \u4E0D\u5728\u5728\u5236\u8868\u4E2D
lookup.6001.empty=\u5355\u677F\u7EDF\u8BA1\u65E5\u62A5\u6570\u636E\u5B57\u6BB5\u914D\u7F6E\uFF086001\uFF09\u4E3A\u7A7A
lookup.6008.empty=\u6574\u673A\u7EDF\u8BA1\u65E5\u62A5\u6570\u636E\u5B57\u5178\u914D\u7F6E\uFF086008\uFF09\u4E3A\u7A7A
lookup.221103.empty=\u6574\u673A\u5DE5\u5E8F\uFF08221103\uFF09\u4E3A\u7A7A
export.data.more.than=\u5BFC\u51FA\u6570\u636E\u91CF\u5927\u4E8E {0}\uFF0C\u8BF7\u7F29\u5C0F\u67E5\u8BE2\u8303\u56F4
date.range.too.long=\u67E5\u8BE2\u65F6\u95F4\u533A\u95F4\u592A\u957F
date.range.is.empty=\u67E5\u8BE2\u65F6\u95F4\u533A\u95F4\u4E3A\u7A7A

sn.not.in.bill=\u6761\u7801{0}\u4E0D\u5728\u8BE5\u5165\u5E93\u5355\u4E2D\u6216\u6761\u7801\u72B6\u6001\u4E0D\u662F\u5DF2\u63D0\u4EA4
bill.can.not.close=\u5355\u636E\uFF1A{0}\u4E2D\u4E0D\u5B58\u5728\u63A5\u6536\u7684\u6761\u7801\uFF0C\u4E0D\u80FD\u5173\u95ED\u5355\u636E

box.is.null=\u7BB1\u53F7\u4E3A\u7A7A
box.content.is.null=\u5355\u636E\u4E2D\u4E0D\u5B58\u5728\u5DF2\u63D0\u4EA4\u7684\u6761\u7801\u5728\u8BE5\u5BB9\u5668\u4E2D
box.content.not.all.in.bill=\u64CD\u4F5C\u5931\u8D25\uFF0C\u7BB1\u4E2D\u6761\u7801\u5B58\u5728\u4E0D\u5728\u5355\u636E\u53F7\u4E2D\u7684\u6761\u7801
bill.be.scan.receive=\u5355\u636E{0}\u88AB\u626B\u63CF\u63A5\u6536\u4E86\uFF0C\u4E0D\u80FD\u518D\u4F7F\u7528\u63A5\u6536\u529F\u80FD
bill.can.not.reject=\u5355\u636E{0}\u88AB\u626B\u63CF\u63A5\u6536\u4E86\uFF0C\u4E0D\u80FD\u518D\u4F7F\u7528\u62D2\u7EDD\u529F\u80FD

board.scan.code.list.to.be.bound.is.null=\u5355\u677F\u626B\u63CF\u9700\u7ED1\u5B9A\u4EE3\u7801\u6E05\u5355\u4E3A\u7A7A
sn.is.not.exists.in.table.wipinfo=\u6761\u7801\u5728\u6761\u7801\u8868\u4E2D\u4E0D\u5B58\u5728
main.sn.is.binded.completed=\u4E3B\u6761\u7801\u5DF2\u7ED1\u5B9A\u5B8C\u6210
sub.sn.is.binded=\u5B50\u6761\u7801\u5DF2\u7ED1\u5B9A\u5728\u4E3B\u6761\u7801{0}\u4E0A
material.code.corresponding.to.the.sub-bar.code.is.not.in.the.material.list.to.be.bound=\u5B50\u6761\u7801\u5BF9\u5E94\u7269\u6599\u4EE3\u7801\u4E0D\u5728\u9700\u7ED1\u5B9A\u7269\u6599\u6E05\u5355\u4E2D
whether.data.dictionary.information.is.controlled.by.batch.without.parent.cards=\u6CA1\u6709\u5B50\u6BCD\u5361\u662F\u5426\u6309\u6279\u6B21\u7BA1\u63A7\u7684\u6570\u636E\u5B57\u5178\u4FE1\u606F
the.batch.to.which.the.sub-bar.code.belongs.is.bound.to.the.batch.of.the.master.bar.code=\u5B50\u6761\u7801\u6240\u5C5E\u6279\u6B21\u548C\u4E3B\u6761\u7801\u6279\u6B21\u4E0D\u5B58\u5728\u7ED1\u5B9A\u5173\u7CFB
itemCode.is.binded.complete=\u7269\u6599{0}\u5DF2\u7ED1\u5B9A\u5B8C\u6210
the.previous.subcode.is.not.bound.successfully=\u5F53\u524D\u5B50\u6761\u7801\u672A\u7ED1\u5B9A\u6210\u529F
material.code.corresponding.to.the.main.code.is.not.in.the.material.list.to.be.bound=\u4E3B\u6761\u7801\u5BF9\u5E94\u7269\u6599\u4EE3\u7801\u4E0D\u5728\u9700\u7ED1\u5B9A\u7269\u6599\u6E05\u5355\u4E2D
sn.not.exist.wip.info=\u6761\u7801\u4E0D\u5B58\u5728
please.start.the.order.first=\u8BF7\u5148\u5C06{0}\u6307\u4EE4\u5F00\u5DE5
main.sn.is.not.belongs.to.this.prodplanid = \u4E3B\u6761\u7801\u4E0D\u662F\u8BE5\u6279\u6B21\u7684
cannot.reason.not.blank=\u4E0D\u80FD\u7ED1\u5B9A\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
main.sn.is.not.belongs.to.this.workroderno=\u4E3B\u6761\u7801\u4E0D\u662F\u8BE5\u6307\u4EE4\u7684
production.not.match.plan=\u6599\u5355\u4EE3\u7801\u548C\u6279\u6B21\u4E0D\u5339\u914D

barcode.generate.error=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u751F\u6210\u6761\u7801\u5F02\u5E38! {0}
update.aps.qty.error=\u8C03\u7528APS\u66F4\u65B0\u534A\u6210\u54C1\u5165\u5E93\u6570\u91CF\u5F02\u5E38\uFF01\u63A5\u6536id: {0}

sn.has.been.storaged=\u6761\u7801{0}\u5DF2\u5165\u5E93\uFF0C\u8BF7\u786E\u8BA4
requirementinfo.not.allow.empty=\u7269\u6599\u4EE3\u7801\u548C\u73AF\u4FDD\u5C5E\u6027\u4E0D\u5141\u8BB8\u4E3A\u7A7A
actual.stock.qty.inconsistent.submitted.quantity=\u5165\u5E93\u5B9E\u9645\u6570\u91CF\u548C\u63D0\u4EA4\u6570\u91CF\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
get.erp.move.qty.error=\u4EFB\u52A1{0}\u83B7\u53D6erp\u53EF\u79FB\u52A8\u6570\u91CF\u5F02\u5E38
current.status.not.allow.null=\u5F53\u524D\u72B6\u6001\u4E0D\u5141\u8BB8\u64CD\u4F5C\uFF0C\u8BF7\u68C0\u67E5\uFF0C\u5355\u53F7\uFF1A{0}
warehouseEntryInfo.stockType.is.null=\u8BE5\u5165\u5E93\u5355\u4ED3\u5E93\u7C7B\u578B\u4E3A\u7A7A, \u5165\u5E93\u5355\u53F7\uFF1A{0}
locator.is.null= ERP\u8D27\u4F4D\u5BF9\u5E94ID\u4E3A\u7A7A
req.bill.no.is.null=\u65E0\u6548\u7684\u9700\u6C42\u5355\u53F7\u6216\u9700\u6C42\u6570\u91CF
commit.qty.morethan.max=\u63D0\u4EA4\u6570\u91CF\u5927\u4E8E\u9700\u6C42\u5355\u6700\u5927\u53EF\u5165\u5E93\u6570\u91CF
req.bill.is.lock=\u8BE5\u9700\u6C42\u5355\u5DF2\u88AB\u9501\u5B9A
pallet.number.box.information.is.not.empty=\u6808\u677F\u53F7\u7BB1\u5185\u5BB9\u4FE1\u606F\u672A\u7A7A
warehouse.mode.req.empty=\u6574\u673A\u5165\u5E93\u5355\u7684\u9700\u6C42\u5355\u4E0D\u80FD\u4E3A\u7A7A
warehouseentryinfo.is.locked=\u5165\u5E93\u5355\u5DF2\u88AB\u9501\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u64CD\u4F5C\uFF0C\u5355\u53F7\uFF1A{0}
no.fallback.privilege=\u7528\u6237\u6CA1\u6709\u56DE\u9000\u6743\u9650
process.fallback.is.not.support=\u8BE5\u5DE5\u5E8F\u4E0D\u652F\u6301\u56DE\u9000
no.reject.permission=\u5F53\u524D\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650
sn.is.repeat=\u4EE5\u4E0B\u6761\u7801\u91CD\u590D\uFF0C\u8BF7\u786E\u8BA4{0}\uFF01
transfer.no.operation = {0}\u4EFB\u52A1\u65E0\u9700\u8FDB\u884C\u8F6C\u6B63\u64CD\u4F5C!
transfer.barcode.verification={0}\u6761\u7801\u5DF2\u7ECF\u63D0\u4EA4\u6216\u8005\u6B63\u5728\u8F6C\u6B63\u4E2D,\u8BF7\u91CD\u65B0\u5237\u65B0\u4EFB\u52A1!
transfer.barcode.length.verification=\u4E00\u6B21\u6700\u591A\u5141\u8BB8\u63D0\u4EA4{0}\u4E2A\u6761\u7801\u6761\u7801!
transfer.sigle.stock.verification=\u5F53\u524D\u4EFB\u52A1\u7684\u6E90\u4ED3\u5E93\u6570\u636E\u9700\u4FDD\u6301\u4E00\u81F4, \u8BF7\u68C0\u67E5\uFF01
file.empty=\u6587\u4EF6\u4E3A\u7A7A
params.err=\u8F93\u5165\u53C2\u6570\u4E0D\u6B63\u786E
file.init.fail=\u6587\u4EF6\u521D\u59CB\u5316\u5931\u8D25
receptionBy.is.null =\u6761\u7801\u7684\u63A5\u6536\u4EBA\u4E3A\u7A7A:
returnedBy.is.null =\u6761\u7801\u7684\u8FD4\u8FD8\u4EBA\u4E3A\u7A7A:
crafsection.is.null =\u6761\u7801\u7684\u4E3B\u5DE5\u5E8F\u4E3A\u7A7A:
sub.crafsection.is.null =\u6761\u7801\u7684\u5B50\u5DE5\u5E8F\u4E3A\u7A7A:
sn.is.null.param=\u6761\u7801\u4E3A\u7A7A:
sn.was.to.be.repaired=\u6761\u7801\u5DF2\u9001\u4FEE:
only.upload.file.like.xls.xlsx.csv = \u4EC5\u5141\u8BB8\u4E0A\u4F20xls\u3001xlsx\u3001csv\u7C7B\u578B\u6587\u4EF6
sn.is.not.unlock = \u6761\u7801{0}\u672A\u89E3\u9501
sns.is.null=\u7B2C{0}\u4E2A\u6761\u7801\u4E3A\u7A7A
last.operation.not.found=\u672A\u627E\u5230\u6700\u540E\u4E00\u9053\u5DE5\u5E8F\uFF0CrouteId\uFF1A{0}
never.find.storag.name = \u672A\u67E5\u5230\u4ED3\u5E93\u540D\u79F0
sn.process.cannot.be.repair=\u6761\u7801{0}\u5B50\u5DE5\u5E8F\u4E0D\u5141\u8BB8\u9001\u4FEE\uFF0C\u8BF7\u786E\u8BA4!
warehouse.billNo.is.null=\u5165\u5E93\u5355\u636E\u53F7\u4E3A\u7A7A
warehouse.billNo.not.exist=\u5165\u5E93\u5355\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
warehouse.billNo.isReceived=\u8BE5\u5165\u5E93\u5355\u5DF2\u63A5\u6536\uFF0C\u8BF7\u786E\u8BA4!
lpn.no.warhouse.record=\u5BB9\u5668{0}\u65E0\u63D0\u4EA4\u5165\u5E93\u8BB0\u5F55\uFF0C\u8BF7\u5148\u63D0\u4EA4\u751F\u4EA7\u5165\u5E93\u5355\uFF01
lpn.status.is.not.in.storage=\u5BB9\u5668{0}\u72B6\u6001\u4E0D\u4E3A\u5F85\u5165\u5E93\uFF0C\u8BF7\u786E\u8BA4\uFF01
transfer.warhouse.failed=\u79FB\u5E93\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4\uFF01 \u9519\u8BEF\u4FE1\u606F:{0}
erp.machine.move.or.complete.failed=erp\u6574\u673A\u79FB\u52A8\u6216\u5B8C\u5DE5\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4\uFF01 \u9519\u8BEF\u4FE1\u606F:{0}
sn.workOrderNo.is.null=\u6761\u7801{0}\uFF1A\u6307\u4EE4\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5\uFF01
reelId.empty=reelId\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
reelId.no.smt.return=Reel ID\uFF1A{0}\u65E0\u9000\u6599\u4FE1\u606F
reelId.work.order.empty=reelId\uFF1A{0}\u6307\u4EE4\u4E3A\u7A7A
reelId.no.work.order=reelId\uFF1A{0}\u65E0\u6307\u4EE4\u4FE1\u606F
reelId.work.order.not.closed=reelId\u5BF9\u5E94\u6307\u4EE4:{0}\u5B8C\u5DE5\u6216\u96F6\u661F\u677F\u6302\u8D77\u624D\u80FD\u9000\u6599
reelId.not.exists.center.factory=reelId \u5728\u4E2D\u5FC3\u5DE5\u5382\u4E0D\u5B58\u5728\uFF01
reelId.item.type.a=reelId A\u6750\u76EE\u524D\u4E0D\u652F\u6301\u9000\u7EBF\u8FB9\u4ED3\uFF0C\u8BF7\u76F4\u63A5\u9000\u592E\u4ED3\uFF01
reelId.wetLevel.not.supported=\u5F53\u524DreelId\u9632\u6F6E\u7B49\u7EA7\u4E3A{0}\uFF0C\u4E0D\u5141\u8BB8\u9000\u6599!
alloc.no.setting.data.tip=\u6CA1\u6709\u8BBE\u7F6E\u592E\u4ED3\u8C03\u62E8\u9700\u8981\u7684\u914D\u7F6E\u4FE1\u606F\uFF01
lookup.no.is.null=\u6570\u636E\u5B57\u5178\u65E0\u8FD4\u56DE
look.up.is.empty=\u6570\u636E\u5B57\u5178\u672A\u914D\u7F6E
update.task.info.error=\u66F4\u65B0\u4EFB\u52A1\u4FE1\u606F\u5F02\u5E38
out.type.is=\u51FA\u5E93\u7C7B\u578B\u4E3A\u6309\u7269\u6599\u4EE3\u7801+\u6570\u91CF\u51FA\u5E93\u65F6\uFF0C\u4E0D\u80FD\u4E3A\u7A7A
get.field.delivery.date.failed=\u83B7\u53D6\u4E3B\u5DE5\u5E8F\u5BF9\u5E94\u4EE3\u7801\u4EA4\u671F\u8868\u5B57\u6BB5\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4!
failed.to.get.code.due.date.data=\u83B7\u53D6\u4EE3\u7801\u4EA4\u671F\u6570\u636E\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4!
process.transfer.workOrderNo.is.null=\u5F53\u524D\u6307\u4EE4\u548C\u76EE\u6807\u6307\u4EE4\u4E0D\u80FD\u4E3A\u7A7A
process.transfer.prodPlanId.is.not.match=\u5F53\u524D\u6307\u4EE4\u548C\u76EE\u6807\u6307\u4EE4\u4E0D\u5339\u914D , \u5F53\u524D\u6307\u4EE4:{0}, \u76EE\u6807:{1}

task.is.running={0} \u4EFB\u52A1\u6B63\u5728\u6267\u884C\u3002\u8BF7\u7A0D\u540E\u91CD\u8BD5

call.aps.write.back.fail=\u8C03\u7528APS\u56DE\u5199\u5165\u5E93\u6570\u91CF\u5931\u8D25 {0}
current.factory.is.write.back=\u5F53\u524D\u5DE5\u5382\u6B63\u5728\u56DE\u5199APS

reelid.prepareinfo.is.not.match=\u6B64reelid{0}\u5BF9\u5E94\u7684\u7EBF\u4F53/\u6307\u4EE4\u548C\u9875\u9762\u4E0D\u4E00\u81F4\uFF0C\u5982\u8981\u4EA4\u63A5\u8BF7\u91CD\u7F6E\u9875\u9762\u6570\u636E
reelid.notin.prepare=\u5907\u6599\u8868\u4E2D\u6CA1\u6709\u6B64REELID\u4FE1\u606F
reelid.have.picked=\u6B64REELID\u5DF2\u88AB{0}\u6307\u4EE4\u63A5\u6536
reelid.workorder.error=\u6B64REELID\u53D1\u5F80\u6307\u4EE4:{0}
uph.null=\u672A\u83B7\u53D6\u5230\u6307\u4EE4:{0}\u4EA7\u80FD
work.order.smt.null=\u6307\u4EE4WORK_ORDER_SMT\u4FE1\u606F\u4E0D\u5B58\u5728

materialRequisitionBill.is.null=\u9886\u6599\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
materialRequisitionBill.info.null=\u672A\u67E5\u8BE2\u5230\u5F53\u524D\u9886\u6599\u5355\u53F7\u4FE1\u606F
billStstus.is.null=\u5355\u636E\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
confirm.status.error=\u53EA\u80FD\u786E\u8BA4\u5355\u636E\u72B6\u6001\u4E3A'\u5F85\u786E\u8BA4'\u7684\u5355\u636E
recitem.status.error=\u53EA\u80FD\u6536\u6599\u5355\u636E\u72B6\u6001\u4E3A'\u5DF2\u786E\u8BA4'\u7684\u5355\u636E

changeType.is.null=\u66F4\u6362\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
oldMaterialStatus.status.error=\u65E7\u6599\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
scrapReason.status.error=\u4F5C\u5E9F\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
send.status.error=\u53EA\u80FD\u53D1\u6599\u5355\u636E\u72B6\u6001\u4E3A'\u5DF2\u9886\u6599'\u7684\u5355\u636E
inforbillno.is.null=INFOR\u9886\u6599\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A

return.item.error={0}:\u7269\u6599\u5DF2\u4F7F\u7528\uFF0C\u4E0D\u80FD\u8FDB\u884C\u9000\u6599
return.item.error.idnull=\u7EF4\u4FEE\u660E\u7EC6ID\u4E0D\u80FD\u4E3A\u7A7A
return.item.status.error=\u53EA\u6709\u7269\u6599\u72B6\u6001\u662F:\u5DF2\u9886\u6599,\u5DF2\u53D1\u6599,\u5DF2\u6536\u6599\u624D\u53EF\u4EE5\u9000\u6599
bill.status.error=\u5F53\u524D\u5355\u636E\u72B6\u6001\u662F:{0},\u4E0D\u80FD\u8FDB\u884C\u7269\u6599\u53D1\u653E\u76F8\u5173\u64CD\u4F5C\uFF0C\u8BF7\u786E\u8BA4
bill.no.is.empty=\u5355\u636E\u53F7\u4E0D\u80FD\u4E3A\u7A7A
bill.not.exists=\u5355\u636E\u4E0D\u5B58\u5728
factory.id.not.conifg.lookup=\u5DE5\u5382id:{0} \u672A\u914D\u7F6Einfor\u4ED3,\u8BF7\u68C0\u67E5

ptp.interface.error=\u70B9\u5BF9\u70B9\u63A5\u53E3\u8C03\u7528\u5F02\u5E38:{0},\u5F02\u5E38\u4FE1\u606F:{1}
bom.null=BOM\u6570\u636E\u4E3A\u7A7A
return.bom.null=\u8BF7\u5148\u786E\u8BA4\u662F\u5426\u662F\u6280\u6539\u7269\u6599\u6216\u5176\u4ED6\u539F\u56E0\uFF0C\u8BE5\u7269\u6599\u5728BOM\u6570\u636E\u4E2D\u4E0D\u5B58\u5728
current.batch.is.being.submitted=\u5F53\u524D\u6279\u6B21\u6709\u6B63\u5728\u63D0\u4EA4\u5165\u5E93\u7684\u64CD\u4F5C \u8BF7\u7A0D\u540E\u91CD\u8BD5
current.batch.is.task.being.submitted=\u5F53\u524D\u4EFB\u52A1\u6B63\u5728\u63D0\u4EA4\u8F6C\u6B63\u5165\u5E93,\u8BF7\u7A0D\u540E\u91CD\u8BD5
current.batch.is.being.changed=\u5F53\u524D\u6279\u6B21\u6B63\u5728\u53D8\u66F4\u5DE5\u827A\u8DEF\u5F84 \u8BF7\u786E\u8BA4
param.container.is.empty=\u5BB9\u5668id\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
not.found.container.entry=\u672A\u627E\u5230\u8BE5\u5BB9\u5668\u7684\u5355\u636E\u4FE1\u606F
sn.is.not.planid=\u626B\u63CF\u6761\u7801\u4E0D\u662F\u5F53\u524D\u6279\u6B21\uFF0C\u8BF7\u786E\u8BA4\uFF01
item.civ=CIV\u7BA1\u63A7\u7269\u6599\uFF0C\u4E0D\u5141\u8BB8\u9000\u56DE\u7EBF\u8FB9\u4ED3
dip.smt.bom.info.workOrder.locked=DIP\u4E0A\u6599\u4FE1\u606F\u6B63\u5728\u7EF4\u62A4\u4E2D\uFF0C\u8BF7\u7A0D\u540E
pda.transfer.scan.reelid.redis.locked=\u6599\u76D8{0}\u6B63\u5728\u8F6C\u673A\u626B\u63CF\u4E2D\uFF0C\u8BF7\u7A0D\u540E\uFF01

get.lookup.value.error=\u83B7\u53D6\u6570\u636E\u5B57\u5178\u503C\u5F02\u5E38! {0}
log.process.error=\u70B9\u5BF9\u70B9\u8C03\u7528getProcessName\u5931\u8D25

reelid.no.flow.cannot.return={0}\u6CA1\u6709\u7EBF\u8FB9\u4ED3\u51FA\u5E93\u4EA4\u6613\uFF0C\u4E0D\u5141\u8BB8\u9000\u7EBF\u8FB9\u4ED3

failed.to.update.sys.look.up.meaning=\u66F4\u65B0\u6570\u636E\u5B57\u5178\u503C\u5931\u8D25

item.code.not.in.bind.list=\u6761\u7801{0}\u5BF9\u5E94\u7684\u7269\u6599\u4EE3\u7801\u4E0D\u5728\u5F85\u7ED1\u5B9A\u6E05\u5355\u4E2D
sn.is.scaning=\u6761\u7801\u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u8BF7\u7A0D\u540E\uFF01
details.is.empty=\u660E\u7EC6\u4FE1\u606F\u4E3A\u7A7A\uFF01
prodplanId.not.match=\u4EE5\u4E0B\u6761\u7801\uFF1A{0} \u7684\u7EF4\u4FEE\u6279\u6B21\u4E0E\u754C\u9762\u6240\u9009\u6279\u6B21\u4E0D\u4E00\u81F4\u8BF7\u786E\u8BA4\uFF01
locked.bill.no.str=\u4EE5\u4E0B\u5355\u636E\u53F7\u6B63\u5728\u88AB\u64CD\u4F5C\uFF0C\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4\uFF1A{0}
material.requisition.record.not.been.processed={0}\u7EF4\u4FEE\u9886\u6599\u8BB0\u5F55\u672A\u5904\u7406\u5B8C\u6210\uFF0C\u8BF7\u786E\u8BA4\uFF01
repair.material.requisition.is.not.used={0}\u7EF4\u4FEE\u9886\u6599\u5355\u4E2D\u7684{1}\u7269\u6599\u672A\u4F7F\u7528\uFF0C\u4E0D\u5141\u8BB8\u8FD4\u8FD8
material.is.not.used.cannot.scarp={0}\u7EF4\u4FEE\u9886\u6599\u5355\u4E2D\u7684{1}\u7269\u6599\u672A\u4F7F\u7528\uFF0C\u4E0D\u5141\u8BB8\u62A5\u5E9F
no.maintenance.record.sub.sn.consistent={0}\u672A\u627E\u5230\u4E0E\u9886\u6599\u8BB0\u5F55\u5B50\u90E8\u4EF6\u6761\u7801{1}\u4E00\u81F4\u7684\u7EF4\u4FEE\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4\uFF01
exist.material.requisition.cannot.be.rejected=\u5B58\u5728\u9886\u6599\u5355\uFF0C\u4E0D\u80FD\u9A73\u56DE
sys.lookuptype.6671.is.null=\u6570\u636E\u5B57\u51786671\u672A\u914D\u7F6E\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.must.be.greater=\u6761\u7801\u5E8F\u53F7\u987B\u4ECE1\u5F00\u59CB
wip.daily.statistic.job.running=\u5355\u677F\u9AD8\u7EA7\u65E5\u62A5\u7EDF\u8BA1\u4EFB\u52A1\u5DF2\u6267\u884C
erp.warehousing.failed=ERP\u5165\u5E93\u5931\u8D25{0}
unknown.error.handler=\u7CFB\u7EDF\u51FA\u73B0\u672A\u77E5\u9519\u8BEF\uFF0C\u9519\u8BEF\u65E5\u5FD7\u7F16\u53F7\u4E3A\uFF1A{0},\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u4EBA\u5458\u5904\u7406\uFF0C\u8C22\u8C22\uFF01
unBinding.set.is.not.empty=\u8BE5\u6279\u6B21\u8BBE\u7F6E\u4E86\u5F02\u5E38\u7ED1\u5B9A\uFF0C\u5B50\u6761\u7801\u4E0D\u80FD\u626B\u63CF\uFF01
repair.status.error=\u975E\u62DF\u5236\u72B6\u6001\u5355\u636E\uFF0C\u4E0D\u80FD\u63D0\u4EA4
center.factory.bom.error=\u8C03\u7528\u4E2D\u5FC3\u5DE5\u5382\u67E5\u8BE2bom\u5206\u9636\u5931\u8D25
b.smt.bom.info.empty={0}\u5DE5\u5E8F\u5BFC\u5165\u7684\u4E0A\u6599\u8868\u4E3A\u7A7A\u6216\u7528\u91CF\u90FD\u4E3A0
reelid.is.used.by.workOrder=\u6599\u76D8\u5DF2\u7ECF\u88AB{0}\u6307\u4EE4\u4F7F\u7528\u4E86
multiple.data.for.the.reelid=\u8BE5\u6599\u76D8\u5728\u673A\u53F0\u5728\u7528\u8868\u5B58\u5728\u591A\u6761\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.data.for.the.itemCode.in.bom.smt=\u6307\u4EE4\u5BF9\u5E94\u4E0A\u6599\u8868\u6CA1\u8FD9\u4E2A\u7269\u6599{0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
pda.transfer.scan.not.completed={0}\u6307\u4EE4\u8F6C\u673A\u626B\u63CF\u672A\u5B8C\u6210\uFF0C\u4E0D\u5141\u8BB8\u63A5\u6599
the_station_has_a_loading_record=\u7AD9\u4F4D\u5DF2\u6709\u4E0A\u6599\u8BB0\u5F55
failed.to.get.tag.information=\u83B7\u53D6\u4F4D\u53F7\u4FE1\u606F\u5931\u8D25
the.number.of.trays.cannot.be.zero=\u6599\u76D8\u7684\u6570\u91CF\u4E0D\u80FD\u4E3A0
reelid.is.not.for.this.workOrder=\u6CA1\u6709\u8BE5\u6761\u7801\u4FE1\u606F\u6216\u8BE5\u6761\u7801\u4E0D\u662F\u8FD9\u4E2A\u6307\u4EE4\u7684

factory.id.not.match=\u5DE5\u5382ID\u8F93\u5165\u9519\u8BEF,\u6B64\u63A5\u53E3\u53EA\u80FD\u67E5\u8BE2\u5DE5\u5382ID\u4E3A:{0}\u7EF4\u4FEE\u8BB0\u5F55
item.code.is.null.wl=\u8BF7\u8F93\u5165\u7269\u6599\u4EE3\u7801
pagenum.is.null=\u8BF7\u4F20\u5165\u5206\u9875\u9875\u7801\u548C\u6570\u91CF,\u6BCF\u9875\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E1000

do_not_allow_batch_writing_to_the_whitelist=\u6682\u4E0D\u5141\u8BB8\u6279\u91CF\u5199\u5165\u767D\u540D\u5355
sn.length.must.twelve=\u6761\u7801{0}\u957F\u5EA6\u5FC5\u987B\u662F12\u4F4D
special_characters_in_barcode=\u6761\u7801{0}\u5B58\u5728\u7279\u6B8A\u5B57\u7B26
sn.length.must.twelve.and.speialChart=\u6761\u7801{0}\u957F\u5EA6\u5FC5\u987B\u662F12\u4F4D {1}\u5B58\u5728\u7279\u6B8A\u5B57\u7B26\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn_qty_cannot_exceed_100=\u4E00\u6B21\u6279\u91CF\u65B0\u589E\u767D\u540D\u5355\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7{0}
sn.itemNo.is.null=\u6761\u7801{0}\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A
there_is_data_with_a_blank_barcode=\u5B58\u5728\u6761\u7801\u4E3A\u7A7A\u7684\u6570\u636E
barcode.get.template.error=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6A21\u677F\u5217\u8868\u5931\u8D25
barcode.get.template.error.msg=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6A21\u677F\u5217\u8868\u5931\u8D25:{0}
failed_to_obtain_pdm_english_code=\u83B7\u53D6PDM\u82F1\u6587\u4EE3\u53F7\u5931\u8D25
failed_to_get_barcode_center_url=\u83B7\u53D6\u6570\u636E\u5B57\u5178\u914D\u7F6E\u6761\u7801\u4E2D\u5FC3\u63A5\u53E3URL\u5931\u8D25
failed.to.get.barcode.center.download.url=\u83B7\u53D6\u6761\u7801\u4E2D\u5FC3\u9002\u914D\u7A0B\u5E8F\u4E0B\u8F7D\u5730\u5740\u5931\u8D25
failed_to_adjust_barcode_center_print_interface=\u8C03\u6761\u7801\u4E2D\u5FC3\u6253\u5370\u63A5\u53E3\u5931\u8D25
task_isLead_data_dictionary_does_not_exist=\u4EFB\u52A1\u73AF\u4FDD\u5C5E\u6027\u5728\u6570\u636E\u5B57\u5178\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
the_task_is_printing_barcodes=\u8BE5\u4EFB\u52A1\u6B63\u5728\u8FDB\u884C\u6761\u7801\u6253\u5370\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
the_task_insufficient_number_of_printable_barcodes=\u8BE5\u4EFB\u52A1\u53EF\u6253\u5370\u6761\u7801\u6570\u91CF\u4E0D\u8DB3{0},\u8BF7\u786E\u8BA4\uFF01
failed_to_write_print_record=\u5199\u6253\u5370\u8BB0\u5F55\u5931\u8D25
failed_to_query_print_record=\u67E5\u8BE2\u6253\u5370\u8BB0\u5F55\u5931\u8D25
the_barcode_to_be_printed_is_not_continuous=\u5F85\u6253\u5370\u6761\u7801\u4E0D\u8FDE\u7EED\uFF0C\u8BF7\u786E\u8BA4!
call.barCode.center.to.print.falied=\u8C03\u6761\u7801\u4E2D\u5FC3\u6253\u5370\u6761\u7801\u5931\u8D25:{0}
the_barcode_has_not_been_printed=\u8BE5\u6761\u7801\u672A\u8FDB\u884C\u8FC7\u6253\u5370\u64CD\u4F5C\uFF0C\u4E0D\u80FD\u8FDB\u884C\u8865\u6253
duplicate_barcode=\u6761\u7801\u91CD\u590D,\u8BF7\u786E\u8BA4\uFF01
workorder.not.found.prodplanid=\u6279\u6B21 {0} \u65E0\u6307\u4EE4
failed_to_generate_reelid_in_the_factory=\u8C03\u4E2D\u5FC3\u5DE5\u5382\u751F\u6210reelid\u5931\u8D25
failed_to_generate_reelid_in_the_factory_new=\u8C03\u4E2D\u5FC3\u5DE5\u5382\u751F\u6210reelid\u5931\u8D25:{0}
new.lpn.qty.must.less.old.lpn.qty=\u65B0\u5BB9\u5668\u6570\u91CF\u5FC5\u987B\u5C0F\u4E8E\u65E7\u5BB9\u5668\u539F\u6709\u6570\u91CF\u4E14\u5C0F\u4E8E\u65E7\u5BB9\u5668\u5269\u4F59\u6570\u91CF
combined_materials_are_not_allowed_to_be_split=\u7EC4\u5408\u7269\u6599\u4E0D\u5141\u8BB8\u62C6\u5206
the_current_order_is_being_unboxed_or_deducted=\u5F53\u524D\u6307\u4EE4\u6B63\u5728\u8FDB\u884C\u62C6\u7BB1\u6216\u8005\u6263\u6570\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
failed_to_update_box_content_data=\u66F4\u65B0\u7BB1\u5185\u5BB9\u6570\u636E\u5931\u8D25
failed_to_insert_box_content_data=\u65B0\u589E\u7BB1\u5185\u5BB9\u6570\u636E\u5931\u8D25
work.order.no.status.error=\u5F53\u524D\u6307\u4EE4\u72B6\u6001\u4E0D\u662F{0}\uFF0C\u4E0D\u80FD\u5F00\u5DE5\u8BF7\u786E\u8BA4\uFF01
failed_to_update_the_qty_of_reelids=\u66F4\u65B0\u4E2D\u5FC3\u5DE5\u5382reelid\u6570\u91CF\u5931\u8D25
sync_tech_info_error=\u540C\u6B65\u6280\u6539\u4FE1\u606F\u5931\u8D25
smt.machine.h.running=\u5F53\u524D\u6307\u4EE4\u6B63\u5728\u65B0\u589E\u626B\u63CF\u5386\u53F2\u5934\u8868\u64CD\u4F5C\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5!
barcode.get.template.info.failed=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6A21\u677F\u4FE1\u606F\u5931\u8D25
pass.scan.new.running = \u5F53\u524D\u6307\u4EE4\u5B50\u5DE5\u5E8F\u6B63\u5728\u6279\u91CF\u8FC7\u7AD9\u626B\u63CF\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5!
sn.is.doing.other.opt = \u6761\u7801\u6B63\u5728\u6267\u884C\u5176\u5B83\u64CD\u4F5C,\u8BF7\u7A0D\u540E\u91CD\u8BD5{0}
failed_to_get_the_inbound_sub_process=\u83B7\u53D6\u5165\u5E93\u5B50\u5DE5\u5E8F\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4\uFF01
transfer.billNo.not.exist=\u8BE5\u8F6C\u5E93\u5355\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
transfer.application.type.not.zj.bm=\u8BE5\u8F6C\u5E93\u5355\u7684\u8F6C\u5E93\u7C7B\u578B\u4E0D\u7B26\u5408\uFF0C\u8BF7\u786E\u8BA4\uFF01
transfer.billNo.closed=\u8BE5\u8F6C\u5E93\u5355\u5DF2\u5173\u95ED\uFF0C\u8BF7\u786E\u8BA4\uFF01
lpn.no.transfer.record=\u5BB9\u5668{0}\u65E0\u63D0\u4EA4\u8F6C\u5E93\u7533\u8BF7\u5355\u7684\u8BB0\u5F55\uFF0C\u8BF7\u5148\u63D0\u4EA4\u8F6C\u5E93\u7533\u8BF7\u5355\uFF01
lpn.status.is.not.approved=\u5BB9\u5668{0}\u72B6\u6001\u4E0D\u4E3A\u5DF2\u5BA1\u6279\uFF0C\u8BF7\u786E\u8BA4\uFF01
bill.details.sn.status.error=\u5355\u636E\uFF1A{0},Sn:{1},\u72B6\u6001\u4E0D\u662F\u5DF2\u63D0\u4EA4\u8BF7\u68C0\u67E5\u3002
batch.scan.not.morethan.500=\u4E00\u6B21\u63D0\u4EA4\u6761\u7801\u4E0D\u80FD\u8D85\u8FC7{0}\u4E2A
batch.scan.limit.exceed=\u8BE5\u6279\u6B21\u956D\u96D5\u6253\u7801\u4EC5{0}\u4E2A\uFF0C\u4E0D\u5141\u8BB8\u8FC7\u7AD9\u6570\u91CF\u8D85\u8FC7
failed_to_update_the_transfer_request_form=\u66F4\u65B0\u8F6C\u5E93\u7533\u8BF7\u5355
failed_to_get_bill_of_material_version=\u83B7\u53D6\u6599\u5355\u7248\u672C\u5931\u8D25
failed_to_push_the_assembly_relationship=\u8C03pdm\u63A8\u9001\u7EC4\u88C5\u5173\u7CFB\u5931\u8D25
prodplan.exist.sn.record=\u6279\u6B21\u5B58\u5728\u8F93\u5165\u6765\u6E90\u4E3A\u6709\u6761\u7801\u5F55\u5165\u8BB0\u5F55\uFF0C\u4E0D\u80FD\u8FDB\u884C\u65E0\u6761\u7801\u5F55\u5165
prodplan.exist.no.sn.record=\u6279\u6B21\u5B58\u5728\u8F93\u5165\u6765\u6E90\u4E3A\u65E0\u6761\u7801\u5F55\u5165\u8BB0\u5F55\uFF0C\u4E0D\u80FD\u8FDB\u884C\u6709\u6761\u7801\u5F55\u5165
prodplan.is.null=\u6279\u6B21\u53F7\u4E3A\u7A7A
prodplan.qty.less=\u6570\u91CF\u4E0D\u80FD\u5C0F\u4E8E1
prodplan.qty.more=\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E1000
call.barCode.center.expandQuery.barCode.falied=\u8C03\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6761\u7801\u6269\u5C55\u4FE1\u606F\u5931\u8D25:{0}
sn.workOrderNo.info.is.empty.in.wipInfo=\u6761\u7801 {0} \u5728wip_info\u6CA1\u6709\u6307\u4EE4\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01


asyn.compsation.exe.error=\u5F02\u6B65\u8865\u507F\u6267\u884C\u5931\u8D25\uFF0C\u4E0A\u6B21\u4E00\u6267\u884C\u8FD8\u672A\u7ED3\u675F
insert.asyn.compsation.param.error=ID\u3001\u4E1A\u52A1\u4EE3\u7801busineCode\u3001\u53C2\u6570params\u4E0D\u80FD\u4E3A\u7A7A
bucode_or_compensationtimes_is_null=\u4E1A\u52A1\u4EE3\u7801busineCode\u548C\u4E00\u6B21\u8865\u507F\u6B21\u6570compensation\u4E0D\u80FD\u4E3A\u7A7A
businenesscode_is_null=\u4E1A\u52A1\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
email_setting_error=\u8BF7\u6B63\u786E\u914D\u7F6E\u90AE\u4EF6\u53D1\u9001\u914D\u7F6E,\u6536\u4EF6\u4EBA,\u4E3B\u9898,\u53D1\u9001\u95F4\u9694.\u6570\u636E\u5B57\u51782039

get_erpinfo_error=\u83B7\u53D6SOURCE_LINE_ID:{0} ERP\u4FE1\u606F\u5931\u8D25
create.billNo.error=\u8C03\u7528\u4E2D\u5FC3\u5DE5\u5382\u751F\u6210\u5355\u636E\u53F7\u5931\u8D25\u3002


failed_to_call_pdm=\u8C03pdm\u63A5\u53E3\u5931\u8D25:{0}
get.pdm.bom.ver.failed=\u83B7\u53D6\u6700\u65B0\u7684\u7248\u672C\u4FE1\u606F\u5931\u8D25
create.bill.no.error=\u4E2D\u5FC3\u751F\u6210\u81EA\u589E\u5355\u636E\u53F7\u5931\u8D25
spm.bill.status.exception=SPM \u5355\u636E:{0} \u72B6\u6001\u4E0D\u662F\u5DF2\u63D0\u4EA4, \u4E0D\u80FD\u62D2\u6536!
lms.bill.packing.exception=LMS \u5355\u636E:{0} \u5728LMS\u4E2D\u5DF2\u4EA7\u751F\u88C5\u7BB1\u4FE1\u606F\uFF0C\u4E0D\u80FD\u62D2\u6536\uFF01
failed.to.get.sn.info=\u65E0\u6B64\u6761\u7801\u4FE1\u606F
failed.to.get.task.item.list.info=\u83B7\u53D6\u4EFB\u52A1{0}\u7269\u6599\u9700\u6C42\u6E05\u5355\u5931\u8D25
Failed.to.obtain.material.substitution.relationship=\u83B7\u53D6\u7269\u6599\u4EE3\u7801{0}\u66FF\u4EE3\u5173\u7CFB\u5931\u8D25
Serial.code.does.not.allow.batch.scanning=\u5E8F\u5217\u7801\u4E0D\u5141\u8BB8\u6279\u91CF\u626B\u63CF
insufficient.quantity.to.be.bound=\u672C\u6B21\u7ED1\u5B9A\u7684\u5B50\u6761\u7801(\u7269\u6599\u4EE3\u7801{0})\u8D85\u51FA\u4E3B\u6761\u7801\u7684\u6240\u9700\u6570\u91CF
item.code.has.been.bound=\u7269\u6599\u4EE3\u7801{0}\u5DF2\u7ED1\u5B9A\u5B8C\u6210
failed_to_obtain_the_standard_quantity_of_material_code=\u83B7\u53D6\u7269\u6599\u4EE3\u7801{0}\u6807\u51C6\u7528\u91CF\u5931\u8D25
item_code_exist_in_list_more_than_one=\u7269\u6599\u4EE3\u7801\u53EF\u66FF\u4EE3\u7269\u6599{0}\u5728\u7ED1\u5B9A\u6E05\u5355/\u4EFB\u52A1\u9700\u6C42\u6E05\u5355\u4E2D\u5B58\u5728\u591A\u4E2A
failed_to_get_material_standard_usage=\u83B7\u53D6\u7269\u6599\u6807\u51C6\u7528\u91CF\u5931\u8D25
failed_to_get_task_info=\u83B7\u53D6\u6307\u4EE4\u5BF9\u5E94\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25
same.batch.code.cannot.be.bound.to.repeatedly.sn=\u540C\u4E00\u6279\u6B21\u7801\u4E0D\u5141\u8BB8\u91CD\u590D\u7ED1\u5728\u540C\u4E00\u4E3B\u6761\u7801
use.qty.can.not.be.decimal=\u6761\u7801\u4E3A\u5E8F\u5217\u7801\u65F6\uFF0C\u6807\u51C6\u7528\u91CF\u4E0D\u80FD\u4E3A\u5C0F\u6570
failed.to.get.erp.task.qty=\u83B7\u53D6erp\u4EFB\u52A1\u6570\u91CF\u5931\u8D25
call.service.error=\u8C03\u7528\u670D\u52A1\u5F02\u5E38\uFF1A{0}--{1}
replace.sn.item.code.veify.failed=\u66F4\u6362\u6761\u7801\u7684\u7269\u6599\u4EE3\u7801\u4E0D\u662F{0}
spm.bill.no.empty=\u5355\u636E\u53F7\u4E3A\u7A7A
spm.bill.no.exist=\u5355\u636E\u53F7\u5728Infor \u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.bill.completed={0} \u5355\u636E\u5DF2\u5B8C\u6210\u63A5\u6536\u64CD\u4F5C\uFF0C\u8BF7\u786E\u8BA4!
spm.bill.type.not.support=\u76EE\u524D\u4E0D\u652F\u6301\u8BE5\u7C7B\u578B\u5355\u636E {0},\u8BF7\u786E\u8BA4!
spm.bill.type.empty=\u8BE5\u5355\u636E\u53F7\u4E0B\u5B58\u5728\u7C7B\u578B\u4E3A\u7A7A\u7684PKG ID/SN\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.bill.type.multiple=\u8BE5\u5355\u636E\u53F7\u4E0B\u5B58\u5728\u591A\u79CD\u7C7B\u578B\u7684PKG ID/SN\uFF0C\u4E0D\u652F\u6301\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.bill.scan.sn.lock.fail=PKG ID/SN {0} \u6B63\u5728\u626B\u63CF\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
spm.scan.params.check.msg=\u53C2\u6570{0}\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.bill.barcode.no.exist=\u5F53\u524DPKG ID/SN {0} \u5728\u5355\u636E\u53F7 {1} \u4E0B\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.scan.barcode.complete=\u5F53\u524DPKG ID/SN {0} \u5728\u5355\u636E\u53F7 {1} \u4E0B\u5DF2\u7ECF\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.barcode.qty.error=PKG ID \u6570\u91CF\u6BD4\u5BF9\u4E0D\u901A\u8FC7\uFF0C\u53D1\u6599\u6570\u91CF\u4E3A {0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
spm.bill.no.lock=\u5355\u636E\u53F7 {0} \u6B63\u5728\u8FDB\u884C\u65B0\u589E\u64CD\u4F5C\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
work.order.not.started=\u6307\u4EE4{0}\u672A\u5F00\u5DE5
trans.org.erp.fail=\u7EC4\u7EC7\u95F4\u8F6C\u79FB\u4FE1\u606F\u63A8\u9001\u81F3erp\u5931\u8D25
trans.org.erp.fail.msg=\u7EC4\u7EC7\u95F4\u8F6C\u79FB\u4FE1\u606F\u63A8\u9001\u81F3erp\u5931\u8D25\uFF0C\u8D85\u8FC7\u91CD\u8BD5\u6B21\u6570\u3002\ndeal_id\uFF1A{0}
workOrder.status.must.be.follows=\u6307\u4EE4\u72B6\u6001\u5FC5\u987B\u4E3A{0}
b.smt.bom.detail.loading={0} \u6B63\u5728\u5907\u6599\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u4E0B\u3002
work.order.preparation={0} \u6307\u4EE4\u662F\u9996\u5907\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u53D8\u66F4\u4E0A\u6599\u8868\uFF0C\u8BF7\u9996\u5907\u5B8C\u6210\u540E\u518D\u8BD5\u3002
b.smt.upload.ok=\u4E0A\u6599\u8868\u5BFC\u5165\u5B8C\u6210:{0}\u3002
b.smt.upload.ignore=\u672A\u66F4\u65B0\u4E0A\u6599\u8868\u4FE1\u606F:{0}\u3002
smt.location.info.empty=\u4EE5\u4E0BSMT\u7AD9\u4F4D\u4FE1\u606F\u4E0D\u5B58\u5728\u8BF7\u786E\u8BA4\uFF01{0}
match.line.code.empty=\u9762\u522B{0} \u672A\u6392\u4EA7\u5F53\u524D\u7EBF\u4F53\u8BF7\u786E\u8BA4\uFF01\uFF01

barcode.repair.not.returned=\u6761\u7801{0}\u7EF4\u4FEE\u672A\u8FD4\u8FD8\uFF0C\u8BF7\u786E\u8BA4
sn.status.not.warehouse.in=\u6761\u7801 {0}\u4E0D\u662F\u5165\u5E93\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.is.warehouse.out=\u6761\u7801{0}\u5DF2\u51FA\u5E93
blink={0}
sn.null={0}\u6761\u7801\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
sn.cannot.return={0}\u6761\u7801\u5B50\u5DE5\u5E8F\u4E3A{1}\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u88C5\u7BB1\u9000\u5E93\u64CD\u4F5C
setting.is.null=\u6570\u636E\u5B57\u5178{0}\u672A\u8BBE\u7F6E
sn.qty.over=\u6761\u7801\u4E0D\u80FD\u8D85\u8FC7{0}\u4E2A
sn.itemno.not.same={0}\u6761\u7801\u6240\u5C5E\u4EE3\u7801\u4E3A{1}\uFF0C\u4E0E\u6761\u7801\u6E05\u5355\u4E2D\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
sn.process.not.fit={0}\u6761\u7801\u5B50\u5DE5\u5E8F\u4E3A{1}\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u88C5\u7BB1\u9000\u5E93\u64CD\u4F5C
barcode.is.controlled.param=\u6761\u7801 {0} \u88AB\u7BA1\u63A7\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u3002
wip.next.process=\u8BF7\u53BB{0}\u4E0B\u5DE5\u5E8F\u626B\u63CF\u3002
poilt.test.error.param=\u4E2D\u8BD5\u63A5\u53E3\u9519\u8BEF,{0}
spm.call.error.param=spm \u7BA1\u63A7\u9519\u8BEF,{0}
has.not.rollover.param=\u6761\u7801\uFF1A{0} \u5B58\u5728\u7EF4\u4FEE\u672A\u8FD4\u8FD8\u3002
sn.is.scaning.param=\u6761\u7801\uFF1A{0} \u6B63\u5728\u626B\u63CF,\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
is.scaned.param=\u6761\u7801:{0} \u5DF2\u7ECF\u626B\u63CF!
to.next.process.code=\u8BF7\u53BB({0})\u5DE5\u5E8F\u626B\u63CF\uFF01
sn.has.rel.task={0} \u6761\u7801\u5DF2\u4E0E{1}\u8FD4\u5DE5\u4EFB\u52A1\u7ED1\u5B9A
sn.not.in.warehouse=\u6761\u7801{0}\u5728{1}\u4ED3\u5E93\u4E0D\u5B58\u5728\u5E93\u5B58\uFF0C\u8BF7\u786E\u8BA4
source.warehouse.is.null=\u6E90\u4ED3\u5E93\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
isLead_data_dictionary_does_not_exist=\u73AF\u4FDD\u5C5E\u6027{0}\u5728\u6570\u636E\u5B57\u5178\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.is.associated.with.zte.serial.code=\u6761\u7801{0}\u5DF2\u5173\u8054\u4E2D\u5174\u5E8F\u5217\u7801\uFF0C\u8BF7\u626B\u63CF\u4E2D\u5174\u5E8F\u5217\u7801
sub.sn.hb.attr.veify.not.passed=\u6761\u7801{0}\u73AF\u4FDD\u5C5E\u6027\u6821\u9A8C\u4E0D\u901A\u8FC7
sn.not.rework.task=\u6761\u7801\u4E0D\u5C5E\u4E8E\u8FD4\u5DE5\u4EFB\u52A1\uFF0C\u4E0D\u80FD\u6DF7\u4EFB\u52A1\u626B\u63CF
rework.task.difference=\u6761\u7801\u6240\u5C5E\u4EFB\u52A1\u4E3A{0}\uFF0C\u4E0D\u80FD\u6DF7\u4EFB\u52A1\u626B\u63CF
reelid_is_registered=reelid\u5DF2\u6CE8\u518C\uFF0C\u8BF7\u786E\u8BA4\uFF01
reelId_has_been_handed_over=reelid{0}\u5DF2\u4EA4\u63A5\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.has.bind.main.sn={0}\u6761\u7801\u5DF2\u4E0E\u6574\u673A\u6761\u7801{1}\u7ED1\u5B9A\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
item.no.not.same.cannot.replace=\u66FF\u6362\u6761\u7801\u6599\u5355\u4E0E\u539F\u6761\u7801\u6599\u5355\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
not.find.sn.cannot.replace=\u672A\u627E\u5230{0}\u6761\u7801\u8BB0\u5F55\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
sn.has.create.rework.task={0}\u6761\u7801\u5DF2\u521B\u5EFA\u8FD4\u5DE5\u4EFB\u52A1\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
sn.not.exist.stock={0}\u6761\u7801\u5728\u6807\u6A21\u5E93\u5B58\u4E2D\u4E0D\u5B58\u5728\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u66FF\u6362\u64CD\u4F5C
cannot.find.veneer.sn=\u6CA1\u6709\u627E\u5230\u6574\u673A\u6761\u7801{0}\u5BF9\u5E94\u7684\u5355\u677F\u6761\u7801
unconfirmed_reelid_cannot_be_returned=\u672A\u786E\u8BA4\u7684reelid {0} \u4E0D\u80FD\u9000\u6599,\u8BF7\u786E\u8BA4\uFF01
return.times.more.than.max=reelid {0} \u9000\u6599\u6B21\u6570\u8D85\u8FC7\u6700\u5927\u5141\u8BB8\u6B21\u6570{1}\u6B21, \u9700\u8981\u5148\u8FDB\u884C\u9000\u6599\u786E\u8BA4\u624D\u53EF\u4EE5\u8FDB\u884C\u9000\u6599\u6536\u8D27
batch_or_barcode_cannot_be_empty=\u6279\u6B21\u6216\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
sns.is.not.exits=\u6761\u7801{0}\u4E0D\u5B58\u5728
prodplanId.is.not.exits=\u6279\u6B21{0}\u4E0D\u5B58\u5728
batch_barcode_repeats=\u6279\u6B21/\u6761\u7801{0}\u91CD\u590D
must_be_less_than_1000=\u6279\u91CF\u64CD\u4F5C\u6279\u6B21/\u6761\u7801\u5FC5\u987B\u5C0F\u4E8E1000
the_current_document_is_in_operation=\u5F53\u524D\u5355\u636E\u6B63\u5728\u64CD\u4F5C\u4E2D,\u8BF7\u7A0D\u540E
the_current_document_is_not_in_this_state=\u5F53\u524D\u5355\u636E\u4E0D\u662F{0}\u72B6\u6001\uFF0C\u8BF7\u786E\u8BA4
sn.lock.error.msg={0} \u6761\u7801\u5728\u5DE5\u5E8F {1} \u9501\u5B9A\uFF0C\u9501\u5B9A\u5355\u53F7\uFF1A{2}\uFF0C\u8BF7\u8054\u7CFB {3} \u5904\u7406
source.task.lock.error.msg=\u6279\u6B21 {0} \u5728\u5DE5\u5E8F {1} \u9501\u5B9A\uFF0C\u9501\u5B9A\u5355\u53F7:{2}\uFF0C\u8BF7\u8054\u7CFB{3}\u5904\u7406
child.lock.error.msg=\u6761\u7801 {0} \u5B50\u6761\u7801 {1} \u5DF2\u88AB\u9501\u5B9A\uFF0C\u9501\u5B9A\u5355\u53F7:{2}\uFF0C\u8BF7\u8054\u7CFB {3} \u5904\u7406
child.source.task.lock.error.msg=\u6279\u6B21 {0} \u88AB\u9501\u5B9A\uFF0C\u9501\u5B9A\u5355\u53F7\uFF1A{1}\uFF0C\u8BF7\u8054\u7CFB {2} \u5904\u7406
details_cannot_be_empty_for_submit=\u5355\u636E\u63D0\u4EA4\u65F6\u660E\u7EC6\u4E0D\u80FD\u4E3A\u7A7A
criteria_cannot_be_null_at_the_same_time=\u9501\u5B9A\u5355\u53F7/\u6279\u6B21/\u6761\u7801/\u65F6\u95F4\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
the_time_frame_cannot_be_greater_than_one_year=\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u5927\u4E8E\u4E00\u5E74
document_number_cannot_be_empty=\u5355\u636E\u53F7\u4E0D\u80FD\u4E3A\u7A7A
lock_type_cannot_be_null=\u9501\u5B9A\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
document_details_cannot_exceed_1000=\u5355\u636E\u660E\u7EC6\u4E0D\u80FD\u8D85\u8FC71000
lock_reason_cannot_be_null=\u9501\u5B9A\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
sn_item_cannot_exceed_100=sn\u6761\u7801\u4E0D\u80FD\u8D85\u8FC7100\u6761
call.ifis.timeout=\u8C03\u7528ifis\u63A5\u53E3\u8D85\u65F6
call.ifis.error=\u8C03\u7528ifis\u63A5\u53E3\u5931\u8D25 {0}
repair.info.too.many=\u7EF4\u4FEE\u4FE1\u606F\u8D85\u8FC71000\u6761
insufficient.number.sn.tobe.repaired=\u6279\u6B21{0}\u5F85\u7EF4\u4FEE\u7684\u6761\u7801\u6709{1}\u4E2A\uFF0C\u6570\u91CF\u4E0D\u8DB3
lock_bill_no_is_empty=\u9501\u5B9A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
un_lock_user_no_is_empty=\u89E3\u9501\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
un_lock_reason_is_empty=\u89E3\u9501\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
lock_bill_no_not_exist=\u5355\u53F7\u4E0D\u5B58\u5728
unlock_sn_is_empty=\u89E3\u9501\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
unlock_plan_is_empty=\u89E3\u9501\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
unlock_craft_sn_all_empty=\u89E3\u9501\u6761\u7801\u548C\u5DE5\u5E8F\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
sn_more_than_1000=\u6761\u7801\u6570\u4E0D\u80FD\u8D85\u8FC71000
sn_more_than_100=\u6761\u7801\u6570\u4E0D\u80FD\u8D85\u8FC7100
sn_more_than=\u6761\u7801\u6570\u4E0D\u80FD\u8D85\u8FC7{0}
plan_more_than=\u6279\u6B21\u6570\u4E0D\u80FD\u8D85\u8FC7{0}, {1}
task_more_than=\u4EFB\u52A1\u6570\u4E0D\u80FD\u8D85\u8FC7{0}, {1}
wip_not_exist=\u6761\u7801\u4E0D\u5B58\u5728\uFF1A{0}
lpn_wip_not_exist=\u7BB1\u7801\u4E0D\u5B58\u5728\u7BB1\u5185\u5BB9
lpn_is_null=\u7BB1\u7801\u4E0D\u80FD\u4E3A\u7A7A
item_no_must_same=\u7269\u6599\u4EE3\u7801\u5FC5\u987B\u4E00\u81F4, {0}


pkcode_has_received=\u8BE5\u6599\u76D8\u5DF2\u63A5\u6599\uFF0C\u8BF7\u786E\u8BA4\uFF01
too_much_data_error=\u6570\u636E\u91CD\u590D\uFF0C\u8BF7\u786E\u8BA4
pkcode_not_used_error=\u8BE5\u6599\u76D8\u6CA1\u4E0A\u6599\u8FC7\uFF0C\u8BF7\u786E\u8BA4\u6216\u8005\u5148\u505A\u9996\u5907
workorder_not_start=\u8BE5\u6599\u76D8\u65E0\u5BF9\u5E94\u7684\u5F00\u5DE5\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4
workorder_not_found=\u8BE5\u6599\u76D8\u5BF9\u5E94\u6307\u4EE4\u4FE1\u606F\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
pk_code_not_regist=ReelId\u672A\u6CE8\u518C\uFF0C\u8BF7\u786E\u8BA4
old_and_new_pkcode_can_not_be_null=\u65E7\u6599\u76D8\u548C\u7EED\u6599\u76D8\u4E0D\u80FD\u4E3A\u7A7A
wms_has_the_reelid=\u8BE5reelId\u7EBF\u8FB9\u4ED3\u8FD8\u672A\u53D1\u6599
check_wms_stock_error=\u67E5\u8BE2reelId\u662F\u5426\u5728\u7EBF\u8FB9\u4ED3\u7684\u8FC7\u7A0B\u53D1\u751F\u9519\u8BEF
pdaRsError_001=\u672A\u627E\u5230\u65E7\u6599\u76D8\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
pdaRsError_002=\u672A\u627E\u5230\u65B0\u6599\u76D8\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
pdaRsError_003=\u8BF7\u786E\u8BA4\u65B9\u5411\u662F\u5426\u4E00\u81F4\uFF1F
pdaRsError_004=\u4E0D\u540C\u5382\u5BB6\u7269\u6599\u5728\u7F16\u5E26\u5185\u65B9\u5411\u4E0D\u4E00\u81F4\uFF0C\u7981\u6B62\u63A5\u6599
pdaRsError_005=\u626B\u63CF\u7684\u65B0\u6599\u76D8\u548C\u6599\u76D8\u627E\u7AD9\u4F4D\u5BF9\u5E94\u7684REELID\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
pdaRsError_006=\u8BE5reelId\u7EBF\u8FB9\u4ED3\u8FD8\u672A\u53D1\u6599
pdaRsError_007=\u65B0\u65E7\u6599\u76D8\u7684\u4F9B\u5E94\u5546\u7F16\u7801\u4E0D\u4E00\u81F4
pdaRsError_008=\u65B0\u6599\u76D8:{0}\u7684\u65B9\u5411\u4E3A{1}\uFF0C\u65E7\u6599\u76D8:{2}\u7684\u65B9\u5411\u4E3A{3}\uFF0C\u65B9\u5411\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
pdaRsError_009=\u65B0\u6599\u76D8:{0}\u7684\u65B9\u5411\u4E3A{1}\uFF0C\u65E7\u6599\u76D8:{2}\u7684\u65B9\u5411\u4E3A{3}\uFF0C\u65B9\u5411\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
pdaRsError_010=\u65B0\u6599\u76D8:{0}\uFF0C\u65E7\u6599\u76D8:{1}\u7269\u6599\u5382\u5BB6\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\u7269\u6599\u65B9\u5411
pdaRsError_011=\u65B0\u6599\u76D8:{0}\u7684\u65B9\u5411\u4E3A{1}\uFF0C\u65E7\u6599\u76D8:{2}\u7684\u65B9\u5411\u4E3A{3}\uFF0C\u65B9\u5411\u4E0D\u4E00\u81F4


parent.sn.can.not.be.null=\u4E3B\u677F\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
list.child.sn.can.not.be.null=\u5B50\u677F\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
operator.can.not.be.null=\u7ED1\u5B9A\u64CD\u4F5C\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
parent.sn.format.error=\u4E3B\u677F{0}\u683C\u5F0F\u9519\u8BEF\uFF0C\u5FC5\u987B\u4EE5P\u5F00\u5934\uFF0C\u540E\u63A512\u4F4D\u6570\u5B57
child.sn.is.not.scanned=\u5B50\u677F\u6761\u7801\u4E2D{0}\u672A\u626B\u63CF\uFF0C\u65E0\u6CD5\u7ED1\u5B9A
ct.route.detail.of.prodplanId.not.exist=\u6279\u6B21 {0} \u5BF9\u5E94\u5DE5\u827A\u8DEF\u5F84\u4FE1\u606F\u4E0D\u5B58\u5728
preparation_can_not_unlock=\u62DF\u5236\u4E2D\u5355\u636E\u4E0D\u80FD\u89E3\u9501
reelId.of.prodplanid.is.null=\u7269\u6599\u4EE3\u7801\u6CE8\u518C\u7684\u6279\u6B21{0}\u672A\u80FD\u5728\u672C\u5730\u5DE5\u5382\u627E\u5230
sn.binding.not.complete= SN {0} \u5728\u5DE5\u5E8F{1}\u5355\u677F\u7ED1\u5B9A\u6CA1\u6709\u5B8C\u6210\u8BF7\u786E\u8BA4\u3002
failed_to_get_task_info_by_prodplanId=\u83B7\u53D6\u6279\u6B21{0}\u5BF9\u5E94\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25
failed_to_get_task_info_by_taskNo=\u83B7\u53D6\u4EFB\u52A1{0}\u5BF9\u5E94\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25
failed.to.get.assemblyrelation_for_sn=\u672A\u83B7\u53D6\u5230\u6761\u7801\u88C5\u914D\u7ED1\u5B9A\u7684\u6574\u673A\u6761\u7801
product.type.can.not.be.null=\u4EA7\u54C1\u5927\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
error.msg.from.mds.is.null=\u672A\u901A\u8FC7\u4E2D\u8BD5\u63A5\u53E3\u83B7\u53D6\u5230\u5BF9\u5E94\u7684\u6545\u969C\u63CF\u8FF0
corresponding.stage.is.null=\u6570\u636E\u5B57\u51786915\u9001\u4FEE\u5F55\u5165\u7533\u8BF7\u90E8\u95E8\u5BF9\u5E94\u7684\u751F\u4EA7\u9636\u6BB5\u672A\u914D\u7F6E\uFF0C\u5982\u5DF2\u914D\u7F6E\u8BF7\u5237\u65B0\u9875\u9762\u91CD\u8BD5
the.entered.barcode.is.not.a.board.barcode=\u8F93\u5165\u7684\u6761\u7801\u975E\u5355\u677F\u6761\u7801
an_assembly_is_not_completed=\u6761\u7801{0}\u4E00\u6B21\u88C5\u914D\u672A\u5B8C\u6210
secondary_assembly_not_completed=\u6761\u7801{0}\u4E8C\u6B21\u88C5\u914D\u672A\u5B8C\u6210
param.is.null=\u53C2\u6570\u7F3A\u5931\u8BF7\u786E\u8BA4
tran.pk.error={0} \u7EFC\u5408\u5907\u6599\u672A\u5B8C\u6210\uFF0C\u4E0D\u80FD\u505A\u8F6C\u673A\u626B\u63CF
prepare.info.of.feeder.not.exist=feeder({0})\u65E0\u5907\u6599\u8BB0\u5F55
feeder.no.is.null=feederNo\u4E0D\u80FD\u4E3A\u7A7A
line.name.not.existing=\u672A\u627E\u5230\u7EBF\u4F53\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
line.not.support.intelligent.feeder=\u8BE5\u7EBF\u4F53\u4E0D\u652F\u6301\u667A\u80FDfeeder\uFF0C\u8BF7\u786E\u8BA4\uFF01
prodplanId.of.reelId.not.match.prepare=reelid{0}\u7684\u6CE8\u518C\u6279\u6B21\u548C\u5907\u6599/\u673A\u53F0\u5728\u7528\u9636\u6BB5\u6279\u6B21\u4E0D\u4E00\u81F4\u3002
feeder.insert.avl.error=\u7AD9\u4F4D\uFF1A{0}\uFF0C\u7269\u6599\u4EE3\u7801\uFF1A{1}\uFF0Cfeeder\u63D2\u5165AVL\u6821\u9A8C\u62A5\u9519
feeder.insert.avl.check.failed=feeder\u63D2\u5165\uFF0CAVL\u6821\u9A8C\u7ED3\u679C\uFF1A{0}
feeder.has.multiple.record=feeder{0} \u5B58\u5728\u591A\u6761\u63D0\u524D\u5907\u6599\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4\uFF01
params.not.match.prepare.data=feeder{0} \u5728\u63D0\u524D\u5907\u6599\u8868\u4E2D\u7684\u8BB0\u5F55\u548C\u4F20\u5165\u53C2\u6570\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
workorder.already.have.prepare.data={0} \u6307\u4EE4\u5DF2\u6709\u63D0\u524D\u5907\u6599\u4FE1\u606F
pls.renew.tray.before.completing=\u8BF7\u5148\u7EED\u6599\u76D8\u518D\u5B8C\u5DE5\u3002\u7AD9\u4F4D\uFF0C\u7269\u6599\u4EE3\u7801\uFF1A{0}
prodplanid.not.belongs.to.workorder.or.next=\u7269\u6599\u6279\u6B21\u4E0D\u662F\u8BE5\u6307\u4EE4\u6216\u9884\u8F6C\u4EA7\u6307\u4EE4\u7684\uFF0C\u8BF7\u786E\u8BA4
no.material.transfer.operation.allowed={0}\u6279\u6B21\u5B58\u5728\u672A\u7528\u5B8C\u7684\u7269\u6599{1}\uFF0Creelid\uFF1A{2}\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u632A\u6599\u64CD\u4F5C
transfer.strategy.configuration.line.mismatch=\u8F6C\u4EA7\u7B56\u7565\u914D\u7F6E\u7EBF\u4F53\u4E0D\u5339\u914D
feeder.not.exist.mouting=feeder{0}\u672A\u627E\u5230\u5BF9\u5E94\u7684\u7269\u6599\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.no.of.prepare.error=prepare\u8868\u4E2D\u8BE5feeder\u6570\u636E\u6307\u4EE4\u4E3A\u7A7A\u6216\u683C\u5F0F\u4E0D\u5BF9
tar_work_order_is_null=\u76EE\u6807\u6307\u4EE4\u4E0D\u80FD\u4E3A\u7A7A
work_order_no_smt_detail=\u6307\u4EE4 {0} \u5728 {1} \u6A21\u7EC4\u6CA1\u6709\u4E0A\u6599\u8868\u8BE6\u7EC6\u4FE1\u606F
work_order_module_no_prepare=\u5907\u6599\u672A\u5B8C\u6210\uFF0C\u6307\u4EE4 {0} \u6A21\u7EC4 {1} \u7AD9\u4F4D\uFF1A{2}
line_module_has_mouting=\u7EBF\u4F53 {0} \u6A21\u7EC4 {1} \u5B58\u5728\u6709\u6548\u673A\u53F0\u5728\u7528\uFF0C\u4E0D\u53EF\u5207\u6362
failed_to_query_en_code_barcode_information=\u67E5\u8BE2EN\u7801{0}\u5BF9\u5E94\u6761\u7801\u4FE1\u606F\u5931\u8D25
failed_to_adjust_barcode_center_interface=\u8C03\u6761\u7801\u4E2D\u5FC3\u63A5\u53E3\u5931\u8D25\uFF1AURL:{0}
failed_to_register_barcode_in_barcode_center=\u8C03\u6761\u7801\u4E2D\u5FC3\u6CE8\u518C\u6761\u7801\u5931\u8D25:{0}

location_not_exist=\u7AD9\u4F4D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
line_no_sup_model_chg=\u7EBF\u4F53 {0} \u4E0D\u652F\u6301\u6309\u6A21\u7EC4\u6362\u7EBF\uFF0C\u8BF7\u786E\u8BA4
line_no_sup_in_feeder=\u7EBF\u4F53 {0} \u4E0D\u652F\u6301\u667A\u80FDFeeder\uFF0C\u8BF7\u786E\u8BA4
location.sn.error={0} \u7AD9\u4F4D\u7F16\u7801\u4E3A\u7A7A\u6216\u683C\u5F0F\u9519\u8BEF
smt.a.bom.not.active=\u6279\u6B21:{0}\u7684\u4E0A\u6599\u8868A\u9762\u672A\u5BFC\u5165\u6216\u5DF2\u5931\u6548\uFF0C\u8BF7\u786E\u8BA4
smt.b.bom.not.active=\u6279\u6B21:{0}\u7684\u4E0A\u6599\u8868B\u9762\u672A\u5BFC\u5165\u6216\u5DF2\u5931\u6548\uFF0C\u8BF7\u786E\u8BA4
not.have.smt.work.order=\u6279\u6B21:{0}\u4E0D\u5B58\u5728\u6216\u8BE5\u6279\u6B21\u65E0SMT\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
batch.complete.analysis.not.undone=\u6279\u6B21\u9F50\u5957\u5206\u6790\u672A\u5B8C\u6210\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.id.is.null=\u672A\u8F93\u5165reelId\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.id.not.register.item.check=reelId:{0}\u672A\u6CE8\u518C\u5230\u6279\u6B21:{1},\u8BF7\u786E\u8BA4\uFF01
lfid.not.match.register.info=\u8F93\u5165\u7684lfid:{0}\u548CreelId:{1}\u7684\u6CE8\u518C\u4FE1\u606F\u4E0D\u4E00\u81F4\uFF01
reel.id.not.pre.check=reelId:{0}\u4E0D\u5B58\u5728\u9884\u6E05\u70B9\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4!
reel.id.finish.check=reelId:{0}\u5DF2\u7ECF\u5B8C\u6210\u6E05\u70B9\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.id.forbidden.check=reelId:{0}\u662F\u4E0D\u53EF\u6E05\u70B9\u72B6\u6001\uFF0C\u8BF7\u786E\u8BA4\uFF01
no_prodplan.id.need.compute=\u6CA1\u6709\u9700\u8981\u8BA1\u7B97\u7684\u6279\u6B21
all.prodplan.id.exist=\u6240\u6709\u6279\u6B21\u5747\u5B58\u5728\u8BB0\u5F55
get.pack.spec.faild=\u83B7\u53D6\u7269\u6599\u4EE3\u7801\u65B9\u5411\u6027\u5931\u8D25
update.last.tssue.seq.date.faild=\u4FEE\u6539\u6700\u540E\u53D1\u6599\u8BA1\u7B97\u65F6\u95F4\u6570\u636E\u5B57\u5178\u5931\u8D25
get.pk.code.info.failed=\u83B7\u53D6\u6279\u6B21\u53D1\u6599\u4FE1\u606F\u5931\u8D25
barcode_repeat=\u6761\u7801{0}\u91CD\u590D\u6B21\u6570{1}
the_barcode_corresponds_to_the_wrong_batch=\u6761\u7801{0}\u5BF9\u5E94\u6279\u6B21\u4E3A{1}
barcodes_are_not_numbers=\u6761\u7801{0}\u4E0D\u662F\u6570\u5B57
the_barcode_length_is_not_12_digits=\u6761\u7801{0}\u957F\u5EA6\u4E0D\u662F12\u4F4D
batches_are_not_numbers=\u6279\u6B21{0}\u4E0D\u662F\u6570\u5B57
sub_processes_cannot_be_scrapped=\u5B50\u5DE5\u5E8F{0}\u4E0D\u53EF\u62A5\u5E9F
the_barcode_exists_in_the_document_in_preparation=\u6761\u7801\u5B58\u5728\u62DF\u5236\u4E2D\u7684\u5355\u636E
sn_scrap_is_not_allowed=\u6761\u7801\u72B6\u6001\u662F\u201C\u7EF4\u4FEE\u5B8C\u6210\u201D\u4E14\u7EF4\u4FEE\u7684\u6B21\u5C0F\u7C7B\u4E3A\u201C\u62A5\u5E9F\u201D\u624D\u5141\u8BB8\u62A5\u5E9F\uFF1A{0}
parameter_cannot_be_empty=\u6761\u7801,\u6279\u6B21\uFF0C\u62A5\u5E9F\u5927\u7C7B\uFF0C\u62A5\u5E9F\u5C0F\u7C7B\uFF0C\u62A5\u5E9F\u539F\u56E0\u63CF\u8FF0\uFF0C\u8D23\u4EFB\u90E8\u95E8\uFF0C\u7533\u8BF7\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
workshop_name_does_not_exist=\u8F66\u95F4\u540D\u79F0\u4E0D\u5B58\u5728
scrap_class_name_error=\u62A5\u5E9F\u5927\u7C7B\u540D\u79F0\u9519\u8BEF
subcard_barcode_cannot_be_imported=\u5B50\u5361\u7684\u6761\u7801\u4E0D\u80FD\u5BFC\u5165
job_number_does_not_exist=\u7533\u8BF7\u4EBA\u5DE5\u53F7\u4E0D\u5B58\u5728
import_details_verification_failed=\u5BFC\u5165\u660E\u7EC6\u6821\u9A8C\u5931\u8D25
sn_verification_failed=\u6761\u7801\u6821\u9A8C\u5931\u8D25
sn_has_been_packed_and_cannot_be_changed=\u6761\u7801\u5DF2\u88C5\u7BB1,\u4E0D\u5141\u8BB8\u6362\u7BB1,\u6240\u5728\u7BB1\u53F7\u662F\uFF1A{0}
cannot_mix_packing=\u6761\u7801\u7684\u6307\u4EE4\u662F\uFF1A{0}\uFF0C\u4E0D\u80FD\u6DF7\u7740\u88C5\u7BB1
current_factory_is_not_changsha_factory=\u5F53\u524D\u5DE5\u5382\u975E\u957F\u6C99\u5DE5\u5382
unknown_error_for_scan=\u6761\u7801\u626B\u63CF\u6821\u9A8C\u672A\u901A\u8FC7
appendix_info_save_failed=\u9644\u4EF6\u4FE1\u606F\u5B58\u50A8\u5931\u8D25
import_data_cannot_exceed=\u5BFC\u5165\u6570\u636E\u4E0D\u80FD\u8D85\u8FC7{0}\u6761
the_time_frame_cannot_be_greater_than_half_one_year=\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u5927\u4E8E\u534A\u5E74
current_document_information_not_found=\u672A\u627E\u5230\u5F53\u524D\u5355\u636E\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4!
the_current_status_of_the_document_is=\u5355\u636E\u5F53\u524D\u72B6\u6001\u4E3A{0},\u4E0D\u662F{1}\uFF0C\u8BF7\u786E\u8BA4
to_find_document_details=\u672A\u627E\u5230\u5355\u636E{0}\u660E\u7EC6\u4FE1\u606F
sub_operation_does_not_meet_scrap_barcode=\u6761\u7801{0}\u5B50\u5DE5\u5E8F\u4E0D\u6EE1\u8DB3\u62A5\u5E9F\u6761\u7801
approver_cannot_be_empty=\u6750\u6599\u6280\u672F\u8D28\u91CF\u5DE5\u7A0B\u5E08,\u4F9B\u5E94\u94FE\u8D22\u7ECF\u90E8\u8D22\u52A1,\u751F\u4EA7\u90E8\u90E8\u957F,\u5236\u9020\u603B\u7ECF\u7406\u4E0D\u80FD\u4E3A\u7A7A
approver_cannot_be_empty_two=\u8F66\u95F4\u4E3B\u4EFB,\u4F9B\u5E94\u94FE\u8D22\u7ECF\u90E8\u8D22\u52A1,\u751F\u4EA7\u90E8\u90E8\u957F,\u5236\u9020\u603B\u7ECF\u7406\u4E0D\u80FD\u4E3A\u7A7A
approver_cannot_be_empty_common=\u5BA1\u6279\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
failed_to_call_center_factory_interface=\u8C03\u4E2D\u5FC3\u5DE5\u5382\u65B0\u589E\u5BA1\u6279\u4FE1\u606F\u63A5\u53E3\u5931\u8D25:{0}
withdraw_approval_information=\u8C03\u4E2D\u5FC3\u5DE5\u5382\u64A4\u56DE\u5BA1\u6279\u4FE1\u606F\u63A5\u53E3\u5931\u8D25:{0}

get.uuid.info.failed=\u83B7\u53D6UUID\u4FE1\u606F\u5931\u8D25
sn.status.is.repair.scrap=\u6761\u7801{0}\u72B6\u6001\u4E3A \u62A5\u5E9F\u4E2D/\u5DF2\u62A5\u5E9F \u4E0D\u5141\u8BB8\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4
scrap.bill.no.status.is.not.approval=\u62A5\u5E9F\u7533\u8BF7\u5355\u72B6\u6001\u4E0D\u662F\u201C\u5BA1\u6279\u5B8C\u6210\u201D\uFF0C\u8BF7\u786E\u8BA4\uFF01
scrap.bill.no.not.exist=\u62A5\u5E9F\u5355\u636E\u53F7{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
scrap.bill.prod.plan.id.not.have.task.info=\u6279\u6B21{0}\u6CA1\u6709\u627E\u5230\u5BF9\u5E94\u7684\u4EFB\u52A1\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
feeder_in_use=feeder {0} \u6B63\u5728\u4F7F\u7528\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A
reel_id_in_use=reelId {0} \u6B63\u5728\u4F7F\u7528\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A
reel.id.item.check.info.not.exist=reelId {0} \u4E0D\u5B58\u5728\u6E05\u70B9\u8BB0\u5F55\uFF0C\u65E0\u6CD5\u6253\u5370\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.id.item.check.not.finish=reelId {0} \u8FD8\u672A\u6E05\u70B9\uFF0C\u65E0\u6CD5\u6253\u5370\uFF0C\u8BF7\u786E\u8BA4\uFF01
the_current_document_status_cannot_be_updated=\u5F53\u524D\u5355\u636E\u72B6\u6001\u4E3A{0},\u4E0D\u80FD\u66F4\u65B0\u4E3A{1}
prod.plan.id.and.task.no.all.empty=\u67E5\u8BE2/\u5BFC\u51FA\u65F6\u6279\u6B21\u548C\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u548CreelId\u4E0D\u80FD\u5168\u4E3A\u7A7A\uFF0C\u81F3\u5C11\u8F93\u5165\u5176\u4E2D\u4E00\u9879\uFF0C\u8BF7\u786E\u8BA4\uFF01
result.list.empty=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\u8F93\u5165\u7684\u67E5\u8BE2\u6761\u4EF6\u662F\u5426\u6B63\u786E\uFF01
inventory.information.not.found=\u6CA1\u6709\u627E\u5230\u6279\u6B21{0}\u7684\u6E05\u70B9\u4FE1\u606F
the_following_fields_cannot_be_empty=\u6D4B\u8BD5\u7C7B\u578B,\u6761\u7801,\u6D4B\u8BD5\u7ED3\u679C,\u6D4B\u8BD5\u65F6\u95F4, \u6D4B\u8BD5\u4EBA\u5458,\u6765\u6E90\u7CFB\u7EDF\u4E0D\u80FD\u4E3A\u7A7A
lfid.not.found.in.prod={0}\u6279\u6B21\u672A\u627E\u5230lfid\u4E3A{1}\u7684\u53D1\u6599\u8BB0\u5F55
reel.id.registered={0}reel id\u5DF2\u6CE8\u518C\uFF0C\u8BF7\u786E\u8BA4
record.not.found.with.barcode={0}\u6761\u7801\u5728{1}lfid\u672A\u627E\u5230\u53D1\u6599\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4
barcode.has.registered={0} lfid{1}\u6761\u7801\u5DF2\u5B8C\u6210\u6CE8\u518C\uFF0C\u4E0D\u5141\u8BB8\u518D\u6B21\u6CE8\u518C
the_power_module_must_pass_bimu=\u6D4B\u8BD5\u7C7B\u578B\u4E3A\u7535\u6E90\u6A21\u5757\u5FC5\u987B\u4F20imu
when_the_test_type_is_generic=\u6D4B\u8BD5\u7C7B\u578B\u4E3A\u901A\u7528\u65F6\u5FC5\u987B\u4F20\u5B50\u5DE5\u5E8F,\u5DE5\u7AD9
callback_to_write_microservice_failed=\u8C03\u56DE\u5199\u5FAE\u670D\u52A1\u5931\u8D25,Url:{0}
callback_to_write_microservice_failed_msg=\u8C03\u56DE\u5199\u5FAE\u670D\u52A1\u5931\u8D25,\u9519\u8BEF\u4FE1\u606F{0}
no_corresponding_site_information_found=\u672A\u627E\u5230{0}\u5BF9\u5E94\u7684\u5DE5\u7AD9\u4FE1\u606F
sn_item_batch_can_not_be_empty=\u6761\u7801,\u6279\u6B21,\u6599\u5355\u4EE3\u7801,\u7EF4\u62A4\u65F6\u95F4\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
information_corresponding_to_the_sub_process_not_found=\u672A\u627E\u5230\u5B50\u5DE5\u5E8F\u4EE3\u7801{0}\u5BF9\u5E94\u7684\u5DE5\u5E8F\u4FE1\u606F
get.bom.null=\u83B7\u53D6\u6599\u5355\u4EE3\u7801{0}\u7684BOM\u6570\u636E\u5931\u8D25
failed_to_obtain_the_corresponding_process_path=\u83B7\u53D6\u6279\u6B21\u6216\u8005\u6599\u5355\u4EE3\u7801{0}\u5BF9\u5E94\u5DE5\u827A\u8DEF\u5F84\u5931\u8D25
bill_of_material_code_does_not_exist=\u6599\u5355\u4EE3\u7801{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
no_sub_processes_to_control=\u8BE5\u6279\u6B21/\u6599\u5355\u6CA1\u6709\u9700\u8981\u7BA1\u63A7\u7684\u5B50\u5DE5\u5E8F,\u8BF7\u786E\u8BA4!
barcode_repeats=\u6761\u7801{0}\u91CD\u590D
sn_batch_exception_skip_information_has_been_maintained=\u6761\u7801{0}\u5BF9\u5E94\u7684\u6279\u6B21{1}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
sn_item_exception_skip_information_has_been_maintained=\u6761\u7801{0}\u5BF9\u5E94\u7684\u6599\u5355{1}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
batch_exception_skip_information_has_been_maintained=\u6279\u6B21{0}\u5BF9\u5E94\u7684\u6599\u5355{1}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
item_exception_skip_information_has_been_maintained=\u6599\u5355{0}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
barcode_has_maintained_exception_skip_information=\u6761\u7801{0}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
batch_has_maintained_exception_skip_information=\u6279\u6B21{0}\u5DF2\u7EF4\u62A4\u5F02\u5E38\u8DF3\u8FC7\u4FE1\u606F
there_is_no_test_record_for_the_barcode=\u6761\u7801{0}\u5728\u5B50\u5DE5\u5E8F {1} \u4E0D\u5B58\u5728\u6D4B\u8BD5\u8BB0\u5F55
barcode_test_failed=\u6761\u7801{0}\u5728\u5B50\u5DE5\u5E8F {1} \u6D4B\u8BD5\u4E0D\u901A\u8FC7,\u539F\u56E0\uFF1A{2}
barcode_test_failed_two=\u6761\u7801{0}\u5728\u5B50\u5DE5\u5E8F {1} \u6D4B\u8BD5\u4E0D\u901A\u8FC7
barcodes_are_not_the_same_batch=\u8F93\u5165\u7684\u6761\u7801\u4E0D\u662F\u540C\u4E00\u4E2A\u6279\u6B21\uFF0C\u8BF7\u786E\u8BA4
the_process_path_needs_to_have_warehousing=\u6307\u4EE4{0}\u5DE5\u827A\u8DEF\u5F84\u81F3\u5C11\u9700\u89812\u4E2A\u8282\u70B9\u4EE5\u4E0A,\u5E76\u4E14\u9700\u8981\u6709\u5165\u5E93
failed_to_get_the_corresponding_last_station_of_process_code=\u6D4B\u8BD5\u7BA1\u63A7:\u83B7\u53D6\u5B50\u5DE5\u5E8F{0}\u5BF9\u5E94\u7EBF\u4F53\u5EFA\u6A21\u7684\u6700\u540E\u5DE5\u7AD9\u5931\u8D25
repair.form.item.sn.null=\u7269\u6599\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
repair.form.repaired.sn.null=\u9001\u4FEE\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
repair_quantity_control={0}\u6761\u7801{1}\u4F4D\u53F7\u4E3A\u4FEE\u6B21\u6570\u5DF2\u8FBE\u5230\u9608\u503C\uFF0C\u4E0D\u5141\u8BB8\u9001\u4FEE
repair.form.replaced.sn.has.bound=\u66F4\u6362\u7684\u7269\u6599\u6761\u7801\u5B58\u5728\u7ED1\u5B9A\u5173\u7CFB
replace.sn.env.query.null.by.code.center=\u66F4\u6362\u7684\u7269\u6599\u6761\u7801\u5728\u6761\u7801\u4E2D\u5FC3\u67E5\u8BE2\u7684\u73AF\u4FDD\u5C5E\u6027\u4E3A\u7A7A
replace.sn.env.convert.error.by.dict=\u66F4\u6362\u7684\u7269\u6599\u6761\u7801\u901A\u8FC7\u6570\u636E\u5B57\u5178\u8F6C\u6362\u4E3A\u5177\u4F53\u73AF\u4FDD\u6570\u503C\u53D1\u751F\u9519\u8BEF
time.can.not.be.null=\u65E5\u671F\u5F00\u59CB\u65F6\u95F4\u548C\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
time.zone.cannot.span.thirty.days=\u65E5\u671F\u5F00\u59CB\u65F6\u95F4\u548C\u7ED3\u675F\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u8D85\u8FC730\u5929
time.zone.cannot.span.180.days=\u65E5\u671F\u5F00\u59CB\u65F6\u95F4\u548C\u7ED3\u675F\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u8D85\u8FC7180\u5929
reel_feeder_both_bound=reel id \u548C feeder \u90FD\u6709\u7ED1\u5B9A\u8BB0\u5F55
reel_feeder_both_replace=\u540C\u65F6\u66FF\u6362\u7684 reelId \u548C feeder \uFF0C\u90FD\u4E0D\u80FD\u6709\u7ED1\u5B9A\u8BB0\u5F55
the_same_line_can_only_start=\u540C\u4E00\u7EBF\u4F53\u53EA\u80FD\u5F00\u5DE5{0}\u6761{1}\u6307\u4EE4
the_same_line_body_can_only_start_one_command=\u5F53\u524D\u7EBF\u4F53\u5DF2\u5F00\u5DE5\u5176\u4ED6\u6307\u4EE4
return.error=\u6279\u6B21:{0},\u7269\u6599\u4EE3\u7801:{1},\u5DF2\u53D1\uFF1A{2}\uFF0C\u5DF2\u9000:{3},\u672C\u6B21\u9000\u6599:{4},\u4E0D\u80FD\u518D\u9000\u6599\u8BF7\u786E\u8BA4\uFF01
print.machine.is.null=\u6253\u5370\u673A\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
print.template.name.is.null=\u6253\u5370\u6A21\u677F\u540D\u79F0\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
call.interface.querymultibrandbyitemno.failure=\u67E5\u8BE2\u591A\u54C1\u724C\u4FE1\u606F\u5931\u8D25
print.ip.is.null=\u83B7\u53D6\u7684ip\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
bind_list_no_allow_both_process_station=\u7ED1\u5B9A\u6E05\u5355\u4E0D\u5141\u8BB8\u540C\u65F6\u7EF4\u62A4\u5B50\u5DE5\u5E8F\u548C\u5DE5\u7AD9
exists_process_setting=\u5B58\u5728\u6309\u5B50\u5DE5\u5E8F\u7EF4\u62A4\u7684\u6E05\u5355\uFF0C\u4E0D\u5141\u8BB8\u8BBE\u7F6E\u6309\u5DE5\u7AD9\u7EF4\u62A4
person_job_no_match={0}\u7EBF\u4F53\u6CA1\u6709\u5F00\u5DE5\u6240\u9700\u7684{1}\u5DE5\u5E8F\u5C97\u4F4D\u80FD\u529B\uFF0C\u4E0A\u5C97\u524D\u8BF7\u5148\u8BA4\u8BC1
job_person_no_match=\u5F00\u5DE5\u4EBA\u4E0D\u5177\u5907{1}\u5DE5\u5E8F\u5C97\u4F4D\u4E4B\u4E00\uFF0C\u6216\u672A\u5728{0}\u7EBF\u4F53\u6253\u5361
exists_work_station_setting=\u5B58\u5728\u6309\u5DE5\u7AD9\u7EF4\u62A4\u7684\u6E05\u5355\uFF0C\u4E0D\u5141\u8BB8\u8BBE\u7F6E\u6309\u5B50\u5DE5\u5E8F\u7EF4\u62A4
barcode.params.all.null=\u6761\u7801\u76F8\u5173\u53C2\u6570\u4E0D\u80FD\u5168\u4E3A\u7A7A\uFF01
sn.attr.info.is.null=\u8BE5\u6761\u7801\u672A\u505A\u8FC7\u6808\u677F\u88C5\u7BB1\u626B\u63CF\uFF0C\u4E0D\u652F\u6301\u8865\u6253
whole.barcode.of.sn.attr.info.is.null=\u627E\u4E0D\u5230en/mac\u5730\u5740\u5BF9\u5E94\u7684\u6574\u673A\u6761\u7801
whole.barcode.wip.info.null=\u6574\u673A\u6761\u7801\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
mail.send.err=\u90AE\u4EF6\u53D1\u9001\u9519\u8BEF
return.error.qty=reelId:{0},\u4EA4\u6613\u6307\u4EE4:{1},\u53D1\u6599\u6570\u91CF\uFF1A{2}\uFF0C\u9000\u6599\u6570\u91CF:{3},\u9000\u6599\u6570\u91CF\u5927\u4E8E\u53D1\u6599\u6570\u91CF\u4E0D\u80FD\u518D\u9000\u6599\u8BF7\u786E\u8BA4\uFF01
submit.scrap.bill.error=\u3010{0}\u3011\u6279\u6B21\u5355\u677F\u62A5\u5E9F\u5355\u63D0\u4EA4\u5931\u8D25,\u8BF7\u786E\u8BA4\u4FE1\u606F\u540E\u91CD\u8BD5
submit.scrap.bill.error.batch=\u9664\u3010{0}\u3011\u6279\u6B21\u5916,\u5176\u4F59\u6279\u6B21\u5355\u677F\u62A5\u5E9F\u5355\u63D0\u4EA4\u6210\u529F,\u8BF7\u786E\u8BA4\u4FE1\u606F\u540E\u91CD\u8BD5
lock.barcode.prodplanid.id.null=\u67E5\u8BE2\u6279\u6B21\u9501\u5B9A\u5355\u53F7\u65F6\uFF0C\u6279\u6B21\u4E3A\u7A7A
in.process.code.not.exist=\u67E5\u8BE2\u6279\u6B21\u9501\u5B9A\u5355\u53F7\u65F6\uFF0C\u5DE5\u5E8F\u7EC4\u4E3A\u7A7A
there_is_already_valid_machine_data_in_use={0}\u7AD9\u4F4D\u5DF2\u5B58\u5728\u6709\u6548\u673A\u53F0\u5728\u7528\u6570\u636E\uFF0C\u7981\u6B62\u79BB\u7EBF\u8F6C\u673A\u64CD\u4F5C
no_comprehensive_preparation={0}\u6CA1\u6709\u8FDB\u884C\u7EFC\u5408\u5907\u6599\uFF0C\u4E0D\u80FD\u8FDB\u884C\u8F6C\u673A\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4
failed_to_get_batch_first_command=\u83B7\u53D6\u6279\u6B21\u9996\u6307\u4EE4\u5931\u8D25
sn.has.been.bound.unbind.first=\u6761\u7801\u5DF2\u7ED1\u5B9A\uFF0C\u8BF7\u5148\u89E3\u7ED1
send.wip.ext.to.spm.lock=\u88C5\u914D\u5173\u7CFB\u6B63\u5728\u56DE\u5199
update_deal_status_failed=\u66F4\u65B0\u6280\u6539\u5355\u5904\u7406\u72B6\u6001\u5931\u8D25
email_content_for_techical_change=\u6280\u6539\u5355\u53F7\uFF1A{0},{1}\u5728PDM\u7CFB\u7EDF\u5C06\u6280\u6539\u5355\u53D1\u653E\uFF0C\u7CFB\u7EDF\u5DF2\u81EA\u52A8\u4E3A\u60A8\u5728iMES\u7CFB\u7EDF\u4E0A\u751F\u6210\u6280\u6539\u7BA1\u63A7\u5355\uFF0C\u5355\u53F7\u4E00\u81F4\u3002
email_content_for_techical_change_voided=\u6280\u6539\u5355\u53F7\uFF1A{0},{1}\u5728PDM\u7CFB\u7EDF\u5C06\u6280\u6539\u5355\u4F5C\u5E9F\uFF0C\u7CFB\u7EDF\u5DF2\u81EA\u52A8\u4E3A\u60A8\u5728iMES\u7CFB\u7EDF\u4E0A\u4F5C\u5E9F\u6B64\u6280\u6539\u7BA1\u63A7\u5355\u3002
email_content_for_techical_change_delete=\u6280\u6539\u5355\u53F7\uFF1A{0},{1}\u5728PDM\u7CFB\u7EDF\u5C06\u6280\u6539\u5355\u5220\u9664\uFF0C\u7CFB\u7EDF\u5DF2\u81EA\u52A8\u4E3A\u60A8\u5728iMES\u7CFB\u7EDF\u4E0A\u4F5C\u5E9F\u6B64\u6280\u6539\u7BA1\u63A7\u5355\u3002
techical_change_not_exits={0}\u6280\u6539\u5355\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\u6280\u6539\u5355\u53F7\u662F\u5426\u6B63\u786E
prodplanid_not_equal=\u6279\u6B21\u5BFC\u5165\u4E0E\u754C\u9762\u4E0D\u4E00\u81F4
techical_change_not_equal=\u6280\u6539\u5355\u53F7\u5BFC\u5165\u4E0E\u754C\u9762\u4E0D\u4E00\u81F4
techical_change_is_null=\u6280\u6539\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
techical_change_be_one=\u53EA\u80FD\u5BFC\u5165\u540C\u4E00\u4E2A\u6280\u6539\u5355\u7684\u6570\u636E
prodplanid_change_be_one=\u53EA\u80FD\u5BFC\u5165\u540C\u4E00\u4E2A\u6279\u6B21\u7684\u6570\u636E
techical_change_unlocked={0}\u6280\u6539\u5355\u5DF2\u89E3\u9501\uFF0C\u8BF7\u786E\u8BA4\u6280\u6539\u5355\u53F7\u662F\u5426\u6B63\u786E
the_current_document_cannot_be_unlocked=\u5F53\u524D\u5355\u636E\u4E0D\u53EF\u89E3\u9501
the_offline_flag_sn_verification_failed=\u5F53\u524D\u6761\u7801\u4E0D\u6EE1\u8DB3\u975E\u5728\u7EBF\u6761\u4EF6\uFF0C\u6761\u7801\u5E94\u5F53\u4E0D\u5728wip_info\u4E2D\u6216\u5B50\u5DE5\u5E8F\u4E3A\u5165\u5E93
Failed_obtain_barcode_item_no=\u83B7\u53D6\u6761\u7801\u5BF9\u5E94\u6599\u5355\u4FE1\u606F\u5931\u8D25
unlock.process.must.be.queried.with.time=\u89E3\u9501\u5DE5\u5E8F\u987B\u914D\u5408\u89E3\u9501\u65F6\u95F4\u4E00\u8D77\u67E5\u8BE2
remark.can.not.be.null=\u9A73\u56DE\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
must.enter.a.query.criteria=\u5FC5\u987B\u8F93\u5165\u4E00\u4E2A\u67E5\u8BE2\u6761\u4EF6
sn.technical.control.error={0} \u6761\u7801\u6280\u6539\u672A\u5B8C\u6210\uFF0C\u6280\u6539\u5355\u53F7 {1};
prod.technical.control.error={0} \u6279\u6B21\u6280\u6539\u672A\u5B8C\u6210\uFF0C\u6280\u6539\u5355\u53F7 {1};
technical.exec.info.existed={0}\u8BE5\u6761\u7801\u5DF2\u7ECF\u53CD\u9988\u6280\u6539\uFF0C\u65E0\u9700\u518D\u6B21\u53CD\u9988
technical.exec.info.error=\u63D0\u4EA4\u626B\u63CF\u6570\u636E\u5F02\u5E38
the_chg_req_no_is_not_found=\u672A\u627E\u5230\u8BE5\u6280\u6539\u5355\u53F7\u4FE1\u606F
the_chg_req_no_and_prod_plan_id_is_exist=\u8BE5\u6280\u6539\u5355\u53F7\u8BE5\u6279\u6B21\u5DF2\u5B58\u5728\u5F02\u5E38\u8DF3\u8FC7\u7EF4\u62A4\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
only_submitted_documents_can_perform_this_operation=\u53EA\u6709\u5DF2\u63D0\u4EA4\u72B6\u6001\u7684\u5355\u636E\u53EF\u4EE5\u6267\u884C\u8BE5\u64CD\u4F5C
technical_transformation_sheet_is_being_processed=\u5F53\u524D\u6280\u6539\u5355\u6B63\u5728\u5904\u7406\u4E2D
task.tree.lost=\u5B50\u5361\u4EFB\u52A1\u4FE1\u606F\u83B7\u53D6\u5931\u8D25
statistics.in.progress=\u5DF2\u5B8C\u6210\u5355\u677F\u6D4B\u8BD5\u5408\u683C\u7387\u7EDF\u8BA1
prodplan.statistics.in.progress=\u5F53\u524D\u6279\u6B21\u6B63\u5728\u7EDF\u8BA1\u4E2D
real.time.prodplanid.cant.null=\u5B9E\u65F6\u67E5\u8BE2\u65F6\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
not.real.time.date.cant.null=\u975E\u5B9E\u65F6\u67E5\u8BE2\u65F6\u9996\u4EF6\u5165\u5E93\u65F6\u95F4\u4E0E\u6279\u6B21\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
not.real.time.date.max.one.year=\u975E\u5B9E\u65F6\u67E5\u8BE2\u65F6\u9996\u4EF6\u5165\u5E93\u65E5\u671F\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E00\u5E74
not.find.route=\u672A\u627E\u5230\u5DE5\u827A\u4FE1\u606F
not.find.item.ver.no=\u672A\u627E\u5230\u7248\u672C\u4FE1\u606F
failed_to_get_preview_link=\u83B7\u53D6\u6587\u4EF6\u9884\u89C8\u8FDE\u63A5\u5931\u8D25{0}
failed_to_generate_preview_header=\u751F\u6210\u6587\u4EF6\u9884\u89C8\u9274\u6743\u5934\u4FE1\u606F\u5931\u8D25
fail_to_upload_file=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
transfer.work.order.status.error={0}\u6307\u4EE4\u72B6\u6001\u975E'\u5DF2\u5F00\u5DE5','\u5DF2\u63D0\u4EA4','\u6302\u8D77'\uFF0C\u4E0D\u5141\u8BB8\u8F6C\u673A\u626B\u63CF
no_tech_chg_detail=\u6CA1\u6709\u6280\u6539\u8BE6\u7EC6\u4FE1\u606F
no_pdm_tech_chg_info=\u6CA1\u6709PDM\u6280\u6539\u4FE1\u606F
plan_lock_not_exist=\u6279\u6B21\u9501\u5B9A\u5355\u4E0D\u5B58\u5728
tec_chg_not_in_preparation=\u6280\u6539\u5355\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D
task_no_tec_chg_craft=\u6279\u6B21{0}\u672A\u9009\u62E9\u7BA1\u63A7\u5DE5\u5E8F
chg_req_base_is_null=\u6280\u6539\u57FA\u672C\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
pre_chg_file_null=\u8BF7\u4E0A\u4F20\u63D0\u524D\u6280\u6539\u6587\u4EF6
tec_chg_email_send_null=\u6280\u6539\u901A\u77E5\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
tec_chg_can_not_submit=\u5F53\u524D\u72B6\u6001\u4E0D\u80FD\u63D0\u4EA4\u6280\u6539
tec_chg_file_is_null=\u8BF7\u4E0A\u4F20\u6280\u6539\u6587\u4EF6
task_exceed_threshold=\u6280\u6539\u672A\u5B8C\u5DE5\u6279\u6B21\u4E0D\u80FD\u8D85\u8FC7\u9608\u503C
work_order_not_finished=\u6307\u4EE4{0} \u672A\u5B8C\u5DE5\uFF0C\u4E0D\u5141\u8BB8\u89E3\u7ED1\uFF0C\u8BF7\u66F4\u6362feeder

get.boardonline.info.failed = \u83B7\u53D6board_online\u4FE1\u606F\u5931\u8D25
get.barsubmit.info.failed = \u83B7\u53D6barsubmit\u4FE1\u606F\u5931\u8D25
trans.no.is.empty=\u8F6C\u4EA4\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
trans.no.not.exist={0}\u8F6C\u4EA4\u5355\u53F7\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
trans.no.allowed.box.ok={0}\u8F6C\u4EA4\u5355\u5DF2\u5B8C\u6210\u88C5\u7BB1\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.not.in.bill.no=\u6761\u7801{0}\u4E0D\u5C5E\u4E8E{1}\u8F6C\u4EA4\u5355\uFF0C\u8BF7\u786E\u8BA4
sn.not.trans.to.here=\u6761\u7801{0}\u672A\u8F6C\u4EA4\u81F3\u672C\u5DE5\u5E8F\uFF0C\u8BF7\u786E\u8BA4
sn.boxed=\u6761\u7801{0}\u5DF2\u88C5\u7BB1
bill.submit.ing=\u7BB1\u53F7{0}\u6B63\u5728\u8F6C\u4EA4\u5355{1}\u88C5\u7BB1
isRepair_update_failed = \u6761\u7801\u8FD4\u4FEE\u72B6\u6001\u66F4\u65B0\u5931\u8D25
check.technical.detail = \u8BF7\u67E5\u770B{0}\u6280\u6539\u6216\u6761\u7801\u9501\u5B9A\u6587\u4EF6\u53CA\u6280\u6539\u7269\u6599\u6B63\u786E\u6027!
sn.flow.ctrl.failed=\u6761\u7801{0}\u6D41\u7A0B\u7BA1\u63A7\u6821\u9A8C\u672A\u901A\u8FC7\uFF0C\u8BF7\u68C0\u67E5
someone_is_currently_exporting=\u5F53\u524D\u6B63\u5728\u5BFC\u51FA\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5

parameter_fields_not_configured=\u5B50\u6A21\u5757\u5BF9\u5E94\u4E3B\u6A21\u5757\u7684\u5B57\u6BB5\u7C7B\u578B\u4E3A\u57FA\u7840\u7C7B\u578B\u65F6\uFF0C\u672A\u914D\u7F6E\u53C2\u6570\u5B57\u6BB5
field_mapping_not_configured=\u672A\u914D\u7F6E\u5B57\u6BB5\u6620\u5C04
unsupported_data_acquisition_method=\u4E0D\u652F\u6301\u7684\u6570\u636E\u83B7\u53D6\u65B9\u5F0F
no_microservices_configured=\u672A\u914D\u7F6E\u5FAE\u670D\u52A1
no_reflection_method_configured=\u672A\u914D\u7F6E\u53CD\u5C04\u65B9\u6CD5
reflection_method_configuration_error=\u53CD\u5C04\u65B9\u6CD5\u914D\u7F6E\u9519\u8BEF
sql_not_configured=sql\u672A\u914D\u7F6E
sub_module_not_configured=\u5B50\u6A21\u5757\u672A\u914D\u7F6E
no_associated_fields=\u65E0\u5173\u8054\u5B57\u6BB5
unsupported_association_field_type=\u4E0D\u652F\u6301\u7684\u5173\u8054\u5B57\u6BB5\u7C7B\u578B

no.test.work.order=\u65E0\u6D4B\u8BD5\u4E3B\u5DE5\u5E8F\u6307\u4EE4
concise.daily.scheduled.task.failed=\u7B80\u660E\u65E5\u62A5\u5B9A\u65F6\u4EFB\u52A1\u6267\u884C\u5931\u8D25\uFF01\u672C\u6B21\u6279\u6B21\u4E3A\uFF1A{0}, \u9875\u6570\u4E3A\uFF1A{1}\u3002
dqas.control.error=\u4E2D\u8BD5\u63A5\u53E3\u5DE5\u5E8F\u7BA1\u63A7\u4E0D\u901A\u8FC7\uFF0C\u6761\u7801:{0}
warehouse.submit.running=\u4EFB\u52A1{0}\u6B63\u5728\u63D0\u4EA4\u5165\u5E93\u5355\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
warehouse.submit.error.msg={0}\u5DF2\u9886\u7528\u5230\u4E3B\u4EFB\u52A1\u6570\u91CF\u4E3A:{1}\uFF0C\u8FD8\u5DEE{2}\u5757\uFF0C\u8BF7\u5148\u5904\u7406\u518D\u63D0\u4EA4\u5165\u5E93;
whether_the_file_is_encrypted=\u9000\u6599\u6587\u4EF6\u89E3\u6790\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4\u6587\u4EF6\u662F\u5426\u5DF2\u52A0\u5BC6
file_has_no_data=\u6587\u4EF6\u6CA1\u6709\u6570\u636E
there_is_an_empty_reelid=\u6587\u4EF6\u5B58\u5728reelid\u4E3A\u7A7A\u7684\u6570\u91CF,\u8BF7\u786E\u8BA4\uFF01
reelid.not.exist.pk.code=\u6599\u76D8\u5728imes\u4E0D\u5B58\u5728,\u8BF7\u5148\u6CE8\u518C:{0}
failed_to_obtain_task_information=\u83B7\u53D6\u6279\u6B21{0}\u5BF9\u5E94\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25
failed_to_get_batch_information=\u83B7\u53D6reelid:{0}\u5BF9\u5E94\u6279\u6B21\u4FE1\u606F\u5931\u8D25
failed_to_add_material_return_data=\u65B0\u589Ereelid {0}\u9000\u6599\u6570\u636E\u5931\u8D25,\u8BF7\u91CD\u8BD5!
the_reelid_of_production_line_splitting={0}\u4E3A\u4EA7\u7EBF\u62C6\u5206\u51FA\u6765\u7684reelid,\u8BF7\u9000\u592E\u4ED3\uFF01
the_latest_returned_material_quantity_is_abnormal=\u9000\u6599\u6570\u91CF\u4E3A\u7A7A,\u4E0D\u80FD\u9000\u6599,\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u5904\u7406!
not_the_material_from_the_line_side_warehouse={0}\u6CA1\u6709\u7EBF\u8FB9\u4ED3\u51FA\u5E93\u4EA4\u6613,\u4E0D\u662F\u7EBF\u8FB9\u4ED3\u53D1\u51FA\u7684\u6599,\u4E0D\u5141\u8BB8\u9000\u7EBF\u8FB9\u4ED3,\u8BF7\u9000\u592E\u4ED3\uFF01
the_instruction_is_abnormal_completion={0}\u6307\u4EE4\u4E3A\u975E\u6B63\u5E38\u5B8C\u5DE5,\u4E0D\u80FD\u9000\u6599,\u8BF7\u627E\u4E1A\u52A1\u4EE3\u8868\u5904\u7406\uFF01
concise.daily.query.page.or.row.illegal=\u7B80\u660E\u65E5\u62A5\u5FC5\u987B\u5206\u9875\u67E5\u8BE2\uFF0C\u8BF7\u786E\u8BA4\u662F\u5426\u6709\u5206\u9875\u53C2\u6570\uFF01
sys.look.config.error=\u7B80\u660E\u65E5\u62A5\u6570\u636E\u5B57\u51781003010\u914D\u7F6E\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.exist.scrap=\u6761\u7801{0}\u5B58\u5728\u7EF4\u4FEE\u6B21\u5C0F\u7C7B\u4E3A\u62A5\u5E9F\u7684\u8BB0\u5F55\uFF0C\u4E0D\u80FD\u7EE7\u7EED\u9886\u6599\uFF01

the_number_of_trays_is_zero=\u6599\u76D8{0}\u6570\u91CF\u4E3A\u7A7A\u6216\u8005\u4E3A0,\u4E0D\u80FD\u9000\u6599,\u8BF7\u786E\u8BA4\uFF01
material_preparation_in_advance_exists=\u6599\u76D8{0}\u5B58\u5728\u63D0\u524D\u5907\u6599,\u4E0D\u80FD\u9000\u6599\uFF0C\u8BF7\u786E\u8BA4
material_the_machine_is_in_use=\u6599\u76D8{0}\u5B58\u5728\u673A\u53F0\u5728\u7528\u4FE1\u606F\u6216\u8005\u662F\u7EED\u6599\u76D8,\u4E0D\u80FD\u9000\u6599\uFF0C\u8BF7\u786E\u8BA4
the_upper_and_lower_thresholds_maintained_are_not_integers=\u7EF4\u62A4\u7684\u4E0A\u4E0B\u9608\u503C\u975E\u6574\u6570
email.technical.notice=\u6280\u6539\u5355\u53F7\uFF1A{0}\uFF0C\u7B2C{1}\u6B21\u53D1\u653E\uFF0C\u6279\u6B21\u6709\uFF1A{2}\uFF0C\u8BF7\u6CE8\u610F\u67E5\u6536\u3002
issuance.crafsection.is.null=\u6280\u6539\u6587\u4EF6\u4E0B\u53D1\u5DE5\u5E8FissuanceCraftSection\u4E3A\u7A7A
lock.prodplan.msg=\u8BF7\u67E5\u770B{0}\u6279\u6B21\u9501\u5B9A\u4FE1\u606F
task.qty.sufficient=\u5F53\u524D\u4EFB\u52A1\u6570\u91CF{0}\u5C0F\u4E8E\u7B49\u4E8E\u5DF2\u5165\u5E93\u6570\u91CF{1}\u4E0D\u80FD\u518D\u63D0\u4EA4\u5165\u5E93
task.qty.sufficient.msg=\u5F53\u524D\u4EFB\u52A1\u6570\u91CF{0}\u5DF2\u5165\u5E93\u6570\u91CF{1},\u6700\u5927\u5165\u5E93\u6570\u91CF\u4E3A{2}\u8BF7\u786E\u8BA4
bill.type.unsupported.submit=\u5F53\u524D\u5355\u636E\u7C7B\u578B\uFF1A{0} \u4E0D\u652F\u6301\u63D0\u4EA4\u5165\u5E93\u8BF7\u786E\u8BA4!
out.source.unsupported=\u5F53\u524D\u4EFB\u52A1\u975E\u5916\u534F\u4EFB\u52A1\uFF0C\u4E0D\u80FD\u63D0\u4EA4\u5916\u534F\u5165\u5E93\u5355
bill.submitted.error=\u5355\u636E\u53F7{0}\u5DF2\u7ECF\u63D0\u4EA4\u5165\u5E93\u4E0D\u80FD\u518D\u63D0\u4EA4,\u8BF7\u786E\u8BA4!
the_thread_body_is_switching_with_one_button=\u5F53\u524D\u7EBF\u4F53\u6B63\u5728\u4E00\u952E\u5207\u6362\uFF0C\u8BF7\u4E0D\u8981\u91CD\u590D\u70B9\u51FB
submit.qty.is.null=\u63D0\u4EA4\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
trace_type_has_no_work_order=\u5F53\u524D\u8FFD\u6EAF\u7C7B\u578B\u6CA1\u6709\u5BF9\u5E94\u6307\u4EE4\u4FE1\u606F\uFF0C\u6CA1\u6709\u8FFD\u6EAF\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4
submit.qty.not.integer=\u63D0\u4EA4\u6570\u91CF\u53EA\u80FD\u662F\u6B63\u6574\u6570
export.scan.history.miss.params = \u53C2\u6570\u7F3A\u5931,\u9009\u62E9\u5BFC\u51FA\u6761\u7801\u626B\u63CF\u5386\u53F2\u65F6, \u5355\u677F\u6761\u7801\u3001\u6279\u6B21\u3001\u6307\u4EE4\u8BF7\u81F3\u5C11\u8F93\u5165\u4E00\u9879
the.exported.data.cannot.be.larger.than.100000 = \u5BFC\u51FA\u7684\u6570\u636E\u4E0D\u80FD\u8D85\u8FC710\u4E07\u6761
export.down.max.limit=\u5F53\u524D\u5DE5\u53F7\u6B63\u5728\u4E0B\u8F7D \u6216\u8005 \u6587\u4EF6\u540C\u65F6\u4E0B\u8F7D\u8D85\u8FC7\u6700\u5927\u8BBE\u7F6E\u503C,\u8BF7\u7A0D\u540E\u518D\u6267\u884C\u5BFC\u51FA
search.scan.history.info.is.null=\u6CA1\u6709\u67E5\u8BE2\u5230\u8BE5\u6761\u4EF6\u4E0B\u7684\u6761\u7801\u626B\u63CF\u5386\u53F2\u4FE1\u606F\uFF0C\u4E0D\u6267\u884C\u5BFC\u51FA
prodplan.new.sn.ing=\u6279\u6B21{0}\u6B63\u5728\u751F\u6210\u6761\u7801\u4E2D
please_return_to_the_central_warehouse=\u57CE\u5821\u5361\u4E3B\u5B50\u5361\u5171\u7528\u6599\u8BF7\u9000\u592E\u4ED3
submit.by.work.maxSize=\u6309\u7BB1\u8F6C\u4EA4\u6700\u5927\u652F\u6301{0}\u4E2A\u4E0D\u540C\u6307\u4EE4\u4E00\u8D77\u8F6C\u4EA4
box.contents.are.empty.params=\u7BB1{0}\u5185\u5BB9\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4!
query.box.order.error=\u6307\u4EE4\u4FE1\u606F{0}\u4E0D\u5B58\u5728
workOrder.line.code.empty=\u6307\u4EE4\u7EBF\u4F53{0}\u4E3A\u7A7A\uFF01
work.next.diff=\u5F53\u524D\u6307\u4EE4{0}\u548C\u6307\u4EE4{1}\u4E0B\u5DE5\u5E8F\u4E0D\u4E00\u81F4\u4E0D\u80FD\u4E00\u8D77\u8F6C\u4EA4!
work.submitting.error=\u5F53\u524D\u6307\u4EE4{0}\u6B63\u5728\u8F6C\u4EA4\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5!
kangxun.work.order.diff=\u6307\u4EE4{0} \u5F53\u524D\u5DE5\u5E8F\u4E0D\u662F\u5EB7\u8BAF\u534A\u6210\u54C1\u5E93\uFF0C\u4E0D\u80FD\u548C\u6307\u4EE4{1}\u4E00\u8D77\u63D0\u4EA4!
sn.regular.error=\u5DF2\u4E0B\u6761\u7801{0}\u4E0D\u662F12\u4F4D\u6570\u5B57/\u8D85\u51FA\u4EFB\u52A1\u6570\u91CF/\u4E0D\u5C5E\u4E8E\u5F53\u524D\u6279\u6B21\uFF0C\u8BF7\u786E\u8BA4!
process.code.workOrderNo.not.exist=\u6279\u6B21{0}\u4E0D\u5B58\u5728\u5305\u542B{1}\u5DE5\u5E8F\u7684\u6307\u4EE4!
need.one.day.in.the.past=\u9009\u62E9\u7684\u65E5\u671F\u53EA\u80FD\u4E3A\u8FC7\u53BB\u7684\u4E00\u5929
technical.lock.msg=\u6280\u6539\u9501\u5B9A\u5355\u53F7\uFF1A{0}
spi_not_pass=\u6761\u7801{0}\u7684SPI\u68C0\u6D4B\u7ED3\u679C\u4E0D\u901A\u8FC7\u6216\u4E0D\u5B58\u5728
aoi_not_pass=\u6761\u7801{0}\u5728\u5DE5\u7AD9{1}\u7684AOI\u68C0\u6D4B\u7ED3\u679C\u4E0D\u901A\u8FC7\u6216\u4E0D\u5B58\u5728
station_not_exit=\u5DE5\u7AD9{0}\u4E0D\u5B58\u5728
please.check.params=\u53C2\u6570\u7F3A\u5931\uFF0C\u8BF7\u786E\u8BA4\u6279\u6B21\u548C\u5B50\u5DE5\u5E8F\u7B49\u662F\u5426\u5DF2\u4F20\u5165
the.task.is.aux.scanning=\u5F53\u524D\u6279\u6B21\u6B63\u5728\u8FDB\u884C\u8BE5\u5B50\u5DE5\u5E8F\u8F85\u52A9\u626B\u63CF\uFF0C\u8BF7\u7A0D\u540E\u6267\u884C
not.found.aux.process=\u6CA1\u6709\u627E\u5230\u8BE5\u5B50\u5DE5\u5E8F\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\u662F\u5426\u7EF4\u62A4
aux.qty.is.exceed=\u8F93\u5165\u7684\u672C\u6B21\u7D22\u916C\u6570\u91CF+\u5DF2\u7D22\u916C\u6570\u91CF>\u8BA1\u5212\u6570\u91CF,\u8BF7\u786E\u8BA4
get.aux.sys.failed=\u8BF7\u68C0\u67E5\u6570\u636E\u5B57\u517828650006\u662F\u5426\u914D\u7F6E\u5B8C\u6574
submit.outsource.forbidden=\u8BE5\u5916\u534F\u4EFB\u52A1\u4E0D\u652F\u6301\u5728\u6B64\u9875\u9762\u63D0\u4EA4\uFF0C\u8BF7\u53BB\u5916\u534F\u5165\u5E93\u5355\u9875\u9762\u63D0\u4EA4!
submit.outsource.forbidden.two=\u8BE5\u5916\u534F\u4EFB\u52A1\u4E0D\u652F\u6301\u5728\u6B64\u9875\u9762\u63D0\u4EA4\uFF0C\u8BF7\u53BB\u5165\u5E93\u5355\u9875\u9762\u63D0\u4EA4!
submit.outsource.forbidden.three=\u5916\u534F\u5355\u636E\u7C7B\u578B\u4E0D\u80FD\u5728\u6B64\u9875\u9762\u63D0\u4EA4\uFF0C\u8BF7\u5207\u6362\u5355\u636E\u7C7B\u578B\u518D\u63D0\u4EA4!
creation_time_cannot_be_empty=\u521B\u5EFA\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A\u5E76\u4E14\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E00\u5E74
query_time_cannot_span_one_year=\u67E5\u8BE2\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC71\u5E74
query_param_person_needs_to_cooperate_with_time=\u53D8\u66F4\u4EBA\u9700\u8981\u914D\u5408\u53D8\u66F4\u65F6\u95F4\u4E00\u8D77\u67E5\u8BE2
only_itemNo_or_prodplanId_can_queried_separately=\u53EA\u6709\u6599\u5355\u4EE3\u7801\u3001\u6279\u6B21\u53EF\u5355\u72EC\u67E5\u8BE2
currently_exporting=\u5F53\u524D\u6B63\u5728\u5BFC\u51FA\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
params.over.length=\u6279\u6B21\u6216\u6761\u7801\u6570\u91CF\u8D85\u9650\uFF0C\u8BF7\u786E\u8BA4
wip.ext.exist=\u88C5\u914D\u5173\u7CFB{0}\u5DF2\u5728imes\u5B58\u5728\uFF0C\u5171{1}\u6761\u672A\u5199\u5165\u6570\u636E\uFF0C\u8BF7\u77E5\u6089
max.query.size=\u6700\u5927\u67E5\u8BE2\u53C2\u6570\u6570\u91CF\u4E3A500\uFF0C\u8BF7\u5220\u51CF\u67E5\u8BE2\u6570\u91CF
update.warehouse.date.in=\u6B63\u5728\u4FEE\u6539\u6279\u6B21\u9996\u4EF6\u5165\u5E93\u65E5\u671F\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
sn.exist.factory={0}\u6761\u7801\u4E0D\u5728\u672C\u5DE5\u5382\u52A0\u5DE5\uFF0C\u8BF7\u5207\u6362\u81F3{1}\u53CD\u9988\u6280\u6539\u7ED3\u679C!
sn.rule.error={0}\u6761\u7801\u4E0D\u7B26\u5408\u6761\u7801\u89C4\u5219\uFF0C\u8BF7\u68C0\u67E5!
operation_cannot_exceed_1000=\u9AD8\u7EA7\u65E5\u62A5\u6574\u673A\u5DE5\u5E8F\u4E0D\u80FD\u8D85\u8FC71000
data_volume_exceeds_5_w=\u6570\u636E\u91CF\u8D85\u8FC75W\uFF0C\u5DF2\u90AE\u4EF6\u5BFC\u51FA\uFF0C\u8BF7\u5173\u6CE8\u90AE\u4EF6\u4FE1\u606F
bill_no_not_exist=\u4EE5\u4E0B\u5355\u636E\u53F7\u4E0D\u5B58\u5728\uFF1A{0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.chg.exist={0} \u6280\u6539\u5355\u4E0B{1} \u5DF2\u7ECF\u53CD\u9988\u6280\u6539\uFF0C\u65E0\u9700\u518D\u6B21\u53CD\u9988;
chg.submit.error={0}\u5F53\u524D\u5355\u636E\u6B63\u5728\u64CD\u4F5C,\u8BF7\u7A0D\u540E\u518D\u8BD5!
sys.lookUpType.221103.not.configure=\u6570\u636E\u5B57\u5178221103\u672A\u914D\u7F6E
print.scenes.not.get=\u6CA1\u6709\u83B7\u53D6\u5230\u751F\u4EA7\u573A\u666F
print.scenes.unsupported=\u4E0D\u652F\u6301\u7684\u751F\u4EA7\u573A\u666F
quantity.should.not.less.than1=\u6570\u91CF\u4E0D\u5E94\u5C0F\u4E8E1
wrong.sn.type=\u6253\u7801\u7C7B\u578B\u6709\u8BEF
small.sn.not.allowed.in.first.instruction=\u7B2C\u4E00\u9762\u6307\u4EE4\u4E0D\u5141\u8BB8\u6253\u5C0F\u677F\u7801\uFF0C\u8BF7\u786E\u8BA4
big.sn.not.allowed.in.second.instruction=\u7B2C\u4E8C\u9762\u6307\u4EE4\u4E0D\u5141\u8BB8\u6253\u5927\u677F\u7801\uFF0C\u8BF7\u786E\u8BA4
big.sn.not.allowed.in.first.instruction=\u7B2C\u4E00\u9762\u6307\u4EE4\u4E0D\u5141\u8BB8\u6253\u5927\u677F\u7801\uFF0C\u8BF7\u786E\u8BA4
sn.not.allowed.in.second.instruction=\u7B2C\u4E8C\u9762\u6307\u4EE4\u4E0D\u5141\u8BB8\u6253\u7801\uFF0C\u8BF7\u786E\u8BA4
incoming.sn.null=\u672A\u4F20\u5927\u677F\u6761\u7801\uFF0C\u8BF7\u68C0\u67E5\u6253\u5370\u573A\u666F\u662F\u5426\u6B63\u786E
incoming.sn.not.null=\u4F20\u5165\u4E86\u5927\u677F\u6761\u7801\uFF0C\u8BF7\u68C0\u67E5\u6253\u5370\u573A\u666F\u662F\u5426\u6B63\u786E
parent.sn.null=\u4F20\u5165\u7684\u5C0F\u677F\u6761\u7801\u6CA1\u6709\u5927\u677F\u6761\u7801\uFF0C\u8BF7\u68C0\u67E5
has.printed.cannot.repeat={0}\u5927\u677F\u6761\u7801\u5DF2\u6253\u5370\u8FC7\u5C0F\u677F\u6761\u7801\uFF0C\u4E0D\u80FD\u91CD\u590D\u6253\u5370
workorder.has.record.printing.failure={0}\u6307\u4EE4\u5B58\u5728\u6253\u5370\u5931\u8D25\u8BB0\u5F55{1}\uFF0C\u8BF7\u786E\u8BA4
craft.not.contain.current=\u5DE5\u827A\u8DEF\u5F84\u4E0D\u5305\u542B\u5F53\u524D\u5DE5\u5E8F\uFF0C\u8BF7\u68C0\u67E5
no.chinese.name.field.value.was.obtained.for.the.product=\u672A\u83B7\u53D6\u5230\u4EA7\u54C1\u4E2D\u6587\u540D\u5B57\u6BB5\u503C
the.english.code.field.for.the.latest.version.of.pdm.has.not.been.obtained=\u672A\u83B7\u53D6\u5230PDM\u6700\u65B0\u7248\u672C\u7684\u82F1\u6587\u4EE3\u53F7\u5B57\u6BB5
the.chinese.name.of.the.product.does.not.match.the.english.code.of.pdm=\u4EA7\u54C1\u4E2D\u6587\u540D\u4E0EPDM\u7684\u82F1\u6587\u4EE3\u53F7\u4E0D\u4E00\u81F4
non.machine.219.barcode.cannot.be.bound.to.network.access.certificate=\u975E\u6574\u673A219\u6761\u7801\u4E0D\u80FD\u8FDB\u884C\u5165\u7F51\u8BC1\u7ED1\u5B9A
not.smt.craft=\u975Esmt\u5DE5\u5E8F\uFF0C\u8BF7\u68C0\u67E5
not.find.unbound.big.sn=\u6CA1\u6709\u627E\u5230\u672A\u7ED1\u5B9A\u7684\u5927\u677F\u7801
not.find.unbound.fixture.sn=\u6CA1\u6709\u627E\u5230\u672A\u7ED1\u5B9A\u7684\u5939\u5177\u7801
not.find.unbound.big.sn.by.fixture=\u6839\u636E\u5939\u5177\u7801\u6CA1\u6709\u627E\u5230\u5927\u677F\u7801
not.find.workorder.by.line.prodplanid=\u6839\u636E\u7EBF\u4F53\u6279\u6B21\u6CA1\u6709\u627E\u5230\u5DF2\u5F00\u5DE5\u7684\u6307\u4EE4
sn.not.found.in.sn.assign.table=\u5728\u6761\u7801\u5206\u914D\u8868\u6CA1\u6709\u627E\u5230\u6761\u7801
get.carft.null=\u672A\u67E5\u8BE2\u5230\u6307\u4EE4\u7684\u5DE5\u827A\u8DEF\u5F84
maximum.of.batches.reached=\u5DF2\u8FBE\u5230\u6279\u6B21\u6700\u5927\u6570\u91CF
sn.not.feed.back={0}\u6761\u7801\u672A\u53CD\u9988\u6280\u6539\u7ED3\u679C\uFF0C\u65E0\u9700QC\u786E\u8BA4!
sn.feed.back.complete={0}\u6761\u7801\u5DF2\u5B8C\u6210\u6280\u6539\u786E\u8BA4!
submit.outsource.qty.sub=\u5F53\u524D\u6279\u6B21:{0},\u7D2F\u52A0\u5F53\u524D\u5165\u5E93\u603B\u6570:{1},\u5DF2\u6CE8\u518C\u6761\u7801\u6570\u91CF\uFF1A{2},\u4E0D\u80FD\u63D0\u4EA4\u5165\u5E93!
verNo.has.set.the.printing.scene=pcb\u7248\u672C{0}\u5DF2\u8BBE\u7F6E\u6253\u5370\u573A\u666F\uFF0C\u8BF7\u786E\u8BA4
verNo.is.null=pcb\u7248\u672C\u4E3A\u7A7A
verNo.not.exist=pcb\u7248\u672C\u4E0D\u5B58\u5728
lpn.and.original.lpn.all.null=\u6808\u677F\u53F7\u548C\u4E2D\u8BD5\u7BB1\u53F7\u5FC5\u586B\u5176\u4E00
en.info.not.exist=\u6761\u7801\u4FE1\u606F\u4E0D\u5B58\u5728
rows.more.than.500=\u5355\u9875\u67E5\u8BE2\u6570\u91CF\u4E0D\u5F97\u8D85\u8FC7500
box.has.not.qc=\u7BB1{0}\u672A\u8FDB\u884C\u62BD\u68C0\uFF0C\u4E0D\u5141\u8BB8\u5165\u5E93\uFF0C\u8BF7\u786E\u8BA4
box.qc.fail=\u7BB1{0}\u62BD\u68C0\u4E0D\u5408\u683C\uFF0C\u8BF7\u786E\u8BA4
z.mail.export.sn.ing=\u6B63\u5728\u90AE\u4EF6\u5BFC\u51FA\u4E2D\uFF0C\u8BF7\u5173\u6CE8\u65B0\u90AE\u4EF6
tech.submit.all.chg.null=\u6280\u6539\u6267\u884C\u5168\u90E8\u63D0\u4EA4\uFF0C\u6280\u6539\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
tech.submit.all.craft.section.null=\u6280\u6539\u6267\u884C\u5168\u90E8\u63D0\u4EA4\uFF0C\u4E3B\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
tech.submit.all.prod.null=\u6280\u6539\u6267\u884C\u5168\u90E8\u63D0\u4EA4\uFF0C\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
tech.submit.all.product.code.null=\u6280\u6539\u6267\u884C\u5168\u90E8\u63D0\u4EA4\uFF0C\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
tech.submit.all.unlock.type.null=\u6280\u6539\u6267\u884C\u5168\u90E8\u63D0\u4EA4\uFF0C\u89E3\u9501\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
submit.all.sn.null=\u6240\u9009\u6761\u4EF6\u65E0\u53EF\u89E3\u9501\u6761\u7801\uFF0C\u8BF7\u786E\u8BA4\uFF01
get.solder.open.add.fail=\u83B7\u53D6\u6307\u4EE4\u9521\u818F\u5F00\u74F6\u52A0\u626B\u8BB0\u5F55\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4
solder.no.open.add={0}\u6307\u4EE4\u672A\u8FDB\u884C\u9521\u818F\u626B\u63CF\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u5F00\u5DE5\u64CD\u4F5C
work.order.no.not.start=\u6307\u4EE4{0}\u672A\u5F00\u5DE5\uFF0C\u8BF7\u5148\u5F00\u5DE5\u6307\u4EE4!
return.qty.null=reelID {0}, \u5728\u9000\u6599\u8868\u4E2D\u6570\u91CF\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
reelid.not.exists=reelId\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
unable.to.obtain.work.order=\u83B7\u53D6\u4E0D\u5230reelId\u5BF9\u5E94\u7684\u6307\u4EE4
work.order.status.incorrect=reelId\u5BF9\u5E94\u7684\u6307\u4EE4\u672A\u5B8C\u5DE5\uFF0C\u4E0D\u80FD\u4FEE\u6539\u9000\u6599\u6570\u91CF
reelid.is.return=\u5DF2\u8FDB\u884C\u9000\u6599\u786E\u8BA4\uFF0C\u4E0D\u5141\u8BB8\u4FEE\u6539\u9000\u6599\u6570\u91CF
reelid.is.recovery=\u5DF2\u8FDB\u884C\u9000\u6599\u56DE\u6536\uFF0C\u4E0D\u5141\u8BB8\u4FEE\u6539\u9000\u6599\u6570\u91CF
storage.center.return.null=\u4ED3\u50A8\u4E2D\u5FC3\u6570\u636E\u8FD4\u56DE\u4E3A\u7A7A
query_wip_information_cannot_exceed_1000=\u67E5\u8BE2wip\u4FE1\u606F\u4E0D\u80FD\u8D85\u8FC71000\u4E2A
failed_to_obtain_material_information=\u8C03\u6570\u65B9\u5E73\u53F0API\u83B7\u53D6\u7269\u6599\u4FE1\u606F\u5931\u8D25
time.zone.cannot.span.seven.days=\u67E5\u8BE2\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u5F97\u8D85\u8FC77\u5929
time.cannot.be.null=\u65F6\u95F4\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
return.info.is.null=\u4E0D\u5B58\u5728\u9000\u6599\u4FE1\u606F
qty.must.be.a.positive.integer=\u5165\u53C2\u6570\u91CF\u5FC5\u987B\u4E3A\u6B63\u6574\u6570
params.is.null.or.qty.exceed.4=\u8F93\u5165\u53C2\u6570\u4E3A\u7A7A\u6216\u8005reelid\u6570\u91CF\u5927\u4E8E4\uFF0C\u8BF7\u786E\u8BA4\uFF01
reelid.is.null=\u5165\u53C2reelid\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
bindList.is.null=\u83B7\u53D6\u4E0D\u5230\u7269\u6599\u4EE3\u7801\u6E05\u5355
get.item.info.fail=\u83B7\u53D6\u7269\u6599\u4EE3\u7801\u53EF\u66FF\u4EE3\u7269\u6599\u5931\u8D25
process.last.workstation.error=\u5B50\u5DE5\u5E8F{0}\u7684\u6700\u540E\u5DE5\u7AD9\u5E94\u4E3A{1}
item.not.bound=\u5DE5\u7AD9{0}\u4E0B\u7684\u7269\u6599\u4EE3\u7801{1}\u6CA1\u6709\u7ED1\u5B9A\u5B8C\u6210

line.code.of.com.ass.scan.null=\u6574\u673A\u88C5\u914D\u626B\u63CF\u7EBF\u4F53\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
process.code.of.com.ass.scan.null=\u6574\u673A\u88C5\u914D\u626B\u63CF\u5B50\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.of.com.ass.scan.null= \u6574\u673A\u88C5\u914D\u626B\u63CF\u4E3B\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.not.have.wip.info=\u4E3B\u6761\u7801[{0}]\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\u4E3B\u6761\u7801\u6B63\u786E\u6027\uFF01
main.barcode.craft.section.illegal=\u4E3B\u6761\u7801[{0}]\u5DF2\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.not.have.binding.setting=\u4E3B\u6761\u7801[{0}]\u7684\u6599\u5355[{1}]\u65E0\u7ED1\u5B9A\u5173\u7CFB\u914D\u7F6E\uFF0C\u8BF7\u786E\u8BA4\uFF01
assembly.not.comply.with.rules=\u4E3B\u6761\u7801[{0}]\u672A\u6309\u88C5\u914D\u89C4\u5219\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.have.finished.bind=\u4E3B\u6761\u7801[{0}]\u5728\u5F53\u524D\u5B50\u5DE5\u5E8F/\u5DE5\u7AD9\u5DF2\u88C5\u914D\u5B8C\u6210\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.not.have.work.order.no=\u4E3B\u6761\u7801[{0}]\u672A\u627E\u5230\u6709\u6548\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
skip.total.qty.illegal=\u8DF3\u8F6C\u6570\u91CF\u4E0D\u80FD\u5C0F\u4E8E\u7ED1\u5B9A\u6E05\u5355\u4E2D\u6240\u6709\u7269\u6599\u7684\u6240\u9700\u6570\u91CF\u4E4B\u548C\uFF0C\u8BF7\u786E\u8BA4\uFF01
sub.barcode.craft.section.illegal=\u5B50\u6761\u7801[{0}]\u672A\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
item.of.ssp.have.related.sn=\u5B50\u6761\u7801[{0}]\u5DF2\u5173\u8054\u4E2D\u5FC3\u5E8F\u5217\u7801\uFF0C\u8BF7\u626B\u63CF\u4E2D\u5174\u5E8F\u5217\u7801\uFF01
islead.of.sub.sn.not.find.dictionary=\u5B50\u6761\u7801[{0}]\u7684\u73AF\u4FDD\u5C5E\u6027[{1}]\u5728IMES\u6570\u636E\u5B57\u5178\u4E2D\u672A\u627E\u5230\u5BF9\u5E94\u9879\uFF01
islead.of.main.sn.not.find.dictionary=\u4E3B\u6761\u7801[{0}]\u7684\u73AF\u4FDD\u5C5E\u6027[{1}]\u5728IMES\u6570\u636E\u5B57\u5178\u4E2D\u672A\u627E\u5230\u5BF9\u5E94\u9879\uFF01
lead.flag.of.sub.sn.illegal=\u5B50\u6761\u7801\u7684\u73AF\u4FDD\u5C5E\u6027[{0}]\u4F4E\u4E8E\u4EFB\u52A1\u7684\u73AF\u4FDD\u5C5E\u6027[{1}]
failed_to_obtain_contract_information=\u83B7\u53D6MES\u5408\u540C\u4FE1\u606F\u4EE5\u53CA\u5165\u5E93\u8BB0\u8D26\u5B8C\u6210\u65F6\u95F4\u5931\u8D25
main.sn.is.in.store=\u4E3B\u6761\u7801\u5DF2\u7ECF\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
not.found.replace.item=\u672A\u83B7\u53D6\u5230\u66FF\u4EE3\u7269\u6599
item.no.no.bind.list=\u6599\u5355{0}\u5B50\u5DE5\u5E8F{1}\u5DE5\u7AD9{2}\u672A\u8BBE\u7F6E\u9700\u7ED1\u5B9A\u6E05\u5355\uFF0C\u8BF7\u5148\u8BBE\u7F6E
this.main.sn.is.bind=\u8BE5\u6761\u7801{0}\u5728\u5B50\u5DE5\u5E8F{1}\u5DE5\u7AD9{2}\u4E0B\u5DF2\u7ECF\u5B58\u5728\u7ED1\u5B9A\u8BB0\u5F55\uFF0C\u8BF7\u89E3\u7ED1
bind.count.need.more.than={0}\u4EE3\u7801\u8FD8\u9700\u7ED1\u5B9A{1}\u4E2A\u6761\u7801\uFF0C\u8BF7\u786E\u8BA4
get.item.info.count.fail=\u83B7\u53D6\u66FF\u4EE3\u7269\u6599\u6570\u91CF\u5931\u8D25
sub.sn.not.exist=\u5B50\u6761\u7801\u4FE1\u606F\u672A\u5728\u6761\u7801\u4E2D\u5FC3\u6CE8\u518C\u6216\u4E0D\u6B63\u786E\uFF0C\u8BF7\u68C0\u67E5
not.need.bind.sub.sn=\u6CA1\u6709\u9700\u8981\u7ED1\u5B9A\u7684\u5B50\u6761\u7801
sn.is.lock.can.not.bind=\u6761\u7801{0}\u5B58\u5728\u6709\u6548\u9501\u5B9A\u4FE1\u606F\uFF0C\u4E0D\u5141\u8BB8\u7ED1\u5B9A
batch.is.lock.can.not.bind=\u6279\u6B21{0}\u5B58\u5728\u6709\u6548\u9501\u5B9A\u4FE1\u606F\uFF0C\u4E0D\u5141\u8BB8\u7ED1\u5B9A
sn.item.length.no.12=\u6761\u7801{0}\u7684\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A\u6216\u4E0D\u8DB312\u4F4D\uFF0C\u8BF7\u786E\u8BA4
main.sn.wip.ext.bind.lock=\u4E3B\u6761\u7801{0}\u6B63\u5728\u8FDB\u884C\u7ED1\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
sub.barcode.have.bind.relation=\u5B50\u6761\u7801[{0}]\u5DF2\u5B58\u5728\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u8BF7\u786E\u8BA4\uFF01
sub.barcode.have.bound.in.main.barcode=\u5B50\u6761\u7801[{0}]\u4E3A\u6279\u6B21\u7801\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A\u5728\u4E3B\u6761\u7801[{1}]
main.barcode.wip.info.not.have.task.no=\u4E3B\u6761\u7801[{0}]\u5728\u5236\u4FE1\u606F\u4E2D\u627E\u4E0D\u5230\u4EFB\u52A1\u53F7\uFF0C\u65E0\u6CD5\u67E5\u8BE2ERP\u4EFB\u52A1\u6E05\u5355\uFF0C\u8BF7\u786E\u8BA4\uFF01
item.or.replace.item.in.erp.task.more=\u5B50\u6761\u7801[{0}]\u7684\u7269\u6599\u4EE3\u7801\u5B58\u5728\u591A\u4E2A\u66FF\u4EE3\u7269\u6599\u5728\u4E3B\u6761\u7801\u4EFB\u52A1\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
item.or.replace.item.not.in.bind.list=\u5B50\u6761\u7801[{0}]\u7684\u7269\u6599\u4EE3\u7801\uFF08\u5305\u542B\u66FF\u6362\u7269\u6599\uFF09\u672A\u5728\u4E3B\u6761\u7801\u7ED1\u5B9A\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
item.or.replace.item.in.bind.list.more=\u5B50\u6761\u7801[{0}]\u7684\u7269\u6599\u4EE3\u7801\u5B58\u5728\u591A\u4E2A\u66FF\u4EE3\u7269\u6599\u5728\u4E3B\u6761\u7801\u7ED1\u5B9A\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.lock.not.allow.bind=\u4EE5\u4E0B\u6761\u7801\uFF1A{0}\uFF0C\u6279\u6B21\uFF1A{1}\uFF0C\u5B58\u5728\u9501\u5B9A\u5355\uFF0C\u65E0\u6CD5\u7ED1\u5B9A\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.flow.control.not.pass=\u4E3B\u6761\u7801\u7684\u6D41\u7A0B\u7BA1\u63A7\u6821\u9A8C\u672A\u901A\u8FC7\uFF0C\u539F\u56E0\u4E3A\uFF1A{0}
work.order.no.of.wip.info.not.find.entity=\u4E3B\u6761\u7801\u7684\u5728\u5236\u4FE1\u606F\u8BB0\u5F55\u7684\u6307\u4EE4[{0}]\u672A\u627E\u5230\u5BF9\u5E94\u4E9B\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
main.barcode.is.binding.now=\u8BE5\u4E3B\u6761\u7801\u6B63\u5728\u8FDB\u884C\u88C5\u914D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01
main.barcode.have.more.work.order.no=\u4EFB\u52A1{0}\u5728\u6240\u9009\u7EBF\u4F53\u548C\u5B50\u5DE5\u5E8F\u4E0B\u53D6\u5230\u591A\u4E2A\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
get.current.workorder.error=\u83B7\u53D6\u5F53\u524D\u6307\u4EE4\u5931\u8D25
sn.lost.board.center={0}\u6761\u7801\u5728\u6761\u7801\u4E2D\u5FC3\u4E0D\u5B58\u5728
route.detail.lost={0}\u5DE5\u827A\u8DEF\u5F84\u7F3A\u5931,\u8BF7\u786E\u8BA4!
sub.sn.not.bind={0}\u5B50\u6761\u7801\u4E0D\u9700\u8981\u7ED1\u5B9A,\u8BF7\u786E\u8BA4!
redis.lock.fail.msg=\u83B7\u53D6redis\u9501\u8D44\u6E90\u5931\u8D25\uFF1A{0}
item.bind.over={0}\u4EE3\u7801\u9700\u7ED1\u5B9A{1}\u5DF2\u7ECF\u7ED1\u5B9A{2}\u672C\u6B21\u7ED1\u5B9A\u6570\u91CF{3}\u4E0D\u80FD\u7ED1\u5B9A!
task.no.have.batch.work.order.no=\u4EFB\u52A1{0}\u5728\u5B50\u5DE5\u5E8F{1}\u53D6\u5230\u591A\u4E2A\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
return.sn.is.on.lock=\u8FD4\u8FD8\u6761\u7801{0}\u6216\u6240\u5C5E\u6279\u6B21{1}\u5904\u4E8E\u9501\u5B9A\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.is.not.in.right.imu=\u6761\u7801{0}imu\u4E0D\u5904\u4E8E\u5165\u5E93\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u63A5\u6536\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.is.not.in.ScanReceive=\u6761\u7801\u4E0D\u80FD\u88AB\u626B\u63CF\u63A5\u6536\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.is.more.than.one=\u6761\u7801\u5BF9\u5E94\u7684\u5DF2\u63D0\u4EA4\u7684\u5355\u636E\u8D85\u8FC7\u4E00\u6761\uFF0C\u8BF7\u786E\u8BA4\uFF01
return.info.is.on.lock=\u8FD4\u8FD8\u6761\u7801\u6216\u6279\u6B21{0}\u5904\u4E8E\u9501\u5B9A\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
can.not.more.than.1000=\u5355\u6B21\u67E5\u8BE2\u6761\u7801\u6570\u4E0D\u5F97\u8D85\u8FC71000
upload.test.data.too.much=\u5355\u6B21\u4E0A\u4F20\u7684\u6D4B\u8BD5\u6570\u636E\u4E0D\u5F97\u8D85\u8FC7{0}\u6761
sn.workOrderNo.not.exist=\u6761\u7801{0}\u6307\u4EE4\u4FE1\u606F\u4E3A\u7A7A
line.model.of.sn.not.found=\u6761\u7801{0}\u5BF9\u5E94\u7684\u7EBF\u4F53\u5EFA\u6A21\u4FE1\u606F\u4E3A\u7A7A
line.model.of.sn.not.found.or.status.error=\u6761\u7801{0}\u5BF9\u5E94\u7684\u7EBF\u4F53\u5EFA\u6A21\u4FE1\u606F\u4E3A\u7A7A\u6216\u5F53\u524D\u6761\u7801\u7ED3\u5B58\u975E\u5DE5\u5E8F{1}
task_batch_barcodes_cannot_all_be_empty=\u89E3\u7ED1\u65F6,\u4EFB\u52A1,\u6279\u6B21\uFF0C\u6761\u7801\u4E0D\u80FD\u90FD\u4E3A\u7A7A
main_sn_child_barcodes_cannot_all_be_empty=\u4E3B\u6761\u7801,\u5B50\u6761\u7801\u4E0D\u80FD\u90FD\u4E3A\u7A7A
task_batch_barcodes_cannot_all_be_empty_search=\u4EFB\u52A1,\u6279\u6B21,\u6761\u7801,\u65F6\u95F4\u4E0D\u80FD\u90FD\u4E3A\u7A7A
the_number_of_primary_barcodes_cannot_exceed_100=\u4E3B\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E100
the_number_of_sub_barcodes_cannot_exceed_100=\u5B50\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E100
file.type.illegal=\u6280\u6539\u6587\u4EF6\u7C7B\u578B\u4E0D\u6B63\u786E\uFF0C\u8BF7\u786E\u8BA4\uFF01
product.query.params.can.not.be.null=\u7EBF\u4F53\u3001\u5DE5\u7AD9\u53CA\u6279\u6B21\u4E3A\u5FC5\u9009
get.item.model.info.error=\u83B7\u53D6\u7269\u6599\u89C4\u5219\u578B\u53F7\u8FC7\u7A0B\u53D1\u751F\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4!
one_additional_barcode_printing_cannot_exceed_200=\u4E00\u6B21\u8865\u6253\u6761\u7801\u4E0D\u80FD\u5927\u4E8E200
failed_to_query_barcode_printing_information=\u67E5\u8BE2\u6761\u7801\u6253\u5370\u4FE1\u606F\u5931\u8D25
get.mds.token.error=\u83B7\u53D6\u4E2D\u8BD5token\u5931\u8D25
sn.info.not.exist=\u6761\u7801\uFF1A{0}\u4E0D\u5B58\u5728
sn.is.stock=\u6761\u7801\uFF1A{0}\u5B58\u5728\u5E93\u5B58
sn.not.exist.parent.sn=\u6761\u7801\uFF1A{0}\u6CA1\u6709\u627E\u5230\u6574\u673A\u6761\u7801
task.no.is.different=\u6761\u7801\uFF1A{0}\u4F20\u5165\u4EFB\u52A1\u53F7\u4E0EIMES\u7CFB\u7EDF\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
item.no.is.different=\u6761\u7801\uFF1A{0}\u4F20\u5165\u7269\u6599\u4EE3\u7801\u4E0EIMES\u7CFB\u7EDF\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
sn.num.cannot.exceed.200=\u4F20\u5165\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7200
processCode.or.workStation.is.null=\u83B7\u53D6\u5B50\u5DE5\u5E8F\u548C\u5DE5\u7AD9\u5931\u8D25
process.name.is.different=\u4F20\u5165\u6761\u7801\u7684\u5B50\u5DE5\u5E8F\u540D\u79F0\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4!
workstation.name.is.different=\u4F20\u5165\u6761\u7801\u7684\u5DE5\u7AD9\u540D\u79F0\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4!
update.mouting.failed = \u673A\u53F0\u5728\u7528\u5DF2\u7ECF\u5931\u6548,\u8BF7\u91CD\u65B0\u626B\u63CF\u65E7\u6599\u76D8\u83B7\u53D6\u6709\u6548\u673A\u53F0\u5728\u7528\u6570\u636E
work.order.info.is.none.by.task.and.process=\u901A\u8FC7\u4EFB\u52A1{0}\u548C\u5B50\u5DE5\u5E8F{1}\u83B7\u53D6\u4E0D\u5230\u5DF2\u63D0\u4EA4\u3001\u5DF2\u5F00\u5DE5\u3001\u96F6\u661F\u677F\u6302\u8D77\u7684\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
too.many.work.order.info.by.task.and.process= \u901A\u8FC7\u4EFB\u52A1{0}\u548C\u5B50\u5DE5\u5E8F{1}\u83B7\u53D6\u5230\u5DF2\u63D0\u4EA4\u3001\u5DF2\u5F00\u5DE5\u3001\u96F6\u661F\u677F\u6302\u8D77\u7684\u6307\u4EE4\u6709\u591A\u6761\uFF0C\u8BF7\u786E\u8BA4\uFF01
failed.to.get.item.no.from.wip=\u901A\u8FC7\u6761\u7801\u83B7\u53D6\u5728\u5236\u8868\u5BF9\u5E94\u7269\u6599\u4EE3\u7801\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4\u6761\u7801\u5728\u5236\u8868\u4E2D\u662F\u5426\u5B58\u5728\u4EE5\u53CA\u662F\u5426\u6709\u5BF9\u5E94\u7269\u6599\u4EE3\u7801\uFF01
usage.count.is.null=\u7ED1\u5B9A\u6E05\u5355\u4E2D\u5B58\u5728\u7528\u91CF\u6CA1\u6709\u914D\u7F6E\u7684\u7269\u6599\uFF0C\u8BF7\u786E\u8BA4\uFF01
bind.setting.usage.count.is.null=\u7ED1\u5B9A\u6E05\u5355\u4E2D\u6599\u5355{0}\u7269\u6599{1}\u6CA1\u6709\u914D\u7F6E\u7684\u7269\u6599\uFF0C\u8BF7\u786E\u8BA4\uFF01
mixed.tasks.scan.param.null=\u6DF7\u4EFB\u52A1\u626B\u63CF\u573A\u666F\u5B50\u5DE5\u5E8F\u548C\u5DE5\u7AD9\u5747\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
error.msg.for.unbinding = \u4EE5\u4E0B\uFF1A{0} \u4E0D\u5141\u8BB8\u63D0\u4EA4\u5165\u5E93,\u8BF7\u786E\u8BA4!
task.no.or.item.no.can.not.null = \u53C2\u6570\u7F3A\u5931\uFF0C\u4EFB\u52A1\u53F7\u548C\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
task.no.all.sn.not.finish.binding = \u6240\u6709\u6761\u7801\u5747\u6709\u9700\u8981\u5B8C\u6210\u7ED1\u5B9A\u7684\u88C5\u914D\u5173\u7CFB,\u4E0D\u5141\u8BB8\u63D0\u4EA4\u5165\u5E93,\u8BF7\u786E\u8BA4
no.successful.sn=\u6CA1\u6709\u626B\u63CF\u901A\u8FC7\u7684\u6761\u7801
process.not.exist=\u5B50\u5DE5\u5E8F{0}\u4E0D\u5B58\u5728
comparison.complete=\u5DF2\u6BD4\u5BF9\u5B8C\u6210
item.or.replace.item.not.in.erp.and.bind.list=\u5B50\u6761\u7801[{0}]\u7684\u7269\u6599\u4EE3\u7801\uFF08\u5305\u542B\u66FF\u6362\u7269\u6599\uFF09\u672A\u5728\u4E3B\u6761\u7801erp\u4EFB\u52A1\u6E05\u5355\u4E2D\u4E14\u4E5F\u4E0D\u5728IMES\u7ED1\u5B9A\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
prepare.item.edit.param.repeat=\u5DE5\u5E8F\u51C6\u5907\u9879\u914D\u7F6E\u65B0\u589E/\u4FEE\u6539\u5B58\u5728\u91CD\u590D\u53C2\u6570\uFF1A{0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
get.spm.tld.error=\u8C03SPM\u83B7\u53D6\u5957\u6599\u5355\u4FE1\u606F\u5931\u8D25
bind.info.not.enough=\u6761\u7801{0}\u7ED1\u5B9A\u6570\u636E\u4E0D\u5168\uFF0C\u5177\u4F53\u7ED1\u5B9A\u4FE1\u606F\uFF1A{1}
activity.is.n.and.data.null=\u5168\u90E8\u786E\u8BA4\u672A\u9009\u62E9\u662F\u65F6\uFF0C\u672A\u5B8C\u6210\u51C6\u5907\u9879\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
push.date.time.null=\u63A8\u9001\u6570\u636E\u6700\u540E\u540C\u6B65\u65F6\u95F4\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
not.fount.matrialTray=\u6CA1\u6709\u627E\u5230\u6599\u76D8\u4FE1\u606F
materials.has.prepared=\u5DF2\u7ECF\u7ED1\u5B9A\u5230\u6307\u4EE4{0}
materials.is.use={0}\u5728\u4F7F\u7528\uFF0C\u8BF7\u786E\u8BA4\uFF01
location.inconsistent=\u7AD9\u4F4D\u7269\u6599\u8DDF\u4E0A\u6599\u8868\u7269\u6599\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
order.online.check.prepare.status.failed=\u6307\u4EE4:{0}\u5F00\u5DE5\u8D44\u6E90\u51C6\u5907\u9879\u3010 {2} \u3011\u672A\u5B8C\u6210,\u5982\u9700\u5F00\u5DE5\u8BF7\u524D\u5F80 {1} \u7EBF\u8D44\u6E90\u51C6\u5907\u770B\u677F\u786E\u8BA4!
reelId.not.exist.with.reelId={0}reelId\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
reelId.not.return.material={0}reelid\u65E0\u9000\u6599\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\u6307\u4EE4\u662F\u5426\u5DF2\u5B8C\u5DE5\uFF01
export.missing.sourceBatch=\u5BFC\u51FA\u65F6\u8BF7\u8F93\u5165\u6279\u6B21
locator.id.error= ERP\u8D27\u4F4DID\u4E3A\u7A7A,\u8BF7\u786E\u8BA4\u662F\u5426\u9009\u62E9ERP\u8D27\u4F4D,\u6216\u8005\u5237\u65B0\u9875\u9762\u91CD\u8BD5\uFF01
no_personnel_interface_address_configured=\u672A\u914D\u7F6E\u4EBA\u4E8B\u63A5\u53E3\u5730\u5740,\u8BF7\u786E\u8BA4
failed_to_obtain_personnel_information=\u8C03\u4EBA\u4E8B\u63A5\u53E3\u83B7\u53D6\u4EBA\u4E8B\u4FE1\u606F\u5931\u8D25\uFF1A{0}
sn.is.exist=\u6761\u7801{0}\uFF0C\u5DF2\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
query.params.error=\u67E5\u8BE2\u6761\u4EF6\uFF1A\u6761\u7801\u548C\u66F4\u65B0\u65F6\u95F4\u4E0D\u80FD\u90FD\u4E3A\u7A7A\uFF01
last.update.date.range.too.long=\u66F4\u65B0\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u534A\u5E74
create.date.range.too.long=\u521B\u5EFA\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u534A\u5E74
not.found.ps.task.by.prodplanid={0}\u6279\u6B21\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
warehouse.line.not.allowed.transfer=\u7EBF\u8FB9\u4ED3\u7EBF\u4F53\u6279\u6B21\u4E0D\u5141\u8BB8\u8FDB\u884C\u8DE8\u6279\u6B21\u8F6C\u4EA7
prodplan.id.has.maintained.transfer.strategy=\u8BE5\u6279\u6B21\u5728\u5F53\u524D\u6240\u9009\u7EBF\u4F53\u5DF2\u7EF4\u62A4\u8FC7\u8F6C\u4EA7\u7B56\u7565
pre.prodplan.id.has.maintained.transfer.strategy=\u8BE5\u9884\u8F6C\u4EA7\u6279\u6B21\u5728\u5F53\u524D\u6240\u9009\u7EBF\u4F53\u5DF2\u7EF4\u62A4\u8FC7\u8F6C\u4EA7\u7B56\u7565
not.found.work.order.by.prodplan.id=\u6279\u6B21\u6216\u9884\u8F6C\u4EA7\u6279\u6B21\u5728\u5F53\u524D\u6240\u9009\u7EBF\u4F53\u4E0B\u6CA1\u6709\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4
craft.section.not.scheduled.same.line={0}\u9762\u522B\u6307\u4EE4\u672A\u6392\u4EA7\u5728\u540C\u4E00\u7EBF\u4F53\uFF0C\u4E0D\u652F\u6301\u8DE8\u6279\u6B21\u8F6C\u4EA7
smt.material.inconsistent={0}\u9762\u522B\u6307\u4EE4\u4E0A\u6599\u8868\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u652F\u6301\u8DE8\u6279\u6B21\u8F6C\u4EA7
work.order.have.been.prepared={0}\u6279\u6B21{1}\u6307\u4EE4\u5DF2\u8FDB\u884C\u7EFC\u5408\u5907\u6599\uFF0C\u4E0D\u5141\u8BB8\u7EF4\u62A4\u8DE8\u6279\u6B21\u8F6C\u4EA7\u7B56\u7565
work.order.have.been.prepared.completion.not.allowed={0}\u6279\u6B21{1}\u6307\u4EE4\u5DF2\u8FDB\u884C\u7EFC\u5408\u5907\u6599\uFF0C\u4E0D\u5141\u8BB8\u5B8C\u5DE5
transfer.strategy.info.redis.locked={0}\u6279\u6B21{1}\u7EBF\u4F53{2}\u9884\u8F6C\u4EA7\u6279\u6B21\u8F6C\u4EA7\u7B56\u7565\u6B63\u5728\u63D0\u4EA4\u4E2D\uFF0C\u8BF7\u7A0D\u540E\uFF01
strategy.has.used.not.delete=\u8BE5\u8F6C\u4EA7\u7B56\u7565\u5DF2\u7ECF\u4F7F\u7528\uFF0C\u4E0D\u80FD\u522A\u9664
strategy.has.used.not.update=\u8BE5\u8F6C\u4EA7\u7B56\u7565\u5DF2\u7ECF\u4F7F\u7528\uFF0C\u4E0D\u80FD\u4FEE\u6539
not.found.transfer.strategy=\u8BE5\u8F6C\u4EA7\u7B56\u7565\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
strategy.has.been.maintained={0}\u6279\u6B21\u5728{1}\u7EBF\u4F53\u5DF2\u7EF4\u62A4\u8DE8\u6279\u6B21\u95F4\u8F6C\u4EA7\u7B56\u7565\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u5355\u72EC\u5907\u6599\uFF0C\u53EF\u76F4\u63A5\u4F7F\u7528{2}\u6279\u6B21\u5269\u4F59\u7269\u6599\u751F\u4EA7
batch.formation.loop=\u8F6C\u4EA7\u7B56\u7565\u6279\u6B21\u95F4\u5C06\u5F62\u6210\u73AF\u8DEF\uFF0C\u8BF7\u68C0\u67E5\uFF01
not.found.other.task.by.line.code=\u672A\u627E\u5230\u5176\u4ED6\u6392\u5728\u7EBF\u4F53{0}\u4E14\u72B6\u6001\u4E3A\u5DF2\u63D0\u4EA4\u7684\u6307\u4EE4\u6240\u5C5E\u6279\u6B21\uFF0C\u8BF7\u786E\u8BA4\uFF01
check.work.time.failed=\u5F53\u524D\u5DE5\u5E8F\u6709\u4F5C\u4E1A\u65F6\u957F\u7BA1\u63A7\uFF0C\u6761\u7801{0}\u8DDD\u3010{1}\u3011\u8FC7\u7AD9\u672A\u8FBE\u4F5C\u4E1A\u65F6\u957F\uFF01
check.remain.time.failed=\u4E0A\u5DE5\u5E8F\u6709\u6EDE\u7559\u65F6\u957F\u7BA1\u63A7\uFF0C\u6761\u7801{0}\u8DDD\u3010{1}\u3011\u8FC7\u7AD9\u5DF2\u8D85\u6EDE\u7559\u65F6\u957F\uFF01
cannot.find.content.sn= \u6CA1\u6709\u627E\u5230\u7BB1\u5185\u5BB9\u6761\u7801!
more.than.one.order.exist.in.lpn = \u7BB1\u5185\u5BB9\u6761\u7801\u7ED3\u5B58\u5728\u591A\u4E2A\u6307\u4EE4,\u8BF7\u786E\u8BA4!
sn.not.exist.in.wip.info = \u7BB1\u5185\u5BB9\u6761\u7801{0}\u5728\u5728\u5236\u8868\u4E2D\u4E0D\u5B58\u5728
repair.sn.process.change=\u4E0B\u5217\u9001\u4FEE\u6761\u7801{0}\u5F53\u524D\u5728\u5236\u8868\u5B50\u5DE5\u5E8F\u4E0E\u9001\u4FEE\u626B\u63CF\u65F6\u4E0D\u540C\uFF0C\u8BF7\u786E\u8BA4\u8FD9\u4E9B\u6761\u7801\u662F\u5426\u5DF2\u7ECF\u8F6C\u4EA4\u5230\u4E0B\u5DE5\u5E8F\uFF01
repair.query.param.error=[\u6761\u7801],[\u6279\u6B21],[\u7EF4\u4FEE\u65F6\u95F4],[\u9001\u4FEE\u65F6\u95F4],[\u8FD4\u8FD8/\u62A5\u5E9F\u65F6\u95F4]\u4E0D\u80FD\u5168\u90E8\u4E3A\u7A7A\u4E14\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u534A\u5E74
sn.can.not.batch.pass = \u7BB1\u5185\u5BB9\u6761\u7801{0}\u672A\u901A\u8FC7\u4F5C\u4E1A\u65F6\u957F\u6216\u6EDE\u7559\u65F6\u957F\u7BA1\u63A7\u6821\u9A8C\u4E14\u672A\u8BBE\u7F6E\u56DE\u6D41\u89C4\u5219,\u4E0D\u5141\u8BB8\u6279\u91CF\u8FC7\u7AD9
route.id.is.null=\u5DE5\u827A\u8DEF\u5F84ID\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
scan.process.code.is.null=\u626B\u63CF\u5B50\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
scan.process.code.not.the.same.in.the.wip=\u6761\u7801\u5F53\u524D\u5B50\u5DE5\u5E8F\u672A\u505A\u5B8C\uFF0C\u4E0D\u80FD\u626B\u63CF\u65B0\u5B50\u5DE5\u5E8F\u3002
scan.process.code.not.in.next.can.scan.list=\u5F53\u524D\u626B\u63CF\u7684\u5B50\u5DE5\u5E8F\u4E0D\u662F\u4E0B\u4E00\u4E2A\u53EF\u5141\u8BB8\u626B\u63CF\u7684\u5B50\u5DE5\u5E8F\uFF0C\u8BF7\u786E\u8BA4\uFF01
scan.work.station.not.in.next.can.scan.list=\u5F53\u524D\u626B\u63CF\u7684\u5DE5\u7AD9\u4E0D\u662F\u4E0B\u4E00\u4E2A\u53EF\u5141\u8BB8\u626B\u63CF\u7684\u5DE5\u7AD9\uFF0C\u8BF7\u786E\u8BA4\uFF01
scan.setting.line.code.is.null=\u626B\u63CF\u8BBE\u7F6E\u7684\u7EBF\u4F53\u4E0D\u80FD\u4E3A\u7A7A
scan.work.station.is.null=\u626B\u63CF\u5DE5\u7AD9\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.line.not.the.same.with.scan.line=\u6307\u4EE4\u6240\u6392\u7EBF\u4F53\u548C\u626B\u63CF\u9009\u62E9\u7684\u7EBF\u4F53\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.status.not.allow.to.scan=\u6307\u4EE4\u72B6\u6001\u4E0D\u5141\u8BB8\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.process.group.not.contain.scan.process.code=\u6307\u4EE4\u5B50\u5DE5\u5E8F\u7EC4\u4E2D\u4E0D\u5305\u542B\u626B\u63CF\u5B50\u5DE5\u5E8F\uFF0C\u8BF7\u786E\u8BA4\uFF01
scan.work.order.no.is.null=\u626B\u63CF\u7684\u6307\u4EE4\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.process.group.not.have.must.scan.process=\u6307\u4EE4\u5B50\u5DE5\u5E8F\u7EC4\u4E2D\u4E0D\u5305\u542B\u5FC5\u626B\u5B50\u5DE5\u5E8F\uFF0C\u8BF7\u786E\u8BA4\uFF01
line.code.model.not.have.must.scan.work.station=\u7EBF\u4F53\u5EFA\u6A21\u4E2D\u6CA1\u6709\u5FC5\u626B\u5DE5\u7AD9\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.order.relation.qty.illegal=\u6307\u4EE4\u7684\u76F8\u5173\u4FE1\u606F(\u8BA1\u5212\u6570\u91CF/\u6295\u5165/\u4EA7\u51FA)\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
work.order.process.group.not.exist=\u6307\u4EE4{0}\u672A\u6307\u5B9A\u6307\u4EE4\u5DE5\u5E8F\u7EC4\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.can.not.submit.in.stock=\u5F85\u5165\u5E93\u6761\u7801{0}\u5DF2\u8D85\u6EDE\u7559\u65F6\u957F,\u4E0D\u5141\u8BB8\u63D0\u4EA4\u5165\u5E93,\u8BF7\u786E\u8BA4\u662F\u5426\u9700\u8981\u56DE\u6D41
sn.can.not.receive.in.stock=\u5F85\u63A5\u6536\u6761\u7801{0}\u5DF2\u8D85\u6EDE\u7559\u65F6\u957F,\u4E0D\u5141\u8BB8\u63A5\u6536,\u8BF7\u786E\u8BA4\u662F\u5426\u9700\u8981\u56DE\u6D41
has.set.the.qc.regulation=\u5DF2\u8BBE\u7F6EQC\u62BD\u68C0\u89C4\u5219\uFF0C\u8BF7\u786E\u8BA4
qc.sampling.not.found=\u672A\u627E\u5230\u9001\u68C0\u4FE1\u606F\uFF01
qc.sampling.detail.not.found=\u672A\u627E\u5230\u9001\u68C0\u8BE6\u60C5\u4FE1\u606F\uFF01
the.inspection.form.cannot.be.modified=\u9001\u68C0\u5355\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D\uFF0C\u4E0D\u80FD\u4FEE\u6539
box.contents.has.changed=\u7BB1\u5185\u5BB9\u6570\u91CF\u5DF2\u53D8\u5316\uFF0C\u8BF7\u91CD\u65B0\u626B\u63CF\u540E\u63D0\u4EA4
workorder.balance.has.changed=\u6307\u4EE4\u7ED3\u5B58\u6570\u91CF\u5DF2\u53D8\u5316\uFF0C\u8BF7\u91CD\u65B0\u626B\u63CF\u540E\u63D0\u4EA4
workorder.not.same=\u6307\u4EE4\u4E0D\u4E00\u81F4
processcode.not.same=\u5B50\u5DE5\u5E8F\u4E0D\u4E00\u81F4
workstation.not.same=\u5DE5\u7AD9\u4E0D\u4E00\u81F4
state.not.same=\u6761\u7801\u72B6\u6001\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u68C0\u67E5\u6307\u4EE4\u3001\u5B50\u5DE5\u5E8F\u3001\u5DE5\u7AD9
sn.exists.on.other.qc=\u6761\u7801\u5DF2\u7ECF\u5728\u5176\u4ED6\u62BD\u68C0\u5355\u5B58\u5728{0}
sn.has.been.packed=\u6761\u7801\u5DF2\u7ECF\u88C5\u7BB1\uFF0C\u8BF7\u901A\u8FC7\u7BB1\u62BD\u68C0
sn.not.found.by.lpn=\u6839\u636E\u7BB1\u53F7\u672A\u627E\u5230\u6761\u7801
no.valid.sampling.record=\u65E0\u6709\u6548\u7684\u62BD\u68C0\u5355
no.pass.sampling.record=\u65E0\u5408\u683C\u7684\u62BD\u68C0\u5355
set.default.quality.limit=\u9ED8\u8BA4\u63A5\u53D7\u8D28\u91CF\u9650\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u786E\u8BA4\uFF01
skip.rule.is.null=\u8DF3\u8FC7\u89C4\u5219\u4E3A\u7A7A
sn.is.high.temp.sn=\u6761\u7801{0}\u4E3A\u9AD8\u6E29\u6761\u7801\uFF0C\u4E0D\u5141\u8BB8\u8DF3\u8FC7\u9AD8\u6E29\u5DE5\u5E8F
process.list.is.null=\u5B50\u5DE5\u5E8F\u53EF\u626B\u5217\u8868\u4E3A\u7A7A
not.allowed.transfer=\u4E0A\u4E2A\u6307\u4EE4\u672A\u5F00\u5DE5\uFF0C\u4E0D\u5141\u8BB8\u8F6C\u673A
stock.aux.material.unbinding={0}\u5165\u5E93\u4EFB\u52A1\u5B58\u5728\u8F85\u6599\u672A\u5230\u8FBE\u7ED1\u5B9A\u8981\u6C42,\u4E0D\u80FD\u5165\u5E93,\u5177\u4F53\u7269\u6599\u4E3A\uFF1A{1}
mix.stock.aux.material.unbinding=\u6DF7\u4EFB\u52A1\u63D0\u4EA4\u5165\u5E93\u4E2D\u5B58\u5728\u8F85\u6599\u672A\u5230\u8FBE\u7ED1\u5B9A\u8981\u6C42\u7684\u4EFB\u52A1\u6761\u7801,\u4E0D\u80FD\u5165\u5E93,\u5177\u4F53\u6570\u636E\u4E3A\uFF1A{0}
scan.aux.material.unbinding={0}\u4EFB\u52A1\u5B58\u5728\u8F85\u6599\u672A\u5230\u8FBE\u7ED1\u5B9A\u8981\u6C42,\u4E0D\u80FD\u8FC7\u7AD9,\u5177\u4F53\u7269\u6599\u4E3A\uFF1A{1}
module.no.items=\u8BE5\u6A21\u7EC4\u4E0B\u6CA1\u6709\u4E0A\u6599\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
scan.comparison.complete=\u5DF2\u7ECF\u6BD4\u5BF9\u5B8C\u6BD5\uFF0C\u8BF7\u6362\u6A21\u7EC4
cfgheaderid.is.empty=\u4E0A\u6599\u8868id\u4E0D\u80FD\u4E3A\u7A7A
no.barcode.available.in.lpn=\u65E0\u53EF\u5165\u5E93\u6761\u7801
switch.first.work.order=\u5F53\u524D\u6A21\u7EC4\u4E0D\u5B58\u5728{0}\u6307\u4EE4\u7684\u8F6C\u673A\u8BB0\u5F55\uFF0C\u8BF7\u5148\u5207\u6362\u9996\u6307\u4EE4
insufficient.sampling.quantity=\u9001\u68C0\u5355\u9001\u68C0{0}\u4E2A\u5E94\u8BE5\u62BD\u68C0{1}\u4E2A\uFF0C\u53EA\u62BD\u68C0\u4E86{2}\u4E2A\uFF0C\u4E0D\u80FD\u63D0\u4EA4
failed.to.save.stock.info=\u4FDD\u5B58\u5E93\u5B58\u4FE1\u606F\u5931\u8D25
auto.transfer.failed=\u62BD\u68C0\u5408\u683C\uFF0C\u81EA\u52A8\u5DE5\u5E8F\u8F6C\u4EA4\u5931\u8D25\uFF0C\u8BF7\u5728\u5DE5\u5E8F\u8F6C\u4EA4\u9875\u9762\u64CD\u4F5C\uFF0C\u9519\u8BEF\u4FE1\u606F\uFF1A{0}
wip.print.config.file.name=\u6574\u673A\u6761\u7801\u6253\u5370\u914D\u7F6E\u6A21\u677F.xlsx
Hard.Cord.File.Template=\u6A21\u677F\u6587\u4EF6\u9519\u8BEF\uFF01\u672A\u5305\u542B\u5217\uFF1A
excel.resolution.failure=excel \u89E3\u6790\u5931\u8D25,{0}
auto.creation.not.support.custom.aql=\u4E0D\u652F\u6301\u81EA\u52A8\u521B\u5EFA\u7684\u62BD\u68C0\u662F\u81EA\u5B9A\u4E49\u573A\u666F\uFF0C\u8BF7\u91CD\u65B0\u8BBE\u7F6E\u9ED8\u8BA4\u63A5\u53D7\u8D28\u91CF\u9650AQL
the.file.cannot.be.empty=\u6587\u4EF6\u6216\u6574\u673A\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A!
current.no.items=\u5F53\u9762\u6CA1\u6709\u7269\u6599\uFF0C\u8BF7\u786E\u8BA4\uFF01
not.fount.data.or.not.instruction=\u6CA1\u6709\u627E\u5230\u6599\u76D8\u4FE1\u606F\u6216\u8005\u8BE5\u7269\u6599\u6279\u6B21\u4E0D\u662F\u8BE5\u6307\u4EE4\u7684\uFF0C\u8BF7\u786E\u8BA4
location.not.match=\u5F53\u524D\u6307\u4EE4\u4E0A\u6599\u8868\u4E0D\u5B58\u5728\u8BE5\u7AD9\u4F4D\uFF0C\u8BF7\u786E\u8BA4
reel.id.not.match=ReelId\u548C\u5F53\u524D\u7AD9\u4F4D\u7269\u6599\u4E0D\u5339\u914D
item.code.has.binded.feeder=\u7269\u6599{0}\u5DF2\u7ECF\u7ED1\u5B9AFeeder\u4E86
not.find.feeder.bind.loc=\u627E\u4E0D\u5230feeder\u7ED1\u5B9A\u4F4D\u7F6E
the_erp_start_of_the_query_task_is_incorrect=\u67E5\u8BE2\u4EFB\u52A1{0}ERP\u8D77\u59CB\u5DE5\u5E8F\u9519\u8BEF
line.not.allow.more.than.two_work.order=\u4E00\u6761\u7EBF\u4F53\u4E0D\u5141\u8BB82\u4E2A\u4EE5\u4E0A\u6307\u4EE4\u540C\u65F6\u5F00\u5DE5\uFF0C\u8BF7\u786E\u8BA4
reel.id.exist.in.container.not.allow.split=\u8BE5reelId\u5B58\u5728\u4E8E{0}\u5BB9\u5668\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u62C6\u5206\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
smt.data.line.location.item.code.is.null=smt\u5728\u7528\u4FE1\u606F\u7EBF\u4F53/\u7AD9\u4F4D/\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
update.pl.info.error=\u66F4\u65B0\u629B\u6599\u8868\u629B\u6599\u6570\u636E\u5931\u8D25
scan.type.not.one.or.two=\u626B\u63CF\u65B9\u5F0F\u4E0D\u662F\u9010\u5757\u626B\u63CF\u6216\u8005\u6574\u6279\u626B\u63CF\uFF0C\u8BF7\u786E\u8BA4
sn.in.container=\u6761\u7801{0}\u5DF2\u88C5\u7BB1\uFF0C\u8BF7\u786E\u8BA4
package.scan.sn.process.wrong=\u6761\u7801{0}\u7ED3\u5B58\u4E0D\u662F\u5165\u5E93\u524D\u5B50\u5DE5\u5E8F\u6700\u540E\u5DE5\u7AD9\u6216\u4EC5\u5165\u5E93\u524D\u5B50\u5DE5\u5E8F\u6700\u540E\u5DE5\u7AD9\u672A\u505A\u5B8C\uFF0C\u8BF7\u786E\u8BA4
outsource.reelid.format.error=reelid\u626B\u63CF\u573A\u666F\uFF0C\u5916\u534Freelid\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u4E0D\u80FD\u8FDB\u884C\u626B\u63CF\uFF0C\u683C\u5F0F\u8981\u6C42\u4E3A\u5C0F\u4E8E\u7B49\u4E8E18\u4F4D\uFF0C\u5FC5\u987B\u662F\u5B57\u6BCD\u548C\u6570\u5B57\u7EC4\u5408
sn.can.not.package.scan=\u6761\u7801{0}\u5DF2\u8D85\u6EDE\u7559\u65F6\u957F,\u4E0D\u5141\u8BB8\u6267\u884C\u5305\u88C5\u626B\u63CF
package.scan.lpn.wip.not.exist=\u672A\u8FDB\u884C\u88C5\u7BB1\u64CD\u4F5C\uFF0C\u4E0D\u5141\u8BB8\u6309reelid\u626B\u63CF
package.scan.generate.sn.or.over.station.failed=\u5355\u677F\u5305\u88C5\u626B\u63CF\u4E0B\u5217\u6761\u7801{0}\u5728\u751F\u6210\u6761\u7801\u6216\u8005\u6279\u91CF\u8FC7\u7AD9\u65F6\u51FA\u73B0\u5F02\u5E38\uFF0C\u8BF7\u786E\u8BA4
item.codes.inconsistent={0}\u4E0E{1}\u7684\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
pk.code.not.exists={0}\u6599\u76D8\u672A\u627E\u5230\uFF0C\u8BF7\u786E\u5B9A
bind.type.is.null=\u8BF7\u8BBE\u7F6E\u7ED1\u5B9A\u7C7B\u578B
item.code.is.exist.in.bind.list=\u7269\u6599\u4EE3\u7801{0}\u5728\u7ED1\u5B9A\u6E05\u5355\u4E2D\u5DF2\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
product.code.is.not.exist.in.bind.list=\u8BE5\u4EFB\u52A1\u5BF9\u5E94\u7684\u6599\u5355\u4EE3\u7801{0}\u672A\u8BBE\u7F6E\u8F85\u6599\u7ED1\u5B9A\u6E05\u5355\uFF0C\u8BF7\u6838\u5B9E\u662F\u5426\u9700\u8981\u7ED1\u5B9A
product.code.and.time.can.not.be.empty.at.same.time=\u7269\u6599\u4EE3\u7801\u548C\u8BBE\u7F6E\u65F6\u95F4\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A\uFF01
setting.date.span.can.not.exceed.a.year=\u8BBE\u7F6E\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC71\u5E74\uFF01
aux.sn.code.is.not.exist.in.bind.list=\u8F85\u6599\u6761\u7801{0}\u7684\u7269\u6599\u4EE3\u7801{1}\u4E0D\u5728\u9700\u7ED1\u5B9A\u6E05\u5355\u4E2D\uFF0C\u8BF7\u786E\u8BA4!
task.no.exist.aux.bind.lock=\u4EFB\u52A1\u53F7{0}\u6B63\u5728\u8FDB\u884C\u8F85\u6599\u7ED1\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
barcode.exist.in.bind.list=\u5E8F\u5217\u7801 {0} \u5DF2\u7ECF\u5728\u4EFB\u52A1\u53F7\u4E2D\u88AB\u7ED1\u5B9A,\u4E0D\u5141\u8BB8\u91CD\u590D\u7ED1\u5B9A\uFF01
barcode.category.not.in.bind.list=\u6761\u7801{0}\u7684\u7C7B\u578B\u4E3A{1}\uFF0C\u4E0E\u5DF2\u7ED1\u5B9A\u8BB0\u5F55\u7684\u6761\u7801\u7C7B\u578B\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u7ED1\u5B9A
barcode.lead.need.over.task.lead=\u8F85\u6599\u6761\u7801\u73AF\u4FDD\u5C5E\u6027{0};\u4EFB\u52A1\u7684\u73AF\u4FDD\u5C5E\u6027{1},\u4E0D\u7B26\u5408\u8F85\u6599\u6761\u7801\u73AF\u4FDD\u5C5E\u6027\u5FC5\u987B\u9AD8\u4E8E\u4EFB\u52A1\u7684\u73AF\u4FDD\u5C5E\u6027,\u8BF7\u786E\u8BA4
mount.type.is.null=\u4E0A\u6599\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
printType.or.printScene.is.null=\u6253\u5370\u7C7B\u578B\u6216\u6253\u5370\u573A\u666F\u4E3A\u7A7A
failed_to_query_the_print_template_information=\u67E5\u8BE2\u6253\u5370\u6A21\u677F\u4FE1\u606F\u5931\u8D25
failed_to_get_the_material_code_for_the_lot=\u83B7\u53D6\u6279\u6B21\u5BF9\u5E94\u6750\u6599\u4EE3\u7801\u5931\u8D25
failed_to_get_the_material_sn_for_the_lot=\u83B7\u53D6\u6279\u6B21\u5BF9\u5E94\u6750\u6599\u6761\u7801\u5931\u8D25
tube.core.prod.have.tech=\u7BA1\u82AF\u4EFB\u52A1\u5B58\u5728\u6280\u6539\uFF0C\u8BF7\u786E\u8BA4!
wip.info.not.match.with.pack.info=\u7BA1\u82AF\u4EFB\u52A1\u8FC7\u7AD9\u4FE1\u606F\u548C\u88C5\u7BB1\u4FE1\u606F\u4E0D\u5339\u914D\uFF0C\u8BF7\u786E\u8BA4\uFF01
tube.core.sn.illegal=\u7BA1\u82AF\u6761\u7801\u4E0D\u662F\u5DF2\u6279\u6B21\u5F00\u593412\u4F4D\u6761\u7801\uFF0C\u8BF7\u786E\u8BA4\uFF01
infor.not.have.material.issuance.details=INFOR\u65E0\u8BE5\u4EFB\u52A1\u7684\u53D1\u6599\u660E\u7EC6\uFF0C\u8BF7\u786E\u8BA4!
good.die.qty.not.equal.task.qty=\u4EFB\u52A1\u6570\u91CF\u548C\u91C7\u8D2D\u9001\u8D27GOOD DIE\u6570\u91CF\u4E0D\u76F8\u7B49\uFF0C\u4E0D\u6EE1\u8DB3\u7BA1\u82AF\u88C5\u7BB1\u6761\u4EF6\uFF0C\u8BF7\u786E\u8BA4\uFF01
barcode.generate.tube.core.lpn.error=\u7BA1\u82AF\u88C5\u7BB1\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u751F\u6210\u7684\u7BB1\u53F7\u548CreelId\u6570\u4E0D\u5339\u914D\uFF0C\u8BF7\u786E\u8BA4\uFF01
tube.core.prod.not.exist=\u6279\u6B21\u627E\u4E0D\u5230\u7BA1\u82AF\u4EFB\u52A1\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
prod.of.tube.core.not.have.work.order=\u7BA1\u82AF\u4EFB\u52A1\u6279\u6B21\u4E0D\u5B58\u5728\u6307\u4EE4\uFF0C\u8BF7\u786E\u8BA4\uFF01
tube.core.not.have.first.can.scan.work.station=\u7BA1\u82AF\u6307\u4EE4\uFF0C\u6CA1\u6709\u9996\u4E2A\u5FC5\u626B\u5DE5\u7AD9\uFF0C\u8BF7\u786E\u8BA4\uFF01
good.die.qty.too.larger=\u7BA1\u82AF\u88C5\u7BB1\uFF0C\u88C5\u7BB1\u6570\u4E0D\u80FD\u8D85\u8FC7\u6307\u5B9A\u6570\u91CF{0},\u8BF7\u786E\u8BA4reelId\u7684GoodDie\u6570\u91CF\u3002
delete_batch_cannot_be_greater_than=\u5220\u9664\u6279\u6B21\u4E0D\u80FD\u5927\u4E8E{0}
old.and.new.pkcode.batch.inconsistent=\u65B0\u6599\u76D8\u4E0E\u65E7\u6599\u76D8\u6279\u6B21\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
create.supplement.bill.param.empty=\u53C2\u6570useFor,deliveryAddress,billInfoList\u5168\u90E8\u4E0D\u80FD\u4E3A\u7A7A
create.supplement.bill.param.error=\u53C2\u6570materialRequisitionBill\u4E0D\u80FD\u4E3A\u7A7A
delivery.not.exist=\u914D\u9001\u5730: {0} \u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
no.enough.stock=\u5E93\u5B58\u4E0D\u8DB3,\u65E0\u6CD5\u521B\u5EFA\u8865\u6599\u5355
work.order.cfg.id.changed=\u5F53\u524D\u6307\u4EE4\u4E0A\u6599\u8868id\u5DF2\u7ECF\u53D8\u66F4\uFF0C\u8BF7\u8FD4\u56DE\u4E0A\u4E00\u7EA7\u83DC\u5355\uFF0C\u91CD\u65B0\u8FDB\u884C\u626B\u63CF
enter.query.criteria.before.exporting=\u8BF7\u5148\u8F93\u5165\u67E5\u8BE2\u6761\u4EF6\uFF0C\u67E5\u8BE2\u540E\u518D\u5BFC\u51FA
sn.is.not.12.numbers=\u6761\u7801{0}\u4E0D\u4E3A12\u4F4D\u6570\u5B57
prodplan.id.is.different=\u8F93\u5165\u6761\u7801\u6279\u6B21\u4E0D\u4E00\u81F4
start.sn.exceed.end.sn=\u8D77\u59CB\u6761\u7801\u4E0D\u80FD\u5927\u4E8E\u7EC8\u6B62\u6761\u7801
exceed.work.qty=\u6761\u7801{0}\u8D85\u51FA\u6307\u4EE4\u6570\u91CF{1}
process.must.exist.in.2038=\u5FC5\u987B\u5728\u6570\u636E\u5B57\u51782038\u914D\u7F6E\u7684\u5B50\u5DE5\u5E8F\u624D\u5141\u8BB8\u6279\u91CF\u8FC7\u7AD9\u626B\u63CF
input.sns.more.than.need.del=\u8F93\u5165\u6761\u7801\u6570\u91CF\u5927\u4E8E\u9700\u8981\u5220\u9664\u7684\u6761\u7801\u6570\u91CF{0}\uFF0C\u4E0D\u80FD\u5220\u9664\uFF0C\u8BF7\u786E\u8BA4\uFF01

sn.list.is.null=\u6761\u7801\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
sn.list.exceed.max=\u4F20\u5165\u6761\u7801\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7{0}
prodplan.exist.220.sn=\u6279\u6B21{0}\u5B58\u5728220\u6761\u7801\u751F\u6210\u8BB0\u5F55\uFF0C\u4E0D\u5141\u8BB8\u63D0\u4EA4\u5355\u677F\u5165\u5E93\u5355
locator.id.not.exist=\u83B7\u53D6{0}\u7684\u8D27\u4F4Did\u5F02\u5E38
carry.account.name.not.null=\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A
cannot.submit.k2.bill=\u5F53\u524D\u4E0D\u5141\u8BB8\u63D0\u4EA4\u5165k2\u5E93\u7684\u5355\u677F\u5165\u5E93\u5355
infor.bill.packing.exception=\u5355\u636E{0}\u4E3Ainfor\u5355\u636E\uFF0C\u4E0D\u80FD\u62D2\u6536\uFF01
erp.move.or.done.error=\u5355\u636E{0}erp\u79FB\u52A8\u6216\u5B8C\u5DE5\u5B58\u5728\u5F02\u5E38\uFF0C\u4E0D\u80FD\u62D2\u6536\uFF01
erp.move.error=erp\u79FB\u5E93\u5F02\u5E38! {0}
material_codes_before_after_replacement_not_same=\u66F4\u6362\u540E\u7684\u7269\u6599\u4EE3\u7801\u4E0E\u66F4\u6362\u524D\u7684\u4EE3\u7801\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
sn_has_no_technical_upgrades={0}\u6761\u7801\u65E0\u6280\u6539\u6267\u884C\u60C5\u51B5\uFF0C\u4E0D\u80FD\u66F4\u6362BOM\u5916\u7269\u6599\u4EE3\u7801\u3002\u8BF7\u786E\u8BA4\u5904\u7406\uFF01
confirm_technical_renovation_and_maintenance={0}\u5B58\u5728{1}\u6280\u6539\u5355\uFF0C\u8BF7\u786E\u8BA4\u662F\u5426\u6309\u6280\u6539\u540E\u5185\u5BB9\u7EF4\u4FEE
org.id.inconsistent=\u7EC4\u7EC7id\u4E0D\u4E00\u81F4:{0}
account.name.outbound.failed={0}\u522B\u540D\u51FA\u5E93\u5931\u8D25
bill.completion.and.storage.failed={0}\u5B8C\u5DE5\u5165\u5E93\u5931\u8D25
scanner_record_not_pass=\u6761\u7801{0}\u5728\u5DE5\u7AD9{1}\u7684\u626B\u63CF\u68C0\u6D4B\u7ED3\u679C\u4E0D\u901A\u8FC7\u6216\u4E0D\u5B58\u5728
obtain_batch_220_information=\u83B7\u53D6\u6279\u6B21220\u4FE1\u606F\u5931\u8D25
sn.format.msg=\u6761\u7801\u5FC5\u987B\u4E3A16\u4F4D\u6570\u5B57\u6216\u5B57\u6BCD\u7EC4\u5408\u300112\u4F4D\u6570\u5B57\u3001P+12\u4F4D\u6570\u5B57\u300113\u4F4D\u4EE5\u6279\u6B21\u5F00\u5934\u7684\u6570\u5B57\u6216\u5B57\u6BCD\u7EC4\u5408
pkCode.param.empty=reelId\u6240\u5C5E\u7269\u6599\u4EE3\u7801\u3001\u4F9B\u5E94\u5546\u7F16\u7801\u6216Uuid\u5C5E\u6027\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
print.instock.data.error=\u83B7\u53D6\u5165\u5E93\u5355\u6253\u5370\u6570\u636E\u5931\u8D25
disk.code.control=\u83B7\u53D6\u76D8\u7801\u7BA1\u63A7\u5931\u8D25
scanning_block_by_block_can_only_scan_barcodes=\u9010\u5757\u626B\u63CF\u53EA\u80FD\u626B\u63CF\u6761\u7801\uFF0C\u4E0D\u80FD\u626B\u63CFreelid\u6216\u8005\u7BB1\u7801
last.process.not.allowed.unbind=\u6761\u7801{0}\uFF0C\u6307\u4EE4\u7684\u6700\u540E\u5B50\u5DE5\u5E8F\u4E0D\u5141\u8BB8\u89E3\u7ED1
submit.warehouse.is.null=\u5165\u5E93\u53C2\u6570\u4E3A\u7A7A
box.is.warehouse=\u6240\u9009\u7BB1\u5DF2\u63D0\u4EA4\u5165\u5E93\uFF0C\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5\uFF01
the_barcode_is_not_from_the_current_batch=\u6761\u7801\u4E0D\u662F\u5F53\u524D\u6279\u6B21\u7684\uFF0C\u8BF7\u786E\u8BA4
request.xian.factory=\u8981\u6C42\u5DE5\u5382\u4E3A\u897F\u5B89\u5DE5\u5382
no.data.to.be.updated=\u6CA1\u6709\u5F85\u66F4\u65B0\u7684\u6570\u636E
zj.sn.not.exist=\u6761\u7801\u5DF2\u5168\u90E8\u89E3\u7ED1
please_select_barcode_for_repair_materials=\u8BF7\u9009\u62E9\u7269\u6599\u6761\u7801
issuance_info_null=\u8BE5\u6279\u6B21\u53D1\u6599\u4FE1\u606F\u4E3A\u7A7A
serial.number.is.empty=\u5E8F\u5217\u53F7\u4E3A\u7A7A
second.side.need.small.sn=\u7B2C\u4E8C\u9762\u8BF7\u626B\u5C0F\u677F\u6761\u7801
call_material_qty_api_failed=\u8C03\u7528\u4F4E\u4F4D\u9884\u8B66\u63A5\u53E3\u5931\u8D25
sn.no.weight.info=\u6574\u673A\u6761\u7801{0}\u672A\u7EF4\u62A4\u91CD\u91CF\u4FE1\u606F\uFF0C\u8BF7\u5230\u6574\u673A\u91CD\u91CF\u7EF4\u62A4\u529F\u80FD\u5904\u7406\uFF01
stock.and.loc.not.same.with.task=\u4ED3\u5E93\u4E0E\u4EFB\u52A1\u914D\u7F6E\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u80FD\u5165\u5F53\u524D\u4ED3\u5E93! {0}
out.put.qty.larger.than.work.order.qty=\u6307\u4EE4\u4EA7\u51FA\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E\u6307\u4EE4\u6570\u91CF\u3002
bill.type.is.not.exist=\u5355\u636E\u7C7B\u578B\u4E0D\u5B58\u5728
pmOrgTransferOrder.transferQuantity.required=\u7269\u6599\u4EE3\u7801\u5B58\u5728\u65F6\uFF0C\u8F6C\u79FB\u6570\u91CF\u5FC5\u586B
transfer.qty.more.warehouse.qty=\u8F6C\u79FB\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7\u4ED3\u5E93\u6570\u91CF
sn.is.not.register=\u6761\u7801\uFF1A{0}\uFF0C\u672A\u6CE8\u518C\u4E0D\u5141\u8BB8\u7EF4\u62A4\u91CD\u91CF
sn.has.weight=\u6761\u7801\uFF1A{0}\uFF0C\u5DF2\u7EF4\u62A4\u91CD\u91CF\u4E0D\u80FD\u91CD\u590D\u7EF4\u62A4
weight.not.empty=\u91CD\u91CF\u4E0D\u80FD\u4E3A\u7A7A
weight.not.valid=\u91CD\u91CF\u4EC5\u5141\u8BB8\u8F93\u5165\u6B63\u6570\uFF0C\u6700\u591A\u652F\u63012\u4F4D\u5C0F\u6570
weight.not.valid.positive.number=\u91CD\u91CF\u4EC5\u5141\u8BB8\u8F93\u5165\u6B63\u6570\uFF0C\u8FDB\u884C\u56DB\u820D\u4E94\u5165\u64CD\u4F5C\u4EC5\u4FDD\u75592\u4F4D\u5C0F\u6570
sn.data.size.over=\u6570\u636E\u91CF\u4E0D\u80FD\u8D85\u8FC7{0}\u6761
sn.storage.can.not.modify.weight=\u6761\u7801\u5DF2\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u91CD\u91CF\u7EF4\u62A4
sn.storage.can.not.remove.weight=\u6761\u7801\u5DF2\u5165\u5E93\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
creator.needs.creation.time=\u521B\u5EFA\u4EBA\u3001\u6700\u540E\u66F4\u65B0\u4EBA\u9700\u8981\u914D\u5408\u521B\u5EFA\u65F6\u95F4\u6216\u6700\u540E\u66F4\u65B0\u65F6\u95F4\u67E5\u8BE2
time.supports.months=\u65F6\u95F4\u8DE8\u5EA6\u6700\u591A\u652F\u6301{0}\u4E2A\u6708
input.search.condition=\u8BF7\u8F93\u5165\u67E5\u8BE2\u6761\u4EF6\uFF1A\u6761\u7801\u3001\u4EFB\u52A1\u53F7\u652F\u6301\u5355\u72EC\u67E5\u8BE2\uFF0C\u521B\u5EFA\u4EBA\u3001\u6700\u540E\u66F4\u65B0\u4EBA\u9700\u8981\u914D\u5408\u521B\u5EFA\u65F6\u95F4\u6216\u6700\u540E\u66F4\u65B0\u65F6\u95F4\u67E5\u8BE2

sn.repair.process.forbid=\u6761\u7801{0}\u4E0D\u80FD\u8FD4\u8FD8\u81F3{1}\u5B50\u5DE5\u5E8F!
sn.not.exist.accept=\u6761\u7801{0}\u4E0D\u5B58\u5728\u5F85\u8FD4\u8FD8\u7EF4\u4FEE\u8BB0\u5F55\u4E0D\u80FD\u8FD4\u8FD8\uFF01

alibaba_customer_tasks_not_allow_in_imes=\u963F\u91CC\u5BA2\u6237\u4EFB\u52A1\u4E0D\u5141\u8BB8\u5728imes\u521B\u5EFA\u8FD4\u5DE5\u4EFB\u52A1
sn_alibaba_customer_tasks_not_allow_in_imes=\u6761\u7801{0}\u4E3A\u963F\u91CC\u5BA2\u6237\u4EFB\u52A1\u4E0D\u5141\u8BB8\u5728imes\u521B\u5EFA\u8FD4\u5DE5\u4EFB\u52A1
repairApproval.positionNumber.required=\u7EF4\u4FEE\u5BA1\u6279\u4F4D\u53F7\u5FC5\u586B
attach.file.list.not.equal=\u9644\u4EF6\u5217\u8868\u6570\u636E\u5F02\u5E38\uFF0C\u8BF7\u6E05\u7A7A\u9644\u4EF6\u5217\u8868\u91CD\u65B0\u4E0A\u4F20\u540E\u518D\u8BD5
attach.file.list.over.ten=\u9644\u4EF6\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC710\u4E2A
position.code.not.exist=\u4F4D\u53F7\u4E0D\u5B58\u5728
exist.repeat.approver=\u5B58\u5728\u91CD\u590D\u5BA1\u6279\u4EBA\uFF0C\u8BF7\u786E\u8BA4
failed.to.process.approval.center.kafka.message=\u5904\u7406\u5BA1\u6279\u4E2D\u5FC3kafka\u6D88\u606F\u5931\u8D25
search.one.time.maximum=\u6279\u91CF\u67E5\u8BE2\uFF0C\u4E00\u6B21\u6700\u591A\u67E5\u8BE2{0}\u4E2A\u6761\u7801
sn.reconfiguration.no.completed=\u6761\u7801\u7269\u6599\u4EE3\u7801{0} \u6539\u914D/\u62C6\u89E3 \u672A\u5B8C\u6210!
sn.received.can.not.un.bind=\u6761\u7801{0}\u5B58\u5728\u5DF2\u63A5\u6536\u7684\u5165\u5E93\u5355\uFF0C\u9700\u8D70\u6539\u914D\u4EFB\u52A1\u8FDB\u884C\u62C6\u89E3
sn.inbound.work.need.choice=\u6761\u7801{0}\u5F53\u524D\u72B6\u6001\u662F\u5165\u5E93\uFF0C\u8BF7\u9009\u62E9\u6307\u4EE4\u518D\u626B\u63CF!
sn.not.belonging.task=\u6761\u7801{0} \u4E0D\u5C5E\u4E8E\u5F53\u524D\u4EFB\u52A1{1}\u8BF7\u786E\u8BA4!
only.approving.can.withdraw=\u53EA\u6709\u5BA1\u6279\u4E2D\u53EF\u4EE5\u64A4\u9500
repairApproval.approvalOperateInfo.required=\u5BA1\u6279\u4EBA\u4FE1\u606F\u6709\u7F3A\u5931\uFF0C\u8BF7\u786E\u8BA4
least.create.date=\u8BF7\u81F3\u5C11\u8F93\u5165\u521B\u5EFA\u65F6\u95F4
query.mes.task.pick.error=\u4EFB\u52A1{0}\u5728MES \u62C6\u89E3/\u6539\u914D\u6761\u7801\u4E3A\u7A7A!
sn.config.task.contain=\u6761\u7801{0} \u4E0D\u5728\u4EFB\u52A1{1}\u62C6\u89E3/\u6539\u914D\u6761\u7801\u6E05\u5355\u4E2D!
only.creater.can.withdraw=\u4EC5\u63D0\u4EA4\u4EBA\u53EF\u64A4\u56DE

no_valid_data_found=\u672A\u67E5\u8BE2\u5230\u6709\u6548\u6570\u636E
print_count_must_be_a_positive_integer=\u6253\u5370/\u8865\u6253\u4EFD\u6570\u5FC5\u987B\u4E3A\u6B63\u6574\u6570
the_box_code_has_been_registered=\u7BB1\u7801{0}\u5DF2\u6CE8\u518C
box_code_not_registered=\u7BB1\u7801{0}\u672A\u6CE8\u518C
the_material_barcode_has_been_scanned=\u7269\u6599\u6761\u7801{0}\u5DF2\u7ECF\u8FDB\u884C\u8FC7\u88C5\u7BB1\u626B\u63CF
not_in_compliance_with_the_packing_rules=\u7269\u6599\u6761\u7801{0}\u4E0D\u7B26\u5408\u88C5\u7BB1\u89C4\u5219
up_to_4_decimal_places=\u6570\u91CF\u5FC5\u987B\u4E3A\u6B63\u6570\uFF0C\u5E76\u4E14\u6700\u591A4\u4F4D\u5C0F\u6570
not.used.tasks=\u975E\u6539\u914D/\u62C6\u89E3\u4EFB\u52A1\u4E0D\u53EF\u4F7F\u7528
item.sn.not.exist={0} \u6761\u7801\u4E0D\u5728\u88C5\u914D\u6E05\u5355\u5185\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u62C6\u89E3
material.not.disassembled={0} \u6761\u7801\u6240\u5C5E\u7269\u6599\u4EE3\u7801\u65E0\u9700\u62C6\u89E3\uFF0C\u4E0D\u80FD\u6267\u884C\u62C6\u89E3\u64CD\u4F5C
repeat.the.operation=\u5F53\u524D\u4EFB\u52A1\u53F7{0}\u4E0ESN{1}\u6B63\u5728\u64CD\u4F5C\uFF0C\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C
does.not.need.to.be.assembled={0} \u6761\u7801\u65E0\u9700\u8FDB\u884C\u88C5\u914D\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u88C5\u914D\u64CD\u4F5C
barcode.is.not.scanned={0} \u6761\u7801\u672A\u8FDB\u884C\u63A5\u6536\u626B\u63CF\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u88C5\u914D
barcode.has.been.bound={0} \u6761\u7801\u5DF2\u7ED1\u5B9A\u5230{1}\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u7ED1\u5B9A
quantity.greater.require.number = \u62C6\u89E3/\u88C5\u914D\u6570\u91CF\u5927\u4E8EERP\u9700\u6C42\u6570\u91CF
task.did.not.receive={0} \u4EFB\u52A1\u672A\u9886\u6B64\u6761\u7801\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
enter.a.non.zero.number=\u8BF7\u8F93\u5165\u4E00\u4E2A\u975E0\u6570
customer.info.lost=\u5BA2\u6237{0} \u7269\u6599\u4EE3\u7801{1} \u4E2D\u5FC3\u5DE5\u5382\u5BA2\u6237\u6570\u636E\u7F3A\u5931!
repair.result.code.lost=\u7EF4\u4FEE\u7ED3\u679C\u6570\u636E\u5B57\u5178{0} \u6CA1\u6709\u914D\u7F6E {1} \u9519\u8BEF\u4EE3\u7801
material.bind.incomplete=\u7BB1\u5355\u4E0A\u4F20\u9700\u8981\u7684\u7269\u6599{0}\u672A\u5168\u90E8\u5728\u9700\u7ED1\u5B9A\u6E05\u5355\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
material.bind.no.end=\u6574\u673A\u6761\u7801{0}\u7269\u6599{1}\u672A\u5B8C\u6210\u7ED1\u5B9A
by.container.item.box.no.empty={0}\u7BB1\u5355\u5FC5\u987B\u4E0A\u4F20\u7269\u6599\u539F\u59CB\u7BB1\u7801\u4E0D\u80FD\u4E3A\u7A7A
by.sn.not.sequence.code={0}\u6309sn\u4E0A\u4F20\u7269\u6599\u5FC5\u987B\u662F\u5E8F\u5217\u7801
fix.bom.can.not.be.replace=fixBom\u9700\u8981\u7269\u6599{0}\u4E0D\u80FD\u4F7F\u7528\u66FF\u4EE3\u7269\u6599
not.found.item.bind.info=\u672A\u83B7\u53D6\u5230\u6599\u5355{0}\u7ED1\u5B9A\u6E05\u5355
the_query_conditions_cannot_all_be_empty=\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u90FD\u4E3A\u7A7A\uFF01
box_code_has_been_printed_reissued=\u7BB1\u7801{0}\u5DF2\u6253\u5370/\u8865\u6253
fix.bom.err.check=fixBom\u6570\u636E\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5
item.not.exists.erp.list=\u7269\u6599{0}\u4E0D\u5728\u9700\u6C42\u6E05\u5355\u5185