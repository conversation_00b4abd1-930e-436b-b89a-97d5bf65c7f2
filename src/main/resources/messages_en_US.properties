RetCode.Success=Success
RetCode.ServerError=Server Error
RetCode.AuthFailed=Auth Failed
RetCode.PermissionDenied=Permission Deniede
RetCode.ValidationError=Validation Error
RetCode.BusinessError=Business Error
factoryId.or.empNo.is.null=factory id or emp number is null
params.error=params error! {0}
factory.id.is.error=factoryId is error
sn_must_be_12_number=sn must be 12 number
sn_should_be_12_num_or_p_with_12_num=sn should be 12 number or P + 12 number
sys.look.up.code.is.null = Please check if the device type{0} data dictionary{1} child code is not configured
eqp.subtype.info.is.null=Get the device type{0} the next device subcategory is empty
reelid.not.register=reeId does not register already
reelId.not.in.table.material.return=reelId:{0} does not in table material return, please confirm
status.is.not.null=reelId:{0} status is {1}, please confirm
qty.cannot.be.zero.or.negative=qty cannot be zero or negative
adding.virtual.station.item.code.is.in.progress=adding virtual station item code is in progress,please try again later
pcb.qty.is.null=pcbQty of the {0} command has not been obtained, and virtual stations cannot be added
item.code.is.exists.in.table={0} is already exists in the table, please confirm
item.code.is.not.exists.in.table={0} is not exists in the table, please confirm
qty.exceeds.the.upper.limit=qty exceeds the upper limit:{},please confirm
standard.qty.is.null=item code:{1} corresponding to child card {0} cannot be obtained
cfgheaderid.info.is.null=the information of cfgHeaderId is null
amount.exception=amount exception
reelId.not.exist=reelId:{0} not exist in factory
prodplanid.not.belongs.to.workorder=task info of item code is not belongs to this work order
new.pkcode.count.must.greater.than.zero=count of new reelId must greater tha
item.code.not.match=item code{0} of new reel is not match the item code {1} of the old reel,please confirm
reel.has.been.used=this reelId has be used by work order:{0}
reel.is.using.please.confirm=this reelId is using,please confirm
avl.server.failed=avl server failed
avl.check.failed=avl check failed
scrap.updateTaskQty.failed = scrap updateTaskQty failed
task_cannot_negative= The number of tasks must be greater than 0

mapcrafttocfgheadid_is_null=mapcrafttocfgheadid is null
map_is_null=map is null
get_workorder_condition_error= Call query instruction interface exception :{0}


feeder.has.binged = This Feeder tray has been {0} binded
matrial.has.used = This material tray has been {0} used
has.binged.to.matrial = Has been bound to material {0}
direction.error.forbidden.receive.items = Material at different manufacturer belt direction, forbidden material
factory.server.is.move = factory server is move
dqas.error=DQAS interface error,interface info:{0}
interface.error=interface error,interface info:{0}
customize.msg={0}

update.error.smt.scan.id.null=update error smt scan id null
unbinding.please.wait=unbinding please wait
call.basicsetting.failure=calling the basicSetting service failed
static.workshop.output.failure=calculation failure of statistical line output
not.maintain.manpower=line not maintain  manpower:{0}
manual.statistic.info=The manual statistics submission is successful. It is expected that the statistics will be completed in 5 minutes. If the statistics are abnormal, the email will be notified
current.locationNo.is.virtual=current locationNo is virtual
item.not.match_bom.item=The item code not match with bom, location:{0} please confirm.
order.item.empty=The order's item code is empty, please confirm.
transfer.scan.interactive.error=Location:{0},item code:{1}, transfer scan error.
location.not.feeder.bound=Location:{0}, not feeder bound.
feeder.not.found.eqp.status=Feeder:{0}, not found feeder equipment status.
feeder.full.station.position.empty=Feeder location is empty.
feeder.location.not.match.bom=Feeder location:{0} not match with bom location {1}, please confirm.

line.code.is.null=line code is null
work.order.no.is.null=work order no is null
line.info.is.null=line info is null

entityplanbasic_is_null=EntityPlanBasic is null
sub_card_erp_compesation_info_null=There is no subcard ERP compensation information to be compensated
sub_card_erp_email_error=Please configure the subcard to asynchronously push ERP exception recipients
sub_card_erp_error=Failed to submit subcard receipt to ERP {0}

workstation.can.not.be.null=workStation can not be null
sn.not.exits=sn not exits
craftsection.of.sn.is.not.dip=craftsection of sn is not dip
workorder.status.is.not.started=workorder status is not started
workorder.loading.scan.not.completed=workorder loading scan not completed
b.smt.bom.detail.of.workorder.is.null=bSmtBomDetail of workorder is null
dip.board.scan.get.redis.lock.failed=last scan is not ended,please try again
smt.machine.material.of.workorder.is.null=smtMachineMaterial of workorder is null
item.of.reelid.is.not.enough=item {1} of station {0} is not enough
work.order.not.found=can not find the work order
sn_verification_failed=sn verification failed
sn_has_been_packed_and_cannot_be_changed=The barcode has been packed and cannot be changed. The box number is:{0}
cannot_mix_packing=The workOrder of barcode is: {0},cannot mix packaging
current_factory_is_not_changsha_factory=current factory is not Changsha factory
unknown_error_for_scan=unknown error for scan
work.order.not.found.route=work order\uFF1A{0} has not route details
task.difference=task is difference
item.difference=item is difference
sn.bound.work.order=sn bound work order
sn.not.found=sn not found
task.not.have.details=task has no more details
task.not.have.item.no=task not have item no
task.not.have.route=task not have route details
line.is.not.null=line can not be null
process.is.not.null=process can not be null
work.station.is.not.null=work station can not be null
get.process.details.failure=fail to get process process details
process.details.not.found=can not found process details
wip.current.process.not.completed=wip current process{0}  not completed
first.bind.craft.section.work.order=please bind craft section{} to work order
task.item.not.have.route=task's item not have not route
sn.not.found.first.process=can not find sn {0} first process
process.is.not.first=process not the first
line.model.not.found=line model can not found
sn.not.found.first.work.station=can not find sn{0} first work station
work.station.is.not.first=work station not the first
work.process.and.station.is.first.please.choose.workorder=current sub work process and station is first, please use standard common scan and choose workorder!
workorder.prepare.not.complete=workorder {0} prepare not completed
task.of.sn.is.null=task of sn {0} is null!
task.of.sn.is.rework=task of sn {0} is rework\u3002 can not pass!
get.task.info.error=get task info error!
not.found.sn.route=can not find sn{0} route
not.found.sn.next.process=can not find sn {0} next process
not.found.sn.scan.route=can not find sn {0} scan route
not.found.wip.next.process=please goto wip next process {0}
update.wip.failure=fail to update wip
insert.wip.scan.history.failure=insert wip scan history fail
update.wip.scan.history.failure=update wip scan history fail
insert.wip.test.record.failure=insert wip test record fail
update.work.order.failure=update work order fail
in.qty.big.than.work.order.qty=work order in-qty big than work order qty {0}
out.qty.big.than.work.order.qty=work order out-qty big than work order qty
craft.section.difference.with.wip=work order craft-section not difference with wip
wip.current.process.not.in.process.group=wip current process not in process group
select.error=select error
reelid.prodplanid.is.null=reelid prodplan id is null

worker.is.null=worker is null
factory.id.is.null=factory id is null
emp.no.is.null=emp no is null
prod.binding.setting.is.null=prod binding setting is null
item.type.is.null=item type is null
product.code.is.null=product code is null
product.name.is.null=product name is null
process.code.is.null=process code is null
route.info.is.null=route info is null
workorder.not.find=workorder is not find

start.time.is.later=start time is later
duration.is.more.than.month=duration is more than a month
RetCode.TimeIntervalError=The query interval associated with the reelid entered is greater than 3 months
RetCode.ReelId.out.length=The length of reelid entered exceeds 100
start.or.end.time.null=start or end time is null
process.need.bind.items.not.enough={1} materials need to be bound in process {0}, and {2} materials are missing.
process.need.bind.items.not.finish=process need bind items is not finish
workorder.info.is.null=workorder info is null

productcode.is.null=productcode is null
workorderno.is.not.null=workorderno can not be null
currprocesscode.is.not.null=workorderno can not be null
the.corresponding.information.is.inconsistent= The corresponding information is inconsistent
mainsn.is.not.null=mainsn can not be null
subsn.is.not.null=subsn can not be null
miansn.binded.complete=mainsn binded complete
binded.and.passworkstation.complete=mainsn binded and passworkstation complete
main.sn.scan.success=main sn scan success.please scan subsn
sub.sn.scan.success=sub sn scan success
bind.relation.not.found=please set bind relation first
workstation.error=can not find workstation
item.info.not.found= {0} item info can not found
item.info.find.error=item info find error
bind.count.more.than.usage= {0} bind count more than usage count
rela.count.err= {0} can not found relation {1} bind count more than usage count
rela.err = {0} can not found relation
sn.has.binded = {0} has binded to {1}
can.not.found.mainsn.item.info = can not found mainsn's item info
sn.not.match.task.no = sn not match task no
line.name.is.null = line name can not be null
line.name.not.found = line name can not found
exter.type.not.found = exter type not found
exter.type.err=exter type error
page.is.null=page is null
params.is.null=params is null

factory.id.null=factoryId is null
head.line.not.equal=head quantity is defferent from line quantity with current billNo
update.commitqty.err=error when update the commit quantity
update.head.err=error when update the warehouse info
ope.type.is.null= operation type is null
ope.condition.is.null = operation condition is null
mounting.data.is.null = mounting data is null
prepare.date.is.null= prepare and mounting data is null

pk.id.is.null=pk id is null
param.is.missing=param is missing
error.skip.has.maintenance=error skip has maintenance

main.sn.null=main sn is null
no.main.sn=main sn {0} is not exist\uFF0Cplease confirm\uFF01
no.bind.info_sub=sub sn {0} has no bind information\uFF0Cplease confirm\uFF01
no.bind.info_main=main sn {0} has no bind information\uFF0Cplease confirm\uFF01
no.bind.info.sn=sn {0} has no bind information\uFF0Cplease confirm\uFF01


condition.not.find=condition not found
form.type.not.find=form type not found
must.has.other.condition= must has other condition except form type
export.time.than.two.month=export time can not than two month
export.time.than.three.month=export time can not than three month


form.sn.no.binding.relation=form sn {0} no binding relation,please confirm
sn.no.binding.relation=sn {0} no binding relation,please confirm
form.sn.and.sn.no.binding.relation=form sn {0}and sn {1} no biding relation,please confirm
form.sn.not.in.wip.extend=sn not in wip extend,please confirm
form.sn.not.in.wip.info=sn not in wip info,please confirm
has.distribution=has distribution



qty.more.than.wait.register=qty more than wait register
get.generate.sn.lock.fail=get generate sn lock fail
source.item.id.is.null=source item id is null
factory.info.is.null=factory info is null
source.item.id.not.sync=source item id not synced! please wait and retry
no.return.service.data=no return service data
no.return.bo.data=datawb service fail {0}


feeder.bind.valid.failure=feeder bing valid falure
item.code.empty= item code cannot empty
work.order.empty= work order cannot empty
line.code.empty= line code cannot empty
module.no.empty= module no cannot empty
location.no.empty= locaiton no cannot empty

sn.parent=Barcode SN has Parent SN!
qty.error=Qty is Error!
source.task.erro=Source task format error!
parent.error=Parent SN format error! 
sn.error=Barcode SN is not this Source task!
no.sn.binded=No SN need to bind!

receive.must.after.qc.transform=receive must after qc transform
receive.must.after.qc.recheck=receive must after qc recheck
no.maintain.skip= no maintain skip
has.not.rollover= Not Returned for Repair
last.process.not.configured=The last process is not configured.
not.found.workorder.route=Can not find workorder {0} route
can.not.found.workstation=can not found workstation info

emp.no.empty=emp no is empty
transfer.inter.empty=need transfer is empty 

str.valid.error = parameter verification failed, verification rule: task number and process instruction number cannot be empty at the same time

params.validate.error=params error:{0}
location.info.lacking=location info lacking:{0}
line.info.lacking=line info lacking: {0}
line.not.support.device.or.not.asm={0}: line not support device or not asm
no.online.work.order={0}\uFF1Ano.online.work.order
program.not.match.online.work.order=program not match online work order
no.item.info={0} no item info
no.work.order.online={0} no work order
program.name.analysis.error={0} program.name.analysis.error
work.order.no.cfg.id=work order no cfg id
not.match.with.bom.detail=not match with bom detail
location.has.transfer.scan=location has transfer scan{0}
previous.process.not.submitted.to.this=The previous work order is not submitted to this process
transfer.box.is.null = transfer box is null
get.box.info.error = get box info error
transfer.error = transfer error
get.box.workOrder.error = get box workOrder error
get.line.of.box.workorder.error = get line info of box and workorder error
sn.of.box.cannot.deliver = sn of box {0} can not deliver : {1}

sn.technical.change=sn {0} technical change can not scan
sn.bar.attr.inconsistent.task.no=sn abr attr inconsistent task no {0}, please confirm
child.item.no.inconsistent.erp.list=child item no {0} inconsistent erp list
query.result.is.null=query result is null
dqas.result.is.not.pass=dqas result is not pass
wip.info.of.parent.sn.not.exist=wipinfo of parent sn {0} not exist
parent.task.of.prodplanno.not.exist=parent task of prodplanno {0} not exist
sn.of.parent.sn.is.empty=sn of parent sn is empty
testtype.is.null=testtype is null

sn.is.null=sn is null
snList.exceed.60=snList exceed 60
sn.length.is.12=sn {0} length should be 12!
sn.length.is.30=sn {0} length should be 30!
wipinfo.of.sn.is.null=wipinfo data of sn {0} is null!
currprocesscode.or.workstation.of.wipinfo.is.null=currprocesscode or workstation of wipinfo is null,sn:{0}!
lookup.1087.is.empty=lookup data of 1087 is empty
processcode.and.workstation.of.sn.is.not.in.lookup=processcode and workstation of sn {0} is not in lookup
prodplanId.is.null=prodplanId is null
success=success
poilt.test.error=poilt test error

insert.sn.err = insert sn err
sn.exis.wipinfo.err = sn exis wipinfo err 
sn.exis.whitelistinfo.err = sn exis whitelistinfo err 

current.factory.not.line.info = There is no production line information in the workshop under the current factory
no.get.factory = Factory id not obtained, please try again
no.get.workshop.code = Workshop code not obtained, please try again
time.not.is.null = Time period interval, greater than 0 is not empty
ageing.length = Aging time, greater than 0 is not empty
predict.out = Theoretical output per hour, greater than 0 is not empty
start.time.not.null = Start time string cannot be empty, string format must be (yyyy-MM-dd HH:mm:ss), example: 1999-01-01 00:00:00
end.time.not.null = End time string cannot be empty, string format must be(yyyy-MM-dd HH:mm:ss), example: 1999-01-01 00:00:00
search.for.interfaces.by.parent.sn=No related information found
sn.no.exis = sn no exis
sn.has.scanned = sn {0} has scanned
sys.look.not.config = sys look {0} param not config
no.configured.template.found = The latest nameplate attribute of the MDS is {0} and no configured template was found
barcode.templates.batch.reprinting = The barcode templates for batch reprinting are inconsistent.
sys.look.param.not.config = 1081 sys look param not config
sys.look.param.setting.error.or.over.range=sys look {0} param setting error or over range
sys.look.zj.param.not.config = 1081001 sys look zj param not config
sys.look.wl.param.not.config=1081004 sys look wl param not config
wl.param.not.found = {0} zj param not found
zj.param.not.found = {0} zj param not found
zj.sn.generate.failed = zj sn generate failed
insert.wip.info.error = insert wip info error
poilttest.save.error = poilttest save error
task.no.is.null = task no is null
line.not.found=line not found
zj.sn.has.scan = {0} has scan,zj sn is {0}
min.time.length.not.zero = min time length not zero
max.time.length.not.zero = max time length not zero

not.get.workshop.code = not get workshop code
date.format.error = date format error
external.type.not.found= external.type.not.found
sys.look.external.type.not.found= sys.look.external.type.not.found
product.type.not.found= product.type.not.found

time.length.progressive = progressive value greater than zero
noarr.and.time.is.empty=noarray and time cannot all be empty
type.is.empty=type cannot be empty
need.bind.workorder=cur process and workstation need bind workorder
cur.workstaion.is.not.outbound= cur workstaion is not outbound
out.bound.workstation.not.found= out bound workstation not found
sn.has.out.bound= {0} sn has out bound,can not scan
Maintained.totalQty.exceed.stantard= Maintained totalQty exceed stantard
item.not.in.task= item.not.in.task
these.matrial.has.used = {0} material tray has been used
parent.pk.not.found = parent pk not found
get.pk.code.info.error = get pk code info error
dip.material.san.not.complete=dip material san not complete
work.order.is.null= work order is null
pk.code.null=ReelId is null
get.avl.error=get avl error
item.no.null=item no is null
pkCode.is.null=pkCode is null
get.itemlistno.error=get itemlistno error
product.task.is.null=product task is null
reelid.sn.is.null=reelid\u7684\u6761\u7801\u4E0D\u5B58\u5728
reelId.is.null=ReelId is not exist
print.info.null=print info is null
lpn.info.null=lpn info is null
parent.pk.null=parent pk is null
ps.task.null=get item no errorget.pk.code.info.error = get pk code info error
exceed.stantard.count=exceed stantard count :{0} the remaining maintainable quantity is {1}
no.message.need.to.deleted=no message need to deleted
isolation.can.not.outbound=isolation can not outbound
sn.not.in.wip= {0} not in wip
sn.and.apply.time.and.receive.time.is.null = At least sn, apply time or receiving time must be entered
data.repair.status.is.not.to.be.received = The data status is not to be received
data.repair.status.is.not.fiction= The data status is not fiction
data.repair.status.is.not.to.be.received.or.maintenance= The data status is not to be received or maintenance
no.permisssion.to.reject.in.received.status = There is no permission to reject in the state of being received
no.permisssion.to.reject.in.maintenance.status= There is no permission to reject under the condition of maintenance

env.check.failed=env.check.failed
return.repair.sn.item.code.not.match=return.repair.sn.item.code.not.match
no.repair.order.found=no.repair.order.found
main.process.not.match=main process.not.match
main.process.is.not.warehouse.entry=main process.is.not.warehouse.entry
return.sn.repair.sn.main=process.not.match=return.sn.repair.sn.main process.not.match
barcode.of.machine.not.allowed.replaced=barcode.of.machine.not.allowed.replaced
repair.record.not.found=repair.record.not.found
repair.sn.not.to.be.repaired=repair.sn.not.to.be.repaired
data.not.found=data.not.found
wip.info.data.not.found=wip.info.data.not.found
sn.sent.repair.not.allowed=sn {} sent repair not allowed
sub.sn.not.found.in.wipinfo=sub sn not found in wipinfo
item.sn.not.found.in.wipinfo=item sn not found in wipinfo
replace.sn.not.found.in.wipinfo=replace sn not found in wipinfo
return.sn.is.empty=return.sn.is.empty
return.sn.is.scraped=return.sn.is.scraped
work.order.has.complete= work.order.has.complete
return.repair.sn.item.no.not.found=return.repair.sn.item.no.not.found
rec.id.is.null=rec id is null
repair.head.info.error=repair head info error
rec.info.error=rec info error
sn.get.item.code=sn get item code
sn.get.item.code.null=sn get item code null
rec.info.null=rec info null
return.repair.sn.is.be.same=return repair sn is be.same
sup.select.error=sup select error
not.find.record.of.this.sn = "not find record of this sn";
not.find= "not find";
this.sn= "\uFF0Cthis sn\uFF01";
never.get.this.sn= "never get this sn\uFF01";
never.get.fromStation.of.sn= "never get fromStation of sn\uFF01";
this.barcode.has.no.binding.records= "this barcode has no binding records";
the.system.barcode.corresponding.to.the.barcode.does.not.exist.in.the.wipInfo.table = "the system barcode corresponding to the barcode does not exist in the wipInfo table";
the.barcode.status.of.the.board.does.not.match.the.source= "the barcode status of the board does not match the source\uFF01";
the.entire.device.can.be.sent.for.repair.only.after.being.ex-warehoused = "the entire device can be sent for repair only after being ex-warehoused\uFF01";
insertPmRepairRBatch= "  insertPmRepairRcvBatch   {}";
submitPmRepairRcvBatch= "  submitPmRepairRcvBatch   {}";
deliveryNo= "\uFF0Cthis deliveryNo\uFF01";
there.is.no.material.code.information= "there is no material code information";cv
return.sn.craf.section.check.data.is.not.found=return.sn.craf.section.check.data.is.not.found
change.item.no.failed=change.item.no.failed
rcv.info.error=rcv info error
parent.pk.exist=pkreant pk exist
no.bom.detail.info=no bom detail info
no.mounting.detail.info=no mounting detail info
no.reel.id.in.mounting=no reel id in mounting
no.location.no.in.his=no location no in his
zj.sn.is.null=zj sn is null
bom.list.is.null=bom list is null
sub.level.query.error=sub level query error
headid.of.workorder.is.null=headid of workorder is null
data_dictionary.over.stock.time.is.null=data_dictionary.over.stock.time.is.null
data_dictionary.repair.tock.time.is.null=data_dictionary.repair.tock.time.is.null
sys.error=sys error
no.query.result=no query result
gx.interface.error=gx interface error

process.is.null=process.is.null
item.name.empty=item.name.empty
line.info.is.not.found.of.code=line.info.is.not.found.of.code
work.order.no.is.not.correct=The status of the instruction is not (submitted, started, pending), and importing the material table is not allowed
line.station.info.is.not.found.of.code=line.station.info.is.not.found.of.code
no.bom.data.to.split=no.bom.data.to.split
tag.splitting.failed=tag.splitting.failed
basic.material.info.is.not.maintained=basic.material.info.is.not.maintained
no.bom.details_data=no.bom.details_data
tag.analysis.failed=tag.analysis.failed
the.rule.of.independent.number.is.not.satisfied=the.rule.of.independent.number.is not.satisfied
there.are.multiple.connectors=there.are.multiple.connectors
the.consecutive.number.rule.is.not.met=the.consecutive.number.rule.is.not.met
not.consecutive.tag=not.consecutive.tag
characters.before.two.tag.numbers.are.not.equal=characters.before.two.tag.numbers.are.not.equal
start.tag.is.greater.than.or.equal.to.end.tag=start.tag.is.greater.than.or.equal.to.end.tag
file.parsing.error=file.parsing.error
file.excelAnalysis.error=File parsing error, please confirm whether the file is encrypted
error=error
task.no.can.not.null=task.no.can.not.null
plan.no.is.null=plan.no.is.null
task.quantity.is.null=task.quantity.is.null
task.quantity.more.than=task.quantity.more.than
existed=existed
fault.code.and.fault.phenomenon.are.required=fault.code.and.fault.phenomenon.are.required
file.initialization.failed=file.initialization.failed
file.is.empty=file.is.empty
data.is.empty=data.is.empty
data.import.succeeded=data.import.succeeded
data.validation.fail=data.validation.fail
the.table.does.not.contain.the.following.columns=the.table.does.not.contain.the.following.columns
data.error.formula.cannot.be.used=data.error.formula.cannot.be.used
data.type_error=data.type_error
paging.query.parameters.is.empty=paging.query.parameters.is.empty
upn.exception.calculation=upn.exception.calculation
uploaded.file.not.found=uploaded.file.not.found
file.parsing.success=file.parsing.success
verification.passed=verification.passed
error.code.is.null=error.code.is.null
error.code.not.found=error.code.not.found
excel.parsing.error=excel.parsing.error
error.reason.code.is.null=error.reason.code.is.null
mt.small.type.not.found=mt.small.type.not.found
pre.processing.destination.code.already.exists=pre.processing.destination.code.already.exists
department.code.is.empty=department.code.is.empty
department.name.is.empty=department.name.is.empty
creator.is.empaty=creator.is.empaty
department.code.is.existed=department.code.is.existed
department.name.is.existed=department.name.is.existed
modifier.is.empaty=modifier.is.empaty
employee.is.not.exist=employee {0} is not exist
factory.code.is.exist=factory.code.is.exist
workorder.craftsection.is.empty=workorder.craftsection.is.empty
line.code.is.existed=line.code.is.existed
workshop.code.is.existed=workshop.code.is.existed
directory.code.is.existed=directory.code.is.existed
insert.data.is.zero=insert.data.is.zero
dictionary.code.is.existed=dictionary.code.is.existed
date.is.inconsistent.please.modify.Client.date=date.is.inconsistent.please.modify.Client.date
server.date= ,server.date:
no.valid.date.information.transmit=no.valid.date.information.transmit 
process.code.is.not.existed=process.code.is.not.existed
packing.record.is.not.found={0}packing.record.is.not.found
not.found.sn.bar.code.record={0}not.found.sn.bar.code.record
failed.to.insert.period.output.information=failed.to.insert.period.output.information
failed.to.update.period.output.information=failed.to.update.period.output.information
workorder.soursys.error=workorder.soursys.error
smt.workorder.update.fail=smt.workorder.update.fail
header.table.insert.fail=header.table.insert.fail
detail.table.insert.fail=detail.table.insert.fail
bsmtbomdetail.table.item.code.error=bsmtbomdetail.table.item.code.error
bsmtbomdetail.table.pcb.qty.error=bsmtbomdetail.table.pcb.qty.not.a.positive.integer
no.station.column=no.station.column
no.item.code.column=no.item.code.column
no.am.column=no.am.column
no.bm.column=no.bm.column
related.line.info.is.not.found=related.line.info.is.not.found
no.work.order.information.for.double.track.line=no.work.order.information.for.double.track.line
item.information.not.maintained=item.information.not.maintained
start.work.order.is.empty=start.work.order.is.empty
offline=offline
ng.no.mounting.detail.info=ng.no.mounting.detail.info
ng.mounting.detail.info.is.not.enough=ng.mounting.detail.info.is.not.enough
work.station.info.is.not.configured=work.station.info.is.not.configured
code.printing.succeeded=code.printing.succeeded
update.mounting.info.failed=update.mounting.info.failed
submit.failed=submit.failed
idention.or.entitytype.can.not.be.empty=idention.or.entitytype.can.not.be.empty
entitytype.must.be.tooling.or.fixture=entitytype.must.be.tooling.or.fixture
idention.is.existed=idention.is.existed
device.interaction.information.insert.failed=device.interaction.information.insert.failed
file.format.error.please.use.excel=file.format.error.please.use.excel!
create.excel.error=create.excel.error
batch.query.contract.numbers.fifty.at.most=batch.query.contract.numbers.fifty.at.most
batch.query.return.order.five.hundred.at.most=batch.query.return.order.five.hundred.at.most
batch.query.packing.box.order.five.hundred.at.most=batch.query.packing.box.order.five.hundred.at.most
sn.is.existed.the.work.station=Barcode {0} has been scanned in the work station {1}, please confirm
failed.to.insert.temporary.table=Failed to insert temporary table, please contact administrator
job.information.is.empty=Query job instruction table, job information is empty
pk.code.info.is.empty=PK code info lookup failed!
exist.smt.material.mounting.info=exist smt material mounting info,please scan the new reelId
pk.code.info.qty.is.null=PK code info quantity is null!
total.number.of.defective.products.is.zero=The total number of defective products is 0
repair.source.is.empty=Repair source is empty, please resubmit
building.is.empty=building is empty
sn.has.not.been.repair.returned=The barcode has not been returned for maintenance
whole.machine.can.only.be.sent.out.for.repair=The whole machine can only be sent out for repair
no.production.record.found.for.this.barcode=No production record found for this barcode
not.repair.two=sn {0} is not two-repair. It can't be returned
craft.section.inconsistent.can.not.be.returned=sn {0} original process {1} inconsistent to now can not be returned
line.code.inconsistent.can.not.be.returned=sn {0} original line code {1} inconsistent to now can not be returned
from.station.inconsistent.can.not.be.returned=sn {0} original from station {1} inconsistent to now can not be returned
repair.status.not.complete.can.not.be.returned=sn {0} repair status {1} not complete can not be returned
input.type.not.has.sn.can.not.be.returned=sn {0} input type not has sn can.not.be.return
input.type.not.has.no.sn.can.not.be.returned=sn {0} input type not has no sn can.not.be.return
prodplanid.has.no.complated.sn=prodplanid {0} has no complated sn
prodplanid.shall.be.seven.digits=prodplanid shall be seven digits
repair.return.qty.error=repair return qty error
sn.delete.fis.error=sn {0} delete fis error
mes.repair.restore.error=mes repair restore error
input.record.not.found=sn {0} input.record.not.found
it.needs.to.be.repaired.first=sn {0} needs to be repaired first
no.wip.info.record.or.not.repairing=sn {0} no wip info record or not repairing
not.be.repairing=sn {0} not be repairing
repair.zs.vaildate.fail=Repair Chinese verification failed. Specific information: {0}
no.record.in.scan.history.table=No record in scan history table
craft.section.not.loaded=craft section not loaded
failed.to.update.qty=Failed to update Qty
batch.insert.transaction.table.failed=Batch insert transaction table failed
no.data.to.save=No data to save
sn.is.scrapped=sn is scrapped:{0}
sn.of.the.device.is.scrapped=The sn of the device has been scrapped
status_no_restore_device=The device to the sn has not been restore
device.to.sn.is.scrap=device.to.sn.is.scrap
no_wipinfo_of_sn_corresponding_device=no wipinfo of sn corresponding device
no.information.to.submit=No information to submit
sn_is_submited_by_other=sn_is_submited_by_other
repair.input.is.required.before.scrap=Repair input is required before scrap!
sn.has.no.planid=sn has no planId, cannot be processed!
item.no.is.not.exist=item no {0} is not exist
multi.brand.not.support=bill {0} has multi brand
search_reelid_error=search the historical trace reelid record table failed
not.the.same.planid=It is not a planId , cannot be processed !
not.be.repair.summit=Can't be scrapped without repair submission!
cannot.scrap.repeatedly=Cannot scrap repeatedly!
afferent.value.is.null=afferent.value.is.null
uncleaned.sn.in.box=There are uncleaned sn in the box, please confirm
process.inconsistencies.in.referral.process=There are inconsistencies in the process, please re-transmit
workoder.not.matched.prodplanid=workoder:{0}  not matched to prodPlanId {1} please confirm !
first.process.workStation.of.workOrder.not.found=first process or first workStation info of workOrder is not found. Please confirm! 
workOrder_status_incorrect=The command status is incorrect, please confirm!
exception_in_get_next_process=exception in get next process
sn_not_belong_prodPlanId=sn {0} is not belong prodPlanId {1},please confirm!
period.output.error=period output error
xType.failed.warehouse=Get {0}  [{1}] Failed to enter the warehouse, please confirm !!
box.contents.are.empty=Box contents are empty
barcode_is_controlled=Barcode is controlled, please contact administrator
delete_successed=delete successed
spi_and_instruction_is_empty=spi and instruction is empty
linecode.tracesource.error=linecode: {0} tracesource: {1} error
sn_input_error=sn input error
workorder_is_null_or_less_than_seven=workorder_is_null_or_less_than_seven
Barcode.prodPlanId.error=prodPlanId of Barcode error
line.info.not.maintained=The SPI has called the number of boards, and the line information is not maintained: lineCode-{0}
smt.investment.scan.success=SMT investment scan success
spi.deductions.and.retroactive.exception=Deductions and retroactive calculation abnormalities in SPI
workorder_id_is_null=workorder_id_is_null
moduleno.is.null=Traceability is calculated by module, but moduleNo is null
last.module.data.is.passed.in=Traceability is not calculated by module, last module data is passed in
pk_code_info_not_found=pk code info not found ! pkcode:
itemqty_is_null=itemqty is null! pkcode: {0}
qty_is_null_of_reelId=qty is null of the reelId
bomqty_is_null=bomqty is null!cfgHeaderId: {0} locationNo: {1}
smt_tracing_cfgheaderid_is_null=smt tracing cfgheaderid is null
workShop_is_null=workShop is null
Incorrect.traceback.type.parameter=Incorrect traceback type parameter
mac.address.not.obtained=mac.address.not.obtained
failed.to.insert.or.update.binding.information=Failed to insert or update binding information, please confirm
no.started.work.order=no started work order
standard_dosage_cannot_be_zero=Standard dosage cannot be zero
spi_data_process_success=spi data process success
work_order_process_code_group_is_null=work order process code group is_null
param_validation_error=param validation error
has.be.seted.spi.deductions.and.retroactive=has.be.seted.spi.deductions.and.retroactive
smt.material.low.level.calculation.successful=SMT material low level calculation successful
input_content_format_incorrect=[NG], the input content format is incorrect, please check!
work_order_not_summited=work order not summited
work_order_qty_is_full=work order qty is full
work_order_not_bound_craft=work order not bound craft
employee.info.is.not.found=[NG], employee information does not exist, please re-enter it!
scans_number_cannot_be_zero=scans number cannot be zero
please_scan_the_slab=please scan the slab
slab_is_splited=The slab {0} has been split. Please scan for another slab.
failed.to.insert.staging.scan.table=Failed to insert staging scan table
failed.to.delete.staging.scan.table=Failed to delete staging scan table
please.scan.the.small.board=Big board: {0}, please scan the small board
bigSn_has_not_been_disassembled=bigSn: {0} has not been disassembled, please scan the small board
bigSn_is_scaned=bigSn: {0} is scaned, please scan the small board
failed.to.update.staging.scan.table=Failed to update staging scan table
bigSn_is_scaned_qty=smallSn of bigsn {0} is scaned {1} , Please continue to scan the smallSn
operation.configuration.is.incoreect=[NG], the operation configuration of the process is incorrect, please check
sn_is_empty_of_work_order=sn is empty of the work order,please check
write.back.failed=Write back failed, please contact the administrator
scan_in_workstation_of_next=Please scan in the work station of {0}
sn_is_bad=Bar code is bad
work_order_not_done=sn: {0} work order: {1} did not go through all processes
fail.too.many.need.repair=Too many failures, please send for repair
is.scaned=is scaned
please.go.to.next.process=please go to {0} process
please.go.to.workstation.of.next.process=please go to workstation of {0}
scan.in.workstation.of.next.process=scan in workstation of {0}
save.succeed=save succeed
write.back.step.failed=write back step failed
write.back.step.succeed=write back step succeed
defective.products.cannot.be.stored=Defective products cannot be stored
products.need.test=Products need to do single board test
sn.process.is.changed=The current barcode operation has changed. Please scan again
not.allowed.to.invest.across.prodPlanId=It is not allowed to invest across batches!
sn.is.not.exits=sn.is.not.exits
sn.not.delong.item.code=The sn does not belong to {0} item code, please confirm! "
no.item.info.of.item.code=No item info of item code
sn.has.not.been.invested=sn has not been invested!
sn.out.of.prodPlanId.range=sn is out of prodPlanId range, prodPlanId quantity is
workOrder.is.full=The workOrder is full and cannot continue to invest
wip.workOrderNo.not.exist=WIP workOrderNo: {0} does not exist, please check
route.not.completed=The route is not completed, and the bill of materials cannot be switched
wip.process.not.in.route=wip current process is not in the route path
not.found.work.order.scan.route=not found work order {0} scan route
to.wip.next.process=Please go to the next process of WIP current process ( {0}) 
param_is_empty=param is empty
form_sn_can_not_be_null=form sn can not be null
get_eqp_info_exception=get eqp info exception
eqp_info_not_found=eqp_info not found
set.batch.bind.info=Please set the batch binding information in the general scan
prodPlanId.is.not.match=The current barcode prodPlanId does not match the bound batch, and cannot be mixed for batch scanning
status.not.warehouse.in.or.out=The veneer bar code {0} is not warehouse entry or warehouse out,  binding is not allowed!
formSn.error.during.unbind=formSn {0}is abnormal during unbinding, please contact the administrator
formSn.binding=formSn binding ...
mac_address_can_not_be_null=mac address can not be null
no.device.for.mac.address=No device information found for mac address
mac.device.no.line.or.workstation=Line body or work station information is not maintained in the device corresponding to the mac address
process.is.null.line.station=The process corresponding to this line and station is null, please check
no.address.in.plc=The corresponding IP information was not queried when the PLC was released.
maintain.url.plc.data.dictionary=Please maintain the url address released by the online platform plc in the data dictionary
not.tested.or.test.failed=The board barcode has not been tested or failed, and offline scanning is not allowed!
sn_is_arehouse_entry=The current status of the bar code {0} is not in storage, and boxing is not allowed!
sn.is.scrapped.not.allowed.box=The bar code {0} has been scrapped and is not allowed to be boxed!
sn.not.been.returned.not.allowed.box=The bar code {0} has not been returned for repair and is not allowed to be boxed!
sn.is.existed.in.box=The veneer bar code {0} exists in the box {1}, please confirm!
no.test.instructions.or.instructions.not.scheduled=No test instructions or test instructions not scheduled
not.allowed.to.offline=The last procedure type of the test instruction is automatic test, which is not allowed to go offline
update_prodplanid_information_failed=Update prodPlanId information failed
failed.to.get.redis.lock=Failed to obtain redis lock resource, please try again
sub_sn_has_bound=The sub barcode has been bound to {0} and duplicate binding is not allowed
shipment.data.is.empty=Shipment data is empty
entity.is.is.empty=Entity ID is empty
cannot.find.system.user=Cannot find system user
demand.note.number.is.empty=Demand note number is empty
item.code.is.null=item code is null
id.is.null=id is null
Create.the.SmtSnMtlTracingT.table=Create the SmtSnMtlTracingT table:
exception_info_fo_failure=The exception information is:
create_smtsnmtltracingt_table_by_quarter=Create SmtSnMtlTracingT score table by quarter
must_specify_time=Must specify scan start time and end time
scan_start_time_cannot_later=Scan start time cannot be later than scan end time
primary.key.not.null=Primary key must have a value
factory_no_material_traceability=Factory {0} Material traceability has not been configured
search_result_is_null=search result is null
search_bom_detail_id_fail=search bom detail id fail
query_upn_failed=Query work order corresponding uph failed
available.quantity.query.failed=Available quantity query failed
querying.standard.usage.failed=Querying standard usage failed
process.name.error=The format of the process name is incorrect
corresponding.process.information.not.found=corresponding process information is not found
wip.extendinfo.table.save.failed=Failed to save Wip ExtendInfo table
wip.extendinfo.table.update.failed=Failed to update Wip ExtendInfo table
tracking.or.task.no.not.exist='Plan tracking number / task number' does not exist!
routes.in.batch.not.stored.in.warehouse=Routes in this batch have not been stored in the warehouse.
wait_in_warehouse_not_exist=wait in warehouse is not exist
warehousing.failed=Warehousing failed!
organization.id.not.match=The organization ID of this task does not match the organization id of the subinventory; please check, thank you!
task.id.not.exist=The original taskId of the task does not exist
task.not.query.requirements=The task did not query the corresponding requirements
task_org_id_is_null=Task organization ID is null
task_id_is_null=task id is null
tasks.qty.is_zero=Total number of tasks is zero
cannot.exceed.total.number=The sum of the submitted number and the submitted number cannot exceed the total number of tasks
item.code.not.enough.sent=TaskNo {0} Item code {1} The number of sent items is insufficient and cannot be completed. This submission failed
imu_update_failed=imu update failed
in.warehousing.succeed=in warehousing succeed
not.warehouse.out=The veneer bar code {0} is not warehouse out
no_warehouse_in=no warehouse in
sn.summit.succeed=scan barcode summit succeed
sn.must.start.seven=The bar code must be a 12 digit number starting with 7
scan_succeed=scan_succeed
sn.in.process.workstation=The barcode is in the sub-process : {0}, workStationName: {1}, please confirm!
routeId.is.empty=routeId is empty!
routeDetail_info_not_found=No routeDetail information found!
no.need.to.bind.materials=No need to bind materials
daughter.card.binding.verification.passed=Passage verification of the Daughter-mother card
daughter.mother.card.binding.verification.failed=Daughter-mother card binding verification failed! Operation {0} needs to bind material {1}, which is still need {2}
sn_not_returned=barcode is not returned
sn_does_not_exist_repair=Repair record of Bar code does not exist
sn_is_repair_two=Bar code needs to be sent for repair in two dimensions
sn_is_repaira_succeed=The barcode has been repaired!
sn.previous.process.not.started=The previous process has not yet started, please execute the previous process first
wrok.order.status.is.not.pending.commited=The current instruction status is not pending or committed
related.line.exist.started.work.order=Associated line body: {0} has a start instruction, please complete first
work_order_smt_info_is_null=Instruction SMT message is empty
mount.info.is.null=Loading mount information is empty, please import the loading mount table first
previous.process.not.exist=The previous process does not exist, please check the process information
remark.info.maintenance.exception=remark information maintenance exception
get.work.order.info.failed=Failed to get instruction information through source task
call.service.failed=Call to service failed
work.order.maintenance.info.not.found=No command capacity maintenance information was found
min.num.of.work.order.is.one=The minimum number of instructions is 1
minimum.output.is.zero=The minimum output is 0
workOrder.qty.cannot.less.output.qty=Instruction quantity value cannot be less than output quantity
interface.returned.data.exception=Interface returned data exception
mount_info_not_found=No mount information found!
mount_save_failed=Failed to save mount information, an exception occurred!
mount_update_failed=Failed to update mount information, an exception occurred!
material.already.exists.station=Material already exists at this station, please replace!
module.detail.not.found=No module loading details found!
material.feeder.info.not.found=No Material feeder information binding found!
source.cannot.be.empty=Source cannot be empty;
import.start.end.time.cannot.null=Import start time and deadline cannot be empty;
time.format.is.wrong=Time format is wrong!
reel.id.is.wrong=Reel ID is wrong
reel.id.verify.succeed=Reel ID verify succeed
operation.pk.code.exception=Operation PK code information is abnormal
export_data_exception=export data exception
scheduled.task.executed.successfully=Scheduled push task executed successfully
scheduled.task.executed.failed=Scheduled push task executed failed
summit_successfully=summit successfully
batch.over.station.scan.successful=Batch over station scan successful
data.is.precessed=The data has been processed. For details, please see the table in the lower right corner!
batch.over.station.scan.failed=Batch over station scan failed, please check batch stop scan record!
please.enter.the.correct.barcode=Please enter the correct barcode information
formSn.bind.error=formSn binding error, please contact the administrator
Sporadic.formSn.bind.error=Sporadic.formSn binding error, please contact the administrator
sn.contral.verify.success=Bar code control verification passed
call_successfully=call successfully
box.is.used=Boxes have been used by other demand forms during shipment
pda.boxed.unknown.error=Unknown abnormality occurred during PDA shipment by box, please contact customer service
email.maintenance.information.not.found=Can't find email maintenance information!
Failed.get.email.maintenance.info=Failed to get email maintenance information for SMT scan rate!
deal.feeder.bind.info.error=An exception occurred when processing the feeder information binding information
update.data.failed.conversion.strategy.three=inFailed to update the data in the scenario where the conversion strategy three is executed and the material table id is different
save_mount_history_data_error=An exception occurred when saving the details of the loading history
generating.excel.attention.mail=Generating excel, please pay attention to the mail
qty.to.stored.not.enough=Insufficient quantity to be stored
document.no.is.existed=\u5355\u636E\u53F7\u5DF2\u5B58\u5728\uFF01
file_import_verify_failed=File upload verification failed
file_import_failed=File upload  failed
failed.to.save.file.data=Failed to save file data
failed.to.get.file.token=failed to get file token

sn.too.bigger=sn too bigger
get.lpn.error=get lpn error
get.bom.error=get bom error
get.lpn.null = Container code does not exist
get.pkcode.null = No REELID information was queried

bom.contents.code.qty.error=Failed to get the number of boxes
bom.itemcode.less=Please scan the tray with material code [{0}]!
box.contents.code.qty.less=[{0}] quantity of raw material container is not enough
bom.itemcode.then=Material with material code [{0}] is not in the loading table!
box.contents.update.error=Box content update failed
get.sub.bom.info.error=get sub bom info error

material.qty.greater.bom.usage=Material code usage is greater than bom usage, please modify sub-operation attributes
item_code_not_exist_bom_list=Item code is not in the bom list
dip_scan_finished=The bar code dip board scanning has been completed, please confirm
greater.than.bom.qty=The quantity of material loading table + deducted quantity + plug-in quantity is greater than BOM quantity, please confirm
worker_station_cannot_be_empty=worker station cannot be empty
sn.not.exist.wip=Complete machine barcode corresponding to barcode does not exist in tabulation
sn.is.returned=This barcode has been returned, please confirm
get.zs.workstation.relationship.failed=Failed to obtain the corresponding relationship between the pilot docking work station and imes sub process and work station
sn.has.no.scan.record.in.workstation=This barcode has no scanning record in {0} work station, please confirm
failed.to.get.scan.history={0}Failed to get scan history
failed.to.get.zs.processCode.list=Failed to obtain the list of sub processes for ZS
call.zs.service.failed=Call zs service failed
failed.to.bind.for.zs=Failed to bind for ZS
failed.to.get.assemblyrelation=Failed to obtain assembly relationship in commissioning

item.sn.is.null = Item barcode cannot be empty
is.control.is.null = Whether the control information cannot be empty
get.item.sn.pk.null = The information of the material barcode is not found in the PK_CODE_INFO table

get.replace.sn.pk.null = No replacement barcode information was found in the PK_CODE_INFO table
not.control.sn.env.fail = Failed to pass the verification of environmental attributes of uncontrolled materials
get.contr.reelidsn.null=The REELID barcode information of the material barcode is not queried in the control table
replace.sn.same=The replacement barcode cannot be the same as the material barcode
get.zj.env.error = The interface of the obtained environmental protection attribute is abnormal

get.itemsn.env.null = Environmental attributes of barcode not obtained
get.replacesn.env.null = Environmental property of replacing barcode is not obtained
replacesn.env.fail = Replace the barcode's environmental protection attribute verification failed
get.style.null = No model information of REELID
get.prodplan.null = No REELID bill of lading code

sn.compare.error=Barcode {0} The current process is {1}, please confirm whether the barcode has been transferred

sn.exceed.99999=sn can not exceed 99999
task.of.prdoplanid.not.exist=task of prdoplan {0} not exist
sn_no_last_process=The barcode is not closed and exists in the final process:{0}
sn_submitted=sn submitted:{0}
item.no.of.prdoplanid.not.exist=item no of sn {0} and prdoplanid {1} not.exist
workorder.of.prdoplanid.not.exist=workorder of prdoplan {0} not exist
workorder.of.prdoplanid.data.error=workorder of prdoplan {0} data error
workorder.not.draw.up=workorder {0} not draw up
sn.total.count.exceed.taskqty=sn total count [{0}] exceed taskqty [{1}]
ct.route.detail.of.itemno.not.exist=route detail of itemno {0} not exist
sn.between.range.exist=sn between {0} - {1} has exist
printqty.can.not.bigger.than.taskqty=create qty can not be bigger than taskqty

get.item.code.null = The system cannot obtain the material information of the material barcode, please carefully check whether the material meets the requirements
get.ms.item.code.ptp.error = Getting the material code of the controlled material barcode is abnormal
get.item.code.not.consistent = The material code of the material barcode is inconsistent with the input, please check carefully

replace.sn.is.null = Replacement barcode cannot be empty
get.time.range.null = No scanning history information of the barcode has been queried, and the start and end time of the retrospective query cannot be obtained
get.item.sn.null = No item barcode information was found
item.code.null = Item code cannot be empty

failed.to.get.aps.stock=fail to get aps stock
sn.record.not.found={0}barcode record not found, please confirm
get.re.work.sn.max.num.failed=Failed to get the maximum number of reworked barcodes
exceeded.max.num.of.rework.sn=The maximum number of {0} exceeded, please confirm
barcode.material.code.inconsistent={0} barcode material code is inconsistent with {1}, please confirm
barcode.task.no.inconsistent=Original task of {0} barcode is inconsistent with {1}, please confirm
reelId.not.register=reelId:{0} not register
reelId.empty=reelId empty, please confirm
reelId.no.smt.return=Reel ID:{0} no smt return
reelId.work.order.empty=reelId:{0} work order empty
reelId.no.work.order=reelId:{0} no work order
reelId.work.order.not.closed=reelId:{0} work order not closed
alloc.no.setting.data.tip=alloc no setting data tip
lookup.no.is.null=sys Lookup is null

factory.id.not.match=The factory ID input error, this interface can only query the factory ID: {0} maintenance records
item.code.is.null.wl=Please enter the item code
pagenum.is.null=Please input the page number and quantity of the pagination, the quantity of each page cannot be greater than 1000

date.range.is.null=Extraction date and task type cannot be empty
sn.work.station=sn work station error
sn.can.not.empty=sn can not be empty
sn.all.not.found=sn all not found
sn.process.code=sn process code error
sn.no.wip=sn {0} no wip
lookup.6001.empty=wip daily report lookup type 6001 is empty
lookup.6008.empty=machine daily report lookup type 6008 is empty
lookup.221103.empty=lookup type 221103 is empty
export.data.more.than=export data more than {0}
date.range.too.long=date range too long
date.range.is.empty=date range is empty

sn.not.in.bill=sn{0} not in bill
bill.can.not.close=bill\uFF1A{0} can not close

box.is.null= box is null
box.content.is.null=box content is null
box.content.not.all.in.bill= failure , box content not all in bil
bill.be.scan.receive=bill{0} is be scan receive
bill.can.not.reject=bill{0}be scan
board.scan.code.list.to.be.bound.is.null=board scan code list to be bound is null
sn.is.not.exists.in.table.wipinfo=sn is not exists in table wipinfo
main.sn.is.binded.completed=main sn is binded completed
sub.sn.is.binded=sub sn is binded on main sn {0}
material.code.corresponding.to.the.sub-bar.code.is.not.in.the.material.list.to.be.bound=material code corresponding to the sub-bar code is not in the material list to be bound
whether.data.dictionary.information.is.controlled.by.batch.without.parent.cards=whether data dictionary information is controlled by batch without parent cards
the.batch.to.which.the.sub-bar.code.belongs.is.bound.to.the.batch.of.the.master.bar.code=the batch to which the sub-bar code belongs is bound to the batch of the master bar code
itemCode.is.binded.complete=itemCode {0} is binded complete
the.previous.subcode.is.not.bound.successfully=the previous subcode is not bound successfully
material.code.corresponding.to.the.main.code.is.not.in.the.material.list.to.be.bound=material code corresponding to the main code is not in the material list to be bound

sn.not.exist.wip.info=Barcode is not exist in wipInfo
please.start.the.order.first=Please start the order {0} first
cannot.reason.not.blank=cannot binding reason is blank
production.not.match.plan=Production Code And Batch Are Not Match

main.sn.is.not.belongs.to.this.workroderno=main sn is not belongs to this workroderno
main.sn.is.not.belongs.to.this.prodplanid=main sn is not belongs to this prodplanid

sn.has.been.storaged=Barcode{0} has been put into storage, please confirm
requirementinfo.not.allow.empty=itemCode,leadFlag not allow empty
actual.stock.qty.inconsistent.submitted.quantity=The actual stock in quantity is inconsistent with the submitted quantity, please confirm!
get.erp.move.qty.error=Task{0} Abnormal access to ERP mobile quantity 

barcode.generate.error=barcode generate error! {0}
update.aps.qty.error=update aps qty error! receive id: {0}

req.bill.no.is.null=req.bill.no.is.null
commit.qty.morethan.max=commit.qty.morethan.max
req.bill.is.lock=req bill is lock
pallet.number.box.information.is.not.empty=pallet number box information is not empty
warehouse.mode.req.empty=warehouse.mode.req.empty

current.status.not.allow.null=current status not allow null, billNo is {0}
no.fallback.privilege=the user does not have fallback privileges
process.fallback.is.not.support=backout is not supported in this process
no.reject.permission=the current user does not have permission to dismiss
sn.is.repeat=barcode is repeat\uFF0Cplease confirm {0}\uFF01
transfer.no.operation = {0} task does not require regularization operation!
transfer.barcode.verification={0} barcode has been submitted or is in the process of being confirmed. Please refresh the task again!
transfer.barcode.length.verification=A maximum of {0} barcode submissions are allowed at once!
transfer.sigle.stock.verification=The source warehouse data for the current task needs to be consistent, please check!
file.empty=file empty
file.init.fail=file init fail
receptionBy.is.null =sn receptionBy is null :
returnedBy.is.null =sn returnedBy is null :
crafsection.is.null =sn crafsection is null :
sub.crafsection.is.null = sn sub crafsection is null :
sn.is.null.param=sn is null param :
sn.was.to.be.repaired=sn was to be repaired
only.upload.file.like.xls.xlsx.csv =only upload file like xls or xlsx or csv
last.operation.not.found=last operation not found\uFF0CrouteId\uFF1A{0}
sn.is.not.unlock =sn {0} is not unlock 
params.err=Params Error
sns.is.null=sns {0} is null
never.find.storag.name =never find storag name
sn.process.cannot.be.repair=Barcode sub {0} process cannot be sent for repair,please confirm!
sn.workOrderNo.is.null=sn{0}\uFF1AworkOrderNo is null , please check!
warehouse.billNo.is.null=warehouse billNo is empty
warehouse.billNo.not.exist=warehouse billNo not exist,please confirm!
warehouse.billNo.isReceived=warehouse billNo isReceived,please confirm!
lpn.no.warhouse.record=There is no storage record of container{0}, please submit the production warehousing order first!
lpn.status.is.not.in.storage=Container is not in storage, please confirm! 
transfer.warhouse.failed=Transfer failed, please confirm!  errorMsg:{0}
erp.machine.move.or.complete.failed=ERP machine movement or completion failure, please confirm! errorMsg:{0}
warehouseentryinfo.is.locked=this warehouseEntryInfo is locked, billNo is {0}
warehouseEntryInfo.stockType.is.null= This WarehouseEntryInfo stockType is null , billNo is {0}
locator.is.null= locatorId is null
out.type.is=When the outbound type is according to the material code + quantity, it cannot be empty
update.task.info.error=update task info failed
get.field.delivery.date.failed=Failed to get the field of delivery date table corresponding to main operation code, please confirm!
failed.to.get.code.due.date.data=Failed to get code due date data\uFF0Cplease confirm!
process.transfer.workOrderNo.is.null=Process Transfer workOrderNo is null
process.transfer.prodPlanId.is.not.match= Process Transfer workOrderNo is not match , source:{0}, target:{1}

task.is.running=task {0} is running! please wait and retry

call.aps.write.back.fail=call aps write back fail {0}
current.factory.is.write.back=\u5F53\u524D\u5DE5\u5382\u6B63\u5728\u56DE\u5199APS

reelid.prepareinfo.is.not.match=workorder and lineCode with this REELID {0} is not match with the page information\uFF0CIf you want to hand over, please reset the page data
reelid.notin.prepare=There is no REELID information in the preparation table
reelid.have.picked=This REELID has been received by {0}
reelId_has_been_handed_over=reelid{0} has been handed over\uFF0Cplease confirm!
reelid.workorder.error=This REELID has finished sending instructions:{0}
reelId.not.exists.center.factory=ReelId does not exist in the central factory.
reelId.item.type.a=the reelId A materials do not support the side warehouse. Please return to the central warehouse directly\uFF01
reelId.wetLevel.not.supported=The current moisture-proof level of reelId is {0}. It is not allowed to return materials.
uph.null=Command not received: {0} capacity
work.order.smt.null=The order WORK_ORDER_SMT information does not exist

materialRequisitionBill.is.null=Requisition bill number cannot be empty
materialRequisitionBill.info.null=The current material requisition number information has not been queried
billStstus.is.null=The bill status cannot be empty
confirm.status.error=Only the documents whose status is'Pending Confirmation' can be confirmed
recitem.status.error=Only receipts with a status of'confirmed'

changeType.is.null=Change type cannot be null
oldMaterialStatus.status.error=The old material status cannot be empty
scrapReason.status.error=Reason for invalidation cannot be empty
send.status.error=Only the documents with the status of "received" can be sent
inforbillno.is.null=INFOR picking list number cannot be empty

return.item.error={0}: The material has been used and cannot be returned
return.item.error.idnull=The maintenance detail ID cannot be empty
return.item.status.error=Only when the material status is: Picked, Issued, Received can the material be returned
bill.status.error=The current bill status is: {0}, the material issuance related operations cannot be performed, please confirm
bill.no.is.empty=bill no is empty
bill.not.exists=bill not exists
factory.id.not.conifg.lookup=factoryId {0} not conifg lookup

ptp.interface.error=Point-to-point interface call exception: {0}, exception information: {1}
bom.null=Bbom is null
return.bom.null=return.bom.null
current.batch.is.being.submitted=The current batch has an operation that is being submitted for warehousing. Please try again later
current.batch.is.task.being.submitted=The current task is being submitted for regularization and put into storage. Please try again later
current.batch.is.being.changed=The current batch has an operation that is being changed
param.container.is.empty=The param container is empty, please confirm!
not.found.container.entry=AN entry for the container was not found
sn.is.not.planid=sn is not this prodplanId,please confirm!
item.civ=Civ control materials, not allowed to return to the line side warehouse
dip.smt.bom.info.workOrder.locked=dip smt bom info is updateing or inserting,please wait

item.code.not.in.bind.list=item.code.not.in.bind.list

pda.transfer.scan.reelid.redis.locked=reelid {0} is transfer scaning,please confirm!
sn.is.scaning=sn is scaning,please wait!
locked.bill.no.str=The following bill is being operated, please do not submit it repeatedly: {0}

get.lookup.value.error=get lookup value error! {0}
log.process.error=log process error

reelid.no.flow.cannot.return={0} There is no online side warehouse outbound transaction, and it is not allowed to return online side warehouse

failed.to.update.sys.look.up.meaning=failed to update sys look up meaning

details.is.empty=Details is empty\uFF01
prodplanId.not.match=The following bar code :{0} maintenance batch and interface selected batch inconsistent please confirm!
material.requisition.record.not.been.processed={0}The material requisition record has not been processed, please confirm!
repair.material.requisition.is.not.used={0} material in {0} repair material requisition is not used and cannot be returned
material.is.not.used.cannot.scarp=The {1} material in the {0} Repair Picking List is not used and cannot be scrapped
no.maintenance.record.sub.sn.consistent={0}No maintenance record consistent with barcode {1} of picking record sub component found, please confirm! 
exist.material.requisition.cannot.be.rejected=There is a material requisition and cannot be rejected
sys.lookuptype.6671.is.null=sys lookuptype 6671 is null,please confirm!
sn.must.be.greater=The barcode serial number must start from 1
wip.daily.statistic.job.running=wip daily report statistics job has been executed
erp.warehousing.failed=ERP storage failed{0}
unknown.error.handler=There is an unknown error in the system, the error log number is: {0}, please contact the operation and maintenance personnel to deal with it, thank you!
unBinding.set.is.not.empty=Abnormal binding data is set for this batch, and the sub-barcodes of the cannot be scanned!


repair.status.error=documents in non fake status cannot be submitted
center.factory.bom.error=Failed to call the center factory to query the bom classification
b.smt.bom.info.empty={0}\u5DE5\u5E8F\u5BFC\u5165\u7684\u4E0A\u6599\u8868\u4E3A\u7A7A\u6216\u7528\u91CF\u90FD\u4E3A0
reelid.is.used.by.workOrder=The tray has been used by the {0} workOrder
multiple.data.for.the.reelid=There are multiple pieces of data in the table for this material tray, please confirm!
no.data.for.the.itemCode.in.bom.smt=The instruction corresponding to the feeding table does not have this material {0}, please confirm!
pda.transfer.scan.not.completed={0} The instruction transfer scan is not completed, and material receiving is not 
the_station_has_a_loading_record=The station has a loading record
failed.to.get.tag.information=failed to get tag information
the.number.of.trays.cannot.be.zero=The number of trays cannot be zero
reelid.is.not.for.this.workOrder=There is no information about the barcode or the barcode is not for this instruction
do_not_allow_batch_writing_to_the_whitelist=Batch writing to the whitelist is temporarily not allowed
sn.length.must.twelve=The length of the barcode {0} must be 12 digits
special_characters_in_barcode=There are special characters in the barcode {0}
sn_qty_cannot_exceed_100=The number of whitelist barcodes added in one batch cannot exceed {0}
sn.length.must.twelve.and.speialChart=The length of the barcode {0} must be 12 digits. {1} There are special characters, please confirm!
sn.itemNo.is.null=barcode {0} material code is empty
there_is_data_with_a_blank_barcode=there is data with a blank barcode
barcode.get.template.error=Failed to call the barcode center to get the template list
barcode.get.template.error.msg=Failed to call the barcode center to get the template list: {0}
failed_to_obtain_pdm_english_code=failed to obtain pdm english code
failed_to_get_barcode_center_url=Failed to get the URL of the barcode center interface for data dictionary configuration
failed.to.get.barcode.center.download.url=Failed to obtain the download address of the barcode center adapter
failed_to_adjust_barcode_center_print_interface=failed to adjust barcode center print interface
task_isLead_data_dictionary_does_not_exist=The task environmental protection attribute does not exist in the data dictionary, please confirm!
the_task_is_printing_barcodes=The task is printing barcodes, please try again later!
the_task_insufficient_number_of_printable_barcodes=The number of barcodes that can be printed for this task is insufficient {0}, please confirm!
failed_to_write_print_record=Failed to write print record
failed_to_query_print_record=Failed to query print record
the_barcode_to_be_printed_is_not_continuous=The barcode to be printed is not continuous, please confirm!
call.barCode.center.to.print.falied=Failed to print barcode in barcode center:{0}
the_barcode_has_not_been_printed=The barcode has not been printed and cannot be reprinted
duplicate_barcode=Duplicate barcode, please confirm!
workorder.not.found.prodplanid=Batch {0} No instructions
failed_to_generate_reelid_in_the_factory=failed to generate reelid in the factory
failed_to_generate_reelid_in_the_factory_new=failed to generate reelid in the factory:{0}
new.lpn.qty.must.less.old.lpn.qty=The number of new containers must be less than the original number of the old container and less than the remaining number of the old container
combined_materials_are_not_allowed_to_be_split=combined materials are not allowed to be split
the_current_order_is_being_unboxed_or_deducted=The current order is being unboxed or deducted, please try again later!
failed_to_update_box_content_data=Failed to update box content data
failed_to_insert_box_content_data=Failed to add box content data
work.order.no.status.error=The current workOrderNo status is not {0}. Please confirm that the project cannot be started!
failed_to_update_the_qty_of_reelids=Failed to update the number of factory reelids in the center
sync_tech_info_error=sync technical info error
smt.machine.h.running=The current workOrderNo is scanning historical header tables. Please try again later!
barcode.get.template.info.failed=barcode get template info failed
pass.scan.new.running = The current instruction sub-procedure is being scanned in batches. Please try again later!
sn.is.doing.other.opt = Sn is doing other operate! please wait and retry {0}
transfer.billNo.not.exist=transfer bill no is not exist, please confirm!
transfer.application.type.not.zj.bm=tramsfer application type not ZJ to BM, please confirm!
transfer.billNo.closed=transfer bill no is closed, please confirm!
lpn.no.transfer.record=There is no transfer record of container{0}, please submit the production transfer order first!
lpn.status.is.not.approved=Container is not approved, please confirm!
bill.details.sn.status.error=\u5355\u636E\uFF1A{0},Sn:{1},\u660E\u7EC6\u72B6\u6001\u5F02\u5E38\u8BF7\u68C0\u67E5\u3002
batch.scan.not.morethan.500=Cannot submit more than {0} barcodes at one time
batch.scan.limit.exceed=This batch has only {0} codes, not allowed to exceed
failed_to_update_the_transfer_request_form=update transfer application form
failed_to_get_bill_of_material_version=Failed to get bill of material version
prodplan.exist.sn.record=prodplan exist sn record, cannot input no sn record
prodplan.exist.no.sn.record=prodplan exist no sn record, cannot input sn record
prodplan.is.null=prodplan is null
prodplan.qty.less=prodplan qty cannot less than 1
prodplan.qty.more=prodplan qty cannot more than 1000

asyn.compsation.exe.error=Asynchronous compensation execution failed, the last execution has not ended
insert.asyn.compsation.param.error=ID, business code busineCode, parameter params cannot be empty
bucode_or_compensationtimes_is_null=Business code busineCode and one compensation times compensation cannot be empty
businenesscode_is_null=Business code cannot be empty
email_setting_error=Please correctly configure the email sending configuration, recipient, subject, and sending interval. Data dictionary 2039

get_erpinfo_error=Failed to obtain SOURCE_LINE_ID: {0} ERP information
create.billNo.error=The call center factory failed to generate the bill number.

failed_to_push_the_assembly_relationship=Failed to adjust pdm to push the assembly relationship
call.barCode.center.expandQuery.barCode.falied=Failed to get barcode extension information from barcode Transfer Center: {0}
sn.workOrderNo.info.is.empty.in.wipInfo=Barcode {0} in WIP_ Info no instruction information, please confirm!
failed_to_call_pdm=failed to call pdm:{0}
get.pdm.bom.ver.failed=failed to get bom version
create.bill.no.error=The center failed to generate the self-adding ticket number
spm.bill.status.exception=The SPM bill: {0} status is not submitted and cannot be rejected.
lms.bill.packing.exception=The LMS bill:{0} have generated packing information in LMS and cannot be rejected!
failed.to.get.sn.info=failed to get the barCode info
failed.to.get.task.item.list.info=Failed to get BOM list for task {0}
Failed.to.obtain.material.substitution.relationship=Failed to obtain material code {0} substitution relationship
Serial.code.does.not.allow.batch.scanning=Serial code does not allow batch scanning
insufficient.quantity.to.be.bound=Insufficient quantity of material code {0} to be bound
item.code.has.been.bound=Item code {0} has been bound
failed_to_obtain_the_standard_quantity_of_material_code=Failed to obtain the standard quantity of material code {0}
item_code_exist_in_list_more_than_one=There are  multiple repleace item codes {0} in the binding list or task requirement list
failed_to_get_material_standard_usage=Failed to get material standard usage
failed_to_get_task_info=failed to get task info for workorderNo
same.batch.code.cannot.be.bound.to.repeatedly.sn=The same batch code cannot be bound to the same primary barcode repeatedly
use.qty.can.not.be.decimal=When the barcode is a serial code, the quantity cannot be decimal
failed.to.get.erp.task.qty=failed to get erp task qty
replace.sn.item.code.veify.failed= replacement barcode is not {0}

call.service.error=Service invocation error:{0}--{1}
spm.bill.no.empty=bill No is empty
spm.bill.no.exist=Bill No. does not exist in Infor. Please confirm!
spm.bill.type.not.support=This type of bill is not currently supported for {0}, please confirm!
spm.bill.type.empty=There is a PKG ID/SN whose type is null under the bill. Please confirm!
spm.bill.completed=The {0} bill has been received. Please confirm it!
spm.bill.type.multiple=There are multiple types of barcodes under the ticket number. Scanning is not supported. Please confirm!
spm.bill.scan.sn.lock.fail=The PKG ID/SN {0} is being scanned, please try again later!
spm.scan.params.check.msg=The parameter {0} cannot be null. Please confirm!
spm.bill.barcode.no.exist=Current PKG ID/SN {0} does not exist under document number # {1}, please confirm!
spm.scan.barcode.complete=The current PKG ID/SN {0} has been scanned under document number # {1}, please confirm!
spm.barcode.qty.error=The PKG ID quantity alignment does not pass, the material quantity is {0}, please confirm!
spm.bill.no.lock=BillNo {0} is adding. Please try again later!
work.order.not.started=work order {0} is not started
trans.org.erp.fail=Inter organization transfer information push to ERP failed
trans.org.erp.fail.msg=Inter organization transfer information push to ERP failed, exceeding the number of retries.\nDEAL_ID\uFF1A{0}
workOrder.status.must.be.follows=workOrder status must be follows{0}
work.order.preparation={0} The instruction is in the first preparation to change the material sheet. Please try again after the first preparation is completed.
b.smt.upload.ok=Import of the loading sheet is completed.
smt.location.info.empty=The following SMT station position information does not exist, please confirm!{0}
match.line.code.empty=face type {0} no production scheduling line body
barcode.repair.not.returned=Barcode {0} repair not returned, please confirm
sn.status.not.warehouse.in=barCode {0} not warehouse entry,please confirm\uFF01
sn.is.warehouse.out=barcode {0} is warehouse out
blink={0}
b.smt.upload.ignore=List information not updated lineCode:{0},craftSection:{1}.
sn.null={0} sn not found, please check.
sn.cannot.return={0} sn sub process is {1}, cannot return.
setting.is.null=setting {0} is null
sn.qty.over=sn qty over than {0}
sn.itemno.not.same={0} sn's itemNo is {1}, not same to other, please check.
sn.process.not.fit={0} sn's process is {1}, cannot return
barcode.is.controlled.param=Barcode {0} is controlled, please contact the administrator.
wip.next.process=Go to {0} Next operation to scan.
poilt.test.error.param=poilt test error,{0}
spm.call.error.param=spm controlled, {0}
has.not.rollover.param=sn\uFF1A{0} Repair is not returned.
sn.is.scaning.param=SN: {0} is scanning, please try again later!
is.scaned.param=SN:{0} has been scanned.
to.next.process.code=Please go to the ({0}) process scanning!
sn.has.rel.task={0} barcode has been bound to the {1} rework task
sn.not.in.warehouse=The barcode {0} does not exist in the {1} warehouse. Please confirm
source.warehouse.is.null=Source warehouse is null. Please confirm
isLead_data_dictionary_does_not_exist=The environmental protection attribute {0} does not exist in the data dictionary, please confirm!
sn.is.associated.with.zte.serial.code= bar code {0} has been associated with ZTE serial code, please scan ZTE serial code
sub.sn.hb.attr.veify.not.passed=Barcode {0} environmental protection attribute verification failed
rework.task.difference=The task of the barcode is {0}, which cannot be scanned with mixed tasks
sn.not.rework.task=Barcode is not a rework task and cannot be scanned with mixed tasks
reelid_is_registered=reelid has registered, please confirm!
not.rework.cannot.replace=Replacement operations are not allowed on non-rework job barcodes
sn.has.bind.main.sn=The barcode {0} has been bound to the barcode {1} of the whole machine, and replacement operation is not allowed
item.no.not.same.cannot.replace=The replacement barcode bill of materials is inconsistent with the original barcode bill of materials, and replacement is not allowed
not.find.sn.cannot.replace=The barcode {0} record not found, replacement is not allowed
sn.has.create.rework.task=Rework task has been created for {0} barcode, replacement operation is not allowed
sn.not.exist.stock=The barcode {0} does not exist in the standard stock, and replacement is not allowed
cannot.find.veneer.sn=The veneer barcode corresponding to the barcode {0} of the whole machine could not be found
unconfirmed_reelid_cannot_be_returned=Unconfirmed reelid {0} cannot be returned, please confirm!
return.times.more.than.max= reelid{0} return.times.more.than.max{1}, cannot be returned
batch_or_barcode_cannot_be_empty=batch or barcode cannot be empty
sns.is.not.exits=Barcode {0} does not exist
batch_barcode_repeats=Batch barcode {0} is duplicated
prodplanId.is.not.exits=Batch {0} does not exist
must_be_less_than_1000=Batch operation batch barcode must be less than 1000
the_current_document_is_in_operation=The current document is in operation, please wait
the_current_document_is_not_in_this_state=The current document is not in {0} status, please confirm
sn.lock.error.msg={0} barCode is locked in process {1}. The locked order number is {2}. Please contact {3} for handling
source.task.lock.error.msg=Batch {0} is locked in process {1}. The locking order number is {2}. Please contact {3} for handling
child.lock.error.msg={0} sub BarCode {1} is locked, and the locked order number: {2}. Please contact {3} for handling
child.source.task.lock.error.msg=Batch {0} is locked (ticket No.: {1}). Please contact {2} for handling
details_cannot_be_empty_for_submit=Details cannot be empty when the document is submitted
criteria_cannot_be_null_at_the_same_time=Locked single number batch barcode time cannot be empty at the same time
the_time_frame_cannot_be_greater_than_one_year=the_time_frame_cannot_be_greater_than_one_year
document_number_cannot_be_empty=document number cannot be empty
lock_type_cannot_be_null=lock type cannot be null
document_details_cannot_exceed_1000=Document details cannot exceed 1000
lock_reason_cannot_be_null=lock reason cannot be null
sn_item_cannot_exceed_100=sn cannot exceed 100
call.ifis.timeout=call iFIS timeout
call.ifis.error=call iFIS error {0}
repair.info.too.many=more than 1000 maintenance information
insufficient.number.sn.tobe.repaired=There are {1} barcodes to be repaired in batch {0}, and the number is insufficient
lock_bill_no_is_empty=lock bill no is empty
un_lock_user_no_is_empty=un lock user no is empty
un_lock_reason_is_empty=un lock reason is empty
lock_bill_no_not_exist=billno not exist
unlock_sn_is_empty=unlock sn is empty
unlock_plan_is_empty=unlock plan is empty
unlock_craft_sn_all_empty=unlock craft sn all empty
sn_more_than_1000=sn more than 1000
sn_more_than_100=sn more than 100
sn_more_than=sn more than {0}
plan_more_than=plan more than {0}, {1}
task_more_than=task more than {0}, {1}
wip_not_exist=sn or lpn not exist: {0}
lpn_wip_not_exist=the box code does not contain the box content
lpn_is_null=the box code cannot be empty
item_no_must_same=item no must same, {0}
task.org.id.not.match=The task {0} organization ID is inconsistent with the sub inventory organization ID {1}. Please check
no_sup_step_task=Mixed task submission only supports standard model tasks

pkcode_has_received=Reel has been accepted,please confirm
too_much_data_error=Too much data,please confirm
pkcode_not_used_error=Reel has not been feeded,please confirm
workorder_not_start=no workorder has been started by this Old reel,please confirm
workorder_not_found=workorder not found
pk_code_not_regist=ReelId Not Regist
old_and_new_pkcode_can_not_be_null=old and new pkcode can not be null
wms_has_the_reelid=The reelId exists in the Wms inventory, which leads to the inability to prepare materials
check_wms_stock_error=the process verifying whether the reelId is in the WMS inventory occurs error
pdaRsError_001=Cann't found old reelid, please confirm.
pdaRsError_002=Cann't found new reelid, please confirm.
pdaRsError_003=Please confirm the direction is consistent or not?
pdaRsError_004=Material at different manufacturer belt direction, forbidden material.
pdaRsError_005=The scanned new material tray and the reelId corresponding to the location of the material tray are inconsistent. Please confirm
pdaRsError_006=The reelId exists in the Wms inventory, which leads to the inability to prepare materials.
pdaRsError_007=The supplier codes of the old and new trays are inconsistent
pdaRsError_008=new Pkcode {0} direction is {1},old Pkcode:{2} direction is {3}.Please confirm
pdaRsError_009=new Pkcode {0} direction is {1},old Pkcode:{2} direction is {3}.Please confirm
pdaRsError_010=New Pkcode: {0}, Old Pkcode:{1} Supplier Code Inconsistent
pdaRsError_011=The direction of the new material plate:{0} is {1}, and the direction of the old material plate:{2} is {3}, and the direction is inconsistent.
parent.sn.can.not.be.null=parent sn can not be null
list.child.sn.can.not.be.null=the list of Childsn can not be null
operator.can.not.be.null=the operator can not be null
parent.sn.format.error=parent sn {0} format error, parent sn need start with 'P' and is followed by 12 digits
child.sn.is.not.scanned=The barcodes {0} of the subboard is not scanned
ct.route.detail.of.prodplanId.not.exist=The routing information corresponding to batch {0} does not exist
preparation_can_not_unlock=bill in perpara can not be unlock
reelId.of.prodplanid.is.null=the proplanId {0} which the reelId registered is not found
sn.binding.not.complete=SN {0} In operation {1} veneer binding is not complete please confirm.
failed_to_get_task_info_by_prodplanId=Failed to obtain task information corresponding to batch {0}
product.code.and.time.can.not.be.empty.at.same.time=product code and time can not be empty at same time
setting.date.span.can.not.exceed.a.year=setting date span can not exceed a year
failed_to_get_task_info_by_taskNo=Failed to obtain task information corresponding to taskNo {0}
failed.to.get.assemblyrelation_for_sn=failed to.get assembly relation info for sn
product.type.can.not.be.null=product type can not be null
error.msg.from.mds.is.null=error msg from mds is null
the.entered.barcode.is.not.a.board.barcode=the entered barcode is not a board barcode
corresponding.stage.is.null=corresponding stage of lookupCode 6915 is null\uFF0Cplease config or refrash
an_assembly_is_not_completed=The barcode {0} has not been assembled once
secondary_assembly_not_completed=The secondary assembly of barcode {0} has not been completed
param.is.null=param is null
tran.pk.error=The {0} comprehensive material preparation is not completed, and the transfer scanning cannot be performed.
prepare.info.of.feeder.not.exist=feeder({0}) has no material preparation record
feeder.no.is.null=feederNo can not be null
line.name.not.existing=No line information found, please confirm
line.not.support.intelligent.feeder=line do not support intelligent feeding operation.
prodplanId.of.reelId.not.match.prepare=The prodplanId of reelId is inconsistent with the prepare/mouting table.
feeder.insert.avl.error=location: {0}, item code: {1}, feeder inserts AVL verification error
feeder.insert.avl.check.failed=Feeder insertion, AVL check result: {0}
feeder.has.multiple.record=Feeder{0} has multiple records.
params.not.match.prepare.data=The data of Feeder{0} in the  preparation table is inconsistent with the  parameters
workorder.already.have.prepare.data={0} workorder already have advance material preparation information
pls.renew.tray.before.completing=Please renew the material tray before completing the work. Location, material code: {0}
prodplanid.not.belongs.to.workorder.or.next=task info of item code is not belongs to this work order or next work order
no.material.transfer.operation.allowed = There are unused materials {0} in batch {1}, reelid: {2}, and material transfer operation is not allowed
transfer.strategy.configuration.line.mismatch=Transfer strategy configuration line mismatch
feeder.not.exist.mouting=Feeder{0} did not find the corresponding material information, please confirm!
work.order.no.of.prepare.error=the work order number of the date in the prepare table is null or incorrect, please confirm!
tar_work_order_is_null=target work order is null
work_order_no_smt_detail=work order {0} has no smt detail on module {1}
work_order_module_no_prepare=work order {0} has no prepare in module {1} locations: {2}
line_module_has_mouting=line {0} module {1} has mouting
failed_to_query_en_code_barcode_information=Failed to query the barcode information corresponding to the EN code {0}
failed_to_adjust_barcode_center_interface=failed to call barcode center interface url\uFF1A{0}
failed_to_register_barcode_in_barcode_center=Failed to adjust barcode center registration barcode: {0}
location.sn.error={0} location Sn is empty or malformed

location_not_exist=The location does not exist
line_no_sup_model_chg=Line {0} does not support changing line by module
line_no_sup_in_feeder=Line {0} does not support smart feeders
smt.a.bom.not.active=the smt-a bom of the prodPlanId {0} is non-existent or invalid.
smt.b.bom.not.active=the smt-b bom of the prodPlanId {0} is non-existent or invalid.
not.have.smt.work.order= the prodplanId {0} does not exist or  do not have smt work order.
batch.complete.analysis.not.undone=Batch complete analysis not completed.
reel.id.is.null=reelId is null, please confirm!
reel.id.not.register.item.check=reelId {0} is Not registered to the proplanId {1}, please confirm!
lfid.not.match.register.info=the entered lfid{0} is not match the registration information of  reelId{1}, please confirm!
reel.id.not.pre.check=there is no pre-inventory record for reelId{0}, please confirm!
reel.id.finish.check=reelId{0} has been counted, please confirm!
reel.id.forbidden.check=reelId{0} is not available for inventory, please confirm!
no_prodplan.id.need.compute=no prodplan need compute
all.prodplan.id.exist=all prodplan exist
get.pack.spec.faild=get packSpec faild
update.last.tssue.seq.date.faild=update last tssueSeq date faild
get.pk.code.info.failed=Get batch material issuance information failed
barcode_repeat=Barcode {0} Repeats {1}
the_barcode_corresponds_to_the_wrong_batch=The barcode {0} corresponds to the batch {1}
barcodes_are_not_numbers=barcode {0} is not a number
the_barcode_length_is_not_12_digits=Barcode {0} is not 12 digits long
sub_processes_cannot_be_scrapped=Sub-operation {0} cannot be scrapped
batches_are_not_numbers=batch {0} is not a number
the_barcode_exists_in_the_document_in_preparation=The barcode exists in the document in preparation
sn_scrap_is_not_allowed=Scrap is allowed only when the barcode status is "repair completed" and the sub-category of repair is "Scrap" \uFF1A{0}
parameter_cannot_be_empty=Barcode, batch, scrap category, scrap category, scrap reason description, responsible department, applicant cannot be empty
workshop_name_does_not_exist=workshop name does not exist
scrap_class_name_error=scrap class name error
subcard_barcode_cannot_be_imported=The barcode of the daughter card cannot be imported
job_number_does_not_exist=The application number does not exist
import_details_verification_failed=Import details verification failed
appendix_info_save_failed=appendix info save failed
import_data_cannot_exceed=Import data cannot exceed {0} pieces
the_time_frame_cannot_be_greater_than_half_one_year=The time range cannot be greater than half a year
current_document_information_not_found=The current document information is not found,please confirm!
the_current_status_of_the_document_is=The current status of the document is {0}, not {1}, please confirm
to_find_document_details=To find the details of the document {0}
sub_operation_does_not_meet_scrap_barcode=The barcode {0} sub-process does not satisfy the scrap barcode
approver_cannot_be_empty=Engineer of Production Quality Department, Finance Department of Supply Chain Finance Department, Minister of Production Quality Department, Deputy General Manager of Production cannot be empty
approver_cannot_be_empty_two=Workshop Director, Finance Department of Supply Chain Finance Department, Head of Component Production Department, Deputy General Manager of Production cannot be empty
approver_cannot_be_empty_common=Approver cannot be empty
failed_to_call_center_factory_interface=Failed to add approval information interface for adjustment center factory: {0}
withdraw_approval_information=Failed to withdraw the approval information interface of the adjustment center factory: {0}
get.uuid.info.failed=get uuid info failed
scrap.bill.no.status.is.not.approval=The status of the scrap application form is not "Approval Completed", please confirm!
scrap.bill.no.not.exist=The scrap document number {0} does not exist, please confirm!
scrap.bill.prod.plan.id.not.have.task.info=No corresponding task information was found for batch {0}, please confirm!
sn.status.is.repair.scrap=sn{0} status is repair scrap

feeder_in_use=feeder {0} is in used, can not unbind
reel_id_in_use=reelId {0} is in used, can not unbind
reel.id.item.check.info.not.exist=There is no inventory record for reelId {0} and cannot be printed, please confirm!
reel.id.item.check.not.finish=reelId {0} has not been counted and cannot be printed, please confirm!
the_current_document_status_cannot_be_updated=The current document status is {0} and cannot be updated be{1}
prod.plan.id.and.task.no.all.empty=The batch and plan tracking numbers cannot be all empty. Enter at least one of them, please confirm!
result.list.empty=The query result is empty, please confirm whether the input query conditions are correct!
inventory.information.not.found=Inventory information for batch {0} not found
the_following_fields_cannot_be_empty=Test Type, Barcode, Test Result, Test Time, Tester, Source System cannot be empty
lfid.not.found.in.prod=No issuance record with lfid {1} found in batch {0}
reel.id.registered={0}REEL ID has been registered, please confirm!
record.not.found.with.barcode={1}lfid No material issuance record with barcode {0} was found in. Please confirm!
barcode.has.registered=The {1} barcode in {0} lfid has been registered and cannot be registered again
the_power_module_must_pass_bimu=The power module must pass imu
when_the_test_type_is_generic=When the test type is general, it must be passed to the sub-process, the station
wip.not.exist.wip.info=The barcode {0} does not exist in the wip info sheet
callback_to_write_microservice_failed=Callback to write microservice failed,Url:{0}
callback_to_write_microservice_failed_msg=Failed to call back to write microservice, error message {0}
no_corresponding_site_information_found=The site information corresponding to {0} was not found
sn_item_batch_can_not_be_empty=Barcode, batch, bill of material code ,maintenance time cannot be empty at the same time
information_corresponding_to_the_sub_process_not_found=The operation information corresponding to the sub-operation code {0} was not found
get.bom.null=Failed to get BOM data of bill of material code {0}
failed_to_obtain_the_corresponding_process_path=Failed to obtain the process path corresponding to the batch or bill of material code {0}
bill_of_material_code_does_not_exist=The bill of material code {0} does not exist, please confirm!
no_sub_processes_to_control=There are no sub-processes that need to be controlled in this batch of materials, please confirm!
barcode_repeats=Duplicate barcode {0}
sn_batch_exception_skip_information_has_been_maintained=The batch {1} corresponding to barcode {0} has maintained abnormal skip information
sn_item_exception_skip_information_has_been_maintained=The bill of material {1} corresponding to barcode {0} has maintained abnormal skip information
batch_exception_skip_information_has_been_maintained=The bill of material {1} corresponding to batch {0} has maintained abnormal skip information
barcode_has_maintained_exception_skip_information=Barcode {0} has maintained exception skip information
batch_has_maintained_exception_skip_information=Batch {0} has maintained exception skip information
item_exception_skip_information_has_been_maintained=BOM {0} has maintained exception skip information
there_is_no_test_record_for_the_barcode=There is no test record for barcode {0} in sub-process {1}
barcode_test_failed=The barcode {0} failed the test in sub-process {1}, reason: {2}
barcode_test_failed_two=Barcode {0} failed the test in sub-process {1}
barcodes_are_not_the_same_batch=The entered barcode is not the same batch, please confirm
the_process_path_needs_to_have_warehousing=The process path of the instruction {0} requires at least 2 nodes and needs to have warehousing
failed_to_get_the_corresponding_last_station_of_process_code=test control:Failed to obtain the last station of line body modeling corresponding to sub-process {0}
repair.form.item.sn.null=Item barcode cannot be empty
repair.form.repaired.sn.null=Repair barcode cannot be empty
repair_quantity_control={0} barcode {1} code is the number of repairs has reached the threshold, and it is not allowed to be sent for repair
repair.form.replaced.sn.has.bound=The barcode of the replaced material is bound
replace.sn.env.query.null.by.code.center=The environmental property of the replaced material barcode in the barcode center query is empty
replace.sn.env.convert.error.by.dict=The barcode of the replaced material is converted to the specific environmental value through the data dictionary and an error occurs
time.can.not.be.null=time can not be null
time.zone.cannot.span.thirty.days=time zone cannot span thirty days
time.zone.cannot.span.180.days=time zone cannot span 180 days
reel_feeder_both_bound=reel id and feeder both bound
reel_feeder_both_replace=Both reelId and feeder replaced at the same time cannot have binding records
the_same_line_can_only_start=The same line body can only start {0} {1} instructions
the_same_line_body_can_only_start_one_command=The current line body has started other instructions
return.error=Batch: {0}, material code: {1}, sendQty: {2}, returnQty: {3}, reelIdQty: {4}. Please confirm if the \
  materials cannot be returned again!
print.machine.is.null=Printer is empty, please confirm!
print.template.name.is.null=Print template name is empty, please confirm!
print.ip.is.null=The obtained ip is empty, please confirm!
call.interface.querymultibrandbyitemno.failure=Query of multi-brand information failed!
bind_list_no_allow_both_process_station=bind_list_no_allow_both_process_station
exists_process_setting=exists_process_setting
person_job_no_match={0} line person job no match {1}
job_person_no_match={0} line job person no match {1}
exists_work_station_setting=exists_work_station_setting
barcode.params.all.null=Barcode related parameters cannot be all empty!
sn.attr.info.is.null=The barcode has not been scanned for pallet packing and does not support reprinting
whole.barcode.of.sn.attr.info.is.null=Cannot find the whole barcode corresponding to en/mac address
whole.barcode.wip.info.null=Whole barcode does not exist, please confirm!
return.error.qty=reelId: {0}, trading order: {1}, the quantity: {2}, the quantity: {3}, the quantity is greater than the quantity can not return please confirm!
mail.send.err=mail send err
submit.scrap.bill.error=submit.scrap.bill.error:{0}, please confirm and try again!
submit.scrap.bill.error.batch = submit.scrap.bill.error.batch: {0}, please confirm and try again!
lock.barcode.prodplanid.id.null=When querying batch lock order ID, the batch is empty
in.process.code.not.exist=When querying batch lock order number, the process group is empty
there_is_already_valid_machine_data_in_use=There is already valid machine data in use at the station {0}, offline transfer operation is prohibited
no_comprehensive_preparation={0} The reelid material has not been comprehensively prepared and cannot be scanned for transfer. Please confirm
failed_to_get_batch_first_command=failed to get batch first command
sn.has.been.bound.unbind.first=sn has been bound, please unbind first
send.wip.ext.to.spm.lock=Assembly relation is updating
update_deal_status_failed=Failed to update the processing status of the technical transformation sheet
email_content_for_techical_change=The technical transformation order number: {0}, {1} The technical transformation order is issued in the PDM system, and the system has automatically generated a technical transformation control order for you on the iMES system, with the same number.
email_content_for_techical_change_voided=The technical transformation order No.: {0}, {1} The technical transformation order is voided in the PDM system, and the system has automatically voided this transformation control order on the iMES system for you.
email_content_for_techical_change_delete=The technical transformation sheet No.: {0}, {1} will be deleted in the PDM system, and the system has automatically voided this technical transformation control sheet on the I MES system for you.
techical_change_not_exits=The {0} technical transformation sheet does not exist, please confirm whether the technical transformation sheet number is correct
techical_change_unlocked=The {0} technical transformation sheet has been unlocked, please confirm whether the technical transformation sheet number is correct
prodplanid_not_equal=The technical renovation order number not equal
techical_change_not_equal=The technical renovation order number not equal
techical_change_is_null=The technical renovation order number cannot be empty
techical_change_be_one=The technical renovation order number can only be one
prodplanid_change_be_one=The prodplanid can only be one
the_current_document_cannot_be_unlocked=The current document cannot be unlocked
the_offline_flag_sn_verification_failed=the_offline_flag_sn_verification_failed
Failed_obtain_barcode_item_no=Failed to obtain barcode corresponding material list information
sn.technical.control.error={0} sn technical modification is not completed, technical no {1};
prod.technical.control.error={0} prodplanId technical modification is not completed, technical no {1};
technical.exec.info.existed=The sn has been fed back for technical improvement, so there is no need to feed back again
technical.exec.info.error=The scan data error
unlock.process.must.be.queried.with.time=the unlocking process must be queried together with the unlocking time
remark.can.not.be.null=remark can not be null
must.enter.a.query.criteria=must enter a query criteria
the_chg_req_no_is_not_found=Failed to found the chg req no
the_chg_req_no_and_prod_plan_id_is_exist=The batch of abnormal skip information already exists in the technical transformation sheet number, please confirm
only_submitted_documents_can_perform_this_operation=Only submitted documents can perform this operation
technical_transformation_sheet_is_being_processed=Currently, the technical transformation sheet is being processed
task.tree.lost=Task tree lost
statistics.in.progress=Statistics in progress
prodplan.statistics.in.progress=Prodplan statistics in progress
real.time.prodplanid.cant.null=real time query prodplanId can not null
not.real.time.date.cant.null=not real time query date can not null
not.real.time.date.max.one.year=not real time query date max one year
not.find.route=not find route
not.find.item.ver.no=not find item ver no
failed_to_get_preview_link=Failed to get file preview connection {0}
failed_to_generate_preview_header=Failed to generate file preview authentication header information
fail_to_upload_file=File upload failed
transfer.work.order.status.error={0}status is not 'started', 'submitted', 'suspended'\uFF0Ccannot transfer scan
no_tech_chg_detail=no_tech_chg_detail
no_pdm_tech_chg_info=no_pdm_tech_chg_info
plan_lock_not_exist=plan_lock_not_exist
tec_chg_not_in_preparation=tec_chg_not_in_preparation
task_no_tec_chg_craft=task_no_tec_chg_craft{0}
chg_req_base_is_null=chg_req_base_is_null
pre_chg_file_null=pre_chg_file_null
tec_chg_email_send_null=tec_chg_email_send_null
tec_chg_can_not_submit=tec_chg_can_not_submit
tec_chg_file_is_null=tec_chg_file_is_null
task_exceed_threshold=task_exceed_threshold
work_order_not_finished=work_order {0} not_finished, can not unbind

get.boardonline.info.failed = get boardonline info failed
get.barsubmit.info.failed = get barsubmit info failed
trans.no.is.empty=trans no is empty
trans.no.not.exist={0}trans no not exist, please confirm
trans.no.allowed.box.ok={0}trans no allowed box ok\uFF0Cplease confirm
sn.not.in.bill.no=Barcode {0} does not belong to {1} delivery order, please confirm
sn.not.trans.to.here=Barcode {0} has not been transferred to this process, please confirm
sn.boxed=Barcode {0} boxed
bill.submit.ing=Case number {0} is being transferred to bill {1} for packing
isRepair_update_failed = isRepair_update_failed
check.technical.detail = heck whether the {0} technical modification or barcode locking file and technical modification materials are correct.
sn.flow.ctrl.failed=Barcode {0} process control verification failed, please check
someone_is_currently_exporting=currently exporting. Please try again later
no.test.work.order=no test craftSection workOrder no

parameter_fields_not_configured=parameter_fields_not_configured
field_mapping_not_configured=field_mapping_not_configured
unsupported_data_acquisition_method=unsupported_data_acquisition_method
no_microservices_configured=no_microservices_configured
no_reflection_method_configured=no_reflection_method_configured
reflection_method_configuration_error=reflection_method_configuration_error
sql_not_configured=sql_not_configured
sub_module_not_configured=sub_module_not_configured
no_associated_fields=no_associated_fields
unsupported_association_field_type=unsupported_association_field_type
sn_status_is_poor_and_no_stopover_is_allowed=sn status is poor and no stopover is allowed

concise.daily.scheduled.task.failed=Failed to execute the concise daily report timing task! This batch is: {0}, and/n pages are: {1}.
dqas.control.error=The process management and control of the pilot test interface fails, and the bar code: {0}
warehouse.submit.running=TaskNo {0} is submitting the warehousing order. Please try again later!
warehouse.submit.error.msg=The number of main tasks acquired by the {0} is: {1}, but the quantity is still {2}. Please handle it before warehousing.
whether_the_file_is_encrypted=Failed to parse the return file, please confirm whether the file has been encrypted
file_has_no_data=file has no data
there_is_an_empty_reelid=The file has an empty number of reelid, please confirm!
reelid.not.exist.pk.code=The material tray does not exist in imes, please register first:{0}
failed_to_obtain_task_information=Failed to obtain task information corresponding to batch {0}
failed_to_get_batch_information=Failed to get the batch information corresponding to reelid: {0}
failed_to_add_material_return_data=Failed to add return data, please try again! {0}
the_reelid_of_production_line_splitting={0}Please return to the central warehouse for the reelid separated from the production line!
the_latest_returned_material_quantity_is_abnormal=The returned material quantity is empty, and cannot be returned. Please contact the Operation and Maintenance Department for handling!
not_the_material_from_the_line_side_warehouse={0}There is no ex warehouse transaction from the line side warehouse, and the materials are not issued from the line side warehouse. It is not allowed to return to the line side warehouse. Please return to the central warehouse!
the_instruction_is_abnormal_completion=The {0} instruction is abnormal completion and cannot be returned. Please find the business representative to handle it!
concise.daily.query.page.or.row.illegal=The concise daily report must be queried by pages. Please confirm whether there are paging parameters!
sys.look.config.error=The concise daily report data dictionary 1003010 is incorrectly configured. Please confirm!
the_number_of_trays_is_zero=The quantity of pallet {0} is empty or 0, and the material cannot be returned, please confirm!
material_preparation_in_advance_exists=The material tray {0} has been prepared in advance and cannot be returned. Please confirm
material_the_machine_is_in_use=The pallet {0} has the information that the machine is in use, and the material cannot be returned. Please confirm
the_upper_and_lower_thresholds_maintained_are_not_integers=the upper and lower thresholds maintained are not integers
email.technical.notice=Technical Change Order No.: {0}, issued for the {1} th time, batch: {2}. Please check it.
issuance.crafsection.is.null=The issuanceCraftSection of the technical modification file issuance procedure is null.
sn.exist.scrap=The barcode {0} has a record of scrapping for the repair sub category, and cannot continue picking!
lock.prodplan.msg=Please check the {0} batch locking information.
task.qty.sufficient=The current task quantity {0} is less than or equal to the warehoused quantity {1} cannot be submitted for warehousing.
task.qty.sufficient.msg=Current task quantity {0} warehoused quantity {1}. The maximum warehoused quantity is {2}. Please confirm
bill.type.unsupported.submit=Current billType: {0} does not support submitting for warehousing Please confirm!
out.source.unsupported=The current task is not an outsourced task, and the outsourced warehousing form cannot be submitted
bill.submitted.error=Bill No. {0} has been submitted and cannot be submitted again. Please confirm
the_thread_body_is_switching_with_one_button=The current line and dule is switching with one button, please do not click repeatedly
submit.qty.is.null=submit qty is null
trace_type_has_no_work_order=The current traceability type has no corresponding instruction information
submit.qty.not.integer=The submitted quantity can only be a positive integer.
export.scan.history.miss.params = The parameter is missing. please enter at least one item of board barcode, batch and command
the.exported.data.cannot.be.larger.than.100000 = The exported data cannot be larger than 100000
export.down.max.limit = The current job number is being downloaded or the simultaneous downloading of files exceeds the maximum setting value, please check the configuration details of the data dictionary 1004041005.
search.scan.history.info.is.null=The barcode scanning history information under this condition is not found, and the export will not be performed
prodplan.new.sn.ing=The prodplan {0} is generating barcode
please_return_to_the_central_warehouse=Please return the materials shared by the master and sub-card of the castle card to the central warehouse
submit.by.work.maxSize=Up to {0} different instructions can be forwarded together by box.
box.contents.are.empty.params=Box contents {0} is empty
query.box.order.error=workOrder no {0} not exist
workOrder.line.code.empty=workOrder line code {0}is empty\uFF01
work.next.diff=The procedures of the current instruction {0} and instruction {1} are inconsistent and cannot be transferred together!
work.submitting.error=The workOrderNo {0} is being transferred. Please try again later!
kangxun.work.order.diff=The instruction {0} is not submitted together with the instruction {1} because the current operation is not in the Kangxun semi-finished product warehouse.
sn.regular.error=The barcode {0} is not a 12-digit number/exceeds the task quantity/does not belong to the current batch. Please confirm!
process.code.workOrderNo.not.exist=There is no {1} process instruction for proplanId {0}!
need.one.day.in.the.past=need one day in the past
technical.lock.msg=Technical Change Lock No.: {0}
spi_not_pass=SPI result of sn {0} not pass or not exist
aoi_not_pass=AOI result of sn {0}, station {1} not pass or not exist
station_not_exit=station {0} not exist
please.check.params=The parameter is missing. Please confirm whether the batch and sub-operation have been passed in
the.task.is.aux.scanning=The current batch is undergoing auxiliary scanning of this sub-operation, please execute later
not.found.aux.process=The sub-operation information is not found. Please confirm whether to maintain it
aux.qty.is.exceed=The entered current claim quantity+claimed quantity>planned quantity, please confirm
get.aux.sys.failed=Please check whether the data dictionary 2865006 is configured completely
submit.outsource.forbidden=Outsourcing tasks cannot be submitted on this page. Please go to the outsourcing warehousing document page to submit.
submit.outsource.forbidden.two=Outsourcing tasks cannot be submitted on this page. Please go to the warehousing document page to submit.
submit.outsource.forbidden.three=The outsourcing document type cannot be submitted on this page. Please switch the document type before submitting!
creation_time_cannot_be_empty=The creation time cannot be empty and the range cannot exceed one year
query_time_cannot_span_one_year=query time cannot span one year
query_param_person_needs_to_cooperate_with_time=query param person needs to cooperate with time
only_itemNo_or_prodplanId_can_queried_separately=only itemNo or prodplanId can queried separately
currently_exporting=currently_exporting_please_try_again_later
params.over.length=params over length,please confirm
wip.ext.exist=wip ext {0} exist in imes system, all {1} no write please confirm
max.query.size=The maximum number of query parameters is 500. Please delete the query quantity.
update.warehouse.date.in=the warehouse date is updating
sn.exist.factory=The {0} barcode is not processed in this factory. Please switch to the {1} to feed back the technical modification result!
sn.rule.error=The {0} barcode does not comply with the barcode rules. Please check!
operation_cannot_exceed_1000=The overall process of the advanced daily report cannot exceed 1000
data_volume_exceeds_5_w=The data volume exceeds 5 W and has been exported by mail. Please pay attention to the mail information
bill_no_not_exist=billNos:{0} is not exsit, please confirm \uFF01
sn.chg.exist={0} {1} The technical improvement order has been fed back, and no further feedback is required.
chg.submit.error={0} The current ticket is being operated. Please try again later!
sys.lookUpType.221103.not.configure=sysLookType 221103 not configure
print.scenes.not.get=print scenes not get
print.scenes.unsupported=print scenes unsupported
quantity.should.not.less.than1=quantity should not less than 1
wrong.sn.type=wrong sn type
small.sn.not.allowed.in.first.instruction=small sn not allowed in first instruction
big.sn.not.allowed.in.second.instruction=big sn not allowed in second instruction
big.sn.not.allowed.in.first.instruction=big sn not allowed in first instruction
sn.not.allowed.in.second.instruction=sn not allowed in second instruction
incoming.sn.null=No big board barcode is transferred, please check whether the printing scene is correct
incoming.sn.not.null=The big board barcode has been transferred in, please check whether the printing scene is correct
parent.sn.null=The incoming small board barcode has no large board barcode, please check
has.printed.cannot.repeat={0} The big board barcode has been printed and the small board barcode cannot be printed repeatedly
workorder.has.record.printing.failure={0} workorder has record printing failure {1}
craft.not.contain.current=craft not contain current
not.smt.craft=not smt craft
no.chinese.name.field.value.was.obtained.for.the.product=no chinese name field value was obtained for the product
the.english.code.field.for.the.latest.version.of.pdm.has.not.been.obtained=the english code field for the latest version of pdm has not been obtained
the.chinese.name.of.the.product.does.not.match.the.english.code.of.pdm=the chinese name of the product does not match the english code of pdm
non.machine.219.barcode.cannot.be.bound.to.network.access.certificate=non machine 219 barcode cannot be bound to network access certificate
not.find.unbound.big.sn=not find unbound big sn
not.find.unbound.fixture.sn=not find unbound fixture sn
not.find.unbound.big.sn.by.fixture=not find unbound big sn by fixture
not.find.workorder.by.line.prodplanid=not find workorder by line prodplanid
sn.not.found.in.sn.assign.table=sn not found in sn assign table
get.carft.null=No commanded process path
maximum.of.batches.reached=The maximum number of batches has been reached
maximum.of.batches.reached.partly=The remaining quantity of batch {0} is less than the number of splices {1}, please modify the number of splices
sn.not.feed.back={0} The sn does not feed back the technical modification result, and no QC confirmation is required!
sn.feed.back.complete={0} sn has been confirmed for technical modification!
submit.outsource.qty.sub=prodplanId: {0}, accumulated current warehousing total:{1},registered quantity:{2},\
  cannot be submitted for warehousing!
verNo.has.set.the.printing.scene=verNo {0} has set the printing scene
verNo.is.null=verNo is null
verNo.not.exist=verNo not exist
lpn.and.original.lpn.all.null=lpn and originalLpn all null
en.info.not.exist=en info not exist
rows.more.than.500=rows can not be more than 500
box.has.not.qc=Box {0} has not undergone QC sampling inspection and is not allowed to be stored. Please confirm
box.qc.fail=Box {0} failed the random inspection, please confirm
z.mail.export.sn.ing=this sn export ing on zMail, please look zMail
tech.submit.all.chg.null=All technical renovation executions have been submitted, and the technical renovation order number cannot be empty. Please confirm!
tech.submit.all.craft.section.null=All technical modifications have been submitted, and the main process cannot be empty. Please confirm!
tech.submit.all.prod.null=All technical renovation execution submitted, batch cannot be empty, please confirm!
tech.submit.all.product.code.null=All technical modifications have been submitted, and the material list code cannot be empty. Please confirm!
tech.submit.all.unlock.type.null=All technical modifications have been submitted, and the unlock type cannot be empty. Please confirm!
submit.all.sn.null= submit all have not any sn.

get.solder.open.add.fail=Failed to obtain the instruction for opening and scanning record of solder paste. Please confirm.
solder.no.open.add={0} Instruction does not scan solder paste, start operation is not allowed
work.order.no.not.start=The instruction {0} has not started. Please start the instruction first!
return.qty.null=the object {0} return qty is null
reelid.not.exists=reelId not exists\uFF0Cplease confirm!
unable.to.obtain.work.order=unable to obtain work order
work.order.status.incorrect=The instruction corresponding to reelId has not been completed, and the return quantity cannot be modified
reelid.is.return=reelId is return,modifying the return quantity is not allowed
reelid.is.recovery=reelId is recovery,modifying the return quantity is not allowed
storage.center.return.null=storage center return is null
query_wip_information_cannot_exceed_1000=Query wip information cannot exceed 1000
failed_to_obtain_material_information=Failed to retrieve material information from the API of the data retrieval platform
time.zone.cannot.span.seven.days=time zone cannot span seven days
time.cannot.be.null=time cannot be null
return.info.is.null=reelId return info is null
qty.must.be.a.positive.integer=qty must be a positive integer
params.is.null.or.qty.exceed.4=input params is null or the total of reelid exceed four ,please confirm!
reelid.is.null=reelid is not null
bindList.is.null=bindList is null
get.item.info.fail=get item info fail
process.last.workstation.error=The final station of sub process {0} should be {1}
item.not.bound=The item code {1} under station {0} has not been bound completely

line.code.of.com.ass.scan.null=line code is null
process.code.of.com.ass.scan.null=process code is null
main.barcode.of.com.ass.scan.null=The main barcode for the entire machine assembly scan cannot be empty, please confirm!
main.barcode.not.have.wip.info=The main barcode [{0}] does not exist. Please confirm the correctness of the main barcode!
main.barcode.craft.section.illegal=The main barcode [{0}] has been stored and cannot be scanned. Please confirm!
main.barcode.not.have.binding.setting=The material list [{0}] of the main barcode [{1}] has no binding relationship configuration. Please confirm!
assembly.not.comply.with.rules=The main barcode [{0}] has not been scanned according to the assembly rules, please confirm!
main.barcode.have.finished.bind=The main barcode [{0}] has been assembled in the current sub process/station. Please confirm!
main.barcode.not.have.work.order.no=The main barcode [{0}] cannot find a valid instruction, please confirm!
skip.total.qty.illegal=The skip quantity cannot be less than the sum of the required quantities for all materials in the binding list. Please confirm!
sub.barcode.craft.section.illegal=The sub barcode [{0}] has not been stored and cannot be scanned. Please confirm!
item.of.ssp.have.related.sn=The sub barcode [{0}] is already associated with the central serial code. Please scan the ZTE serial code!
islead.of.sub.sn.not.find.dictionary=No corresponding item was found in IMES data dictionary for the environmental protection attribute [{1}] of the sub barcode[{0}]!
islead.of.main.sn.not.find.dictionary=The environmental protection attribute [{1}] of the primary barcode[{0}] is not found in the IMES data dictionary!
lead.flag.of.sub.sn.illegal=The environmental attribute of the sub barcode [{0}] is lower than the environmental attribute of the task [{1}]
failed_to_obtain_contract_information=Failed to obtain MES contract information and completion time of inbound accounting
main.sn.is.in.store=primary barcode has been stored and cannot be scanned
not.found.replace.item=no substitute material obtained
item.no.no.bind.list=BOM {0} Subprocess {1} Station {2} List to be bound is not set, please set it first
this.main.sn.is.bind=The barcode {0} already has a binding record under sub-operation {1} station {2}. Please unbind it
bind.count.need.more.than={0} code needs to bind {1} barcode, please confirm
get.item.info.count.fail=get replace item count fail
sub.sn.not.exist=The sub-barcode information is not registered or incorrect in the barcode center. Please check
not.need.bind.sub.sn=not need bind sub sn
sn.is.lock.can.not.bind=Binding is not allowed because there is a valid lock ticket for barcode {0}
batch.is.lock.can.not.bind=Binding is not allowed because there is a valid lock ticket for batch {0}
sn.item.length.no.12=barcode {0} item code is empty or length not 12 bit, please confirm
main.sn.wip.ext.bind.lock=Primary barcode {0} is binding, please try again later
sub.barcode.have.bind.relation=The sub barcode [{0}] already has a binding relationship, please confirm!
sub.barcode.have.bound.in.main.barcode=The sub barcode [{0}] is a batch code and cannot be bound to the main barcode [{1}] again
main.barcode.wip.info.not.have.task.no=The main barcode [{0}] cannot find the task number in the production information and cannot query the ERP task list. Please confirm!
item.or.replace.item.in.erp.task.more=There are multiple substitute materials in the material code of sub barcode [{0}] in the main barcode task list. Please confirm!
item.or.replace.item.not.in.bind.list=The material code (including replacement materials) of sub barcode [{0}] is not in the main barcode binding list, please confirm!
item.or.replace.item.in.bind.list.more=There are multiple substitute materials in the material code of sub barcode [{0}] in the main barcode binding list. Please confirm!
sn.lock.not.allow.bind=The following barcode: {0}, batch: {1}, there is a lock order and cannot be bound. Please confirm!
main.barcode.flow.control.not.pass=The process control verification of the main barcode failed due to {0}
work.order.no.of.wip.info.not.find.entity=The instruction [{0}] for the in process information record of the main barcode cannot find corresponding information. Please confirm!
main.barcode.is.binding.now=The main barcode is currently being assembled, please try again later!
main.barcode.have.more.work.order.no=Task {0} has received multiple instructions from sub process {2} of line {1}. Please confirm!
sn.lost.board.center=The {0} barcode does not exist in the barcode center.
route.detail.lost={0} route detail lost,please confirm!
sub.sn.not.bind={0} sub-barcode does not need to be bound. Please confirm!
redis.lock.fail.msg={0}: The redis lock resource fails to be obtained.
item.bind.over={0} code needs to be bound {1} already bound {2} number of this binding {3} cannot be bound!
get.current.workorder.error=get current workorder error
task.no.have.batch.work.order.no=Task{0}has received multiple instructions from sub process {1}. Please confirm\uFF01
return.sn.is.on.lock=return sn {0} or proplan_id {1} is on lock,please confirm!
sn.is.not.in.right.imu=sn {0} imu is not in right imu, please confirm!
sn.is.not.in.ScanReceive=The barcode cannot be scanned and received. Please confirm!
sn.is.more.than.one=The barcodes corresponding to more than one submitted documents. Please confirm!
return.info.is.on.lock=return info {0} is on lock,please confirm!
upload.test.data.too.much=The number of test data uploaded in a single upload shall not exceed {0}
sn.workOrderNo.not.exist=the workOrder info of sn{0} is empty
line.model.of.sn.not.found=line model info of sn{0} not found
line.model.of.sn.not.found.or.status.error=line model info of sn{0} not found or current process code is not {1}
can.not.more.than.1000=the size of query sn list can not more than 1000
task_batch_barcodes_cannot_all_be_empty=When unbinding, tasks, batches, and barcodes cannot all be empty
main_sn_child_barcodes_cannot_all_be_empty=The main barcode and sub barcode cannot both be empty
task_batch_barcodes_cannot_all_be_empty_search=Task, batch, barcode, and time cannot all be empty
the_number_of_primary_barcodes_cannot_exceed_100=The number of primary barcodes cannot exceed 100
the_number_of_sub_barcodes_cannot_exceed_100=The number of sub barcodes cannot exceed 100
file.type.illegal=The technical modification file type is incorrect, please confirm!
product.query.params.can.not.be.null=lineCode and workStation and batch can not be null
get.item.model.info.error=An error occurred during the process of obtaining the material rule model. Please confirm!
one_additional_barcode_printing_cannot_exceed_200=One additional barcode printing cannot exceed 200
failed_to_query_barcode_printing_information=Failed to query barcode printing information
get.mds.token.error=get mds token error
sn.info.not.exist=sn\uFF1A{0} not exist
sn.is.stock=sn: {0} is stock
sn.not.exist.parent.sn=sn: {0} not exist parentSn
task.no.is.different=sn: {0}taskNo is different
item.no.is.different=sn: {0}itemNo is different
sn.num.cannot.exceed.200= sn num cannot exceed 200
processCode.or.workStation.is.null=processCode or workStation is null
process.name.is.different=processName is different
workstation.name.is.different=workStationName is different
update.mouting.failed = The handset is invalid. Scan the old feeder tray again to obtain valid data.
work.order.info.is.none.by.task.and.process=work order info is none by task {0} and process{1}, please confirm!
too.many.work.order.info.by.task.and.process=Too many work order info by task {0} and process{1}, please confirm!
failed.to.get.item.no.from.wip=Failed to get item no from wip info by sn
usage.count.is.null=usage count is null, please confirm\uFF01
bind.setting.usage.count.is.null=Binding list material {0} item {1} has no configured materials, please confirm!
mixed.tasks.scan.param.null=mixed tasks scan param null!
error.msg.for.unbinding = These\uFF1A{0},can not submit,please confirm!
task.no.or.item.no.can.not.null = taskNo or itemNo can not null
task.no.all.sn.not.finish.binding = taskNo All Sn Not Finish Binding
process.not.exist=process {0} is not exist
item.or.replace.item.not.in.erp.and.bind.list=The material code (including replacement materials) of the sub barcode [{0}] is not in the main barcode ERP task list and is not in the IMES binding list. Please confirm!
prepare.item.edit.param.repeat=prepare item edit param repeat
get.spm.tld.error=Failed to obtain nesting list information from SPM 
comparison.complete=comparison complete
bind.info.not.enough=sn{0}binding info not enough\uFF1A{1}
activity.is.n.and.data.null=Confirm All When Yes is not selected, the unfinished preparation items cannot be empty. Please confirm!
push.date.time.null=The last synchronization time of the push data is empty, please confirm!
not.fount.matrialTray=Not fount matrialTray
materials.has.prepared=materials has prepared to {0}
materials.is.use={0}materials is use, Please confirm!
location.inconsistent=Location No is not consistent with the material in the table, Please confirm!
order.online.check.prepare.status.failed=order {0} online {1} check prepare {2} status failed
export.missing.sourceBatch=Please enter sourceBatch for export
reelId.not.exist.with.reelId={0}reelId not exists\uFF0Cplease confirm!
reelId.not.return.material={0}reelId does not in table material return, please confirm workorder has been completed!
no_personnel_interface_address_configured=No personnel interface address configured, please confirm
failed_to_obtain_personnel_information=Failed to access personnel information through the personnel interface: {0}
not.found.ps.task.by.prodplanid=ProdplanId {0} does not exist, please confirm
warehouse.line.not.allowed.transfer=Cross batch production transfer is not allowed for the line body batch in the edge warehouse
prodplan.id.has.maintained.transfer.strategy=This batch has already maintained a production transition strategy in the currently selected line body
pre.prodplan.id.has.maintained.transfer.strategy=The pre conversion batch has already maintained a conversion strategy on the currently selected line body
not.found.work.order.by.prodplan.id=Batch or pre production batch has no instructions under the currently selected line. Please confirm
craft.section.not.scheduled.same.line=The {0} area instruction is not scheduled for production on the same line, and cross batch transfer is not supported
smt.material.inconsistent=The {0} smt material is inconsistent and cross batch transfer is not supported
work.order.have.been.prepared=The {1} instruction in batch {0} has been comprehensively prepared, and it is not allowed to maintain the cross batch production strategy
work.order.have.been.prepared.completion.not.allowed=The {1} instruction in batch {0} has been comprehensively prepared, and completion not allowed!
transfer.strategy.info.redis.locked=Batch {0}, Line {1}, Pre production Batch {2}, Production Strategy is currently being submitted. Please wait!
strategy.has.used.not.delete=The transfer strategy has already been used and cannot be deleted
strategy.has.used.not.update=The transfer strategy has already been used and cannot be modified
not.found.transfer.strategy=The transfer strategy does not exist, please confirm
strategy.has.been.maintained=Batch {0} has maintained transfer strategy in the {1} line,Preparation is not allowed,Can be produced directly from batch {2} surplus material
batch.formation.loop=The production transfer strategy will form a loop between batches, please check!
not.found.other.task.by.line.code=No other batch of instructions belonging to line {0} with submitted status was found. Please confirm!
sn.is.exist=sn{0}\uFF0Cis exist\uFF0Cplease confirm!
query.params.error=query params error
last.update.date.range.too.long=lastUpdateDate range too long
create.date.range.too.long=createDate range too long
check.work.time.failed=sn {0} check {1} work time failed
check.remain.time.failed=sn {0} check {1} remain time failed
cannot.find.content.sn=cannot.find.content.sn
more.than.one.order.exist.in.lpn = more than one order exist in lpn
sn.not.exist.in.wip.info = sn {0} not exist in wip info
repair.sn.process.change=repair sn {0} process.change
repair.query.param.error=[inSns],[prodplanId],[repairDate],[sendRepairDate],[repairRcvDateQry] can not all be empty and time range can not exceed 183 days
sn.can.not.batch.pass = sn {0} can not batch pass
route.id.is.null=The process path ID cannot be empty, please confirm!
scan.process.code.is.null=Scan sub process cannot be empty, please confirm!
scan.process.code.not.the.same.in.the.wip=The current sub process of the barcode is not completed and cannot be scanned for new sub processes.
scan.process.code.not.in.next.can.scan.list=The current scanned sub process is not the next sub process that can be scanned.Please confirm!
scan.work.station.not.in.next.can.scan.list=The current station being scanned is not the next station that can be scanned.Please confirm!
scan.setting.line.code.is.null=The line body set for scanning cannot be empty
scan.work.station.is.null=Scan station cannot be empty, please confirm!
work.order.line.not.the.same.with.scan.line=The line body arranged by the command and the line body selected by the scan are not consistent.Please confirm!
work.order.status.not.allow.to.scan=Command status does not allow scanning, please confirm!
work.order.process.group.not.contain.scan.process.code=The instruction sub process group does not include the scanning sub process.Please confirm!
scan.work.order.no.is.null=The command number for scanning cannot be empty, please confirm!
work.order.process.group.not.have.must.scan.process=The sub process group of the instruction does not include the required scan sub process.Please confirm!
line.code.model.not.have.must.scan.work.station=There is no need to scan the workstation in line modeling, please confirm!
work.order.order.relation.qty.illegal=The relevant information of the instruction (planned quantity/input/output) cannot be empty, please confirm!
work.order.process.group.not.exist=Instruction {0} does not specify an instruction process group, please confirm!
sn.can.not.submit.in.stock=sn {0} can not submit in stock 
sn.can.not.receive.in.stock=sn {0} can not receive in stock 
sn.can.not.package.scan=sn {0} can not package scan 
has.set.the.qc.regulation=QC regulation have been set up, please confirm
qc.sampling.not.found=QC sampling not found
qc.sampling.detail.not.found=QC sampling detail not found
the.inspection.form.cannot.be.modified=The status of the inspection form is not in preparation and cannot be modified
box.contents.has.changed=The quantity of box contents has changed. Please re scan and submit
workorder.balance.has.changed=The workorder balance quantity has changed. Please rescan and submit again
workorder.not.same=workorder not same
processcode.not.same=processcode not same
workstation.not.same=workstation not same
state.not.same=The barcode status is inconsistent. Please check the instructions, sub processes, and workstations
sn.exists.on.other.qc=The barcode already exists on other inspection forms {0}
sn.has.been.packed=The barcode has been packed, please conduct spot checks on the box
sn.not.found.by.lpn=No barcode found based on container number
skip.rule.is.null=skip rule is null
sn.is.high.temp.sn=sn is high temp sn
no.valid.sampling.record=No valid sampling record
no.pass.sampling.record=No pass sampling record
set.default.quality.limit=The default acceptance quality limit has not been set, please confirm!
process.list.is.null=processList is null
not.allowed.transfer=No turnaround until the last instruction has been given.
stock.aux.material.unbinding=stock aux material unbinding {0}
mix.stock.aux.material.unbinding=mix stock aux material unbinding
scan.aux.material.unbinding=scan aux material unbinding {0}
module.no.items=The module no items, please confirm
scan.comparison.complete=comparison complete,Please change the module
cfgheaderid.is.empty=cfgheaderid can not be empty
no.barcode.available.in.lpn=no barcode available in lpn
switch.first.work.order=There is no transfer record for {0} instruction in the current module. Please switch the first instruction
insufficient.sampling.quantity={0} inspection forms were sent for inspection, and {1} should have been sampled. Only {2} were sampled and cannot be submitted
failed.to.save.stock.info=failed to save stock info
auto.transfer.failed=Qualified sampling inspection, automatic process transfer failed. Please operate on the process transfer page. Error message: {0}
wip.print.config.file.name=BarcodePrintConfigurationTemplate.xlsx
auto.creation.not.support.custom.aql=The sampling that does not support automatic creation is a custom scene. Please reset the default acceptance quality limit AQL
Hard.Cord.File.Template=File Template Error\uFF01Included Column\uFF1A
excel.resolution.failure=excel resolution failure,{0}
the.file.cannot.be.empty=The File Or The Whole Barcode Cannot Be Empty!
current.no.items=Current side no items, please confirm.
not.fount.data.or.not.instruction=No tray information found or the material batch is not for the instruction. Please confirm
location.not.match=The current instruction loading table does not have this station, please confirm
reel.id.not.match=ReelId and current station material do not match
item.code.has.binded.feeder=Materiel {0} has the Feeder binding
not.find.feeder.bind.loc=Feeder binding location not found
the_erp_start_of_the_query_task_is_incorrect=The ERP start of the query task {0} is incorrect
line.not.allow.more.than.two_work.order=A line body does not allow more than 2 work Order to start at the same time, please confirm!
reel.id.exist.in.container.not.allow.split=The reelId exists in the {0} container and cannot be split. Please confirm!
smt.data.line.location.item.code.is.null=The SMT information of line/location/itemcode is null. Please confirm!
update.pl.info.error=Failed to update throwing data
scan.type.not.one.or.two=scan type not one or two
package.scan.sn.process.wrong=package scan sn process wrong
outsource.reelid.format.error=outsource reelid format error
package.scan.lpn.wip.not.exist=package scan lpn wip not exist
package.scan.generate.sn.or.over.station.failed=package scan generate sn or over station failed
sn.in.container=sn {0} in container
item.codes.inconsistent=The material codes of {0} and {1} are inconsistent. Please confirm
pk.code.not.exists={0} material tray not found, please confirm
bind.type.is.null=bind type can not be null
item.code.is.exist.in.bind.list=item code is exist in bind list
product.code.is.not.exist.in.bind.list=product code is not exist in bind list
aux.sn.code.is.not.exist.in.bind.list=aux sn {0} item code {1} not exist in bind list!
task.no.exist.aux.bind.lock=task no {0} is aux binding,please wait!
barcode.exist.in.bind.list=barcode {0} is exist in bind list!
barcode.category.not.in.bind.list=The barcode {0} has a type of {1}, which is inconsistent with the barcode type of the already bound record, and binding is not allowed.
barcode.lead.need.over.task.lead=barcode {0};task lead{1},please confirm!
mount.type.is.null=mountType can not be null
printType.or.printScene.is.null=The print type or print scene is empty
failed_to_query_the_print_template_information=Failed to query the print template information
failed_to_get_the_material_code_for_the_lot=Failed to get the material code for the lot
failed_to_get_the_material_sn_for_the_lot=Failed to get the material sn for the lot
tube.core.prod.have.tech=There is a technical modification in the core task, please confirm!
wip.info.not.match.with.pack.info=The station information for the core task does not match the packing information, please confirm!
tube.core.sn.illegal=The barcode for the tube core is not the first 12 digits of the batch, please confirm!
infor.not.have.material.issuance.details=INFOR has no material issuance details for this task, please confirm!
good.die.qty.not.equal.task.qty=The number of tasks and the quantity of Good DIE for procurement and delivery are not equal, which does not meet the packing conditions for the core. Please confirm!
barcode.generate.tube.core.lpn.error=The box number generated by calling the barcode center for core packaging does not match the number of reelIds. Please confirm!
tube.core.prod.not.exist=Batch cannot find core task information, please confirm!
prod.of.tube.core.not.have.work.order=There is no instruction for the core task batch, please confirm!
tube.core.not.have.first.can.scan.work.station=Core management command, there is no first mandatory scanning station, please confirm!
good.die.qty.too.larger=The packaging of the core cannot exceed the specified quantity {0}. Please confirm the GoodDie quantity for reelId.
old.and.new.pkcode.batch.inconsistent=The batch number of the new material tray is inconsistent with the old one. Please confirm.
create.supplement.bill.param.empty=param useFor,deliveryAddress,billInfoList can not be blank
create.supplement.bill.param.error=param materialRequisitionBill in billInfoList can not be blank
delivery.not.exist=delivery: {0} not exist
no.enough.stock=no enough stock
delete_batch_cannot_be_greater_than=Delete batch cannot be greater than {0}
work.order.cfg.id.changed=The cfgId of current work order has changed. Please return to the previous menu and rescan.
enter.query.criteria.before.exporting=enter query criteria before exporting.
sn.is.not.12.numbers=sn {0} is not 12 numbers
prodplan.id.is.different=prodplanId is different
start.sn.exceed.end.sn=start sn exceed end sn
exceed.work.qty=exceed work qty
process.must.exist.in.2038=process must exist in 2038
input.sns.more.than.need.del=input sns more than need del{0}
sn.list.is.null=snList is null
sn.list.exceed.max=snList exceed max
prodplan.exist.220.sn=Batch {0} has a 220 barcode generation record and is not allowed to submit a single board receipt
locator.id.not.exist=Exception in obtaining location ID for {0}
carry.account.name.not.null=Alias cannot be empty
cannot.submit.k2.bill=Currently, it is not allowed to submit single board storage orders for K2 library
infor.bill.packing.exception=Document {0} is an Infor document and cannot be rejected!
erp.move.or.done.error=bill {0} erp move or done error, cannot be rejected!
erp.move.error=erp move error! {0} 
material_codes_before_after_replacement_not_same=The material code after replacement is inconsistent with the code before replacement. Please confirm
sn_has_no_technical_upgrades={0}The barcode has no implementation status of technical transformation, and the BOM external material code cannot be replaced. Please confirm the processing!
confirm_technical_renovation_and_maintenance={0} There is a {1} technical renovation order, please confirm if the repair is carried out according to the content after the technical renovation

org.id.inconsistent=Inconsistent org IDs: {0}
account.name.outbound.failed=The current outbound bill failed
bill.completion.and.storage.failed={0} Completion and storage failed
scanner_record_not_pass=Scanner result of sn {0}, station {1} not pass or not exist
sn.format.msg=The barcode must be a 16 digit number or letter combination, a 12 digit number, a P+12 digit number, or a 13 digit number or letter combination starting with the batch
obtain_batch_220_information=Failed to obtain batch 220 information
pkCode.param.empty=pkCode param empty
print.instock.data.error=Failed to obtain printing data for the warehouse receipt
disk.code.control=Failed to obtain disk code control
scanning_block_by_block_can_only_scan_barcodes=Scanning block by block can only scan barcodes, not reels or box codes

last.process.not.allowed.unbind=last process not allowed unbind
submit.warehouse.is.null=The submit warehouse parameter is null.
box.is.warehouse=The selected box has been submitted for storage. Please refresh and try again!
the_barcode_is_not_from_the_current_batch=The barcode is not from the current batch, please confirm
request.xian.factory=The factory is required to be Xi'an factory
no.data.to.be.updated=No data to be updated
zj.sn.not.exist=zj sn not exist
please_select_barcode_for_repair_materials=Please select barcode for materials
issuance_info_null=The The prodplanid issuance info is null
serial.number.is.empty=The serial number is empty
second.side.need.small.sn=Please scan the barcode on the second side
call_material_qty_api_failed=Failed to call low-level warning interface
sn.no.weight.info=machine sn {0} not config weight, please config weight first!
stock.and.loc.not.same.with.task=stock and loc not same with task! {0}
out.put.qty.larger.than.work.order.qty=The number of instruction outputs cannot exceed the number of instructions.
bill.type.is.not.exist=Bill type is not exist
pmOrgTransferOrder.transferQuantity.required=If the item code exists, the transfer quantity is required.
transfer.qty.more.warehouse.qty=transfer.qty.more.warehouse.qty
sn.repair.process.forbid=sn {0} cannot be returned to the {1} process.
sn.not.exist.accept=sn {0} does not exist. Repair records to be returned cannot be returned.
repairApproval.positionNumber.required=The repair approval position number is required.
attach.file.list.not.equal=Attachment list data error. Please clear the attachment list and upload it again.
attach.file.list.over.ten=The number of attachments cannot exceed 10.att
position.code.not.exist=Position code not exist
exist.repeat.approver=There are repeated approvers, please validation
sn.is.not.register=The barcode\uFF1A{0}\uFF0CWeight maintenance is not allowed without registration
sn.has.weight=The barcode\uFF1A{0}\uFF0CMaintained weight cannot be maintained again
weight.not.empty=Weight can not empty
weight.not.valid=Weight only allows positive numbers to be entered, with a maximum of 2 decimal places supported.
weight.not.valid.positive.number=Weight only allows positive numbers to be entered.
sn.data.size.over=Data size can not over {0}
sn.storage.can.not.modify.weight=Sn storage can not modify weight
sn.storage.can.not.remove.weight=Sn storage can not remove weight
creator.needs.creation.time=The creator needs to coordinate with the creation time for querying.
time.supports.months=The time span supports a maximum of {0} months.
input.search.condition=Please enter search criteria: Barcode and Task Number support individual search, while Creator and Last Updater need to be used in conjunction with Create Time or Last Update Time for searching.
search.one.time.maximum=Batch query. A maximum of {0} barcodes can be queried at a time.
sn.reconfiguration.no.completed=Sn material code {0} modification/disassembly is not completed!
failed.to.process.approval.center.kafka.message=Failed to process approval center kafka message
sn.received.can.not.un.bind=Barcode {0} has received a warehouse receipt and needs to be disassembled through a reconfiguration task
sn.inbound.work.need.choice=The current status of barcode {0} is "Inbound". Please select a command and scan again!
sn.not.belonging.task=The sn {0} does not belong to the current task {1}. Please confirm!
only.approving.can.withdraw=Only approving can be withdraw.
repairApproval.approvalOperateInfo.required=The approver information is missing. Please confirm
least.create.date=Please enter at least the created time
query.mes.task.pick.error=The disassembling/modifying task {0} in MES is null!
sn.config.task.contain=sn {0} is not in the disassembly/modification barcode list of task {1}!
only.creater.can.withdraw=Only creater can withdraw

no_valid_data_found=No valid data found
print_count_must_be_a_positive_integer=The number of prints/reprints must be a positive integer
box_code_not_registered=Box code {0}  not registered
the_box_code_has_been_registered=Box code {0} has been registered
the_material_barcode_has_been_scanned=The material barcode {0} has been scanned for packing
up_to_4_decimal_places=The quantity must be a positive number with a maximum of 4 decimal places
not_in_compliance_with_the_packing_rules=Material barcode {0} does not comply with the packing rules
alibaba_customer_tasks_not_allow_in_imes=alibaba customer tasks not allow in imes
sn_alibaba_customer_tasks_not_allow_in_imes=sn alibaba customer tasks not allow in imes

not.used.tasks=not used for non reconfiguration/teardown tasks
item.sn.not.exist={0} The barcode is not included in the assembly list and is not allowed to be disassembled
material.not.disassembled={0} The item code to which the barcode belongs does not need to be disassembled, and the disassembly operation cannot be performed
does.not.need.to.be.assembled={0} The barcode does not need to be assembled and does not allow assembly operations
barcode.is.not.scanned={0} The barcode is not scanned and is not allowed to be assembled
task.did.not.receive=The {0} task did not receive this barcode and was not allowed to scan
barcode.has.been.bound={0} The barcode has been bound to {1}, and repeated binding is not allowed
repeat.the.operation==The current task number {0} and SN {1} are in operation, do not repeat the operation
quantity.greater.require.number = The number of dismantling/assembling is greater than the quantity required by the ERP
enter.a.non.zero.number = enter.a.non.zero.number
customer.info.lost=Customer {0} material code {1} factory customer data of the center is missing.
repair.result.code.lost=Error code of repair result data dictionary {0} not configured {1}
material.bind.incomplete=The material {0} required for uploading the packing list is not in the binding list and cannot be scanned
material.bind.no.end=Whole machine barcode {0}, material {1} not fully bound
by.container.item.box.no.empty={0} The packing list must upload the original box code of the material, which cannot be empty
by.sn.not.sequence.code={0} The material uploaded by SN must be a serial code
fix.bom.can.not.be.replace=FixBom requires material {0} and cannot use substitute materials
not.found.item.bind.info=not found item no {0} binding listthe_query_conditions_cannot_all_be_empty=The query conditions cannot all be empty!
box_code_has_been_printed_reissued=Box code {0} has been printed/reissued
fix.bom.err.check=fixBom data error
item.not.exists.erp.list=item no{0} not exists erp list