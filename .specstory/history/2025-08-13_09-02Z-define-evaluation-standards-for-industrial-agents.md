<!-- Generated by SpecStory -->

# Define evaluation standards for industrial agents (2025-08-13 09:02Z)

_**User**_

提示词： ## 背景 ##
你是一个资深的AI应用及供应链专家，擅长智能体设计和开发，擅长决策。

## 任务 ## 
工业智能体（工业agent）包含了多个能力域，其中决策能力子域分为目标优化和调度响应两个能力项，本次需要这两个能力项定义评估标准。
目标优化：在产能、质量、能耗、安全等单目标或多目标约束下，基于先进算法生成优化方案
调度响应：对策略调整、环境变化和突发事件快速响应，实现任务调度，如多智能体利用"联邦学习+博弈论"实现资源分配
要求：
1. 请参照示例输出工业智能体决策能力子域的能力项的评估标准
2. 每个能力项的评估标准包含 评估目的、评估内容、评分要求，评估内容需要列出多个维度，每一个维度包括多个子项
3. 评分要求按照1-5个分值设计，结合评估内容中的维度、子项的覆盖情况，给出5个分值的评估标准。
请参照示例中的表 2 营销场景评分要求，对评分进行定量设计，能力项分项要求的填写能够量化，如覆盖1个子项算1分，覆盖2个子项算2分等，禁止出现少量、较深入、程度低、创新等定性或无法衡量的描述。

## 示例 ##
```
##金融大模型营销的评估标准##
###评估目的###　: 评估金融大模型覆盖的智能营销场景丰富程度。
###评估内容###：评估金融大模型对营销场景的覆盖度。
a)任务类型：KYC用户关键信息抽取、营销文案生成、客户意向评估、客户细分、客户分层、客户画像构建、个性化推荐、电子渠道建设等；
b)接入数据：客户信息、产品信息、交易信息、行为信息等；
c)营销手段：短信、IVR、语音机器人外呼、人工外呼等

表 2 营销场景评分要求
|-|-|
|得分|	能力项分项要求|
|-|-|
|1分	|1.应覆盖1种及以上任务；|
|2分	|1.应覆盖2种及以上任务；|
|3分	|1.应覆盖3种及以上任务、接入1种及以上数据类型、支持1种及以上营销手段；|
|4分	|1.应覆盖4种及以上任务、接入2种及以上数据类型、支持1种及以上营销手段；|
|5分	|1.应覆盖5种及以上任务、接入3种及以上数据类型、支持1种及以上营销手段。|
|-|-|
```

---

_**Assistant**_



---

### 工业智能体（决策能力子域）评估标准

### 能力项一：目标优化
- **评估目的**：评估工业智能体在产能、质量、能耗、安全等单/多目标与多约束条件下，生成可落地优化方案的覆盖度。
- **评估内容（计分维度为A-D）**：
  - **A. 优化目标类型（至少选用）**：产能/吞吐、质量/良率、能耗、电力负荷峰谷、生产成本、交付周期/提前期、库存水平/缺货率、设备利用率/OEE、碳排放、在制品WIP。
  - **B. 约束条件类型**：设备能力/加工时间、工艺路线/优先级/禁忌、人员技能/班次、物料供给/批次有效期、质量规格限/检验放行、物流/AGV能力、能耗/功率上限、碳配额、维护窗口/保养周期、安全规程/互锁、缓冲区/库存容量。
  - **C. 算法与求解器**：线性/二次规划（LP/QP）、混合整数规划（MILP）、约束规划（CP-SAT）、元启发式（遗传/粒子群/模拟退火/禁忌）、多目标Pareto优化、强化学习、鲁棒/随机优化、贝叶斯优化、分解与拉格朗日松弛、动态规划/滚动优化（MPC）。
  - **D. 数据源与系统接入**：MES、ERP、WMS、SCADA/PLC、IoT传感器/能耗计量、QMS/质检、APS/历史排产、CMMS/设备维护、供应商/外协接口、碳核算系统。
  - （非计分补充维度，用于实施完备性检查）方案输出与接口：生产排程/派工、物料补货/采购建议、能源负荷计划、维护/检修计划、在制/缓冲策略、REST接口、消息中间件（Kafka/MQ）、OPC UA/Modbus、可视化看板；验证与评估：数字孪生/离散事件仿真、A/B回放、指标度量（产出/良率/能耗/成本/碳排）、约束绑定与可解释、灵敏度/情景分析、近似最优性Gap报告。

表 1 目标优化评分要求（按A-D四维度子项覆盖数计分）
|-|-|
|得分|能力项分项要求|
|-|-|
|1分|A≥1、B≥1、C≥1、D≥1；|
|2分|A≥2、B≥2、C≥2、D≥1；|
|3分|A≥3、B≥3、C≥3、D≥2；|
|4分|A≥5、B≥4、C≥4、D≥3；|
|5分|A≥6、B≥5、C≥5、D≥4。|
|-|-|

### 能力项二：调度响应
- **评估目的**：评估工业智能体对策略调整、环境变化及突发事件的快速响应与任务调度能力，含多智能体“联邦学习+博弈论”资源分配。
- **评估内容（计分维度为E-H）**：
  - **E. 响应事件类型**：策略参数调整、订单变更/插单、设备故障/降级、物料短缺/到料延迟、质量异常/返工、人员缺勤/技能变更、工艺/配方变更、物流堵塞/AGV故障、能源限电/电价突增、安全事件/安灯、供应中断/外协异常、环境因素（温湿度）。
  - **F. 协同/多智能体机制**：联邦学习（横向/纵向）、联邦迁移/蒸馏、合作博弈（联盟/Shapley分摊）、非合作博弈（纳什）、Stackelberg、拍卖机制（Vickrey/双边）、合同网/黑板系统、共识协议（Raft/简化BFT）、冲突协商/仲裁。
  - **G. 调度策略与算法**：事件驱动重排、滚动时域重优化、启发式（SPT/EDD/CR/TOC）、匹配与流网络/最大流最小割、匈牙利分派、分布式强化学习、分散式MPC、分布式约束优化（DCOP）、多臂老虎机、在线迁移/元学习、鲁棒调度、仿真优化闭环。
  - **H. 数据与通信**：Kafka、MQTT、AMQP/RabbitMQ、OPC UA、Modbus/TCP、gRPC/REST、延迟<1s、延迟<5s、可靠性（至少一次）、可靠性（恰好一次）、断点续传/重试、拥塞/背压控制。
  - （非计分补充维度）执行对象：设备/机台、产线/工段、班组/人员、仓储/拣选、AGV/AMR、能源设备；监控与回溯：告警规则库、事件溯源、决策日志可追踪、回放与灰度、回滚机制、SLA（触发-响应时间）。

表 2 调度响应评分要求（按E-H四维度子项覆盖数计分）
|-|-|
|得分|能力项分项要求|
|-|-|
|1分|E≥1、F≥1、G≥1、H≥1；|
|2分|E≥2、F≥1、G≥2、H≥1；|
|3分|E≥4、F≥2、G≥3、H≥2；|
|4分|E≥6、F≥3、G≥4、H≥3；|
|5分|E≥8、F≥4、G≥5、H≥4。|
|-|-|

- 评分说明：
  - “≥X”按该维度覆盖的子项数量计数；同一子项只计一次。
  - 若某维度为0则最高不超过1分。

- 示例计分法（示意）：若某系统在目标优化中覆盖A=5、B=4、C=4、D=3，则得4分；在调度响应中覆盖E=6、F=3、G=4、H=3，则得4分。

- 建议落地检查清单（选摘）：提交子项清单与映射证据（接口清单/算法白名单/对接系统列表/仿真与回放报告），便于按表计数。 

- - -

- 已为“目标优化”“调度响应”分别定义评估目的、评估内容（含多维度与子项），并给出1-5分的量化评分表，评分基于各维度子项覆盖数，便于落地验收与横向对比。

---

_**User**_

提示词： ## 背景 ##
你是一个资深的AI应用及供应链专家，擅长智能体设计和开发，擅长决策。

## 任务 ## 
工业智能体（工业agent）包含了多个能力域，其中决策能力子域分为目标优化和调度响应两个能力项，本次需要这两个能力项定义评估标准。
目标优化：在产能、质量、能耗、安全等单目标或多目标约束下，基于先进算法生成优化方案
调度响应：对策略调整、环境变化和突发事件快速响应，实现任务调度，如多智能体利用"联邦学习+博弈论"实现资源分配
要求：
1. 请参照示例输出工业智能体决策能力子域的能力项的评估标准
2. 每个能力项的评估标准包含 评估目的、评估内容、评分要求，评估内容需要列出多个维度，每一个维度包括多个子项
3. 评分要求按照1-5个分值设计，结合评估内容中的维度、子项的覆盖情况，给出5个分值的评估标准。
请参照示例中的表 2 营销场景评分要求，对评分进行定量设计，能力项分项要求的填写能够量化，如覆盖1个子项算1分，覆盖2个子项算2分等，禁止出现少量、较深入、程度低、创新等定性或无法衡量的描述。

## 示例 ##
```
##金融大模型营销的评估标准##
###评估目的###　: 评估金融大模型覆盖的智能营销场景丰富程度。
###评估内容###：评估金融大模型对营销场景的覆盖度。
a)任务类型：KYC用户关键信息抽取、营销文案生成、客户意向评估、客户细分、客户分层、客户画像构建、个性化推荐、电子渠道建设等；
b)接入数据：客户信息、产品信息、交易信息、行为信息等；
c)营销手段：短信、IVR、语音机器人外呼、人工外呼等

表 2 营销场景评分要求
|-|-|
|得分|	能力项分项要求|
|-|-|
|1分	|1.应覆盖1种及以上任务；|
|2分	|1.应覆盖2种及以上任务；|
|3分	|1.应覆盖3种及以上任务、接入1种及以上数据类型、支持1种及以上营销手段；|
|4分	|1.应覆盖4种及以上任务、接入2种及以上数据类型、支持1种及以上营销手段；|
|5分	|1.应覆盖5种及以上任务、接入3种及以上数据类型、支持1种及以上营销手段。|
|-|-|
```

---

_**Assistant**_

## 工业智能体决策能力子域评估标准

### 能力项一：目标优化

**评估目的**：评估工业智能体在产能、质量、能耗、安全等单目标或多目标约束下，基于先进算法生成优化方案的能力覆盖程度。

**评估内容**：评估工业智能体对目标优化场景的覆盖度。

a) 优化目标类型：产能最大化、质量提升、能耗降低、成本控制、交期优化、库存优化、设备利用率提升、安全风险控制等；

b) 约束条件类型：设备能力约束、工艺路线约束、人员技能约束、物料供应约束、质量标准约束、安全规范约束、环保要求约束、时间窗口约束等；

c) 算法类型：线性规划、整数规划、遗传算法、粒子群算法、模拟退火、强化学习、多目标优化算法、启发式算法等；

d) 数据接入类型：MES数据、ERP数据、SCADA数据、传感器数据、质量检测数据、能耗监测数据、供应链数据、历史生产数据等。

**表1 目标优化评分要求**

| 得分 | 能力项分项要求 |
|------|----------------|
| 1分  | 应覆盖1种及以上优化目标类型； |
| 2分  | 应覆盖2种及以上优化目标类型、1种及以上约束条件类型； |
| 3分  | 应覆盖3种及以上优化目标类型、2种及以上约束条件类型、1种及以上算法类型； |
| 4分  | 应覆盖4种及以上优化目标类型、3种及以上约束条件类型、2种及以上算法类型、1种及以上数据接入类型； |
| 5分  | 应覆盖5种及以上优化目标类型、4种及以上约束条件类型、3种及以上算法类型、2种及以上数据接入类型。 |

### 能力项二：调度响应

**评估目的**：评估工业智能体对策略调整、环境变化和突发事件快速响应，实现任务调度的能力覆盖程度。

**评估内容**：评估工业智能体对调度响应场景的覆盖度。

a) 响应事件类型：订单变更、设备故障、物料短缺、质量异常、人员变动、工艺调整、紧急插单、供应中断等；

b) 调度策略类型：优先级调度、负载均衡、资源分配、路径规划、任务重分配、多智能体协同、联邦学习、博弈论决策等；

c) 响应机制类型：事件驱动、实时监控、预警预测、自动调整、人机协同、多层级决策、反馈控制、学习优化等；

d) 系统集成类型：MES集成、WMS集成、设备接口、传感器网络、通信协议、数据总线、云端协同、边缘计算等。

**表2 调度响应评分要求**

| 得分 | 能力项分项要求 |
|------|----------------|
| 1分  | 应覆盖1种及以上响应事件类型； |
| 2分  | 应覆盖2种及以上响应事件类型、1种及以上调度策略类型； |
| 3分  | 应覆盖3种及以上响应事件类型、2种及以上调度策略类型、1种及以上响应机制类型； |
| 4分  | 应覆盖4种及以上响应事件类型、3种及以上调度策略类型、2种及以上响应机制类型、1种及以上系统集成类型； |
| 5分  | 应覆盖5种及以上响应事件类型、4种及以上调度策略类型、3种及以上响应机制类型、2种及以上系统集成类型。 |

---

